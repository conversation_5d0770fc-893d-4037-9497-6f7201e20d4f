module("logic.extensions.sharestreet.view.ShareFurnitureListComp",package.seeall)

local ShareFurnitureListComp = class("ShareFurnitureListComp", ListBinderView)

local sortFuncList = {}

function ShareFurnitureListComp:ctor(fixNumEachLine)
	self._model = BaseListModel.New()
    ShareFurnitureListComp.super.ctor(self, self._model,
		"leftGo/item/list",
		ShareStreetAddFurnitureViewPresentor.IconUrl,
		ShreFurnitureItemCell,
		{kScrollDirV, 122, 122, 4, 4, fixNumEachLine or 5})
end

function ShareFurnitureListComp:buildUI()
	ShareFurnitureListComp.super.buildUI(self)

	self._rightGo = self:getGo("rightGo")
	self._singleGO = self:getGo("rightGo/single")
	self._multiGO = self:getGo("rightGo/multiple")
    self._multListContainer = self:getGo("rightGo/multiple/listview/content")
	self._shareIconList = {}

	self._slider = CommonSlider.Add(self:getGo("rightGo/single/count"))

	self._txtDonateCount = self:getText("rightGo/single/count/Image1/countTxt")
	self._txtCount = self:getText("rightGo/single/info/goInfo/count/countTxt")

	-- self._nullGo = self:getGo("nullGo")
	self._leftNullGo = self:getGo("leftGo/commonnullview")

    self._btnFilter = self:getBtn("leftGo/btnFilter")
	self._btnFilter:AddClickListener(self.onClickFilter, self)
	self._btnSearch = self:getBtn("leftGo/btnSearch")
	self._btnSearch:AddClickListener(self._onClickSearch, self)
	self._btnMenu = self:getBtn("leftGo/btnMenu")
	self._searchIpt = self:getInput("leftGo/searchIpt")

	self._rewardObjectPool = ObjectPool.New(0,
		handler(self._createRewardItem,self),
		handler(self._disposeRewardItem, self),
		handler(self._resetRewardItem, self)
		)
	self._goSelective = self:getGo("leftGo/selective")
	self._goSelectAll = self:getGo("leftGo/toggleAll")
	self._togMultiSelect = Toggle.New(self:getGo("leftGo/selective"), handler(self.onMultiSelectChange, self), true)
	self._togSelectAll = self:getToggle("leftGo/toggleAll")
	self:getBtn("leftGo/toggleAll/btnSelectAll"):AddClickListener(self.onClickSelectAll, self)
	self._btnMenu:AddClickListener(self.onClickMenu, self)
	self._bubbleGO = self:getGo("leftGo/btnMenu/bubbleGo")
	Framework.UIGlobalTouchTrigger.Get(self._bubbleGO):AddIgnoreTargetListener(self.hideBubble, self)
	self.rankBtns = {}
	self.rankTG = ToggleGroup.New(handler(self.selectRank, self, self))
	for i=1, 7 do
		self.rankTG:addView(goutil.findChild(self._bubbleGO, "type" .. i))
	end

	self._goMultiModeNoSelectionTips = self:getGo("multiModeNoSelectionTips")
end

function ShareFurnitureListComp:bindEvents()
    ShareFurnitureListComp.super.bindEvents(self)
    self._slider:addSliderChangeListener(self._onRecycleCountChange, self)
end

function ShareFurnitureListComp:onEnter()
    ShareFurnitureListComp.super.onEnter(self)
	self.selectPart = nil
    self._shareIconList = {}
	CommonHUDFacade.instance:hideHUD("ShareFurnitureListComp")
	self:_setSortType(1)

	GlobalDispatcher:addListener(GlobalNotify.ItemChange,self._refreshItems,self)
    if self._viewPresentor.tabComp then
        self._viewPresentor.tabComp:addListener(ItemNotify.ChangeTab,self.onChangeTab,self) 
    end
	-- self:registerLocalNotify(ItemNotify.ChangeTab, self._setItems, self)
	self:registerLocalNotify(ItemNotify.SelectItem, self._selectItem, self)
	self._togMultiSelect:setSelect(false)
	self._filterTags = {}
	self:_hideSearch()
	self:onMultiSelectChange(false)
	self._bubbleGO:SetActive(false)
	self.rankTG:clickViews(true, 1)
	self._isOpened = true
end

function ShareFurnitureListComp:onExit()
    ShareFurnitureListComp.super.onExit(self)
	CommonHUDFacade.instance:showHUD("ShareFurnitureListComp")
	self._isOpened = false
	GlobalDispatcher:removeListener(GlobalNotify.ItemChange,self._refreshItems,self)
	-- self:unregisterLocalNotify(ItemNotify.ChangeTab, self._setItems, self)
	self:unregisterLocalNotify(ItemNotify.SelectItem, self._selectItem, self)
    if self._viewPresentor.tabComp then
        self._viewPresentor.tabComp:removeListener(ItemNotify.ChangeTab,self.onChangeTab,self) 
    end
    self:clearMultiSelect()
end

function ShareFurnitureListComp:clearMultiSelect()
    for i=1, #self._shareIconList do
        CommonIconMgr.instance:returnCommonIcon(self._shareIconList[i])
    end
    self._shareIconList = {}
end

function ShareFurnitureListComp:reset()
	self.isMultiSelect = false
	self._togMultiSelect:setSelect(false)
	self:clearMultiSelect()
	-- self:setSelectItem(nil)
	self:setSelectItems({}, true)
	self:setItems({})
end

function ShareFurnitureListComp:_onRecycleCountChange(num)
	self._recycleCount = num
	self._txtDonateCount.text = lang("数量 <color=#e38534>{1}</color> 个", num)
end


function ShareFurnitureListComp:onMultiSelectChange(isOn)
	self.isMultiSelect = isOn
	self._singleGO:SetActive(not isOn)
	self._multiGO:SetActive(isOn)
	self._togSelectAll.gameObject:SetActive(isOn and self._model:getMoCount() > 0)
	if self._isOpened then
		if self.isMultiSelect then
			self:setSelectItem(nil)
            -- self:clearMultiSelect()
			self:setSelectItems({})
			self._togSelectAll.isOn = false
		else
			-- self:setSelectItem(nil)
			self:setSelectItem(self._model:getMoByIndex(1),true)
		end
		self:refreshRight()
	end
end

function ShareFurnitureListComp:_selectItem(mo)
	if mo == nil or mo.id == -1 then
		--todo 蒙版效果
		self:setSelectItem()
	else
		if self.isMultiSelect then
			local selectItems = self:getSelectItems()
			local index = table.indexof(selectItems, mo)
			if  index then
				table.remove(selectItems, index)
			else
				table.insert(selectItems, mo)
			end
			self:setSelectItems(selectItems)	
		else
			self:setSelectItem(mo)

		end
	end
	self:refreshRight()
end

function ShareFurnitureListComp:refreshRight()
	if self.isMultiSelect then
        self._rightGo:SetActive(#self:getSelectItems() > 0)
		self._goMultiModeNoSelectionTips:SetActive(#self:getSelectItems() == 0 and self._model:getMoCount() > 0)
		self:_refreshMultiSelect()
	else
		self._goMultiModeNoSelectionTips:SetActive(false)
        if #self:getSelectItems() > 0 then
            self._rightGo:SetActive(true)
			self:_refreshSingleSelect()
        else
            self._rightGo:SetActive(false)
        end
		-- self._nullGo:SetActive(true)
		-- self._rightGo:SetActive(false)		
	end

end

function ShareFurnitureListComp:_refreshSingleSelect()
	self._itemMo = self:getSelectItem()
	self._slider:setMinAndMaxCount(1, self._itemMo.num)
	local define = ItemService.instance:getDefine(self._itemMo.id)
    if self._viewPresentor.infoComp then
		self._viewPresentor.infoComp:setItemId(self._itemMo.id,self._itemMo)
		self._viewPresentor.infoComp:refreshView(true, 112)
		self._txtCount.text = self._itemMo.num
    end
	-- local reward = define.recycleOutput[1]

	self._slider:setCurrentCount(1)		
end

function ShareFurnitureListComp:_refreshMultiSelect()
	local list = self:getSelectItems()
    --因为只有增加一个和删除一个两种情况
    if #self._shareIconList < #list then
        --新增图标
        for i=1, #list do
            if i > #self._shareIconList or self._shareIconList[i]._itemId ~= list[i].id then
                local newIcon = CommonIconMgr.instance:fetchCommonIcon()
                newIcon:setWidthAndHeight(85):showFreeState(false):showRarenessGo(true):showSpecial(true)
                newIcon:buildData(list[i])
                goutil.addChildToParent(newIcon._go, self._multListContainer)
                table.insert(self._shareIconList, i, newIcon)
            end
        end
    elseif #self._shareIconList > #list then
        local index1 = 1
        local index2 = 1
        while index1 <= #self._shareIconList do
            if index1 > #list or self._shareIconList[index1]._itemId ~= list[index2].id then
                local icon = table.remove(self._shareIconList, index1)
                CommonIconMgr.instance:returnCommonIcon(icon)
            else
                index1 = index1 + 1
                index2 = index2 + 1
            end
        end           
    end
end

function ShareFurnitureListComp:_sortItem(items)
	table.sort(items, self._curSortFunc)
end	

function ShareFurnitureListComp:_setSortType(sortType, needRefresh)
	self._curSortFunc = sortFuncList[sortType]
	if needRefresh then
		self:_refresh()
	end
end

function ShareFurnitureListComp:setItems(allItems)
    printInfo("setItems", #allItems)
    self._allItems = allItems
	self:_refreshItems()
    if not self.isMultiSelect then
        self:setSelectItem(self._model:getMoByIndex(1))
        self:refreshRight()
    end
end


function ShareFurnitureListComp:_refreshItems()
	local items = {}

	for i=1, #self._allItems do
		local currentItem = self._allItems[i]
		if self:_filterItem(currentItem) then
            table.insert(items, currentItem)
        end
	end
    printInfo("show items", #items)
	self:_sortItem(items)
    printInfo("items", #items)
	if not self.isMultiSelect then
		local mo = self:getSelectItem()
		if mo ~= nil then
			local index = table.indexof(items, mo)
			if not index then
				local lastIndex = self._model:getMoIndex(mo) or 1
				mo = items[math.min(lastIndex, #items)]
			end
		end
		self:setSelectItem(mo, false)
		self:refreshRight()
	end
	self._model:setMoList(items)


	self._togSelectAll.isOn = false

	local isFilter = (self._filterTags and #self._filterTags > 0) or self.showRare ~= 0
	local isSearch = not string.nilorempty(self._searchText)
	-- print("isFilter", isFilter)
	if #items<=0 and (not isFilter) and (not isSearch) then
		if self._leftNullGo then
			self._leftNullGo:SetActive(true)
		end
		self._goSelectAll:SetActive(false)
		self._goSelective:SetActive(false)
		self._btnFilter.gameObject:SetActive(false)
		self._btnSearch.gameObject:SetActive(false)
		self._btnMenu.gameObject:SetActive(false)
		-- 没有item时隐藏multiple模式
		self._multiGO:SetActive(false)
		self._singleGO:SetActive(false)
		self._goMultiModeNoSelectionTips:SetActive(false)
	else
		if self._leftNullGo then
			self._leftNullGo:SetActive(false)
		end
		self._goSelectAll:SetActive(self.isMultiSelect and self._model:getMoCount() > 0)
		self._goSelective:SetActive(self._canShare)
		self._btnFilter.gameObject:SetActive(true)
		self._btnSearch.gameObject:SetActive(true)
		self._btnMenu.gameObject:SetActive(true)
		-- 有item时根据当前模式显示对应UI
		self._multiGO:SetActive(self.isMultiSelect)
		self._singleGO:SetActive(not self.isMultiSelect)
	end
end

function ShareFurnitureListComp:_filterItem(item)
    local define = ItemService.instance:getDefine(item.id)
    if self.showRare ~= 0 and define.quality ~= self.showRare then
        return false
    end
    if self.selectPart and not self.selectPart:contains(define:getPart()) then
        return false
    end

	if self._searchText and not string.find(define.name, self._searchText) then
		return false
	end
	if define.tags and  #self._filterTags > 0 then
		local hasTag = false
		for j, tag in ipairs(define.tags) do
			if table.indexof(self._filterTags, tag) ~= false then
				hasTag = true
				break
			end
		end
		if not hasTag then
			return false
		end
	end
    return true
end

function ShareFurnitureListComp:_onClickPreview()
	ViewMgr.instance:open("WorkshopGain",self._itemMo.id,true,nil,true)
	-- ViewMgr.instance:open("ItemPreview", self._itemMo.id)
end

function ShareFurnitureListComp:onClickSelectAll()
	local oldValue = self._togSelectAll.isOn
	if oldValue then
		self:setSelectItems({})	
	else
		self:setSelectItems(arrayutil.copy(self._model:getMoList()))
	end
	self:refreshRight()
	self._togSelectAll.isOn = not oldValue
end

function ShareFurnitureListComp:getShareItems()
    if self.isMultiSelect then
        return self:getSelectItems()
    else
        local item = self:getSelectItem()
        if item then
            return {{id = item.id, num = self._slider:getCurrentCount()}}
        else
            return {}
        end
    end
end

function ShareFurnitureListComp:setCanShare(canShare)
    self._canShare = canShare
    self._togMultiSelect:getView():SetActive(canShare)
    self._slider._go:SetActive(canShare)
end

function ShareFurnitureListComp:onClickMenu()
	self._bubbleGO:SetActive(true)
end

function ShareFurnitureListComp:hideBubble()
	self._bubbleGO:SetActive(false)
end

function ShareFurnitureListComp:selectRank(index)
	self.showRare = index - 1
    self:refresh()
end

function ShareFurnitureListComp:onChangeTab(selectedType)
    self.selectPart = selectedType
    self:refresh()
end

function ShareFurnitureListComp:refresh()
	if self._isOpened then
		if not self.isMultiSelect then
			self:setSelectItems({})	
		end
		self:_refreshItems()
		self:refreshRight()
		self:hideBubble()
	end
end

function ShareFurnitureListComp:_onClickSearch()
	if self._isShowSearch then
		self:_hideSearch()
	else
		self:_showSearch()
	end
end

function ShareFurnitureListComp:_showSearch()
	self._searchIpt:AddOnEndEdit(self._handlerSearch, self)
	self._isShowSearch = true
	self._searchIpt.gameObject:SetActive(true)
	-- self._txtSearch.input:ActivateInputField()
end

function ShareFurnitureListComp:_hideSearch()
	self._searchIpt:RemoveOnEndEdit()
	self._searchIpt:SetText("")
	self._isShowSearch = false
	self._searchText = nil
	self._searchIpt.gameObject:SetActive(false)
	self:refresh()
end

function ShareFurnitureListComp:_handlerSearch()
	self._searchText = string.trim(self._searchIpt:GetText())
	self:refresh()
end

function ShareFurnitureListComp:onClickFilter()
	local copyList = arrayutil.copy(self._filterTags)
	ViewMgr.instance:open("FurTagEdit", copyList, handler(self._onChangeTags, self))
end

function ShareFurnitureListComp:_onChangeTags(tags)
	self._filterTags = tags
	-- self._tagSelectGo:SetActive(#tags ~= 0)
	self:refresh()
end

local function sortByGainTime(left, right)
	-- useCount = 0 的排前面
	local leftHasUseCount = (left.useCount or 0) > 0
	local rightHasUseCount = (right.useCount or 0) > 0
	if leftHasUseCount ~= rightHasUseCount then
		return rightHasUseCount
	end
	-- quality 大的排前面
	local leftQuality = left:getDefine().quality
	local rightQuality = right:getDefine().quality
	if leftQuality ~= rightQuality then
		return leftQuality > rightQuality
	end
	return left.gainTime > right.gainTime
end

local function sortById(left, right)
	-- useCount = 0 的排前面
	local leftHasUseCount = (left.useCount or 0) > 0
	local rightHasUseCount = (right.useCount or 0) > 0
	if leftHasUseCount ~= rightHasUseCount then
		return rightHasUseCount
	end
	-- quality 大的排前面
	local leftQuality = left:getDefine().quality
	local rightQuality = right:getDefine().quality
	if leftQuality ~= rightQuality then
		return leftQuality > rightQuality
	end
	return left:getDefine().id < right:getDefine().id
end

local function sortByNum(left, right)
	-- useCount = 0 的排前面
	local leftHasUseCount = (left.useCount or 0) > 0
	local rightHasUseCount = (right.useCount or 0) > 0
	if leftHasUseCount ~= rightHasUseCount then
		return rightHasUseCount
	end
	-- quality 大的排前面
	local leftQuality = left:getDefine().quality
	local rightQuality = right:getDefine().quality
	if leftQuality ~= rightQuality then
		return leftQuality > rightQuality
	end
	if left.num ~= right.num then
		return left.num > right.num
	else
		return sortById(left, right)
	end
end

local function showByType(left, right)
	-- useCount = 0 的排前面
	local leftHasUseCount = (left.useCount or 0) > 0
	local rightHasUseCount = (right.useCount or 0) > 0
	if leftHasUseCount ~= rightHasUseCount then
		return rightHasUseCount
	end
	-- quality 大的排前面
	local leftQuality = left:getDefine().quality
	local rightQuality = right:getDefine().quality
	if leftQuality ~= rightQuality then
		return leftQuality > rightQuality
	end
	local typeLeft = ItemType.getTypeById(left.id)
	local typeRight = ItemType.getTypeById(left.id)
	if typeLeft ~= typeRight then
		return typeLeft < typeRight
	else
		return sortByNum(left, right)
	end
end

local function sortByRarity(left, right)
	-- useCount = 0 的排前面
	local leftHasUseCount = (left.useCount or 0) > 0
	local rightHasUseCount = (right.useCount or 0) > 0
	if leftHasUseCount ~= rightHasUseCount then
		return rightHasUseCount
	end
	-- quality 大的排前面
	local leftQuality = left:getDefine().quality
	local rightQuality = right:getDefine().quality
	if leftQuality ~= rightQuality then
		return leftQuality > rightQuality
	end
	if left:getDefine().quality ~= right:getDefine().quality then
		return left:getDefine().quality < right:getDefine().quality
	else
		return showByType(left, right)
	end
end





sortFuncList[0] = sortByGainTime
sortFuncList[1] = sortByRarity
sortFuncList[2] = sortById

return ShareFurnitureListComp


