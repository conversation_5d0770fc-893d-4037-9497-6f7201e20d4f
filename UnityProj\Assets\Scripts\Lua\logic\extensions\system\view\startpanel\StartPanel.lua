module("logic.extensions.system.view.startpanel.StartPanel", package.seeall)
local StartPanel = class("StartPanel", ViewComponent)

function StartPanel:buildUI()
	self.btnStart = self:getBtn("btnStart")
	self.btnStart:AddClickListener(self.onClickStart, self)
	self.btnEnter = self:getBtn("btnEnter")
	-- self.btnEnter:AddClickListener(self.onClickEnter, self)
	self.serverGO = self:getGo("server")
	self.txtServerName = self:getText("server/txtServerName")
	self._topRight = self:getGo("topRight")
	self._originTopRightPos = GameUtils.getLocalPos(self._topRight)
	self._txtVersion = self:getText("topRight/txtVersion")
	self._txtNetworkId = self:getText("topRight/txtNetworkId")
	self._txtUserId = self:getText("topRight/txtUserId")
	self:getBtn("server/btnSelect"):AddClickListener(self.onClickSelectServer, self)
	self:getBtn("right/btnNotice"):AddClickListener(self.onClickNotice, self)
	self.greenGO = self:getGo("server/greenGo")
	self.greyGO = self:getGo("server/greyGo")
	self.redGO = self:getGo("server/redGo")
	self.togAgree = self:getToggle("togAgree")
	self:getBtn("togAgree/btnProtocal"):AddClickListener(self.onClickProtocal, self)
	self.txtProtocal = self:getText("togAgree/btnProtocal/txtContent2")
	self:getBtn("togAgree/btnPrivate"):AddClickListener(self.onClickPrivate, self)
	self.txtPrivacy = self:getText("togAgree/btnPrivate/txtContent4")
	self.btnFix = self:getBtn("right/btnFix")
	self.btnFix:AddClickListener(self.onClickFix, self)
	self.btnCenter = self:getBtn("right/btnCenter")
	self.btnCenter:AddClickListener(self.onClickCenter, self)
	self:getBtn("btnAgeTips"):AddClickListener(self.onClickAgeTips, self)
	self.btnCustomerService = self:getBtn("right/btnCustomerService")
	self.btnCustomerService:AddClickListener(self.onClickCustomerService, self)
	self.btnTestUrl = self:getBtn("btnStart/btnTestUrl")
	self.btnTestUrl:AddClickListener(self.onClickTestUrl, self)
	self:getBtn("txtInfo/btnText"):AddClickListener(self.onClickICP, self)
	--视频
	self._video = self:getGo("video")
end

function StartPanel:destroyUI()
end

function StartPanel:onEnter()
	self.serverGO:SetActive(false)
	self.btnEnter.gameObject:SetActive(false)
	self.btnStart.gameObject:SetActive(true)
	self.btnFix.gameObject:SetActive(not SDKManager.getSDKInstacne():isCloudGame())
	GlobalDispatcher:addListener(GlobalNotify.OnLoginSDKSuccess, self.onLoginSDKSuccess, self)
	GlobalDispatcher:addListener(GlobalNotify.OnLoginWebSuccess, self.onLoginWebSuccess, self)
	GlobalDispatcher:addListener(GlobalNotify.OnLoginQueueFinish, self.OnLoginQueueFinish, self)
	GlobalDispatcher:addListener(GlobalNotify.OnSDKLogout, self.onSDKLogout, self)
	self:registerNotify(GlobalNotify.OpenService_UpdateView, self._setOpenServiceTipInfo, self)
	self.greenGO:SetActive(false)
	self.greyGO:SetActive(false)
	self.redGO:SetActive(false)
	self.btnCenter.gameObject:SetActive(false)
	self.btnCustomerService.gameObject:SetActive(SDKManager.getSDKInstacne():isShowUserCenterButton())
	-- self.btnCustomerService.gameObject:SetActive(false)
	self._txtUserId.text = ""
	LogService.instance:logNewHand(103)
	local protocal = SDKManager.getSDKInstacne().getProtocolName()
	if not string.nilorempty(protocal) then
		self.txtProtocal.text = protocal
	end

	local privacy = SDKManager.getSDKInstacne().getPrivacyName()
	if not string.nilorempty(privacy) then
		self.txtPrivacy.text = privacy
	end
	self.togAgree.isOn = true
	self.togAgree.gameObject:SetActive(false)
	self.btnTestUrl.gameObject:SetActive(enableDebug)
	-- if Framework.OSDef.RunOS == Framework.OSDef.IOS then
	-- 	self.btnTestUrl.gameObject:SetActive(true) --为了ios测试性能临时打开url按钮
	-- end
	self._txtVersion.text = BootstrapPjabGameConfigMgr.getDisplayVersion()
	self._txtNetworkId.text = "nid:" .. BootstrapUtil.getAliUid()
	if self.togAgree.isOn then
		if LoginService.instance.loginState == LoginService.LoginState_Web then
			self:loginWeb()
		else
			self:onClickStart()
		end
	end
	self._video:SetActive(true)
	local url = Framework.FileUtils.Instance:FullPathForFile("movie/openingvideo.mp4", 1)
	self._recordView = RecordScreenPreviewController.New()
	self._recordView:play(url, self._video, handler(self.onPlayCallback, self))
end

function StartPanel:onPlayCallback()
	SoundManager.instance:stopBGM()
end

function StartPanel:onClickStart()
	if not self.togAgree.isOn then
		FlyTextManager.instance:showFlyText(lang("请查看并勾选同意下方的协议，即可进入游戏哦。"))
		return
	end

	if LoginService.instance.loginState == LoginService.LoginState_None then
		self:startLoginSDK()
	elseif LoginService.instance.loginState == LoginService.LoginState_SDK then
		self:loginWeb()
	elseif LoginService.instance.loginState == LoginService.LoginState_Web then
		LoginService.instance.areaInfo = self.areaInfo
		LoginService.instance:startQueue(true)
	end
end

function StartPanel:showLoginSelectDlg()
	local params = {}
	params.handler = function(isOK)
		if isOK then
			ViewMgr.instance:open("LoginPanel")
		elseif isOK == false then
			LoginService.instance:loginSDK()
		end
	end
	params.text = lang("选择登录方式")
	params.okTxt = lang("内网模拟")
	params.cancelTxt = "SDK"
	ViewMgr.instance:open("DialogView", params)
end

function StartPanel:startLoginSDK()
	Framework.LocalStorage.Instance:SetInt("AgreeProtocol", 1)
	LogService.instance:logNewHand(104)
	if not Framework.OSDef.isEditor then
		if BootstrapPjabGameConfigMgr.getURLType() == 0 or BootstrapPjabGameConfigMgr.getURLType() == 1 then
			self:showLoginSelectDlg()
		else
			LoginService.instance:loginSDK()
		end
	else
		ViewMgr.instance:open("LoginPanel")
	end
end

function StartPanel:onSDKLogout()
	self.serverGO:SetActive(false)
	ViewMgr.instance:close("SelectZonePanel")
	self.btnEnter.gameObject:SetActive(false)
	self.btnStart.gameObject:SetActive(true)
end

function StartPanel:onLoginSDKSuccess(accountInfo)
	self.accountInfo = accountInfo
	self.btnCenter.gameObject:SetActive(SDKManager.getSDKInstacne():isShowUserCenterButton())
	LoginService.instance.accountInfo = accountInfo
	self:loginWeb()
end

function StartPanel:loginWeb()
	LoginService.instance:loginWeb()
end

function StartPanel:onLoginWebSuccess(data)
	LogService.instance:logNewHand(107)
	-- self.btnStart.gameObject:SetActive(false)
	-- self.togAgree.gameObject:SetActive(false)
	local userId = tonumber(data.userId)
	self._txtUserId.text = userId <= 0 and "" or "uid:" .. userId
	local isShowServer = BootstrapPjabGameConfigMgr.getURLType() == 0 or BootstrapPjabGameConfigMgr.getURLType() == 1
	self.serverGO:SetActive(isShowServer)
	-- self.btnEnter.gameObject:SetActive(true)

	-- self.btnCustomerService.gameObject:SetActive(SDKManager.getSDKInstacne():isShowUserCenterButton())
	self:selectServer(data.defaultArea)
	if not LoginService.instance:_isShowOpenServiceCountDownPanel() and not LoginService.instance.isAutoShowedNotice then
		LoginService.instance.isAutoShowedNotice = true
		self:onClickNotice()
	end
end

function StartPanel:OnLoginQueueFinish()
	LoginService.instance:loginServer(self.areaInfo)
end

function StartPanel:onClickSelectServer()
	if self._lastClickTime == nil then
		self._lastClickTime = Time.realtimeSinceStartup
		LoginService.instance:selectArea(handler(self.selectServer, self))
	else
		local lastClickTime = self._lastClickTime
		self._lastClickTime = Time.realtimeSinceStartup
		if self._lastClickTime - lastClickTime > 15 then
			LoginService.instance:selectArea(handler(self.selectServer, self))
		else
			LoginService.instance:selectCacheArea(handler(self.selectServer, self))
		end
	end
	LogService.instance:logNewHand(1071)
end
function StartPanel:selectServer(areaInfo)
	self.areaInfo = areaInfo
	self.txtServerName.text = areaInfo.name
	self.greenGO:SetActive(false)
	self.greyGO:SetActive(false)
	self.redGO:SetActive(false)
	if areaInfo.state == 1 then
		self.greenGO:SetActive(true)
		if LoginService.instance.isAutoLogin then
			LoginService.instance:loginServer(areaInfo)
		end
	elseif areaInfo.state == 2 then
		self.redGO:SetActive(true)
	else
		self.greyGO:SetActive(true)
	end
end

-- function StartPanel:onClickEnter()
-- 	LoginService.instance:startQueue(true)
-- end

function StartPanel:onClickNotice()
	NewNoticeController.instance:openNoticeView(true)
end

function StartPanel:onClickProtocal()
	LogService.instance:logNewHand(105)
	-- local url = string.format(UnityWebBridgeUrl.URL_USER_PROTOCAL, os.time())
	-- self:_setUserProtocalContent(not UnityWebBridge.isExistFunc())
	-- self:_showUserView(url)
	SDKManager.getSDKInstacne().showProtocolView()
end

function StartPanel:onClickPrivate()
	LogService.instance:logNewHand(106)
	-- local url = string.format(UnityWebBridgeUrl.URL_USER_PRIVATE, os.time())
	-- self:_setUserProtocalContent(not UnityWebBridge.isExistFunc())
	-- self:_showUserView(url)
	SDKManager.getSDKInstacne().showPrivacyView()
end

function StartPanel:_showUserView(url)
	local isShowWeb = UnityWebBridge.isExistFunc()
	if isShowWeb then
		ViewFacade.openWebView(url)
	end
	-- local scrollBg = self:getGo("userProtocal/middleView/bg/Image2")
	-- goutil.setActive(scrollBg, not isShowWeb)
	-- goutil.setActive(self._userProtocalGo,true)
end

function StartPanel:onClickFix()
	DialogHelper.showConfirmDlg(
		lang("你确定要修复资源吗?修复完成会退出游戏，需要手动启动游戏"),
		function(isOK)
			if isOK then
				self:_repairGameAndReboot()
			end
		end
	)
end

function StartPanel:_repairGameAndReboot()
	GameUtils.repair()
	BootstrapLauncher.instance:applicationQuit()
end

function StartPanel:onClickAgeTips()
	ViewMgr.instance:open("AgeTipsPanel")
end

function StartPanel:onExit()
	if self._recordView then
		self._recordView:stop()
		self._recordView = nil
	end
	GlobalDispatcher:removeListener(GlobalNotify.OnLoginSDKSuccess, self.onLoginSDKSuccess, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnLoginWebSuccess, self.onLoginWebSuccess, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnSDKLogout, self.onSDKLogout, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnLoginQueueFinish, self.OnLoginQueueFinish, self)
	self:unregisterNotify(GlobalNotify.OpenService_UpdateView, self._setOpenServiceTipInfo, self)
	SoundManager.instance:replayBGM()
end

function StartPanel:onClickCenter()
	SDKManager.getSDKInstacne():showUserCenter()
end

function StartPanel:onClickTestUrl()
	ViewMgr.instance:open("TestUrlPanel", handler(self.showLoginSelectDlg, self))
end

function StartPanel:onClickCustomerService()
	SDKManager.getSDKInstacne():helper()
end

function StartPanel:_setOpenServiceTipInfo()
	local isShowCountDown = LoginService.instance:_isShowOpenServiceCountDownPanel()
	self.btnStart.gameObject:SetActive(not isShowCountDown)
	local isShowServer = BootstrapPjabGameConfigMgr.getURLType() == 0 or BootstrapPjabGameConfigMgr.getURLType() == 1
	self.serverGO:SetActive(isShowServer and not isShowCountDown)
	if isShowCountDown then
		GameUtils.setLocalPos(
			self._topRight,
			self._originTopRightPos.x,
			self._originTopRightPos.y - 122.37,
			self._originTopRightPos.z
		)
	else
		GameUtils.setLocalPos(self._topRight, self._originTopRightPos.x, self._originTopRightPos.y, self._originTopRightPos.z)
	end
end

function StartPanel:onClickICP()
	SDKManager.getSDKInstacne():showWebBrowser("https://beian.miit.gov.cn/#/Integrated/index")
end

return StartPanel
