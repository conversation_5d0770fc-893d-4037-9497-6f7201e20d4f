module("logic.extensions.sharestreet.view.ShareStreetRankView", package.seeall)
---@class ShareStreetRankView
local ShareStreetRankView = class("ShareStreetRankView", ListBinderView)

function ShareStreetRankView:ctor()
    self._listModel = BaseListModel.New()
    ShareStreetRankView.super.ctor(self, self._listModel, "listview/pageGo/list", ShareStreetRankViewPresentor.Url_Item,
        ShareStreetRankItem, {kScrollDirV, 328, 250, 18, 18, 3}, false)
end

--- view初始化时会执行
function ShareStreetRankView:buildUI()
    ShareStreetRankView.super.buildUI(self)

    self._btnClose = self:getBtn("btnclose/btnClose")

    self._tgTopTab = ToggleGroup.New(handler(self._onClickTopTab, self))
	self._tgTopTab:addView(self:getGo("listview/pageGo/tabGo/content/tab_1"))
	self._tgTopTab:addView(self:getGo("listview/pageGo/tabGo/content/tab_2"))

    self._tgLeftTab = ToggleGroup.New(handler(self._onClickLeftTab, self))
	self._tgLeftTab:addView(self:getGo("btnGo/content/btn_1"))
	self._tgLeftTab:addView(self:getGo("btnGo/content/btn_2"))

    self._txtNull = self:getText("listview/pageGo/list/commonnullview/txt")
    self._goNull = self:getGo("listview/pageGo/list/commonnullview")

    self._iptSearch = self:getInput("btnSearch/inputSearch")
    self._goSearch = self:getGo("btnSearch/unselect")
    self._ctSearch = Framework.UIClickTrigger.Get(self._goSearch)
end

--- view初始化时会执行，在buildUI之后
function ShareStreetRankView:bindEvents()
    ShareStreetRankView.super.bindEvents(self)

    self._btnClose:AddClickListener(self.close, self)
    self._ctSearch:AddClickListener(self._onClickSearch, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetRankView:onEnter()
    ShareStreetRankView.super.onEnter(self)

    self._tgLeftTab:setIndex(1)
    self._tgTopTab:setIndex(1)
    self:_updateView()

	self:registerNotify(GlobalNotify.StartLoadScene, self.close, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetRankView:onEnterFinished()
    ShareStreetRankView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetRankView:onExit()
    ShareStreetRankView.super.onExit(self)

	self:unregisterNotify(GlobalNotify.StartLoadScene, self.close, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetRankView:onExitFinished()
    ShareStreetRankView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetRankView:unbindEvents()
    ShareStreetRankView.super.unbindEvents(self)

    self._btnClose:RemoveClickListener()
    self._ctSearch:RemoveClickListener()
end

--- view销毁时会执行
function ShareStreetRankView:destroyUI()
    ShareStreetRankView.super.destroyUI(self)
end

function ShareStreetRankView:_getRankInfo(isFriend)
    if isFriend == nil then
        isFriend = false
    end
    ShareStreetAgent.instance:sendGetStreetPopularityRankRequest(isFriend, handler(self._onGetRankInfo, self))
end

function ShareStreetRankView:_onGetRankInfo(streetInfoList)
    local moList = {}
    for i, streetNO in ipairs(streetInfoList) do
        local itemData = {
            no = streetNO,
            roleInfo = streetNO.streetOwner,
            emptyAreaCount = streetNO.areaCount - streetNO.memberCount ---剩余空位数量, 用于后面排序
        }
        table.insert(moList, itemData)
    end

    if #moList == 0 then
        self:showNullView()
    else
        -- table.sort(moList, function(a, b)
        --     return a.emptyAreaCount > b.emptyAreaCount
        -- end) --按空位数排序
    end
    self._listModel:setMoList(moList)
end

function ShareStreetRankView:_getRecommendInfo()
    ShareStreetAgent.instance:sendGetStreetRecommendListRequest(handler(self._onGetRankInfo, self))
end

function ShareStreetRankView:_onClickTopTab(index, isSelected)
	if not isSelected then return end
	self:_updateView()
end

function ShareStreetRankView:_onClickLeftTab(index, isSelected)
	if not isSelected then return end
	self:_updateView()
end

function ShareStreetRankView:_updateView()
    self._iptSearch:SetText("")
    self._goNull:SetActive(false)
    local isFriend = self:_getIsFriendBtnSelected()
    local isRank = self:_getIsRankBtnSelected()
    if isRank then
        self:_getRankInfo(isFriend)
    else
        local myStreetInfo = ShareStreetModel.instance:getUserInfo()
        if myStreetInfo:getOwnerId() ~= UserInfo.userId or myStreetInfo:getMemberCount() > 1 then
            self:showNullView(lang("已加入街区，或自己街区存在其它成员，无法申请加入街区"))
            self._listModel:setMoList({})
            return
        end
        if isFriend then
            self:_getRankInfo(true)
        else
            self:_getRecommendInfo()
        end
    end
end

function ShareStreetRankView:_getIsFriendBtnSelected()
    return self._tgTopTab:getIndex() == 2
end

function ShareStreetRankView:_getIsRankBtnSelected()
    return self._tgLeftTab:getIndex() == 1
end

function ShareStreetRankView:showNullView(txt)
    if string.nilorempty(txt) then
        txt = lang("暂没有符合条件的玩家")
    end
    self._goNull:SetActive(true)
    self._txtNull.text = txt
end

function ShareStreetRankView:_onClickSearch()
    local searchKey = self._iptSearch:GetText()
    if string.nilorempty(searchKey) then
        return
    end
    local initialLetter = string.sub(searchKey, 1, 1)
    print("initialLetter", initialLetter)
    local searchType = 1
    if initialLetter == "#" then
        searchType = 0
        searchKey = string.sub(searchKey, 2)
    elseif initialLetter == "*" then
        searchType = 2
        searchKey = string.sub(searchKey, 2)
    end
    self._goNull:SetActive(false)
    ShareStreetAgent.instance:sendSearchShareStreetRequest(searchKey,searchType,handler(self._onGetRankInfo, self))
    self._iptSearch:SetText("")
end

return ShareStreetRankView
