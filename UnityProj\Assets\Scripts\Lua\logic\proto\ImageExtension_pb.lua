-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.ImageExtension_pb", package.seeall)


local tb = {}
tb.IMAGETYPE_ENUM = protobuf.EnumDescriptor()
tb.IMAGETYPE_HEAD_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_SHARE_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_FEEDBACK_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_ISLAND_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_RELATIVE_WISH_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_PURIKURA_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_ATM_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_PHOTO_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_TEMPLATE_SHARE_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_TEMPLATE_DOWNLOAD_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_TEMPLATE_DESIGN_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_STORY_TELLER_POSTER_ENUMITEM = protobuf.EnumValueDescriptor()
tb.IMAGETYPE_SHARE_STREET_ENUMITEM = protobuf.EnumValueDescriptor()
GETDOWNLOADURLREQUEST_MSG = protobuf.Descriptor()
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD = protobuf.FieldDescriptor()
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD = protobuf.FieldDescriptor()
GETUPLOADTOKENREQUEST_MSG = protobuf.Descriptor()
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD = protobuf.FieldDescriptor()
tb.GETUPLOADTOKENREQUEST_NUM_FIELD = protobuf.FieldDescriptor()
GETUPLOADTOKENREPLY_MSG = protobuf.Descriptor()
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD = protobuf.FieldDescriptor()
UPLOADSUCCESSREQUEST_MSG = protobuf.Descriptor()
tb.UPLOADSUCCESSREQUEST_INFO_FIELD = protobuf.FieldDescriptor()
GETDOWNLOADURLREPLY_MSG = protobuf.Descriptor()
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD = protobuf.FieldDescriptor()
IMAGEUPLOADSUCCESSINFO_MSG = protobuf.Descriptor()
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD = protobuf.FieldDescriptor()
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD = protobuf.FieldDescriptor()
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD = protobuf.FieldDescriptor()
UPLOADSUCCESSREPLY_MSG = protobuf.Descriptor()

tb.IMAGETYPE_HEAD_ENUMITEM.name = "HEAD"
tb.IMAGETYPE_HEAD_ENUMITEM.index = 0
tb.IMAGETYPE_HEAD_ENUMITEM.number = 1
tb.IMAGETYPE_SHARE_ENUMITEM.name = "SHARE"
tb.IMAGETYPE_SHARE_ENUMITEM.index = 1
tb.IMAGETYPE_SHARE_ENUMITEM.number = 2
tb.IMAGETYPE_FEEDBACK_ENUMITEM.name = "FEEDBACK"
tb.IMAGETYPE_FEEDBACK_ENUMITEM.index = 2
tb.IMAGETYPE_FEEDBACK_ENUMITEM.number = 3
tb.IMAGETYPE_ISLAND_ENUMITEM.name = "ISLAND"
tb.IMAGETYPE_ISLAND_ENUMITEM.index = 3
tb.IMAGETYPE_ISLAND_ENUMITEM.number = 4
tb.IMAGETYPE_RELATIVE_WISH_ENUMITEM.name = "RELATIVE_WISH"
tb.IMAGETYPE_RELATIVE_WISH_ENUMITEM.index = 4
tb.IMAGETYPE_RELATIVE_WISH_ENUMITEM.number = 5
tb.IMAGETYPE_PURIKURA_ENUMITEM.name = "PURIKURA"
tb.IMAGETYPE_PURIKURA_ENUMITEM.index = 5
tb.IMAGETYPE_PURIKURA_ENUMITEM.number = 6
tb.IMAGETYPE_ATM_ENUMITEM.name = "ATM"
tb.IMAGETYPE_ATM_ENUMITEM.index = 6
tb.IMAGETYPE_ATM_ENUMITEM.number = 7
tb.IMAGETYPE_PHOTO_ENUMITEM.name = "PHOTO"
tb.IMAGETYPE_PHOTO_ENUMITEM.index = 7
tb.IMAGETYPE_PHOTO_ENUMITEM.number = 8
tb.IMAGETYPE_TEMPLATE_SHARE_ENUMITEM.name = "TEMPLATE_SHARE"
tb.IMAGETYPE_TEMPLATE_SHARE_ENUMITEM.index = 8
tb.IMAGETYPE_TEMPLATE_SHARE_ENUMITEM.number = 9
tb.IMAGETYPE_TEMPLATE_DOWNLOAD_ENUMITEM.name = "TEMPLATE_DOWNLOAD"
tb.IMAGETYPE_TEMPLATE_DOWNLOAD_ENUMITEM.index = 9
tb.IMAGETYPE_TEMPLATE_DOWNLOAD_ENUMITEM.number = 10
tb.IMAGETYPE_TEMPLATE_DESIGN_ENUMITEM.name = "TEMPLATE_DESIGN"
tb.IMAGETYPE_TEMPLATE_DESIGN_ENUMITEM.index = 10
tb.IMAGETYPE_TEMPLATE_DESIGN_ENUMITEM.number = 11
tb.IMAGETYPE_STORY_TELLER_POSTER_ENUMITEM.name = "STORY_TELLER_POSTER"
tb.IMAGETYPE_STORY_TELLER_POSTER_ENUMITEM.index = 11
tb.IMAGETYPE_STORY_TELLER_POSTER_ENUMITEM.number = 12
tb.IMAGETYPE_SHARE_STREET_ENUMITEM.name = "SHARE_STREET"
tb.IMAGETYPE_SHARE_STREET_ENUMITEM.index = 12
tb.IMAGETYPE_SHARE_STREET_ENUMITEM.number = 13
tb.IMAGETYPE_ENUM.name = "ImageType"
tb.IMAGETYPE_ENUM.full_name = ".ImageType"
tb.IMAGETYPE_ENUM.values = {tb.IMAGETYPE_HEAD_ENUMITEM,tb.IMAGETYPE_SHARE_ENUMITEM,tb.IMAGETYPE_FEEDBACK_ENUMITEM,tb.IMAGETYPE_ISLAND_ENUMITEM,tb.IMAGETYPE_RELATIVE_WISH_ENUMITEM,tb.IMAGETYPE_PURIKURA_ENUMITEM,tb.IMAGETYPE_ATM_ENUMITEM,tb.IMAGETYPE_PHOTO_ENUMITEM,tb.IMAGETYPE_TEMPLATE_SHARE_ENUMITEM,tb.IMAGETYPE_TEMPLATE_DOWNLOAD_ENUMITEM,tb.IMAGETYPE_TEMPLATE_DESIGN_ENUMITEM,tb.IMAGETYPE_STORY_TELLER_POSTER_ENUMITEM,tb.IMAGETYPE_SHARE_STREET_ENUMITEM}
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.name = "imageType"
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.full_name = ".GetDownloadUrlRequest.imageType"
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.number = 1
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.index = 0
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.label = 2
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.has_default_value = false
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.default_value = nil
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.enum_type = IMAGETYPE_ENUM
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.type = 14
tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD.cpp_type = 8

tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.name = "imageId"
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.full_name = ".GetDownloadUrlRequest.imageId"
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.number = 2
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.index = 1
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.label = 2
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.has_default_value = false
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.default_value = ""
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.type = 9
tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD.cpp_type = 9

GETDOWNLOADURLREQUEST_MSG.name = "GetDownloadUrlRequest"
GETDOWNLOADURLREQUEST_MSG.full_name = ".GetDownloadUrlRequest"
GETDOWNLOADURLREQUEST_MSG.filename = "ImageExtension"
GETDOWNLOADURLREQUEST_MSG.nested_types = {}
GETDOWNLOADURLREQUEST_MSG.enum_types = {}
GETDOWNLOADURLREQUEST_MSG.fields = {tb.GETDOWNLOADURLREQUEST_IMAGETYPE_FIELD, tb.GETDOWNLOADURLREQUEST_IMAGEID_FIELD}
GETDOWNLOADURLREQUEST_MSG.is_extendable = false
GETDOWNLOADURLREQUEST_MSG.extensions = {}
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.name = "imageType"
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.full_name = ".GetUploadTokenRequest.imageType"
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.number = 1
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.index = 0
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.label = 2
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.has_default_value = false
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.default_value = nil
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.enum_type = IMAGETYPE_ENUM
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.type = 14
tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD.cpp_type = 8

tb.GETUPLOADTOKENREQUEST_NUM_FIELD.name = "num"
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.full_name = ".GetUploadTokenRequest.num"
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.number = 2
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.index = 1
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.label = 1
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.has_default_value = false
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.default_value = 0
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.type = 5
tb.GETUPLOADTOKENREQUEST_NUM_FIELD.cpp_type = 1

GETUPLOADTOKENREQUEST_MSG.name = "GetUploadTokenRequest"
GETUPLOADTOKENREQUEST_MSG.full_name = ".GetUploadTokenRequest"
GETUPLOADTOKENREQUEST_MSG.filename = "ImageExtension"
GETUPLOADTOKENREQUEST_MSG.nested_types = {}
GETUPLOADTOKENREQUEST_MSG.enum_types = {}
GETUPLOADTOKENREQUEST_MSG.fields = {tb.GETUPLOADTOKENREQUEST_IMAGETYPE_FIELD, tb.GETUPLOADTOKENREQUEST_NUM_FIELD}
GETUPLOADTOKENREQUEST_MSG.is_extendable = false
GETUPLOADTOKENREQUEST_MSG.extensions = {}
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.name = "imageId"
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.full_name = ".GetUploadTokenReply.imageId"
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.number = 1
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.index = 0
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.label = 3
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.has_default_value = false
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.default_value = {}
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.type = 9
tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD.cpp_type = 9

GETUPLOADTOKENREPLY_MSG.name = "GetUploadTokenReply"
GETUPLOADTOKENREPLY_MSG.full_name = ".GetUploadTokenReply"
GETUPLOADTOKENREPLY_MSG.filename = "ImageExtension"
GETUPLOADTOKENREPLY_MSG.nested_types = {}
GETUPLOADTOKENREPLY_MSG.enum_types = {}
GETUPLOADTOKENREPLY_MSG.fields = {tb.GETUPLOADTOKENREPLY_IMAGEID_FIELD}
GETUPLOADTOKENREPLY_MSG.is_extendable = false
GETUPLOADTOKENREPLY_MSG.extensions = {}
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.name = "info"
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.full_name = ".UploadSuccessRequest.info"
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.number = 1
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.index = 0
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.label = 3
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.has_default_value = false
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.default_value = {}
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.message_type = IMAGEUPLOADSUCCESSINFO_MSG
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.type = 11
tb.UPLOADSUCCESSREQUEST_INFO_FIELD.cpp_type = 10

UPLOADSUCCESSREQUEST_MSG.name = "UploadSuccessRequest"
UPLOADSUCCESSREQUEST_MSG.full_name = ".UploadSuccessRequest"
UPLOADSUCCESSREQUEST_MSG.filename = "ImageExtension"
UPLOADSUCCESSREQUEST_MSG.nested_types = {}
UPLOADSUCCESSREQUEST_MSG.enum_types = {}
UPLOADSUCCESSREQUEST_MSG.fields = {tb.UPLOADSUCCESSREQUEST_INFO_FIELD}
UPLOADSUCCESSREQUEST_MSG.is_extendable = false
UPLOADSUCCESSREQUEST_MSG.extensions = {}
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.name = "privateDownloadUrl"
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.full_name = ".GetDownloadUrlReply.privateDownloadUrl"
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.number = 1
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.index = 0
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.label = 2
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.has_default_value = false
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.default_value = ""
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.type = 9
tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD.cpp_type = 9

GETDOWNLOADURLREPLY_MSG.name = "GetDownloadUrlReply"
GETDOWNLOADURLREPLY_MSG.full_name = ".GetDownloadUrlReply"
GETDOWNLOADURLREPLY_MSG.filename = "ImageExtension"
GETDOWNLOADURLREPLY_MSG.nested_types = {}
GETDOWNLOADURLREPLY_MSG.enum_types = {}
GETDOWNLOADURLREPLY_MSG.fields = {tb.GETDOWNLOADURLREPLY_PRIVATEDOWNLOADURL_FIELD}
GETDOWNLOADURLREPLY_MSG.is_extendable = false
GETDOWNLOADURLREPLY_MSG.extensions = {}
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.name = "imageType"
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.full_name = ".ImageUploadSuccessInfo.imageType"
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.number = 1
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.index = 0
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.label = 2
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.has_default_value = false
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.default_value = nil
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.enum_type = IMAGETYPE_ENUM
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.type = 14
tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD.cpp_type = 8

tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.name = "imageId"
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.full_name = ".ImageUploadSuccessInfo.imageId"
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.number = 2
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.index = 1
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.label = 2
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.has_default_value = false
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.default_value = ""
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.type = 9
tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD.cpp_type = 9

tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.name = "picUrl"
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.full_name = ".ImageUploadSuccessInfo.picUrl"
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.number = 3
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.index = 2
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.label = 2
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.has_default_value = false
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.default_value = ""
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.type = 9
tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD.cpp_type = 9

IMAGEUPLOADSUCCESSINFO_MSG.name = "ImageUploadSuccessInfo"
IMAGEUPLOADSUCCESSINFO_MSG.full_name = ".ImageUploadSuccessInfo"
IMAGEUPLOADSUCCESSINFO_MSG.filename = "ImageExtension"
IMAGEUPLOADSUCCESSINFO_MSG.nested_types = {}
IMAGEUPLOADSUCCESSINFO_MSG.enum_types = {}
IMAGEUPLOADSUCCESSINFO_MSG.fields = {tb.IMAGEUPLOADSUCCESSINFO_IMAGETYPE_FIELD, tb.IMAGEUPLOADSUCCESSINFO_IMAGEID_FIELD, tb.IMAGEUPLOADSUCCESSINFO_PICURL_FIELD}
IMAGEUPLOADSUCCESSINFO_MSG.is_extendable = false
IMAGEUPLOADSUCCESSINFO_MSG.extensions = {}
UPLOADSUCCESSREPLY_MSG.name = "UploadSuccessReply"
UPLOADSUCCESSREPLY_MSG.full_name = ".UploadSuccessReply"
UPLOADSUCCESSREPLY_MSG.filename = "ImageExtension"
UPLOADSUCCESSREPLY_MSG.nested_types = {}
UPLOADSUCCESSREPLY_MSG.enum_types = {}
UPLOADSUCCESSREPLY_MSG.fields = {}
UPLOADSUCCESSREPLY_MSG.is_extendable = false
UPLOADSUCCESSREPLY_MSG.extensions = {}

ATM = 7
FEEDBACK = 3
GetDownloadUrlReply = protobuf.Message(GETDOWNLOADURLREPLY_MSG)
GetDownloadUrlRequest = protobuf.Message(GETDOWNLOADURLREQUEST_MSG)
GetUploadTokenReply = protobuf.Message(GETUPLOADTOKENREPLY_MSG)
GetUploadTokenRequest = protobuf.Message(GETUPLOADTOKENREQUEST_MSG)
HEAD = 1
ISLAND = 4
ImageUploadSuccessInfo = protobuf.Message(IMAGEUPLOADSUCCESSINFO_MSG)
PHOTO = 8
PURIKURA = 6
RELATIVE_WISH = 5
SHARE = 2
SHARE_STREET = 13
STORY_TELLER_POSTER = 12
TEMPLATE_DESIGN = 11
TEMPLATE_DOWNLOAD = 10
TEMPLATE_SHARE = 9
UploadSuccessReply = protobuf.Message(UPLOADSUCCESSREPLY_MSG)
UploadSuccessRequest = protobuf.Message(UPLOADSUCCESSREQUEST_MSG)

return _G["logic.proto.ImageExtension_pb"]
