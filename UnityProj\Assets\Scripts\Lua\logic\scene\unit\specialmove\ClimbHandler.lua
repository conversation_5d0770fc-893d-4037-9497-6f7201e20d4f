module("logic.scene.unit.specialmove.ClimbHandler",package.seeall)
local ClimbHandler = class("ClimbHandler", PlayerShortActionHandler)

function ClimbHandler:onStart()
	self.unit:setNavEnable(false)
	printInfo("jump pos", self.info.x, self.info.y, self.info.z)
	self.time = ClimbHandler.time
    
	local startX, startY, startZ = self.unit:getPos()
	if not startZ then
		startZ = self.info.z
	end
	local dis =  math.abs(self.info.z-startZ)
	self.time = math.max(dis / 3, 1.5)

	self.unit:setDirection(1)
	SceneTimer:setTimer(0, self.startJump, self, false)
end


function ClimbHandler:startJump()
	self.aniHelper = self.unit.skinView:getAnimHelper()
	self.aniHelper:setAnimation("4.0_znq_panpa", true, 1)
	SoundManager.instance:playEffect(142185, self.unit.go)
	self.tween = self.unit.go.transform:DOMoveY(self.info.z, self.time)
	self.tween:SetEase(DG.Tweening.Ease.Linear)
	self.tween:OnComplete(handler(self.jumpOver, self))
end

function ClimbHandler:jumpOver()
	SoundManager.instance:stopEffect(142185, self.unit.go)
	self.tween = nil
	-- self.unit:teleport(self.info.x, self.info.y, self.info.z)
	self.unit:dispatch(UnitNotify.Arrive, self)
	self:finish()
end

function ClimbHandler:onStop()
	SceneTimer:removeTimer(self.startJump, self)
	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end
	if self.aniHelper then
		self.aniHelper:stopAnimation(1)
		self.aniHelper = nil
	end
	self.unit:dispatch(UnitNotify.Arrive, self)
	-- self.unit:teleport(self.info.x, self.info.y)
end
return ClimbHandler