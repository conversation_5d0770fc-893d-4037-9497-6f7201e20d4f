-- {excel:H活动配置表.xlsx, sheetName:export_限时宠物配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_limitPetPanel_setting", package.seeall)

local title = {id=1,actId=2,resPath=3,text1=4,text2=5,text3=6,collectionItemId=7,reward=8,luaSpecialCompNameList=9,extraParams=10,loginRewards=11}

local dataList = {
	{100, 1346, "ui/newactivity/activitywerewolfpetview.prefab", "<color=#285b94>神秘宠物出现！“熊次元”活动开启期间，在规定的捉宠地点使用高级宠物笛，有机会遇见</color><color=#d76a1e>奶噗噗（幼态）</color><color=#285b94>。</color>", "<color=#d76a1e>{1}</color>", "<color=#285b94>青木森林、奥比广场、淘宝街、奥比斯雪山、友谊庄园、奥比斯山脚。</color>", nil, nil, {"CommonLimitTimePetSpecialComp"}, nil, nil},
	{101, 0, "ui/activity/activitytimelimitpetview.prefab", "<color=#956f48>神秘宠物出现！“风华节”活动开启期间，在规定的捉宠地点使用高级宠物笛，有机会遇见</color><color=#e0773a>饕餮（幼态）、貔貅（幼态）</color><color=#956f48>，同时收集两只宠物（幼态）还可获得额外奖励！</color>", "<color=#e0773a>1月12日 05:00-2月1日 23:59</color>", "<color=#956f48>青木森林、奥比广场、淘宝街、奥比斯雪山、友谊庄园、奥比斯山脚。</color>", {19003800,19003700}, {{itemId=13002245,count=1}}, {"CommonLimitTimePetSpecialComp"}, nil, nil},
	{102, 0, "ui/newactivity/activitylimitfishview.prefab", "", "", "", nil, nil, {"CommonLimitTimePetSpecialComp"}, nil, nil},
	{103, 1347, "ui/newactivity/activityreturnlimitpetview.prefab", "<color=#d0f2fd>神秘宠物限时回归！活动开启期间，在规定的捉宠地点使用高级宠物笛，有机会遇见</color><color=#f8e9bd>星海独角鲸（幼态）</color><color=#d0f2fd>。</color>", "<color=#f8e9bd>{1}</color>", "<color=#d0f2fd>青木森林、奥比广场、淘宝街、奥比斯雪山、友谊庄园、奥比斯山脚。</color>", nil, nil, {"CommonLimitTimePetSpecialComp"}, nil, nil},
	{104, 2234, "ui/newactivity/activitywerewolfpetview_2.prefab", "<color=#ffffff>神秘宠物出现！“风华节”活动开启期间，在规定的捉宠地点使用高级宠物笛，有机会遇见</color><color=#fcdd88>四种限时宠物（幼态）</color><color=#ffffff>，同时</color><color=#fcdd88>收集四种</color><color=#ffffff>宠物和每日签到还可获得</color><color=#fcdd88>额外奖励</color><color=#ffffff>！</color>", "<color=#fcdd88>{1}</color>", "<color=#ffffff>醒神仔-青木森林\n焦绿猫-淘宝街\n发射小人-奥比广场\n卡皮巴拉小黄豚-奥比斯雪山</color>", {19006300,19006400,19006600,19006500}, {{itemId=13004613,count=1}}, {"Activity249SigninView"}, {rewardCommonIconCfg={showCount=false,rareness=false,widthAndHeght=126}}, {{itemId=14030002,count=40},{itemId=16000033,count=5},{itemId=14030002,count=40},{itemId=16000033,count=5},{itemId=16000033,count=5}}},
	{105, 2300, "ui/newactivity/activitywerewolfpetview_3.prefab", "<color=#fffdf9>神秘宠物限时回归！活动开启期间，在规定的捉宠地点使用高级宠物笛，有机会遇见</color><color=#ffec91>五彩麟龙（幼态）</color><color=#fffdf9>。</color>", "<color=#ffec91>{1}</color>", "<color=#fffdf9>青木森林、奥比广场、淘宝街、奥比斯雪山、友谊庄园、奥比斯山脚。</color>", nil, nil, {"CommonLimitTimePetSpecialComp"}, nil, nil},
}

local t_limitPetPanel_setting = {
	[100] = dataList[1],
	[101] = dataList[2],
	[102] = dataList[3],
	[103] = dataList[4],
	[104] = dataList[5],
	[105] = dataList[6],
}

t_limitPetPanel_setting.dataList = dataList
local mt
if LimitPetPanelDefine then
	mt = {
		__cname =  "LimitPetPanelDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or LimitPetPanelDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_limitPetPanel_setting