module("logic.extensions.tetris.TetrisUIController",package.seeall)

local TetrisUIController = class("TetrisUIController", BaseController)

function TetrisUIController:ctor()
    self.hasGetRewardMap = {}
    self.levelInfo = {}
    self.levelScoreMap = {}
    self.hasGetTargetReward = {}
    self.winCount = 0
    self.hasGetWinCountReward = {}
    self.matchScore = 0
    self.hasUnlockSkill = {}
    self.nowSkill = 0
    self.dailyTimes = 0
end

function TetrisUIController:onInit()
	self:registerNotify(GlobalNotify.getActivityInfoFinish, self.checkLevelRedPoint, self)
	self:registerNotify(GlobalNotify.onServerPushActivity, self._onServerPushActivity, self)
end

function TetrisUIController:onReset()
	self:unregisterNotify(GlobalNotify.getActivityInfoFinish, self.checkLevelRedPoint, self)
	self:unregisterNotify(GlobalNotify.onServerPushActivity, self._onServerPushActivity, self)
    removetimer(self.checkLevelRedPoint, self)
end

function TetrisUIController:_onServerPushActivity()
	ViewMgr.instance:close("SingleGameResultPanel")
	settimer(4,self.checkLevelRedPoint,self,false)
end

function TetrisUIController:checkLevelRedPoint()
	if ActivityFacade.instance:getActivityIsOpen(GameEnum.ActivityEnum.ACTIVITY_278) then
		local activityInfo = ActivityModel.instance:getActivityInfo(GameEnum.ActivityEnum.ACTIVITY_278)
		if activityInfo then
			local activityId = activityInfo:getActivityId()
			local redKey = "TetrisNewLevel"
			local isExist = false
			for i = 1, 7 do
				if self:isNewLevel(i) then
					isExist = true
					break
				end
			end
			RedPointController.instance:setRedIsExist(redKey, isExist)
			self:localNotify(TetrisNotify.OnLevelRedUpdate)
		end
	end
end

function TetrisUIController:openEntry()
	--ViewMgr.instance:open("TetrisAnniversaryActView")
    GamePromoteFacade.openByActivityId(278)
end

function TetrisUIController:onGetGameInfoData(msg)
    self.hasGetRewardMap = {}
    self.levelInfo = msg.levelInfo
    self.levelScoreMap = {}
    for k, v in ipairs(self.levelInfo) do
        self.levelScoreMap[v.level] = v
    end
    self.hasGetTargetReward = msg.hasGetTargetReward
    for k, v in ipairs(self.hasGetTargetReward) do
        self.hasGetRewardMap[v] = true
    end
    self.winCount = msg.winCount
    self.hasGetWinCountReward = msg.hasGetWinCountReward or {}
    self.matchScore = msg.matchScore
    self.hasUnlockSkill = msg.hasUnlockSkill
    self.nowSkill = msg.nowSkill
    self.dailyTimes = msg.dailyTimes
end

function TetrisUIController:setReward(rewardId)
	self.hasGetRewardMap[rewardId] = true
end

-- isMulti: 是否匹配模式
function TetrisUIController:getRewardState(rewardId, isMulti)
    local state = CommonIcon.State_None
    if not isMulti then
        if self.hasGetRewardMap[rewardId] then
            state = CommonIcon.State_Received
        else
            local define = TetrisConfig.getTargetRewardByRewardId(rewardId)
            local levelCfg = TetrisConfig.getLevelConfigById(define.levelId)
            -- 无尽模式奖励状态
            if not levelCfg.isPlot and levelCfg.gameRoomType == 1 then
                local levelId = define.levelId
                local score = define.needScore
                local data = self.levelScoreMap[levelId]
                if data ~= nil then
                    if data.score >= score then
                        state = CommonIcon.State_CanGet
                    end
                end
            end
        end
    else
        if table.indexof(self.hasGetWinCountReward, rewardId) then
            state = CommonIcon.State_Received
        else
            local define = TetrisConfig.getWinRewardById(rewardId)
            -- 匹配模式奖励状态
            local winCount = define.winCount
            if self.winCount >= winCount then
                state = CommonIcon.State_CanGet
            end
        end
    end
	return state
end

function TetrisUIController:isPassLevel(levelId)
	if self.levelScoreMap then
		local data = self.levelScoreMap[levelId]
		if data then
			return data.isPlotPass
		end
	end
	return false
end

function TetrisUIController:isNewLevel(level)
	local define = TetrisConfig.getLevelConfigById(level)
	local isExist = false
	local key = "Tetris_NewLevel"..level
	local openTime = TimeUtil.dateStr2TimeStamp(define.openTime,"T")
	local curTime = ServerTime.now()
	if curTime > openTime then
		local curTimeStamp = LocalStorage.instance:getValue({name = key, type = 2}, nil)
		if string.nilorempty(curTimeStamp) or (tonumber(curTimeStamp) < openTime) then
			return true
		end
	end
	return false
end

function TetrisUIController:isLevelUnlock(levelId)
	local define = TetrisConfig.getLevelConfigById(levelId)
	return TimeUtil.isNowAfter(define.openTime)
end

-- @params rewardType: 无尽模式还是多人模式，无尽模式为1，多人模式为2
function TetrisUIController:sendGetReward(rewardType, rewardId, callback)
    local func = function(changeSetId)
        if changeSetId then
            local items = ItemService.instance:popChangeSet(changeSetId)
            DialogHelper.showRewardsDlg(items, lang("获得物品"), nil, nil, false)
        end

        if rewardType == 1 then
            self:setReward(rewardId)
        elseif rewardType == 2 then
            table.insert(self.hasGetWinCountReward, rewardId)
        end
        if callback then
            callback()	
        end
        self:localNotify(TetrisNotify.OnGetReward)
    end
    if rewardType == 1 then
        Activity278Agent.instance:sendGetTetrisTargetRewardRequest(rewardId,func)
    elseif rewardType == 2 then
        Activity278Agent.instance:sendGetTetrisWinCountRewardRequest(rewardId,func)
    end
end

function TetrisUIController:getDailyTimws()
    return self.dailyTimes
end

function TetrisUIController:getWinCount()
    return self.winCount
end

function TetrisUIController:getMatchScore()
    return self.matchScore
end

function TetrisUIController:setNowSkill(skillId)
    self.nowSkill = skillId
end

function TetrisUIController:getNowSkill()
    return self.nowSkill
end

function TetrisUIController:getLevelInfo(levelId)
    return self.levelScoreMap[levelId]
end

function TetrisUIController:setViewSelectInfo(selectIndex)
    self.enterViewSelect = selectIndex
end

function TetrisUIController:getViewSelectInfo()
    return self.enterViewSelect or 1
end

TetrisUIController.instance = TetrisUIController.New()
return TetrisUIController