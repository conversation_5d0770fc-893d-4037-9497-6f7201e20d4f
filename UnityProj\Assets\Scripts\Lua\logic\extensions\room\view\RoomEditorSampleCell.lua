module("logic.extensions.room.view.RoomEditorSampleCell",package.seeall)

local RoomEditorSampleCell = class("RoomEditorSampleCell", MySampleListCell, BaseLuaComponent)

RoomEditorSampleCell.Color = {
    Red = "#FD725D",
    Normal = "#744C4C"
}

RoomEditorSampleCell.CellUrl = "ui/scene/room/edittemplateitem.prefab"
function RoomEditorSampleCell:Awake()
    RoomEditorSampleCell.super.Awake(self)
    self._sysIcon = self:getGo("sysIcon")
	self._imgCheck = self:getGo("btnCheck/img")
    self._spNum = self:getGo("spNum"):GetComponent(goutil.Type_UIImage)

end

function RoomEditorSampleCell:OnDestroy()
    RoomEditorSampleCell.super.OnDestroy(self)
    RedPointController.instance:unregisterRedPoint(self._imgNewBubble)
end

function RoomEditorSampleCell:onSetMo(mo)
    local isSelfSample = self._listView.property.isSelfSample
    self._sysIcon:SetActive(not isSelfSample)
    self._icon:SetActive(isSelfSample)
    self._numberGo:SetActive(false)
	if self._imgShare then
		self._imgShare:SetActive(false)
	end
	goutil.setActive(self._imgCheck, not isSelfSample)
	
    if isSelfSample then
        RoomEditorSampleCell.super.onSetMo(self, mo)
    else
        self._spNum.gameObject:SetActive(true)
        self._lockGo:SetActive(false)
        self._btnCheck.gameObject:SetActive(true)
        self._txtGo:SetActive(true)
        self._imgNull:SetActive(false)
        self._mo = mo
        self._nameGo:SetActive(false)
        local totalSize = 0
        local totalComform = 0
        local totalCount = #self._mo
        local collectCount = 0
        local furniture, w, h
        local ownMap = {}
        for _, config in ipairs(self._mo) do
            furniture = FurnitureConfig.getFurnitureDefine(config.furniture.id)
            ownMap[furniture.id] = (ownMap[furniture.id] or 0) + 1
            if ItemService.instance:getItemTotalNum(furniture.id) >= ownMap[furniture.id] then
                collectCount = collectCount + 1
            end
            totalComform = totalComform + furniture.comfort
            w, h = RoomConfig.getFurnitureSize(furniture.id)
            totalSize = totalSize + (w * h)
        end
        local finish = collectCount >= totalCount
        self._txtNum.text = finish and lang("已收集") or lang("{1}/{2}", collectCount, totalCount)
        Framework.ColorUtil.SetImageColor(self._spNum, RoomEditorSampleCell.Color[finish and "Normal" or "Red"])
    
        self._txtSize.text = totalSize
        self._txtComfort.text = totalComform
        IconLoader.setSpriteToImg(self._sysIcon, RoomConfig.getSampleIconUrl(self._mo[1].id))
        RedPointController.instance:unregisterRedPoint(self._imgNewBubble)
        RedPointController.instance:registerRedPoint(self._imgNewBubble, {RoomController.SampleRedPrefix .. self._mo[1].id})
    end
end

function RoomEditorSampleCell:hasSave()
    if self._listView.property.isSelfSample then
        return RoomEditorSampleCell.super.hasSave(self)
    else
        return true
    end
end

function RoomEditorSampleCell:isLock()
    if self._listView.property.isSelfSample then
        return RoomEditorSampleCell.super.isLock(self)
    else
        return false
    end
end

function RoomEditorSampleCell:_onClickInfo()
    if self._listView.property.isSelfSample then
        return RoomEditorSampleCell.super._onClickInfo(self)
    else
        FurnitureSampleView.show(self._mo, nil, true, self._mo.islandType)
    end
end

function RoomEditorSampleCell:_onClickSelf()
	if self._inCD then return end
    RoomEditorSampleCell.super._onClickSelf(self)
	self._inCD = true
	SceneTimer:setTimer(1, function()
		self._inCD = false
	end, nil, false)
end

return RoomEditorSampleCell