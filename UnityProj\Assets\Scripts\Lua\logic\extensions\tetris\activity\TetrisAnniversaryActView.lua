module("logic.extensions.tetris.activity.TetrisAnniversaryActView",package.seeall)
---@class TetrisAnniversaryActView
local TetrisAnniversaryActView = class("TetrisAnniversaryActView",ViewComponent)

local TetrisSingleLevelId = 8 --俄罗斯方块单人关卡id

function TetrisAnniversaryActView:buildUI()
	self._btnClose = self:getBtn("btnclose/btnClose")
	self._btnHelp = self:getBtn("btnhelp")
	self._btnRoom = self:getBtn("btnRoom")
	self._btnRank = self:getBtn("btnRank")
	self._btnLevel1 = self:getBtn("leftContent/levelitem1")
	self._btnLevel2 = self:getBtn("leftContent/levelitem2")

	self._goLevelItemSingle = self:getGo("leftContent/levelitem1")
	self._goLevelItemMulti = self:getGo("leftContent/levelitem2")

	self._txtTodayGain = self:getText("rightTop/txtRatioNum")

	self._goRight_1 = self:getGo("rightGo_1")
	self._goRight_2 = self:getGo("rightGo_2")

	self._awardContent = self:getGo("rightGo_1/listview/rightContent")
	self._awardContent1 = self:getGo("rightGo_2/listview/rightContent")

	self._awardGo_1 = self:getGo("rightGo_1/awardGo")
	self._awardGo_2 = self:getGo("rightGo_2/awardGo")

	self._btnPlay_1 = self:getBtn("rightGo_1/btnPlay")
	self._btnPlay_2 = self:getBtn("rightGo_2/btnPlay")

	local matchingGo = self:getGo("matchingGo")
	self._matchingComp = GameRoomMatchComp.New(matchingGo, self)
	self.gameType = GameEnum.GameRoomType.TETRIS

	self._txtDesc = self:getText("txtGo/txtDesc")
	self._txtMaxScore = self:getText("txtGo/txtMaxScore")

	self._txtPkScore = self:getText("txtGo/txtPkScore")

	self._iconList_1 = {}
	self._iconList_2 = {}
	self._rewardCellList = {}
	self._rewardCellList_1 = {}

	self._redPointGos = {}
	self._redPointGos[1] = self:getGo("leftContent/levelitem1/imgRedPoint")
	self._redPointGos[2] = self:getGo("leftContent/levelitem2/imgRedPoint")

	self._txtDailyScore = self:getText("rightGo_1/txtTips")
	self._txtDailyScore1 = self:getText("rightGo_2/txtTips")

	self._goImgSign = self:getGo("rightTop/imgSign")

end

function TetrisAnniversaryActView:bindEvents()
	self._btnLevel1:AddClickListener(self._onSelectLevel, self, {1})
	self._btnLevel2:AddClickListener(self._onSelectLevel, self, {2})
	self._btnPlay_1:AddClickListener(self._onClickBtnPlay, self)
	self._btnPlay_2:AddClickListener(self._onClickBtnPlay, self)
	self._btnClose:AddClickListener(self._onClickBtnClose, self)
	self._btnHelp:AddClickListener(self._onHelpBtnClose, self)
	self._btnRank:AddClickListener(self._onRankingBtnClick, self)
	self._btnRoom:AddClickListener(self._onRoomBtnClick, self)
end

function TetrisAnniversaryActView:onEnter()
	Activity278Agent.instance:sendGetTetrisGameInfoRequest(handler(self.onGetInfo, self))
	GameRoomController.instance:registerLocalNotify(GameRoomDefine.Notify_OnMatchStateChanged,self.onMatchStateChange,self)
	TetrisUIController.instance:registerLocalNotify(TetrisNotify.OnGetReward, self.checkRedPoint, self)

	self:localNotify(TetrisNotify.OnGetGameInfo)
	local limitConfig = ActivityConfig.getActivityLimitConfigById(TetrisConfig.gainLimitId)
	local limitInfo = ActivityFacade.instance:getGainLimitModel({TetrisConfig.gainLimitId})
	local dailyLimit = (limitInfo and limitInfo[1]) and limitInfo[1].dailyLimit or 0
	local totalDailyLimit = ActivityConfig.getGainLimitTotalDailyLimitById(TetrisConfig.gainLimitId)
	self._txtTodayGain.text = string.format("<color=#ffc545>%d</color>/%d",dailyLimit or 0,totalDailyLimit or 0)
	IconLoader.setIconForItem(self._goImgSign,limitConfig.itemid)

	local selectIndex = TetrisUIController.instance:getViewSelectInfo()
	self:_onSelectLevel({selectIndex})
end

function TetrisAnniversaryActView:onExit()
	GameRoomController.instance:unregisterLocalNotify(GameRoomDefine.Notify_OnMatchStateChanged,self.onMatchStateChange,self)
	TetrisUIController.instance:unregisterLocalNotify(TetrisNotify.OnGetReward, self.checkRedPoint, self)
	self:_setBtnGrayOrHighlight(false)
	if self._matchingComp then
		self._matchingComp:onClickCancel()
		self._matchingComp:onExit()
	end
	
end

function TetrisAnniversaryActView:unbindEvents()
	self._btnLevel1:RemoveClickListener()
	self._btnLevel2:RemoveClickListener()
	self._btnPlay_1:RemoveClickListener()
	self._btnPlay_2:RemoveClickListener()
	self._btnClose:RemoveClickListener()
	self._btnHelp:RemoveClickListener()
	self._btnRank:RemoveClickListener()
	self._btnRoom:RemoveClickListener()
end

function TetrisAnniversaryActView:destroyUI()
	for k,v in ipairs(self._rewardCellList) do
		v:dispose()
	end
	self._rewardCellList = nil

	for k,v in ipairs(self._rewardCellList_1) do
		v:dispose()
	end
	self._rewardCellList_1 = nil

	for k,v in ipairs(self._iconList_1) do
		CommonIconMgr.instance:returnCommonIcon(v)
	end

	for k,v in ipairs(self._iconList_2) do
		CommonIconMgr.instance:returnCommonIcon(v)
	end

	self._iconList_1 = nil
	self._iconList_2 = nil
end

function TetrisAnniversaryActView:onGetInfo(msg)
	TetrisUIController.instance:onGetGameInfoData(msg)
	if not self._curLevel then 
		self._curLevel = 1
	end

	self:refreshView(self._curLevel)
	self:setLevelInfo(1)
	self:setLevelInfo(2)
end

function TetrisAnniversaryActView:_onSelectLevel(params)
	local index = params[1]
	self._goRight_1:SetActive(index == 1)
	self._goRight_2:SetActive(index == 2)
	self:refreshView(index)

	self:setLevelSelect(1)
	self:setLevelSelect(2)

	RedPointController.instance:setLastClick("Tetris_Reward", ServerTime.now())
end

function TetrisAnniversaryActView:refreshView(index)
	self._curLevel = index
	local defines --右侧奖励列表
	local define  --右下奖励
	if index == 1 then 
		defines	= TetrisConfig.getTargetRewardByLevelId(TetrisSingleLevelId)
		define = TetrisConfig.getLevelConfigById(TetrisSingleLevelId)

		self._txtDesc.text = define.levelDes
		local levelInfo = TetrisUIController.instance:getLevelInfo(TetrisSingleLevelId)
		local score = levelInfo ~= nil and levelInfo.score or 0
		self._txtMaxScore.text = score
		self._txtDailyScore.text = lang("得分达到<color=#dba03b>{1}</color>", define.needScore)
	else 
		defines = TetrisConfig.getAllWinReward()
		define = TetrisConfig.getBattleRewardByWinType(1)

		self._txtDesc.text = TetrisConfig.getLevelConfigById(9).levelDes
		local maxDailyTimes = TetrisConfig.getCommonCfgByKey("Act448MaxEffectDailyTimes")
		self._txtPkScore.text =  string.format("%d/%d", TetrisUIController.instance:getDailyTimws(), tonumber(maxDailyTimes))
		self._txtDailyScore1.text = lang("参与次数<color=#dba03b>1</color>")
	end
	
	self._txtMaxScore.gameObject:SetActive(index == 1)
	self._txtPkScore.gameObject:SetActive(index == 2)
	 
	local awardContent = index == 1 and self._awardContent or self._awardContent1
	local rewardCellList = index == 1 and self._rewardCellList or self._rewardCellList_1
	for k,v in ipairs(defines) do
		local cell = rewardCellList[k]
		if not cell then
			local go = goutil.findChild(awardContent, "scorerewarditem"..k)
			goutil.setActive(go, true)
			cell = TetrisLevelRewardItem.New(go)
			table.insert(rewardCellList, cell)
		end
		cell:setRewardType(index)
		cell:onSetMo(v)
	end
	
	local items = define.reward
	local goAward = index == 1 and self._awardGo_1 or self._awardGo_2
	local iconList = index == 1 and self._iconList_1 or self._iconList_2
	for i=1,#items do
		if not iconList[i] then
			local icon = CommonIconMgr.instance:fetchCommonIcon()
			goutil.addChildToParent(icon:getPrefab(), goAward)
			icon:setWidthAndHeight(73):showRarenessGo(true):showSpecial(true)
			iconList[i] = icon
		end
		iconList[i]:buildData(items[i])
	end

	self:checkRedPoint()
end

function TetrisAnniversaryActView:_onClickBtnPlay()
	if not TetrisController.instance:checkActivityIsOpen(true) then
		return
	end

	if self:_checkIsMatching() then
		return
	end

	TetrisUIController.instance:setViewSelectInfo(self._curLevel)

	if self._curLevel == 1 then 
		local func = function(roomId)
			GameCentreController.instance:joinRoom(GameEnum.GameRoomType.TETRIS, roomId)
		end
	
		TetrisUIModel.instance:setNowLevelId(TetrisSingleLevelId)
		GameRoomAgent.instance:sendGameRoomSingleStartRequest(GameEnum.GameRoomType.TETRIS, 1, TetrisSingleLevelId, func)
	else
		self:tryStartGame()
	end
end

function TetrisAnniversaryActView:_onClickBtnClose()
	self:close()
end

function TetrisAnniversaryActView:_onHelpBtnClose()
	-- local title = lang("俄罗斯方块活动规则")
	-- local content = lang("俄罗斯方块活动规则描述")
	-- ViewMgr.instance:open("ActivityRuleCommon", title, content)
	GameRoomFacade.showHelpPanel(9)
end

function TetrisAnniversaryActView:_onRankingBtnClick()
	printWarn(self._curLevel)
	if self._curLevel == 1 then 
		--TetrisRankingView
		ViewMgr.instance:open("CommonRankingView",GameEnum.ActivityEnum.ACTIVITY_278,{
				GameEnum.RankingTypeEnum.ACTIVITY_278_LEVEL_1_RANKING,
				GameEnum.RankingTypeEnum.ACTIVITY_278_LEVEL_1_RANKING
			},
			{
				CommonRankingNotify.rankingConfigs.TetrisMatchList,
				CommonRankingNotify.rankingConfigs.TetrisMatchFriendList
			},lang("排行榜"))
	else
		ViewMgr.instance:open("CommonRankingView",GameEnum.ActivityEnum.ACTIVITY_278,{
			GameEnum.RankingTypeEnum.ACTIVITY_278_MATCH_SCORE_RANKING,
			GameEnum.RankingTypeEnum.ACTIVITY_278_MATCH_SCORE_RANKING
		},
		{
			CommonRankingNotify.rankingConfigs.TetrisMatchList,
			CommonRankingNotify.rankingConfigs.TetrisMatchFriendList
		},lang("匹配排行榜"))
	end
end

function TetrisAnniversaryActView:_onRoomBtnClick()
	self._matchingComp:onClickCancel()
	ViewMgr.instance:open("GameRoomListPanel", GameEnum.GameRoomType.TETRIS)
end

function TetrisAnniversaryActView:tryStartGame()
	TetrisUIModel.instance:setNowLevelId(self._curLevel)
	--BreakoutGameController.instance:setLastSceneId(SceneManager.instance:getCurSceneId())
	self:_setBtnGrayOrHighlight(true)
	GameRoomMatchController.instance:clearMatchSuccState()
	GameRoomMatchController.instance:tryBeginMatch(
		self.gameType or GameEnum.GameRoomType.TETRIS,
		handler(self.onStartMatch, self)
	)
end

function TetrisAnniversaryActView:onCancelMatch()
	self:_setBtnGrayOrHighlight(false)
end

function TetrisAnniversaryActView:onClickCancel()
	local matchingComp = self._matchingComp
	local waitingType = matchingComp:getWaitingType()
	if waitingType then
		self._matchingComp:onClickCancel()
	end
end

function TetrisAnniversaryActView:_setBtnGrayOrHighlight(isGray)
	Framework.UIGray.SetGray(self._btnPlay_2.gameObject,isGray,true,0.2)
end

function TetrisAnniversaryActView:onStartMatch(msg)
	self._matchingComp:onStartMatch(msg)
end

function TetrisAnniversaryActView:_checkIsMatching()
	local matchingComp = self._matchingComp
	local waitingType = matchingComp:getWaitingType()
	if waitingType then
		if waitingType == GameRoomDefine.gameMatch_type.match then
			FlyTextManager.instance:showFlyText(lang("正在匹配中，请取消再试"))
			return true
		elseif waitingType == GameRoomDefine.gameMatch_type.quickJoin then
			FlyTextManager.instance:showFlyText(lang("正在匹配中，请取消再试"))
		end
		return true
	end
	return false
end

function TetrisAnniversaryActView:setLevelInfo(index)
	local go = index == 1 and self._goLevelItemSingle or self._goLevelItemMulti

	self:setLevelSelect(index)
	local score
	if index == 1 then 
		local levelInfo = TetrisUIController.instance:getLevelInfo(TetrisSingleLevelId)
		printWarn(levelInfo and levelInfo.score)
		score = levelInfo ~= nil and levelInfo.score or 0
		goutil.findChild(go, "selectGo/txtMaxScoreText"):SetActive(score > 0)
		goutil.findChild(go, "unSelectGo/txtMaxScoreText"):SetActive(score > 0)
		goutil.findChild(go, "selectGo/txtHint"):SetActive(score <= 0)
		goutil.findChild(go, "unSelectGo/txtHint"):SetActive(score <= 0)
		goutil.findChild(go, "selectGo/txtScore"):SetActive(score > 0)
		goutil.findChild(go, "unSelectGo/txtScore"):SetActive(score > 0)
	else
		score = TetrisUIController.instance:getMatchScore()
		local winCount = TetrisUIController.instance:getWinCount()
		goutil.findChild(go, "selectGo/txtMaxScoreText"):SetActive(winCount > 0)
		goutil.findChild(go, "unSelectGo/txtMaxScoreText"):SetActive(winCount > 0)
		goutil.findChild(go, "selectGo/txtHint"):SetActive(winCount <= 0)
		goutil.findChild(go, "unSelectGo/txtHint"):SetActive(winCount <= 0)
		goutil.findChild(go, "selectGo/txtScore"):SetActive(winCount > 0)
		goutil.findChild(go, "unSelectGo/txtScore"):SetActive(winCount > 0)
	end

	
	goutil.findChildTextComponent(go, "selectGo/txtScore").text = score
	goutil.findChildTextComponent(go, "unSelectGo/txtScore").text = score
end

function TetrisAnniversaryActView:setLevelSelect(index)
	local go = index == 1 and self._goLevelItemSingle or self._goLevelItemMulti
	local isSelect = self._curLevel == index
	goutil.findChild(go, "selectGo"):SetActive(isSelect)
	goutil.findChild(go, "unSelectGo"):SetActive(not isSelect)
end

function TetrisAnniversaryActView:checkRedPoint()
	local allRewardCfg = TetrisConfig.getAllTargetReward()
	local isShowRed = false
	for k,v in ipairs(allRewardCfg) do
        local levelCfg = TetrisConfig.getLevelConfigById(v.levelId)
        if not levelCfg.isPlot and levelCfg.gameRoomType == 1 then
            if TetrisUIController.instance:getRewardState(v.rewardId) == CommonIcon.State_CanGet then
                isShowRed = true
                break
            end
        end
	end
	goutil.setActive(self._redPointGos[1], isShowRed)
	
	isShowRed = false
	local defines2 = TetrisConfig.getAllWinReward()
	for k,v in ipairs(defines2) do
		if TetrisUIController.instance:getRewardState(v.rewardId, true) == CommonIcon.State_CanGet then
			isShowRed = true
			break
		end
	end
	goutil.setActive(self._redPointGos[2], isShowRed)
end

function TetrisAnniversaryActView:onMatchStateChange(state)
	if state == GameEnum.WerewolfMatchState.MATCH_REMATCH then
		FlyTextManager.instance:showFlyText(lang("部分玩家超时未准备，将重新进行匹配"))
		GameRoomMatchController.instance:clearMatchSuccState()
		GameRoomMatchController.instance:retryBeginMatch(
			self.gameType or GameEnum.GameRoomType.TETRIS,
			handler(self.onStartMatch, self)
		)
	elseif state == GameEnum.WerewolfMatchState.MATCH_ALL_CONFRIM then
		self:close()
		ViewMgr.instance:close("GamePromoteTab") 
		ToyHouseGotoUtil.closeMap()
		ViewMgr.instance:clearBackStack()
	else
		self._matchingComp:onMatchStateChange(state)
	end
	if state == GameEnum.WerewolfMatchState.MATCH_OVERTIME_FAIL or state == GameEnum.WerewolfMatchState.MATCH_CONFRIM_OVER then
		self:_setBtnGrayOrHighlight(false)
	end
end

return TetrisAnniversaryActView