module("logic.extensions.task.facade.TaskFacade", package.seeall)

local TaskFacade = class("TaskFacade", BaseFacade)

function TaskFacade:ctor()
end
--向服务器读取任务信息
--@param taskTypes 数组,元素为GameEnum.TaskTypeEnum
--@param getTaskInfoHandler 更新完成回调
function TaskFacade:loadTaskInfo(taskTypeList, getTaskInfoHandler)
	TaskAgent.instance:sendTaskInfoRequest(taskTypeList, getTaskInfoHandler)
end

function TaskFacade:isTaskDoing(taskId)
	if TaskModel.instance:isLocalMode() then
		return false
	end
	return TaskModel.instance:isTaskDoing(taskId)
end

function TaskFacade:isTaskFinish(taskId)
	if TaskModel.instance:isLocalMode() then
		return true
	end
	return TaskModel.instance:isTaskCompleted(taskId)
end

function TaskFacade:isTaskStepFinish(taskStepId)
	if TaskModel.instance:isLocalMode() then
		return true
	end
	return TaskModel.instance:isTaskStepCompleted(taskStepId)
end
--- 查询任务步骤的目标是否全部完成
---@param taskStepId int 步骤id
function TaskFacade:isTaskStepObjectiveCompleted(taskStepId)
	if self:isTaskStepFinish(taskStepId) then
		return true
	end
	return TaskStepController.instance:isAllObjectiveCompleted(taskStepId)
end

function TaskFacade:isTaskStepDoing(taskStepId)
	return TaskModel.instance:isTaskStepDoing(taskStepId)
end

function TaskFacade:showTaskFinishPanelByCI(
	taskName,
	npcId,
	npcAnimationName,
	taskDesc,
	changeSetId,
	onEndHandler,
	onEndHandlerObj,
	titleName)
	local items = ItemService.instance:popChangeSet(changeSetId)
	self:showTaskFinishPanel(taskName, npcId, npcAnimationName, taskDesc, items, onEndHandler, onEndHandlerObj, titleName)
end

function TaskFacade:showTaskFinishPanel(
	taskName,
	npcId,
	npcAnimationName,
	taskDesc,
	items,
	onEndHandler,
	onEndHandlerObj,
	titleName)
	local extData = {
		npcId = npcId,
		npcAni = npcAnimationName,
		taskDesc = taskDesc,
		titleName = titleName
	}
	ViewMgr.instance:open("TaskFinish", taskName, items, extData, onEndHandler, onEndHandlerObj)
end

function TaskFacade:showTaskPanel(taskId, tabIndex)
	tabIndex = tabIndex or TaskObjectivePanel.Tab_Business
	ViewMgr.instance:open("TaskObjectivePanel", tabIndex, taskId)
end

function TaskFacade:isInStoryMode()
	return TaskController.instance:isInStory()
end

function TaskFacade:isInDialogueMode()
	return TaskController.instance:isInDialogue()
end

function TaskFacade:isNewbieTaskFinish()
	return TaskFacade.instance:isTaskFinish(TaskSetting.LastNewbieTaskId)
	-- return true
end
TaskFacade.viewMode = {}
TaskFacade.viewMode.taskMode = 1
TaskFacade.viewMode.loginMode = 2

function TaskFacade:playMovie(movieName, isSkip, startHandler, finishHandler, sendEvent)
	self.movieName = movieName
	self.isSkip = isSkip
	self.startHandler = startHandler
	self.finishHandler = finishHandler
	self.sendEvent = sendEvent
	ViewMgr.instance:open("VideoSkipView",self.startHandler, self.isSkip, self.sendEvent,self.finishHandler,movieName)
end

function TaskFacade:SkipPvEx()
	self:_onMovieEnd()
end

function TaskFacade:_onMovieEnd()
	TaskFacade.instance:closeMovie()
	ViewMgr.instance:close("VideoSkipView")
	ViewMgr.instance:close("LoginVideoView")
	SceneRoot2DMgr.instance:getSceneUIRoot():GetComponent("Canvas").enabled = true
	local curScene = SceneManager.instance:getCurScene()
	if curScene and curScene.tipsMgr then
		curScene.tipsMgr:_handleAllSceneTipsActiveState(true)
		curScene.tipsMgr:_handleViewMgrFullScreenHide()
	end
	CommonHUDFacade.instance:showHUD("TaskMovie")
	TaskUtil.BlockClick(false, "PlayMovie")


	local sceneManager = SceneManager.instance:getCurScene()
	if sceneManager then
		local unitMgr = sceneManager:getUnitMgr()
		local sameUnits = unitMgr:getUnits(SceneUnitType.Player)
		if sameUnits then
			for _,unit in ipairs(sameUnits) do
				SceneManager.instance:getCurScene():getComponent(ScenePetMgr):setUserFollowerVisible(unit.id, true)
				unitMgr:setUnitVisible(unit,true)
			end
			--隐藏宠物icon
			self.tipsMgr = SceneManager.instance:getCurScene().tipsMgr
			if self.tipsMgr ~= nil then
				self.tipsMgr:showUnitGuide(true)
			end
		end
	end
	GlobalDispatcher:removeListener(GlobalNotify.ExitGame, self._onSceneReload, self)
	if self.finishHandler ~= nil then
		self.finishHandler()
		self.finishHandler = nil
	end
	self.sendEvent = nil
end

function TaskFacade:_onSceneReload()
	GlobalDispatcher:removeListener(GlobalNotify.ExitGame, self._onSceneReload, self)
	SceneRoot2DMgr.instance:getSceneUIRoot():GetComponent("Canvas").enabled = true
	CommonHUDFacade.instance:showHUD("DreamLikeFacade")
	TaskFacade.instance:closeMovie()
	TaskUtil.BlockClick(false, "PlayMovie")
	local sceneManager = SceneManager.instance:getCurScene()
	if sceneManager then
		local unitMgr = sceneManager:getUnitMgr()
		local sameUnits = unitMgr:getUnits(SceneUnitType.Player)
		if sameUnits then
			for _,unit in ipairs(sameUnits) do
				SceneManager.instance:getCurScene():getComponent(ScenePetMgr):setUserFollowerVisible(unit.id, true)
				unitMgr:setUnitVisible(unit,true)
			end
			--隐藏宠物icon
			self.tipsMgr = SceneManager.instance:getCurScene().tipsMgr
			if self.tipsMgr ~= nil then
				self.tipsMgr:showUnitGuide(true)
			end
		end
	end
end

TaskFacade.instance = TaskFacade.New()
return TaskFacade
