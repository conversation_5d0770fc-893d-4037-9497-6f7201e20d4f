-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity464Extension_pb", package.seeall)


local tb = {}
GET464FIRSTRECHARGEREWARDSREQUEST_MSG = protobuf.Descriptor()
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD = protobuf.FieldDescriptor()
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD = protobuf.FieldDescriptor()
GET464FIRSTRECHARGEREWARDSREPLY_MSG = protobuf.Descriptor()
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
GET464FIRSTRECHARGEINFOREQUEST_MSG = protobuf.Descriptor()
GET464FIRSTRECHARGEINFOREPLY_MSG = protobuf.Descriptor()
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD = protobuf.FieldDescriptor()
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD = protobuf.FieldDescriptor()
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD = protobuf.FieldDescriptor()
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD = protobuf.FieldDescriptor()
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD = protobuf.FieldDescriptor()

tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.name = "themeId"
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.full_name = ".Get464FirstRechargeRewardsRequest.themeId"
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.number = 1
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.index = 0
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.label = 2
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.default_value = 0
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.type = 5
tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD.cpp_type = 1

tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.name = "day"
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.full_name = ".Get464FirstRechargeRewardsRequest.day"
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.number = 2
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.index = 1
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.label = 2
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.default_value = 0
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.type = 5
tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD.cpp_type = 1

GET464FIRSTRECHARGEREWARDSREQUEST_MSG.name = "Get464FirstRechargeRewardsRequest"
GET464FIRSTRECHARGEREWARDSREQUEST_MSG.full_name = ".Get464FirstRechargeRewardsRequest"
GET464FIRSTRECHARGEREWARDSREQUEST_MSG.filename = "Activity464Extension"
GET464FIRSTRECHARGEREWARDSREQUEST_MSG.nested_types = {}
GET464FIRSTRECHARGEREWARDSREQUEST_MSG.enum_types = {}
GET464FIRSTRECHARGEREWARDSREQUEST_MSG.fields = {tb.GET464FIRSTRECHARGEREWARDSREQUEST_THEMEID_FIELD, tb.GET464FIRSTRECHARGEREWARDSREQUEST_DAY_FIELD}
GET464FIRSTRECHARGEREWARDSREQUEST_MSG.is_extendable = false
GET464FIRSTRECHARGEREWARDSREQUEST_MSG.extensions = {}
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.full_name = ".Get464FirstRechargeRewardsReply.changeSetId"
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.number = 1
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.index = 0
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.label = 1
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.default_value = 0
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.type = 5
tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD.cpp_type = 1

GET464FIRSTRECHARGEREWARDSREPLY_MSG.name = "Get464FirstRechargeRewardsReply"
GET464FIRSTRECHARGEREWARDSREPLY_MSG.full_name = ".Get464FirstRechargeRewardsReply"
GET464FIRSTRECHARGEREWARDSREPLY_MSG.filename = "Activity464Extension"
GET464FIRSTRECHARGEREWARDSREPLY_MSG.nested_types = {}
GET464FIRSTRECHARGEREWARDSREPLY_MSG.enum_types = {}
GET464FIRSTRECHARGEREWARDSREPLY_MSG.fields = {tb.GET464FIRSTRECHARGEREWARDSREPLY_CHANGESETID_FIELD}
GET464FIRSTRECHARGEREWARDSREPLY_MSG.is_extendable = false
GET464FIRSTRECHARGEREWARDSREPLY_MSG.extensions = {}
GET464FIRSTRECHARGEINFOREQUEST_MSG.name = "Get464FirstRechargeInfoRequest"
GET464FIRSTRECHARGEINFOREQUEST_MSG.full_name = ".Get464FirstRechargeInfoRequest"
GET464FIRSTRECHARGEINFOREQUEST_MSG.filename = "Activity464Extension"
GET464FIRSTRECHARGEINFOREQUEST_MSG.nested_types = {}
GET464FIRSTRECHARGEINFOREQUEST_MSG.enum_types = {}
GET464FIRSTRECHARGEINFOREQUEST_MSG.fields = {}
GET464FIRSTRECHARGEINFOREQUEST_MSG.is_extendable = false
GET464FIRSTRECHARGEINFOREQUEST_MSG.extensions = {}
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.name = "rechargeNum"
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.full_name = ".Get464FirstRechargeInfoReply.rechargeNum"
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.number = 1
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.index = 0
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.label = 1
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.default_value = 0
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.type = 5
tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD.cpp_type = 1

tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.name = "signNum"
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.full_name = ".Get464FirstRechargeInfoReply.signNum"
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.number = 2
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.index = 1
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.label = 1
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.default_value = 0
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.type = 5
tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD.cpp_type = 1

tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.name = "rewardBitMark"
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.full_name = ".Get464FirstRechargeInfoReply.rewardBitMark"
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.number = 3
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.index = 2
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.label = 1
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.default_value = 0
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.type = 5
tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD.cpp_type = 1

tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.name = "themeId"
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.full_name = ".Get464FirstRechargeInfoReply.themeId"
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.number = 4
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.index = 3
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.label = 1
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.default_value = 0
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.type = 5
tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD.cpp_type = 1

tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.name = "act124State"
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.full_name = ".Get464FirstRechargeInfoReply.act124State"
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.number = 5
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.index = 4
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.label = 2
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.has_default_value = false
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.default_value = false
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.type = 8
tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD.cpp_type = 7

GET464FIRSTRECHARGEINFOREPLY_MSG.name = "Get464FirstRechargeInfoReply"
GET464FIRSTRECHARGEINFOREPLY_MSG.full_name = ".Get464FirstRechargeInfoReply"
GET464FIRSTRECHARGEINFOREPLY_MSG.filename = "Activity464Extension"
GET464FIRSTRECHARGEINFOREPLY_MSG.nested_types = {}
GET464FIRSTRECHARGEINFOREPLY_MSG.enum_types = {}
GET464FIRSTRECHARGEINFOREPLY_MSG.fields = {tb.GET464FIRSTRECHARGEINFOREPLY_RECHARGENUM_FIELD, tb.GET464FIRSTRECHARGEINFOREPLY_SIGNNUM_FIELD, tb.GET464FIRSTRECHARGEINFOREPLY_REWARDBITMARK_FIELD, tb.GET464FIRSTRECHARGEINFOREPLY_THEMEID_FIELD, tb.GET464FIRSTRECHARGEINFOREPLY_ACT124STATE_FIELD}
GET464FIRSTRECHARGEINFOREPLY_MSG.is_extendable = false
GET464FIRSTRECHARGEINFOREPLY_MSG.extensions = {}

Get464FirstRechargeInfoReply = protobuf.Message(GET464FIRSTRECHARGEINFOREPLY_MSG)
Get464FirstRechargeInfoRequest = protobuf.Message(GET464FIRSTRECHARGEINFOREQUEST_MSG)
Get464FirstRechargeRewardsReply = protobuf.Message(GET464FIRSTRECHARGEREWARDSREPLY_MSG)
Get464FirstRechargeRewardsRequest = protobuf.Message(GET464FIRSTRECHARGEREWARDSREQUEST_MSG)

return _G["logic.proto.Activity464Extension_pb"]
