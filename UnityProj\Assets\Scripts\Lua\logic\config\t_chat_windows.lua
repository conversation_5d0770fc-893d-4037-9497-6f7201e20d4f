-- {excel:J交互式物品配置.xlsx, sheetName:export_聊天泡泡}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_chat_windows", package.seeall)

local title = {id=1,subType=2,name=3,showDate=4,sortId=5,quality=6,desc=7,useCondition=8,gainWayText=9}

local dataList = {
	{30000001, 1, "默认气泡框", nil, 99999, 1, "初始气泡框", 0, "默认获得"},
	{30005001, 3, "红宝石气泡框", nil, 1, 5, "红宝石会员的闪耀的象征！", 1, "红宝石获得"},
	{30005002, 2, "幽灵之星", nil, 2, 4, "睁开眼睛吧！我知道你没睡着，在被窝里发抖呢！", 0, "幽灵熊集卡获得"},
	{30005003, 2, "小尾巴告急", "2024-07-25T05:00:00", 3, 4, "天呐，我看不到我可爱的小尾巴了，在哪里在哪里！", 0, "萌二集卡获得"},
	{30005004, 2, "人鱼故梦", "2024-09-27T05:00:00", 4, 4, "海中摇曳的泡影，如同他心中遥远而朦胧的帝国故梦。", 0, "心镜迷影获得"},
	{30005005, 2, "小泡芙羞羞", "2024-10-31T05:00:00", 5, 4, "哎呀，被你看到我了，好……好害羞……", 0, "小泡芙集卡获得"},
	{30005006, 2, "绘彩红耳兔", "2025-02-06T05:00:00", 6, 4, "红色的兔耳帽里，包裹着它那温柔的笑。", 0, "Starbliss集卡获得"},
	{30005007, 2, "猪米的幸福", "2025-05-01T05:00:00", 7, 4, "有灵感就出去画画，无聊就去找朋友聚聚，累了就睡个觉……幸福就是这么简单。", 0, "添点米花获得"},
	{30005008, 2, "甜蜜牙牙乐", "2025-05-22T05:00:00", 8, 4, "认真刷牙的小熊会拥有一口健康又美丽的牙齿！", 0, "小牙仙获得"},
	{30005009, 2, "甜蜜告白", "2025-07-24T05:00:00", 9, 4, "你是我最想公之于众的甜蜜小秘密！", 0, "优米鹿获得"},
}

local t_chat_windows = {
	[30000001] = dataList[1],
	[30005001] = dataList[2],
	[30005002] = dataList[3],
	[30005003] = dataList[4],
	[30005004] = dataList[5],
	[30005005] = dataList[6],
	[30005006] = dataList[7],
	[30005007] = dataList[8],
	[30005008] = dataList[9],
	[30005009] = dataList[10],
}

t_chat_windows.dataList = dataList
local mt
if ChatWindowsDefine then
	mt = {
		__cname =  "ChatWindowsDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or ChatWindowsDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_chat_windows