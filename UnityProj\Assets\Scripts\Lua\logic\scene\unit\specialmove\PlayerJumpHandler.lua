module("logic.scene.unit.specialmove.PlayerJumpHandler",package.seeall)
local PlayerJumpHandler = class("PlayerJumpHandler", PlayerShortActionHandler)

function PlayerJumpHandler:onStart()

	self.unit:lookTo(self.info.x, self.info.y)	
	self.unit:setNavEnable(false)
	if not self.info.z then 
		self.info.z = GameUtils.GetPosYOnGround(self.info.x, self.info.y)
	end
	printInfo("jump pos", self.info.x, self.info.y, self.info.z)
	local startX, startY, startZ = self.unit:getPos()
	if not startZ then
		startZ = self.info.z
	end
	local dis = math.sqrt((self.info.x-startX)^2+(self.info.y-startY)^2 + (self.info.z-startZ)^2)
	self.time = dis / 8

	self.aniHelper = self.unit.skinView:getAnimHelper()
	self.aniHelper:setAnimation("jump_1", false, 1)
	self.aniHelper:addAnimation("jump_3", false, 1, self.time - 0.3)
	self.aniHelper:addCustomEventCallback("jump_1", "start", self.startJump, self, true)	
	-- self.aniHelper:addCompleteCallback("jump_3", self.jumpOver, self, true)	
	-- self.aniHelper:addCompleteCallback("jump", self.jumpOver, self, true)	
	self.aniHelper:addCustomEventCallback("jump_3", "start", self.playSound, self, false)

end

function PlayerJumpHandler:playSound()
	local terrain = self.unit.walkSoundComp:getCurTerrain()
	if terrain == 10 then	--跳到船上
		SoundManager.instance:playEffect(130097, self.unit.go)
	else
		SoundManager.instance:playEffect(130098, self.unit.go)
	end
end

function PlayerJumpHandler:startJump()
	self.tween = self.unit.go.transform:DOMove(Vector3.New(self.info.x, self.info.z, self.info.y), self.time)
	self.tween:SetEase(DG.Tweening.Ease.Linear)
	self.tween:OnComplete(handler(self.jumpOver, self))
	SoundManager.instance:playEffect(142184, self.unit.go)
end

function PlayerJumpHandler:jumpOver()
	self.tween = nil
	-- self.unit:teleport(self.info.x, self.info.y)
	-- self.unit:setPos(self.info.x, self.info.y, self.info.z)
	self:finish()
end

function PlayerJumpHandler:onStop()
	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end
	self.aniHelper:removeCustomEventCallback("jump_1", "start", self.startJump, self)	
	self.aniHelper:removeCustomEventCallback("jump_3", "start", self.playSound, self)

	self.aniHelper:stopAnimation(1)
	self.unit:teleport(self.info.x, self.info.y, self.info.z)
	self.aniHelper = nil
end
return PlayerJumpHandler