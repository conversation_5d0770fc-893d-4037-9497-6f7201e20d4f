module("logic.extensions.room.view.administration.MyIslandPresentor",package.seeall)

local MyIslandPresentor = class("MyIslandPresentor", ViewPresentor)

--- 配置view需要的资源列表
function MyIslandPresentor:dependWhatResources()
	return {
		"ui/scene/room/myislandview.prefab",
		MyIslandHouseCell.ItemUrl,
		MyIslandHouseList.DragHouseGoUrl
	}
end

--- 配置view所在的ui层
function MyIslandPresentor:attachToWhichRoot()
	return ViewRootType.FullScreen
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function MyIslandPresentor:buildViews()
	self.mainView = MyIslandView.New()
	self.editView = MyIslandHouseList.New()
	self.effectView = MyIslandEffectList.New()
	self.comfortBar = MyIslandComfortBar.New("imgGroup/comfortbar")
	return {
		self.mainView,
		self.editView,
		self.effectView,
		self.comfortBar
	}
end

function MyIslandPresentor:isMyHouse()
	return self.mainView.houseData:isMyHouse()
end

return MyIslandPresentor