-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity466Extension_pb", package.seeall)


local tb = {}
GETACT466INFOREQUEST_MSG = protobuf.Descriptor()
GAINACT466PROGRESSREWARDREPLY_MSG = protobuf.Descriptor()
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT466LEAVEMESSAGEREPLY_MSG = protobuf.Descriptor()
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
GAINACT466PROGRESSREWARDREQUEST_MSG = protobuf.Descriptor()
ACT466LEAVEMESSAGEREQUEST_MSG = protobuf.Descriptor()
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD = protobuf.FieldDescriptor()
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD = protobuf.FieldDescriptor()
GETACT466INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD = protobuf.FieldDescriptor()
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD = protobuf.FieldDescriptor()
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD = protobuf.FieldDescriptor()
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD = protobuf.FieldDescriptor()

GETACT466INFOREQUEST_MSG.name = "GetAct466InfoRequest"
GETACT466INFOREQUEST_MSG.full_name = ".GetAct466InfoRequest"
GETACT466INFOREQUEST_MSG.filename = "Activity466Extension"
GETACT466INFOREQUEST_MSG.nested_types = {}
GETACT466INFOREQUEST_MSG.enum_types = {}
GETACT466INFOREQUEST_MSG.fields = {}
GETACT466INFOREQUEST_MSG.is_extendable = false
GETACT466INFOREQUEST_MSG.extensions = {}
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.full_name = ".GainAct466ProgressRewardReply.changeSetId"
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GAINACT466PROGRESSREWARDREPLY_MSG.name = "GainAct466ProgressRewardReply"
GAINACT466PROGRESSREWARDREPLY_MSG.full_name = ".GainAct466ProgressRewardReply"
GAINACT466PROGRESSREWARDREPLY_MSG.filename = "Activity466Extension"
GAINACT466PROGRESSREWARDREPLY_MSG.nested_types = {}
GAINACT466PROGRESSREWARDREPLY_MSG.enum_types = {}
GAINACT466PROGRESSREWARDREPLY_MSG.fields = {tb.GAINACT466PROGRESSREWARDREPLY_CHANGESETID_FIELD}
GAINACT466PROGRESSREWARDREPLY_MSG.is_extendable = false
GAINACT466PROGRESSREWARDREPLY_MSG.extensions = {}
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.full_name = ".Act466LeaveMessageReply.changeSetId"
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.number = 1
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.index = 0
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.label = 1
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.type = 5
tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT466LEAVEMESSAGEREPLY_MSG.name = "Act466LeaveMessageReply"
ACT466LEAVEMESSAGEREPLY_MSG.full_name = ".Act466LeaveMessageReply"
ACT466LEAVEMESSAGEREPLY_MSG.filename = "Activity466Extension"
ACT466LEAVEMESSAGEREPLY_MSG.nested_types = {}
ACT466LEAVEMESSAGEREPLY_MSG.enum_types = {}
ACT466LEAVEMESSAGEREPLY_MSG.fields = {tb.ACT466LEAVEMESSAGEREPLY_CHANGESETID_FIELD}
ACT466LEAVEMESSAGEREPLY_MSG.is_extendable = false
ACT466LEAVEMESSAGEREPLY_MSG.extensions = {}
GAINACT466PROGRESSREWARDREQUEST_MSG.name = "GainAct466ProgressRewardRequest"
GAINACT466PROGRESSREWARDREQUEST_MSG.full_name = ".GainAct466ProgressRewardRequest"
GAINACT466PROGRESSREWARDREQUEST_MSG.filename = "Activity466Extension"
GAINACT466PROGRESSREWARDREQUEST_MSG.nested_types = {}
GAINACT466PROGRESSREWARDREQUEST_MSG.enum_types = {}
GAINACT466PROGRESSREWARDREQUEST_MSG.fields = {}
GAINACT466PROGRESSREWARDREQUEST_MSG.is_extendable = false
GAINACT466PROGRESSREWARDREQUEST_MSG.extensions = {}
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.name = "message"
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.full_name = ".Act466LeaveMessageRequest.message"
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.number = 1
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.index = 0
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.label = 2
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.has_default_value = false
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.default_value = ""
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.type = 9
tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD.cpp_type = 9

tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.name = "backgroundIndex"
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.full_name = ".Act466LeaveMessageRequest.backgroundIndex"
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.number = 2
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.index = 1
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.label = 1
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.has_default_value = false
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.default_value = 0
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.type = 5
tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD.cpp_type = 1

ACT466LEAVEMESSAGEREQUEST_MSG.name = "Act466LeaveMessageRequest"
ACT466LEAVEMESSAGEREQUEST_MSG.full_name = ".Act466LeaveMessageRequest"
ACT466LEAVEMESSAGEREQUEST_MSG.filename = "Activity466Extension"
ACT466LEAVEMESSAGEREQUEST_MSG.nested_types = {}
ACT466LEAVEMESSAGEREQUEST_MSG.enum_types = {}
ACT466LEAVEMESSAGEREQUEST_MSG.fields = {tb.ACT466LEAVEMESSAGEREQUEST_MESSAGE_FIELD, tb.ACT466LEAVEMESSAGEREQUEST_BACKGROUNDINDEX_FIELD}
ACT466LEAVEMESSAGEREQUEST_MSG.is_extendable = false
ACT466LEAVEMESSAGEREQUEST_MSG.extensions = {}
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.name = "curGlobalProgress"
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.full_name = ".GetAct466InfoReply.curGlobalProgress"
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.number = 1
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.index = 0
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.label = 2
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.has_default_value = false
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.default_value = 0
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.type = 5
tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD.cpp_type = 1

tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.name = "selfMessage"
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.full_name = ".GetAct466InfoReply.selfMessage"
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.number = 2
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.index = 1
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.label = 1
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.has_default_value = false
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.default_value = ""
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.type = 9
tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD.cpp_type = 9

tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.name = "backgroundIndex"
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.full_name = ".GetAct466InfoReply.backgroundIndex"
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.number = 3
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.index = 2
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.label = 1
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.has_default_value = false
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.default_value = 0
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.type = 5
tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD.cpp_type = 1

tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.name = "gainedGlobalProgressRewardIds"
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.full_name = ".GetAct466InfoReply.gainedGlobalProgressRewardIds"
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.number = 4
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.index = 3
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.label = 3
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.has_default_value = false
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.default_value = {}
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.type = 5
tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD.cpp_type = 1

GETACT466INFOREPLY_MSG.name = "GetAct466InfoReply"
GETACT466INFOREPLY_MSG.full_name = ".GetAct466InfoReply"
GETACT466INFOREPLY_MSG.filename = "Activity466Extension"
GETACT466INFOREPLY_MSG.nested_types = {}
GETACT466INFOREPLY_MSG.enum_types = {}
GETACT466INFOREPLY_MSG.fields = {tb.GETACT466INFOREPLY_CURGLOBALPROGRESS_FIELD, tb.GETACT466INFOREPLY_SELFMESSAGE_FIELD, tb.GETACT466INFOREPLY_BACKGROUNDINDEX_FIELD, tb.GETACT466INFOREPLY_GAINEDGLOBALPROGRESSREWARDIDS_FIELD}
GETACT466INFOREPLY_MSG.is_extendable = false
GETACT466INFOREPLY_MSG.extensions = {}

Act466LeaveMessageReply = protobuf.Message(ACT466LEAVEMESSAGEREPLY_MSG)
Act466LeaveMessageRequest = protobuf.Message(ACT466LEAVEMESSAGEREQUEST_MSG)
GainAct466ProgressRewardReply = protobuf.Message(GAINACT466PROGRESSREWARDREPLY_MSG)
GainAct466ProgressRewardRequest = protobuf.Message(GAINACT466PROGRESSREWARDREQUEST_MSG)
GetAct466InfoReply = protobuf.Message(GETACT466INFOREPLY_MSG)
GetAct466InfoRequest = protobuf.Message(GETACT466INFOREQUEST_MSG)

return _G["logic.proto.Activity466Extension_pb"]
