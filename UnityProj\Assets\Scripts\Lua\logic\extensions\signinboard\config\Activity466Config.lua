module("logic.extensions.signinboard.config.Activity466Config",package.seeall)

local Activity466Config = class("Activity466Config")

local commonConfig = ConfigLoader.New("act_466_common")
local awardConfig = ConfigLoader.New("act_466_global_progress_reward")
local canSelectMsgConfig = ConfigLoader.New("act_466_can_select_msg")
local messageConfig = ConfigLoader.New("act_466_message")
local messageSpeedConfig = ConfigLoader.New("act_466_message_speed")

function Activity466Config.getCommonConfig(key)
    return commonConfig:getConfig()[ToyHouseGotoUtil.signinBoard][key].value
end

function Activity466Config.getAwardConfig(id)
    return awardConfig:getConfig()[ToyHouseGotoUtil.signinBoard][id]
end

function Activity466Config.getAllAwardConfig()
    return awardConfig:getConfig()[ToyHouseGotoUtil.signinBoard]
end

function Activity466Config.getAllCanSelectMsgConfig()
    return canSelectMsgConfig:getConfig().dataList
end

function Activity466Config.getMessageConfig()
    return messageConfig:getConfig().dataList
end

function Activity466Config.getMessageSpeed(progress)
    for k,v in ipairs(messageSpeedConfig:getConfig().dataList) do
        if progress <= v.progress then
            return v.speed
        end
    end
    printError("周年签到板找不到对应弹幕速度的配置, 进度："..tostring(progress))
    return 0.5
end

return Activity466Config