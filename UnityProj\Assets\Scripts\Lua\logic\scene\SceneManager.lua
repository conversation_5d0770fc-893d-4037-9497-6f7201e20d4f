
module("logic.scene.SceneManager", package.seeall)
local SceneManager = class("SceneManager", SceneMgrBase)
function SceneManager:ctor()
	SceneManager.super.ctor(self)
	self._sceneUIRoot = goutil.find("SCENEROOT2D")
	self.nameContainer =goutil.findChild(self._sceneUIRoot,"NAMEBAR")
	self.isFirstEnterScene = true
	self._targetChannel = 0
end

--扩展框架里面的SceneNotify、SceneLayer、SceneTag、UnitNotify、UnitTag等
function SceneManager:_extendConstDef()
	
end

SceneLayer.EffectHigh = "EffectHigh"
SceneLayer.ElfBattle = "ElfBattle"
SceneLayer.Custom = "Custom"
UnitTag.Furniture = "UnitFurniture"
UnitTag.CouncilBuilding = "CouncilBuilding"
UnitTag.FriendCheckUnit = "FriendCheckUnit"
UnitTag.ClickTop = "Player"
SceneTag.SceneTrigger = "SceneTrigger"
SceneTag.FriendShadowUnit = "FriendShadowUnit"
SceneTag.MistyFog = "MistyFog"
SceneTag.Ground = "Ground"
SceneNotify.EnterCollider = 100
SceneNotify.ExitCollider = 101
SceneNotify.PreloadFinished = 102
-- SceneNotify.UserStateChange = 103
-- SceneNotify.UserStateRemove = 104
-- SceneNotify.UserClothesChange = 105
SceneNotify.UserStartMove = 106
SceneNotify.UserStopMove = 107
SceneNotify.CameraZoom = 109
SceneNotify.PlayerActionChange = 110
SceneNotify.PlayerShortActionChange = 111
SceneNotify.ShowMoveTarget = 112
SceneNotify.InteractionChange = 114
SceneNotify.OnCustomCommand = 115
SceneNotify.RoleInfoChange = 116
--昼夜改变, true为白天，false为晚上
SceneNotify.DaytimeChange = 117
SceneNotify.UserVarUpdate = 118
--开始双人动作
SceneNotify.DoublePoseFinish = 119
SceneNotify.ThrowHit = 120
SceneNotify.ThrowInterrupt = 121
SceneNotify.ThrowAniCompleted = 122
SceneNotify.ThrowAniOnThrow = 123
SceneNotify.OnThrowHitResponse = 124
--队伍变化
SceneNotify.QueueChange = 125
SceneNotify.JoinSceneFinish = 126

--- 服务器推送了该场景新的玩法入口
SceneNotify.SceneEntryInfoChange = 127
SceneNotify.AnniversaryGalaAttachedAskFinish = 128
SceneNotify.AnniversaryGalaStartHandFinish = 129
SceneNotify.AnniversaryGalaBecomeCouple = 130
SceneNotify.AnniversaryGalaFlyDone = 131

UnitNotify.Remove = 101
UnitNotify.ModelChange = 102
UnitNotify.ClothesChange = 103
UnitNotify.DirectionChange = 104
-- UnitNotify.UserVariableChange = 102
function SceneManager:_registerScenes()
	self:_registerScene(SceneType.City, CommonSceneBase)
	self:_registerScene(SceneType.City3D, Common3DScene)
	self:_registerScene(SceneType.House, RoomScene)
	self:_registerScene(SceneType.Island, IslandScene)
	self:_registerScene(SceneType.Werewolf, WerewolfScene)
	self:_registerScene(SceneType.Council, CouncilScene)
	self:_registerScene(SceneType.GameRoomWaitingScene, GameRoomWaitingScene)
	self:_registerScene(SceneType.MoveHouseScene, MoveHouseScene)
	self:_registerScene(SceneType.CombineGameScene, CombineGameScene)
	self:_registerScene(SceneType.HappyRestaurant, HappyRestaurantScene)
	self:_registerScene(SceneType.Snake2, Snake2Scene)
	self:_registerScene(SceneType.CommonGameContainerScene, CommonGameContainerScene)
	self:_registerScene(SceneType.GameRoomWaitingScene3D, GameRoomWaitingScene3D)
	self:_registerScene(SceneType.CommonGameContainerScene2D, CommonGameContainerScene2D)
	self:_registerScene(SceneType.AnniversaryGalaScene, AnniversaryGalaScene)
	self:_registerScene(SceneType.EcoParkScene, EcoParkScene)
	self:_registerScene(SceneType.Sea, SeaScene)
	self:_registerScene(SceneType.DollHouse, DollHouseScene)
	self:_registerScene(SceneType.DuommScene, DuommScene)
	self:_registerScene(SceneType.Free3DScene, CommonFree3DScene)
	-- UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.Default), LayerMask.NameToLayer(SceneLayer.Default), true)
	-- UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.Unit), LayerMask.NameToLayer(SceneLayer.Unit), true)
	-- UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.SceneTrigger), LayerMask.NameToLayer(SceneLayer.SceneTrigger), true)
	-- UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.SceneTrigger), LayerMask.NameToLayer(SceneLayer.Default), true)
	UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.MainPlayer), LayerMask.NameToLayer(SceneLayer.Unit), true)
	UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.MainPlayer), LayerMask.NameToLayer(SceneLayer.MainPlayer), true)
	UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.Water), LayerMask.NameToLayer(SceneLayer.Unit), true)
	UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.Water), LayerMask.NameToLayer(SceneLayer.Water), true)
	UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.Water), LayerMask.NameToLayer(SceneLayer.MainPlayer), true)
	UnityEngine.Physics.autoSyncTransforms = false
	local cam = CameraTargetMgr.instance:getMainCameraTarget():getCamera()
	local newMask = bitutil.getNewBitValue(cam.cullingMask, LayerMask.NameToLayer(SceneLayer.Water)) 
	cam.cullingMask = newMask

	-- UnityEngine.Physics.IgnoreLayerCollision(LayerMask.NameToLayer(SceneLayer.MainPlayer), LayerMask.NameToLayer(SceneLayer.SceneTrigger), true)
end

--多个场景类型共用的组件，创建单例对象
function SceneManager:_createSingletonComps()
	SceneTriggerMgr.instance = SceneTriggerMgr.New()
	FullScreenViewWatcher.instance = FullScreenViewWatcher.New()
	VirtualCameraMgr.instance = VirtualCameraMgr.New()
	SceneGlobalTouchMgr.instance = SceneGlobalTouchMgr.New()
end

function SceneManager:loadSceneByAliasId(aliasId, bornX, bornY, fromSceneId, handler)
	SceneAliasIdHelper.LoadSceneByAliasId(aliasId, bornX, bornY, fromSceneId, handler)
end

function SceneManager:loadSceneWithChannel(sceneId, bornX, bornY, channelId)
	local params = {}
	params.sceneId = sceneId
	params.bornX = bornX
	params.bornY = bornY
	params.channelId = channelId
	params.ignoreSame = true
	self:loadSceneWithParams(params)
end

function SceneManager:loadScene(sceneId, bornX, bornY, fromSceneId, handler, ignoreSame, teamId)
	local params = {}
	params.sceneId = sceneId
	params.bornX = bornX
	params.bornY = bornY
	params.fromSceneId = fromSceneId
	params.handler = handler
	params.ignoreSame = ignoreSame
	--组队玩法场景(玩法ID_队伍ID，队伍ID=0:即创建队伍，队伍ID>0:加入指定队伍)
	params.teamId = teamId
	self:getCurScene().actionMgr:startAction(SceneActionType.LoadScene, params)
end

function SceneManager:loadSingleScene(sceneId, bornX, bornY, fromSceneId, handler, ignoreSame)
	local params = {}
	params.sceneId = sceneId
	params.bornX = bornX
	params.bornY = bornY
	params.fromSceneId = fromSceneId
	params.handler = handler
	params.ignoreSame = ignoreSame
	params.isSingle = true
	self:getCurScene().actionMgr:startAction(SceneActionType.LoadScene, params)
end

function SceneManager:forceLoadScene(sceneId, bornX, bornY, fromSceneId, handler, ignoreSame, isSingle)
	local params = {}
	params.sceneId = sceneId
	params.bornX = bornX
	params.bornY = bornY
	params.fromSceneId = fromSceneId
	params.handler = handler
	params.ignoreSame = ignoreSame
	params.isSingle = isSingle
	self:loadSceneRaw(params)
end

function SceneManager:loadSceneWithParams(params)
	return self:getCurScene().actionMgr:startAction(SceneActionType.LoadScene, params)
end

function SceneManager:loadSceneRaw(params)
	if params.sceneId == self:getCurSceneId() and HouseModel.instance:checkNextOwnerIsSame(params) and not params.ignoreSame then
		if params.handler then
			params.handler()
		end
		return false
	end
	if not SceneConfig.checkEnter(params.sceneId) then
		return false
	end
	self.loadSceneHandler = params.handler

	local cfg = SceneConfig.getSceneConfig(params.sceneId)
	local bornPoint = cfg:getBornPos(params.fromSceneId)
	local bornX = params.bornX or (bornPoint and bornPoint[1])
	local bornY = params.bornY or (bornPoint and bornPoint[2])
	if not (bornX and bornY) then
		printError("请配置场景出生点sceneId =", params.sceneId)
		return false
	end

	--增加一点点偏移，防止坐标重叠时交错问题
	local innerOffset = math.random(1, 100) * 0.001
	bornY = bornY + innerOffset
	local channelId = params.channelId or 0
	local ownerId = params.userId or UserInfo.userId
	local isNpc = params.isNpc or false
	local teamParam = params.teamId
	local roomId = params.roomId -- 游戏大厅房间id
	local password = params.password -- 场景密码
	if not TaskFacade.instance:isNewbieTaskFinish() then
		params.isSingle = true
	end
	SceneController.instance:joinScene(channelId, params.sceneId, bornX, bornY, ownerId, isNpc, params.isSingle, teamParam, roomId, password, params.councilId,
	function()
		self.isReload = false
		self._enterParams = params
		self:enter(cfg.sceneType, params.sceneId, bornX, bornY)
	end)
	return true
end

function SceneManager:loadWerewolfSceneWithParams(params)
	return self:getCurScene().actionMgr:startAction(SceneActionType.LoadWerewolfSceneAction, params)
end

function SceneManager:loadGameSceneRaw(params)
	printWarn(table.serialize(params))
	self.loadSceneHandler = params.handler
	local cfg = SceneConfig.getSceneConfig(params.sceneId)
	local bornPoint = cfg:getBornPos(params.fromSceneId)
	local bornX = params.bornX or bornPoint[1]
	local bornY = params.bornY or bornPoint[2]
	--增加一点点偏移，防止坐标重叠时交错问题
	local innerOffset = math.random(1, 100) * 0.001
	local channelId = params.channelId or 0
	local ownerId = params.userId or UserInfo.userId
	local isNpc = params.isNpc or false
	local teamParam = params.teamId
	bornY = bornY + innerOffset
	self._enterParams = params
	self:enter(cfg.sceneType, params.sceneId, bornX, bornY)
	return true
end

function SceneManager:reloadScene()
	GlobalDispatcher:dispatch(GlobalNotify.ReloadScene)
	local params = {}
	params.sceneId = SceneManager.instance:getCurSceneId()
	params.userId = self:getSceneOwner()
	params.ignoreSame = true
	self.isReload = true
	SceneHelp.instance:reconnectionJudge(params)
	ViewMgr.instance:closeAllModalViews()
	SceneManager.instance:loadSceneRaw(params)
end

function SceneManager:_showLoading(sceneId)
	-- if UserInfo.isFirstLogin and self.isFirstEnterScene then
	-- 	ViewMgr.instance:open("SpLoadingPanel")
	-- else
	if self._enterParams.hideLoading then
		CachedBG.instance:show()
		LoadingMask.instance:show()
	else
		if not ViewMgr.instance:isOpen("SpLoadingPanel") then
			if SceneConfig.getSceneConfig(sceneId).sceneType == SceneType.Sea then
				self.loadingViewName = "SeaLoadingPanel"
			elseif sceneId == 106 then
				self.loadingViewName = "DreamRoomLoadingPanel"
			else
				self.loadingViewName = "LoadingPanel"
			end
			ViewMgr.instance:open(self.loadingViewName, sceneId)
		end
	end
end

function SceneManager:_hideLoading()
	if self._enterParams.hideLoading then
		CachedBG.instance:close()
		LoadingMask.instance:close()
		GlobalDispatcher:dispatch(GlobalNotify.OpenScene)
	else
		settimer(0.5, function()

			if ViewMgr.instance:isOpen("SpLoadingPanel") then
				ViewMgr.instance:close("SpLoadingPanel")
				ViewMgr.instance:destroy("SpLoadingPanel")
			else
				ViewMgr.instance:close(self.loadingViewName)
			end
			GlobalDispatcher:dispatch(GlobalNotify.OpenScene)
		end, nil, false)
	end
	self.isFirstEnterScene = false
	self:getCurScene().tipsMgr:showSceneName()
end

function SceneManager:leaveScene()
	if self._curSceneType > 0 and self._curSceneId > 0 then
		local curScene = self._scenes[self._curSceneType]
		curScene:onExit()
	end
end

function SceneManager:onEnterFinished()
	-- if UserInfo.isFirstLogin and self.isFirstEnterScene then
	-- 	settimer(2, self._onEnterFinished, self, false)
	-- else
	self:_onEnterFinished()
	-- end
end

function SceneManager:_onEnterFinished()
	if self.loadSceneHandler then
		self.loadSceneHandler()
		self.loadSceneHandler = nil
	end
	self._enterParams.handler = nil
	printInfo("GlobalNotify.EnterScene")
	GlobalDispatcher:dispatch(GlobalNotify.EnterScene, self:getCurSceneId())
	SceneManager.super.onEnterFinished(self)
end

function SceneManager:getCurScenePlayerUnitType()
	return self:getCurScene():getPlayerUnitType()
end

function SceneManager:getCurSceneName()
	return SceneConfig.getSceneConfig(self:getCurSceneId()).name
end

function SceneManager:getCurSceneAliasId()
	return SceneAliasIdHelper.GetCurSceneAliasId()
end

function SceneManager:reset()
	for _, scene in pairs(self._scenes) do
		goutil.destroy(scene:getContainer())
	end
	self._scenes = {}
end

function SceneManager:getCurChannel()
	return self._curChannel
end

function SceneManager:setChannel(id)
	self._curChannel = id
end

function SceneManager:getEnterParams()
	return self._enterParams
end

function SceneManager:isSingle()
	return self._enterParams.isSingle
end

function SceneManager:getSceneOwner()
	return self._sceneOwner
end

--设置当前场景的拥有的
function SceneManager:setSceneOwner(id)
	self._sceneOwner = id
end

function SceneManager:isEnetering()
	return self._isEnetering
end

function SceneManager:setSceneTimeDelay(delay)
	self._sceneTimeDelay = delay
end

function SceneManager:getSceneDelay()
	return self._sceneTimeDelay
end

function SceneManager:setSceneTime(sceneTime)
	self._sceneTime = sceneTime
	self._localTimeStamp = Time.realtimeSinceStartup
end

function SceneManager:getSceneTime()
	return self._sceneTime + self._sceneTimeDelay + math.floor((Time.realtimeSinceStartup - self._localTimeStamp) * 1000)
end

function SceneManager:isInGamingScene()
	--- 暂时没有其他好的办法qwq
	local curSceneType = self:getCurSceneType()
	local isInGamingScene = curSceneType == SceneType.Werewolf
		-- or curSceneType == SceneType.GameRoomWaitingScene 
		or curSceneType == SceneType.MoveHouseScene 
		or curSceneType == SceneType.CombineGameScene
		or curSceneType == SceneType.Snake2
		or curSceneType == SceneType.CommonGameContainerScene
		or curSceneType == SceneType.CommonGameContainerScene2D
		or curSceneType == SceneType.DollHouse
		or curSceneType == SceneType.DuommScene
		-- or curSceneType == SceneType.GameRoomWaitingScene3D
	return isInGamingScene
end

function SceneManager:isInGameWaitingScene()
	local curSceneType = self:getCurSceneType()
	local isInGameWaitingScene = curSceneType == SceneType.GameRoomWaitingScene
		or curSceneType == SceneType.GameRoomWaitingScene3D
	return isInGameWaitingScene
end

function SceneManager:isInSea()
	return self:getCurSceneType() == SceneType.Sea
end

function SceneManager:isInFree3DScene()
    return self:getCurSceneType() == SceneType.Free3DScene
end

SceneManager.instance = SceneManager.New()
return SceneManager 