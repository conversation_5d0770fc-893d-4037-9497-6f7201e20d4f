module("logic.extensions.room.view.sampledetail.MySampleListCell",package.seeall)

local MySampleListCell = class("MySampleListCell", ListBinderCell, BaseLuaComponent)

MySampleListCell.CellUrl = "ui/scene/room/editdiytemplistitem.prefab"

function MySampleListCell:Awake()
    self._imgSelected = self:getGo("imgSelected")
    self._icon = self:getGo("icon")
    self._imgNull = self:getGo("imgNull")
    self._lockGo = self:getGo("lockGo")
    self._unlockIcon = self:getGo("lockGo/countGo/icon")
    self._txtUnlock = self:getText("lockGo/countGo/txtName")
    self._btnCheck = self:getBtn("btnCheck")
    self._txtGo = self:getGo("txtGo")
    self._txtComfort = self:getText("txtGo/txtComfort")
    self._txtSize = self:getText("txtGo/txtSize")
    self._nameGo = self:getGo("nameGo")
    self._txtName = self:getText("nameGo/txtName")
    self._numberGo = self:getGo("numberGo")
    self._textCellNumber = self:getText("numberGo/Text")
	
    self._txtNum = self:getText("spNum/txtNum")
    self._imgNewBubble = self:getGo("imgNewBubble")
	
	self._imgShare = self:getGo("imgShare")

    self:getBtn("click"):AddClickListener(self._onClickSelf, self)
    self._btnCheck:AddClickListener(self._onClickInfo, self)
    RoomController.instance:registerLocalNotify(RoomNotifyName.ChangeSampleName, self._onNameChange, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnSampleShareSucc, self._onSampleShareSucc, self)
end

function MySampleListCell:OnDestroy()
    RoomController.instance:unregisterLocalNotify(RoomNotifyName.ChangeSampleName, self._onNameChange, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnSampleShareSucc, self._onSampleShareSucc, self)
    MySampleListCell.super.OnDestroy(self)
end

function MySampleListCell:hasSave()
    return self._mo:hasSave()
end

function MySampleListCell:isLock()
    return self._mo.isLock ~= nil and self._mo:isLock()
end

function MySampleListCell:onSetMo(mo)
    self._mo = mo
    local isLock = self:isLock()
    local hasSave = self:hasSave()
    self._imgNull:SetActive(not isLock and not hasSave)
    self._lockGo:SetActive(isLock)
    self._btnCheck.gameObject:SetActive(hasSave)
    self._txtGo:SetActive(hasSave)
    self._nameGo:SetActive(hasSave)
    self._txtName.text = mo.name
    self._txtName.gameObject:SetActive(true)
    self._imgNewBubble:SetActive(false)
	if self._imgShare then
		self._imgShare:SetActive(RoomSampleModel.instance:getSample(self._mo.id):isShared())
	end

    if isLock then
        self._unlockConfig = RoomConfig.getSampleUnlock(self._cellIndex)
        IconLoader.setIconForItem(self._unlockIcon, self._unlockConfig.unlockConsume[1].id)
        self._txtUnlock.text = lang("{1}解锁", self._unlockConfig.unlockConsume[1].count)
    end
    self._numberGo:SetActive(hasSave)
    self._textCellNumber.text = self._cellIndex
    local totalCount = #self._mo.furnitures
    local totalComform = 0
    local totalSize = 0
    local collectCount = 0
    local w, h
    local ownMap = {}
    for _, furnitureMO in ipairs(self._mo.furnitures) do
        ownMap[furnitureMO.id] = (ownMap[furnitureMO.id] or 0) + 1
        if ItemService.instance:getItemTotalNum(furnitureMO.id) >= ownMap[furnitureMO.id] then
            collectCount = collectCount + 1
        end
        totalComform = totalComform + furnitureMO.cfg.comfort
        w, h = furnitureMO:getSize()
        totalSize = totalSize + (w * h)
    end
    local finish = collectCount >= totalCount
    self._txtComfort.text = totalComform
    self._txtSize.text = totalSize
    if self._spNum then
        self._txtNum.text = lang("{1}/{2}", collectCount, totalCount)
        self._spNum.gameObject:SetActive(self._mo:hasSave())
        Framework.ColorUtil.SetImageColor(self._spNum, RoomEditorSampleCell.Color[finish and "Normal" or "Red"])
    end
end

function MySampleListCell:_setCollect()
end

function MySampleListCell:onSelect(isSelected)
    self._imgSelected:SetActive(isSelected)
    if isSelected then
        self._listView:_onSelectSample(self)
    end
end

function MySampleListCell:_onClickSelf()
    if self:isLock() then
        UnlockSampleConfirmer.show(self._mo.templateId)
    else
        if self._listView.onClickCell then
            self._listView:onClickCell(self)
        end
    end
end

function MySampleListCell:_onClickInfo()
    FurnitureSampleView.show(self._mo:buildSystemSample(), self._mo.templateId, not self._listView._cantFocusFurniture, self._mo.islandType)
end

function MySampleListCell:_onNameChange(id, name)
    if self._mo and self._mo.templateId == id then
        self._txtName.text = name
    end
end

function MySampleListCell:_onSampleShareSucc(templateId)
	if self._mo.templateId == templateId and self._imgShare then
		self._imgShare:SetActive(RoomSampleModel.instance:getSample(self._mo.templateId):isShared())
	end
end

return MySampleListCell