module("logic.extensions.traveller.view.uplottery.TravellerUpLotteryTab", package.seeall)

local TravellerUpLotteryTab = class("TravellerUpLotteryTab", ViewComponent)

function TravellerUpLotteryTab:buildUI()
	self._tiredIcons = {}
	self._taskIcons = {}
	self._tiredConfigs = {}
	self._viewGos = {}
	self._tgGroup = ToggleGroup.New(handler(self._onClickTab, self))
	self._redGo = {}
	for i = 1, 3 do
		table.insert(self._viewGos, self:getGo("Panel" .. i))
		self._tgGroup:addView(self:getGo("tab/ordertab_" .. i))
		local redGo = self:getGo(string.format("tab/ordertab_%d/imgRedPoint", i))
		table.insert(self._redGo, redGo)
		redGo:SetActive(false)
		if i == 1 then
			RedPointController.instance:registerRedPoint(redGo, {"Traveller_UpLottery_Tab" .. i})
		end
	end
	self._txtTired = self:getText("Panel2/txtGroup/txtTired")
	self:getText("Panel2/bubblego/txtTips").text = lang("TravellerUpBubble")
	self._awardProgress = self:getSlider("Panel2/awardListview/content/awardProgress")
	self._imgTaskOver = self:getGo("Panel3/imgOver")
	self._btnGo = self:getBtn("Panel3/btnGo")
	self._btnGo:AddClickListener(self._onClickTask, self)
	self:getText("Panel3/txtGroup/txtTip1").text = lang("TravellerUpTaskDesc")
	self._taskIds = TravellerUpLotteryConfig.getLotteryCommonConfig("TravellerUpTaskIds"):splitToNumber(',')
end

function TravellerUpLotteryTab:_serverRefresh()
	ViewMgr.instance:clearBackStack()
	ViewMgr.instance:closeAllModalViews()
	self:close()
end

function TravellerUpLotteryTab:destroyUI()
	for i, redGo in ipairs(self._redGo) do
		RedPointController.instance:unregisterRedPoint(redGo)
	end
	for i, icon in ipairs(self._tiredIcons) do
		CommonIconMgr.instance:returnCommonIcon(icon)
	end
	for i, icon in ipairs(self._taskIcons) do
		CommonIconMgr.instance:returnCommonIcon(icon)
	end
	self._tiredIcons = nil
	self._taskIcons = nil
end

function TravellerUpLotteryTab:onExit()
	self:unregisterNotify(GlobalNotify.OnServerRefresh, self._serverRefresh, self)
	self._hasGetData = false
end

function TravellerUpLotteryTab:onEnter()
	self._actId = tonumber(TravellerUpLotteryConfig.getLotteryCommonConfig("TravellerUpActId"))
	self._tgGroup:clickViews(true, 1)
	self._imgTaskOver:SetActive(self:_isFinishTask())
	self._btnGo.gameObject:SetActive(not self:_isFinishTask())
	self:registerNotify(GlobalNotify.OnServerRefresh, self._serverRefresh, self)
end

function TravellerUpLotteryTab:_onClickTab(index, isSelected)
	RedPointController.instance:setLastClick("Traveller_UpLottery_Tab" .. index)
	for i, go in ipairs(self._viewGos) do
		go:SetActive(index == i)
		if index == 2 and not self._hasGetData then
			Activity221Agent.instance:sendAct221GetInfoRequest(handler(self._onGetData, self))
		end
		if index == 3 then
			settimer(0.01, self._initTaskPage, self, false)
		end
	end
end

function TravellerUpLotteryTab:_initTaskPage()
	if self._taskInited then return end
	self._content = self:getGo("Panel3/awardListview/content")
	for _1, taskId in ipairs(self._taskIds) do
		local task = TaskConfig.instance:getTaskCO(taskId)
		local awardStrs = task.rewards:split(',')
		for _2, str in ipairs(awardStrs) do
			local awardStr2 = str:splitToNumber(':')
			local param = {}
			param.id = awardStr2[1]
			param.num = awardStr2[2]
			param.rareness = true
			param.parent = self._content
			param.widthAndHeght = 80
			local icon = CommonIconMgr.instance:fetchCommonIconWithParams(param)
			table.insert(self._taskIcons, icon)
		end
	end
	self._taskInited = true
end

function TravellerUpLotteryTab:_onGetData(msg)
	self._personPoint = msg.personPoint
	self._drawInfos = msg.drawInfos
	self._txtTired.text = self._personPoint
	local allTiredConfigs = arrayutil.copy(CommonConfig.getConfig("act221_process_reward") [self._actId])
	table.sort(allTiredConfigs, function(a,b)
		return a.id < b.id
	end)
	if not self._tiredInited then
		local container = self:getGo("Panel2/awardListview/content")
		local awardItem = self:getGo("Panel2/awardListview/content/awardItem")
		local max = 0
		for i, config in ipairs(allTiredConfigs) do
			if config.type == 1 then
				local item = goutil.clone(awardItem, "awardItem" .. i)
				goutil.addChildToParent(item, container)
				local param = {}
				param.id = config.reward.id
				param.num = config.reward.count
				param.rareness = true
				param.parent = goutil.findChild(item, "commonIconGo")
				param.widthAndHeght = 87
				local icon = CommonIconMgr.instance:fetchCommonIconWithParams(param)
				table.insert(self._tiredIcons, icon)
				max = math.max(max, config.process)
				goutil.findChildTextComponent(item, "txtTired").text = config.process
				table.insert(self._tiredConfigs, config)
				local imgArrow = goutil.findChildImageComponent(item, "imgArrow")
				
				local quality = ItemService.instance:getRank(config.reward.id)
				Framework.ColorUtil.SetImageColor(imgArrow, GameUtils.getQualityColor(quality))
			end
		end
		awardItem:SetActive(false)
		self._progressMax = max
		self._tiredInited = true
	end
	self._awardProgress:SetValue(self._personPoint / self._progressMax)
	self._hasGetData = true
	for i, icon in ipairs(self._tiredIcons) do
		if table.indexof(self._drawInfos, i) ~= false then
			icon:showAwardState(CommonIcon.State_Received)
		elseif self._personPoint >= self._tiredConfigs[i].process then
			icon:showAwardState(CommonIcon.State_CanGet)
			icon:addIconClickListener(self._onClickTiredIcon, self, {i})
		else
			icon:showAwardState(CommonIcon.State_None)
		end
	end
end

function TravellerUpLotteryTab:_onClickTiredIcon(itemId, params)
	local index = params[1]
	Activity221Agent.instance:sendAct221DrawRewardRequest(index, function(msg)
		DialogHelper.showRewardsDlgByCI(msg.changeSetId)
		self._tiredIcons[index]:showAwardState(CommonIcon.State_Received)
		self._tiredIcons[index]:removeIconLickListener()
	end)
end

function TravellerUpLotteryTab:_onClickTask()
	local isFinish, taskId = self:_isFinishTask()
	if isFinish then return end
	self:close()
	ViewMgr.instance:open("TaskObjectivePanel", TaskObjectivePanel.Tab_Business)
end

function TravellerUpLotteryTab:_isFinishTask()
	for i, taskId in ipairs(self._taskIds) do
		if not TaskFacade.instance:isTaskFinish(taskId) then
			return false
		end
	end
	return true
end

return TravellerUpLotteryTab 