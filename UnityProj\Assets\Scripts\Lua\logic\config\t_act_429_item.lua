-- {excel:429合成玩法.xlsx, sheetName:export_物品}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act_429_item", package.seeall)

local title = {activityId=1,id=2,group=3,level=4,icon=5}

local dataList = {
	{2108, 1101, 1, 1, "cmyhc_icon_01"},
	{2108, 1102, 1, 2, "cmyhc_icon_02"},
	{2108, 1103, 1, 3, "cmyhc_icon_03"},
	{2108, 1104, 1, 4, "cmyhc_icon_04"},
	{2108, 1105, 1, 5, "cmyhc_icon_05"},
	{2108, 1201, 2, 1, "cmyhc_icon_06"},
	{2108, 1202, 2, 2, "cmyhc_icon_07"},
	{2108, 1203, 2, 3, "cmyhc_icon_08"},
	{2108, 1204, 2, 4, "cmyhc_icon_09"},
	{2108, 1205, 2, 5, "cmyhc_icon_10"},
	{2108, 1301, 3, 1, "cmyhc_icon_11"},
	{2108, 1302, 3, 2, "cmyhc_icon_12"},
	{2108, 1303, 3, 3, "cmyhc_icon_13"},
	{2108, 1304, 3, 4, "cmyhc_icon_14"},
	{2108, 1305, 3, 5, "cmyhc_icon_15"},
	{2282, 1101, 1, 1, "37hdj_props_01"},
	{2282, 1102, 1, 2, "37hdj_props_02"},
	{2282, 1103, 1, 3, "37hdj_props_03"},
	{2282, 1104, 1, 4, "37hdj_props_04"},
	{2282, 1105, 1, 5, "37hdj_props_05"},
	{2282, 1201, 2, 1, "37hdj_props_06"},
	{2282, 1202, 2, 2, "37hdj_props_07"},
	{2282, 1203, 2, 3, "37hdj_props_08"},
	{2282, 1204, 2, 4, "37hdj_props_09"},
	{2282, 1205, 2, 5, "37hdj_props_10"},
	{2282, 1301, 3, 1, "37hdj_props_11"},
	{2282, 1302, 3, 2, "37hdj_props_12"},
	{2282, 1303, 3, 3, "37hdj_props_13"},
	{2282, 1304, 3, 4, "37hdj_props_14"},
	{2282, 1305, 3, 5, "37hdj_props_15"},
	{2509, 1101, 1, 1, "37hdj_props_01"},
	{2509, 1102, 1, 2, "37hdj_props_02"},
	{2509, 1103, 1, 3, "37hdj_props_03"},
	{2509, 1104, 1, 4, "37hdj_props_04"},
	{2509, 1105, 1, 5, "37hdj_props_05"},
	{2509, 1201, 2, 1, "37hdj_props_06"},
	{2509, 1202, 2, 2, "37hdj_props_07"},
	{2509, 1203, 2, 3, "37hdj_props_08"},
	{2509, 1204, 2, 4, "37hdj_props_09"},
	{2509, 1205, 2, 5, "37hdj_props_10"},
	{2509, 1301, 3, 1, "37hdj_props_11"},
	{2509, 1302, 3, 2, "37hdj_props_12"},
	{2509, 1303, 3, 3, "37hdj_props_13"},
	{2509, 1304, 3, 4, "37hdj_props_14"},
	{2509, 1305, 3, 5, "37hdj_props_15"},
}

local t_act_429_item = {
	[2108] = {
		[1101] = dataList[1],
		[1102] = dataList[2],
		[1103] = dataList[3],
		[1104] = dataList[4],
		[1105] = dataList[5],
		[1201] = dataList[6],
		[1202] = dataList[7],
		[1203] = dataList[8],
		[1204] = dataList[9],
		[1205] = dataList[10],
		[1301] = dataList[11],
		[1302] = dataList[12],
		[1303] = dataList[13],
		[1304] = dataList[14],
		[1305] = dataList[15],
	},
	[2282] = {
		[1101] = dataList[16],
		[1102] = dataList[17],
		[1103] = dataList[18],
		[1104] = dataList[19],
		[1105] = dataList[20],
		[1201] = dataList[21],
		[1202] = dataList[22],
		[1203] = dataList[23],
		[1204] = dataList[24],
		[1205] = dataList[25],
		[1301] = dataList[26],
		[1302] = dataList[27],
		[1303] = dataList[28],
		[1304] = dataList[29],
		[1305] = dataList[30],
	},
	[2509] = {
		[1101] = dataList[31],
		[1102] = dataList[32],
		[1103] = dataList[33],
		[1104] = dataList[34],
		[1105] = dataList[35],
		[1201] = dataList[36],
		[1202] = dataList[37],
		[1203] = dataList[38],
		[1204] = dataList[39],
		[1205] = dataList[40],
		[1301] = dataList[41],
		[1302] = dataList[42],
		[1303] = dataList[43],
		[1304] = dataList[44],
		[1305] = dataList[45],
	},
}

t_act_429_item.dataList = dataList
local mt
if Act429ItemDefine then
	mt = {
		__cname =  "Act429ItemDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act429ItemDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act_429_item