-- {excel:410赛季链式礼包.xlsx, sheetName:export_常量配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act410_common_config", package.seeall)

local title = {activityId=1,key=2,value=3}

local dataList = {
	{1835, "mailId", "137"},
	{1835, "allGoodsId", "900094"},
	{1835, "weekRefresh", "0"},
	{1933, "mailId", "137"},
	{1933, "allGoodsId", "900113"},
	{1933, "weekRefresh", "0"},
	{2129, "mailId", "137"},
	{2129, "allGoodsId", "900136"},
	{2129, "weekRefresh", "0"},
	{2245, "mailId", "137"},
	{2245, "allGoodsId", "900156"},
	{2245, "weekRefresh", "0"},
	{2373, "mailId", "137"},
	{2373, "allGoodsId", "900184"},
	{2373, "weekRefresh", "0"},
	{2464, "mailId", "137"},
	{2464, "allGoodsId", "900209"},
	{2464, "weekRefresh", "0"},
}

local t_act410_common_config = {
	[1835] = {
		["mailId"] = dataList[1],
		["allGoodsId"] = dataList[2],
		["weekRefresh"] = dataList[3],
	},
	[1933] = {
		["mailId"] = dataList[4],
		["allGoodsId"] = dataList[5],
		["weekRefresh"] = dataList[6],
	},
	[2129] = {
		["mailId"] = dataList[7],
		["allGoodsId"] = dataList[8],
		["weekRefresh"] = dataList[9],
	},
	[2245] = {
		["mailId"] = dataList[10],
		["allGoodsId"] = dataList[11],
		["weekRefresh"] = dataList[12],
	},
	[2373] = {
		["mailId"] = dataList[13],
		["allGoodsId"] = dataList[14],
		["weekRefresh"] = dataList[15],
	},
	[2464] = {
		["mailId"] = dataList[16],
		["allGoodsId"] = dataList[17],
		["weekRefresh"] = dataList[18],
	},
}

t_act410_common_config.dataList = dataList
local mt
if Act410CommonConfigDefine then
	mt = {
		__cname =  "Act410CommonConfigDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act410CommonConfigDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act410_common_config