-- {excel:C采集配置.xlsx, sheetName:export_采集场景配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_collection", package.seeall)

local title = {sceneId=1,id=2,type=3,pos=4,isFlip=5}

local dataList = {
	{3, 1, 2, {-4.63,0.52,2.2}, false},
	{3, 2, 2, {7.52,-9.9}, false},
	{3, 3, 14, {6.66,7.29,1.66}, false},
	{3, 4, 16, {-3.6,5.54,1.25}, false},
	{3, 5, 17, {1.01,-2.6,1.46}, false},
	{3, 7, 4, {-1.01,-3.9,1.33}, false},
	{3, 8, 1, {-11.16,-7.56,-0.54}, false},
	{3, 9, 5, {-7.78,-5.23}, false},
	{3, 10, 4, {-10.24,12.77,1.22}, false},
	{6, 1, 22, {-15.82,1.5,2.5}, false},
	{7, 1, 7, {-9.91,-0.93,0.23}, false},
	{7, 2, 8, {-7.95,-3.32,-1.07}, false},
	{7, 3, 9, {5.73,-1.54,-0.65}, false},
	{7, 4, 10, {9.4,-2.56,-0.89}, false},
	{7, 5, 11, {10.18,2.84,0.04}, false},
	{7, 6, 12, {2.72,0.41}, false},
	{7, 7, 30, {-8.06,8.01,0.79}, false},
	{7, 8, 31, {8.297,9.835,-0.44}, false},
	{8, 1, 18, {-9.92,-2.5}, false},
	{8, 2, 19, {12.14,21.1}, false},
	{8, 3, 20, {-26.34,0.9}, false},
	{8, 4, 21, {14.14,-10.18,1.6}, false},
	{17, 1, 23, {-24.38,-18.36}, false},
	{17, 2, 23, {-35.9,-20.39}, false},
	{17, 3, 23, {-20.16,-6.433}, false},
	{17, 4, 24, {-30.29,-25.92}, false},
	{17, 5, 25, {-41.06,-28.63}, false},
	{17, 6, 26, {-26.38,-32.41}, false},
	{17, 7, 27, {19.15,-41.80}, false},
	{17, 8, 28, {37.74,-40.35}, false},
	{17, 9, 29, {22.35,-33.65}, false},
	{17, 10, 33, {37.93,0.39}, false},
}

local t_collection = {
	[3] = {
		[1] = dataList[1],
		[2] = dataList[2],
		[3] = dataList[3],
		[4] = dataList[4],
		[5] = dataList[5],
		[7] = dataList[6],
		[8] = dataList[7],
		[9] = dataList[8],
		[10] = dataList[9],
	},
	[6] = {
		[1] = dataList[10],
	},
	[7] = {
		[1] = dataList[11],
		[2] = dataList[12],
		[3] = dataList[13],
		[4] = dataList[14],
		[5] = dataList[15],
		[6] = dataList[16],
		[7] = dataList[17],
		[8] = dataList[18],
	},
	[8] = {
		[1] = dataList[19],
		[2] = dataList[20],
		[3] = dataList[21],
		[4] = dataList[22],
	},
	[17] = {
		[1] = dataList[23],
		[2] = dataList[24],
		[3] = dataList[25],
		[4] = dataList[26],
		[5] = dataList[27],
		[6] = dataList[28],
		[7] = dataList[29],
		[8] = dataList[30],
		[9] = dataList[31],
		[10] = dataList[32],
	},
}

t_collection.dataList = dataList
local mt
if CollectionDefine then
	mt = {
		__cname =  "CollectionDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or CollectionDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_collection