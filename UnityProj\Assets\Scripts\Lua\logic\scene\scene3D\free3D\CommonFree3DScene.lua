module("logic.scene.scene3D.free3D.CommonFree3DScene", package.seeall)

local CommonFree3DScene = class("CommonFree3DScene", Common3DScene)

function CommonFree3DScene:overrideComps()
    CommonFree3DScene.super.overrideComps(self)
    self:_addComponent("camera", SceneFree3DCamera)
    self:_addComponent("mapMgr", SceneFree3DMapMgr)
    self:_addComponent("unitFactory", SceneFree3DUnitMgr)
    self:_addComponent("controller", SceneFree3DController)
    self.camera:setParams(self:camParams())
end

function CommonFree3DScene:camParams()
    return {
        camFar = CommonFree3DConst.camFar,
        camFarMin = CommonFree3DConst.camFarMin,
        camFarMax = CommonFree3DConst.camFarMax,
        camHig = CommonFree3DConst.camHig,
        camHigMin = CommonFree3DConst.camHigMin,
        camHigMax = CommonFree3DConst.camHigMax,
        camFocus = CommonFree3DConst.camFocus,
        camRotaH = CommonFree3DConst.camRotaH,
        camRotaV = CommonFree3DConst.camRotaV,
        camRotaVMin = CommonFree3DConst.camRotaVMin,
        camRotaVMax = CommonFree3DConst.camRotaVMax,
        camMoveSpeedH = CommonFree3DConst.camMoveSpeedH,
        camMoveSpeedV = CommonFree3DConst.camMoveSpeedV,
        camRotaSpeedH = CommonFree3DConst.camRotaSpeedH,
        camRotaSpeedV = CommonFree3DConst.camRotaSpeedV,
        showPlayerMin = CommonFree3DConst.showPlayerMin,
        showPlayerMax = CommonFree3DConst.showPlayerMax
    }
end

function CommonFree3DScene:getPlayerUnitType()
    return SceneUnitType.Free3DPlayer
end

function CommonFree3DScene:getAllPlayers()
    return self:getUnitMgr():getUnits(self:getPlayerUnitType())
end

return CommonFree3DScene
