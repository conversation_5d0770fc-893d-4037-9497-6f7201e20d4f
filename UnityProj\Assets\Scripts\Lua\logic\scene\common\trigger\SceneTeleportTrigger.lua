module("logic.scene.common.trigger.SceneTeleportTrigger",package.seeall)
local SceneTeleportTrigger = class("SceneTeleportTrigger", SceneTriggerItemBase)

local handlerMap = {}
handlerMap[1] = "Jump"
handlerMap[2] = "Random"
handlerMap[3] = "Climb"
handlerMap[4] = "PaperPlane"




function SceneTeleportTrigger:onInit()
	self.sceneIcon = SceneIcon.Create()
	IconLoader.setAtlasToImg(self.sceneIcon.icon, AtlasUrl.Common, self.config.triggerValue.icon or "shop")
	self.sceneIcon:addListener(self.onClickItem, self)
	self.sceneIcon:setLockVisible(false)
	self.triggerPos =  self.config.triggerPos
	local pos = self.triggerGO.transform.position
	if self.config.triggerValue.fixPosX then
		pos.x = pos.x + self.config.triggerValue.fixPosX
	end
	if self.config.triggerValue.fixPosY then
		pos.y = pos.y + self.config.triggerValue.fixPosY
	end
	if self.config.triggerValue.fixPosZ then
		pos.z = pos.z + self.config.triggerValue.fixPosZ
	end
	local range = self.config.triggerValue.range or 5
	SceneManager.instance:getCurScene():getTipsMgr():addSceneTips(self.sceneIcon.go, pos.x, pos.y + 2, pos.z)
	self.sceneIcon:setAutoHide(
		true,
		{
			x = pos.x,
			y = VirtualCameraMgr.instance:is3DScene() and pos.z or pos.y
		},
		range
	)
	self["init" .. handlerMap[self.config.triggerValue.type]](self)
end

function SceneTeleportTrigger:onClickSceneTrigger(triggerGO, triggerConfig)
	print("SceneTeleportTrigger:onClickSceneTrigger")
	-- if not SceneController.instance:checkIdle() then
	-- 	return
	-- end
	if not SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.Jump, true) then
		return
	end
	SceneController.instance:walkTo(self.triggerPos[1], self.triggerPos[2], self.triggerPos[3], false,
		function(suc)
			if suc then
				self.sceneIcon:hide()
				--马上执行会被walkTo的onArrive影响，等一帧再执行
				settimer(0, self["start" .. handlerMap[self.config.triggerValue.type]], self, false)
			end
		end
	)
end

-- function ElevatorTrigger:onFinish()
-- 	CommonHUDFacade.instance:showHUD()
-- 	ViewBlockMgr.instance:blockClick(false)
-- end

function SceneTeleportTrigger:onDispose()
	self["dispose" .. handlerMap[self.config.triggerValue.type]](self)
end

function SceneTeleportTrigger:initJump()
	self.aniJump = Framework.AnimationAdapter.GetFrom(self.triggerGO, "jump")
end

function SceneTeleportTrigger:startJump()
	print("SceneTeleportTrigger:startJump")
	-- SceneController.instance:teleport(self.config.triggerPos[1], self.config.triggerPos[2], self.config.triggerPos[3], false)
	SceneController.instance:jump(self.config.triggerValue.jumpPos[1], self.config.triggerValue.jumpPos[2], 2, nil, self.config.triggerValue.jumpPos[3])
	if self.aniJump then
		self.aniJump:Play("ani_room_bengchuang", true)
	end
end

function SceneTeleportTrigger:disposeJump()

end

function SceneTeleportTrigger:initClimb()
end

function SceneTeleportTrigger:startClimb()
	local triggerValue = self.config.triggerValue
	local startPos = {posX=triggerValue.climbPos[1], posY=triggerValue.climbPos[2], posZ=triggerValue.climbPos[3]}
	self.userPlayer = SceneManager.instance:getCurScene():getUserPlayer()
	self.userPlayer:teleport(startPos.posX, startPos.posY, startPos.posZ)
	SceneAgent.instance:sendMoveRequest({startPos}, 1)
	SceneController.instance:jump(triggerValue.climbPos[1], triggerValue.climbPos[2], 9, handler(self.onArrive, self), triggerValue.climbPos[3] + triggerValue.moveY)
end

function SceneTeleportTrigger:onArrive()
	SceneController.instance:teleport(self.config.triggerValue.jumpPos[1], self.config.triggerValue.jumpPos[2], self.config.triggerValue.jumpPos[3], false)
end

function SceneTeleportTrigger:disposeClimb()

end


function SceneTeleportTrigger:initRandom()
	self.effectGo = goutil.find("SCENEROOT/standstill/wujian/ab-s158_obj_jinkuai/v4_0_ab-s158_obj_jinkuai_effect_01")
	if self.effectGo then
		self.effectGo:SetActive(false)
	end
end

function SceneTeleportTrigger:startRandom()
	print("SceneTeleportTrigger:startJump")
	if self.effectGo then
		self.effectGo:SetActive(true)
		SoundManager.instance:playEffect(142183, self.triggerGO)
	end
	ViewBlockMgr.instance:blockClick(true)
	settimer(1, function()
		ViewBlockMgr.instance:blockClick(false)
		if self.effectGo then
			self.effectGo:SetActive(false)
		end
		local pos = arrayutil.randomOne(self.config.triggerValue.randomPos)
		SceneController.instance:teleport(pos[1], pos[2], pos[3], false)
	end, self, false)

end

function SceneTeleportTrigger:disposeRandom()

end

function SceneTeleportTrigger:initPaperPlane()
end

function SceneTeleportTrigger:startPaperPlane()
	print("SceneTeleportTrigger:startPlane")
	-- SceneController.instance:teleport(self.config.triggerPos[1], self.config.triggerPos[2], self.config.triggerPos[3], false)
	SceneController.instance:jump(self.config.triggerValue.jumpPos[1], self.config.triggerValue.jumpPos[2], 11, nil, self.config.triggerValue.jumpPos[3])

end

function SceneTeleportTrigger:disposePaperPlane()

end

return SceneTeleportTrigger