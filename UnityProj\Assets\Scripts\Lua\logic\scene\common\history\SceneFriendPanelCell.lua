module("logic.scene.common.history.SceneFriendPanelCell",package.seeall)

local SceneFriendPanelCell = class("SceneFriendPanelCell", ListBinderCell, BaseLuaComponent)

function SceneFriendPanelCell:Awake()
	self.txtName = self:getText("txtName")
	self.btnAdd = self:getBtn("btnAdd")
	self.btnAdd:AddClickListener(self._onClickAdd, self)
	self.goMale = self:getGo("img_1")
	self.goFemale = self:getGo("img_2")
	self.goAdded = self:getGo("imgOk")
	self.goHeadIcon = self:getGo("imgHeadIcon")
	Framework.ButtonAdapter.Get(self.goHeadIcon):AddClickListener(self._onClickHead, self)
end

function SceneFriendPanelCell:onSetMo(mo)
	self.mo = mo
	self.txtName.text = mo.name
	HeadPortraitHelper.instance:setHeadPortraitWithUserId(self.goHeadIcon, mo.id)
	self.goMale:SetActive(mo.sex == GameEnum.SexEnum.Male)
	self.goFemale:SetActive(mo.sex == GameEnum.SexEnum.Female)
	local isFriend = FriendService.instance:queryIsFriend(mo.id)
	self.goAdded:SetActive(isFriend)
	self.btnAdd.gameObject:SetActive(not isFriend)
end

function SceneFriendPanelCell:_onClickAdd()
	local inBlack = FriendService.instance:queryIsInBlack(self.mo.id)
	if inBlack then
		DialogHelper.showMsg(lang("对方在您的黑名单中"))
		return
	end
	FriendController.instance:askFriendRequest(
		self.mo.id,
		function()
			self.goAdded:SetActive(true)
			self.btnAdd.gameObject:SetActive(false)
		end
	)
end

function SceneFriendPanelCell:_onClickHead()
	ViewFacade.showUserInfo(self.mo.id)
end

return SceneFriendPanelCell


