module("logic.extensions.activity.flyingchess.view.GamePoseView", package.seeall)

local GamePoseView = class("GamePoseView", ListBinderView)

function GamePoseView:ctor()
    self.model = BaseListModel.New()
    GamePoseView.super.ctor(
        self,
        self.model,
        "list",
        GamePosePresentor.Url_Item,
        GamePoseCell,
        {kScrollDirV, 90, 95, 2, 2.7, 3}
    )
    self.lastClickTime = 0
end

function GamePoseView:buildUI()
    GamePoseView.super.buildUI(self)
end

function GamePoseView:bindEvents()
    GamePoseView.super.bindEvents(self)
    Framework.UIGlobalTouchTrigger.Get(self.mainGO):AddIgnoreTargetListener(self._onClickOutside, self)
end

function GamePoseView:unbindEvents()
    GamePoseView.super.unbindEvents(self)
end

function GamePoseView:onEnter()
    GamePoseView.super.onEnter(self)
    local params = self:getOpenParam()
    self._callback = params[2]
    local showPoseIds = params[1]

    self:initFavoritePose()
    local list = {}
    for k, v in ipairs(showPoseIds) do
        local define = PoseConfig.getPoseDefine(v)
        if define.itemId <= 0 or ItemService.instance:hasItem(define.itemId) then
            table.insert(list, define)
        end
    end

    table.sort(list, function(a, b)
        local indexA = self.favoriteMap[a.id] or 0
        local indexB = self.favoriteMap[b.id] or 0
        if indexA ~= indexB then
            return indexA > indexB
        else
            return a.id < b.id
        end
    end)	

    self.model:setMoList(list)
end

function GamePoseView:onExit()
    GamePoseView.super.onExit(self)
end

function GamePoseView:initFavoritePose()
    local str = LocalStorage.instance:getValue(StorageKey.PoseFavorite)
    self.favoriteIds = string.splitToNumber(str, ",") 
    self.favoriteMap = {}
    for i=1, #self.favoriteIds do
        self.favoriteMap[self.favoriteIds[i]] = true
    end
end

function GamePoseView:isFavoritePose(id)
    return self.favoriteMap[id]
end


function GamePoseView:_onClickOutside()
    ViewMgr.instance:close("GamePose")
end

function GamePoseView:onClickIcon(mo)
    if Time.realtimeSinceStartup - self.lastClickTime > 0.7 then
        self.lastClickTime = Time.realtimeSinceStartup
        self._callback(mo.id)
    end
end

return GamePoseView