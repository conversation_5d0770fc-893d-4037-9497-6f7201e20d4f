module("logic.extensions.sharestreet.view.ShareStreetFurnitureViewPresentor", package.seeall)
---@class ShareStreetFurnitureViewPresentor
local ShareStreetFurnitureViewPresentor = class("ShareStreetFurnitureViewPresentor", ViewPresentor)

function ShareStreetFurnitureViewPresentor:ctor()
    ShareStreetFurnitureViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetFurnitureViewPresentor:dependWhatResources()
    return {"ui/street/streetshareview.prefab", ShareStreetAddFurnitureViewPresentor.IconUrl,
            ShareStreetAddFurnitureViewPresentor.BtnFilter1, ShareStreetAddFurnitureViewPresentor.BtnFilter2,
            ShareStreetAddFurnitureViewPresentor.BtnFilter3, ShareStreetAddFurnitureViewPresentor.BtnFilter4}
end

function ShareStreetFurnitureViewPresentor:attachToWhichRoot()
    return ViewRootType.Popup
end

function ShareStreetFurnitureViewPresentor:buildViews()
    self.listComp = ShareFurnitureListComp.New(4)
    -- self.tabComp = ShareFurnitureTab.New()
    self.infoComp = ItemInfoComponent.New("rightGo/single/info")
    return {
        -- self.tabComp,
        self.infoComp,
        self.listComp,
        ShareStreetFurnitureView.New(),
    }
end

function ShareStreetFurnitureViewPresentor:onClickOutside()

end

return ShareStreetFurnitureViewPresentor
