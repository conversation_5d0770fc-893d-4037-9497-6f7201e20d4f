-- {excel:429合成玩法.xlsx, sheetName:export_奖励}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act_429_reward", package.seeall)

local title = {activityId=1,rewardId=2,costMaterialItemNum=3,reward=4}

local dataList = {
	{2108, 1, 60, {{count=200,id=16000679}}},
	{2108, 2, 120, {{count=200,id=16000679}}},
	{2108, 3, 180, {{count=200,id=16000679}}},
	{2108, 4, 180, {{count=50,id=16000678}}},
	{2108, 5, 240, {{count=200,id=16000679}}},
	{2108, 6, 300, {{count=200,id=16000679}}},
	{2108, 7, 360, {{count=200,id=16000679}}},
	{2108, 8, 420, {{count=200,id=16000679}}},
	{2108, 9, 420, {{count=50,id=16000678}}},
	{2282, 1, 60, {{count=200,id=16000740}}},
	{2282, 2, 120, {{count=200,id=16000740}}},
	{2282, 3, 180, {{count=200,id=16000740}}},
	{2282, 4, 180, {{count=50,id=16000742}}},
	{2282, 5, 240, {{count=200,id=16000740}}},
	{2282, 6, 300, {{count=200,id=16000740}}},
	{2282, 7, 360, {{count=200,id=16000740}}},
	{2282, 8, 420, {{count=200,id=16000740}}},
	{2282, 9, 420, {{count=50,id=16000742}}},
	{2509, 1, 60, {{count=200,id=16000740}}},
	{2509, 2, 120, {{count=200,id=16000740}}},
	{2509, 3, 180, {{count=200,id=16000740}}},
	{2509, 4, 180, {{count=50,id=16000742}}},
	{2509, 5, 240, {{count=200,id=16000740}}},
	{2509, 6, 300, {{count=200,id=16000740}}},
	{2509, 7, 360, {{count=200,id=16000740}}},
	{2509, 8, 420, {{count=200,id=16000740}}},
	{2509, 9, 420, {{count=50,id=16000742}}},
}

local t_act_429_reward = {
	[2108] = {
		[1] = dataList[1],
		[2] = dataList[2],
		[3] = dataList[3],
		[4] = dataList[4],
		[5] = dataList[5],
		[6] = dataList[6],
		[7] = dataList[7],
		[8] = dataList[8],
		[9] = dataList[9],
	},
	[2282] = {
		[1] = dataList[10],
		[2] = dataList[11],
		[3] = dataList[12],
		[4] = dataList[13],
		[5] = dataList[14],
		[6] = dataList[15],
		[7] = dataList[16],
		[8] = dataList[17],
		[9] = dataList[18],
	},
	[2509] = {
		[1] = dataList[19],
		[2] = dataList[20],
		[3] = dataList[21],
		[4] = dataList[22],
		[5] = dataList[23],
		[6] = dataList[24],
		[7] = dataList[25],
		[8] = dataList[26],
		[9] = dataList[27],
	},
}

t_act_429_reward.dataList = dataList
local mt
if Act429RewardDefine then
	mt = {
		__cname =  "Act429RewardDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act429RewardDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act_429_reward