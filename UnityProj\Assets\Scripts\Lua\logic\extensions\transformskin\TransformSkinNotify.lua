module("logic.extensions.transformskin.TransformSkinNotify",package.seeall)

local TransformSkinNotify = {}

local _id = 1
local function genId()
	local id = _id
	_id = _id + 1
	return "TransformSkin_" .. id
end

--拖动setting
TransformSkinNotify.OpSettingBeginDrag = genId()
TransformSkinNotify.OpSettingDrag = genId()
TransformSkinNotify.OpSettingEndDrag = genId()

--拖动unit
TransformSkinNotify.OpUnitBeginDrag = genId()
TransformSkinNotify.OpUnitDrag = genId()
TransformSkinNotify.OpUnitEndDrag = genId()

--设置操作
TransformSkinNotify.OnOkOp = genId()
TransformSkinNotify.OnCancelOp = genId()

--变身状态变更
TransformSkinNotify.transformSkinStateChange = genId()

--播放长动作状态动画
TransformSkinNotify.playLongStateAnim = genId()


return TransformSkinNotify