module("logic.extensions.room.view.RoomEditorPresentor", package.seeall)
local RoomEditorPresentor = class("RoomEditorPresentor", ViewPresentor)

RoomEditorPresentor.Url_View = "ui/scene/room/editview.prefab"
RoomEditorPresentor.Fur_Cell = "ui/scene/room/editfuritem.prefab"

function RoomEditorPresentor.showView(selectType)
	local houseType = HouseModel.instance:getUserProperty(HouseUserProperty.HouseType)
	local islandType = HouseModel.instance:getUserProperty(HouseUserProperty.IslandType, 0)
	if houseType == 0 and islandType == 2 then
		--街区的，要先拿数据，干脆重新写了
		RoomEditorPresentor.showViewInShareStreet(selectType)
		return
	end
	local furTypes, partFilter
	if SceneManager.instance:getCurSceneType() == SceneType.House then
		if HouseModel.instance:getRoomParams(RoomParams.Params_Mode) == RoomScene.Type.FashionRace then
			partFilter = FurnitureType.PartFilterType.CanPlaceInRace
		elseif HouseModel.instance:getRoomParams(RoomParams.Params_Mode) == RoomScene.Type.ElfHouse then
			partFilter = FurnitureType.PartFilterType.CanPlaceInPetRoom
		else
			partFilter = FurnitureType.PartFilterType.CanPlaceInRoom
		end
	elseif HouseModel.instance:getRoomParams(RoomParams.Params_Mode) == RoomScene.Type.TemplatePreview then
		partFilter = FurnitureType.PartFilterType.CanPlaceInTemplate
	elseif islandType == 1 then
		partFilter = FurnitureType.PartFilterType.CanPlaceInSea
	else
		partFilter = FurnitureType.PartFilterType.CanPlaceInIsland
	end
	local areas = RoomConfig.getAllAreaCnofigs(houseType, islandType)
	local areaTypes = {}
	local subTypes
	for i = 1, #areas do
		subTypes = areas[i].SubType
		for j = 1, #subTypes do
			if table.indexof(areaTypes, subTypes[j]) == false then
				table.insert(areaTypes, subTypes[j])
			end
		end
	end
	--partFilter:筛选列表室内外类型
	--areaTypes:每个家具可放置的区域类型
	ViewMgr.instance:open("RoomEditView", {partFilter, areaTypes, selectType})
end

function RoomEditorPresentor.showViewInShareStreet(selectType)
	ShareStreetAgent.instance:sendGetStreetShareFurnitureCountRequest(true, function(totalShare)
		RoomEditModel.instance:setShareFurnitures(totalShare)
		--partFilter:筛选列表室内外类型
		--areaTypes:每个家具可放置的区域类型
		local partFilter = FurnitureType.PartFilterType.CanPlaceInShareStreet
		local areas = RoomConfig.getAllAreaCnofigs(0, 2)
		local areaTypes = {}
		local subTypes
		for i = 1, #areas do
			subTypes = areas[i].SubType
			for j = 1, #subTypes do
				if table.indexof(areaTypes, subTypes[j]) == false then
					table.insert(areaTypes, subTypes[j])
				end
			end
		end
		ViewMgr.instance:open("RoomEditView", {partFilter, areaTypes, selectType})
	end)
end

function RoomEditorPresentor.closeView()
	ViewMgr.instance:close("RoomEditView")
end

function RoomEditorPresentor:dependWhatResources()
	local urls =
	{
		RoomEditorPresentor.Url_View,
		RoomEditorPresentor.Fur_Cell,
		RoomEditorToggle.BtnFilter0,
		RoomEditorToggle.BtnFilter1,
		RoomEditorToggle.BtnFilter2,
		RoomEditorToggle.BtnFilter3,
		RoomEditorToggle.BtnFilter4,
		RoomEditorSorter.ItemUrl,
		RoomEditorSampleCell.CellUrl,
		RoomEditorBaseMode.IconUrl,
		RoomEditorBgCell.CellUrl,
	}
	return urls
end

function RoomEditorPresentor:buildViews()
	self.viewComp = RoomEditorView.New()
	self.sorter = RoomEditorSorter.New()
	self.toggle = RoomEditorToggle.New()
	--普通家具
	self._furListL = RoomEditorFurListView.New("verBottom/off/furList", RoomEditorPresentor.Fur_Cell, RoomEditorFurCell, {kScrollDirH, 118, 118, 32, 0, 1})
	local widthConfig = RoomEditorPresentor:_getListConfig(1)
	self._furListH = RoomEditorFurListView.New("verBottom/on/furList", RoomEditorPresentor.Fur_Cell, RoomEditorFurCell, {kScrollDirV, 118, 118, widthConfig.spacing, 0, widthConfig.cellCount})
	self.furList = RoomEditorFurList.New(widthConfig, self._furListL, self._furListH)
	--多选家具
	self._multiListL = RoomEditorFurListView.New("verBottom/off/multiDragList", RoomEditorPresentor.Fur_Cell, RoomEditorAreaFurCell, {kScrollDirH, 118, 118, 32, 0, 1})
	self._multiListH = RoomEditorFurListView.New("verBottom/on/multiDragList", RoomEditorPresentor.Fur_Cell, RoomEditorAreaFurCell, {kScrollDirV, 118, 118, 28, 0, 7})
	self.multiDrag = RoomEditorMultiDragView.New(nil, self._multiListL, self._multiListH)
	--样板
	self._sampleListL = RoomEditorFurListView.New("verBottom/off/templateList", RoomEditorSampleCell.CellUrl, RoomEditorSampleCell, {kScrollDirH, 204, 118, 32, 0, 1})
	widthConfig = RoomEditorPresentor:_getListConfig(2)
	self._sampleListH = RoomEditorFurListView.New("verBottom/on/templateList", RoomEditorSampleCell.CellUrl, RoomEditorSampleCell, {kScrollDirV, 204, 118, widthConfig.spacing, 13, widthConfig.cellCount})
	self.sample = RoomEditorSample.New(nil, self._sampleListL, self._sampleListH)
	--背景
	self._bgListL = RoomEditorFurListView.New("verBottom/off/bgList", RoomEditorBgCell.CellUrl, RoomEditorBgCell, {kScrollDirH, 118, 118, 32, 0, 1})
	widthConfig = RoomEditorPresentor:_getListConfig(3)
	self._bgListH = RoomEditorFurListView.New("verBottom/on/bgList", RoomEditorBgCell.CellUrl, RoomEditorBgCell, {kScrollDirV, 118, 118, widthConfig.spacing, 13, widthConfig.cellCount})
	self.bgList = RoomEditorBgList.New(nil, self._bgListL, self._bgListH)
	--天气
	self._weatherListL = RoomEditorFurListView.New("verBottom/off/weatherList", RoomEditorBgCell.CellUrl, RoomEditorWeatherCell, {kScrollDirH, 118, 118, 32, 0, 1})
	widthConfig = RoomEditorPresentor:_getListConfig(3)
	self._weatherListH = RoomEditorFurListView.New("verBottom/on/weatherList", RoomEditorBgCell.CellUrl, RoomEditorWeatherCell, {kScrollDirV, 118, 118, widthConfig.spacing, 13, widthConfig.cellCount})
	self.weatherList = RoomEditorWeatherList.New(nil, self._weatherListL, self._weatherListH)
	
	--街区
	self._streetListL = RoomEditorFurListView.New("verBottom/off/streetList", RoomEditorBgCell.CellUrl, RoomEditorStreetCell, {kScrollDirH, 118, 118, 32, 0, 1})
	widthConfig = RoomEditorPresentor:_getListConfig(3)
	self._streetListH = RoomEditorFurListView.New("verBottom/on/streetList", RoomEditorBgCell.CellUrl, RoomEditorStreetCell, {kScrollDirV, 118, 118, widthConfig.spacing, 13, widthConfig.cellCount})
	self.streetList = RoomEditorStreetList.New(nil, self._streetListL, self._streetListH)
	
	self.surface = RoomEditorSurface.New()
	self.search = RoomEditorSearcher.New()
	self.joystick = CustomJoystickView.New()
	self.joystick:setJoystickUpdateHandler(function(x, y)
		SceneManager.instance:getCurScene().camera:offset(x / 4, y / 4)
	end)
	self._listViews = {self.furList, self.multiDrag, self.sample, self.bgList, self.weatherList, self.streetList}
	self._type2Mode = {
		[FurnitureType.DecorateType] = {mode = RoomEditModel.Mode.Paper},
		[FurnitureType.Sample] = {mode = RoomEditModel.Mode.Sample, list = self.sample},
		[FurnitureType.Outside] = {mode = RoomEditModel.Mode.Outside, list = self.bgList},
		[FurnitureType.Weather] = {mode = RoomEditModel.Mode.Weather, list = self.weatherList},
		[FurnitureType.ShareStreet] = {mode = RoomEditModel.Mode.ShareStreet, list = self.streetList},
	}
	return {
		self.viewComp,
		self.furList, self._furListL, self._furListH,
		self.toggle,
		self.sorter,
		self.search,
		self._multiListL, self._multiListH, self.multiDrag,
		self._sampleListL, self._sampleListH, self.sample,
		self._bgListL, self._bgListH, self.bgList,
		self._weatherListL, self._weatherListH, self.weatherList,
		self._streetListL, self._streetListH, self.streetList,
		self.surface,
		RoomEditorCapacity.New("verTop/leftGo/btnExpansion"),
		RoomEditorComfortView.New("verTop/leftGo/comfortBar"),
		RoomEditorComplexityView.New("verTop/leftGo"),
		RoomEditorBaseMode.New(),
		RoomEditorTemplateForFriend.New(),
		self.joystick,
	}
end

function RoomEditorPresentor:attachToWhichRoot()
	return ViewRootType.Hud
end
function RoomEditorPresentor:playEnterAnimation()
	self:_onEnterAnimationDone()
	RoomEditModel.instance:_notifyCurrencyChange()
end

function RoomEditorPresentor:setIsLower(isLower)
	if self._isLower == isLower then return end
	self._isLower = isLower
	for _, listComp in ipairs(self._listViews) do
		listComp:changeListView(isLower)
	end
end

function RoomEditorPresentor:changeSearchType(searchType, resetData)
	self._curActiveList:changeSearchType(searchType, resetData)
end

function RoomEditorPresentor:changeFilterTags(filterTags)
	self._curActiveList:changeFilterTags(filterTags)
end

function RoomEditorPresentor:searchFurniture(content)
	self._curActiveList:searchFurniture(content)
end

function RoomEditorPresentor:onSelectSubType(subType, foucsFirst)
	local isNowSpecType = self:_isSpecialType(nil, RoomEditModel.instance._mode)
	local specType, specMode = self:_isSpecialType(subType.id, nil)
	self._curActiveList = self.furList
	if specType ~= nil then
		RoomEditModel.instance:changeMode(specMode.mode)
		if specMode.list then
			self._curActiveList = specMode.list
		end
	elseif isNowSpecType ~= nil then
		RoomEditModel.instance:changeMode(RoomEditModel.Mode.Edit)
	end
	self._curActiveList:onSelectSubType(subType, foucsFirst)
end

function RoomEditorPresentor:_isSpecialType(type, mode)
	for specType, specMode in pairs(self._type2Mode) do
		if specType == type or(type ~= nil and specType == FurnitureConfig.getPartDefine(type).parentId) or specMode.mode == mode then
			return specType, specMode
		end
	end
end

function RoomEditorPresentor:onSorterChange()
	self._curActiveList:onSorterChange()
end

function RoomEditorPresentor:getFurTypes(parentType)
	local partFilter = self:getInOutDoorPartType()
	local children = FurnitureConfig.getPartChildren(parentType)
	local list = {}
	for i = 1, #children do
		if children[i]:checkPartFilter(partFilter) then
			table.insert(list, children[i])
		end
	end
	return list
end

function RoomEditorPresentor:getInOutDoorPartType()
	return self:getFirstParam() [1]
end

function RoomEditorPresentor:getInOutDoorFurType()
	return self:getFirstParam() [2]
end

function RoomEditorPresentor:_getListConfig(index)
	local configs = FurnitureConfig.getFurListConfig(index)
	local curRatio = math.floor(UnityEngine.Screen.width / UnityEngine.Screen.height * 1000) / 1000
	for i, c in ipairs(configs) do
		if curRatio <= c.ratio then
			return c
		end
	end
	return configs[#configs]
end

return RoomEditorPresentor 