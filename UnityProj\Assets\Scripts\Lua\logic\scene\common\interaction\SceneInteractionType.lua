module("logic.scene.common.interaction.SceneInteractionType",package.seeall)
local SceneInteractionType = class("SceneInteractionType")

local fixedList = {}
local idMap = {}
function SceneInteractionType.getHandler(type, key)
	if idMap[type] then
		return idMap[type].handler.New(type, key)
	end
end

function SceneInteractionType.getSceneInteraction(sceneId)
	local list = fixedList[sceneId]
	return list or {}
end

function SceneInteractionType:ctor(typeId, handler, varType, sceneId)
	if typeId == nil then
		printError("<color=red>SceneInteractionType：快导枚举！！！！ -=>>  【" .. tostring(handler.__cname) .. "】</color>")
		return
	end
	self.typeId = typeId
	if sceneId then
		local list = arrayutil.getOrCreateList(fixedList, sceneId)
		table.insert(list, self)
	end
	self.handler = handler
	self.varType = varType
	if idMap[typeId] then
		printError("SceneInteractionType id重复:" .. typeId)
	else
		idMap[typeId] = self
	end
end

function SceneInteractionType.parse(typeId, data)
	local type = idMap[typeId]
	if type.varType then
		return type.varType:parseVar(data[1])
	else
		return data
	end
end

function SceneInteractionType.getTypeById(id)
	return idMap[id]
end
--UserVarKey.New(id, 数据类型（1数字，2字符串，3pb对象）, 是否是数组, pbClass)
SceneInteractionType.ColaGame = SceneInteractionType.New(TinyGameDefine.ColaGame, ColaGameInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.HitRiceCake = SceneInteractionType.New(TinyGameDefine.HitRiceCake, HitRiceCakeInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.ReverseFlag = SceneInteractionType.New(TinyGameDefine.ReverseFlag, ReverseFlagInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.AttackMole = SceneInteractionType.New(TinyGameDefine.AttackMoleGame, AttackMoleInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.SnowBall = SceneInteractionType.New(TinyGameDefine.SnowBall, SnowBallInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.DaLeDou = SceneInteractionType.New(TinyGameDefine.DaLeDou, DaLeDouInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.AllotWheat = SceneInteractionType.New(TinyGameDefine.AllotWheat, AllotWheatInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.GatherHoney = SceneInteractionType.New(TinyGameDefine.GatherHoney, GatherHoneyInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.HuaYunGrab = SceneInteractionType.New(TinyGameDefine.HuaYunGrab, HuaYunGrabInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.GOBANG1 = SceneInteractionType.New(TinyGameDefine.GOBANG1, GobangInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.GOBANG2 = SceneInteractionType.New(TinyGameDefine.GOBANG2, GobangInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.TimeBombGame = SceneInteractionType.New(GameEnum.SceneInteractionType.TIME_BOMB, TimeBombSceneGameHandler, CustomVar.New(3, false, SceneExtension_pb.SceneGameVarNO))
SceneInteractionType.DiscoBall = SceneInteractionType.New(GameEnum.SceneInteractionType.DISCO_BALL, DiscoBallHandler)
SceneInteractionType.SakuraSceneGame = SceneInteractionType.New(10004, PartyFlowerProp)
SceneInteractionType.PartyMusic = SceneInteractionType.New(10005, PartyMusic)
SceneInteractionType.PaoPaoTang = SceneInteractionType.New(GameEnum.SceneInteractionType.CROSS_BUBBLE, CrossBubbleSceneGameHandler, CustomVar.New(3, false, SceneExtension_pb.SceneGameVarNO))
SceneInteractionType.MusicBrother = SceneInteractionType.New(GameEnum.SceneInteractionType.MUSICE_BOTHER,MusicBrotherInteraction, CustomVar.New(3, false, SceneExtension_pb.NpcControlInfoNO))
-- SceneInteractionType.HappyNpc = SceneInteractionType.New(GameEnum.SceneInteractionType.WORLD_BOSS_NPC,HappyNpcInteraction, CustomVar.New(3, false, SceneExtension_pb.HappyInfoNO))
SceneInteractionType.Seesaw = SceneInteractionType.New(GameEnum.SceneInteractionType.SEESAW, Seesaw)
SceneInteractionType.LibraryLottery = SceneInteractionType.New(GameEnum.SceneInteractionType.LIBRARY_LOTTERY, LibraryLotteryController, nil, 14)
SceneInteractionType.HotPotSpa = SceneInteractionType.New(GameEnum.SceneInteractionType.HOT_POT_SPA, HotPotSpa, nil, 17)
SceneInteractionType.Bubble = SceneInteractionType.New(GameEnum.SceneInteractionType.BUBBLE,BubbleInteraction, CustomVar.New(1, false))
SceneInteractionType.AobiMonumentValley = SceneInteractionType.New(GameEnum.SceneInteractionType.MONUMENT_VALLEY,AobiMonumentValleyInteraction, nil, 12)
SceneInteractionType.AffinityCheck = SceneInteractionType.New(GameEnum.SceneInteractionType.FRIEND_CHECK_TEAM, AffinityCheckInteraction, CustomVar.New(3, false, FriendshipCheckExtension_pb.FriendshipCheckInfoNO))
SceneInteractionType.CouncilTouchElf = SceneInteractionType.New(GameEnum.SceneInteractionType.COUNCIL_TOUCH_ELF, CouncilTouchElfInteraction,CustomVar.New(3, false, SceneExtension_pb.CouncilDonateAllShareRewardsNO))
SceneInteractionType.MistyForestScene = SceneInteractionType.New(GameEnum.SceneInteractionType.FOGGY_FOREST_SCENE, MistyForestGameInteraction,CustomVar.New(3, false, FoggyForestExtension_pb.FoggyForestNO))
SceneInteractionType.AffinityCheckScene = SceneInteractionType.New(GameEnum.SceneInteractionType.FRIEND_FOGGY_SCENE, AffinityCheckGameInteraction, CustomVar.New(3, false, FriendshipCheckExtension_pb.FriendshipCheckSkinInfoList))
-- SceneInteractionType.HappyBox = SceneInteractionType.New(GameEnum.SceneInteractionType.WORLD_BOSS_BOX, HappyBoxInteraction,CustomVar.New(3, false, SceneExtension_pb.HappyBoxInfoNO))
SceneInteractionType.CouncilSpa = SceneInteractionType.New(GameEnum.SceneInteractionType.COUNCIL_SPA, CouncilSpaInteraction, nil, 20)
SceneInteractionType.TeamScene = SceneInteractionType.New(GameEnum.SceneInteractionType.TEAM_SCENE, SceneTeamInteraction,CustomVar.New(3, false, SceneExtension_pb.SceneTeamNO))
SceneInteractionType.GOFNpc = SceneInteractionType.New(GameEnum.SceneInteractionType.GOD_FLOWER_NPC, GodOfFlowerInteraction, CustomVar.New(3, false, SceneExtension_pb.GodFlowerInfoNO))
SceneInteractionType.GOFBox = SceneInteractionType.New(GameEnum.SceneInteractionType.GOD_FLOWER_BOX, GOFBoxInteraction, CustomVar.New(3, false, SceneExtension_pb.GodFlowerBoxInfoNO))
SceneInteractionType.FriendCheckBreeze = SceneInteractionType.New(GameEnum.SceneInteractionType.FRIEND_CHECK_BREEZE, FriendCheckBreezeInteraction, CustomVar.New(3, false, FriendshipCheckExtension_pb.FriendshipCheckSkinInfoList))
SceneInteractionType.FriendCheckShimmer = SceneInteractionType.New(GameEnum.SceneInteractionType.FRIEND_CHECK_SHIMMER, FriendCheckShimmerInteraction, CustomVar.New(3, false, FriendshipCheckExtension_pb.FriendshipCheckSkinInfoList))
SceneInteractionType.StarCeremonyInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.ACTIVITY_151, StarCeremonyInteraction, nil, 30)
SceneInteractionType.StarFarewellInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.ACTIVITY_148, StarFarewellInteraction, nil, 30)
SceneInteractionType.StarMatch = SceneInteractionType.New(GameEnum.SceneInteractionType.STAR_MATCH, StarMatchInteraction, nil, 30)
SceneInteractionType.AirShpParty = SceneInteractionType.New(GameEnum.SceneInteractionType.AIR_SHIP_PARTY, AirShipPartyInteraction, nil, 33)
SceneInteractionType.GameRoomWaiting = SceneInteractionType.New(GameEnum.SceneInteractionType.GAME_ROOM_INFO, GameRoomWaitingInteraction, CustomVar.New(3, false, GameRoomExtension_pb.GameRoomFullInfoNO))
-- SceneInteractionType.FullMoon = SceneInteractionType.New(GameEnum.SceneInteractionType.FULL_MOON, FullMoonInteraction, nil, 6)
SceneInteractionType.FriendCheckDawn = SceneInteractionType.New(GameEnum.SceneInteractionType.FRIEND_CHECK_DAWN, FriendCheckDawnInteraction, CustomVar.New(3, false, FriendshipCheckExtension_pb.FriendshipCheckSkinInfoList))
SceneInteractionType.FriendDawnScene = SceneInteractionType.New(GameEnum.SceneInteractionType.FRIEND_DAWN_SCENE, FriendDawnSceneInteraction,nil,37)
SceneInteractionType.CouncilBoss = SceneInteractionType.New(GameEnum.SceneInteractionType.COUNCIL_DEFEND, CBossInteraction, CustomVar.New(3, false, CouncilDefendExtension_pb.CouncilDefendNO))
SceneInteractionType.PIPELINE = SceneInteractionType.New(GameEnum.SceneInteractionType.PIPELINE,AssemblyLineInteraction, CustomVar.New(3, false, Activity215Extension_pb.ActPipelineAllInfoNO))
SceneInteractionType.HappyRestaurant = SceneInteractionType.New(GameEnum.SceneInteractionType.ACTIVITY_214,HappyRestaurantInteraction, nil, 44)
SceneInteractionType.LanternRiddle = SceneInteractionType.New(GameEnum.SceneInteractionType.ACTIVITY_217,LanternRiddleSceneInteraction, nil, 95)
SceneInteractionType.BackflowParty = SceneInteractionType.New(GameEnum.SceneInteractionType.BACKFLOW_PARTY, BackflowPartyInteraction, CustomVar.New(3, false, Backflow2Extension_pb.BackflowPartySceneInfoNO))
SceneInteractionType.AnniversaryGala = SceneInteractionType.New(GameEnum.SceneInteractionType.ACTIVITY_255, AnniversaryGalaSceneInteraction, nil,52)
SceneInteractionType.AnniversaryGalaGodLike = SceneInteractionType.New(GameEnum.SceneInteractionType.SCENE_GROUP_GOD_LIKE, AnniversaryGalaGodlikeInteraction)
SceneInteractionType.ThrowCake1 = SceneInteractionType.New(TinyGameDefine.ThrowCake1, ThrowCakeInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.ThrowCake2 = SceneInteractionType.New(TinyGameDefine.ThrowCake2, ThrowCakeInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.ThrowCake3 = SceneInteractionType.New(TinyGameDefine.ThrowCake3, ThrowCakeInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.ThrowCake4 = SceneInteractionType.New(TinyGameDefine.ThrowCake4, ThrowCakeInteraction, CustomVar.New(3, false, SceneExtension_pb.TinyGameStateInfo))
SceneInteractionType.Activity_265 = SceneInteractionType.New(GameEnum.SceneInteractionType.Activity_265,InterstellarScienceGameInteraction, CustomVar.New(3, false, Activity265Extension_pb.Act265AnswerNO))
SceneInteractionType.Piano = SceneInteractionType.New(GameEnum.SceneInteractionType.PIANO_STAGE, PianoInteraction, nil, 55)
SceneInteractionType.SeaSceneSpaCar = SceneInteractionType.New(GameEnum.SceneInteractionType.SEA_SCENE_SPA_CAR,SeaSpaCarInteraction,CustomVar.New(3, false, Activity433Extension_pb.Act433GameNO))
SceneInteractionType.CelebParty = SceneInteractionType.New(GameEnum.SceneInteractionType.CELEB_PARTY, CelebPartyInteraction, CustomVar.New(3, false, Activity471Extension_pb.CelebPartyInfoNO))
SceneInteractionType.DreamRoom = SceneInteractionType.New(GameEnum.SceneInteractionType.DreamRoom, DreamRoomInteraction, nil, 106)

--这里不再用枚举了，key经常变
--倒计时系列活动场景互动
-- SceneInteractionType.NewYearStone = SceneInteractionType.New(20029, NewYearStoneInteraction, nil, 5)
-- SceneInteractionType.NewYearStar = SceneInteractionType.New(20030, NewYearStarInteraction, nil, 12)
SceneInteractionType.NewYearTimeDown = SceneInteractionType.New(20031, NewYearTimeDownInteraction, nil, 12)
SceneInteractionType.NewYearWallPaper = SceneInteractionType.New(20032, NewYearWallPaperInteraction, nil, 12)
-- SceneInteractionType.NewYearMeteor = SceneInteractionType.New(20033, NewYearMeteorInteraction, nil, 12)

SceneInteractionType.NewYearNpc = SceneInteractionType.New(GameEnum.SceneInteractionType.WORLD_BOSS_NPC, SFInteraction, CustomVar.New(3, false, Activity219Extension_pb.Act219WorldBossInfoNO))
SceneInteractionType.NewYearBox = SceneInteractionType.New(GameEnum.SceneInteractionType.WORLD_BOSS_BOX, SFBoxInteraction, CustomVar.New(3, false, Activity219Extension_pb.Act219WorldBossBoxInfoNO))

SceneInteractionType.GobangFurniture = SceneInteractionType.New(20040, GobangFurnitureInteraction, CustomVar.New(3, false, GobangExtension_pb.GobangFurnitureSceneNO))

SceneInteractionType.WealthGod = SceneInteractionType.New(GameEnum.SceneInteractionType.GOD_OF_WEALTH_QUEUE, WealthGodFollowInteraction, CustomVar.New(3, false, Activity295Extension_pb.Act295QueueNO))

SceneInteractionType.RoyalPalaceInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.ROYALPALACE_SCENE, RoyalPalaceInteraction, nil, 67)
SceneInteractionType.QingMingRiverInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.QINGMINGRIVER_SCENE, QingMingRiverInteraction, nil, 68)
SceneInteractionType.PortalInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.CLOTHES_PORTAL, PortalInteraction, CustomVar.New(3, false, ClothesExtension_pb.SceneClothesPortalInfoNO))
SceneInteractionType.transformSkinInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.PLACING_ITEM, TransformSkinInteraction, CustomVar.New(3, false, SceneInteractiveExtension_pb.ScenePlacingItemNO))
-- SceneInteractionType.AobiSquareInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.AOBISQUZRE_SCENE, AobiSquareInteraction, nil, 6)
SceneInteractionType.SeaDanceInteraction = SceneInteractionType.New(GameEnum.SceneInteractionType.SEA_DANCE, SeaDanceInteraction, CustomVar.New(3, false, Activity419Extension_pb.SeaDancePoolInfoNO))
SceneInteractionType.SeaBoss = SceneInteractionType.New(GameEnum.SceneInteractionType.SEA_SCENE_BOSS, SeaBossInteraction,CustomVar.New(3, false, Activity428Extension_pb.Act428BossGameNO))
SceneInteractionType.HuaYunScene = SceneInteractionType.New(GameEnum.SceneInteractionType.HUAYUN_SCENE, HuaYunSceneInteraction, nil, 16)
SceneInteractionType.BonfireParty = SceneInteractionType.New(GameEnum.SceneInteractionType.ACTIVITY_445_DANCE, BonfirePartyInteraction, CustomVar.New(3, false, Activity445Extension_pb.Act445ShowAreaInfoNO))
SceneInteractionType.MovieCity = SceneInteractionType.New(GameEnum.SceneInteractionType.MovieCity,MovieCityInteraction,nil,95)

return SceneInteractionType