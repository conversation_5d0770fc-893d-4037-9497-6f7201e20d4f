module("logic.scene.unit.comp.ScenePosComp",package.seeall)
local ScenePosComp = class("ScenePosComp", UnitComponentBase)

local Threshold = 0.01
local Dir = UnitDirection

local function getZ(pos)
	if not pos.HasField or pos:<PERSON><PERSON><PERSON>("posZ") then
		return pos.posZ
	else
		return nil
	end
end

function ScenePosComp:onInit()
	self.mover = self._unit.mover
	self.mover:addListener(UnitNotify.PosChanged, self.onPosChanged, self)
	self.mover:addListener(UnitNotify.StartMove, self.onStartMove, self)
	self.mover:addListener(UnitNotify.Interrupt, self.onInterrupt, self)
	self.mover:addListener(UnitNotify.Arrive, self.onArrive, self)
	self.needInterrupt = true
	self._serverPos = {}
end

function ScenePosComp:onPosChanged()
	local x, y, z = self.mover:getPos()
	y = y + self._unit.indexId * 0.00001
	if self._mount then
		self._mount:changePos(x, y, z)
	else
		GameUtils.setPosInScene(self._unit.go, x, y, z)
	end
	local target = self.mover:getCurWayPoint()
	if target then
		self:setNewDir(target.x, target.y)
		-- call(self._unit.onPosChanged, self)
	end 
end

function ScenePosComp:onUpdate()
	self.mover:update()
end

function ScenePosComp:setNewDir(tx, ty)
		local x, y = self.mover:getPos()	
		-- local newDir = self._unit:getDirection()
		-- if tx - x < -Threshold then
		-- 	newDir = bit.band(newDir, 2)
		-- elseif tx - x > Threshold then
		-- 	newDir = bit.bor(newDir, 1)
		-- end
		-- if ty - y < -Threshold then
		-- 	newDir = bit.band(newDir, 1)
		-- elseif ty - y > Threshold then
		-- 	newDir = bit.bor(newDir, 2)
		-- end

		local newDir = self._unit:getDirection()
		if newDir == nil then
			return
		end
		if tx - x < -Threshold then
			newDir = Dir.setLeftRight(newDir, true)
		elseif tx - x > Threshold then
			newDir = Dir.setLeftRight(newDir, false)
		end
		if ty - y < -Threshold then
			newDir = Dir.setFrontBack(newDir, true)
		elseif ty - y > Threshold then
			newDir = Dir.setFrontBack(newDir, false)
		end
		if newDir ~= self._unit:getDirection() then
			self._unit:setDirection(newDir)
		end
end

local function call(func, target)
	if func then
		func(target)
	end
end

function ScenePosComp:onStartMove()
	call(self._unit.onStartMove, self._unit)
end 

function ScenePosComp:onInterrupt()
	call(self._unit.onInterrupt, self._unit)
end

function ScenePosComp:onStopMove()
	call(self._unit.onStopMove, self._unit)
end

function ScenePosComp:onArrive()
	call(self._unit.onArrive, self._unit)
end

function ScenePosComp:setPath(path, beginTime, isInit)
	local startPos
	local startIndex = 1
	startPos = path[startIndex]
	startIndex = startIndex + 1
	local hasZ = getZ(startPos) ~= nil
	--根据时间差跳过已经走过的路程，得到角色现在所在的起点
	if beginTime then
		local elapsedTime = GameUtils.getServerTimeStamp() - beginTime

			local deltaTime = math.max(0, elapsedTime)
			local totalDistance = deltaTime * self._unit:getSpeed() / 1000
			local dx, dy, dz, dis
			while startIndex <= #path do
				dx = path[startIndex].posX - startPos.posX
				dy = path[startIndex].posY - startPos.posY
				dis = math.sqrt(dx*dx + dy*dy)
				if totalDistance >= dis then
					totalDistance = totalDistance - dis
					startPos = path[startIndex]
					startIndex = startIndex + 1
				else
					local pos = {}
					pos.posX = totalDistance / dis * dx + startPos.posX
					pos.posY = totalDistance / dis * dy + startPos.posY
					if hasZ then
						dz = path[startIndex].posZ - startPos.posZ
						pos.posZ = totalDistance / dis * dz + startPos.posZ
					end
					startPos = pos
					break
				end
			end

			if isInit or elapsedTime > 500 or startIndex > #path then
				--初始化或者经过时间大于500毫秒或者已经移动完成的时候要直接设置坐标
				local newDir = self._unit:getDirection()
				if dx < -Threshold then
					newDir = Dir.setLeftRight(newDir, true)
				elseif dx > Threshold then
					newDir = Dir.setLeftRight(newDir, false)
				end
				if dy < -Threshold then
					newDir = Dir.setFrontBack(newDir, true)
				elseif dy > Threshold then
					newDir = Dir.setFrontBack(newDir, false)
				end
				self._unit:setDirection(newDir)
				self._unit:setPos(startPos.posX, startPos.posY, hasZ and startPos.posZ or nil)
			end
	end
	local ox, oy = self.mover:getPos()
	if #path >= startIndex then
		self._serverPos.x = startPos.posX
		self._serverPos.y = startPos.posY
		self._serverPos.z = getZ(startPos)
		local destPos = path[#path]
		local dx = destPos.posX - startPos.posX
		local dy = destPos.posY - startPos.posY
		local dz = hasZ and destPos.posZ - startPos.posZ or 0
		--根据起点与终点的距离算出插值的时间，最大1秒
		local lerpTime = math.min(1, math.sqrt(dx*dx + dy*dy + dz*dz) / self.mover._speed)
		self:_setWayPoint(path[startIndex].posX, path[startIndex].posY, getZ(path[startIndex]), self._serverPos, lerpTime)	
		-- printInfo("set ppp", path[startIndex].posX, path[startIndex].posY, ox, oy, startPos.posX, startPos.posY, lerpTime)
		for i=startIndex+1, #path do
			-- printInfo("set ppp", path[i].posX, path[i].posY, ox, oy, self.mover._speedX, self.mover._speedY)
			self.mover:addWayPoint(path[i].posX, path[i].posY, getZ(path[i]))	
			-- printInfo("way point", path[i].posX, path[i].posY)
		end
		-- self.mover:update()
	else
		self:onArrive()
	end
end



function ScenePosComp:moveTo(x, y)
	self:_setWayPoint(x, y)
end

function ScenePosComp:_setWayPoint(x, y, z, startPos, lerpTime)
	if self.needInterrupt and self.mover:getCurWayPoint() then
		self:onInterrupt()
	end
	self.mover:setWayPoint(x, y, z, startPos, lerpTime)	
end

function ScenePosComp:walkTo(x, y, targetRange, canJump, checkMove)
	local mapMgr = SceneManager.instance:getCurScene().mapMgr
	local startX, startY = self.mover:getPos()
	local wayPoints, find= mapMgr:findPath(self._unit, x, y, nil, targetRange, checkMove)	
	if find then
		-- printInfo("path", wayPoints[#wayPoints].posX, wayPoints[#wayPoints].posY)
		self:setPath(wayPoints)
		-- self.mover:setWayPoint(wayPoints[1].posX, wayPoints[1].posY)			
		-- for i=2, #wayPoints do
		-- 	self.mover:addWayPoint(wayPoints[i].posX, wayPoints[i].posY)	
		-- end	
	elseif canJump then
		self:setPos(x, y)
		self:onArrive()
	end
	return find,wayPoints
end

function ScenePosComp:finishMove()
	local pos,lastPos
	local len = #self.mover._wayPoints
	if len > 0 then
		pos = self.mover._wayPoints[len]
		if len > 1 then
			lastPos = self.mover._wayPoints[len-1]
		else
			lastPos = self.mover:getCurWayPoint()
		end
		local dx = pos.x - lastPos.x
		local dy = pos.y - lastPos.y
		local newDir = Dir.getDirection(dy < 0, dx < 0)
		self._unit:setDirection(newDir)
	else
		pos = self.mover:getCurWayPoint()
	end
	if pos then
		self.mover:setPosDirectly(pos.x, pos.y)
		self:onArrive()
	end
end

function ScenePosComp:setPos(x, y, z)
	self.mover:setPosDirectly(x, y, z)
	call(self._unit.onStopMove, self._unit)
end

function ScenePosComp:stop()
	self.mover:clearWayPoints()
	call(self._unit.onStopMove, self._unit)
end

function ScenePosComp:onDestroy()
	self.mover:removeListener(UnitNotify.PosChanged, self.onPosChanged, self)
	self.mover:removeListener(UnitNotify.StartMove, self.onStartMove, self)
	-- self.mover:removeListener(UnitNotify.Interrupt, self.onInterrupt, self)
	self.mover:removeListener(UnitNotify.Arrive, self.onArrive, self)

end

function ScenePosComp:setMount(mount)
	self._mount = mount
end

function ScenePosComp:getMount()
	return self._mount
end

return ScenePosComp