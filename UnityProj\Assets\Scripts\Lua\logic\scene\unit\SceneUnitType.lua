module("logic.scene.unit.SceneUnitType", package.seeall)
local SceneUnitType = class("SceneUnitType")

function SceneUnitType:ctor(cls, resName, loadType, updateFrame)
	self.loadType = loadType or 1
	self.cls = cls
	self.resName = resName
	self.updateFrame = updateFrame
end

function SceneUnitType:getInstance()
	local go
	if self.resName then
		if self.loadType == 1 then
			go = CommonRes.instance:getResInstance(self.resName)
		else
			go = goutil.clone(SceneManager.instance:getCurScene():getSceneRes(self.resName))
		end
	else
		go = goutil.create("unit", false)
	end
	local newUnit = PjAobi.LuaComponent.Add(go, self.cls)
	return newUnit, go
end

function SceneUnitType:getName()
	return self.cls.__cname
end

--SceneUnitType.New(unit类, 资源url, 资源来源类型（1：CommonRes，2：Scene)
--updateFrame，需要每帧执行update
SceneUnitType.Player = SceneUnitType.New(PlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.UnitTaskPlayer = SceneUnitType.New(PlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.Furniture = SceneUnitType.New(RoomUnitFurnishing, nil, 1, false)
SceneUnitType.Plant = SceneUnitType.New(FarmPlantUnit, nil, 1, false)
SceneUnitType.SpineFurniture = SceneUnitType.New(RoomSpineUnit, nil, 1, false)
SceneUnitType.SurfaceFurniture = SceneUnitType.New(SurfaceUnitFurnishing, nil, 1, false)
SceneUnitType.SpecialFurniture = SceneUnitType.New(SpecialFurnishing, nil, 1, false)
SceneUnitType.Npc = SceneUnitType.New(NpcUnit, CommonResPath.NPC, 1, true)
SceneUnitType.ShowPet = SceneUnitType.New(PetUnit, CommonResPath.Pet, 1, true)
SceneUnitType.Pet = SceneUnitType.New(PetUnit, CommonResPath.Pet, 1, true)
SceneUnitType.Elf = SceneUnitType.New(ElfUnit, CommonResPath.Elf, 1, true)
SceneUnitType.ElfEgg = SceneUnitType.New(ElfEggUnit, nil, 1, false)
SceneUnitType.Collect = SceneUnitType.New(CollectionUnit, CommonResPath.CollectUnit, 1, true)
SceneUnitType.SceneObj = SceneUnitType.New(SceneObjUnit, nil, 1, true)
SceneUnitType.TaskSceneObj = SceneUnitType.New(SceneObjUnit, nil, 1, true)
SceneUnitType.SnailUnit = SceneUnitType.New(SnailUnit, CommonResPath.Elf, 1, true)
SceneUnitType.Parrot = SceneUnitType.New(ParrotUnit, CommonResPath.Elf, 1, true)
SceneUnitType.SimpleInteraction = SceneUnitType.New(SimpleInteractionUnit, CommonResPath.SimpleInteraction, 1, true)
SceneUnitType.BurrowUnit = SceneUnitType.New(BurrowUnit, CommonResPath.BurrowUnit, 1, true)
SceneUnitType.SnowBallUnit = SceneUnitType.New(SnowBallUnit, CommonResPath.SnowBallUnit, 1, true)
SceneUnitType.IceCarvingUnit = SceneUnitType.New(IceCarvingUnit, nil, 1, true)
SceneUnitType.UnlockAreaDiJing = SceneUnitType.New(RoomUnlockUnit, nil, 1, false)
SceneUnitType.MusicBrotherUnit = SceneUnitType.New(MusicBrotherUnit, CommonResPath.Elf, 1, true)
SceneUnitType.WalkableUnit = SceneUnitType.New(WalkableUnit, CommonResPath.NPC, 1, true)
SceneUnitType.HappyNpcUnit = SceneUnitType.New(HappyNpcUnit, CommonResPath.NPC, 1, true)
SceneUnitType.TileMapUnit = SceneUnitType.New(TileMapUnit, CommonResPath.NPC, 1, true)
SceneUnitType.HerdSheepUnit = SceneUnitType.New(HerdSheepUnit, CommonResPath.Player, 1, true)
SceneUnitType.BeehiveUnit = SceneUnitType.New(BeehiveUnit, nil, 1, true)
SceneUnitType.FriendTestUnit = SceneUnitType.New(FriendTestUnit, nil, 1, true)
SceneUnitType.FriendShadowUnit = SceneUnitType.New(FriendShadowUnit, nil, 1, true)
SceneUnitType.CouncilObjBase = SceneUnitType.New(CouncilBuildingUnit, nil, 1, true)
SceneUnitType.ElfBattleUnit = SceneUnitType.New(ElfBattleUnit, CommonResPath.Elf, 1, false)
SceneUnitType.ElfBattleElfJudgment = SceneUnitType.New(ElfBattleElfJudgment, CommonResPath.Elf, 1, false)
SceneUnitType.CarvingUnit = SceneUnitType.New(CarvingUnit, nil, 1, true)
SceneUnitType.GodOfFlowerUnit = SceneUnitType.New(GodOfFlowerUnit, CommonResPath.NPC, 1, true)
SceneUnitType.CushionUnit = SceneUnitType.New(SceneCushionUnit, nil, 1, true)
SceneUnitType.FurnitureCushionUnit = SceneUnitType.New(FurnitureCushionUnit, nil, 1, true)
SceneUnitType.ClotehsStagePlayer = SceneUnitType.New(PlayerUnit, CommonResPath.Player, 1)
SceneUnitType.WerewolfObjectUnit = SceneUnitType.New(WerewolfObjectUnit, nil, 1, false)
SceneUnitType.WerewolfPlayerUnit = SceneUnitType.New(WerewolfPlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.WerewolfGhostUnit = SceneUnitType.New(WerewolfGhostUnit, CommonResPath.Player, 1, true)
SceneUnitType.FriendBreezeNpcUnit = SceneUnitType.New(FriendBreezeNpcUnit, nil, 1, true)
SceneUnitType.CottageUnit = SceneUnitType.New(CottageUnit, nil, 1, true)
SceneUnitType.CouncilBoss_BossUnit = SceneUnitType.New(CouncilBoss_BossUnit, nil, 1, true)
SceneUnitType.CouncilBoss_EnemyUnit = SceneUnitType.New(CouncilBoss_EnemyUnit, nil, 1, true)
SceneUnitType.MoveHouseFurtObjUnit = SceneUnitType.New(MoveHouseFurtObjUnit, nil, 1, true)
SceneUnitType.MoveHousePlayerUnit = SceneUnitType.New(MoveHousePlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.CombineGamePlayerUnit = SceneUnitType.New(CombineGamePlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.SFUnit = SceneUnitType.New(SFUnit, CommonResPath.NPC, 1, true)
SceneUnitType.HappyRestaurantNPC = SceneUnitType.New(HappyRestaurantNpcUnit, CommonResPath.Player, 1, true)
SceneUnitType.HappyRestaurantSpecialNPC = SceneUnitType.New(HappyRestaurantSpecialNpcUnit, CommonResPath.Player, 1, true)
SceneUnitType.HappyRestaurantAI = SceneUnitType.New(HappyRestaurantAIUnit, CommonResPath.Player, 1, true)
SceneUnitType.Snake2PlayerUnit = SceneUnitType.New(Snake2PlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.Snake2TailUnit = SceneUnitType.New(SceneObjUnit, nil, 1, true)
SceneUnitType.Snake2FoodUnit = SceneUnitType.New(SceneObjUnit, nil, 1, false)
SceneUnitType.Snake2BuffItemUnit = SceneUnitType.New(SceneObjUnit, nil, 1, false)
SceneUnitType.PhantomCreatureUnit = SceneUnitType.New(PhantomCreatureUnit, nil, 1, true)
SceneUnitType.PhantomCreatureRewardUnit = SceneUnitType.New(PhantomCreatureRewardUnit, nil, 1, false)
SceneUnitType.MusicGamePlayerUnit = SceneUnitType.New(MusicGamePlayerUnit, CommonResPath.Player, 1, false)
SceneUnitType.LumberJackPlayerUnit = SceneUnitType.New(LumberJackPlayerUnit, CommonResPath.Player, 1, false)
SceneUnitType.ActCollectUnit = SceneUnitType.New(ActCollectUnit, nil, 1, true)
SceneUnitType.AnniversaryGalaUnit = SceneUnitType.New(AnniversaryGalaUnit, CommonResPath.Player, 1, true)
SceneUnitType.MoveOrDiePlayerUnit = SceneUnitType.New(MoveOrDie_PlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.MoveOrDieSceneObj = SceneUnitType.New(MoveOrDie_SceneObjUnit, nil, 1, true)
SceneUnitType.GalaParallelPlayer = SceneUnitType.New(GalaParallelPlayer, CommonResPath.ParallelPlayer, 1, true)
SceneUnitType.AnniversaryGalaGodlikeUnit = SceneUnitType.New(AnniversaryGalaUnit, CommonResPath.Player, 1, true)
SceneUnitType.PatrolNpcUnit = SceneUnitType.New(PatrolNpcUnit,CommonResPath.NPC, 1, true)
SceneUnitType.MusicBandNpcUnit = SceneUnitType.New(MusicBandNpcUnit,CommonResPath.NPC, 1, true)
SceneUnitType.MusicBandElfUnit = SceneUnitType.New(ElfUnit, CommonResPath.Elf, 1, true)
SceneUnitType.PenguinTrailUnit = SceneUnitType.New(PenguinTrailUnit,nil, 1, true)
SceneUnitType.AnimalWorldUnit = SceneUnitType.New(AnimalWorldUnit,nil, 1, true)
SceneUnitType.TestBakeUnit = SceneUnitType.New(TestBakeUnit, nil, 1, true)
SceneUnitType.WealthGod = SceneUnitType.New(WealthGodUnit, nil, 1, true)	--财神爷跟随
SceneUnitType.RhythmBattlesPlayerUnit = SceneUnitType.New(RhythmBattlesPlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.PortalUnit = SceneUnitType.New(PortalUnit, nil, 1, false)
SceneUnitType.EcoParkIslandSpecies = SceneUnitType.New(EcoParkIslandSpeciesUnit, CommonResPath.Species, 1, true)
SceneUnitType.TransformSkinUnit = SceneUnitType.New(TransformSkinUnit, nil, 1, false)
SceneUnitType.SeaMountUnit = SceneUnitType.New(SeaMountUnit, nil, 1, true)
SceneUnitType.PetBattlesUnit = SceneUnitType.New(PetBattlesUnit, CommonResPath.Pet, 1, true)
SceneUnitType.AnniversaryInteractionUnit = SceneUnitType.New(AnniversaryInteractionUnit, nil, 1, true)
SceneUnitType.DollHousePlayerUnit = SceneUnitType.New(DollHousePlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.DollHouseSkillUnit = SceneUnitType.New(DollHouseSkillUnit, nil, 1, false)
SceneUnitType.IceSkatingPlayerUnit = SceneUnitType.New(IceSkatingPlayerUnit, IceSkatingConfig.Player, 2, true)
SceneUnitType.IceSkatingMonsterUnit = SceneUnitType.New(IceSkatingMonsterUnit, nil, 1, true)
SceneUnitType.IceSkatingSnowBallUnit = SceneUnitType.New(IceSkatingSnowBallUnit, nil, 1, true)
SceneUnitType.Act452NpcUnit = SceneUnitType.New(Act452NpcUnit,CommonResPath.NPC, 1, true)
SceneUnitType.PacManPlayerUnit = SceneUnitType.New(PacManPlayerUnit,PacManGameConfig.Player,2, true)
SceneUnitType.Free3DPlayer = SceneUnitType.New(Free3DPlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.FlyingChessPlayerUnit = SceneUnitType.New(FlyingChessPlayerUnit, CommonResPath.Player, 1, true)
SceneUnitType.FlyingChessEventUnit = SceneUnitType.New(FlyingChessEventUnit, nil, 1, false)
SceneUnitType.FlyingChessPiece = SceneUnitType.New(FlyingChessPieceUnit, nil, 1, true)
SceneUnitType.FlyingChessVehicleUnit = SceneUnitType.New(FlyingChessVehicleUnit, nil, 1, false)
SceneUnitType.FlyingChessActionUnit = SceneUnitType.New(FlyingChessActionUnit, nil, 1, false)
SceneUnitType.CelebPartyNpcUnit = SceneUnitType.New(CelebPartyNpcUnit, nil, 1, true)
SceneUnitType.CelebPartyPixieUnit = SceneUnitType.New(CelebPartyPixieUnit, nil, 1, true)


--在小屋小岛排序（家具本来在内）
SceneUnitType.SortInRoom = {
    SceneUnitType.Player,
    SceneUnitType.UnitTaskPlayer,
    SceneUnitType.EcoParkIslandSpecies,
    SceneUnitType.Npc,
    SceneUnitType.Pet,
	SceneUnitType.ShowPet,
    SceneUnitType.SceneObj,
    SceneUnitType.Elf,
    SceneUnitType.CushionUnit,
    SceneUnitType.PortalUnit,
	SceneUnitType.TransformSkinUnit,
    SceneUnitType.SeaMountUnit,
    SceneUnitType.FurnitureCushionUnit
}
--编辑模式下要隐藏
SceneUnitType.HideInEdit = {
    SceneUnitType.Player,
    SceneUnitType.UnitTaskPlayer,
    SceneUnitType.EcoParkIslandSpecies,
    SceneUnitType.Npc,
    SceneUnitType.Pet,
    SceneUnitType.SceneObj,
    SceneUnitType.Elf,
    SceneUnitType.CushionUnit,
    SceneUnitType.PortalUnit,
    SceneUnitType.SeaMountUnit,
    SceneUnitType.FurnitureCushionUnit
}
--自由场景自动面向摄像机
SceneUnitType.FaceToFreeCamera = {
    SceneUnitType.Free3DPlayer,
    SceneUnitType.Npc,
    SceneUnitType.Pet,
    SceneUnitType.SeaMountUnit,
    SceneUnitType.Elf,
    SceneUnitType.CushionUnit,
    SceneUnitType.CelebPartyPixieUnit,
}

return SceneUnitType
