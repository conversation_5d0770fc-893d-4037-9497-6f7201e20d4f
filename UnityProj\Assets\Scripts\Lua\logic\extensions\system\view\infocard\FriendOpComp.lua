module("logic.extensions.system.view.infocard.FriendOpComp", package.seeall)

local FriendOpComp = class("FriendOpComp", ViewComponent)

--- view初始化时会执行
function FriendOpComp:buildUI()
	self.otherGO = self:getGo("left/otherGO")
	self._followBtn = self:getBtn("left/otherGO/followBtn")
	self._followBtn:AddClickListener(self._onClickFollow, self)
	self._unFollowBtn = self:getBtn("left/otherGO/isFollowed")
	self._unFollowText = self:getText("left/otherGO/isFollowed/Text")
	self._unFollowBtn:AddClickListener(self._onClickUnFollow, self)
	self._addFriendBtn = self:getBtn("left/otherGO/friendBtn")
	self._addFriendBtn:AddClickListener(self._onClickAddFriend, self)
	self._isAddedFriendBtn = self:getBtn("left/otherGO/isAddedFriend")
	self._removeFriendBtn = self:getBtn("left/otherGO/removeFriendBtn")
	self._removeFriendBtn:AddClickListener(self._onClickRemoveFriend, self)
	-- self._blacklistBtn = self:getBtn("btnBlacklist")
	-- self._blacklistBtn:AddClickListener(self._onClickBlacklist,self)
	self:getBtn("left/otherGO/btnChat"):AddClickListener(self._onClickChat, self)
	self:getBtn("left/otherGO/btnHouse"):AddClickListener(self._onClickGotoHouse, self)
	-- self:getBtn("otherGO/treeBtn"):AddClickListener(self._onClickTree,self)
	self._btnReport = self:getBtn("left/setGo/setView/isReport")
	self._btnReport:AddClickListener(self._onClickReport, self)
	self._addBlackBtn = self:getBtn("left/setGo/setView/blackBtn")
	self._addBlackBtn:AddClickListener(self._onClickAddBlack, self)
	self._unBlackBtn = self:getBtn("left/setGo/setView/unBlackBtn")
	self._unBlackBtn:AddClickListener(self._onClickRemoveBlack, self)
	self._removeFensBtn = self:getBtn("left/setGo/setView/removeFensBtn")
	self._removeFensBtn:AddClickListener(self._onClickRemoveFans, self)
	self.btnGroupView = self:getGo("left/setGo/setView")
	self._btnGroup = self:getBtn("left/setGo")
	self._btnGroup:AddClickListener(self._onClickGroup, self)

	local tipsTrigger = Framework.UIGlobalTouchTrigger.Get(self.btnGroupView)
	tipsTrigger:AddIgnoreTargetListener(
		function()
			goutil.setActive(self.btnGroupView, false)
		end,
		self
	)
end

function FriendOpComp:onEnter()
	self.userId = self:getOpenParam()[1]
	self.info = self:getOpenParam()[2]
	FriendController.instance:registerLocalNotify(FriendNotify.OnAddBlack, self._onAddBlack, self)
	FriendController.instance:registerLocalNotify(FriendNotify.OnRemovePeopleFromBlack, self._refreshBlack, self)
	FriendController.instance:registerLocalNotify(FriendNotify.OnDeleteFans, self.onDeleteFans, self)
	GlobalDispatcher:addListener(GlobalNotify.OnRefreshUserInfo, self._onRefreshMainInfo, self)
	self:refreshMain()
end

function FriendOpComp:onExit()
	GlobalDispatcher:removeListener(GlobalNotify.OnRefreshUserInfo, self._onRefreshMainInfo, self)
	FriendController.instance:unregisterLocalNotify(FriendNotify.OnAddBlack, self._onAddBlack, self)
	FriendController.instance:unregisterLocalNotify(FriendNotify.OnRemovePeopleFromBlack, self._refreshBlack, self)
	FriendController.instance:unregisterLocalNotify(FriendNotify.OnDeleteFans, self.onDeleteFans, self)
end

function FriendOpComp:_onRefreshMainInfo(userId, userInfo)
	self.userId = userId
	self.info = userInfo
	self:refreshMain()
end

function FriendOpComp:refreshMain()
	self.isMySelf = UserInfo.userId == self.userId
	self.isFan = bitutil.checkBitValue(self.info.relation, 2)
	self.isBlock = self.info.isBlacked
	self.otherGO.gameObject:SetActive(not self.isMySelf and not self.isBlock)
	self._btnGroup.gameObject:SetActive(not self.isMySelf)
	self.btnGroupView.gameObject:SetActive(false)
	goutil.setActive(self._isAddedFriendBtn.gameObject, false)
	self:_refreshFriendBtn()
end

function FriendOpComp:_onClickAddFriend()
	local inBlack = FriendService.instance:queryIsInBlack(self.userId)
	if inBlack then
		DialogHelper.showMsg(lang("对方在您的黑名单中"))
		return
	end
	FriendController.instance:askFriendRequest(
		self.userId,
		function(inApply)
			if inApply then
				if FriendModel.instance:isAttentCountMax() then
					self.info.relation = 5
				else
					self.info.relation = 7
				end
				self:_refreshFriendBtn()
			end
		end
	)
end

function FriendOpComp:_onClickFollow()
	local inBlack = FriendService.instance:queryIsInBlack(self.userId)
	if inBlack then
		DialogHelper.showMsg(lang("对方在您的黑名单中"))
		return
	end

	FriendController.instance:followRequest(
		self.userId,
		true,
		function()
			local result =
				(bitutil.getNewBitValue(self.info.relation, 1) == -1) and (self.info.relation) or
				(bitutil.getNewBitValue(self.info.relation, 1))
			self.info.relation = result
			self:_refreshFriendBtn()
		end
	)
end

function FriendOpComp:_onClickRemoveFriend()
	local str = lang("是否解除和{1}的好友关系？（会同时解除对他的关注）", self.info.roleSimpleInfo.nickname)

	local list = FriendModel.instance:getListByState(FriendModel.State.friend)
	local mo = list:getMoById(self.userId)
	if mo and mo.isRelative == true then
		str = lang("是否删除好友？同时也会解除亲友关系")
	end

	DialogHelper.showConfirmDlg(
		str,
		function(flag)
			if flag then
				FriendController.instance:removeFriend(
					self.userId,
					function()
						local result =
							(bitutil.clearBitValue(self.info.relation, 0) == -1) and (self.info.relation) or
							(bitutil.clearBitValue(self.info.relation, 0))
						self.info.relation = result
						result =
							(bitutil.clearBitValue(self.info.relation, 1) == -1) and (self.info.relation) or
							(bitutil.clearBitValue(self.info.relation, 1))
						self.info.relation = result
						self:_refreshFriendBtn()
					end
				)
			end
		end
	)
end

function FriendOpComp:_onClickUnFollow()
	FriendController.instance:followRequest(
		self.userId,
		false,
		function()
			local result =
				(bitutil.clearBitValue(self.info.relation, 1) == -1) and (self.info.relation) or
				(bitutil.clearBitValue(self.info.relation, 1))
			self.info.relation = result
			self:_refreshFriendBtn()
		end
	)
end

function FriendOpComp:_onClickAddBlack()
	FriendController.instance:addBlackList(self.userId, self.info.roleSimpleInfo.nickname)
end

function FriendOpComp:_onClickRemoveBlack()
	FriendController.instance:removeBlackRequest(self.userId)
end

function FriendOpComp:_onClickReport()
	local extData = {}
	local reportIdList = {1, 2, 3, 4, 5, 6, 7, 8, 39}
	if HouseModel.instance:isInHouse(HouseModel.HouseOrIsland) and HouseModel.instance.userId == self.userId then
		table.insert(reportIdList, 27)
		extData[#reportIdList] = {["houseType"] = HouseModel.instance:getUserProperty(HouseUserProperty.HouseId)}
	elseif HouseModel.instance:isInIsland(HouseModel.HouseOrIsland) and HouseModel.instance.userId == self.userId then
		table.insert(reportIdList, 26)
	elseif HouseModel.instance:isInSeabed() and HouseModel.instance.userId == self.userId then
		table.insert(reportIdList, 53)
	elseif HouseModel.instance:isInSeabedHouse() and HouseModel.instance.userId == self.userId then
		table.insert(reportIdList, 54)
	elseif (HouseModel.instance:isInIsland(HouseModel.Block) or HouseModel.instance:isInHouse(true, HouseModel.Block)) and (HouseModel.instance.userId == self.userId) then
		-- table.insert(reportIdList, 55)
		-- table.insert(reportIdList, 56)
		table.insert(reportIdList, 57)
		-- table.insert(reportIdList, 58)
	else
		table.insert(reportIdList, 26)
		table.insert(reportIdList, 27)
		table.insert(reportIdList, 53)
		table.insert(reportIdList, 54)
	end
	table.insert(reportIdList, 29)
	ReportFacade.instance:showReport(
		self.userId,
		self.info.roleSimpleInfo.nickname,
		reportIdList,
		{
			self.info.roleSimpleInfo.nickname,
			self.info.InformationCard.idealDesc,
			self.info.InformationCard.signature,
			tostring(self.info.InformationCard.age)
		},
		extData
	)
end

function FriendOpComp:_onClickChat()
	self:close()
	ChatFacade.instance:talkTo(self.userId, self.info.roleSimpleInfo.nickname, self.info.roleSimpleInfo.sex)
end

function FriendOpComp:_onClickGotoHouse()
	LogService.instance:logClick(119)
	FarmFacade.instance:visitDefaultIsland(self.userId)
	self:close()
end

function FriendOpComp:_refreshFriendBtn()
	if (self.isMySelf) then
		return
	end
	local relation = self.info.relation --这一个人与我什么关系, (低位到高位分别表示: 是否好友 是否关注 是否粉丝)
	self.isFriend = bitutil.checkBitValue(relation, 0)
	self.isAttention = bitutil.checkBitValue(relation, 1)
	self:_refreshBlack(self.userId)
end

function FriendOpComp:_refreshBlack(userId)
	if self.userId ~= userId then
		return
	end
	local isInBlack = FriendService.instance:queryIsInBlack(self.userId)
	self._followBtn.gameObject:SetActive(not isInBlack and not self.isAttention)
	self._unFollowBtn.gameObject:SetActive(not isInBlack and self.isAttention)
	self._addFriendBtn.gameObject:SetActive(not isInBlack and not self.isFriend)
	self._removeFriendBtn.gameObject:SetActive(not isInBlack and self.isFriend)
	self._addBlackBtn.gameObject:SetActive(not isInBlack)
	self._unBlackBtn.gameObject:SetActive(isInBlack)
	self._removeFensBtn.gameObject:SetActive(self.isFan and not isInBlack)
end

function FriendOpComp:_onAddBlack()
	self.isFan = false
	self:_refreshBlack(self.userId)
end

function FriendOpComp:_onClickGroup()
	self.btnGroupView.gameObject:SetActive(true)
	self:_refreshFriendBtn()
end

function FriendOpComp:_onClickRemoveFans()
	if (self.isMySelf) then
		return
	end

	if (FriendViewModel.instance:isInVisitor()) then
		FlyTextManager.instance:showFlyText(lang("拜访删除粉丝提示"))
		return
	end
	FriendService.instance:showDeleteFans(self.userId, self.info.roleSimpleInfo.nickname)
end

function FriendOpComp:onDeleteFans()
	self.isFan = false
	self:_refreshBlack(self.userId)
end

return FriendOpComp
