module("logic.extensions.activity.flyingchess.controller.FlyingChessGameController", package.seeall)

local FlyingChessGameController = class("FlyingChessGameController", CommonSceneGameControllerBase)

function FlyingChessGameController:ctor()
    FlyingChessGameController.super.ctor(self)
end

function FlyingChessGameController:onInit()
    self.isStartGame = false
end

function FlyingChessGameController:onPreloadFinished()
    self.isEnding = false
    self.exitGameByHand = false
    self.noSortUnitDict = {}
    self._performingActionUnitMap = {}
    
    FlyingChessTriggerController.instance:init()
    
    local players = GameCentreModel.instance:getAllPlayer()
    local maxChessPiece = FlyingChessGameModel.instance.maxChessPiece
	for userId, playerInfo in pairs(players) do
        if userId == UserInfo.userId then
            FlyingChessGameModel.instance.isAutoState = playerInfo.isAuto
        end
        FlyingChessGameModel.instance:setChessPlayer(playerInfo.posIdx, playerInfo.id)
    end

    self:registerLocalNotify(FlyingChessNotify.OnShowMask, self._showMask, self)
    SceneController.instance:registerLocalNotify(SceneNotify.DaytimeChange, self.dayOrNightEffect, self)
    GameCentreController.instance:registerLocalNotify(GameCentreController.Notify_BroadcastInfo, self._onBroadCast, self)
    GameCentreController.instance:registerLocalNotify(GameCentrePlayerInfoVar.FlyingChess_IsAuto, self._onUpdateIsPlayerAuto, self)
    GameCentreController.instance:registerLocalNotify(GameCentrePlayerInfoVar.FlyingChess_DelayEvent, self._onUpdateDelayEvent, self)
    GameCentreController.instance:registerLocalNotify(GameCentrePlayerInfoVar.FlyingChess_ActionInfo, self._onUpdateActionPerform, self)
end

function FlyingChessGameController:onEnterSceneFinished()
    self._scene = SceneManager.instance:getCurScene()
    self._unitMgr = self._scene:getUnitMgr()
    self._scene.camera:setPos(0, 0)
    tfutil.SetZ(self._scene.camera.audioListener, 0)
    self._maskGo = goutil.findChild(self._scene.stage.background, "mask")
    self.gameStateMgr = GameStateMgr.New(FlyingChessGameState.None)
    
    self._bgDayGo = goutil.findChild(self._scene.stage.background, "bg_day")
    self._bgNightGo = goutil.findChild(self._scene.stage.background, "bg_night")

    local selfPlayerInfo = GameCentreModel.instance:getPlayerInfo(UserInfo.userId)
    FlyingChessGameModel.instance:setSelfPos(selfPlayerInfo.posIdx)

    local boardGo = goutil.findChild(self._scene.stage.background, "map/boardGo")
    local chessboardGo = self._scene.stage:getInstance(FlyingChessConfig["ChessBorad"..selfPlayerInfo.posIdx], "chessboard"..selfPlayerInfo.posIdx)
    goutil.addChildToParent(chessboardGo, boardGo)

    self._startTips = {}
    self._turnGoList = {}
    self._apearGoList = {}
    self._chessAimEffectList = {}
    for i=1, 4 do
        local turnGo = goutil.findChild(chessboardGo,"turnGo"..i)
        goutil.setActive(turnGo, false)
        table.insert(self._turnGoList, turnGo)

        local startTipGo = goutil.findChild(chessboardGo,"startTips"..i)
        goutil.setActive(startTipGo, false)
        table.insert(self._startTips, startTipGo)

        local appearGo = goutil.findChild(chessboardGo,"appearGo"..i)
        goutil.setActive(appearGo, false)
        table.insert(self._apearGoList, appearGo)

        local aimEffect = goutil.findChild(self._scene.stage.background, "map/flyingchesstargetposeffect"..i)
        goutil.setActive(aimEffect, false)
        table.insert(self._chessAimEffectList,aimEffect)
    end
    
    
    self:initPosList()
    self:initUnits()
    self:startGame()
    self:dayOrNightEffect()
end

function FlyingChessGameController:onExitScene()
    self.isStartGame = false
    FlyingChessEventBubbleView.hide(true)
    ViewMgr.instance:close("FlyingChessGame")

    self:unregisterLocalNotify(FlyingChessNotify.OnShowMask, self._showMask, self)
    SceneController.instance:unregisterLocalNotify(SceneNotify.DaytimeChange, self.dayOrNightEffect, self)
    GameCentreController.instance:unregisterLocalNotify(GameCentreController.Notify_BroadcastInfo, self._onBroadCast, self)
    GameCentreController.instance:unregisterLocalNotify(GameCentrePlayerInfoVar.FlyingChess_IsAuto, self._onUpdateIsPlayerAuto, self)
    GameCentreController.instance:unregisterLocalNotify(GameCentrePlayerInfoVar.FlyingChess_DelayEvent, self._onUpdateDelayEvent, self)
    GameCentreController.instance:unregisterLocalNotify(GameCentrePlayerInfoVar.FlyingChess_ActionInfo, self._onUpdateActionPerform, self)
end

function FlyingChessGameController:setGameState(state)
    if not self.isStartGame then return end

    local lastState = self.gameStateMgr:getState()
    self:localNotify(FlyingChessNotify.OnGameStatePreExit, lastState)
    self.gameStateMgr:setState(state)
    self:localNotify(FlyingChessNotify.OnGameStateUpdate, lastState)
    print(string.format("设置游戏状态,上一个状态：%d,当前状态：%d,", lastState.id, state.id))
end

function FlyingChessGameController:getGameState()
    if self.gameStateMgr ~= nil then
        return self.gameStateMgr:getState()
    end
    return FlyingChessGameState.None
end

function FlyingChessGameController:getPlayerPos(playerIdx)
    local clientPosIdx =  FlyingChessGameModel.instance:getClientPosIdx(playerIdx)
    return GameUtils.getPos(goutil.findChild(self._scene.stage.background, "map/poslist/playerPos"..clientPosIdx))
end

function FlyingChessGameController:getChessContainer(playerIdx)
    local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(playerIdx)
    return goutil.findChild(self._scene.stage.background, "map/poslist/pieceBornPos"..clientPosIdx)
end

function FlyingChessGameController:getChessPiecePos(playerIdx, chessId)
    
    local gridIdx = FlyingChessGameModel.instance:getChessGridIdx(chessId)
    if gridIdx == FlyingChessPosUtils.homeGridIdx  then
        return FlyingChessGameModel.instance:getChessHomePos(chessId)
    else
        return self:getGridPos(playerIdx, gridIdx, true)
    end
end

function FlyingChessGameController:getGridPos(playerIdx, gridIdx, isNeedTransfer)
    local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(playerIdx)
    if isNeedTransfer then
        gridIdx = FlyingChessPosUtils.getClientGridIdx(playerIdx, gridIdx)
    end
    if gridIdx >= FlyingChessPosUtils.minCommonGridIdx and gridIdx <= FlyingChessPosUtils.maxCommonGridIdx then
        return self._commonGridList[gridIdx]
    elseif table.indexof(FlyingChessPosUtils.specialGridIdxArr, gridIdx) then
        return self._specialGridList[clientPosIdx][gridIdx]
    else
        printError("不存在棋子位置",playerIdx, gridIdx)
    end
end

function FlyingChessGameController:getChessIdsByPlayerIdx(playerIdx)
    local chessIds = {}
    local maxChessPiece = FlyingChessGameModel.instance.maxChessPiece
    for i=1, maxChessPiece do
        local chessId = maxChessPiece * (playerIdx - 1) + i
        table.insert(chessIds, chessId)
    end
    return chessIds
end

function FlyingChessGameController:getGameTime()
    return GameCentreController.instance:getRoomTime()
end

function FlyingChessGameController:initPosList()
    self._commonGridList = {}
    local container = goutil.findChild(self._scene.stage.background, "map/poslist/common")
    local childCount = container.transform.childCount
    for i = 1, childCount do
        local child = container.transform:GetChild(i - 1)
        table.insert(self._commonGridList, GameUtils.getPos(child.gameObject))
    end
    self._specialGridList = {}
    for i = 1, 4 do
        local container = goutil.findChild(self._scene.stage.background, "map/poslist/special"..i)
        local childCount = container.transform.childCount
        self._specialGridList[i] = {}
        for j = 1, childCount do
            local child = container.transform:GetChild(j - 1)
            local _,en = string.find(child.name, "placeholderchesspiece")
            local index = string.sub(child.name, en + 1)
            index = tonumber(index)
            table.insert(self._specialGridList[i], index, GameUtils.getPos(child.gameObject))
        end
    end

    self._actionPosList = {}
    local container = goutil.findChild(self._scene.stage.background, "map/actionposlist")
    local childCount = container.transform.childCount
    for i = 1, childCount do
        local child = container.transform:GetChild(i- 1)
        table.insert(self._actionPosList, GameUtils.getPos(child.gameObject))
    end
end

function FlyingChessGameController:initUnits()
    local players = GameCentreModel.instance:getAllPlayer()
    local maxChessPiece = FlyingChessGameModel.instance.maxChessPiece
    local gameState = FlyingChessGameModel.instance:calGameState()
    --还没正式开始游戏
    local isNotStartGame = gameState == FlyingChessGameState.None or gameState == FlyingChessGameState.Ready

	for userId, playerInfo in pairs(players) do
		local player = self._scene:getUnitMgr():addUnit(SceneUnitType.FlyingChessPlayerUnit, playerInfo)
        if userId == UserInfo.userId then
			self._scene:setUserPlayer(player)
		end
		player.skinView:setScale(0.2)
		player:setSpeed(4)
        
        local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(playerInfo.posIdx) 
        player:setDirection(FlyingChessPosUtils.clientPlayerIdx2Dir[clientPosIdx])
        local pos = self:getPlayerPos(playerInfo.posIdx)
        player:setIdleCompEnable(false)
        player:setSpecialIdleAnim("sit")
        
        local info = {id = playerInfo.posIdx, url = FlyingChessConfig.Vehicle}
        local vehicleUnit = self._scene:getUnitMgr():addUnit(SceneUnitType.FlyingChessVehicleUnit, info)
        local apearanceId = FlyingChessGameModel.instance:getApearanceId(playerInfo.posIdx)
        local url = FlyingChessConfig["Vehicle"..apearanceId]
        vehicleUnit:setPos(pos.x, pos.y, pos.z)
        vehicleUnit:setDirection(FlyingChessPosUtils.clientPlayerIdx2Dir[clientPosIdx])
        vehicleUnit:setView(url, function(spineGo)
            player:setVehicle(vehicleUnit, FlyingChessConfig.vehicle2PlayerAnim[apearanceId])
            player:setOnVehicle()
        end)
        
        local chessIds = {}
        local container = self:getChessContainer(playerInfo.posIdx)

        for i=1, maxChessPiece do
            local chessId = maxChessPiece * (playerInfo.posIdx - 1) + i
            local homePos = GameUtils.getPos(container.transform:GetChild(i-1))
            FlyingChessGameModel.instance:setChessHomePos(chessId, homePos)
            table.insert(chessIds, chessId)

            local pos = self:getChessPiecePos(playerInfo.posIdx, chessId)
            local unit = self._unitMgr:addUnit(SceneUnitType.FlyingChessPiece, {id = chessId, posIdx = playerInfo.posIdx})
            unit:setView(FlyingChessConfig["Piece"..playerInfo.posIdx],"flyingchesspiece"..playerInfo.posIdx)
            unit:playAnimation("idle", true, false, true, true)
            unit:addClickListener(self._onClickPiece, self)
            unit:setVisible(not isNotStartGame and FlyingChessGameModel.instance:isOverlapAndShow(unit))
            unit.idleComp:setEnable(true)

            local gridIdx = FlyingChessGameModel.instance:getChessGridIdx(chessId)
            local overlapChessIds = FlyingChessGameModel.instance:getOverlapChessIds(playerInfo.posIdx, gridIdx)
            local count = 1
            if overlapChessIds ~= nil then
                count = #overlapChessIds
            end
            local isShow = count > 1
            unit:showChessCount(isShow, count)
            if FlyingChessGameModel.instance:isArriveEnd(chessId) then
                unit:setPos(homePos.x, homePos.y, FlyingChessConfig.defaultZ)
                unit:setWinEffectIsShow(false)
                unit:setWinGoIsShow(true)
                unit:setSpineGoIsShow(false)
            else
                unit:setPos(pos.x, pos.y, FlyingChessConfig.defaultZ)
            end
        end
	end
    self:initEventUnits()
    self:initActionUnits()
end

function FlyingChessGameController:getEventSceneId(eventId, gridIdx)
    return eventId * 100 + gridIdx
end

function FlyingChessGameController:addEventUnit(eventId, gridIdx)
    local id = self:getEventSceneId(eventId, gridIdx)
    local unit = self._unitMgr:addUnit(SceneUnitType.FlyingChessEventUnit, {id = id, eventId = eventId})
    unit:setView(FlyingChessConfig.getEventResUrl(eventId), handler(self._onEventLoaded, self))
    unit:addClickListener(self._onClickEvent,self)
    local gridIdx = FlyingChessPosUtils.getEventClientGridIdx(1, gridIdx)
    local pos = self:getGridPos(1, gridIdx)
    unit:setPos(pos.x, pos.y, FlyingChessConfig.defaultZ)
end

function FlyingChessGameController:initEventUnits()
    local dict = FlyingChessGameModel.instance:getAllEventDict()
    for k, v in pairs(dict) do
        self:addEventUnit(v, k)
    end
end

function FlyingChessGameController:_onClickEvent(unit)
    FlyingChessEventBubbleView.show(unit)
end

function FlyingChessGameController:getActionSceneId(actionId, index)
    return actionId * 100 + index
end

function FlyingChessGameController:initActionUnits()
    local dict = FlyingChessGameModel.instance:getAllActionDict()
    for k, v in pairs(dict) do
        local actionId = v
        local id = self:getActionSceneId(actionId, k)
        local unit = self._unitMgr:addUnit(SceneUnitType.FlyingChessActionUnit, {id = id, actionId = actionId, index = k})
        unit:setView(FlyingChessConfig.Action)
        unit:addClickListener(self._onClickAction, self)
        local pos = self._actionPosList[k]
        if pos ~= nil then
            unit:setPos(pos.x, pos.y, FlyingChessConfig.defaultZ)
        end
    end
end

function FlyingChessGameController:_onEventLoaded()
    
end

function FlyingChessGameController:testEvent()
    local eventId,startIdx = 5,35
    self:addEventUnit(eventId,startIdx)
    local selfPlayPosIdx = FlyingChessGameModel.instance:getSelfPosIdx()
    local params = {eventId = eventId, startIdx = startIdx, playerPosIdx = selfPlayPosIdx}
    local vehicleUnit = self._unitMgr:getUnit(SceneUnitType.FlyingChessVehicleUnit, selfPlayPosIdx)
    vehicleUnit:appendEventPerform(params)
end

function FlyingChessGameController:_onClickAction(unit)
    if FlyingChessGameModel.instance:isSelfRound() and self:getGameState() == FlyingChessGameState.SelectChessPiece then return end
    if FlyingChessGameModel.instance:isSelfRound() or self._performingActionUnitMap[UserInfo.userId] ~= nil then
        FlyTextManager.instance:showFlyText(lang("当前状态不能与流星交互哦~"))
        return
    end

    local index = unit.info.index
    Activity462Agent.instance:sendClick462ClientEventRequest(index,function(changeSetId)
        FlyItem.instance:addFlyItemsByWorldPos(ItemService.instance:popChangeSet(changeSetId))
        self:doPerformAction(UserInfo.userId, unit)
    end)
end

function FlyingChessGameController:doPerformAction(userId, unit)
    FlyingChessEventBubbleView.hide()

    self._performingActionUnitMap[userId] = unit
    local eventId = unit.info.actionId
    local define = FlyingChessConfig.getActionDefine(eventId)

    local playerPos = unit:getPlayerPos()
    local playerUnit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPlayerUnit, userId)
    local vehicleUnit = playerUnit:getVehicleUnit()
    self._unitMgr:setUnitVisible(vehicleUnit, false)
    
    playerUnit:setOutVehicle()
    playerUnit:setDirection(UnitDirection.RightDown)
    playerUnit:setPos(playerPos.x, playerPos.y, playerPos.z)
	PjAobi.CSGameUtils.AddSpineBoneFollower(playerUnit.go, unit:getSpineGo(), "zuowei1", false, false, false, false)
	local follower = playerUnit.go:GetComponent("BoneFollower")
	follower.enabled = true

    playerUnit:playAnimation(define.starAnim, false, false, true, true)
    unit:playAnimation(define.bearAnim, false, false, true, true)
    local playTime = define.playTime/1000
    if eventId == 2 then
        unit:showSpecialActionGo()
        PjAobi.CSGameUtils.AddSpineBoneFollower(unit._specialActionGo, unit:getSpineGo(), "zuowei1", false, false, false, false)
        local specialFollower = unit._specialActionGo:GetComponent("BoneFollower")
        specialFollower.enabled = true
    end

    SceneTimer:setTimer(playTime, function()
        if eventId == 2 then
            unit:hideSpecialActionGo()
            local specialFollower = unit._specialActionGo:GetComponent("BoneFollower")
            specialFollower.enabled = false
        end
        local posIdx = GameCentreModel.instance:getPlayerInfo(userId).posIdx
        -- local vehicleUnit = self._unitMgr:getUnit(SceneUnitType.FlyingChessVehicleUnit, posIdx)
        -- self._unitMgr:setUnitVisible(vehicleUnit, true)
        follower.enabled = false
        self._unitMgr:removeUnit(SceneUnitType.FlyingChessActionUnit, unit.id)
        playerUnit:setOnVehicle()
        local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(posIdx) 
        playerUnit:setDirection(FlyingChessPosUtils.clientPlayerIdx2Dir[clientPosIdx])
        self._performingActionUnitMap[userId] = nil
    end, nil, false)
end

function FlyingChessGameController:_onUpdateIsPlayerAuto(userId, isAuto)
    local players = GameCentreModel.instance:getAllPlayer()
    for k, v in pairs(players) do
        if k == userId then
            FlyingChessGameModel.instance:setIsAutoState(v.posIdx, isAuto)
            break
        end
    end

    if userId == UserInfo.userId then
        FlyingChessGameModel.instance.isAutoState = isAuto
        self:localNotify(FlyingChessNotify.OnSelfIsAutoUpdate)
    end
end

function FlyingChessGameController:_onUpdateDelayEvent(userId, delayEvent)
    local players = GameCentreModel.instance:getAllPlayer()
    for k, v in pairs(players) do
        if k == userId then
            FlyingChessGameModel.instance:setDelayEvent(v.posIdx, delayEvent)
            break
        end
    end
end

function FlyingChessGameController:_onUpdateActionPerform(userId, msg)
    --自己的表演在点击的时候就触发了
    if userId == UserInfo.userId then return end

    local id = self:getActionSceneId(msg.eventId, msg.gridId)
    local unit = self._unitMgr:getUnit(SceneUnitType.FlyingChessActionUnit, id)
    self:doPerformAction(userId, unit)
end

function FlyingChessGameController:refreshMapEvent()
    if self._unitMgr == nil then return end
    local units = self._unitMgr:getUnits(SceneUnitType.FlyingChessEventUnit)
    for i=#units, 1, -1 do
        local unit = units[i]
        self._unitMgr:removeUnit(SceneUnitType.FlyingChessEventUnit, unit.id)
    end
    FlyingChessEventBubbleView.hide()
    self:initEventUnits()
end

function FlyingChessGameController:refreshMapAction()
    if self._unitMgr == nil then return end
    local units = self._unitMgr:getUnits(SceneUnitType.FlyingChessActionUnit)
    for i=#units, 1, -1 do
        local unit = units[i]
        local isPerforming = false
        for k, v in pairs(self._performingActionUnitMap) do
            if v == unit then
                isPerforming = true
                break
            end
        end
        if not isPerforming then
            self._unitMgr:removeUnit(SceneUnitType.FlyingChessActionUnit, unit.id)
        end
    end
    self:initActionUnits()
end

function FlyingChessGameController:_onClickPiece(unit)
    if not FlyingChessGameModel.instance:isSelfRound() then return end
    if self:getGameState() ~= FlyingChessGameState.SelectChessPiece then return end
    local chessId = unit.info.id
    if FlyingChessGameModel.instance:isArriveEnd(chessId) then return end
    if FlyingChessGameModel.instance:isInHome(chessId)  then
        if not table.indexof(FlyingChessPosUtils.startDicePoints, FlyingChessGameModel.instance.dicePoints) then
            return
        end
    end

    local ownerChessIds = FlyingChessGameController.instance:getChessIdsByPlayerIdx(FlyingChessGameModel.instance:getSelfPosIdx())
    if not table.indexof(ownerChessIds, chessId) then
        return
    end

    local gridIdx = FlyingChessGameModel.instance:getChessGridIdx(chessId)
    -- printError("_onClickPiece11", chessId, gridIdx)
    local overlapChessIds = FlyingChessGameModel.instance:getOverlapChessIds(FlyingChessGameModel.instance:getSelfPosIdx(), gridIdx)
    if overlapChessIds ~= nil and #overlapChessIds > 1 and not FlyingChessGameModel.instance:isInHome(chessId) then
        -- printError("_onClickPiece22", table.concat( overlapChessIds, ", ", 1, #overlapChessIds ))
        unit.nameBar:showBtnMenu(true, function(flag)
            unit.nameBar:showBtnMenu(false)
            if flag == 1 then
                self:selectMoveChessRequest(overlapChessIds)
            else
                self:selectMoveChessRequest({chessId})
            end
        end)
    else
        self:selectMoveChessRequest({chessId})
    end
end

function FlyingChessGameController:sortUnits()
    local sortUnitTypes = {SceneUnitType.FlyingChessEventUnit, SceneUnitType.FlyingChessPiece, SceneUnitType.FlyingChessActionUnit}
    local sortUnits = {}
    for k, v in ipairs(sortUnitTypes) do
        local units = self._unitMgr:getUnits(v)

        for kk, vv in ipairs(units) do
            if self.noSortUnitDict ~= nil and self.noSortUnitDict[vv] then
            else
                table.insert(sortUnits, vv)
            end
        end
    end
    
	table.sort(sortUnits, function(a, b)
		local _, y1 = Framework.TransformUtil.GetPos(a.go.transform, 0, 0, 0)
		local _, y2 = Framework.TransformUtil.GetPos(b.go.transform, 0, 0, 0)
		if y1 ~= y2 then
			return y1 > y2
		end
	end)
	
    local curTempY = FlyingChessConfig.defaultZ
	for k, v in ipairs(sortUnits) do
		curTempY = curTempY - 0.05
		tfutil.SetZ(v.go, curTempY) 
	end
end

function FlyingChessGameController:startSortUnits()
	SceneTimer:setTimer(0.15, self.sortUnits, self, true)
end

function FlyingChessGameController:stopSortUnits()
	SceneTimer:removeTimer(self.sortUnits, self)
end

function FlyingChessGameController:setTurnEffect(playerIdx)
    local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(playerIdx)
    for k,v in ipairs(self._turnGoList) do
        local isShow = k == clientPosIdx
        goutil.setActive(v, false)
        if isShow then
            goutil.setActive(v, true)
        end
        
    end
end

function FlyingChessGameController:showCurStartTip()
    local realPosIdx = FlyingChessGameModel.instance:getCurRoundPosIdx()
    local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(realPosIdx)
    goutil.setActive(self._startTips[clientPosIdx], true)
    SceneTimer:setTimer(6, function()
        goutil.setActive(self._startTips[clientPosIdx], false)
    end, self, false)
end

function FlyingChessGameController:showAppearGos()
    local players = GameCentreModel.instance:getAllPlayer()

    local units = self._unitMgr:getUnits(SceneUnitType.FlyingChessPiece)
    for k,v in ipairs(units) do
        v:setVisible(FlyingChessGameModel.instance:isOverlapAndShow(v))
        v:showAppearEffect()
        v:setDelayShowSpineGo()
    end

	for userId, playerInfo in pairs(players) do
        local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(playerInfo.posIdx)
        goutil.setActive(self._apearGoList[clientPosIdx], true)
    end
    SceneTimer:setTimer(3, function()
        for k, v in ipairs(self._apearGoList) do
            goutil.setActive(v, false)
        end
    end, self, false)
end

function FlyingChessGameController:setLastSceneId(sceneId)
	self.lastSceneId = sceneId
end

function FlyingChessGameController:startGame()
    self.isStartGame = true
    ViewMgr.instance:open("FlyingChessGame")
    local gameState = FlyingChessGameModel.instance:calGameState()
    self:setGameState(gameState)
    FlyingChessGameController.instance:startSortUnits()
    --短线重连的情况重新设置IdlePoseComp的开关
    if gameState ~= FlyingChessGameState.Ready then
        self:checkChessPieceIdle()
    end
end

function FlyingChessGameController:tryExitCurGame()
	-- to be overrided
	self:tryExitGame()
end

function FlyingChessGameController:genPreloadResList()
	-- to be overrided
end

function FlyingChessGameController:updateRoomState(state, oldState)

end

function FlyingChessGameController:updatePlayerInfo()
	print("PumpTurtle==>:玩家信息更新")
end

function FlyingChessGameController:onClientPush(userid, valuee)
	print("PumpTurtle==>:玩家信息推送",self.playerInfo[userid].roleSimpleInfo.nickname, valuee)
	--LandlordController.instance:localNotify(LandlordController.PushClient, userid, valuee)
end

function FlyingChessGameController:_showMask(isShow)
    goutil.setActive(self._maskGo, isShow)
end

function FlyingChessGameController:checkChessPieceIdle()
    local posIdx = FlyingChessGameModel.instance:getCurRoundPosIdx()
    local players = GameCentreModel.instance:getAllPlayer()
    for userId, playerInfo in pairs(players) do
        local isOnTurn = posIdx == playerInfo.posIdx
        local chessIds = FlyingChessGameController.instance:getChessIdsByPlayerIdx(playerInfo.posIdx)
        for k,v in ipairs(chessIds) do
            local unit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPiece, v)
            unit.idleComp:setEnable(not isOnTurn)
        end
    end
end

function FlyingChessGameController:startDiceRequest(handler)
    Activity462Agent.instance:sendFlyingChessStartDiceRequest(FlyingChessGameModel.instance.diceTimes,function()

    end)
end

function FlyingChessGameController:selectMoveChessRequest(moveChessIds, handler)
    Activity462Agent.instance:sendFlyingChessSelectMoveChessRequest(moveChessIds,function()
        if handler then
            handler()
        end
    end)
end

function FlyingChessGameController:setChessAimEffect(playerPosIdx, bShow, gridIdx)
    goutil.setActive(self._chessAimEffectList[playerPosIdx], bShow)
    if bShow then
        local pos = self:getGridPos(playerPosIdx, gridIdx, true)
        tfutil.SetXY(self._chessAimEffectList[playerPosIdx], pos.x,pos.y)
    end
end

--主动告诉后端表现结束，所有玩家都会发，后端收到第一个玩家的协议，表现阶段就结束了
function FlyingChessGameController:endPerforming()
    --表现结束才进去结算
    if self.isEnding then  
        self:doEndGame()
        return
    end
    if self:getGameState() == FlyingChessGameState.Performing then
        Activity462Agent.instance:sendFlyingChessClientMoveEndRequest(FlyingChessGameModel.instance.diceTimes,function()
        end)
    end
end

function FlyingChessGameController:changeAutoStateRequest(handler)
    Activity462Agent.instance:sendFlyingChessChangeAutoStateRequest(function()
        FlyingChessGameModel.instance.isAutoState = not FlyingChessGameModel.instance.isAutoState
        if handler then
            handler()
        end
    end)
end

function FlyingChessGameController:endGame(datas)
    self.isEnding = true
    local playerDataList = {}
    for k,v in ipairs(datas) do
        local playerInfo = GameCentreModel.instance:getPlayerInfo(v.userNetId)
        local data = {}
        data.userId = v.userNetId
        data.score = v.score
        data.rank = v.rank
        data.endChessNum = v.endChessNum
        data.isNoOperateAuto = v.isNoOperateAuto
        data.playerPosIdx = playerInfo.posIdx
        data.nickname = playerInfo.roleSimpleInfo.nickname
        table.insert(playerDataList, data)
    end
    table.sort(playerDataList, function(a, b)
        return a.rank < b.rank
    end)
    if #playerDataList == 0 then
        self:setGameState(FlyingChessGameState.None)
        self:tryExitGame(false)
        return
    end

    FlyingChessGameModel.instance.playerDataList = playerDataList
    FlyingChessGameModel.instance.winPlayerPosIdx = playerDataList[1].playerPosIdx

    if FlyingChessGameController.instance.exitGameByHand then
        self:setGameState(FlyingChessGameState.None)
        self:tryExitGame(false)
    else
        --防卡死处理，弹出结算处理在FlyingChessGameController.instance:endPerforming()
        SceneTimer:setTimer(10, self.doEndGame, self, false)
    end

end

function FlyingChessGameController:doEndGame()
    FlyingChessGameController.instance:stopSortUnits()
    SceneTimer:removeTimer(self.doEndGame, self)
    self:setGameState(FlyingChessGameState.EndGame)
end

function FlyingChessGameController:tryExitGame(isAgain)
    FlyingChessGameModel.instance:init()
    FlyingChessGameModel.instance.isMatchAgain = isAgain
	GameRoomController.instance:tryQuitGame(GameEnum.GameRoomType.FLYING_CHESS, false, false, self.lastSceneId, handler(self.onQuitFinish, self))
end

function FlyingChessGameController:onQuitFinish()
    GamePromoteFacade.openByIndex(1)
end

function FlyingChessGameController:backRoom()
    FlyingChessGameModel.instance:init()
    GameRoomAgent.instance:sendExitGameRoomSceneRequest()
    GameRoomController.instance:backToWaitingRoom(GameEnum.GameRoomType.FLYING_CHESS)
end

function FlyingChessGameController:showPose(id)
    local nowTime = ServerTime.now()
    if self._clickShowPoseTime ~= nil and nowTime - self._clickShowPoseTime < 2 then
        return
    end
    self._clickShowPoseTime = nowTime
    if FlyingChessGameModel.instance:isSelfRound() and self:getGameState() == FlyingChessGameState.Performing then
        return
    end
    GameRoomAgent.instance:sendGameRoomBroadcastStateRequest(GameRoomExtension_pb.MAP_SHARE, nil, {UserInfo.userId, tostring(id)})
end

function FlyingChessGameController:_onBroadCast(msg)
    if msg.key == GameRoomExtension_pb.MAP_SHARE then
        local userId = msg.params[1]
        local id = tonumber(msg.params[2])
        self:doShowPose(userId, id)
    end
end

function FlyingChessGameController:dayOrNightEffect()
    local dt = os.date("*t", ServerTime.now())
    local isDayTime = dt.hour >= 6 and dt.hour < 19
    goutil.setActive(self._bgDayGo, isDayTime)
    goutil.setActive(self._bgNightGo, not isDayTime)
end

function FlyingChessGameController:doShowPose(userId, id)
    local playerUnit = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SceneUnitType.FlyingChessPlayerUnit, userId)
    playerUnit:setOutVehicle()
    local playerInfo = GameCentreModel.instance:getPlayerInfo(userId)
    local pos = self:getPlayerPos(playerInfo.posIdx)
    playerUnit:setPos(pos.x, pos.y)
    playerUnit.poseComp:showPose(id, function()
        playerUnit:setOnVehicle()
    end, false)
end

FlyingChessGameController.instance = FlyingChessGameController.New()
return FlyingChessGameController