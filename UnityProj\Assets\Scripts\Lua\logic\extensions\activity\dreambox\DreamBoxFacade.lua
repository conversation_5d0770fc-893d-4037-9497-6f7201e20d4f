module("logic.extensions.activity.dreambox.DreamBoxFacade", package.seeall)
local DreamBoxFacade = class("DreamBoxFacade")

function DreamBoxFacade.gotoDreamBox(activityId)
	local activityInfo = ActivityModel.instance:getActInfoByActId(activityId)
	if activityInfo == nil then
		FlyTextManager.instance:showFlyText(lang("不在活动时间内~"))
		return false
	end
	if not activityInfo:getIsShow() then
		FlyTextManager.instance:showFlyText(activityInfo:getOpenTimeText())
		return false
	end

	if DreamBoxFacade.activityList[activityId] then
		DreamBoxFacade.activityList[activityId]()
		return true
	end
	-- FlyTextManager.instance:showFlyText("周年庆配置有问题：actId" .. tostring(activityId))
	return false
end

function DreamBoxFacade.openMap()
	ViewMgr.instance:open("DreamBoxMapPanel")
end

function DreamBoxFacade:openMap_1()
	ViewMgr.instance:open("DreamBoxMapPanel_1")
	-- ViewMgr.instance:open("DreamBoxSubMapPanel",1)
end

function DreamBoxFacade.openSignin()
	ViewMgr.instance:open("PrayDaySigninView")
end

function DreamBoxFacade.openShop()
	-- ViewMgr.instance:open("CommonSmallShop")
	-- ViewMgr.instance:open("LanternShop")
	ViewMgr.instance:open("DreamBoxSubMapPanel",3)
end

function DreamBoxFacade.openShop_1()
	--ViewMgr.instance:open("DreamBoxSubMapPanel", 3)
	RedPointController.instance:setLastClick("Shop2279Tab")
	ViewMgr.instance:open("LanternShop")
end

function DreamBoxFacade:openTangram()
	TangramController.instance:enterGame()
end

function DreamBoxFacade.openStarWishCard()
	StarWishController.instance:_openView()
end

function DreamBoxFacade.openTable()
	ViewMgr.instance:open("MakeDreamTable")
end

function DreamBoxFacade.openFatigue()
	ViewMgr.instance:open("DreamBoxFatigue")
end

function DreamBoxFacade.openDreamBoxStory()
	-- ViewMgr.instance:open("DreamBoxStoryView")
	ViewMgr.instance:open("DreamBoxSubMapPanel",2)
end

function DreamBoxFacade.openOrder()
	ViewMgr.instance:open("StrangeFoodOrder")
end

function DreamBoxFacade.openGame()
	-- ViewMgr.instance:open("HappyBreadView")
	-- ViewMgr.instance:open("GardenPartyGiftView")
	ViewMgr.instance:open("RunningGameMainView")
end

function DreamBoxFacade.openWorkShop()
	ViewMgr.instance:open("NewWorkShoppingView")
end

function DreamBoxFacade.openCollectStamp()
	-- PleasanceCollectStampController.instance:_openView()
	ViewMgr.instance:open("CCTVCardMainView_1")
end

function DreamBoxFacade.openCollectStamp_1()
	PleasanceCollectStampController.instance:_openView()
	-- ViewMgr.instance:open("FindMaterialsView")
end

--周年华
function DreamBoxFacade.openGala()
	ViewMgr.instance:open("DreamBoxSubMapPanel",1)
end

function DreamBoxFacade.openRoadBaker()
	ViewMgr.instance:open("RoadSideBakerActivityView")
end

function DreamBoxFacade.openPoemFillingView()
	ViewMgr.instance:open("PoemFillingView")
end

--满月灯谜
function DreamBoxFacade.openMoonRiddle()
	ViewMgr.instance:open("LanternMoonRiddleView")
end

--直购
function DreamBoxFacade.openSpeakerGift()
	ViewMgr.instance:open("DreamBoxSpeakerView")
end

--满月纪念
function DreamBoxFacade.openTask()
	ViewMgr.instance:open("FullMoonCommemorationView")
end

-- 2.5 跨年签到
function DreamBoxFacade.openTaskSign()
	ViewMgr.instance:open("DreamBoxSubMapPanel",1)
end

function DreamBoxFacade.openDreamBoxStory_1()
	ViewMgr.instance:open("DreamBoxStoryView_1")
end


function DreamBoxFacade.openWorkShopping()
	ViewMgr.instance:open("WorkShoppingView")
end

--一笔画
function DreamBoxFacade.openDrawOneLine()
	ViewMgr.instance:open("DWOLView")	
end

function DreamBoxFacade.openSelectOrder()
	ViewMgr.instance:open("ActivitySelectOrderPanel")
end

-- npc提交任务
function DreamBoxFacade.openNpcSubmitView()
	ViewMgr.instance:open("MagpieFestivalInterviewView")	
end

-- 彩熊制造机
function DreamBoxFacade.openBeautyMakerView()
	ViewMgr.instance:open("BeautyMakerLevelView")	
end

--集章
function DreamBoxFacade.openPleasanceCollectStamp()
	PleasanceCollectStampController.instance:_openView()
end

function DreamBoxFacade.openCollectGift()
	ViewMgr.instance:open("GardenPartyGiftView")
end

function DreamBoxFacade.openNewYearTimeLine()
	ViewMgr.instance:open("DreamBoxSubMapPanel",1)
end

function DreamBoxFacade.openNewYearCloth()
	ViewMgr.instance:open("DreamBoxSubMapPanel",1)
	-- ViewMgr.instance:open("CCTVCardMainView")
end

function DreamBoxFacade.openLanternRiddle()
	--ViewMgr.instance:open("DreamBoxSubMapPanel",4)
	ViewMgr.instance:open("LanternMoonRiddleView")
	-- ViewMgr.instance:open("CCTVCardMainView")
end

function DreamBoxFacade.openConnectLineGame()
	ConnectLineGameController.instance:enterGame()
end

function DreamBoxFacade.openHappyBreadView()
	ViewMgr.instance:open("HappyBreadView")
end

function DreamBoxFacade.openRechargeView()
	RechargeFacade.openRechargeView(11)
end

--神庙善行
function DreamBoxFacade.openTempleView()
	-- TempleBeneficenceController.instance:_openView()
	ViewMgr.instance:open("TempleBeneficenceView")
end

function DreamBoxFacade.openCollectView()
	ViewMgr.instance:open("QixiCollectView")
end

function DreamBoxFacade.openMinesWeeperView()
	MinesWeeperFacade.tryOpenView()
end

function DreamBoxFacade.openTwoZeroFourEightView()
	ViewMgr.instance:open("TwoZeroFourEightActivityView")
end

function DreamBoxFacade.openFindTheItemView()
	ViewMgr.instance:open("FindTheItemLevelView")
end

function DreamBoxFacade.openDonateAct()
	DonateActController.instance:openEntry()
end

-- 3.0周年庆 ----------------------------------------------
-- 3.0剧情
function DreamBoxFacade.openCarnivalTaskStoryView()
	ViewMgr.instance:open("DreamBoxSubMapPanel",5)
end
-- 3.0活动服装筹备
function DreamBoxFacade.openCarnivalClothesView()
	ViewMgr.instance:open("DreamBoxSubMapPanel",4)
end
-- 3.0周年旋律活动
function DreamBoxFacade.openCarnivalMelodyView()
	ViewMgr.instance:open("DreamBoxSubMapPanel",3)
end
-- 3.0周年蛋糕活动
function DreamBoxFacade.openCarnivalCakeView()
	ViewMgr.instance:open("DreamBoxSubMapPanel",2)
end

function DreamBoxFacade.openSeaDacneView()
	ViewMgr.instance:open("DreamBoxSubMapPanel",1)
end

function DreamBoxFacade.openTotalRecharge()
	ViewMgr.instance:open("ActivityTotalRecharge430Panel")
end
----------------------------------------------------------

-------不要把下面代码移到上面------------
DreamBoxFacade.templeActivityId = 1848
DreamBoxFacade.mapActivityId = 2204
DreamBoxFacade.signinActivityId = 2393 -- 中主题活动签到专用，别动
DreamBoxFacade.shopActivityId = 2352
DreamBoxFacade.storyActivityId = 2205

DreamBoxFacade.connectLineGame = 1758
DreamBoxFacade.speakerGiftActId = 1793
DreamBoxFacade.starWishActivityId = 1998
DreamBoxFacade.collectStampActivityId = 1573
-- DreamBoxFacade.gameActivityId = 1466
DreamBoxFacade.drawOneLineActivityId = 1896
DreamBoxFacade.npcSubmitTaskId = 1607
DreamBoxFacade.tableActivityId = 1581
DreamBoxFacade.beautyMakerId = 2004 -- 彩熊制造机
DreamBoxFacade.pleasanceCollectStampId = 1610
DreamBoxFacade.tangramId = 1941
DreamBoxFacade.lanternRiddle = 2284

-- 并存的同类活动
DreamBoxFacade.mapActivityId_1 = 2208
DreamBoxFacade.storyActivityId_1 = 2394 -- 中主题活动剧情专用，别动
DreamBoxFacade.shopActivityId_1 = 2279 -- 中主题活动商店专用，别动
DreamBoxFacade.taskSignInActId = 2206

DreamBoxFacade.workshopping = 1800

DreamBoxFacade.openCollectGiftid = 1656
DreamBoxFacade.roadBaker = 2002
DreamBoxFacade.newYearTimeLine = 2190

DreamBoxFacade.runbeanid = 1701
DreamBoxFacade.orderActId = 1702

DreamBoxFacade.newYearClothID = 1766
DreamBoxFacade.fullMoonCommemoration = 1802
DreamBoxFacade.happyBread = 1796
DreamBoxFacade.collectAct = 1845
DreamBoxFacade.MinesWeeper = 1846

DreamBoxFacade.selectOrder = 1884
DreamBoxFacade.twoZeroFourEight = 1895

DreamBoxFacade.findTheItem = 1924 -- 找你妹小游戏

DreamBoxFacade.anniversarySign = 2100
DreamBoxFacade.anniversaryStory = 2102
DreamBoxFacade.anniversaryCake = 2101



------------------------------ 周年庆专用 ------------------------------
DreamBoxFacade.carnivalMapActivityId = 1955 -- 周年庆活动总览id
DreamBoxFacade.carnivalStoryActivityId = 1954 -- 3.0周年庆剧情
DreamBoxFacade.carnivalClothesActivityId = 1952 -- 3.0周年庆服装筹备活动
DreamBoxFacade.carnivalMelodyActivityId = 1951 -- 3.0周年庆旋律活动
DreamBoxFacade.carnivalCakeActivityId = 1953 -- 3.0周年蛋糕活动
DreamBoxFacade.carnivalSeadanceActivityId = 1956 -- 3.0周年海洋舞会
-----------------------------------------------------------------------

DreamBoxFacade.totalRecharge = 2065 -- 累充

DreamBoxFacade.activityList = {
	[DreamBoxFacade.mapActivityId] = DreamBoxFacade.openMap,
	[DreamBoxFacade.mapActivityId_1] = DreamBoxFacade.openMap_1,
	[DreamBoxFacade.shopActivityId] = DreamBoxFacade.openShop,
	[DreamBoxFacade.shopActivityId_1] = DreamBoxFacade.openShop_1,
	[DreamBoxFacade.storyActivityId] = DreamBoxFacade.openDreamBoxStory,
	[DreamBoxFacade.storyActivityId_1] = DreamBoxFacade.openDreamBoxStory_1,
	[DreamBoxFacade.signinActivityId] = DreamBoxFacade.openSignin,
	[DreamBoxFacade.starWishActivityId] = DreamBoxFacade.openStarWishCard,
	[DreamBoxFacade.collectStampActivityId] = DreamBoxFacade.openCollectStamp,
	[DreamBoxFacade.npcSubmitTaskId] = DreamBoxFacade.openNpcSubmitView,
	[DreamBoxFacade.tableActivityId] = DreamBoxFacade.openTable,
	[DreamBoxFacade.beautyMakerId] = DreamBoxFacade.openBeautyMakerView,
	[DreamBoxFacade.pleasanceCollectStampId] = DreamBoxFacade.openPleasanceCollectStamp,
	[DreamBoxFacade.openCollectGiftid] = DreamBoxFacade.openCollectGift,
	[DreamBoxFacade.roadBaker] = DreamBoxFacade.openRoadBaker,
	[DreamBoxFacade.tangramId] = DreamBoxFacade.openTangram,
	[DreamBoxFacade.taskSignInActId] = DreamBoxFacade.openTaskSign,
	[DreamBoxFacade.runbeanid] = DreamBoxFacade.openGame,
	[DreamBoxFacade.orderActId] = DreamBoxFacade.openOrder,
	[DreamBoxFacade.workshopping] = DreamBoxFacade.openWorkShop,
	[DreamBoxFacade.newYearClothID] = DreamBoxFacade.openNewYearCloth,
	[DreamBoxFacade.lanternRiddle] = DreamBoxFacade.openLanternRiddle,
	[DreamBoxFacade.connectLineGame] = DreamBoxFacade.openConnectLineGame,
	[DreamBoxFacade.fullMoonCommemoration] = DreamBoxFacade.openTask,
	[DreamBoxFacade.happyBread] = DreamBoxFacade.openHappyBreadView,
	[DreamBoxFacade.speakerGiftActId] = DreamBoxFacade.openSpeakerGift,
	[DreamBoxFacade.templeActivityId] = DreamBoxFacade.openTempleView,
	[DreamBoxFacade.collectAct] = DreamBoxFacade.openCollectView,
	[DreamBoxFacade.MinesWeeper] = DreamBoxFacade.openMinesWeeperView,
	[DreamBoxFacade.twoZeroFourEight] = DreamBoxFacade.openTwoZeroFourEightView,
	[DreamBoxFacade.selectOrder] = DreamBoxFacade.openSelectOrder,
	[DreamBoxFacade.drawOneLineActivityId] = DreamBoxFacade.openDrawOneLine,
	[DreamBoxFacade.findTheItem] = DreamBoxFacade.openFindTheItemView,
	[DreamBoxFacade.carnivalStoryActivityId] = DreamBoxFacade.openCarnivalTaskStoryView,
	[DreamBoxFacade.carnivalClothesActivityId] = DreamBoxFacade.openCarnivalClothesView,
	[DreamBoxFacade.carnivalMelodyActivityId] = DreamBoxFacade.openCarnivalMelodyView,
	[DreamBoxFacade.carnivalCakeActivityId] = DreamBoxFacade.openCarnivalCakeView,
	[DreamBoxFacade.carnivalSeadanceActivityId] = DreamBoxFacade.openSeaDacneView,
	[DreamBoxFacade.totalRecharge] = DreamBoxFacade.openTotalRecharge,
}

--------------------------------------------------
return DreamBoxFacade
