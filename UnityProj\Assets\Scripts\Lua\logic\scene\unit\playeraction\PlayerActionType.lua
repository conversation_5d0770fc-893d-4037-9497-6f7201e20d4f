module("logic.scene.unit.playeraction.PlayerActionType", package.seeall)
local PlayerActionType = class("PlayerActionType")

local idMap = {}

function PlayerActionType:ctor(typeId, isShort, handler)
	self.typeId = typeId
	self.isShort = isShort
	self.handler = handler
	idMap[typeId] = self
end

function PlayerActionType.getHandler(typeId, unit, isInit)
	return idMap[typeId].handler.New(PlayerActionType.getTypeById(typeId), unit, isInit)
end

function PlayerActionType.getTypeById(id)
	return idMap[id]
end

function PlayerActionType.isShortAction(typeId)
	return idMap[typeId].isShort
end
--这里配了还要去场景动作配置表配置
PlayerActionType.Idle = PlayerActionType.New(0, false, PlayerIdleHandler)
PlayerActionType.Share = PlayerActionType.New(1, false, PlayerShareHandler)
PlayerActionType.Pose = PlayerActionType.New(2, true, PlayerPoseHandler)
PlayerActionType.UseObject = PlayerActionType.New(3, false, PlayerUseObjectHandler)
PlayerActionType.Eat = PlayerActionType.New(4, true, PlayerEatHandler)
PlayerActionType.LongPose = PlayerActionType.New(6, false, PlayerLongPoseHandler)
-- PlayerActionType.Sleep = PlayerActionType.New(7, false, PlayerSleepHandler)
PlayerActionType.GameHost = PlayerActionType.New(8, false, PlayerStartGameHandler)
PlayerActionType.GameGuest = PlayerActionType.New(9, false, PlayerJoinGameHandler)
PlayerActionType.Fishing = PlayerActionType.New(10, false, PlayerFishingHandler)
PlayerActionType.TreasureHunt = PlayerActionType.New(11, false, TreasureHuntHandler)
PlayerActionType.CatchPet = PlayerActionType.New(12, false, PlayerCatchPetHandler)
PlayerActionType.IslanderEventFind = PlayerActionType.New(13, false, IslanderEventFindHandler)
PlayerActionType.SpecialMove = PlayerActionType.New(14, true, PlayerSpecialMoveHandler)
PlayerActionType.PlayingMusic = PlayerActionType.New(15, false, PlayingMusicHandler)
PlayerActionType.Pluck = PlayerActionType.New(16, true, PluckHandler)
PlayerActionType.MultiInteraction = PlayerActionType.New(17, false, PlayerMultiInteractionHandler)
PlayerActionType.StartDoublePose = PlayerActionType.New(19, false, PlayerStartDoublePoseHandler)
PlayerActionType.AcceptPlayDoublePose = PlayerActionType.New(20, true, PlayerAcceptDoublePoseHandler)
PlayerActionType.TakeWell = PlayerActionType.New(21, false, TakeWellHandler)
PlayerActionType.Spa = PlayerActionType.New(22, false, PlayerSpaHandler)
PlayerActionType.HitIce = PlayerActionType.New(23, false, PlayerHitIceHandler)
PlayerActionType.Throw = PlayerActionType.New(24, true, PlayerThrowHandler)
-- PlayerActionType.Shower = PlayerActionType.New(25,false,PlayerShowerHandler)
PlayerActionType.HandThrowItem = PlayerActionType.New(26, false, PlayerHandThrowItemHandler)
PlayerActionType.CableCar = PlayerActionType.New(27, false, PlayerCableCarHandler)
PlayerActionType.SceneTrap = PlayerActionType.New(28, false, SceneTrapHandler)
PlayerActionType.StartQueue = PlayerActionType.New(29, false, PlayerStartQueueHandler)
PlayerActionType.JoinQueue = PlayerActionType.New(30, false, PlayerJoinQueueHandler)
PlayerActionType.Dandelion = PlayerActionType.New(31, false, PlayerDandelionHandler)
PlayerActionType.AffinityCheck = PlayerActionType.New(32, false, PlayerAffinityCheckHandler)
PlayerActionType.TouchCouncilElf = PlayerActionType.New(33, false, PlayerTouchCouncilElfHandler)
PlayerActionType.Parttime = PlayerActionType.New(39, false, PlayerParttimeHandler)
PlayerActionType.CreateCushion = PlayerActionType.New(41, false, PlayerCushionCreateHandler)
PlayerActionType.JoinCushion = PlayerActionType.New(42, false, PlayerCushionJoinHandler)
PlayerActionType.PlayHandItem = PlayerActionType.New(43, false, PlayerPlayHandItemHandler)
PlayerActionType.WerewolfDead = PlayerActionType.New(44, false, PlayerWerewolfDeadHandler)
PlayerActionType.WerewolfPose = PlayerActionType.New(45, true, PlayerWerewolfPoseHandler)
PlayerActionType.WerewolfTunnel = PlayerActionType.New(46, true, PlayerWerewolfTunnelHandler)
PlayerActionType.PlaySoda = PlayerActionType.New(47, false, PlayerPlaySodaHandler)
PlayerActionType.WerewolfGetOutTunnel = PlayerActionType.New(48, true, WerewolfGetOutTunnelHandler)
PlayerActionType.Navigator = PlayerActionType.New(49, false, PlayerNavigatorHandler)
PlayerActionType.StartElfBattlePartice = PlayerActionType.New(50, false, PlayerStartElfBattlePracticeHandler)
PlayerActionType.JoinElfBattlePartice = PlayerActionType.New(51, false, PlayerJoinElfBattlePracticeHandler)
PlayerActionType.CouncilBossDrop = PlayerActionType.New(52, true, PlayerDropHandler)
PlayerActionType.FurnitureInteraction = PlayerActionType.New(53, true, FurnitureInterActionHandler)
PlayerActionType.CombineGameIdle = PlayerActionType.New(54, false, PlayerCombineGameIdleHandler)

PlayerActionType.AssemblyLine_Joinin = PlayerActionType.New(56, false, PlayerAssemblyLineJoininHandler)
PlayerActionType.AssemblyLine_Cooking = PlayerActionType.New(55, true, PlayerAssemblyLineCookingHandler)

PlayerActionType.FurnitureStatusSync = PlayerActionType.New(57, true, FurnitureStatusSyncHandler)
PlayerActionType.WerewolfLoverDead = PlayerActionType.New(58, true, WerewolfLoverDeadHandler)

PlayerActionType.MusicParty_Encourage = PlayerActionType.New(59, true, MusicPartyEncourageHandler)	--打call
PlayerActionType.MusicParty_GoOnStage = PlayerActionType.New(60, false, MusicPartyGoOnStageHandler)	--上舞台
PlayerActionType.MusicParty_Dancing = PlayerActionType.New(61, true, MusicPartyDancingHandler)		--跳舞动作
PlayerActionType.MusicParty_Hifun = PlayerActionType.New(62, true, MusicPartyHifunHandler)		--hifun动作
PlayerActionType.MusicParty_Idle = PlayerActionType.New(63, false, MusicPartyIdleHandler)		--idle动作
PlayerActionType.Fly3D = PlayerActionType.New(64, false, Fly3DHandler)		--3d飞行
PlayerActionType.GobangFurniturePlayer = PlayerActionType.New(65, false, GobangFurniturePlayerHandler) --五子棋家具棋手状态

PlayerActionType.CatchInsect = PlayerActionType.New(67, false, PlayerCatchInsectHandler) --捉虫状态
PlayerActionType.GobangMatchWatchEntry = PlayerActionType.New(66, false, GobangMatchWatchEntryHandler) --五子棋匹配观战入口
PlayerActionType.AnniversaryGalaSceneGame = PlayerActionType.New(68, false, AnniversaryGalaSceneGameHandler) --周年庆场景游戏
PlayerActionType.RainbowTrail = PlayerActionType.New(69, false, RainbowTrailHandler)--彩虹步道
PlayerActionType.AnniversaryGalaSceneOccupyGame = PlayerActionType.New(70, false, AnniversaryGalaSceneOccupyGameHandler) --周年庆场景占坑类游戏
PlayerActionType.AnniversaryGalaColorGrid = PlayerActionType.New(71, false, AnniversaryGalaColorGridHandler) --周年庆场景占坑类游戏
PlayerActionType.AnniversaryGalaStartHand = PlayerActionType.New(72, false, AnniversaryGalaStartHandHandler) --邀请牵手
PlayerActionType.AnniversaryGalaAcceptHand = PlayerActionType.New(73, false, AnniversaryGalaAcceptHandHandler) --接受牵手
PlayerActionType.AnniversaryGalaAttachedAsk = PlayerActionType.New(74, false, AnniversaryGalaAttachedAskHandler) --结缘邀请
PlayerActionType.AnniversaryGalaAttachedAnswer = PlayerActionType.New(75, true, AnniversaryGalaAttachedAnswerHandler) --结缘接受
PlayerActionType.AnniversaryGalaJoinGodlike = PlayerActionType.New(76, true, AnniversaryGalaJoinGodlikeHandler) --诸神状态
PlayerActionType.AnniversaryGalaSite = PlayerActionType.New(77, false, AnniversaryGalaSiteHandler) --周年庆场景排队
PlayerActionType.AnniversaryGalaCar = PlayerActionType.New(78, false, AnniversaryGalaCarHandler) --周年庆花车上

PlayerActionType.WerewolfTransport = PlayerActionType.New(79, false, WerewolfTransportHandler) -- 狼人杀传送

PlayerActionType.InterstellarScienceFinishQuestionHandler = PlayerActionType.New(80, false, InterstellarScienceFinishQuestionHandler) -- 星际答题每次答题结束
PlayerActionType.InterstellarScienceAccelerateMovement = PlayerActionType.New(81, false, InterstellarScienceMoveHandler) -- 星际答题加速移动

PlayerActionType.WealthGodFollow = PlayerActionType.New(83, false, WealthGodFollowHandel)	--财神爷跟随
PlayerActionType.WealthGodEmoji = PlayerActionType.New(84, true, WealthGodEmojiHandel)	--财神爷跟随
PlayerActionType.FurnitureSceneAction = PlayerActionType.New(85, false, FurnitureSceneHandler)	--家具带结束动作表现

PlayerActionType.MusicParty_Wave = PlayerActionType.New(86, true, MusicPartyWaveHandler)	--音乐派对音浪
PlayerActionType.CityPartyTrain = PlayerActionType.New(87, false, CityPartyTrainHandler)	--城市派对火车
PlayerActionType.TransformSkin = PlayerActionType.New(88, false, TransformSkinPlayerHandler)	--变身道具
PlayerActionType.WeatherUmbrella = PlayerActionType.New(89, false, WeatherUmbrellaHandler)
PlayerActionType.JoinWeatherUmbrella = PlayerActionType.New(90, false, JoinWeatherUmbrellaHandler)
PlayerActionType.StartRideMount = PlayerActionType.New(91, false, StartRideMountHandler)	--发起骑坐骑的
PlayerActionType.JoinRideMount = PlayerActionType.New(92, false, JoinRideMountHandler)		--蹭坐骑的
PlayerActionType.LaunchSeaDance = PlayerActionType.New(93, false, LaunchSeaDanceHandler)
PlayerActionType.JoinSeaDance = PlayerActionType.New(94, false, JoinSeaDanceHandler)
PlayerActionType.SeaDancePool = PlayerActionType.New(95, false, PlayerIdleHandler)		--其实就是个空动作，给后端标记一下的
PlayerActionType.SeaDancing = PlayerActionType.New(96, false, PlayerSeaDancingHandler)	--参与舞会中
PlayerActionType.WaitTogetherDance = PlayerActionType.New(97, false, WaitTogetherDanceHandler)
PlayerActionType.SeaFishing = PlayerActionType.New(98, true, SeaFishingHandler)
PlayerActionType.SeaShowFish = PlayerActionType.New(99, true, SeaShowFishHandler)
PlayerActionType.SeaAttackBoss = PlayerActionType.New(100, false,SeaAttackBossHandler)
PlayerActionType.DollHouseUsingSkill = PlayerActionType.New(101, true, DollHouseUsingSkillHandler) -- 玩偶屋使用技能
PlayerActionType.DollHouseAttacked = PlayerActionType.New(102, false, DollHouseAttackedHandler) -- 玩偶屋被攻击
PlayerActionType.DollHouseClimbWall = PlayerActionType.New(103, false, DollHouseClimbWallHandler) -- 玩偶屋翻墙
PlayerActionType.DollhouseIdle = PlayerActionType.New(104, false, DollhouseIdleHandler) -- 玩偶屋待机
PlayerActionType.DollHousePose = PlayerActionType.New(105, false, DollHousePoseHandler) -- 玩偶屋动作
PlayerActionType.DollHouseAniming = PlayerActionType.New(106, false, DollHouseAnimingHandler) -- 玩偶屋瞄准
PlayerActionType.DollHouseCarringPlayer = PlayerActionType.New(107, false, DollHouseCarringHandler) -- 玩偶屋扛起玩家
PlayerActionType.DollHouseBeingCarried = PlayerActionType.New(108, false, DollHouseBeingCarriedHandler) -- 玩偶屋玩家被扛起
PlayerActionType.DollHouseTasking = PlayerActionType.New(109, false, DollHouseTaskingHandler) -- 玩偶屋玩家被扛起
PlayerActionType.DollHouseUseTunnel = PlayerActionType.New(110, false, DollHouseUseTunnelHandler) -- 玩偶屋玩家使用地道
PlayerActionType.DollHouseEscaping = PlayerActionType.New(111, false, DollHouseEscapingHandler) -- 玩偶屋玩家开门中
PlayerActionType.DollHouseHelpingOther = PlayerActionType.New(112, false, DollHouseHelpingOtherHandler) -- 玩偶屋治疗其他玩家
PlayerActionType.DollHouseBeingHelped = PlayerActionType.New(113, false, DollHouseBeingHelpedHandler) -- 玩偶屋被治疗中
PlayerActionType.DollHouseThrowSkill = PlayerActionType.New(114, false, DollHouseThrowSkillHandler) -- 玩偶屋玩家投掷技能
PlayerActionType.CreateFurnitureCushion = PlayerActionType.New(115, false, FurnitureCushionCreateHandler)
PlayerActionType.JoinFurnitureCushion = PlayerActionType.New(116, false, FurnitureCushionJoinHandler)
PlayerActionType.FurnitureCushionDonate = PlayerActionType.New(117, true, FurnitureCushionDonateHandler)
PlayerActionType.IceSkatingBeHurt = PlayerActionType.New(118, false, IceSkatingBeHurtHandler)
PlayerActionType.BonfireParty = PlayerActionType.New(119, false, BonfirePartyHandler)
PlayerActionType.BonfirePartyCall = PlayerActionType.New(120, false, BonfirePartyCallHandler)
PlayerActionType.LongStreetBanquet = PlayerActionType.New(121, false, LongStreetBanquetHandler)
PlayerActionType.CelebParty = PlayerActionType.New(122, false, CelebPartyHandler)
PlayerActionType.StartDoubleFollow = PlayerActionType.New(123, false, StartDoubleFollowHandler)
PlayerActionType.AcceptDoubleFollow = PlayerActionType.New(124, false, JoinDoubleFollowHandler)

return PlayerActionType
