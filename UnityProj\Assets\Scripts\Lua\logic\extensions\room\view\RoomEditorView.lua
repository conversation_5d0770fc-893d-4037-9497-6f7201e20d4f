module("logic.extensions.room.view.RoomEditorView", package.seeall)
local RoomEditorView = class("RoomEditorView", ViewComponent)

local ViewsAnimConfig = {
	VerTop = {showPos = 0, hidePos = 150, dir = kScrollDirV, goPath = "verTop"},
	VerBottom = {showPos = 0, hidePos = - 230, dir = kScrollDirV, goPath = "verBottom"},
	multiiemop = {showPos = 86.1, hidePos = - 68, dir = kScrollDirV, goPath = "multiiemop"},
	joystickGo = {showPos = 110, hidePos = - 200, dir = kScrollDirH, goPath = "joystickGo"},
	HorLeft = {showPos = 0, hidePos = - 200, dir = kScrollDirH, goPath = "horLeft"},
	HorRight = {showPos = 0, hidePos = 200, dir = kScrollDirH, goPath = "horRight"}
}

local ViewsModeConfig = {
	-- {
		-- 	goPath = {"horRight"},
		-- 	showMode = {RoomEditModel.Mode.Edit, RoomEditModel.Mode.Paper, RoomEditModel.Mode.Search, RoomEditModel.Mode.Sample, RoomEditModel.Mode.Outside}
		-- },
		{
		goPath = {"horRight"},
		showMode = {12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 14}
	},
	{
		goPath = {"verTop", "horLeft"},
		showMode = {13, 12, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 14}
	},
	{
		goPath = {"verTop/rightGo/togMultiSelect"},
		showMode = {12, RoomEditModel.Mode.Edit, RoomEditModel.Mode.Paper, RoomEditModel.Mode.Search, RoomEditModel.Mode.Sample, RoomEditModel.Mode.Multi}
	},
	{
		goPath = {"verTop/templateGo"},
		showMode = {13, 12}
	},
	{
		goPath = {"editdiyop"},
		showMode = {13}
	},
	{
		goPath = {"verTop/rightGo/togConstruction"},
		showMode = {12, RoomEditModel.Mode.Edit, RoomEditModel.Mode.Paper, RoomEditModel.Mode.Search, RoomEditModel.Mode.Sample, RoomEditModel.Mode.Multi, RoomEditModel.Mode.Surface}
	},
	{
		goPath = {"verBottom", "verBottom/bg/leftGo"},
		showMode = {14, 12, RoomEditModel.Mode.Edit, RoomEditModel.Mode.Paper, RoomEditModel.Mode.Search, RoomEditModel.Mode.Sample, RoomEditModel.Mode.Outside, RoomEditModel.Mode.Weather}
	},
	{
		goPath = {"multiiemop/btnSave"},
		showMode = {RoomEditModel.Mode.Multi}, sceneType = {5, 79, 109}
	},
	{
		goPath = {"verBottom/off/furList", "verBottom/on/furList", "verBottom/buttonGroup/btnSearch", "verBottom/buttonGroup/btnSort", "verBottom/buttonGroup/btnType"},
		showMode = {12, RoomEditModel.Mode.Edit, RoomEditModel.Mode.Paper, RoomEditModel.Mode.Search}
	},
	{
		goPath = {"verBottom/off/bgList", "verBottom/on/bgList"},
		showMode = {RoomEditModel.Mode.Outside}
	},
	{
		goPath = {"verBottom/off/streetList", "verBottom/on/streetList"},
		showMode = {14}
	},
	{
		goPath = {"verBottom/off/weatherList", "verBottom/on/weatherList"},
		showMode = {RoomEditModel.Mode.Weather}
	},
	{
		goPath = {"verTop/countTips"},
		showMode = {13, 12, RoomEditModel.Mode.Multi, RoomEditModel.Mode.Surface}
	},
	{
		goPath = {"joystickGo"},
		showMode = {RoomEditModel.Mode.Multi, RoomEditModel.Mode.Surface}
	},
	{
		goPath = {"constructionop", "surfaceMask"},
		showMode = {RoomEditModel.Mode.Surface}
	},
	{
		goPath = {"verBottom/off/templateList", "verBottom/on/templateList"},
		showMode = {RoomEditModel.Mode.Sample}
	},
	{
		goPath = {"btnClose"},
		showMode = {RoomEditModel.Mode.ViewTemplate}
	},
}

RoomEditorView.DoTweenMode = {
-- RoomEditModel.Mode.Area,
-- RoomEditModel.Mode.Multi
}

function RoomEditorView:setViewActive(view, show)
	return self:_doAnchorPos(self:getGo(view.goPath), show and view.showPos or view.hidePos, 0.3, view.dir)
end

function RoomEditorView:_doAnchorPos(go, pos, duration, dir)
	local tween
	if dir == kScrollDirH then
		tween = go.transform:DOAnchorPosX(pos, duration)
	else
		tween = go.transform:DOAnchorPosY(pos, duration)
	end
	return tween
end

function RoomEditorView:buildUI()
end

function RoomEditorView:onExit()
	RoomController.instance:localNotify(RoomNotifyName.OnEditViewExit)
	ItemService.instance:clearNewStateByTpyeList({ItemType.FURNITURE.id})
end

function RoomEditorView:onEnter()
	RoomController.instance:localNotify(RoomNotifyName.OnEditViewEnter)
	settimer(1, self._setComponentActive, self, false)
	self:_setComponentActive()
	if RoomTaskHelper.instance:_checkDoingCommonKeyTask("PlaceFurnitureTaskStep") then
		self:_handleDrag(false)
	end
end

function RoomEditorView:bindEvents()
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnBeginDragFurniture, self._onDragFurniture, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnEndDragFurniture, self._onEndDragFurniture, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.CancelClickFurniture, self._onEndDragFurniture, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnEditModeChange, self._onModeChange, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnBeginMultiDrag, self._onDragFurniture, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnEndMultiDrag, self._onEndDragFurniture, self)
	self:getBtn("btnClose"):AddClickListener(self._onClickLeave, self)
end

function RoomEditorView:_onClickLeave()
	RoomController.instance:localNotify(RoomNotifyName.CancelOpFurniture)
	RoomController.instance:localNotify(RoomNotifyName.ResetEditFurniture, true)
	ViewMgr.instance:close("TemplatePreviewHUD")
	FarmFacade.instance:enterMyIslandHideLoading()
end

function RoomEditorView:unbindEvents()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnBeginDragFurniture, self._onDragFurniture, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnEndDragFurniture, self._onEndDragFurniture, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.CancelClickFurniture, self._onEndDragFurniture, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnEditModeChange, self._onModeChange, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnBeginMultiDrag, self._onDragFurniture, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnEndMultiDrag, self._onEndDragFurniture, self)
end

function RoomEditorView:_onDragFurniture()
	self:_handleDrag(false)
end

function RoomEditorView:_onEndDragFurniture(canPlace)
	if not RoomTaskHelper.instance:_checkDoingCommonKeyTask("PlaceFurnitureTaskStep") or canPlace then
		self:_handleDrag(true)
	end
end

function RoomEditorView:_handleDrag(active)
	if self._viewActive == active then return end
	self._viewActive = active
	if self._dragTweens then
		self._dragTweens:Kill()
		self._dragTweens = nil
	end
	self._dragTweens = DG.Tweening.DOTween.Sequence()
	for _, viewInfo in pairs(ViewsAnimConfig) do
		self._dragTweens:Join(self:setViewActive(viewInfo, active))
	end
end

function RoomEditorView:_onModeChange(mode, lastMode)
	-- print("onModeChange ======================", mode, debug.traceback())
	local doTween = false
	for i, rightMode in ipairs(RoomEditorView.DoTweenMode) do
		if RoomEditModel.isChange(mode, lastMode, rightMode) then
			doTween = true
			break
		end
	end
	if doTween then
		if self._areaTweens ~= nil then
			self._areaTweens:Kill()
			self._areaTweens = nil
		end
		self._areaTweens = DG.Tweening.DOTween.Sequence()
		self._areaTweens:Append(self:setViewActive(ViewsAnimConfig.HorLeft, false))
		:Join(self:setViewActive(ViewsAnimConfig.VerBottom, false))
		:Join(self:setViewActive(ViewsAnimConfig.HorRight, false))
		:AppendCallback(handler(self._setComponentActive, self))
		:AppendCallback(handler(self._tgComp.rebuildLayout, self._tgComp))
		:Append(self:setViewActive(ViewsAnimConfig.VerBottom, true))
		:Join(self:setViewActive(ViewsAnimConfig.HorLeft, true))
		:Join(self:setViewActive(ViewsAnimConfig.HorRight, true))
	else
		self:_setComponentActive()
	end
end

function RoomEditorView:_setComponentActive()
	local curMode = RoomEditModel.instance._mode
	local inList, checkSceneType
	print("curMode ================", curMode)
	for _1, modeConfig in ipairs(ViewsModeConfig) do
		checkSceneType = modeConfig.sceneType == nil or table.indexof(modeConfig.sceneType, SceneManager.instance:getCurSceneId()) ~= false
		inList = checkbool(table.indexof(modeConfig.showMode, curMode))
		for _2, goPath in ipairs(modeConfig.goPath) do
			self:getGo(goPath):SetActive(checkSceneType and inList)
		end
	end
end

return RoomEditorView 