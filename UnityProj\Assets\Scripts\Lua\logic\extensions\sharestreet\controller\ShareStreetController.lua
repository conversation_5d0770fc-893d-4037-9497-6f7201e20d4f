module("logic.extensions.sharestreet.controller.ShareStreetController", package.seeall)

local ShareStreetController = class("ShareStreetController", BaseController)

function ShareStreetController:ctor()

end

function ShareStreetController:getMyInfo(callbackHandler)
    self:getInfo(UserInfo.userId, false, callbackHandler)
end

---@param isTemp boolean 不更新model，只能通过回调获取info
function ShareStreetController:getInfo(userId, isSelf, callbackHandler, isTemp)
    userId = userId or UserInfo.userId
    ShareStreetAgent.instance:sendGetShareStreetInfoRequest(userId, isSelf, handler(function(_, shareStreetInfo, extInfo)
        local info = nil
        if shareStreetInfo then
            if isTemp then
                info = ShareStreetInfo.New()
                info:setInfo(shareStreetInfo)
            else
                info = ShareStreetModel.instance:setUserInfo(shareStreetInfo)
            end
        end
        if extInfo then
            if extInfo.like ~= nil then
                ShareStreetModel.instance:setIsLike(userId, extInfo.like)
            end
            if userId == UserInfo.userId then
                if extInfo.hasInvited then
                    ShareStreetModel.instance:setHasInvited(extInfo.hasInvited)
                end
                if extInfo.hasApply then
                    ShareStreetModel.instance:setHasApply(extInfo.hasApply)
                end
                if extInfo.mineStreetId then
                    ShareStreetModel.instance:setMyStreetId(extInfo.mineStreetId)
                    -- if enableDebug and ShareStreetModel.instance:hasCreatedStreet() then
                    --     ShareStreetFacade.instance:setDefaultScene2ShareStreet(true)
                    -- end
                end
                if extInfo.modifyNameCount then
                    ShareStreetModel.instance:setModifyNameCount(extInfo.modifyNameCount)
                end
            end
        end
        if callbackHandler then
            callbackHandler(info)
        end
    end, self))
end

function ShareStreetController:createShareStreet(name, desc, callbackHandler)
    ShareStreetAgent.instance:sendCreateShareStreetRequest(name, desc, handler(function(_, sharestreetinfo)
        ShareStreetModel.instance:setUserInfo(sharestreetinfo)
        if callbackHandler then
            callbackHandler()
        end
    end, self))
end

function ShareStreetController:unlockArea(upgradeAreaId, callbackHandler)
    ShareStreetAgent.instance:sendStreetUnlockAreaRequest(upgradeAreaId, handler(function(_, ci)
        ShareStreetModel.instance:unlockArea(UserInfo.userId)
        if callbackHandler then
            callbackHandler(upgradeAreaId)
        end
        self:localNotify(ShareStreetLocalNotify.OnUnlockArea, upgradeAreaId)
    end, self))
end

function ShareStreetController:inviteFriend(playerId, callbackHandler)
    ShareStreetAgent.instance:sendInviteJoinShareStreetRequest(playerId, handler(function(_)
        self:localNotify(ShareStreetLocalNotify.OnInviteFriend, playerId)
    end, self))
end

---同意受邀加入别人街区
function ShareStreetController:acceptInvite(ownerId, callbackHandler)
    ShareStreetAgent.instance:sendAgreeInviteJoinShareStreetRequest(ownerId, handler(function(_, streetInfo)
        ShareStreetModel.instance:setUserInfo(streetInfo)
	    FlyTextManager.instance:showFlyText("已加入新的街区")
        if callbackHandler then
            callbackHandler()
        end
        self:localNotify(ShareStreetLocalNotify.OnJoinOtherStreetSucc, ownerId)
    end, self))
end
---取消加入别人街区的申请
function ShareStreetController:cancelJoinApply(ownerId, callbackHandler)
    ShareStreetAgent.instance:sendCancelApplyJoinShareStreetRequest(ownerId, handler(function(_)
	    FlyTextManager.instance:showFlyText("取消成功")
        if callbackHandler then
            callbackHandler()
        end
        self:localNotify(ShareStreetLocalNotify.OnCancelJoinApplySucc, ownerId)
    end, self))
end
---同意别人的加入街区申请
function ShareStreetController:acceptJoinRequest(playerId, callbackHandler)
    ShareStreetAgent.instance:sendAgreeJoinShareStreetRequest(playerId, handler(function(_, askList, streetInfo)
        ShareStreetModel.instance:setUserInfo(streetInfo)
        FlyTextManager.instance:showFlyText("已同意加入申请")
        if callbackHandler then
            callbackHandler(askList)
        end
        self:localNotify(ShareStreetLocalNotify.OnAcceptJoinRequestSucc, playerId, askList)
    end, self))
end
---拒绝别人的加入街区申请
function ShareStreetController:refuseJoinRequest(playerId, callbackHandler)
    ShareStreetAgent.instance:sendRefuseJoinShareStreetRequest(playerId, handler(function(_, askList)
        FlyTextManager.instance:showFlyText("已忽略加入申请")
        if callbackHandler then
            callbackHandler()
        end
        self:localNotify(ShareStreetLocalNotify.OnRefuseJoinRequestSucc, playerId, askList)
    end, self))
end
---退出街区
function ShareStreetController:quit(callbackHandler)
    ShareStreetAgent.instance:sendQuitShareStreetRequest(handler(function(_, shareStreetInfo)
        ShareStreetModel.instance:setUserInfo(shareStreetInfo)
        if callbackHandler then
            callbackHandler()
        end
        self:localNotify(ShareStreetLocalNotify.OnQuitSucc)
    end, self))
end
---踢出街区
function ShareStreetController:kick(ownerId, callbackHandler)
    ShareStreetAgent.instance:sendKickOutShareStreetRequest(ownerId, handler(function(_, shareStreetInfo)
        ShareStreetModel.instance:setUserInfo(shareStreetInfo)
        if callbackHandler then
            callbackHandler()
        end
    end, self))
end
---修改名字
function ShareStreetController:changeName(newName, callbackHandler)
    ShareStreetAgent.instance:sendStreetUpdateInfoRequest(newName, 1, handler(function(_, shareStreetInfo)
        ShareStreetModel.instance:getUserInfo():setName(newName)
        if callbackHandler then
            callbackHandler()
        end
        self:localNotify(ShareStreetLocalNotify.OnChangeNameSucc, newName)
    end, self))
end
---修改描述
function ShareStreetController:changeDesc(newDesc, callbackHandler)
    ShareStreetAgent.instance:sendStreetUpdateInfoRequest(newDesc, 2, handler(function(_, shareStreetInfo)
        ShareStreetModel.instance:getUserInfo():setDesc(newDesc)
        if callbackHandler then
            callbackHandler()
        end
        self:localNotify(ShareStreetLocalNotify.OnChangeDescSucc, newDesc)
    end, self))
end

ShareStreetController.instance = ShareStreetController.New()
return ShareStreetController
