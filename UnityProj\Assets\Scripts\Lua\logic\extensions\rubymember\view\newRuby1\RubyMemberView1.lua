module("logic.extensions.rubymember.view.newRuby1.RubyMemberView1", package.seeall)

local RubyMemberView1 = class("RubyMemberView1", ViewComponent)

function RubyMemberView1:buildUI()
    -- 基本部件
    self._btnBuy = self:getBtn("btnBuy")
    self._btnClose = self:getBtn("btnclose/btnClose")
    self._btnGo = self:getBtn("furnitureGo/btnGo")
    self._btnMoreBuff = self:getBtn("privilegeGo/btnDetail")
    -- self._btnRule = self:getBtn("btnRule")

    self.buffItemGo = self:getGo("privilegeGo/listView/content/rubymemberprivilegeitem")
    self.buffContent = self:getGo("privilegeGo/listView/content")
    self.buffCountTips = self:getText("privilegeGo/txtTips")
    self.dailyRewardIcons = {}
    self:buildDailyReward()
    self:buildBaseBuff()
end

function RubyMemberView1:destroyUI()
    for i, v in ipairs(self.dailyRewardIcons) do
        CommonIconMgr.instance:returnCommonIcon(v)
    end
    self.dailyRewardIcons = {}
end

function RubyMemberView1:buildDailyReward()
    local info = RubyMemberConfig.getDailyGift()
    for i = 1, 3, 1 do
        local icon = CommonIconMgr.instance:fetchCommonIcon()
        icon:setWidthAndHeight(80):showCount(true):showRarenessGo(true)
        table.insert(self.dailyRewardIcons, icon)
        local container = self:getGo("everyDayAwardGo/awardItem_" .. i)
        goutil.addChildToParent(icon:getPrefab(), container)
        if info.gifts[i] then
            icon:buildData(info.gifts[i])
        else
            goutil.setActive(container, false)
        end
    end
end

local newIdList = {50,51,52,53}

function RubyMemberView1:buildBaseBuff()
    local buffInfoList = RubyMemberConfig.getBuffInfo().buffList
    self.buffCountTips.text = lang("{1}项尊贵专属特权",#buffInfoList)
    for i = 1, #buffInfoList, 1 do
        if i > 7 then
            break
        end
        local buffGo = goutil.clone(self.buffItemGo)
        goutil.setActive(buffGo, true)
        goutil.addChildToParent(buffGo, self.buffContent)
        if table.indexof(newIdList, buffInfoList[i].buffId) then
            goutil.findChild(buffGo, "newGo"):SetActive(true)
        end
        goutil.findChildTextComponent(buffGo, "txtPrivilege").text = buffInfoList[i].content
        IconLoader.setSpriteToImg(goutil.findChild(buffGo, "imgIcon"), buffInfoList[i].id)
    end
end

function RubyMemberView1:bindEvents()
    RubyMemberView1.super.bindEvents(self)
    self._btnClose:AddClickListener(self.close, self)
    self._btnBuy:AddClickListener(self.buy, self)
    self._btnGo:AddClickListener(self.goShop, self)
    self._btnMoreBuff:AddClickListener(self.showMoreBuff, self)
    -- self._btnRule:AddClickListener(self.showInherit, self)
    self:registerLocalNotify(RubyNotifyConfig.OnGetRubyInfo, self.updateDailyReward, self)
    self:registerNotify(GlobalNotify.OnServerRefresh, self.close, self)
end

function RubyMemberView1:unbindEvents()
    self:unregisterLocalNotify(RubyNotifyConfig.OnGetRubyInfo, self.updateDailyReward, self)
    self:registerNotify(GlobalNotify.OnServerRefresh, self.close, self)
end

function RubyMemberView1:onEnter()
    self:registerLocalNotify(RubyNotifyConfig.RubyMemberChange, self.updateDailyReward, self)
end

function RubyMemberView1:onExit()
    self:unregisterLocalNotify(RubyNotifyConfig.RubyMemberChange, self.updateDailyReward, self)
end

function RubyMemberView1:updateDailyReward()
    print("刷一刷每日奖励")
    local vipType = RubyMemberController.instance:getRubyInfo().vipType
    local dayDrawn = RubyMemberController.instance:getRubyInfo().dayDrawn
    for i, v in ipairs(self.dailyRewardIcons) do
        local state = dayDrawn and CommonIcon.State_Received or CommonIcon.State_None
        v:showAwardState(state)
    end
    if (vipType > -1) and not dayDrawn then
        -- DialogHelper.showMsg("自动领取每日奖励", function()
        --     VipAgent.instance:sendDrawVipDailyRewardRequest(handler(self.receiveGift, self))
        -- end)
        ViewMgr.instance:open("RubyGetDailyReward", handler(self.receiveGift, self))
    end
end

function RubyMemberView1:receiveGift(changeSetId)
    DialogHelper.showRewardsDlgByCI(changeSetId)
    RubyMemberController.instance:updateDayDrawn(true)
    self:updateDailyReward()
end

function RubyMemberView1:buy()
    ViewMgr.instance:open("RubyBuy")
end

function RubyMemberView1:goShop()
    RechargeFacade.openRechargeView(6)
end

function RubyMemberView1:showMoreBuff()
    -- FlyTextManager.instance:showFlyText("等待资源中")
    ViewMgr.instance:open("RubyBuffView")
end

-- function RubyMemberView1:showInherit()
--     ViewMgr.instance:open("RubyInheritView")
-- end

return RubyMemberView1
