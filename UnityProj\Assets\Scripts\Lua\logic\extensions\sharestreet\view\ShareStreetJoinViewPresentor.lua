module("logic.extensions.sharestreet.view.ShareStreetJoinViewPresentor",package.seeall)
---@class ShareStreetJoinViewPresentor
local ShareStreetJoinViewPresentor = class("ShareStreetJoinViewPresentor",ViewPresentor)

ShareStreetJoinViewPresentor.Url_Main = "ui/street/streetjoinpanel.prefab"
ShareStreetJoinViewPresentor.Url_Item = "ui/street/streetinvititem.prefab"

function ShareStreetJoinViewPresentor:ctor()
	ShareStreetJoinViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetJoinViewPresentor:dependWhatResources()
	return {ShareStreetJoinViewPresentor.Url_Main, ShareStreetJoinViewPresentor.Url_Item}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetJoinViewPresentor:buildViews()
	return {ShareStreetJoinView.New()}
end

--- 配置view所在的ui层
function ShareStreetJoinViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetJoinViewPresentor