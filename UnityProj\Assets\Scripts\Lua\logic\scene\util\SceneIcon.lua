module("logic.scene.util.SceneIcon", package.seeall)
local SceneIcon = class("SceneIcon")

function SceneIcon.Create()
	local go = CommonRes.instance:getResInstance(CommonResPath.SceneTips)
	return PjAobi.LuaComponent.Add(go, SceneIcon)
end

function SceneIcon:ctor(compContainer)
	self.go = compContainer.gameObject
end

function SceneIcon:Awake()
	self.icon = goutil.findChild(self.go, "icon")
	self.tipsGO = goutil.findChild(self.go, "tips")
	self.txtTime = goutil.findChildTextComponent(self.go, "txtTime")
	-- local rootTriggle = Framework.UIGlobalTouchTrigger.Get(self.go)
	-- rootTriggle:AddIgnoreTargetListener(self.hideTips,self)
	self.tipsGO:SetActive(false)
	self.txtTile = goutil.findChildTextComponent(self.tipsGO, "txtTitle")
	self.txtTips = goutil.findChildTextComponent(self.tipsGO, "txtTips")
	self.lockGO = goutil.findChild(self.go, "imgLock")
	self._autoHide = false
	self.curScene = SceneManager.instance:getCurScene()
	self._isVisible = true

	self:setStroyHide(true)
end

--剧情模式下隐藏
function SceneIcon:setStroyHide(enable)
	self.isStroyHide = enable
	if enable then
		GlobalDispatcher:addListener(GlobalNotify.OnTaskStoryStart, self._onTaskStoryStart, self)
		GlobalDispatcher:addListener(GlobalNotify.OnTaskStoryEnd, self._onTaskStoryEnd, self)
		self:checkVisible()
	else
		GlobalDispatcher:removeListener(GlobalNotify.OnTaskStoryStart, self._onTaskStoryStart, self)
		GlobalDispatcher:removeListener(GlobalNotify.OnTaskStoryEnd, self._onTaskStoryEnd, self)
	end
end

function SceneIcon:_onTaskStoryStart()
	self:checkVisible()
end

function SceneIcon:_onTaskStoryEnd()
	self:checkVisible()
end

function SceneIcon:setAutoHide(value, targetPos, showRange)
	self.showRange = showRange
	self.targetPos = targetPos
	if self._autoHide == value then
		return
	end
	self._autoHide = value
	if value then
		goutil.setActive(self.go, false)
		self._isInRange = false
		settimer(0.5, self.checkVisible, self, true)
	else
		removetimer(self.checkVisible, self)
		goutil.setActive(self.go, true)
		self._isVisible = true
	end
end

function SceneIcon:checkVisible()
	if not self._isVisible then
		return
	end
	if TaskFacade.instance:isInStoryMode() then
		goutil.setActive(self.go, false)
		return
	end
	local player = self.curScene:getUserPlayer()
	if player and self.targetPos then
		local x, y = player:getPos()
		local isInRange = (self.targetPos.x - x) ^ 2 + (self.targetPos.y - y) ^ 2 < self.showRange ^ 2
		if isInRange ~= self._isInRange then
			if isInRange then
				goutil.setActive(self.go, true)
				tfutil.SetScale(self.go, 0)
				self.go.transform:DOScale(1, 0.3)
			else
				goutil.setActive(self.go, false)
			end
			self._isInRange = isInRange
		end
	end
end

function SceneIcon:addListener(callback, target, params)
	self.click = Framework.UIClickTrigger.Get(self.go)
	self.click:AddClickListener(callback, target, params)
end

function SceneIcon:setLockVisible(visible)
	self.lockGO:SetActive(visible)
	self.icon:SetActive(not visible)
end

function SceneIcon:setTips(title, tips)
	self.txtTile.text = title
	self.txtTips.text = tips
end

function SceneIcon:showTips()
	removetimer(self.hideTips, self)
	settimer(3, self.hideTips, self, false)
	self.tipsGO:SetActive(true)
end

function SceneIcon:hideTips()
	self.tipsGO:SetActive(false)
end

function SceneIcon:setVisible(value)
	self._isVisible = value
	if value then
		self._isInRange = nil
		if self._autoHide then
			self:checkVisible()
		else
			goutil.setActive(self.go, true)
		end
	else
		goutil.setActive(self.go, false)
	end
end

function SceneIcon:hide()
	goutil.setActive(self.go, false)
end

function SceneIcon:dispose()
	goutil.destroy(self.go)
end

function SceneIcon:OnDestroy()
	removetimer(self.hideTips, self)
	if self.isStroyHide then
		GlobalDispatcher:removeListener(GlobalNotify.OnTaskStoryStart, self._onTaskStoryStart, self)
		GlobalDispatcher:removeListener(GlobalNotify.OnTaskStoryEnd, self._onTaskStoryEnd, self)
	end
	self:setAutoHide(false)
	if self.click then
		self.click:RemoveClickListener()
	end
end
return SceneIcon
