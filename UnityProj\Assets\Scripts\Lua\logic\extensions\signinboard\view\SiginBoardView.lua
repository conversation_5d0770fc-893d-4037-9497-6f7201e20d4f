module("logic.extensions.signinboard.view.SiginBoardView",package.seeall)

local SiginBoardView = class("SiginBoardView", ViewComponent)

--- view初始化时会执行
function SiginBoardView:buildUI()
    SiginBoardView.super.buildUI(self)
    self._btnClose = self:getBtn("btnclose/btnClose")
    self._btnhelp = self:getBtn("btnhelp")
    self._btnGo = self:getBtn("btnGo")
    self._txtDes = self:getText("txtDes")
    self._txtProgress = self:getText("txt2")
    self._leaveMsgItem = self:getGo("item")
    self._content = self:getGo("content")
    self._item = self:getGo("content/item")
    self._item:SetActive(false)
    self._redPoint = self:getGo("btnGo/redPoint")
    self._txtTips = self:getText("txtTips")

    self._commonIcons = {}
    self._rewardCfgs = Activity466Config.getAllAwardConfig()
    local leaveMsgRewardCfgInfo = string.splitToNumber(Activity466Config.getCommonConfig("leaveMessageRewards"), ":")
    self._leaveMessageRewardCfg = {id=leaveMsgRewardCfgInfo[1], count=leaveMsgRewardCfgInfo[2]}
    self._maxProgress = tonumber(Activity466Config.getCommonConfig("maxProgressValue"))
    self._npcId = tonumber(Activity466Config.getCommonConfig("npcId"))

    self._txtDes.text = lang("SiginBoardView_desc")
    self._items = {}
end

--- view初始化时会执行，在buildUI之后
function SiginBoardView:bindEvents()
    SiginBoardView.super.bindEvents(self)
    self._btnClose:AddClickListener(self.close, self)
    self._btnhelp:AddClickListener(self._onClickHelp, self)
    self._btnGo:AddClickListener(self._onClickGo, self)
end

--- view销毁时会执行，在destroyUI之前
function SiginBoardView:unbindEvents()
    SiginBoardView.super.unbindEvents(self)
    self._btnClose:RemoveClickListener()
    self._btnhelp:RemoveClickListener()
    self._btnGo:RemoveClickListener()
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function SiginBoardView:onEnter()
    SiginBoardView.super.onEnter(self)
    self:_getInfo()
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function SiginBoardView:onExit()
    SiginBoardView.super.onExit(self)
    for i=1,#self._commonIcons do
        CommonIconMgr.instance:returnCommonIcon(self._commonIcons[i])
    end
    CommonIconMgr.instance:returnCommonIcon(self._leaveMessageCommonIcon)
    self._commonIcons = {}
end

function SiginBoardView:_getInfo()
    Activity466Agent.instance:sendGetAct466InfoRequest(handler(self._onGetInfo, self))
end

function SiginBoardView:_onGetInfo(curglobalprogress, selfmessage, backgroundindex, gainedglobalprogressrewardids)
    self._curGlobalProgress = curglobalprogress
    self._hasGetRewardIds = gainedglobalprogressrewardids or {}
    local progress = curglobalprogress/self._maxProgress*100
    progress = progress > 100 and 100 or progress
    self._txtProgress.text = tostring(math.floor(progress)).."%"
    local index = 1
    for i=1,#self._rewardCfgs do
        local rewardState = table.indexof(self._hasGetRewardIds, self._rewardCfgs[i].id) ~= false and CommonIcon.State_Received or CommonIcon.State_None
        for j=1,#self._rewardCfgs[i].progressReward do
            local item = {}
            item.go = self._items[index]
            if not item.go then
                item.go = goutil.clone(self._item)
                table.insert(self._items, item.go)
            end
            item.go:SetActive(true)
            item.txtCount = goutil.findChildTextComponent(item.go, "txtCount")
            item.txtCount.text = string.format("%.2d", self._rewardCfgs[i].progressValue/self._maxProgress*100).."%"
            local icon = CommonIconMgr.instance:fetchCommonIcon()
            icon:setWidthAndHeight(78):showFreeState(false):showRarenessGo(true):showSpecial(true)
            icon:buildData(self._rewardCfgs[i].progressReward[j])
            icon:showAwardState(rewardState)
            item.icon = icon
            goutil.addChildToParent(icon:getPrefab(), item.go)
            goutil.addChildToParent(item.go, self._content)
            table.insert(self._commonIcons, icon)
            index = index + 1
        end
    end
    self._leaveMessageCommonIcon = CommonIconMgr.instance:fetchCommonIcon()
    self._leaveMessageCommonIcon:setWidthAndHeight(78):showFreeState(false):showRarenessGo(true):showSpecial(true)
    self._leaveMessageCommonIcon:buildData(self._leaveMessageRewardCfg)
    local awareState = not string.nilorempty(selfmessage) and CommonIcon.State_Received or CommonIcon.State_None
    self._leaveMessageCommonIcon:showAwardState(awareState)
    goutil.addChildToParent(self._leaveMessageCommonIcon:getPrefab(), self._leaveMsgItem)
    self._redPoint:SetActive(string.nilorempty(selfmessage) or self:_hasProgressReward())
    self._txtTips.gameObject:SetActive(string.nilorempty(selfmessage) or self:_hasProgressReward())
end

function SiginBoardView:_hasProgressReward()
    for k,v in ipairs(self._rewardCfgs) do
        if self._curGlobalProgress >= v.progressValue and table.indexof(self._hasGetRewardIds, v.id) == false then
            return true
        end
    end
    return false
end

function SiginBoardView:_onClickHelp()
    local title = lang("SiginBoardView_rule_title")
    local content = lang("SiginBoardView_rule_content")
    ViewMgr.instance:open("ActivityRuleCommon", title, content)
end

function SiginBoardView:_onClickGo()
    ViewMgr.instance:close("ToyHouseMap")
    self:close()
    -- NpcService.instance:gotoNpc(self._npcId)
	local curTime = ServerTime.nowDateServerLook().hour
    local npcBehavior = NpcConfig.getNpcBehavior(self._npcId, curTime)
    local fromSceneId = SceneManager.instance:getCurScene():getSceneId()
	if fromSceneId ~= npcBehavior.sceneId then
		SceneManager.instance:loadScene(npcBehavior.sceneId, nil, nil, fromSceneId, handlerWithParams(self._onEnterTargetScene,self,{npcBehavior,self._npcId}))
	else
		self:_onEnterTargetScene(npcBehavior,self._npcId)
	end
end

function SiginBoardView:_onEnterTargetScene(npcBehavior, mNpcId)
	if SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.Teleport) then
		SceneController.instance:teleport(npcBehavior.playerPos[1], npcBehavior.playerPos[2], nil, true)
		local scene = SceneManager.instance:getCurScene()
		local npc = scene:getUnitMgr():getUnit(SceneUnitType.Npc, mNpcId)
		if npc then
			local myPlayer = scene:getUserPlayer()
			local x, y = myPlayer:getPos()
			npc:lookTo(x, y)
			x, y = npc:getPos()
			myPlayer:lookTo(x, y)
			RoleDialogueFacade.instance:showNPCDialogue(mNpcId)
		end
        local free3dCamera = SceneManager.instance:getCurScene():getCameraCtrl()
        free3dCamera:setCamRotate(330, 0)
	else
		FlyTextManager.instance:showFlyText(lang("正在做其他动作"))
	end
end

return SiginBoardView