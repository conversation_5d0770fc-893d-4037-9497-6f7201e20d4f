module("logic.extensions.scene.view.doublepose.DoublePosePanel",package.seeall)
local DoublePosePanel = class("DoublePosePanel", ListBinderView)

function DoublePosePanel:ctor()
	self.model = BaseListModel.New()
    DoublePosePanel.super.ctor(self, self.model,
		"listview",
		DoublePosePanelPresentor.IconPath,
		DoublePoseCell,
		{kScrollDirH, 100, 94, 20, 0, 1})
end

function DoublePosePanel:bindEvents()
	self.headIcon = self:getGo("imgGroup/imgHeadIcon")
	self.txtTips = self:getText("txtTips")

end

function DoublePosePanel:onEnter()
	DoublePosePanel.super.onEnter(self)	
	self.targetUnit = self:getFirstParam()
	_, self.friendshipLevel = FriendModel.instance:getFriendAffinity(self.targetUnit.id)
	self.txtTips.text = lang("对<color=#ffe381>{1}</color>发起动作", self.targetUnit:getNickname())
	HeadPortraitHelper.instance:setHeadPortraitWithUserId(self.headIcon, self.targetUnit.id)
	FriendAgent.instance:sendGetUnlockedFriendshipPoseRequest(self.targetUnit.id, handler(self.onGetInfo, self))
end

local function sortItem(a, b)
	if a.unlocked == b.unlocked then
		if a.define.friendshipLevel == b.define.friendshipLevel then
			return a.id < b.id
		else
			return a.define.friendshipLevel < b.define.friendshipLevel
		end
	elseif a.unlocked and not b.unlocked then
		return true
	else
		return false
	end
end

function DoublePosePanel:onGetInfo(idList)
	local all = PoseConfig.getShowedDoublePose()
	self.buyMap = {}
	for i=1, #idList do
		self.buyMap[idList[i]] = true
	end
	local define
	local list = {}
	for i=1, #all do
		define = all[i]
		list[i] = {id=define.id, define=define, unlocked=self:isUnlock(define)}
	end
	table.sort(list, sortItem)
	self.model:setMoList(list)
end

function DoublePosePanel:isUnlock(define)

	if self.friendshipLevel < define.friendshipLevel then
		return false
	end
	if define.needUnlock then
		return self.buyMap[define.id] ~= nil
	end
	return true
end

function DoublePosePanel:showUnlock(item)
	if self.friendshipLevel < item.define.friendshipLevel then
		--这里亲友度新增了等级名字，需要用名字来提示
		local csv = AffinityConfig.getAllAffinityLevelConfigForId(item.define.friendshipLevel)
		local str = ""
		if csv ~= nil then
			str = lang("需要亲密等级[{1}]解锁",csv.name)
		else
			str = lang("需要亲密等级{1}解锁",  item.define.friendshipLevel)
		end
		FlyTextManager.instance:showFlyText(str)
	else
		self:showBuy(item)
	end
end

function DoublePosePanel:showBuy(item)
	local costId = item.define.unlockItem[1].id
	local costNum = item.define.unlockItem[1].count
	local msg = lang("是否消费{1}{2}解锁该动作吗？", ItemService.instance:getName(costId), costNum)
	DialogHelper.showConfirmDlg(msg, function(isOK)
		if isOK then
			RechargeFacade.isCurrencyEnough(costId, costNum, true, function()
				self:buyPose(item.define.id)
			end)

		end
	end)
end



function DoublePosePanel:buyPose(poseId)
	FriendAgent.instance:sendUnlockFriendshipPoseRequest(self.targetUnit.id, poseId, 
		function(res)
			if res == 0 then	
				self.buyMap[poseId] = true
				self.model:getMoById(poseId).unlocked = true
				self.model:sortMoList(sortItem)
				FlyTextManager.instance:showFlyText(lang("购买成功"))
			elseif res == 1112 then
				FlyTextManager.instance:showFlyText(DialogHelper.getErrorMsg(res))
				self.model:getMoById(poseId).unlocked = true
				self.model:sortMoList(sortItem)				
			else
				FlyTextManager.instance:showFlyText(DialogHelper.getErrorMsg(res))
			end
		end)
end


function DoublePosePanel:onExit()
	DoublePosePanel.super.onExit(self)
	HeadPortraitHelper.instance:clearHeadIcon(self.headIcon)
	self.model:clear()
end

function DoublePosePanel:onClickIcon(mo)
	self:close()
	if mo.define.actionType == 1 then
		SceneController.instance:startDoubleFollow(mo.id, self.targetUnit)
	else
		SceneController.instance:startDoublePose(mo.id, self.targetUnit)
	end
end

return DoublePosePanel