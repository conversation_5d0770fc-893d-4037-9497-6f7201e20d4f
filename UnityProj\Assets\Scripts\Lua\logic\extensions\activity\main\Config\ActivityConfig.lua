module("logic.extensions.activity.main.Config.ActivityConfig", package.seeall)

local ActivityConfig = class("ActivityConfig")

local activityConfig = ConfigLoader.New("activity_config")
local activityTypeConfig = ConfigLoader.New("activity_type_config")
local allSevenDayConfig = ConfigLoader.New("seven_day")
local allSignConfig = ConfigLoader.New("sign")
local signShowConfig = ConfigLoader.New("sign_show")
local calendar = ConfigLoader.New("activitycalendar")
local firstTab = ConfigLoader.New("activitycalendar_firsttab")
local sevenDayRecharge = ConfigLoader.New("seven_day_charge")
local sevenDayRechargeReward = ConfigLoader.New("seven_day_charge_final_reward")
local levelAward = ConfigLoader.New("level_award")
local adviewcfg = ConfigLoader.New("adview")
local limitPetPanel_setting = ConfigLoader.New("limitPetPanel_setting")
local gainLimitConfig = ConfigLoader.New("activity_gain_limit")
local ActivityUGC_setting = ConfigLoader.New("ActivityUGC_setting")

function ActivityConfig.getAllActivityConfig()
	return activityConfig:getConfig().dataList
end

function ActivityConfig.getActivityConfig(activityId)
	return activityConfig:getConfig()[activityId]
end

function ActivityConfig.getActivityTypeConfig(activityDefineId)
	return activityTypeConfig:getConfig()[activityDefineId]
end

function ActivityConfig.getSevenDayCfg(gid, day)
	return allSevenDayConfig:getConfig()[gid][day]
end

function ActivityConfig.getSignCfg(day)
	local list = allSignConfig:getConfig().dataList
	local currentTime = ServerTime.now()
	local timeTable = os.date("*t", currentTime)
	local index = (tonumber(timeTable.month) % 2 == 0) and 1 or 2
	return allSignConfig:getConfig()[index][day]
end

function ActivityConfig.getSignNpcId()
	return signShowConfig:getConfig().dataList[1].npcId
end

function ActivityConfig.getCalendarFirstTabInfo()
	return firstTab:getConfig().dataList
end

function ActivityConfig.getCalendarInfo()
	return calendar:getConfig().dataList
end

function ActivityConfig.getCalendarInfoById(id)
	return calendar:getConfig()[id]
end

function ActivityConfig.getSevenDayCfgByDay(actId, day)
	return sevenDayRecharge:getConfig()[actId][day]
end

function ActivityConfig.getSevenDayRewardCfgByDay(actId)
	return sevenDayRechargeReward:getConfig()[actId]
end

function ActivityConfig.getLevelAwards()
	return levelAward:getConfig().dataList
end
function ActivityConfig.getADConfig()
	return adviewcfg:getConfig().dataList
end

function ActivityConfig.getLimitPetSetting(id)
	return limitPetPanel_setting:getConfig()[tonumber(id)]
end

function ActivityConfig.getAllGainLimitConfig()
	return gainLimitConfig:getConfig().dataList
end

function ActivityConfig.getActivityLimitConfigById(id)
	return gainLimitConfig:getConfig().dataList[id]
end

function ActivityConfig.getGainLimitTotalDailyLimitById(id)
	for i=1,#gainLimitConfig:getConfig().dataList do
		if gainLimitConfig:getConfig().dataList[i].id == id then
			return gainLimitConfig:getConfig().dataList[i].dailyLimit
		end
	end
	return nil
end

function ActivityConfig.getGainLimitTotalLimitById(id)
	for i=1,#gainLimitConfig:getConfig().dataList do
		if gainLimitConfig:getConfig().dataList[i].id == id then
			return gainLimitConfig:getConfig().dataList[i].totalLimit
		end
	end
	return nil
end

function ActivityConfig.getUGCSetting(id)
	return ActivityUGC_setting:getConfig()[tonumber(id)]
end

return ActivityConfig
