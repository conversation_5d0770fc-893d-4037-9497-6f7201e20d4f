module("logic.scene.common.action.JoystickAction",package.seeall)
local JoystickAction = class("JoystickAction", SceneActionBase)
local SendInterval = 0.0
local MinChangeTime = 0.2
local MaxChangeTime = 0.5
function JoystickAction:onStart()
	GlobalDispatcher:addListener(GlobalNotify.JoystickUp,self._onJoystickUp,self)
	GlobalDispatcher:addListener(GlobalNotify.OnDisconnected,self._onJoystickUp,self)
	self._mainPlayer = SceneManager.instance:getCurScene():getUserPlayer()
	self._mapMgr =  SceneManager.instance:getCurScene():getMapMgr()
	UpdateBeat:Add(self._checkJoystickMove,self)	
	self.sendTime = 0
	self.moveX = 0
	self.moveY = 0
	VirtualCameraMgr.instance:focusPlayer()
	self.lastChangeTime = Time.realtimeSinceStartup - 0.1
	self.syncImmediate = VirtualJoystickFacade.instance:getSyncImmediate()
	if self.syncImmediate then
		SendInterval = 0.2
	else
		SendInterval = 0.2
	end
end

function JoystickAction:onStop()
	UpdateBeat:Remove(self._checkJoystickMove,self)
	local sceneId = SceneManager.instance:getCurScene():getSceneId()
	local isFind, wayPoints = nil,nil
	if sceneId == 106 then
		isFind, wayPoints = self._mapMgr:getWalkDirectionPath_Vector3(self._mainPlayer, self.moveX, self.moveY,0.5)
	else
		isFind, wayPoints = self._mapMgr:getWalkDirectionPath(self._mainPlayer, self.moveX, self.moveY, 0.5)
	end

	--print("stop joy", self.moveX, self.moveY, isFind)
	if isFind then
		-- self._mainPlayer:getComponent(ScenePosComp):walkDirection(self.moveX,self.moveY, self._mainPlayer:getSpeed())
		self._mainPlayer:setPath(wayPoints)
		self:_syncPos(wayPoints)
	end
	self._mainPlayer:getComponent(ScenePosComp).needInterrupt = true
	-- self:_syncPos2()	
	self._mainPlayer = nil
	GlobalDispatcher:removeListener(GlobalNotify.JoystickUp,self._onJoystickUp,self)	
	GlobalDispatcher:removeListener(GlobalNotify.OnDisconnected,self._onJoystickUp,self)
	self:finish(true)	
end

function JoystickAction:_onJoystickUp()
	printInfo("stop joyStick")
	self:onStop()
end

function JoystickAction:_getCurJoystickInfo()
	return VirtualJoystickFacade.instance:getVector()
end


function JoystickAction:_checkJoystickMove()
	if not self._mainPlayer then
		return
	end
	local hasChanged = false
	local deltaTime = Time.realtimeSinceStartup - self.lastChangeTime
	if deltaTime >= MinChangeTime then
        local h, v, magnitude = self:_getCurJoystickInfo()
        if VirtualJoystickModel.instance:isLockH() then
            h = 0
        end
        if VirtualJoystickModel.instance:isLockV() then
            v = 0
        end
        if magnitude < 0.1 then
            return
        end
        if deltaTime >= MaxChangeTime or math.abs(h - self.moveX) > 0.01 or math.abs(v - self.moveY) > 0.01 then
            self.lastChangeTime = Time.realtimeSinceStartup
            self.moveX, self.moveY = h, v
            hasChanged = true
        end
    end
	if hasChanged then
		local isFind, wayPoints = nil,nil
		local sceneId = SceneManager.instance:getCurScene():getSceneId()
		if sceneId == 106 then
			isFind, wayPoints = self._mapMgr:getWalkDirectionPath_Vector3(self._mainPlayer, self.moveX, self.moveY, self._mainPlayer:getSpeed())
		else
			isFind, wayPoints = self._mapMgr:getWalkDirectionPath(self._mainPlayer, self.moveX, self.moveY, self._mainPlayer:getSpeed())
		end
		--isFind, wayPoints = self._mapMgr:getWalkDirectionPath(self._mainPlayer, self.moveX, self.moveY, self._mainPlayer:getSpeed())
		if isFind then
			-- self._mainPlayer:getComponent(ScenePosComp):walkDirection(self.moveX,self.moveY, self._mainPlayer:getSpeed())
			self._mainPlayer:setPath(wayPoints)
			self._mainPlayer:getComponent(ScenePosComp).needInterrupt = false
			if Time.realtimeSinceStartup - self.sendTime >= SendInterval then
				self.sendTime = Time.realtimeSinceStartup		
				self:_syncPos(wayPoints)
			end
		end
		-- local px,py = self._mainPlayer:getPos()
		-- local wpx = px + h/2
		-- local wpy = py + v/2
		-- if VirtualCameraMgr.instance:is3DScene() then
		-- end
		-- self:_setwaypoint(wpx, wpy)
		-- if needUpdate or Time.realtimeSinceStartup - self.sendTime >= SendInterval then 
		-- 	self.sendTime = Time.realtimeSinceStartup
			-- self:_syncPos(self.moveX,self.moveY)
		-- end
	end
end


function JoystickAction:_syncPos(wayPoints)
	local x,y = self._mainPlayer:getPos()
	SceneAgent.instance:sendMoveRequest(wayPoints, 0)			
end

function JoystickAction:_syncPos2()
	local x,y = self._mainPlayer:getPos()
	SceneAgent.instance:sendMoveRequest({{posX=x, posY=y}}, 0)			
end






-- function JoystickAction:onDispose()
-- 	UpdateBeat:Remove(self._checkJoystickMove,self)	
-- 	GlobalDispatcher:removeListener(GlobalNotify.JoystickUp,self._onJoystickUp,self)	
-- 	self._mainPlayer = nil

-- end

-- function JoystickAction:canOverlap(type)
-- 	return false
-- end

-- function JoystickAction:canInterrupt(type)
-- 	return true
-- end

function JoystickAction:getType()
	return SceneActionType.Joystick
end



return JoystickAction