module("logic.extensions.sea.view.scene.SeaMainView", package.seeall)

local SeaMainView = class("SeaMainView", ViewComponent)

function SeaMainView:ctor()
    SeaMainView.super.ctor(self)
    self._items = {}
end

function SeaMainView:buildUI()
    self._controlGo = self:getGo("controlGo")
    self._listGo = self:getGo("controlGo/bagGo/listGo")
    self._listView = self:getGo("controlGo/bagGo/listGo/listView")
    self._contentGo = self:getGo("controlGo/bagGo/listGo/listView/content")
    self._itemGo = self:getGo("controlGo/bagGo/listGo/listView/content/item")
    self._txtNothing = self:getGo("controlGo/bagGo/listGo/txtNothing")
    self._imgBulletIcon = self:getGo("controlGo/bagGo/btnBullet/imgBulletIcon")
    self._tipsGo = self:getGo("tipsGo")
    self._bossTipsGo = self:getGo("bosstipsGo")
    self._txtBulletNum = self:getText("controlGo/bagGo/btnBullet/txtBulletNum")
    self._tipsTxtGo = self:getText("tipsGo/Text")
    self._bossTipsTxt = self:getText("bosstipsGo/Text")
    self._btnShowBag = self:getBtn("controlGo/bagGo/btnShowBag")
    self._btnHideBag = self:getBtn("controlGo/bagGo/btnHideBag")
    self._btnBullet = self:getBtn("controlGo/bagGo/btnBullet")
    self._btnNoBullet = self:getBtn("controlGo/bagGo/btnNoBullet")
    self._banJoystickGo = self:getGo("controlGo/banJoystickGo")
    self._screenGo = self:getGo("controlGo/screenGo")
    self._taskGuideGo = self:getGo("taskNpcGo/taskguide")

    self._btnInput = self:getBtn("controlGo/btnInput")
    self._imgKey = self:getGo("controlGo/btnInput/imgKey")
    self._imgFreeBox = self:getGo("controlGo/btnInput/imgFreeBox")
    self._imgPick = self:getGo("controlGo/btnInput/imgPick")
    self._imgUpward = self:getGo("controlGo/btnInput/imgUpward")
    self._imgEmbrace = self:getGo("controlGo/btnInput/imgEmbrace")
    self._imgPlot = self:getGo("controlGo/btnInput/imgPlot")
    self._txtKeyCount = self:getText("controlGo/btnInput/imgKey/txtCount")

    self._btnSpeed = self:getBtn("controlGo/btnGo/btnSpeed")
    self._imgSpeedCD = self:getGo("controlGo/btnGo/btnSpeed/imgCD")
    self._txtSpeedCount = self:getText("controlGo/btnGo/btnSpeed/txtCount")

    self._imgGrayInput = self:getGo("controlGo/btnInput/imgGrayInput")
    self._imgGraySpeed = self:getGo("controlGo/btnGo/btnSpeed/imgGraySpeed")

    self._taskNpcGo = self:getGo("taskNpcGo")
    self._taskSlider = self:getSlider("taskNpcGo/taskSlider")

    self._speedCountDown = self._imgSpeedCD:GetComponent("Image")

    self._joystickGo = self:getGo("controlGo/joystickGo")
    self._joystickCancelGo = self:getGo("controlGo/joystickCancelGo")
    self._joystickBgGo = self:getGo("controlGo/joystickGo/joystickArea/joystickbackground")
    self._joystickCanvasGroup = self._joystickGo:GetComponent("CanvasGroup")

    self._virtualjoystick = goutil.addComponentOnce(self._joystickBgGo, typeof(VirtualJoystick))
    self._virtualjoystickctrl = goutil.addComponentOnce(self._joystickGo, typeof(ClickToShowJoystick))
    self._virtualjoystickctrl:SetFixed(true)
    SeaModel.instance.virtualjoystick = self._virtualjoystick
    SeaModel.instance.virtualjoystickcancel = self._joystickCancelGo

    goutil.setActive(self._listGo, false)
    goutil.setActive(self._itemGo, false)
    goutil.setActive(self._screenGo, false)
    goutil.setActive(self._joystickBgGo, true)
    goutil.setActive(self._joystickCancelGo, false)
    goutil.setActive(self._btnShowBag.gameObject, true)
    goutil.setActive(self._btnHideBag.gameObject, false)

    self._inputIcons = {
        [SeaEnum.Input.Key] = self._imgKey,
        [SeaEnum.Input.FreeBox] = self._imgFreeBox,
        [SeaEnum.Input.Pick] = self._imgPick,
        [SeaEnum.Input.Upward] = self._imgUpward,
        [SeaEnum.Input.Embrace] = self._imgEmbrace,
        [SeaEnum.Input.Plot] = self._imgPlot
    }

    if Framework.OSDef.isEditor then
        if self._virtualjoystick then
            self._virtualjoystick.isNotEnableKeyboard = true
        end
    end
    self._btnShowBag:AddClickListener(self._showBagList, self)
    self._btnHideBag:AddClickListener(self._hideBagList, self)
    self._btnBullet:AddClickListener(self._onBtnBullet, self)
    self._btnNoBullet:AddClickListener(self._onBtnNoBullet, self)
    self._btnInput:AddClickListener(self._onBtnInput, self)
    self._btnSpeed:AddClickListener(self._onBtnSpeed, self)

    local screenUITrigger = Framework.UIClickTrigger.Get(self._screenGo)
    screenUITrigger:AddClickDownListener(self._hideBagList, self)

    local banJoystickUITrigger = Framework.UIClickTrigger.Get(self._banJoystickGo)
    banJoystickUITrigger:AddClickDownListener(self._showNoBulletTips, self)

    self._inputTrigger = Framework.UIClickTrigger.Get(self._btnInput.gameObject)
    self._inputTrigger:AddClickDownListener(self._onInputDown, self)
    self._inputTrigger:AddClickUpListener(self._onInputUp, self)
    self._inputTrigger:SetMultiClickEnable(true)

    Framework.ButtonAdapter.GetFrom(self.mainGO, "controlGo/btnGo/btnSpeed"):SetMultiClickEnable(true)
    Framework.ButtonAdapter.GetFrom(self.mainGO, "controlGo/btnInput"):SetMultiClickEnable(true)
    ClockMgr.instance:addListener(ClockMgr.TickHour, self._checkRefreshHour, self)
    IconLoader.setIconForItem(self._imgKey, SeaConst.keyItemId)


end

function SeaMainView:onEnter()
    self.mainGO.transform:SetAsFirstSibling()
    self:registerLocalNotify(SeaNotify.onMainShow, self._onShow, self)
    self:registerLocalNotify(SeaNotify.onMainHide, self._onHide, self)
    self:registerLocalNotify(SeaNotify.onUpdateInput, self._onUpdateInput, self)
    self:registerLocalNotify(SeaNotify.onRescueGuide, self._onRescueGuide, self)
    self:registerLocalNotify(SeaNotify.onAimInterrupt, self._onStopJoystickMove, self)
    GlobalDispatcher:addListener(GlobalNotify.StartLoadScene, self._onStopJoystickMove, self)
    GlobalDispatcher:addListener(GlobalNotify.ItemChange, self._onItemChange, self)
    self._virtualjoystick:AddDownListener(self._onJoystickDown, self)
    self._virtualjoystick:AddUpListener(self._onJoystickUp, self)
    DownTabController.instance:registerButton(self._controlGo, handler(function(isActive)
        self._controlGo:SetActive(isActive)
    end), DownRightButtonWeightDefine.Button_Lowest)
    DownTabController.instance:tryChangeButtonActive(self._controlGo, true)
    self:_updateView()
end

function SeaMainView:onExit()
    self:unregisterLocalNotify(SeaNotify.onMainShow, self._onShow, self)
    self:unregisterLocalNotify(SeaNotify.onMainHide, self._onHide, self)
    self:unregisterLocalNotify(SeaNotify.onUpdateInput, self._onUpdateInput, self)
    self:unregisterLocalNotify(SeaNotify.onRescueGuide, self._onRescueGuide, self)
    self:unregisterLocalNotify(SeaNotify.onAimInterrupt, self._onStopJoystickMove, self)
    GlobalDispatcher:removeListener(GlobalNotify.StartLoadScene, self._onStopJoystickMove, self)
    GlobalDispatcher:removeListener(GlobalNotify.ItemChange, self._onItemChange, self)
    self._virtualjoystick:RemoveDownListener()
    self._virtualjoystick:RemoveUpListener()
    DownTabController.instance:unregisterButton(self._controlGo)
    SceneTimer:removeTimer(self.checkTimer, self)
end

function SeaMainView:unbindEvents()
    ClockMgr.instance:removeListener(ClockMgr.TickHour, self._checkRefreshHour, self)
end

function SeaMainView:onEnterFinished()
    UpdateBeat:Add(self.onUpdateBeat, self)
end

function SeaMainView:onExitFinished()
    UpdateBeat:Remove(self.onUpdateBeat, self)
end

function SeaMainView:onUpdateBeat()
    self:_updateSpeed()
    self:_updateTask()
    self:_checkActivity()
end

function SeaMainView:_updateView()
    local bulletId = SeaController.instance:getBulletId()
    if bulletId > 0 then
        if not ItemService.instance:hasItem(bulletId) then
            self:_changeBullet()
        end
    else
        self:_changeBullet()
    end
    self:_updateItem()
    self:_updateInput()
    self:_updateBullet()
    self:_checkRefreshHour()
    self:_onRescueGuide(false)

end

function SeaMainView:_updateItem()
    local keyItemNums = ItemService.instance:getItemNum(SeaConst.keyItemId)
    self._txtKeyCount.text = keyItemNums

    local speedItemNums = ItemService.instance:getItemNum(SeaConst.speedItemId)
    self._txtSpeedCount.text = speedItemNums

    if speedItemNums > 0 then
        self._btnSpeed.btn.interactable = true
        self._imgGraySpeed:SetActive(false)
    else
        self._btnSpeed.btn.interactable = false
        self._imgGraySpeed:SetActive(true)
    end
end

function SeaMainView:_updateInput()
    local inputInfo = SeaModel.instance:getInputInfo()
    local inputType = inputInfo and inputInfo.type
    if inputType ~= nil then
        for type, inputIcon in ipairs(self._inputIcons) do
            inputIcon:SetActive(inputType == type)
        end
        self._btnInput.gameObject:SetActive(true)
    else
        self._btnInput.gameObject:SetActive(false)
        self:_onInputUp()
    end
end

function SeaMainView:_updateSpeed()
    local player = SceneManager.instance:getCurScene():getUserPlayer()
    if player then
        local isHasState = player.seaState:hasState(SeaEnum.State.Speed)
        if isHasState then
            local profitItemDefine = SeaConfig.getProfitItem(SeaConst.speedItemId)
            local effectTime = profitItemDefine.effectTime

            local state = player.seaState.states[SeaEnum.State.Speed]
            local endTime = state.endTime

            local now = ServerTime.now()
            if self._current == nil then
                self._current = {
                    now = now,
                    delta = 0
                }
            else
                if self._current.now ~= now then
                    self._current.now = now
                    self._current.delta = 0
                end
            end
            self._current.delta = self._current.delta + Time.deltaTime

            local totalTime = SeaTechTreeController.instance:getBuffValue(SeaEnum.Buff.SpeedBuffTimeUp, effectTime)
            local leftTime = endTime - (self._current.now + self._current.delta)
            local leftFactor = leftTime / totalTime
            self._speedCountDown.fillAmount = leftFactor
        else
            self._speedCountDown.fillAmount = 0
        end
    end
end

function SeaMainView:_updateTask()
    if self._isInputDown == true then
        if self._taskSlider:GetValue() >= 1 then
            self:_onTaskUpdate()
        else
            if self.rescueSpeed then
                self._taskSlider:SetValue(self._taskSlider:GetValue() + self.rescueSpeed * Time.deltaTime)
            end
        end
    end
end

function SeaMainView:_updateBullet()
    local bulletId = SeaController.instance:getBulletId()
    local isHasBullet = bulletId > 0
    if isHasBullet then
        IconLoader.setIconForItem(self._imgBulletIcon, bulletId)
        self._txtBulletNum.text = ItemService.instance:getItemNum(bulletId)
    end
    goutil.setActive(self._banJoystickGo, not isHasBullet)
    goutil.setActive(self._btnBullet.gameObject, isHasBullet)
    goutil.setActive(self._btnNoBullet.gameObject, not isHasBullet)
    self._joystickCanvasGroup.alpha = isHasBullet and 1 or 0.5
end

function SeaMainView:_changeBullet()
    local list = SeaController.instance:getBulletList()
    local bulletId = #list > 0 and list[1].id or 0
    SeaController.instance:setBulletId(bulletId)
end

function SeaMainView:_showBagList()
    local list = SeaController.instance:getBagList()
    goutil.setActive(self._listView, #list > 0)
    goutil.setActive(self._txtNothing, #list == 0)
    if #list > 0 then
        for i, define in ipairs(list) do
            local itemDefine = SeaConfig.getItem(define.id)
            local item = self._items[i]
            if not item then
                item = goutil.clone(self._itemGo, tostring(i))
                goutil.addChildToParent(item, self._contentGo)
                self._items[i] = item
            end
            Framework.UIClickTrigger.Get(item):AddClickListener(function()
                if itemDefine.type == GameEnum.SeaSceneItemType.BULLET then
                    SeaController.instance:setBulletId(define.id)
                    self:_updateBullet()
                end
            end)
            local imgIcon = goutil.findChild(item, "imgIcon")
            local imgLine = goutil.findChild(item, "imgLine")
            local img_1 = goutil.findChild(item, "img_1")
            local img_2 = goutil.findChild(item, "img_2")
            local txtName = goutil.findChildTextComponent(item, "txtGo/txtName")
            local txtDesc = goutil.findChildTextComponent(item, "txtGo/txtDesc")
            local txtNum = goutil.findChildTextComponent(item, "txtNum")
            txtName.text = ItemService.instance:getName(define.id)
            txtNum.text = ItemService.instance:getItemNum(define.id)
            goutil.setActive(txtDesc.gameObject, false)
            IconLoader.setIconForItem(imgIcon, define.id)
            goutil.setActive(imgLine, i < #list)
            goutil.setActive(img_1, itemDefine.type == GameEnum.SeaSceneItemType.BULLET)
            goutil.setActive(img_2, itemDefine.type ~= GameEnum.SeaSceneItemType.BULLET)
            goutil.setActive(item, true)
        end
        for i = #list + 1, #self._items do
            goutil.setActive(self._items[i], false)
        end
    end
    goutil.setActive(self._listGo, true)
    goutil.setActive(self._screenGo, true)
    goutil.setActive(self._btnShowBag.gameObject, false)
    goutil.setActive(self._btnHideBag.gameObject, true)
end

function SeaMainView:_hideBagList()
    goutil.setActive(self._listGo, false)
    goutil.setActive(self._screenGo, false)
    goutil.setActive(self._btnShowBag.gameObject, true)
    goutil.setActive(self._btnHideBag.gameObject, false)
end

function SeaMainView:_showNoBulletTips()
    DialogHelper.showConfirmDlg(lang("捕鱼球不足，\n是否前往工坊制作"), function(isOK)
        if isOK then
            ViewMgr.instance:open("WorkshopPanel", WorkshopSetting.OpenState.AppointWorkShop, WorkshopSetting.ShopType.JingGong)
        end
    end)
end

function SeaMainView:_showNoKeyTips(name, num)
    DialogHelper.showMsg(lang("开启宝箱消耗{1}道具{2}个\n数量不足，无法打开宝箱\n（捕捉鱼王掉落宝箱钥匙）", name, num))
end

function SeaMainView:_onShow()
    print("SeaMainView: show")
    goutil.setActive(self.mainGO, true)
end

function SeaMainView:_onHide()
    print("SeaMainView: hide")
    goutil.setActive(self.mainGO, false)
end

function SeaMainView:_onUpdateInput()
    self:_updateInput()
end

function SeaMainView:_onItemChange()
    local bulletId = SeaController.instance:getBulletId()
    if bulletId > 0 then
        if not ItemService.instance:hasItem(bulletId) then
            self:_changeBullet()
        end
    else
        self:_changeBullet()
    end
    self:_updateItem()
    self:_updateInput()
    self:_updateBullet()
end

function SeaMainView:_onJoystickDown()
    SeaController.instance:localNotify(SeaNotify.onJoystickDown)
    goutil.setActive(self._joystickBgGo, true)
    goutil.setActive(self._joystickCancelGo, true)
end

function SeaMainView:_onJoystickUp()
    SeaController.instance:localNotify(SeaNotify.onJoystickUp)
    goutil.setActive(self._joystickBgGo, true)
    goutil.setActive(self._joystickCancelGo, false)
end

function SeaMainView:_onStopJoystickMove()
    self._virtualjoystickctrl:StopJoystickMove()
end

function SeaMainView:_onBtnBullet()
    self:_changeBullet()
    self:_updateBullet()
end

function SeaMainView:_onBtnNoBullet()
    self:_showNoBulletTips()
end

function SeaMainView:_onBtnInput()
    local inputInfo = SeaModel.instance:getInputInfo()
    local type = inputInfo.type
    local params = inputInfo.params

    if type == SeaEnum.Input.Key then
        local num = ItemService.instance:getItemNum(params.openCost.id)
        if num < params.openCost.num then
            self:_showNoKeyTips(ItemService.instance:getName(params.openCost.id), params.openCost.num)
            return
        end
        local boxUnit = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SeaUnitType.Box, params.id)
        boxUnit:setDisappear()
        SceneTimer:setTimer(1.0, function()
            local scene = SceneManager.instance:getCurScene()
            local compBox = scene.entity:getComponent(SeaCompBox)
            compBox:openBox(params.boxId)
        end, self, false)
        SeaModel.instance:setInputInfo(nil)
        self:_updateInput()

    elseif type == SeaEnum.Input.FreeBox then
        local boxUnit = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SeaUnitType.Box, params.id)
        boxUnit:setDisappear()
        SceneTimer:setTimer(1.0, function()
            local scene = SceneManager.instance:getCurScene()
            local compBox = scene.entity:getComponent(SeaCompBox)
            compBox:openBox(params.boxId)
        end, self, false)
        SeaModel.instance:setInputInfo(nil)
        self:_updateInput()

    elseif type == SeaEnum.Input.Pick then
        SeaTaskController.instance:updateAutoTaskProgress(params.id)

    elseif type == SeaEnum.Input.Upward then
        SeaTaskController.instance:updateAutoTaskProgress(params.id)

    elseif type == SeaEnum.Input.Embrace then
        SeaTaskController.instance:updateTaskProgress(params.id)

    elseif type == SeaEnum.Input.Plot then
        SeaAgent.instance:sendSeaSceneCollectPlotRequest(params.id, function()
            SeaMap.instance:removePlotFragments(SeaFacade.getCurDepth(), params.id)
            local cfg = SeaConfig.getPlotCollection(params.id)
            ViewMgr.instance:open("SeaCloverView", cfg)
            local unit = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SeaUnitType.PlotFrag, params.id)
            unit:onDisappear()
            SeaModel.instance:setInputInfo(nil)
            self:_updateInput()
        end)
    end
end

function SeaMainView:_onBtnSpeed()
    SeaAgent.instance:sendSeaSceneUseProfitItemRequest(SeaConst.speedItemId,function()
        SoundManager.instance:playEffect(SeaSound.SpeedUp)
    end)
end

function SeaMainView:_onInputDown()
    local task = SeaTaskModel.instance:getTaskInfo(SeaFacade.getCurDepth())
    local inputInfo = SeaModel.instance:getInputInfo()
    if inputInfo == nil or task == nil then
        return
    end
    local type = inputInfo.type
    local params = inputInfo.params
    if type == SeaEnum.Input.Key or
        type == SeaEnum.Input.FreeBox or
            type == SeaEnum.Input.Plot
    then
        return
    end
    local taskParams = SeaConfig.getTaskParams(task.taskId)
    if taskParams.isLongPress == false then
        return
    end
    SoundManager.instance:playEffect(SeaSound.Rescue)
    self._inputDownTime = Time.realtimeSinceStartup
    self._isInputDown = true
    if taskParams then
        self.rescueSpeed = tonumber(taskParams.params.rescueSpeed)
        if self.rescueSpeed then
            self._taskSlider.gameObject:SetActive(true)
            self._taskSlider:SetValue(0)
            local unit = SceneManager.instance:getCurScene():getUserPlayer()
            unit:playAnimation(taskParams.params.myAdmin3, true)
        end
        local taskUnit = SceneManager.instance:getCurScene():getUnitMgr():getUnit(inputInfo.params.unitType, params.id)
        if taskUnit ~= nil then
            local x, y, z = taskUnit:getPos()
            GameUtils.setWorldTo2DCameraPos(self._taskNpcGo, gvec3(x, y + 0.5, 0))
        end
    end
    SeaController.instance:localNotify(SeaNotify.onShowTaskProcessGo,params.id)
end

function SeaMainView:_onInputUp()
    if self._isInputDown == true then
        local id = 0
        local inputInfo = SeaModel.instance:getInputInfo()
        if inputInfo  then
            local params = inputInfo.params
            id = params.id
        end
        self._inputDownTime = nil
        self._isInputDown = false
        self._taskSlider.gameObject:SetActive(false)
        local unit = SceneManager.instance:getCurScene():getUserPlayer()
        unit:playAnimation(unit:getIdleAniName(), true)
        SeaController.instance:localNotify(SeaNotify.onHideTaskProcessGo,id)
    end
    SoundManager.instance:stopEffect(SeaSound.Rescue)
end

function SeaMainView:_onTaskUpdate()
    local inputInfo = SeaModel.instance:getInputInfo()
    local params = inputInfo.params
    self._inputDownTime = nil
    self._isInputDown = false
    self._taskSlider.gameObject:SetActive(false)
    local unit = SceneManager.instance:getCurScene():getUserPlayer()
    unit:playAnimation(unit:getIdleAniName(), true)
    SeaModel.instance:setInputInfo(nil)
    self:_updateInput()
    SeaTaskController.instance:updateAutoTaskProgress(params.id)
end

function SeaMainView:_onRescueGuide(value)
    self._taskGuideGo:SetActive(value)
end


--4点到五点提示
function SeaMainView:_checkRefreshHour()
    if ServerTime.nowDateServerLook().hour >= 4 and ServerTime.nowDateServerLook().hour < 5 then
        --
        SceneTimer:removeTimer(self.checkTimer, self)
        SceneTimer:setTimer(1, self.checkTimer, self,true)
    else
        if self.isShowCloseTip then
            SceneTimer:removeTimer(self.checkTimer, self)
            self.isShowCloseTip = false
            self._tipsGo:SetActive(false)
        end
    end
end


--TimeUtil.checkOpenTime
function SeaMainView:checkTimer()
    self.isShowCloseTip = true
    local timeStr = CommonConfig.getValue("sea_scene_common", "ShowOverNightTipTime")
    local dt = os.date("*t", ServerTime.now() + ServerTime._clientToServerOffset)
    local openTimeStrArr = string.split(timeStr,"~")
    local openTime = string.splitToNumber(openTimeStrArr[1], ":")
    local closeTime = string.splitToNumber(openTimeStrArr[2], ":")
    local open = openTime[1] * 3600 + openTime[2] * 60
    local close = closeTime[1] * 3600 + closeTime[2] * 60
    local now = dt.hour * 3600 + dt.min * 60 + dt.sec
    --return open <= now and now <= close
    if open <= now and now <= close then
        self._tipsGo:SetActive(true)
        self._tipsTxtGo.text = lang("海底跨天踢回主城",close - now)
    elseif now >= close then
        self._tipsTxtGo.text = ""
        self._tipsGo:SetActive(false)
        self.isShowCloseTip = false
        SceneTimer:removeTimer(self.checkTimer, self)
    end
end

function SeaMainView:_checkActivity()
    if SceneManager.instance:getCurSceneId() ~= 1401 then
        if self._bossTipsGo.activeSelf == true then
            self._bossTipsGo:SetActive(false)
        end
        return
    end
    local isShow = ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_433)
    if isShow == true and SceneManager.instance:getCurScene().actionMgr:hasAction(SceneActionType.Track) then
        local endTime = ActivityModel.instance:getActivityInfoEndTime(GameEnum.ActivityEnum.ACTIVITY_433)
        local timeLength = endTime - ServerTime.now()
        if timeLength <= 300 then
            self._bossTipsTxt.text = lang("海底观光剩余时间", TimeUtil.second2TimeString(timeLength))
            if self._bossTipsGo.activeSelf == false then
                self._bossTipsGo:SetActive(true)
            end
        else
            if self._bossTipsGo.activeSelf == true then
                self._bossTipsGo:SetActive(false)
            end
        end

    else
        if self._bossTipsGo.activeSelf == true then
            self._bossTipsGo:SetActive(false)
        end
    end
end




return SeaMainView
