-- {excel:W外部分享.xlsx, sheetName:export_外部分享}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_external_share", package.seeall)

local title = {shareId=1,totalTimes=2,dayTimes=3,tips=4,style=5,beforeOpenService=6,desc=7,reward=8,showing=9,closing=10,settings=11}

local dataList = {
	{1, 0, 1, "每日分享可得", 1, 0, "我的第<color=#e38534>{1}</color>套服装!!", nil, true, false, nil},
	{2, 0, 1, "每日分享可得", 1, 0, "我的第<color=#e38534>{1}</color>套家具!!", nil, true, false, nil},
	{3, 0, 1, "每日分享可得", 1, 0, "我的第<color=#e38534>{1}</color>个小屋!!", nil, true, false, nil},
	{4, 0, 1, "每日分享可得", 2, 0, "我的第<color=#e38534>{1}</color>只宠物!!", nil, true, false, nil},
	{5, 0, 1, "每日分享可得", 2, 0, "", nil, true, false, nil},
	{6, 0, 1, "每日分享可得", 1, 0, "快来看我的妙手砸蛋，又收获了藏宝屋的新宝藏！", {{count=10,id=3}}, true, false, nil},
	{7, 0, 1, "每日分享可得", 2, 0, "", nil, true, false, nil},
	{8, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{9, 0, 1, "每日分享可得", 2, 0, "", nil, true, false, nil},
	{10, 0, 1, "每日分享可得", 1, 0, "跨越次元相见，共度美好周年。", {{count=10,id=3}}, true, false, {image="2025_40_lingganqiyuan"}},
	{11, 1, 0, "首次分享可得", 2, 1, "", {{count=5000,id=1}}, true, false, {image="yuchuangjuefenxiang"}},
	{12, 0, 1, "每日分享可得", 2, 0, "", nil, true, false, {}},
	{13, 1, 0, "首次分享可得", 2, 0, "", {{count=100,id=3}}, true, false, nil},
	{14, 0, 1, "每日分享可得", 2, 0, "", nil, true, false, nil},
	{15, 1, 0, "首次分享可得", 2, 1, "", nil, false, true, {image="cre_bg"}},
	{16, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{17, 0, 1, "每日分享可得", 2, 0, "", {{count=100,id=3}}, true, false, nil},
	{18, 1, 0, "首次分享可得", 2, 0, "", {{count=100,id=3}}, true, false, nil},
	{19, 1, 0, "首次分享可得", 2, 0, "", {{count=100,id=3}}, true, false, nil},
	{20, 1, 0, "首次分享可得", 2, 0, "", {{count=100,id=3}}, true, false, nil},
	{21, 0, 1, "每日分享可得", 1, 0, "得到花儿的祝福，运气满满！", {{count=10,id=3}}, true, false, nil},
	{22, 0, 1, "每日分享可得", 2, 0, "绑定我的专属邀请码，和我一起前往《奥比岛·梦想国度》，开启专属好礼！", {{count=20,id=3}}, false, false, nil},
	{23, 0, 1, "每日分享可得", 1, 0, "甜蜜加持，拥有甜点王国不是梦！", {{count=10,id=3}}, true, false, nil},
	{24, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{25, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{26, 0, 0, "每日分享可得", 1, 0, "恭喜！您已将本期彩蛋奖励全部收集完毕！", nil, true, false, nil},
	{27, 1, 0, "首次分享可得", 1, 0, "一起来游仙记~！", {{count=50,id=3}}, true, false, {image="yxzl_fenxianghaibao"}},
	{28, 1, 0, "首次分享可得", 2, 0, "在奥比岛庆生~！", {{count=5,id=2}}, true, false, nil},
	{29, 0, 1, "每日分享可得", 1, 0, "来奥比岛集游仙卡，得福利奖励！", {{count=1,id=16000269}}, true, false, {image="yxz_fenxianghaibao"}},
	{30, 8, 0, "首次分享可得", 1, 0, "看我运气旺盛，又开出了新游仙卡！", {{count=10,id=3}}, true, false, nil},
	{31, 1, 0, "首次分享可得", 1, 0, "参与游园收集拼图盖章，获珍贵奖励！", {{count=1,id=16000006}}, true, false, {image="yyjz_15"}},
	{32, 0, 1, "每日分享可得", 2, 0, "一起看绚烂烟花，度过浪漫热闹的周年庆吧！", {{count=10,id=3}}, true, false, {orign=true}},
	{33, 1, 0, "首次分享可得", 1, 0, "奥比献爱心，公益助萌宠！", {{count=10,id=3}}, true, false, nil},
	{34, 1, 0, "首次分享可得", 1, 0, "我的一份爱心，为流浪生命搭起暖心屋！", nil, true, false, nil},
	{35, 0, 1, "每日分享可得", 1, 0, "如果在料理中加入南瓜和月光，会是什么味道呢？", {{count=10,id=3}}, true, false, nil},
	{36, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{37, 0, 0, "首次分享可得", 2, 0, "", nil, true, false, nil},
	{38, 0, 0, "首次分享可得", 1, 0, "和老朋友的相聚总是那么让人开心!", nil, true, false, nil},
	{39, 0, 0, "首次分享可得", 1, 0, "嗨呀~你的好友回来啦!", nil, true, false, nil},
	{40, 0, 1, "每日分享可得", 1, 0, "做最闪亮的奥比吧！", {{count=10,id=3}}, true, false, nil},
	{41, 1, 0, "首次分享可得", 1, 0, "来奥比岛解锁生活日记，获丰厚福利！", {{count=10,id=3}}, true, false, nil},
	{42, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, {orign=true}},
	{43, 1, 0, "首次分享可得", 1, 0, "来奥比岛解锁生活日记，获丰厚福利！", {{count=10,id=3}}, true, false, nil},
	{44, 0, 1, "每日分享可得", 1, 0, "来奥比岛参与EMMA联动游园活动，获丰厚奖励！", {{count=10,id=3}}, true, false, nil},
	{45, 0, 1, "每日分享可得", 1, 0, "满月代表团圆，而玉兔觉得圆形的东西都是月亮。", {{count=10,id=3}}, true, false, nil},
	{46, 0, 1, "每日分享可得", 1, 0, "我是世界的丑角,一个名为小丑的人..", {{count=10,id=3}}, true, false, {}},
	{47, 1, 0, "首次分享可得", 2, 0, "追寻歌声的足迹，踏上闪耀的舞台。", {{count=10,id=3}}, true, false, nil},
	{48, 0, 1, "每日分享可得", 1, 0, "奥比齐聚甜蜜派对，快乐心愿闪耀成真！", {{count=10,id=3}}, true, false, nil},
	{49, 1, 0, "首次分享可得", 1, 0, "来奥比岛解锁生活日记，获丰厚福利！", {{count=10,id=3}}, true, false, nil},
	{50, 0, 1, "每日分享可得", 1, 0, "与你共赏浪漫的郁金香花海。", {{count=10,id=3}}, true, false, {image="2022_14_yuxiangqiyuan"}},
	{51, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{52, 0, 1, "每日分享可得", 1, 0, "来奥比岛，跟喜羊羊一起喜洋洋！", {{count=10,id=3}}, true, false, {image="xyy_zy"}},
	{54, 0, 1, "每日分享可得", 1, 0, "哎呀，蛋糕还没做好，怎么办怎么办？", {{count=10,id=3}}, true, false, nil},
	{55, 0, 1, "每日分享可得", 1, 0, "终于做好啦，快来品尝一下吧！", {{count=20,id=16000351}}, true, false, nil},
	{56, 0, 1, "每日分享可得", 1, 0, "好想拥有一只打快板的小祥龙，快帮帮我吧！", nil, true, false, nil},
	{57, 0, 1, "每日分享可得", 1, 0, "竹板响连天，幸福过新年！", nil, true, false, nil},
	{58, 0, 1, "每日分享可得", 1, 0, "呜呜，材料也不够了，有谁可以帮帮我？", {{count=10,id=3}}, true, false, nil},
	{59, 0, 1, "每日分享可得", 1, 0, "呼~终于赶上了，快来试穿一下我为您做的衣服吧！", {{count=20,id=16000351}}, true, false, nil},
	{60, 1, 0, "首次分享可得", 1, 0, "一切就跟随风的指引吧...", {{count=20,id=16000351}}, true, false, nil},
	{61, 1, 0, "首次分享可得", 1, 0, "一起来看海，一起听海风的低语。", {{count=10,id=3}}, true, false, nil},
	{62, 0, 1, "每日分享可得1个", 1, 0, "收集巴啦啦法宝卡，变身小魔仙吧！", {{count=1,id=16000636}}, true, false, nil},
	{63, 8, 0, "首次分享可得", 1, 0, "看我运气旺盛，又开出了新法宝卡！", {{count=10,id=3}}, true, false, nil},
	{64, 1, 0, "首次分享可得", 1, 0, "法宝卡全收集达成！", {{count=10,id=3}}, true, false, nil},
	{65, 0, 1, "每日分享可得", 1, 0, "终于做好美味的料理啦，快来品尝一下吧！", {{count=10,id=3}}, true, false, nil},
	{1001, 0, 1, "每日分享可得", 1, 0, "翰墨妙笔绘祝福，丹青画卷庆风华！", {{count=10,id=3}}, true, false, nil},
	{1002, 0, 1, "每日分享可得", 1, 0, "追寻歌声的足迹，踏上闪耀的舞台。", {{count=10,id=3}}, true, false, nil},
	{1003, 0, 1, "每日分享可得", 1, 0, "奥比羊羊手牵手，共庆周年乐无忧", {{count=10,id=3}}, true, false, nil},
	{1004, 0, 1, "每日分享可得", 1, 0, "粉墨春秋芳华曲，千古永韵满人间。", {{count=10,id=3}}, true, false, nil},
	{1005, 0, 1, "每日分享可得", 1, 0, "玄墨点睛秘宝至，神龙下凡过大年！", {{count=10,id=3}}, true, false, nil},
	{1006, 0, 1, "每日分享可得", 1, 0, "已累计分发神龙秘宝{1}份！\n熊宝阔绰！熊宝大气！熊宝万岁！", {{count=10,id=3}}, true, false, nil},
	{1007, 0, 1, "每日分享可得", 1, 0, "星厨来岛美味到，小熊吃饱没烦恼！", {{count=10,id=3}}, true, false, nil},
	{1008, 0, 1, "每日分享可得", 1, 0, "小魔仙和小奥比，巴啦啦能量在这里！", {{count=10,id=3}}, true, false, nil},
	{1009, 0, 1, "每日分享可得", 1, 0, "已累计分发魔仙彩石{1}份！\n巴啦啦能量，奥比小魔仙真棒！", {{count=10,id=3}}, true, false, nil},
	{1010, 0, 1, "每日分享可得", 1, 0, "汇聚快乐因子，解救镜中王国。", {{count=10,id=3}}, true, false, nil},
	{1011, 0, 1, "每日分享可得", 1, 0, "来给予你新年的祝福！", {{count=10,id=3}}, true, false, nil},
	{1012, 0, 1, "每日分享可得", 1, 0, "已累计分发福宝{1}份！\n，奥比小蛇仙真棒！", {{count=10,id=3}}, true, false, nil},
	{1013, 0, 1, "每日分享可得", 1, 0, "奥比遇上调香师，快来邂逅香水奇闻！", {{count=10,id=3}}, true, false, nil},
	{1014, 0, 1, "每日分享可得", 1, 0, "牙齿刷够三分钟，美味糖果尽享用！", {{count=10,id=3}}, true, false, nil},
	{1015, 0, 1, "每日分享可得", 1, 0, "遨游宇宙，遍览群星！", {{count=10,id=3}}, true, false, nil},
	{1016, 0, 1, "每日分享可得", 1, 0, "已累计分发星星{1}份！\n，小奥比真棒！", {{count=10,id=3}}, true, false, nil},
	{66, 0, 1, "每日分享可得", 1, 0, "第109届星际大赛8月10日盛大开启！", {{count=10,id=3}}, true, false, {image="bg_xjdsxcy"}},
	{67, 1, 0, "首次分享可得", 1, 0, "在辽阔的舞台上，互相追逐。", {{count=10,id=3}}, true, false, nil},
	{68, 1, 0, "首次分享可得", 1, 0, "金风玉露一相逢，便胜却人间无数。", {{count=10,id=3}}, true, false, nil},
	{69, 0, 1, "每日分享可得", 1, 0, "期望在这片星空下，我能在这里默默的等待着你...", {{count=10,id=3}}, true, false, nil},
	{70, 1, 0, "首次分享可得", 1, 0, "想听听叶子小鸟们的狂欢大合唱吗？就想想吧！", {{count=10,id=3}}, true, false, nil},
	{71, 1, 0, "首次分享可得", 1, 0, "听说叶子狐狸喜欢叶子小兔，不知道尊嘟假嘟~", {{count=10,id=3}}, true, false, nil},
	{72, 1, 0, "首次分享可得", 1, 0, "看到叶子飞鼠的“回眸一笑”就知道叶子猫不在！", {{count=10,id=3}}, true, false, nil},
	{73, 1, 0, "首次分享可得", 1, 0, "15周年奥比庆典，快乐共舞！", {{count=1,id=18000094}}, true, false, {image="bg_15ty_anniv_03"}},
	{74, 1, 0, "首次分享可得", 1, 0, "缘分，来自那一种独特的微香！", {{count=10,id=3}}, true, false, nil},
	{75, 0, 1, "每日分享可得", 1, 0, "我和向日葵有个约定，每天向阳而生...", {{count=10,id=3}}, true, false, {}},
	{76, 1, 0, "首次分享可得", 1, 0, "生命不止，造物不息。", {{count=10,id=3}}, true, false, {image="bg_zwj_02"}},
	{77, 1, 0, "首次分享可得", 1, 0, "薯队长的旅游车已发车！", {{count=10,id=3}}, true, false, {image="xiaohongshu"}},
	{78, 0, 1, "每日分享可得", 1, 0, "御猫喵喵喵喵喵，奥比嗷嗷嗷嗷嗷——\n新年一起妙妙妙妙妙！", {{count=10,id=3}}, true, false, nil},
	{79, 1, 0, "首次分享可得", 1, 0, "守护熊熊们的公平与正义！", {{count=10,id=3}}, true, false, nil},
	{80, 0, 1, "每日分享可得", 1, 0, "漆黑的长发中染上了一丝银白，难道是和谁有关吗？", {{count=10,id=3}}, true, false, {image="2022_12_wuyingqiyuan"}},
	{81, 0, 1, "每日分享可得", 1, 0, "看我的好运气！在异世图书馆的限定书库里获得宝贝！", {{count=10,id=3}}, true, false, nil},
	{82, 6, 0, "首次分享可得", 1, 0, "开启月光宝盒，收集月亮的秘密！", {{count=10,id=3}}, true, false, nil},
	{83, 1, 0, "首次分享可得", 1, 0, "探索命运道路，聆听未来启示。", {{count=10,id=3}}, true, false, nil},
	{84, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{85, 0, 1, "每日分享可得", 1, 0, "“空灵的琴声，从遥远的海底传来……”", {{count=10,id=3}}, true, false, {}},
	{86, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, {shareHideBtns={1}}},
	{87, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, nil},
	{88, 1, 0, "首次分享可得", 1, 0, "世界广阔，而我们依然相遇。", {{count=10,id=3}}, true, false, nil},
	{89, 0, 1, "每日分享可得", 1, 0, "虽是微弱的光芒，却也温暖着被冰封的心……", {{count=10,id=3}}, true, false, {}},
	{90, 0, 1, "每日分享可得", 1, 0, "距离制作出能敲响幸运铃铛的锤子又近了一步！", {{count=10,id=3}}, true, false, nil},
	{91, 1, 0, "首次分享可得", 1, 0, "神殿的向往者们，接受我们的祝福吧。", {{count=10,id=3}}, true, false, nil},
	{92, 0, 1, "首次分享可得", 1, 0, "我是国宝守护人~", {{count=10,id=3}}, true, false, nil},
	{93, 1, 0, "首次分享可得", 1, 0, "与我们一起倾听水中的故事吧。", {{count=10,id=3}}, true, false, nil},
	{94, 0, 1, "每日分享可得", 1, 0, "金弦诛妖邪，青笔绘佳音。圆缺总有时，灯火映相思…", {{count=10,id=3}}, true, false, {image="2023_tnqy"}},
	{95, 1, 0, "首次分享可得", 1, 0, "海底的鱼儿也向往着绚烂的天空。", {{count=10,id=3}}, true, false, nil},
	{96, 0, 1, "每日分享可得", 1, 0, "“我愿意！”", {{count=10,id=3}}, true, false, {}},
	{97, 0, 1, "每日分享可得", 1, 0, "风带来了新的灵感。", {{count=10,id=3}}, true, false, nil},
	{98, 1, 0, "首次分享可得", 1, 0, "元气小羊，活力满满！", {{count=10,id=3}}, true, false, nil},
	{99, 1, 0, "首次分享可得", 1, 0, "穿越世纪尘埃，再现神庙建筑。", {{count=10,id=3}}, true, false, nil},
	{100, 0, 1, "每日分享可得", 1, 0, "穿越千年而来，月下奇游，金沙狂舞……", {{count=10,id=3}}, true, false, {image="2023_112_qiyouqiyuan"}},
	{101, 1, 0, "首次分享可得", 1, 0, "来吧，这些财宝都是你的。", {{count=10,id=3}}, true, false, nil},
	{102, 0, 1, "每日分享可得", 1, 0, "小熊娃娃与卫兵玩偶，陪伴了谁的童年时光……", {{count=10,id=3}}, true, false, {}},
	{103, 0, 1, "每日分享可得", 1, 0, "一起来庆祝丰收典礼，享受香甜食物吧！", {{count=10,id=3}}, true, false, {image="kuaileqiyuan"}},
	{104, 0, 1, "每日分享可得", 1, 0, "", {{count=10,id=3}}, true, false, nil},
	{105, 0, 1, "每日分享可得", 1, 0, "", {{count=10,id=3}}, true, false, nil},
	{106, 0, 1, "每日分享可得", 1, 0, "奥比月灵手牵手，月夜盛宴共巡游。", {{count=10,id=3}}, true, false, {image="fukeyuelingqiyuan"}},
	{107, 0, 1, "每日分享可得", 1, 0, "聆听跃动音符，绽放希望之花。", {{count=10,id=3}}, true, false, {image="wuyueqiyuan"}},
	{108, 1, 0, "首次分享可得", 1, 0, "期望与你，在美丽的乐园相遇。", {{count=10,id=3}}, true, false, nil},
	{2001, 0, 1, "每日分享可得", 1, 0, "米花旅行团来啦，快来结识新朋友吧！", {{count=10,id=3}}, true, false, nil},
	{2002, 0, 1, "每日分享可得", 1, 0, "找到了，米花旅行团的照片卡！", {{count=10,id=3}}, true, false, nil},
	{109, 0, 1, "每日分享可得", 1, 0, "终于做好美味的料理啦，快来品尝一下吧！", {{count=10,id=3}}, true, false, nil},
	{111, 0, 1, "每日分享可得", 1, 0, "呜呜，材料也不够了，有谁可以帮帮我？", {{count=10,id=3}}, true, false, {shareHideBtns={1,2,3}}},
	{112, 0, 1, "每日分享可得", 1, 0, "呼~终于赶上了，快来试穿一下我为您做的衣服吧！", {{count=20,id=8}}, true, false, {shareHideBtns={1,2,3}}},
	{114, 0, 1, "每日分享可得", 1, 0, "今年的周年蛋糕有异次元风味哦！", {{count=10,id=3}}, true, false, {shareHideBtns={1,2,3}}},
	{115, 0, 1, "每日分享可得", 1, 0, "异次元风味的蛋糕做好啦，快来品尝吧！", {{count=20,id=8}}, true, false, {shareHideBtns={1,2,3}}},
	{116, 0, 1, "每日分享可得", 2, 0, "", {{count=10,id=3}}, true, false, {orign=true}},
	{117, 0, 1, "每日分享可得", 1, 0, "好想拥有这把缤纷沙滩伞，快帮帮我吧！", nil, true, false, nil},
	{118, 0, 1, "每日分享可得", 1, 0, "和小精灵一起前往海滩度假啦！", nil, true, false, nil},
	{119, 0, 1, "每日分享可得", 1, 0, "巴啦啦魔仙送好礼，现金红包大派送！", {{count=10,id=3}}, true, false, nil},
	{120, 1, 0, "首次分享可得", 1, 0, "与小熊一起，探究世界的真理吧。", {{count=10,id=3}}, true, false, nil},
	{121, 0, 1, "每日分享可得", 1, 0, "不同颜色汇聚，组成缤纷的美好世界！", {{count=10,id=3}}, true, false, {image="2023_20_xuancaiqiyuan"}},
	{122, 0, 1, "每日分享可得", 1, 0, "第110届星际大赛8月2日盛大开启！", {{count=10,id=3}}, true, false, {image="bg_31xjxc_02"}},
	{123, 1, 0, "首次分享可得", 1, 0, "透过牌面，似乎能窥见那古老辉煌的文明……", {{count=10,id=3}}, true, false, nil},
	{124, 0, 1, "每日分享可得", 1, 0, "化身萤火之光，照亮浩瀚宇宙。", {{count=10,id=3}}, true, false, {image="xingjiqiyuan"}},
	{125, 1, 0, "首次分享可得", 1, 0, "聆听内心声音，追求正义之光。", {{count=10,id=3}}, true, false, nil},
	{126, 1, 0, "首次分享可得", 1, 0, "16周年奥比庆典，快乐无忧！", {{count=1,id=18000159}}, true, false, {image="bg_16ty_anniv_03"}},
	{127, 1, 0, "首次分享可得", 1, 0, "智者倒看世界，愚者享受自由。", {{count=10,id=3}}, true, false, nil},
	{128, 1, 0, "首次分享可得", 1, 0, "看！这都是我收集到的新装扮！", {{count=10,id=3}}, true, false, nil},
	{129, 1, 0, "首次分享可得", 1, 0, "来吧，与我签订契约吧，你会得到想要的。", {{count=10,id=3}}, true, false, nil},
	{130, 1, 0, "首次分享可得", 1, 0, "聆听审判之音，穿越迷失梦境。", {{count=10,id=3}}, true, false, nil},
	{131, 1, 0, "首次分享可得", 1, 0, "星途智引，引领你走出迷雾。", {{count=10,id=3}}, true, false, nil},
	{132, 0, 1, "每日分享可得", 1, 0, "跨越天与地的距离，点亮星与雪的约定。", {{count=10,id=3}}, true, false, {image="xingzhiqiyuan"}},
	{133, 0, 1, "每日分享可得", 1, 0, "神明落入奥比岛，中二少年多烦恼。", {{count=10,id=3}}, true, false, {image="guangyingqiyuan"}},
	{134, 0, 1, "每日分享可得", 1, 0, "来给予你新年的祝福！", {{count=10,id=3}}, true, false, nil},
	{135, 1, 0, "首次分享可得", 1, 0, "晨曦照耀，万物生长，辉映新生希望。", {{count=10,id=3}}, true, false, nil},
	{136, 0, 1, "每日分享可得", 1, 0, "跨越次元壁，共享奇幻派对！", {{count=10,id=3}}, true, false, nil},
	{137, 1, 0, "首次分享可得", 1, 0, "时光暖辉，春意永驻，温暖每一刻。", {{count=10,id=3}}, true, false, nil},
	{138, 0, 1, "每日分享可得", 1, 0, "小蝴蝶扇动翅膀，永生花跌入春天。", {{count=10,id=3}}, true, false, {image="2024_28_chunriqiyuan"}},
	{139, 1, 0, "首次分享可得", 1, 0, "月辉洒落，划破迷雾，照亮真相。", {{count=10,id=3}}, true, false, nil},
	{140, 0, 1, "每日分享可得", 1, 0, "时空交汇之处，万象融合之所", {{count=10,id=3}}, true, false, {image="2024_29_chengshiqiyuan"}},
	{141, 1, 0, "首次分享可得", 1, 0, "学会掌控力量才是真正的强大。", {{count=10,id=3}}, true, false, nil},
	{142, 0, 1, "每日分享可得", 1, 0, "登录游戏亲身体验，剑与魔法奇幻冒险！", {{count=10,id=3}}, true, false, {image="2024_210_maoxianzhilv"}},
	{143, 1, 0, "首次分享可得", 1, 0, "扭曲的执念，终将在自由旋律中燃烧成灰。", {{count=10,id=3}}, true, false, nil},
	{144, 1, 0, "首次分享可得", 1, 0, "为旅途画上充满欢声笑语的句号。", {{count=10,id=3}}, true, false, nil},
	{145, 1, 0, "首次分享可得", 1, 0, "航线千万条，快乐第一条。成为最强机长，前往快乐次元！", {{count=10,id=3}}, true, false, nil},
	{146, 0, 1, "每日分享可得", 1, 0, "快来看看留下的周年祝福吧~", {{count=10,id=3}}, true, false, nil},
	{147, 0, 1, "每日分享可得", 1, 0, "第111届星际大赛7月31日盛大开启！", {{count=10,id=3}}, true, false, {image="bg_31xjxc_02"}},
	{148, 1, 0, "首次分享可得", 1, 0, "飘落的雨丝，荡起的衣摆，共同编织出一首欢快的乐曲。", {{count=10,id=3}}, true, false, nil},
	{149, 0, 1, "每日分享可得", 1, 0, "鲸与神同心相连，海与陆共庆周年。", {{count=10,id=3}}, true, false, {image="2024_30_huanhaiqiyuan"}},
}

local t_external_share = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
	[9] = dataList[9],
	[10] = dataList[10],
	[11] = dataList[11],
	[12] = dataList[12],
	[13] = dataList[13],
	[14] = dataList[14],
	[15] = dataList[15],
	[16] = dataList[16],
	[17] = dataList[17],
	[18] = dataList[18],
	[19] = dataList[19],
	[20] = dataList[20],
	[21] = dataList[21],
	[22] = dataList[22],
	[23] = dataList[23],
	[24] = dataList[24],
	[25] = dataList[25],
	[26] = dataList[26],
	[27] = dataList[27],
	[28] = dataList[28],
	[29] = dataList[29],
	[30] = dataList[30],
	[31] = dataList[31],
	[32] = dataList[32],
	[33] = dataList[33],
	[34] = dataList[34],
	[35] = dataList[35],
	[36] = dataList[36],
	[37] = dataList[37],
	[38] = dataList[38],
	[39] = dataList[39],
	[40] = dataList[40],
	[41] = dataList[41],
	[42] = dataList[42],
	[43] = dataList[43],
	[44] = dataList[44],
	[45] = dataList[45],
	[46] = dataList[46],
	[47] = dataList[47],
	[48] = dataList[48],
	[49] = dataList[49],
	[50] = dataList[50],
	[51] = dataList[51],
	[52] = dataList[52],
	[54] = dataList[53],
	[55] = dataList[54],
	[56] = dataList[55],
	[57] = dataList[56],
	[58] = dataList[57],
	[59] = dataList[58],
	[60] = dataList[59],
	[61] = dataList[60],
	[62] = dataList[61],
	[63] = dataList[62],
	[64] = dataList[63],
	[65] = dataList[64],
	[1001] = dataList[65],
	[1002] = dataList[66],
	[1003] = dataList[67],
	[1004] = dataList[68],
	[1005] = dataList[69],
	[1006] = dataList[70],
	[1007] = dataList[71],
	[1008] = dataList[72],
	[1009] = dataList[73],
	[1010] = dataList[74],
	[1011] = dataList[75],
	[1012] = dataList[76],
	[1013] = dataList[77],
	[1014] = dataList[78],
	[1015] = dataList[79],
	[1016] = dataList[80],
	[66] = dataList[81],
	[67] = dataList[82],
	[68] = dataList[83],
	[69] = dataList[84],
	[70] = dataList[85],
	[71] = dataList[86],
	[72] = dataList[87],
	[73] = dataList[88],
	[74] = dataList[89],
	[75] = dataList[90],
	[76] = dataList[91],
	[77] = dataList[92],
	[78] = dataList[93],
	[79] = dataList[94],
	[80] = dataList[95],
	[81] = dataList[96],
	[82] = dataList[97],
	[83] = dataList[98],
	[84] = dataList[99],
	[85] = dataList[100],
	[86] = dataList[101],
	[87] = dataList[102],
	[88] = dataList[103],
	[89] = dataList[104],
	[90] = dataList[105],
	[91] = dataList[106],
	[92] = dataList[107],
	[93] = dataList[108],
	[94] = dataList[109],
	[95] = dataList[110],
	[96] = dataList[111],
	[97] = dataList[112],
	[98] = dataList[113],
	[99] = dataList[114],
	[100] = dataList[115],
	[101] = dataList[116],
	[102] = dataList[117],
	[103] = dataList[118],
	[104] = dataList[119],
	[105] = dataList[120],
	[106] = dataList[121],
	[107] = dataList[122],
	[108] = dataList[123],
	[2001] = dataList[124],
	[2002] = dataList[125],
	[109] = dataList[126],
	[111] = dataList[127],
	[112] = dataList[128],
	[114] = dataList[129],
	[115] = dataList[130],
	[116] = dataList[131],
	[117] = dataList[132],
	[118] = dataList[133],
	[119] = dataList[134],
	[120] = dataList[135],
	[121] = dataList[136],
	[122] = dataList[137],
	[123] = dataList[138],
	[124] = dataList[139],
	[125] = dataList[140],
	[126] = dataList[141],
	[127] = dataList[142],
	[128] = dataList[143],
	[129] = dataList[144],
	[130] = dataList[145],
	[131] = dataList[146],
	[132] = dataList[147],
	[133] = dataList[148],
	[134] = dataList[149],
	[135] = dataList[150],
	[136] = dataList[151],
	[137] = dataList[152],
	[138] = dataList[153],
	[139] = dataList[154],
	[140] = dataList[155],
	[141] = dataList[156],
	[142] = dataList[157],
	[143] = dataList[158],
	[144] = dataList[159],
	[145] = dataList[160],
	[146] = dataList[161],
	[147] = dataList[162],
	[148] = dataList[163],
	[149] = dataList[164],
}

t_external_share.dataList = dataList
local mt
if ExternalShareDefine then
	mt = {
		__cname =  "ExternalShareDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or ExternalShareDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_external_share