-- {excel:J交互式物品配置.xlsx, sheetName:export_个人交互道具}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_interactive_prop", package.seeall)

local title = {id=1,name=2,sortId=3,quality=4,produce=5,produceDelay=6,desc=7,hasCapacity=8,properties=9,buffs=10,hasBack=11,canRotate=12,skin=13,idle=14,specAnim=15,sitGo=16,swing=17,frontPos=18,playerDir=19,backPos=20,sitAnimation=21,sitFrontPos=22,sitBackPos=23,sound=24}

local dataList = {
	{25000001, "星星饼干", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "", {"idle"}, false, nil, false, {{x=0,y=0.261,z=-0.001}}, nil, {{x=0,y=0.261,z=-0.001}}, nil, nil, nil, nil},
	{25000002, "叶子坐垫", 40, 3, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "", {"idle"}, false, nil, false, {{x=0,y=0.306,z=-0.001}}, nil, {{x=0,y=0.306,z=-0.001}}, nil, nil, nil, nil},
	{25000003, "白色双人花", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, true, true, "", {"idle"}, false, nil, false, {{x=0.58,y=0.478,z=-0.001},{x=-0.28,y=0.198,z=-0.002}}, nil, {{x=0.395,y=0.437,z=-0.002},{x=-0.363,y=0.682,z=-0.001}}, nil, nil, nil, nil},
	{25000004, "粉色双人花", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, true, true, "", {"idle"}, false, nil, false, {{x=0.58,y=0.478,z=-0.001},{x=-0.28,y=0.198,z=-0.002}}, nil, {{x=0.395,y=0.437,z=-0.002},{x=-0.363,y=0.682,z=-0.001}}, nil, nil, nil, nil},
	{25000005, "熊斗云", 20, 5, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "", {"idle"}, false, nil, false, {{x=0,y=0,z=-0.001}}, nil, {{x=0,y=0,z=-0.001}}, nil, nil, nil, nil},
	{25000006, "摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000007, "丝滑溜溜椅", 20, 5, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, true, true, "", {"idle"}, false, nil, false, {{x=0.703,y=0.6,z=-0.001},{x=-0.418,y=0.327,z=-0.002}}, nil, {{x=0.695,y=0.448,z=-0.002},{x=-0.528,y=0.714,z=-0.001}}, {"sit_haixiu","sit_haixiu_2"}, {{x=0.408,y=0.428,z=-0.001},{x=-0.138,y=0.341,z=-0.002}}, {{x=0.321,y=0.506,z=-0.002},{x=-0.171,y=0.589,z=-0.001}}, nil},
	{25000008, "梦幻摇摇椅", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, nil, false, {{x=0.641,y=0.802,z=-0.001},{x=0.215,y=0.502,z=-0.002}}, nil, nil, nil, nil, nil, nil},
	{25000009, "花朝秋千", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"sr_huazhaoqiuqian"}, false, {"view/swing1/pos","view/swing2/pos"}, true, {{x=0,y=0,z=0},{x=0,y=0,z=0}}, nil, nil, nil, nil, nil, nil},
	{25000010, "蜜糖果篮", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, nil, false, {{x=0.4,y=0.8,z=-0.001},{x=-0.3,y=0.55,z=-0.002}}, nil, nil, nil, nil, nil, nil},
	{25000011, "奇异野餐垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000012, "麋鹿雪橇", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, nil, false, {{x=0.305,y=0.735,z=-0.00010},{x=-0.161,y=0.478,z=-0.00020}}, nil, nil, nil, nil, nil, nil},
	{25000013, "点墨江山", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, true, nil, false, {{x=0.105,y=1.435,z=-0.00010},{x=-0.443,y=1.324,z=-0.00020}}, nil, nil, nil, nil, nil, nil},
	{25000014, "造梦童话", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000015, "霓虹音乐椅", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000016, "趣味纸箱", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"animation"}, false, nil, false, {{x=-0.339,y=0.207,z=-0.00020},{x=0.322,y=0.523,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000017, "小黄鸭纸船", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {isMirror=true,count=2,notMirror="view"}, nil, false, true, "", {"idle"}, false, nil, false, {{x=-0.339,y=0.207,z=-0.00020},{x=0.322,y=0.523,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000018, "羊羊椰树吊床", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, nil, false, {{x=0,y=0.6,z=-0.00020},{x=0.7,y=0.9,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000019, "蝶之画框", 20, 5, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, false, true, "", {"idle"}, false, nil, false, {{x=0.024,y=0.04,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000020, "盛夏冰柠", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000021, "游梦花枝", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000022, "小红摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "front_3,back_3", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000023, "小橙摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "front_4,back_4", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000024, "小黑摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "front_2,back_2", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000025, "小紫摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "front_7,back_7", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000026, "小蓝摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "front_5,back_5", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000027, "小绿摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "front_6,back_6", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000028, "小粉摇摇鸭", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, true, true, "front_1,back_1", {"idle"}, false, nil, false, {{x=0,y=0.236,z=-0.001}}, nil, {{x=0,y=0.236,z=-0.001}}, nil, nil, nil, nil},
	{25000029, "请君入戏", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000030, "共赏月团", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000031, "幽冥竹灵", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000032, "萌趣爆米花", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00010},{x=0,y=0,z=-0.00020}}, nil, nil, nil, nil, nil, nil},
	{25000033, "幸运花环", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000034, "芋泥麻薯碗", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000035, "元夜鱼灯游", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000036, "灼灼花如绣", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000037, "甜心草莓蛋糕", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000038, "向左熊转杯杯椅", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, nil, false, {{x=-0.265,y=0.819,z=-0.00020},{x=0.672,y=1.146,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000039, "熊熊芝士披萨", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle","idle","idle2"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, {0,1}, nil, nil, nil, nil, nil},
	{25000040, "遗迹碎片", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000041, "摇摇木马", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000042, "趣味三角尺", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000043, "萌二夹心饼坐垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, nil, false, {{x=-0.411,y=0.478,z=-0.00020},{x=0.319,y=0.803,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000044, "焦糖小布丁", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000045, "魔法项链坐垫", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, {0,1}, nil, nil, nil, nil, nil},
	{25000046, "璀璨星辰椅", 20, 5, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, false, true, "", {"idle"}, false, {"view/pos1"}, false, {{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000047, "冰凉甜西瓜", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000048, "草莓牛奶", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000049, "梦镜桃心垫", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, {0,0}, nil, nil, nil, nil, nil},
	{25000050, "小泡芙花花坐垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, {0,0}, nil, nil, nil, nil, nil},
	{25000051, "复古黄包车", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000052, "LAURA萌趣帽", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {isMirror=true,count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000053, "毛线团篮子", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000054, "灵蛇献瑞", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00010},{x=0,y=0,z=-0.00030}}, {0,1}, nil, nil, nil, nil, nil},
	{25000055, "桃花酥食盒", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000056, "桃花财运来坐垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, {0,0}, nil, nil, nil, nil, nil},
	{25000057, "SUSUMI宝贝软垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000058, "兔兔气球坐垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0}}, nil, nil, nil, nil, nil, nil},
	{25000059, "玫瑰香雾", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0}}, nil, nil, nil, nil, nil, nil},
	{25000060, "猪米飞毯", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0}}, {0,0}, nil, nil, nil, nil, nil},
	{25000061, "星旋流辉", 20, 5, {{count=3,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他1人坐下，坐在椅子上可获得爱心。", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0}}, nil, nil, nil, nil, nil, nil},
	{25000062, "彩虹坐垫", 30, 4, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, false, true, "", {"idle"}, false, {"view/pos1"}, false, {{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000063, "摇曳醉意", 20, 5, nil, 0, "可以放到任何陆地场景中供自己坐下", false, {count=1}, nil, false, true, "", {"idle"}, false, {"view/pos1"}, false, {{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, nil},
	{25000064, "牵牛花秋千坐垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0}}, nil, nil, nil, nil, nil, nil},
	{25000065, "猫猫魔法棒坐垫", 30, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他1人坐下", false, {count=2}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0}}, {0,0}, nil, nil, nil, nil, nil},
	{25010001, "名侦探茶话桌", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他3人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=4,haveChatRoom=true}, nil, false, true, "", {"idle"}, false, nil, false, {{x=-1.993,y=0.286,z=-0.00050},{x=-1.261,y=0.971,z=-0.00030},{x=0.039,y=1.646,z=-0.00020},{x=1.26,y=1.468,z=-0.00010}}, {0,0,0,1}, nil, nil, nil, nil, nil},
	{25010002, "萌宠营地", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他2人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=3,haveChatRoom=true}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3"}, false, {{x=0,y=0,z=-0.00030},{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00040}}, {0,1,1}, nil, nil, nil, nil, nil},
	{25010003, "熊熊快乐操", 1, 4, nil, 0, "可以放到任何陆地场景中，供自己和其他2人一起做熊熊快乐操。", false, {count=3}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3"}, false, {{x=0,y=0,z=-0.00030},{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00010}}, nil, nil, nil, nil, nil, {notFull=0,id=142053,key="xxklc",full=1}},
	{25010004, "生日蛋糕坐垫", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他2人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=3,haveChatRoom=true}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3"}, false, {{x=0,y=0,z=-0.00060},{x=0,y=0,z=-0.00050},{x=0,y=0,z=-0.00010}}, {1,0,1}, nil, {"3.3_zd_shengribayinhe_idle2"}, nil, nil, {notFull=0,id=142055,key="xxklc",full=1}},
	{25010005, "恐怖人偶故事会", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他2人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=3,haveChatRoom=true}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3"}, false, {{x=0,y=0,z=-0.00030},{x=0,y=0,z=-0.00020},{x=0,y=0,z=-0.00040}}, {0,0,1}, nil, nil, nil, nil, {notFull=0,id=142056,key="xxklc",full=1}},
	{25010006, "绮梦礼赠", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他2人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=3,haveChatRoom=true}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3"}, false, {{x=0.19,y=3.02,z=-0.00060},{x=-2.3,y=4.5,z=-0.00050},{x=1.99,y=4.37,z=-0.00010}}, {1,0,1}, nil, nil, nil, nil, {notFull=0,id=142090,key="xxklc",full=1}},
	{25010007, "恭喜发财利是来", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他3人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=4,haveChatRoom=true}, nil, false, false, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3","view/pos4"}, false, {{x=0,y=0,z=-0.00070},{x=0,y=0,z=-0.00030},{x=0,y=0,z=-0.00010},{x=0,y=0,z=-0.00050}}, {0,0,0,0}, nil, nil, nil, nil, {notFull=0,id=142103,key="xxklc",full=1}},
	{25010008, "时空花园坐垫", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他3人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=4,haveChatRoom=true}, nil, false, false, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3","view/pos4"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0},{x=0,y=0,z=0},{x=0,y=0,z=0}}, {0,0,0,0}, nil, nil, nil, nil, {notFull=0,id=142127,key="xxklc",full=1}},
	{25010009, "怪可爱睡衣派对", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他2人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=3,haveChatRoom=true}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0},{x=0,y=0,z=0}}, {0,1,0}, nil, nil, nil, nil, {notFull=0,id=142160,key="xxklc",full=1}},
	{25010010, "熊熊跳跳棋", 1, 5, {{count=5,id=3}}, 6, "可以放到任何陆地场景中，供自己和其他2人坐下，坐在椅子上可加入茶话会并获得爱心。", false, {count=3,haveChatRoom=true}, nil, false, true, "", {"idle"}, false, {"view/pos1","view/pos2","view/pos3"}, false, {{x=0,y=0,z=0},{x=0,y=0,z=0},{x=0,y=0,z=0}}, {0,0,0}, nil, nil, nil, nil, {notFull=0,id=142166,key="xxklc",full=1}},
	{25020001, "海之萌鱼坐骑", 3, 3, nil, 0, "仅在海底中供自己骑乘，并加快50%移动速度。", false, {hideHandItem=true,count=1,mountSpeed=5000}, {{count=500,id=50}}, false, false, "", {"3.0_sea_xiaban_ride","3.0_sea_shangban_ride"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
	{25020002, "星海之贝坐骑", 2, 5, nil, 0, "仅在海底中供自己和其他1人骑乘，并加快80%移动速度。", false, {count=2,mountSpeed=8000}, {{count=1500,id=50}}, false, false, "", {"3.0_sea_xiaban_sit","3.0_sea_shangban_sit"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
	{25020003, "兰瑟圣翼坐骑", 2, 5, nil, 0, "仅在海底中供自己和其他1人骑乘，并加快80%移动速度。", false, {hasFrontSkin=true,count=2,mountSpeed=8000}, {{count=1500,id=50}}, false, false, "", {"3.0_sea_xiaban_sit","3.0_sea_shangban_sit"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
	{25020004, "星海鲸梦坐骑", 2, 5, nil, 0, "仅在海底中供自己和其他1人骑乘，并加快80%移动速度。", false, {count=2,mountSpeed=8000}, {{count=1500,id=50}}, false, false, "", {"3.0_sea_xiaban_sit","3.0_sea_shangban_sit"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
	{25020005, "酥酥米摩托坐骑", 3, 4, nil, 0, "仅在海底中供自己骑乘，并加快65%移动速度。", false, {soundId=142128,hideHandItem=true,count=1,mountSpeed=6500}, {{count=1000,id=50}}, false, false, "", {"3.0_sea_xiaban_ride","3.0_sea_shangban_ride"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
	{25020006, "酥酥米汽车坐骑", 2, 5, nil, 0, "仅在海底中供自己和其他1人骑乘，并加快80%移动速度。", false, {soundId=142129,hasFrontSkin=true,count=2,hasFlipSkin=true,mountSpeed=8000}, {{count=1500,id=50}}, false, false, "", {"3.0_sea_xiaban_sit","3.0_sea_shangban_sit"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
	{25030001, "熊熊平板车", 1, 3, nil, 0, "仅在陆地中供自己骑乘，并加快30%移动速度。", false, {offset={0,-0.2},hideHandItem=true,onLand=true,count=1,mountSpeed=3000}, nil, false, false, "", {"4.0_zq_huabanche_idle",nil,"4.0_zq_huabanche_move"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
	{25030002, "熊熊次元车", 2, 5, nil, 0, "仅在陆地中供自己骑乘，并加快60%移动速度。", false, {soundId=142168,offset={0,-1},onLand=true,count=1,mountSpeed=6000}, nil, false, false, "", {"sit"}, false, nil, false, nil, nil, nil, nil, nil, nil, nil},
}

local t_interactive_prop = {
	[25000001] = dataList[1],
	[25000002] = dataList[2],
	[25000003] = dataList[3],
	[25000004] = dataList[4],
	[25000005] = dataList[5],
	[25000006] = dataList[6],
	[25000007] = dataList[7],
	[25000008] = dataList[8],
	[25000009] = dataList[9],
	[25000010] = dataList[10],
	[25000011] = dataList[11],
	[25000012] = dataList[12],
	[25000013] = dataList[13],
	[25000014] = dataList[14],
	[25000015] = dataList[15],
	[25000016] = dataList[16],
	[25000017] = dataList[17],
	[25000018] = dataList[18],
	[25000019] = dataList[19],
	[25000020] = dataList[20],
	[25000021] = dataList[21],
	[25000022] = dataList[22],
	[25000023] = dataList[23],
	[25000024] = dataList[24],
	[25000025] = dataList[25],
	[25000026] = dataList[26],
	[25000027] = dataList[27],
	[25000028] = dataList[28],
	[25000029] = dataList[29],
	[25000030] = dataList[30],
	[25000031] = dataList[31],
	[25000032] = dataList[32],
	[25000033] = dataList[33],
	[25000034] = dataList[34],
	[25000035] = dataList[35],
	[25000036] = dataList[36],
	[25000037] = dataList[37],
	[25000038] = dataList[38],
	[25000039] = dataList[39],
	[25000040] = dataList[40],
	[25000041] = dataList[41],
	[25000042] = dataList[42],
	[25000043] = dataList[43],
	[25000044] = dataList[44],
	[25000045] = dataList[45],
	[25000046] = dataList[46],
	[25000047] = dataList[47],
	[25000048] = dataList[48],
	[25000049] = dataList[49],
	[25000050] = dataList[50],
	[25000051] = dataList[51],
	[25000052] = dataList[52],
	[25000053] = dataList[53],
	[25000054] = dataList[54],
	[25000055] = dataList[55],
	[25000056] = dataList[56],
	[25000057] = dataList[57],
	[25000058] = dataList[58],
	[25000059] = dataList[59],
	[25000060] = dataList[60],
	[25000061] = dataList[61],
	[25000062] = dataList[62],
	[25000063] = dataList[63],
	[25000064] = dataList[64],
	[25000065] = dataList[65],
	[25010001] = dataList[66],
	[25010002] = dataList[67],
	[25010003] = dataList[68],
	[25010004] = dataList[69],
	[25010005] = dataList[70],
	[25010006] = dataList[71],
	[25010007] = dataList[72],
	[25010008] = dataList[73],
	[25010009] = dataList[74],
	[25010010] = dataList[75],
	[25020001] = dataList[76],
	[25020002] = dataList[77],
	[25020003] = dataList[78],
	[25020004] = dataList[79],
	[25020005] = dataList[80],
	[25020006] = dataList[81],
	[25030001] = dataList[82],
	[25030002] = dataList[83],
}

t_interactive_prop.dataList = dataList
local mt
if InteractivePropDefine then
	mt = {
		__cname =  "InteractivePropDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or InteractivePropDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_interactive_prop