module("logic.extensions.hud.view.comp.HUDComp_ButtonGroupL", package.seeall)

local HUDComp_ButtonGroupL = class("HUDComp_ButtonGroupL", CommonHUDCompBase)

HUDComp_ButtonGroupL.DailyTaskRedKeys = {"dailyTask", "funcUnlock_DailyTask"}
HUDComp_ButtonGroupL.Path_Btn = "%s/%s"
HUDComp_ButtonGroupL.Path_RedGo = "%s/%s/redPoint"
HUDComp_ButtonGroupL.BtnConfigs = {
	-- {name = "btnGameRoom", container = "1", clickHandler = "onClickGameRoom", redKeys = {"GameMachine"}, funcUnlockId = nil, logId = 173},
	{name = "btnApplet", container = "1", clickHandler = "onClickMiniProgram", redKeys = {}, funcUnlockId = nil, checkVisible = "checkMiniProgram", logId = 176},
	{name = "btnBigAct", container = "2", clickHandler = "onClickBigActivity", redKeys = {"ToyHouseMapHUD"}, funcUnlockId = nil, checkVisible = "checkBigAct", logId = 178},
	{name = "btnMiddleAct", container = "2", clickHandler = "onClickMiddleActivity", redKeys = {"MiddleActivity"}, funcUnlockId = nil, checkVisible = "checkMiddleAct",logId = 179},
	{name = "btnSmallAct", container = "2", clickHandler = "onClickAnniversary", redKeys = {"Anniversary15HUD"}, funcUnlockId = nil, checkVisible = "checkAnniversaryOpen",logId = 180},
	{name = "btnSpecialAct", container = "2", clickHandler = "onClickSpecialActivity", redKeys = {"CCTV"}, funcUnlockId = nil, checkVisible = "checkSpecialAct",logId = 181},
	-- {name = "btnMiliestone", container = "1", clickHandler = "onClickMileStone", redKeys = {"MileStoneMain"}, funcUnlockId = 64}, --, checkVisible = "checkMileStoneVisible"
	{name = "btnHongBao", container = "2", clickHandler = "onClickCashRedEnvelope", redKeys = {"CashRedEnvelopeActivity"}, funcUnlockId = nil, checkVisible = "checkCashRedEnvelope"},
	{name = "btnApplyAct", container = "2", clickHandler = "onClickStarMatchSignUp", redKeys = {"activity1452"}, activityDefineId = 261},
	{name = "btnUnrealarea", container = "2", clickHandler = "onClickUnrealArea", redKeys = {"act431"}, activityDefineId = 431},
	--临时加多个中主题的按钮，因为时间上有冲突
	{name = "btnMiddleActTemp", container = "2", clickHandler = "onClickMiddleActivityTemp", redKeys = {"DreamBox"}, funcUnlockId = nil, checkVisible = "checkMiddleActTemp",logId = 179},
	{name = "btnLandlord", container = "2", clickHandler = "onClickLandlord", redKeys = {"activity439Arena"}, checkVisible = "checkLandlord"},--, funcUnlockId = nil, logId = 179},
	{name = "btnFood", container = "2", clickHandler = "_onClickLongStreetBanqut", redKeys = {"LongStreetBanquet"},activityDefineId = 444}
} ---新增的尽量加到这里, 简洁一些
function HUDComp_ButtonGroupL:onClickUnrealArea()
	self:logClick(195)
	RedPointController.instance:setLastClick("act431")
	UnrealAreaController.code_entry()
end
function HUDComp_ButtonGroupL:onClickStarMatchSignUp()
	RedPointController.instance:setLastClick("activity1452")
	ViewMgr.instance:open("StarMatchShare")
end
function HUDComp_ButtonGroupL:ctor()
end
---管理类初始化时调用(目前版本是CommonHUDController.onInit时调用,以后可能会改)
function HUDComp_ButtonGroupL:onInit()
end
---资源加载完成并构建UI之后调用(资源会在第一次显示时加载)
function HUDComp_ButtonGroupL:buildUI()
	for _, cfg in ipairs(HUDComp_ButtonGroupL.BtnConfigs) do
		local path = string.format(HUDComp_ButtonGroupL.Path_Btn, cfg.container, cfg.name)
		local btn = self:getBtn(path)
		if btn then
			btn:AddClickListener(self._onClickBtn, self, {cfg = cfg})
			self[cfg.name] = btn
			if cfg.redKeys and #cfg.redKeys > 0 then
				local redGo = self:getGo(string.format(HUDComp_ButtonGroupL.Path_RedGo, cfg.container, cfg.name))
				RedPointController.instance:registerRedPoint(redGo, cfg.redKeys)
			end

			if cfg.funcUnlockId then
				FuncUnlockFacade.instance:regGO(cfg.funcUnlockId, btn.gameObject)
			elseif cfg.activityDefineId then
				btn.gameObject:SetActive(ActivityModel.instance:getActivityIsShow(cfg.activityDefineId))
			elseif cfg.checkVisible then
				btn.gameObject:SetActive(self[cfg.checkVisible](self))
			else
				btn.gameObject:SetActive(true)
			end
		else
			printError("ButtonGroupL找不到按钮" .. tostring(path))
		end
	end

	--部分功能由于变化太大 会出现命名与实际相差太大
	--====================== 一直常驻 ==============================
	--商城
	self.btnStore = self:getBtn("1/btnStore")
	self.btnStore:AddClickListener(self.onClickStore, self)
	self._storeRedGo = self:getGo("1/btnStore/redPoint")
	FuncUnlockFacade.instance:regGO(FuncIds.Recharge, self.btnStore.gameObject)
	RedPointController.instance:registerRedPoint(self:getGo("1/btnStore/redPoint"), {"RechargeShop_0"})
	RedPointController.instance:registerRedPoint(self:getGo("1/btnStore/imgNewBubble"), {"RechargeShop_New_0"})
	--活动
	self.btnPoster = self:getBtn("1/btnPoster")
	self.btnPoster:AddClickListener(self.onClickPoster, self)
	self._posterRedGo = self:getGo("1/btnPoster/redPoint")
	RedPointController.instance:registerRedPoint(self._posterRedGo, {"ActivityWinterFirst"})
	--彩蛋
	self.btnLottery = self:getBtn("1/btnLottery")
	self.btnLottery:AddClickListener(self.onClickLottery, self)
	self._imgActivity = self:getGo("1/btnLottery/imgActivity")
	FuncUnlockFacade.instance:regGO(FuncIds.Lottery, self.btnLottery.gameObject)
	RedPointController.instance:registerRedPoint(self:getGo("1/btnLottery/redPoint"), {"Lottery_HUD"})
	--红宝石
	self.btnRubyMember = self:getBtn("1/btnRubyMember")
	self.btnRubyMember:AddClickListener(self._onClickRubyMember, self)
	self._RubyDailyGiftRedPoint = self:getGo("1/btnRubyMember/redPoint")
	RedPointController.instance:registerRedPoint(self._RubyDailyGiftRedPoint, {"Ruby_Daily_Gift"})
	FuncUnlockFacade.instance:regGO(FuncIds.RubyMember, self.btnRubyMember.gameObject)
	--生活指南
	self.btnLifeGuide = self:getBtn("1/btnLifeGuide")
	self.btnLifeGuide:AddClickListener(self.onClickLifeGuide, self)
	RedPointController.instance:registerRedPoint(self:getGo("1/btnLifeGuide/redPoint"), {"LifeGuide"})
	--新手指南
	self.btnNoviceChallenge = self:getBtn("1/btnNoviceChallenge")
	self.btnNoviceChallenge:AddClickListener(self._onClickNoviceChallenge, self)
	self._noviceChallengeRedPoint = self:getGo("1/btnNoviceChallenge/redPoint")
	RedPointController.instance:registerRedPoint(self._noviceChallengeRedPoint, {"SevenDayActive"})
	--财神活动
	self._btnWealthGod = self:getBtn("2/btnWealthGod")
	self._btnWealthGodGo = self:getGo("2/btnWealthGod")
	self._btnWealthGod:AddClickListener(self.onClickWealthGod, self)
	self._wealthGodRedPoint = self:getGo("2/btnWealthGod/redPoint")
	RedPointController.instance:registerRedPoint(self._wealthGodRedPoint, {"WealthGodFirstClick"})
	--迎新
	self._btnInviteNewbie = self:getBtn("1/inviteNewbie")
	self._btnInviteNewbie:AddClickListener(self.onClickInviteNewbie, self)
	self._btnInviteNewbieRedpoint = self:getGo("1/inviteNewbie/redPoint")
	RedPointController.instance:registerRedPoint(self._btnInviteNewbieRedpoint, {"InviteNewbie"})
	--回归
	self._btnReturn = self:getBtn("1/btnPlayerReturn")
	self._btnReturnGo = self:getGo("1/btnPlayerReturn")
	self._btnReturn:AddClickListener(self.onClickReturn, self)
	self._btnReturnRedPoint = self:getGo("1/btnPlayerReturn/redPoint")
	RedPointController.instance:registerRedPoint(self._btnReturnGo, {"AobiReturnUnlock"})
	RedPointController.instance:registerRedPoint(self._btnReturnRedPoint, {"AobiReturn"})
	--首充2
	self._btnFirstRecharge2 = self:getBtn("1/btnFirstRecharge2")
	self._btnFirstRecharge2:AddClickListener(self.onClickFirstRecharge2, self)
	self._FirstRechargeRedPoint2 = self:getGo("1/btnFirstRecharge2/redPoint")
	RedPointController.instance:registerRedPoint(self._FirstRechargeRedPoint2, {"FirstRecharge", "firstChargeCanDarg"})
	--RedPointController.instance:registerRedPoint(self._btnReturnGo, {"AobiReturnUnlock"})
	--RedPointController.instance:registerRedPoint(self._btnReturnRedPoint, {"AobiReturn"})
	--问卷
	-- self.btnFirstCharge = self:getBtn("1/btnFirstCharge")
	-- self.btnFirstCharge:AddClickListener(self.onClickSurvey, self)
	-- self._firstChargeRedGo = self:getGo("1/btnFirstCharge/redPoint")
	--期刊
	-- self.btnNewspaper = self:getBtn("2/btnNewsPaper")
	-- self.btnNewspaper:AddClickListener(self._onClickNewspaper, self)
	-- self._newspaperRedGo = self:getGo("2/btnNewsPaper/redPoint")
	--甜品站
	-- self._btnSweetFood = self:getBtn("2/btnSweetFood")
	-- self._btnSweetFood:AddClickListener(self.onClickSweetFood, self)
	--周一好礼
	-- self._btnMondayGift = self:getBtn("2/btnMondayGift")
	-- self._btnMondayGift:AddClickListener(self.onClickMondayGift, self)
	-- RedPointController.instance:registerRedPoint(self._btnMondayGift.gameObject, {"MondayGift"})
	--周末福利
	-- self._btnWeekendGift = self:getBtn("2/btnWeekendGift")
	-- self._btnWeekendGift:AddClickListener(self.onClickWeekendGift, self)
	-- RedPointController.instance:registerRedPoint(self._btnWeekendGift.gameObject, {"Act284RedPoint"})
	--======================活动 下架了就没有作用了 =================
	--大转盘
	self._btnTurntable = self:getBtn("2/btnTurntable")
	self._btnTurntable:AddClickListener(self._onClickTurntable, self)
	self._TurnTablePoint = self:getGo("2/btnTurntable/redPoint")
	self._TurnTableRedBagPoint = self:getGo("2/btnTurntable/imgActivity")
	FuncUnlockFacade.instance:regGO(FuncIds.Recharge, self._btnTurntable.gameObject)
	RedPointController.instance:registerRedPoint(self._TurnTablePoint, {"OPENING_TURNTABLE"})
	RedPointController.instance:registerRedPoint(self._TurnTableRedBagPoint, {"TURNTABLE_RedBagGift"})
	--碰乌龟
	self._btnTurtleLottery = self:getBtn("2/btnTurtleLottery")
	self._btnTurtleLottery:AddClickListener(self._onClickTurtleLottery, self)
	self._turtleLotteryRedPoint = self:getGo("2/btnTurtleLottery/redPoint")
	FuncUnlockFacade.instance:regGO(FuncIds.Recharge, self._btnTurtleLottery.gameObject)
	RedPointController.instance:registerRedPoint(self._turtleLotteryRedPoint, {"TurtleLottery"})
	--累充
	-- self._btnMultiRecharge = self:getBtn("1/btnMultiRecharge")
	-- self._btnMultiRecharge:AddClickListener(self.onClickMultiRecharge, self)
	-- self._btnMultiRechargeRedPoint = self:getGo("1/btnMultiRecharge/redPoint")
	-- RedPointController.instance:registerRedPoint(self._btnMultiRechargeRedPoint, {"Act_Multi_Recharge"})
	--现金红包
	self._btnCashChest = self:getBtn("2/btnHongBao")
	self._btnCashChest:AddClickListener(self.onClickCashRedEnvelope, self)
	self._goCashChestOpenEffect = self:getGo("2/btnHongBao/openEffect")
	RedPointController.instance:registerRedPoint(self._goCashChestOpenEffect, {"CashRedEnvelopeCashChestActivity"})
	
	 --NewYearHongBaoController.instance:registerHongbaoIcon(self._btnHongBaoRain.gameObject)
	--宠物公益
	-- self._btnPetWelfare = self:getBtn("1/btnPetWelfare")
	-- self._btnPetWelfare:AddClickListener(self.onClickPetWelfare, self)
	-- self._btnPetWelfareRedPoint = self:getGo("1/btnPetWelfare/redPoint")
	-- RedPointController.instance:registerRedPoint(self._btnPetWelfareRedPoint, {"PetWelfare"})
	--燃灯节
	-- self._btnLanternFestival = self:getBtn("1/btnLanternFestival")
	-- self._btnLanternFestival:AddClickListener(self.onClickLanternFestival, self)
	-- self._btnLanternFestivalRedPoint = self:getGo("1/btnLanternFestival/redPoint")
	-- RedPointController.instance:registerRedPoint(self._btnLanternFestivalRedPoint, {"LanternFestival"})
	--游戏大厅游戏红点，读每次的配置了
	self._curGameRoomShowingGame = GameRoomFacade.getPromoGameId()-- GameEnum.GameRoomType.DRAW_AND_GUESS
	self._btnGameRoom = self:getBtn("1/btnGameRoom")
	self._btnGameRoom:AddClickListener(self.onClickGameRoom, self)
	self._btnGameRoom.gameObject:SetActive(true)
	self._btnGameRoomRedPoint = self:getGo("1/btnGameRoom/redPoint")
	--缪拉庆典
	-- self._btnMura = self:getBtn("1/btnMura")
	-- self._btnMura:AddClickListener(self.onClickMura, self)
	-- self._btnMuraRedPoint = self:getGo("1/btnMura/redPoint")
	-- RedPointController.instance:registerRedPoint(self._btnMuraRedPoint, {"QixiSignin"})
	--预更新
	-- self._btnNewGift = self:getBtn("2/btnNewGift")
	-- self._btnNewGift:AddClickListener(self.onClickNewGift, self)
	-- self._btnNewGiftRedPoint = self:getGo("1/btnNewGift/redPoint")
	--开学季
	self._btnTermbegins = self:getBtn("2/btnTermbegins")
	self._btnTermbegins:AddClickListener(self.onClickTermbegins, self)
	self._termbeginsRedPoint = self:getGo("2/btnTermbegins/redPoint")
	RedPointController.instance:registerRedPoint(self._termbeginsRedPoint, {"Termbegins"})
	--IP周年庆
	-- self._btnAnniversary = self:getBtn("1/btnAnniversary")
	-- self._btnAnniversary:AddClickListener(self.onClickAnniversary, self)
	-- self._anniversaryRedPoint = self:getGo("1/btnAnniversary/redPoint")
	-- RedPointController.instance:registerRedPoint(self._anniversaryRedPoint, {"Anniversary"})
	--生态园推广
	self._btnAdvertisement = self:getBtn("2/btnAdvertisement")
	self._btnAdvertisement:AddClickListener(self.onClickAdvertisement, self)
	self._btnAdvertisementPoint = self:getGo("2/btnAdvertisement/redPoint")
	-- FuncUnlockFacade.instance:regGO(FuncIds.EcoPark, self._btnAdvertisement.gameObject)
	RedPointController.instance:registerRedPoint(self._btnAdvertisementPoint, {"EcologyParkAds"})
	-- MiaoMiaoWuTaskController.instance:makeTaskConfig() -- 因为要一进游戏就刷红点，要有地方调这个刷任务数据的方法，就在这里调用了

	-- PredownloadRewardsController.instance:getIsNew(
	-- 	function(isGot)
	-- 		self._btnNewGift.gameObject:SetActive(not isGot)
	-- 	end
	-- )

	--音乐派对
	-- self._btnMusicParty = self:getBtn("2/btnMusicParty")
	-- self._btnMusicPartyEffect = self:getGo("2/btnMusicParty/ui_hud_musicparty_01")
	-- self._btnMusicPartyAnimation = self:getGo("2/btnMusicParty"):GetComponent("Animation")
	-- self._btnMusicParty:AddClickListener(self.onClickMusicParty, self)
	-- self._btnMusicPartyRedPoint = self:getGo("2/btnMusicParty/redPoint")
	-- RedPointController.instance:registerRedPoint(self._btnMusicPartyRedPoint, {"MusicParty"})

	-- self._btnAobiReturn = self:getBtn("2/btnReturnParty")
	-- self._btnAobiReturn:AddClickListener(self._onClickAobiReturn, self)
	-- self._txtAobiReturn = self:getText("2/btnReturnParty/txtTime")

	-- self._btnStarMatchSignup = self:getBtn("1/btnStarMatchSignup")
	-- self._btnStarMatchSignupRedPoint = self:getGo("1/btnStarMatchSignup/redPoint")
	-- self._btnStarMatchSignup:AddClickListener(self._onClickStarMatchSignup, self)
	-- RedPointController.instance:registerRedPoint(self._btnStarMatchSignupRedPoint, {"activity1452"})

	self._btnLibraryLotteryUp = self:getBtn("1/btnLibraryLotteryUp")
	self._btnLibraryLotteryUpRedPoint = self:getGo("1/btnLibraryLotteryUp/redPoint")
	self._btnLibraryLotteryUp:AddClickListener(self._onClickLibraryLotteryUp, self)
	RedPointController.instance:registerRedPoint(self._btnLibraryLotteryUpRedPoint, {"Traveller_UpLottery_Hud"})
	
	self._btnSeasonCollectCard = self:getBtn("1/btnSeasonCollectCard")
	self._btnSeasonCollectCardRedPoint = self:getGo("1/btnSeasonCollectCard/redPoint")
	self._btnSeasonCollectCard:AddClickListener(self._onClickSeasonCollectCard, self)
	RedPointController.instance:registerRedPoint(self._btnSeasonCollectCardRedPoint, {"SeasonCollectCard"})
	

	--惊喜签到
	-- self._btnSurpriseSignIn = self:getBtn("2/btnSurpriseSignIn")
	-- self._btnSurpriseSignIn:AddClickListener(self.onClickSurpriseSignIn, self)
	----------------------
	GlobalDispatcher:addListener(GlobalNotify.OnServerRefresh, self._onServerRefresh, self)
	ClockMgr.instance:addListener(ClockMgr.TickHour, self._onHourUpdate, self)
	self:popupGift_buildUI()

	-- RedPointController.instance:registerRedPoint(self._miniProgramIcon.gameObject, {"MiniProgram"})
	-- self._miniProgramIcon = self:getBtn("1/btnXiaoChengXu")
	-- self._miniProgramIcon:AddClickListener(self.onClickMiniProgram, self)

	self._imgBack = self:getGo("1/btnLottery/imgBack")
    GlobalDispatcher:addListener(GlobalNotify.OnGetFuncUnlockInfo, self._onGetFuncUnlockInfo, self)
	
	self._btnCrossDress = self:getBtn("1/btnCrossDress")
	self._crossDressRedPoint = self:getGo("1/btnCrossDress/redPoint")
	RedPointController.instance:registerRedPoint(self._crossDressRedPoint, {"DressMatchEntrance"})
	self._btnCrossDress:AddClickListener(self._onClickCrossDress, self)
	---新增的限时按钮
	self._btnTimeLimit = self:getBtn("2/btnTimeLimit")
	self._btnTimeLimit:AddClickListener(self._onClickTimeLimitBtn, self)
	self._goTimeLimit = self:getGo("2/btnTimeLimit")
	self._goTimeLimitExpand = goutil.findChild(self._goTimeLimit, "openGo")
	self._goTimeLimitExpand:SetActive(false)
	self._timeLimitExpandTrigger = Framework.UIGlobalTouchTrigger.Get(self._goTimeLimitExpand)

	self._goTimeLimitImgContainer = goutil.findChild(self._goTimeLimit, "img")
	self._goTimeLimit_Icon_SweetFood = goutil.findChild(self._goTimeLimitImgContainer, "img_1")
	self._goTimeLimit_Icon_SurpriseSignIn = goutil.findChild(self._goTimeLimitImgContainer, "img_2")
	self._goTimeLimit_Icon_WeekendGift = goutil.findChild(self._goTimeLimitImgContainer, "img_3")
	self._goTimeLimit_Icon_ReturnParty = goutil.findChild(self._goTimeLimitImgContainer, "img_4")
	self._goTimeLimit_Expand_SweetFood = goutil.findChild(self._goTimeLimitExpand, "item_1")
	self._goTimeLimit_Expand_SurpriseSignIn = goutil.findChild(self._goTimeLimitExpand, "item_2")
	self._goTimeLimit_Expand_WeekendGift = goutil.findChild(self._goTimeLimitExpand, "item_3")
	-- self._goTimeLimit_Expand_ReturnParty = goutil.findChild(self._goTimeLimitExpand, "item_4")
	self._btnTimeLimit_SweetFood = Framework.ButtonAdapter.GetFrom(self._goTimeLimit_Expand_SweetFood, "btnOpen")
	self._btnTimeLimit_SweetFood:AddClickListener(self.onClickSweetFood, self)
	self._btnTimeLimit_SurpriseSignIn = Framework.ButtonAdapter.GetFrom(self._goTimeLimit_Expand_SurpriseSignIn, "btnOpen")
	self._btnTimeLimit_SurpriseSignIn:AddClickListener(self.onClickSurpriseSignIn, self)
	self._btnTimeLimit_WeekendGift = Framework.ButtonAdapter.GetFrom(self._goTimeLimit_Expand_WeekendGift, "btnOpen")
	self._btnTimeLimit_WeekendGift:AddClickListener(self.onClickWeekendGift, self)
	-- self._btnTimeLimit_ReturnParty = Framework.ButtonAdapter.GetFrom(self._goTimeLimit_Expand_ReturnParty, "btnOpen")
	-- self._btnTimeLimit_ReturnParty:AddClickListener(self._onClickAobiReturn, self)
	-- self._txtAobiReturn = goutil.findChildTextComponent(self._goTimeLimit_Expand_ReturnParty, "timeGo/txtTime")
	self._txtSurpriseSignin = goutil.findChildTextComponent(self._goTimeLimit_Expand_SurpriseSignIn, "timeGo/txtTime")
	self:initPersonalizedGiftBag()


	--版本更新福利
	self._btnUpgradeReward = self:getBtn("2/btnUpgradeReward")
	if self._btnUpgradeReward then
		UpgradeRewardModel.instance:registerData("hasRewards", self._onUpgradeRewardChanged, self)
		UpgradeRewardModel.instance:registerData("lastGetTime", self._onUpgradeRewardChanged, self)
		self._btnUpgradeReward:AddClickListener(self._onClicknUpgradeReward, self)
	end

	-- 有缘指南首次点击红点
	self._btnBigActGuide = self:getGo("2/btnBigAct/guide")
	if self._btnBigActGuide then
		RedPointController.instance:registerRedPoint(self._btnBigActGuide, {"NewYearGuideFirstClick"})
	end
end

function HUDComp_ButtonGroupL:_onClickBtn(params)
	local cfg = params.cfg
	if cfg.logId then
		LogService.instance:logClick(cfg.logId)
	end
	trycall(self[cfg.clickHandler], self)
end

function HUDComp_ButtonGroupL:_onClickCrossDress()
	self:logClick(182)
	ViewMgr.instance:open("DressMatchEntrance")
end

function HUDComp_ButtonGroupL:_onGetFuncUnlockInfo()
	self:_updateBtnUpLottery()
end

function HUDComp_ButtonGroupL:_onClickLibraryLotteryUp()
	ViewMgr.instance:open("TravellerUpLotteryView")
end

function HUDComp_ButtonGroupL:_onClickSeasonCollectCard()
	CollectCardController.instance:enterCollectCard()
end

---显示验证函数,在试图显示时调用,如果返回false,则取消本次显示
function HUDComp_ButtonGroupL:onValidateShow()
	return true
end

---每次显示时调用
function HUDComp_ButtonGroupL:onEnter()
	if not (TaskFacade.instance:isNewbieTaskFinish()) then
		GlobalDispatcher:addListener(GlobalNotify.OnTaskFinish, self._checkNewbieTaskFinish, self)
	end
	GlobalDispatcher:addListener(GlobalNotify.OnRedPointPush, self._onRedPointPush, self)
	GlobalDispatcher:addListener(GlobalNotify.getActivityInfoFinish, self._updateActiveBtn, self)
	GlobalDispatcher:addListener(GlobalNotify.ActivityUpdate, self._updateActiveBtn, self)
	GlobalDispatcher:addListener(GlobalNotify.UserLevelUp, self._onUserLevelUp, self)
	GlobalDispatcher:addListener(GlobalNotify.FreeRaceIconChange, self._updateBtnSweetFood, self)
	GlobalDispatcher:addListener(GlobalNotify.SurpriseSignInIconChange, self._updateBtnSurpriseSignIn, self)
	GlobalDispatcher:addListener(GlobalNotify.OnFuncUnlock, self._updateBtnAdvertisement, self)
	LandlordController.instance:registerLocalNotify(LandlordNotify.UpdaterArenaHUD, self.checkLandlord, self)
	-- GlobalDispatcher:addListener(MusicPartyNotify.ProcedureStateChange, self._updateBtnMusicParty, self)
	self._timeLimitExpandTrigger:AddIgnoreTargetListener(self._onClickOutside, self)

	-- self:initSurvey()
	self:checkPosterAndStore()
	self:_updateActiveBtn()
	-- self:_initNewspaperRedPoint()
	self:_updateBtnLifeGuide()
	self:_updateBtnFirstRecharge()
	self:_updateLotteryAct()
	-- self:_updateBtnSweetFood()
	-- self:_updateBtnSurpriseSignIn()
	self:_updateBtnAdvertisement()
	self:initCurGameRoomRedPoint()
	NationalDayController.instance:checkRedPoint()
	SevenDayActiveController.instance:checkRedPoint()
	ParttimeController.instance:checkRedPoint()
	--福利中心按钮
	self:popupGift_onEnter()
	self:aobiReturnParty_onEnter()
	self.btnApplet.gameObject:SetActive(self:checkMiniProgram())
	self:_updateTimeLimitBtns()
	self:_updateTimeLimitExpand()
	--财神
	self:_updateBtnWealthGod()
end

function HUDComp_ButtonGroupL:_onClickOutside()
	self._goTimeLimitExpand:SetActive(false)
end

function HUDComp_ButtonGroupL:_updateTimeLimitBtns()
	self._goTimeLimit:SetActive(true)
	if UserInfo.isReturn and self._partyStartTime and self._partyStartTime > 0 then
		local leftTime = self._partyStartTime + TimeUtil.SceondsPerHour - ServerTime.now()
		print("self._partyStartTime", self._partyStartTime, "leftTime", leftTime, "self._isGetPartyReward", self._isGetPartyReward)
		if leftTime > 0 or not self._isGetPartyReward then
			TaskUtil.showGOThenHideOthers(self._goTimeLimit_Icon_ReturnParty)
			return
		end
	end
	if SurpriseSignInController.instance:getIsCanShowHudIcon() then
		TaskUtil.showGOThenHideOthers(self._goTimeLimit_Icon_SurpriseSignIn)
		return
	end
	if RedPointController.instance:getRedIsExist("Act284RedPoint") then
		TaskUtil.showGOThenHideOthers(self._goTimeLimit_Icon_WeekendGift)
		return
	end
	if FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.FreeRace, false) and FreeRaceFacade.instance:isCanGetRace() then
		TaskUtil.showGOThenHideOthers(self._goTimeLimit_Icon_SweetFood)
		return
	end
	self._goTimeLimit:SetActive(false) --没有满足的限时活动时隐藏
end

function HUDComp_ButtonGroupL:_updateTimeLimitExpand()
	if not self._goTimeLimitExpand.activeSelf then
		return
	end
	-- self._goTimeLimit_Expand_ReturnParty:SetActive(self:_isShowAobiReturnParty())
	if SurpriseSignInController.instance:getIsCanShowHudIcon() then
		self._goTimeLimit_Expand_SurpriseSignIn:SetActive(true)
		self._txtSurpriseSignin.text = SurpriseSignInController.instance:getHUDIconRemainDisplayTime()
		settimer(0.3, self._updateSurpriseSignin, self, true)
	else
		self._goTimeLimit_Expand_SurpriseSignIn:SetActive(false)
	end
	self._goTimeLimit_Expand_WeekendGift:SetActive(RedPointController.instance:getRedIsExist("Act284RedPoint"))
	self._goTimeLimit_Expand_SweetFood:SetActive(FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.FreeRace, false) and FreeRaceFacade.instance:isCanGetRace())
end

function HUDComp_ButtonGroupL:_onClickTimeLimitBtn()
	self:logClick(184)
	if self._goTimeLimitExpand.activeSelf then
		-- self._goTimeLimitExpand:SetActive(false)
		return
	end
	self._goTimeLimitExpand:SetActive(true)
	self:_updateTimeLimitExpand()
end

function HUDComp_ButtonGroupL:_updateSurpriseSignin()
	if SurpriseSignInController.instance:getIsCanShowHudIcon() then
		self._txtSurpriseSignin.text = SurpriseSignInController.instance:getHUDIconRemainDisplayTime()
	else
		removetimer(self._updateSurpriseSignin, self)
	end
end

function HUDComp_ButtonGroupL:_updateLotteryAct()
	RedPointController.instance:addNode(LotteryView.ActLocalKey, {})
	local actLottery = self:_getActLottery()
	RedPointController.instance:unregisterRedPoint(self._imgBack)
	if #actLottery > 0 then
		for _, define in ipairs(actLottery) do
			local redKey = LotteryView.ActRedKey .. define.id
			local localKey = LotteryView.ActLocalKey .. define.id
			if not LocalStorage.instance:getValue({name = localKey, type = 3}, false) then
				RedPointController.instance:addNode(redKey, {})
				RedPointController.instance:addChildToParent(redKey, {LotteryView.ActLocalKey})
				RedPointController.instance:setRedIsExist(redKey, true)
			end
			if define.isBack then
				RedPointController.instance:addNode(LotteryView.ActBackKey .. define.id, {isFirstClickRP = true})
				RedPointController.instance:registerRedPoint(self._imgBack, {LotteryView.ActBackKey .. define.id})
			end
		end
	end
	RedPointController.instance:unregisterRedPoint(self._imgActivity)
	RedPointController.instance:registerRedPoint(self._imgActivity, {LotteryView.ActLocalKey})
end

function HUDComp_ButtonGroupL:_getActLottery()
	local result = {}
	local all = LotteryConfig.getAllConfig()
	for _, define in ipairs(all) do
		if define.showInHUD then
			table.insert(result, define)
		end
	end
	return result
end

function HUDComp_ButtonGroupL:_onUserLevelUp()
	self:_updateBtnLifeGuide()
	self:_updateBtnFirstRecharge()
	self:_updateBtnInviteNew()
end

function HUDComp_ButtonGroupL:_updateBtnCfg()
    for _, cfg in ipairs(HUDComp_ButtonGroupL.BtnConfigs) do
        local btn = self[cfg.name]
        if btn then
			if cfg.funcUnlockId then
				--- 如果绑定了功能开启id的就不用管
			elseif cfg.activityDefineId then
				btn.gameObject:SetActive(ActivityModel.instance:getActivityIsShow(cfg.activityDefineId))
			elseif cfg.checkVisible then
				btn.gameObject:SetActive(self[cfg.checkVisible](self))
			else
				btn.gameObject:SetActive(true)
			end
        end
    end
end

-- function HUDComp_ButtonGroupL:_initNewspaperRedPoint()
-- 	local count = LocalStorage.instance:getValue(StorageKey.NewspaperRedPoint, 0)
-- 	local allNwspaper = AobiNewspaperConfig.getAllNewspaperDefines()
-- 	goutil.setActive(self.btnNewspaper.gameObject, not (count == #allNwspaper))
-- 	goutil.setActive(self._newspaperRedGo, not (count == #allNwspaper))
-- end

-- function HUDComp_ButtonGroupL:setSurvey()
-- 	self.btnFirstCharge.gameObject:SetActive(SurveyController.instance:getCurrentSurvey())
-- end

-- function HUDComp_ButtonGroupL:initSurvey()
-- 	SurveyController.instance:registerLocalNotify(SurveyController.OnSurveyUpdate, self.setSurvey, self)
-- 	self._firstChargeRedGo:SetActive(false)
-- 	self:setSurvey()
-- end

function HUDComp_ButtonGroupL.getTimeIndex()
	local secDate = TimeUtil.dateStr2TimeStamp(GameUtils.getCommonConfig("SurveyBeginDate2"))
	if ServerTime.nowServerLook() >= secDate then
		return 2
	else
		return 1
	end
end

-- function HUDComp_ButtonGroupL:onClickSurvey()
-- 	-- FlyTextManager.instance:showFlyText(lang("功能暂未开放"))
-- 	-- local index = self.getTimeIndex()
-- 	-- RedPointController.instance:setRedIsExist(SurveyRedKeys[index], false)
-- 	-- if index == 1 then
-- 	-- 	--记得要url改成移动端的链接
-- 	-- 	ViewFacade.openWebView("https://www.wjx.cn/vj/wdSKb1J.aspx")
-- 	-- else
-- 	-- 	ViewFacade.openWebView("https://www.wjx.cn/vj/Q0EZ78n.aspx")
-- 	-- end
-- 	SurveyController.instance:openSurvey()
-- end
---每次隐藏时调用
function HUDComp_ButtonGroupL:onExit()
	GlobalDispatcher:removeListener(GlobalNotify.OnTaskFinish, self._checkNewbieTaskFinish, self)
	GlobalDispatcher:removeListener(GlobalNotify.getActivityInfoFinish, self._updateActiveBtn, self)
	GlobalDispatcher:removeListener(GlobalNotify.ActivityUpdate, self._updateActiveBtn, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnRedPointPush, self._onRedPointPush, self)
	GlobalDispatcher:removeListener(GlobalNotify.UserLevelUp, self._onUserLevelUp, self)
	GlobalDispatcher:removeListener(GlobalNotify.FreeRaceIconChange, self._updateBtnSweetFood, self)
	GlobalDispatcher:removeListener(GlobalNotify.SurpriseSignInIconChange, self._updateBtnSurpriseSignIn, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnFuncUnlock, self._updateBtnAdvertisement, self)
	LandlordController.instance:unregisterLocalNotify(LandlordNotify.UpdaterArenaHUD, self.checkLandlord, self)
	-- GlobalDispatcher:removeListener(MusicPartyNotify.ProcedureStateChange, self._updateBtnMusicParty, self)
	-- self._timer:removeTimer(self._onAobiReturnPartyTick, self)
	self._timeLimitExpandTrigger:RemoveIgnoreTargetListener()
	self._goTimeLimitExpand:SetActive(false)
	removetimer(self._updateSurpriseSignin, self)
end

---销毁时调用
function HUDComp_ButtonGroupL:destroyUI()
	AobiReturnController.instance:unregisterLocalNotify("OnAotiReturnPartyCreate", self._createReturnParty, self)
	AobiReturnController.instance:unregisterLocalNotify(
		"AobiReturnTaskController_OnGetPartyEndReward",
		self._onTakePartyEndGift,
		self
	)
	RedPointController.instance:unregisterRedPoint(self._btnLibraryLotteryUpRedPoint)
	-- RedPointController.instance:unregisterRedPoint(self._btnStarMatchSignupRedPoint)
	RedPointController.instance:unregisterRedPoint(self._noviceChallengeRedPoint)
	RedPointController.instance:unregisterRedPoint(self._RubyDailyGiftRedPoint)
	RedPointController.instance:unregisterRedPoint(self._FirstRechargeRedPoint2)
	RedPointController.instance:unregisterRedPoint(self._TurnTablePoint)
	RedPointController.instance:unregisterRedPoint(self._TurnTableRedBagPoint)
	RedPointController.instance:unregisterRedPoint(self._btnReturnRedPoint)
	RedPointController.instance:unregisterRedPoint(self._btnReturnGo)
	-- RedPointController.instance:unregisterRedPoint(self._btnMultiRechargeRedPoint)
	RedPointController.instance:unregisterRedPoint(self._btnInviteNewbieRedpoint)
	RedPointController.instance:unregisterRedPoint(self._posterRedGo)
	RedPointController.instance:unregisterRedPoint(self:getGo("1/btnLifeGuide/redPoint"))
	RedPointController.instance:unregisterRedPoint(self:getGo("1/btnStore/redPoint"))
	RedPointController.instance:unregisterRedPoint(self:getGo("1/btnStore/imgNewBubble"))
	RedPointController.instance:unregisterRedPoint(self:getGo("1/btnLottery/redPoint"))
	RedPointController.instance:unregisterRedPoint(self._goCashChestOpenEffect)
	-- RedPointController.instance:unregisterRedPoint(self._btnPetWelfareRedPoint)
	-- RedPointController.instance:unregisterRedPoint(self._btnLanternFestivalRedPoint)
	-- RedPointController.instance:unregisterRedPoint(self._btnMuraRedPoint)
	-- RedPointController.instance:unregisterRedPoint(self._btnMusicPartyRedPoint)
	-- RedPointController.instance:unregisterRedPoint(self._miniProgramIcon.gameObject)
	-- RedPointController.instance:unregisterRedPoint(self._btnMondayGift.gameObject)
	RedPointController.instance:unregisterRedPoint(self._termbeginsRedPoint)
	RedPointController.instance:unregisterRedPoint(self._crossDressRedPoint)
	RedPointController.instance:unregisterRedPoint(self._btnSeasonCollectCardRedPoint)
	--RedPointController.instance:unregisterRedPoint(self:getGo("2/btnFood/redPoint"))

	FuncUnlockFacade.instance:unregGO(FuncIds.Recharge, self.btnStore.gameObject)
	FuncUnlockFacade.instance:unregGO(FuncIds.Lottery, self.btnLottery.gameObject)
	FuncUnlockFacade.instance:unregGO(FuncIds.RubyMember, self.btnRubyMember.gameObject)
	FuncUnlockFacade.instance:unregGO(FuncIds.Recharge, self._btnTurntable.gameObject)
	FuncUnlockFacade.instance:unregGO(FuncIds.Recharge, self._btnTurtleLottery.gameObject)
	GlobalDispatcher:removeListener(GlobalNotify.OnServerRefresh, self._onServerRefresh, self)
	ClockMgr.instance:removeListener(ClockMgr.TickHour, self._onHourUpdate, self)
    GlobalDispatcher:removeListener(GlobalNotify.OnGetFuncUnlockInfo, self._onGetFuncUnlockInfo, self)
	self:popupGift_destroyUI()
	
	--版本更新福利
	if self._btnUpgradeReward then
		UpgradeRewardModel.instance:unregisterData("hasRewards", self._onUpgradeRewardChanged, self)
		UpgradeRewardModel.instance:unregisterData("lastGetTime", self._onUpgradeRewardChanged, self)
	end
	if self._btnBigActGuide then
		RedPointController.instance:unregisterRedPoint(self._btnBigActGuide, {"NewYearGuideFirstClick"})
	end
end

function HUDComp_ButtonGroupL:logClick(iconId)
	LogService.instance:logClick(iconId)
end

function HUDComp_ButtonGroupL:_onRedPointPush(name, flag)
	-- print("HUDComp_ButtonGroupL:_onRedPointPush", name, flag)
	if name == "ActivityWinterFirst" then
		self:_updateActiveBtn()
	end
	if name == "Act284RedPoint" then
		self:_updateTimeLimitBtns()
		self:_updateTimeLimitExpand()
	end
end

function HUDComp_ButtonGroupL:_checkNewbieTaskFinish()
	if TaskFacade.instance:isNewbieTaskFinish() then
		GlobalDispatcher:removeListener(GlobalNotify.OnTaskFinish, self._checkNewbieTaskFinish, self)
	end
	self:checkPosterAndStore()
end

--检查商店和海报的new标识
function HUDComp_ButtonGroupL:checkPosterAndStore()
	if not (TaskFacade.instance:isNewbieTaskFinish()) then
		goutil.setActive(self._posterRedGo, false)
		goutil.setActive(self._storeRedGo, false)
		return
	end
end

function HUDComp_ButtonGroupL:onClickStore()
	self:logClick(160)
	RechargeFacade.showRechargeView()
end

-- function HUDComp_ButtonGroupL:_onClickNewspaper()
-- 	local allNwspaper = AobiNewspaperConfig.getAllNewspaperDefines()
-- 	goutil.setActive(self._newspaperRedGo, false)
-- 	goutil.setActive(self.btnNewspaper.gameObject, false)
-- 	LocalStorage.instance:setValue(StorageKey.NewspaperRedPoint, #allNwspaper)
-- 	local list = AobiNewspaperConfig.getAllNewspaperDefines()
-- 	ViewMgr.instance:open("NewspaperDetail", list[#list])
-- end

function HUDComp_ButtonGroupL:_onClickRubyMember()
	self:logClick(168)
	ViewMgr.instance:open("RubyMemberPage")
end

function HUDComp_ButtonGroupL:_onClickTurntable()
	RedPointController.instance:setLastClick("TURNTABLE_FirstClick")
	FengHuaTurntableController.instance:openView()
end

function HUDComp_ButtonGroupL:_onClickTurtleLottery()
	RedPointController.instance:setLastClick("TurtleLottery_FirstClick")
	TurtleLotteryHelper.openLotteryView()
end

function HUDComp_ButtonGroupL:_onClickNoviceChallenge()
	ViewMgr.instance:open("SevenDayActivePanel")
end

function HUDComp_ButtonGroupL:onClickPoster()
	self:logClick(137)
	-- goutil.setActive(self._posterRedGo, false)
	ViewMgr.instance:open("ActivityCalendar")
end

function HUDComp_ButtonGroupL:onClickLottery()
	self:logClick(105)
	LotteryController.instance:openLotteryMainView()
end

function HUDComp_ButtonGroupL:onClickFirstRecharge2()
	self:logClick(146)
	RedPointController.instance:setLastClick("FirstRecharge")
	ViewMgr.instance:open("FirstRecharge2")
end

function HUDComp_ButtonGroupL:_updateBtnUpLottery()
	goutil.setActive(
		self._btnLibraryLotteryUp.gameObject,
		ActivityModel.instance:getActivityIsShow(tonumber(LotteryConfig.getLotteryCommonConfig("TravellerUpLotteryId")), false) and 
		FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.Traveller, false)
	)
end

function HUDComp_ButtonGroupL:_updateActiveBtn()
	self:_updateBtnFirstRecharge()
	self:_updateBtnLifeGuide()
	self:_updateBtnUpLottery()
	self:_updateBtnCfg()
	local bOpen = ActivityModel.instance:getActivityIsOpen(GameEnum.ActivityEnum.ACTIVITY_117_SEVEN_DAY_ACTIVE)
	goutil.setActive(self.btnNoviceChallenge.gameObject, bOpen)
	goutil.setActive(
		self._btnTurntable.gameObject,
		ActivityModel.instance:getActivityIsShow(TurnTableConfig.ActivityDefineId)
	)
	goutil.setActive(
		self._btnTurtleLottery.gameObject,
		ActivityModel.instance:getActivityIsShow(Activity461Config.ActId, true)
	)
	-- goutil.setActive(
	-- 	self._btnMultiRecharge.gameObject,
	-- 	false
	-- 	-- ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_193_RECHARGE)
	-- )
	-- if ActivityModel.instance:getActivityIsShow(1358, true) then
	-- 	PredownloadRewardsController.instance:getIsNew(
	-- 		function(isGot)
	-- 			self._btnNewGift.gameObject:SetActive(not isGot)
	-- 		end
	-- 	)
	-- else
	-- 	self._btnNewGift.gameObject:SetActive(false)
	-- end
	if ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_191_INVITE_NEW_PLAYER) then
		Activity191Agent.instance:sendGetAct191InfoRequest(
			function(msg)
				InviteNewbieModel.instance:_setActivityInfo(msg.invitationInfo, msg.bindInfo, msg.userMark)
				goutil.setActive(
					self._btnInviteNewbie.gameObject,
					ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_191_INVITE_NEW_PLAYER) and
						InviteNewbieController.instance:_getUserPostion(UserInfo.getUserLevel()) ~= InviteNewbieNotify.UserPosition.None
				)
			end
		)
	end
	goutil.setActive(self._btnTermbegins.gameObject, ActivityModel.instance:getActivityIsShow(2069, true))
	-- goutil.setActive(self._btnPetWelfare.gameObject, ActivityModel.instance:getActivityIsShow(1263, true))
	-- goutil.setActive(self._btnLanternFestival.gameObject, ActivityModel.instance:getActivityIsShow(1199, true))
	-- goutil.setActive(self._btnGameRoom.gameObject, GameRoomFacade.checkFuncOpen(self._curGameRoomShowingGame))
	-- goutil.setActive(self._btnMura.gameObject, ActivityModel.instance:getActivityIsShow(1243, true))
	-- goutil.setActive(self._btnAnniversary.gameObject, ActivityModel.instance:getActivityIsShow(1524, true))
	-- goutil.setActive(self._btnStarMatchSignup.gameObject, ActivityModel.instance:getActivityIsShow(1452, true))
	RedPointController.instance:setRedIsExist("activity1452Task", not StarMatchShareView.isFinishTask())
	-- self:_updateBtnMusicParty()
	
	goutil.setActive(self._btnSeasonCollectCard.gameObject, ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_405))

	if ActivityModel.instance:getActivityIsShow(429) then
		Activity429Model.instance:resetTaskRed()
	end
	if ActivityModel.instance:getActivityIsShow(425) then
		Activity425Model.instance:resetTaskRed()
	end
end

function HUDComp_ButtonGroupL:_updateBtnFirstRecharge()
	local isShow = ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_464_FIRST_RECHARGE_V4)
	self._btnFirstRecharge2.gameObject:SetActive(isShow)
end

--升级的时候刷新一下专属拉新活动按钮
function HUDComp_ButtonGroupL:_updateBtnInviteNew()
	goutil.setActive(
		self._btnInviteNewbie.gameObject,
		ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_191_INVITE_NEW_PLAYER) and
			InviteNewbieController.instance:_getUserPostion(UserInfo.getUserLevel()) ~= InviteNewbieNotify.UserPosition.None
	)
end

function HUDComp_ButtonGroupL:_updateBtnLifeGuide()
	self.btnLifeGuide.gameObject:SetActive(ActivityModel.instance:getActivityIsShow(111))
end

function HUDComp_ButtonGroupL:_updateBtnSweetFood()
	-- goutil.setActive(
	-- 	self._btnSweetFood.gameObject,
	-- 	FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.FreeRace, false) and FreeRaceFacade.instance:isCanGetRace()
	-- )
	self:_updateTimeLimitBtns()
	self:_updateTimeLimitExpand()
end

-- function HUDComp_ButtonGroupL:_updateBtnMusicParty(procedure)
-- 	local active = MusicPartyController.instance:isCanShowEntranceIcon()
-- 	local isCanShowAnimation = MusicPartyController.instance:isShowHudIconAnimation()
-- 	goutil.setActive(self._btnMusicParty.gameObject, active)
-- 	goutil.setActive(self._btnMusicPartyEffect.gameObject, isCanShowAnimation)
-- 	if isCanShowAnimation then
-- 		self._btnMusicPartyAnimation:Play()
-- 	else
-- 		self._btnMusicPartyAnimation:Stop()
-- 	end
-- end

--惊喜签到，按钮状态更新
function HUDComp_ButtonGroupL:_updateBtnSurpriseSignIn()
	-- goutil.setActive(
	-- 	self._btnSurpriseSignIn.gameObject,
	-- 	SurpriseSignInController.instance:getIsCanShowHudIcon()
	-- )
	self:_updateTimeLimitBtns()
	self:_updateTimeLimitExpand()
end

--推广活动，按钮状态刷新
function HUDComp_ButtonGroupL:_updateBtnAdvertisement()
	local isShow = ActivityModel.instance:getActivityIsShow(1687, true)
	local isUnlock = FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.EcoPark)
	self._btnAdvertisement.gameObject:SetActive(isShow and isUnlock)
end

-- 实例化游戏大厅当前展示在hud上的游戏的红点
function HUDComp_ButtonGroupL:initCurGameRoomRedPoint()
	local isClicked = GameRoomRedPointMgr.instance:isClicked(self._curGameRoomShowingGame)
	local isTaskReward = RedPointController.instance:getRedIsExist("activity449")
	local isXiaoXiaoLeRed = RedPointController.instance:getRedIsExist("activity448")
	goutil.setActive(self._btnGameRoomRedPoint, not isClicked or isTaskReward or isXiaoXiaoLeRed)
end

function HUDComp_ButtonGroupL:onClickLifeGuide()
	self:logClick(148)
	RedPointController.instance:setLastClick("LifeGuide")
	ViewMgr.instance:open("LifeGuideView")
end

-- function HUDComp_ButtonGroupL:onClickMultiRecharge()
-- 	ActivityFacade.instance:open(30)
-- end

function HUDComp_ButtonGroupL:onClickSweetFood()
	self:logClick(186)
	ViewMgr.instance:open("FreeRace")
end

function HUDComp_ButtonGroupL:onClickMondayGift()
	MondayGiftController.instance:open()
end

function HUDComp_ButtonGroupL:onClickWeekendGift()
	ActivityFacade.instance:open(57)
end

function HUDComp_ButtonGroupL:onClickReturn()
	ViewMgr.instance:open("AobiReturnPanel")
end

function HUDComp_ButtonGroupL:onClickInviteNewbie()
	InviteNewbieController.instance:openInviteNewbieView()
end

function HUDComp_ButtonGroupL:onClickCombineGame()
	GameMachineJump.JumptoCombineGame()
end

function HUDComp_ButtonGroupL:onClickCashRedEnvelope()
	RedPointController.instance:setLastClick("CashRedEnvelopeFirstClick", ServerTime.now())
	CashRedEnvelopeController.instance:openEntry()
end

-- function HUDComp_ButtonGroupL:onClickHongBaoRain()
-- 	local isOpen, id = NewYearHongBaoController.instance:CheckNowIsOpen()
-- 	if isOpen then
-- 		ViewMgr.instance:open("NewYearHongBaoRainView", id)
-- 	end
-- end

-- function HUDComp_ButtonGroupL:onClickPetWelfare()
-- 	PetWelfareFacade.gotoPetWelfare(1263)
-- end

-- function HUDComp_ButtonGroupL:onClickLanternFestival()
-- 	RedPointController.instance:setLastClick("LanternFestival", ServerTime.now())
-- 	FullMoonFacade.goToFullMoon(1199)
-- end

function HUDComp_ButtonGroupL:onClickGameRoom()
	LogService.instance:logClick(173)
	GameRoomRedPointMgr.instance:setClicked(self._curGameRoomShowingGame)
	-- GameRoomFacade.openEntryPanel(self._curGameRoomShowingGame)
	-- GameRoomFacade.walkToTriggerAndOpenEntryPanel(self._curGameRoomShowingGame)
	-- ViewMgr.instance:open("GameMachineView")
	GameRoomFacade.openGameMachinePanel()
end

-- function HUDComp_ButtonGroupL:onClickMura()
-- 	QixiSigninFacade.gotoQixiSignin(QixiSigninFacade.ActivityIdList[1])
-- end

function HUDComp_ButtonGroupL:onClickAdvertisement()
	-- RedPointController.instance:setRedIsExist("EcologyParkAds", false)
	RedPointController.instance:setLastClick("EcologyParkAds")
	ViewMgr.instance:open("MiaoMiaoWuTaskMianView")
end

-- function HUDComp_ButtonGroupL:onClickMusicParty()
-- 	MusicPartyController.instance:onClickHudEntrance()
-- end

function HUDComp_ButtonGroupL:onClickTermbegins()
	RedPointController.instance:setLastClick("Termbegins")
	ViewMgr.instance:open("NewTermbeginsPanel")
end

function HUDComp_ButtonGroupL:onClickSurpriseSignIn()
	self:logClick(185)
	SurpriseSignInController.instance:openView()
end

--五点刷新
function HUDComp_ButtonGroupL:_onServerRefresh()
	-- self:_initNewspaperRedPoint()
end

function HUDComp_ButtonGroupL:_onHourUpdate()
	self:_onUpgradeRewardChanged()
end

function HUDComp_ButtonGroupL:popupGift_buildUI()
	self._timer = TimerWrapper.New()
	self._queue = Queue.New()
	self._parent = self:getGo("2")
	self._btnGiftBag = self:getGo("2/btnGiftBag")
	self._btnGiftBag:SetActive(false)
	self._btnDict = {}
	self._goodsList = {}
end

function HUDComp_ButtonGroupL:popupGift_destroyUI()
	self._timer:removeAllTimer()
	RechargeController.instance:unregisterLocalNotify(RechargeNotify.AddPopupGift, self.popupGift_addBtn, self)
	RechargeController.instance:unregisterLocalNotify(RechargeNotify.RemovePopupGift, self.popupGift_removeBtn, self)
	self._queue:clear()
	self._queue = nil
	self._btnDict = nil
	self._goodsList = nil
	self._timer = nil
end

function HUDComp_ButtonGroupL:popupGift_onEnter()
	RechargeController.instance:registerLocalNotify(RechargeNotify.AddPopupGift, self.popupGift_addBtn, self)
	RechargeController.instance:registerLocalNotify(RechargeNotify.RemovePopupGift, self.popupGift_removeBtn, self)
	local allMo = RechargeModel.instance:getAllPopupGoods()
	local count = math.min(3, #allMo)
	for i = 1, count do
		self:popupGift_addBtn(allMo[i])
	end
end

function HUDComp_ButtonGroupL:aobiReturnParty_onEnter()
	-- self._goTimeLimit_Expand_ReturnParty:SetActive(false)
	if not UserInfo.isReturn then
		return
	end
	if self._partyStartTime == nil then
		--Backflow2Agent.instance:sendGetBackflowPartyProgressRequest(
		--	function(msg)
		--		self._partyStartTime = msg.partyStartTime
		--		self._isGetPartyReward = msg.isGetReward
		--		self:aobiReturnParty_onEnter()
		--		self:_updateTimeLimitBtns()
		--		self:_updateTimeLimitExpand()
		--	end
		--)
		return
	end
	if self._partyStartTime > 0 then
		self:_onAobiReturnPartyTick()
		AobiReturnController.instance:registerLocalNotify(
			"AobiReturnTaskController_OnGetPartyEndReward",
			self._onTakePartyEndGift,
			self
		)
		if self._partyStartTime + TimeUtil.SceondsPerHour > ServerTime.now() then
			self._timer:setTimer(1, self._onAobiReturnPartyTick, self, true)
		end
	else
		AobiReturnController.instance:registerLocalNotify("OnAotiReturnPartyCreate", self._createReturnParty, self)
	end
end

function HUDComp_ButtonGroupL:_onTakePartyEndGift()
	print("_onTakePartyEndGift")
	AobiReturnController.instance:unregisterLocalNotify(
		"AobiReturnTaskController_OnGetPartyEndReward",
		self._onTakePartyEndGift,
		self
	)
	self._isGetPartyReward = true
	self:_onAobiReturnPartyTick()
	self:_updateTimeLimitBtns()
	self:_updateTimeLimitExpand()
end

function HUDComp_ButtonGroupL:_createReturnParty()
	AobiReturnController.instance:unregisterLocalNotify("OnAotiReturnPartyCreate", self._createReturnParty, self)
	self._partyStartTime = ServerTime.now()
	self:aobiReturnParty_onEnter()
end

function HUDComp_ButtonGroupL:_onAobiReturnPartyTick()
	local leftTime = self._partyStartTime + TimeUtil.SceondsPerHour - ServerTime.now()
	if self:_isShowAobiReturnParty() then
		if leftTime > 0 then
			-- self._txtAobiReturn.text = TimeUtil.second2TimeString(leftTime)
		else
			self._timer:removeTimer(self._onAobiReturnPartyTick, self)
			-- self._txtAobiReturn.text = lang("即将结束")
		end
	else
		self:_updateTimeLimitBtns()
		self:_updateTimeLimitExpand()
	end
end

function HUDComp_ButtonGroupL:_isShowAobiReturnParty()
	if not UserInfo.isReturn then
		return false
	end
	local leftTime = self._partyStartTime + TimeUtil.SceondsPerHour - ServerTime.now()
	return self._partyStartTime > 0 and (leftTime > 0 or not self._isGetPartyReward)
end

function HUDComp_ButtonGroupL:_onClickAobiReturn()
	self:logClick(183)
	local leftTime = self._partyStartTime + TimeUtil.SceondsPerHour - ServerTime.now()
	if leftTime <= 0 and not self._isGetPartyReward then
		self._isGetPartyReward = true
		-- self._goTimeLimit_Expand_ReturnParty:SetActive(false)
		AobiReturnTaskController.instance:getPartyEndReward()
	else
		AobiReturnController.instance:gotoPartyScene()
	end
end

-- function HUDComp_ButtonGroupL:_onClickStarMatchSignup()
-- 	RedPointController.instance:setLastClick("activity1452")
-- 	ViewMgr.instance:open("StarMatchShare")
-- end

function HUDComp_ButtonGroupL:popupGift_addBtn(mo)
	if #self._goodsList >= 3 then
		return
	end
	if not mo.define:isOnline() then
		return
	end
	if self._btnDict[mo.goodsId] ~= nil then
		return
	end
	local btnView = self._queue:pop()
	if btnView == nil then
		btnView = goutil.clone(self._btnGiftBag, "btnGiftBag" .. mo.goodsId)
		goutil.addChildToParent(btnView, self._parent)
	end
	btnView:SetActive(true)
	IconLoader.setSpriteToImg(btnView, RechargeConfig.getPopupClient(mo.goodsId).hudIconUrl)
	Framework.ButtonAdapter.Get(btnView):AddClickListener(self.popupGift_onClickIcon, self, mo)
	self._btnDict[mo.goodsId] = btnView
	table.insert(self._goodsList, mo)
	self:popupGift_onTickTime()
	self._timer:removeTimer(self.popupGift_onTickTime, self)
	self._timer:setTimer(1, self.popupGift_onTickTime, self, true)
end

function HUDComp_ButtonGroupL:popupGift_onTickTime()
	for i = #self._goodsList, 1, -1 do
		local mo = self._goodsList[i]
		local leftTime = mo.define.offlineTime:getLeftSecod()
		if leftTime <= 0 then
			self:popupGift_removeBtn(mo)
		elseif self._btnDict[mo.goodsId] ~= nil then
			local btnView = self._btnDict[mo.goodsId]
			goutil.findChildTextComponent(btnView, "txtTime").text = self:popupGift_getTimeDesc(leftTime)
		end
	end
	if #self._goodsList == 0 then
		self._timer:removeTimer(self.popupGift_onTickTime, self)
	end
end

function HUDComp_ButtonGroupL:popupGift_getTimeDesc(leftTime)
	local h = math.floor(leftTime / TimeUtil.SceondsPerHour)
	local m = math.floor((leftTime % TimeUtil.SceondsPerHour) / TimeUtil.SceondsPerMinute)
	local s = math.floor(leftTime % TimeUtil.SceondsPerMinute)

	local formatStr = (h > 0 and "%h:" or "") .. "%m" .. (h <= 0 and ":%s" or "")
	formatStr = string.gsub(formatStr, "%%h", TimeUtil.fillZero(h))
	formatStr = string.gsub(formatStr, "%%m", TimeUtil.fillZero(m))
	formatStr = string.gsub(formatStr, "%%s", TimeUtil.fillZero(s))
	return formatStr
end

function HUDComp_ButtonGroupL:popupGift_removeBtn(mo)
	local btnView = self._btnDict[mo.goodsId]
	if btnView == nil then
		return
	end
	table.removebyvalue(self._goodsList, mo)
	btnView:SetActive(false)
	IconLoader.clearImage(btnView)
	Framework.ButtonAdapter.Get(btnView):RemoveClickListener()
	self._queue:push(btnView)
	self._btnDict[mo.goodsId] = nil
end

function HUDComp_ButtonGroupL:popupGift_onClickIcon(mo)
	RechargeFacade.showPopupGift(mo)
end

function HUDComp_ButtonGroupL:checkMiniProgram()
	local isVisible = true
	local channals = {110001, 210009, 610001, 610002}
	local channelId = SDKManager.getSDKInstacne():getPlatformId()
	local isFormal = false
	for k, v in ipairs(channals) do
		if tostring(v) == channelId then
			isFormal = true
			break
		end
	end
	local info = ActivityModel.instance:getActivityInfo(248)
	if not isFormal then
		isVisible = false
	elseif not info then
		isVisible = false
	elseif not RedPointController.instance:getRedIsExist("MiniProgram2") then
		isVisible = false
	elseif ServerTime.now() < info:getInfoStartTime() then
		isVisible = false
	end
	return isVisible
end

function HUDComp_ButtonGroupL:onClickMiniProgram()
	RedPointController.instance:setLastClick("MiniProgram2")
	SDKManager.getSDKInstacne():showWebBrowser(ActivityConfig.getUGCSetting(1).jumpUrl)
end

function HUDComp_ButtonGroupL:onClickNewGift()
	ViewMgr.instance:open("PredownloadRW")
end

function HUDComp_ButtonGroupL:onClickGameMachine()
	ViewMgr.instance:open("GameMachineView")
end

function HUDComp_ButtonGroupL:onClickActTermBegin()
	RedPointController.instance:setLastClick("CarnivalPartyMap", ServerTime.now())
	ViewMgr.instance:open("CarnivalPartyMapView")
end


function HUDComp_ButtonGroupL:checkActTermBegin()
	return ActivityModel.instance:getActInfoByActId(DreamBoxFacade.carnivalMapActivityId) ~= nil
end
--- 2.7.2开学季活动 end

function HUDComp_ButtonGroupL:checkLandlord()
	local mgr = LandlordActivityMgr.getInstance()
	if mgr and mgr:getArenaIsOpen() then
		return true
	else
		return false
	end
end

--周年庆

function HUDComp_ButtonGroupL:onClickAnniversary()
	RedPointController.instance:setLastClick("Anniversary15HUD", ServerTime.now())
	ViewMgr.instance:open("Anniversary15Main")
end

function HUDComp_ButtonGroupL:checkAnniversaryOpen()
	return ActivityModel.instance:getActInfoByActId(DreamBoxFacade.anniversaryCake) ~= nil
end

--大主题活动事件
function HUDComp_ButtonGroupL:onClickBigActivity()
	ActivityGotoUtil.gotoActivity(ToyHouseGotoUtil.mapActivityId, false)
end

function HUDComp_ButtonGroupL:checkBigAct()
	return ActivityModel.instance:getActivityIsShow(ToyHouseGotoUtil.mapActivityId, true)
end

function HUDComp_ButtonGroupL:checkMiddleActTemp()
	return ActivityModel.instance:getActivityIsShow(DreamBoxFacade.mapActivityId, true)
end

--中主题活动事件
function HUDComp_ButtonGroupL:checkMiddleAct()
	return ActivityModel.instance:getActivityIsShow(429)
end

--现金红包活动事件
function HUDComp_ButtonGroupL:checkCashRedEnvelope()
	return ActivityModel.instance:getActivityIsShow(1982, true)
end

function HUDComp_ButtonGroupL:onClickMiddleActivityTemp()
	DreamBoxFacade.gotoDreamBox(DreamBoxFacade.mapActivityId)
end

function HUDComp_ButtonGroupL:onClickMiddleActivity()
	--Activity435Controller.instance:openGame()
	-- MiddleActFacade.instance:openActivityView()
	-- Activity435Controller.instance:openGame()
	-- DreamBoxFacade.gotoDreamBox(DreamBoxFacade.mapActivityId)
	ViewMgr.instance:open("Act429MainView")
end

--特殊小主题/联动活动事件
function HUDComp_ButtonGroupL:checkSpecialAct()
	-- 3.0巴啦啦联动版本改动
	-- return ActivityModel.instance:getActivityIsShow(DreamBoxFacade.mapActivityId_1, true)
	return ActivityModel.instance:getActivityIsShow(1983, true)
end

function HUDComp_ButtonGroupL:onClickSpecialActivity()
	--小红书活动增加埋点
	-- if ActivityModel.instance:getActivityIsShow(1539, true) then
	-- 	LogService.instance:logClick(174)
	-- end
	-- DreamBoxFacade.gotoDreamBox(DreamBoxFacade.mapActivityId_1)
	CCTVFacade.gotoCCTV(1983)
end

function HUDComp_ButtonGroupL:onClickMileStone()
	ViewMgr.instance:open("SeaMileStoneMainView")
end

---精细化运营礼包
function HUDComp_ButtonGroupL:initPersonalizedGiftBag()
	print("initPersonalizedGiftBag")
	PersonalizedGiftBagController.instance:registerLocalNotify(RechargeNotify.PersonalizedGiftBagUpdate, self.setPersonalizedGiftBtn, self)
	self.personalizedGiftBag = self:getBtn("2/btnGift")
	self.personalizedGiftRedPoint = self:getGo("2/btnGift/redPoint")
	self.personalizedGiftBag:AddClickListener(self.onClickPersonalizedGiftBag, self)
	self:setPersonalizedGiftBtn()
end

function HUDComp_ButtonGroupL:setPersonalizedGiftBtn()
	print("update PersonalizedGiftBag")
	self.personalizedGiftBag.gameObject:SetActive(PersonalizedGiftBagController.instance:hasGiftBag())
	self.personalizedGiftRedPoint:SetActive(LocalStorage.instance:getValue(StorageKey.PersonalizedGiftBagIcon) ~= PersonalizedGiftBagController.instance:getActivityId())
end

function HUDComp_ButtonGroupL:onClickPersonalizedGiftBag()
	LocalStorage.instance:setValue(StorageKey.PersonalizedGiftBagIcon, PersonalizedGiftBagController.instance:getActivityId())
	PersonalizedGiftBagController.instance:showActivity()
end
---精细化运营礼包end

--版本更新福利
function HUDComp_ButtonGroupL:_onUpgradeRewardChanged()
	local isTime = ActivityModel.instance:getActivityIsShow(447)
	if not isTime then
		self._btnUpgradeReward.gameObject:SetActive(false)
		return
	end
	local value = UpgradeRewardModel.instance:getValue("hasRewards")
	local lastGetTime = UpgradeRewardModel.instance:getValue("lastGetTime")
	local isShow = value or (lastGetTime ~= 0 and (ServerTime.now() - lastGetTime) < TimeUtil.SceondsPerDay)
	local redPoint = self:getGo("2/btnUpgradeReward/redPoint")
	redPoint.gameObject:SetActive(value)
	self._btnUpgradeReward.gameObject:SetActive(isShow)
end

function HUDComp_ButtonGroupL:_onClicknUpgradeReward()
	local isTime = ActivityModel.instance:getActivityIsShow(447)
	if isTime then 
		ViewMgr.instance:open("UpgradeRewardsView")
	end
end

--财神爷start
function HUDComp_ButtonGroupL:_updateBtnWealthGod()
	local timerData = ActivityHelper.getActiveTimer(WealthGodActivityConfig.getOpenData())
	local timer = timerData.param - ServerTime.now()
	if timerData.state == 1 then
		SceneTimer:removeTimer(self.startWealthGod, self)
		SceneTimer:setTimer(timer, self.startWealthGod, self,false)
		self._btnWealthGodGo:SetActive(false)
	elseif timerData.state == 2 then
		SceneTimer:removeTimer(self.endWealthGod, self,false)
		SceneTimer:setTimer(timer, self.endWealthGod, self,false)
		self._btnWealthGodGo:SetActive(true)
	elseif timerData.state == 0 then
		self._btnWealthGodGo:SetActive(false)
	end
end

function HUDComp_ButtonGroupL:startWealthGod()
	self:_updateBtnWealthGod()
end

function HUDComp_ButtonGroupL:endWealthGod()
	self:_updateBtnWealthGod()
end
function HUDComp_ButtonGroupL:onClickWealthGod()
	ViewMgr.instance:open("WealthGodGoToView")
end

function HUDComp_ButtonGroupL:_onClickLongStreetBanqut()
	ViewMgr.instance:open("LongStreetBanquetActView")
end

function HUDComp_ButtonGroupL:onClickLandlord()
	RedPointController.instance:setLastClick("activity439Arena")
    LandlordPanel.openTab = 2
    GameRoomFacade.openEntryPanel(GameEnum.GameRoomType.DOU_DIZHU)
end

--财神爷end
---基类里面还提供了若干跟ViewComponent一样的方法,例如getGo(), registerNotify(), localNotify()等, 详情参考基类代码
return HUDComp_ButtonGroupL
