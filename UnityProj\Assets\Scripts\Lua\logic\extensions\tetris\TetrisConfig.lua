module("logic.extensions.tetris.TetrisConfig", package.seeall)

local TetrisConfig = class("TetrisConfig")
local act278_level = ConfigLoader.New("act278_level")
local act278_commconfig = ConfigLoader.New("act278_commconfig")
local tetris_room_define = ConfigLoader.New("tetris_room_define")
local act278_battle_reward = ConfigLoader.New("act278_battle_reward")
local act278_target_reward = ConfigLoader.New("act278_target_reward")
local act278_win_count_reward = ConfigLoader.New("act278_win_count_reward")
local skillConfig = ConfigLoader.New("act278_skill")

local activityId = 2454

TetrisConfig.gainLimitId = 16

--初始化游戏玩法数值
function TetrisConfig.initConfigs()
	local levelId = GameCentreModel.instance:getMapId()
	local levelCfg = TetrisConfig.getLevelConfigById(levelId)
	TetrisNotify.downNormalAddSpeedIntervalTime = levelCfg.dIntervalTime
	TetrisNotify.downNormalAddSpeedRateTime = levelCfg.decreaseTime
	TetrisNotify.downNormalStepMaxTime = levelCfg.stepDownMaxTime
	TetrisNotify.downNormalStepMinTime = levelCfg.stepDownMinTime
	TetrisNotify.needHasFixTime = levelCfg.needHasFixTime
	TetrisNotify.initialBlockRowCount = levelCfg.initialBlockRowCount
	TetrisNotify.punishInterval = levelCfg.punishInterval or 0
	TetrisNotify.punishRowCount = levelCfg.punishRowCount or 1
	TetrisNotify.punishEmptyCount = levelCfg.punishEmptyCount or 3
	TetrisNotify.TowerAttackInterval = tonumber(TetrisConfig.getCommonCfgByKey("TowerAttackInterval"))
	TetrisNotify.downSpeedUpMultiple = tonumber(TetrisConfig.getCommonCfgByKey("Act278DownSpeedUpMultiple"))
	TetrisNotify.leftNormalStepTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278LeftNormalStepTime"))
	TetrisNotify.leftSpeedUpMultiple = tonumber(TetrisConfig.getCommonCfgByKey("Act278LeftSpeedUpMultiple"))
	TetrisNotify.syncGameInfoTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278SyncGameInfoTime"))
	TetrisNotify.clearRowEffectTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278ClearRowEffectTime"))
	TetrisNotify.punishEffectFlyTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278PunishEffectFlyTime"))
	TetrisNotify.punishFlyEffectDisappearTime =
		tonumber(TetrisConfig.getCommonCfgByKey("Act278PunishFlyEffectDisappearTime"))
	TetrisNotify.punishEffectBurstTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278PunishEffectBurstTime"))
	TetrisNotify.shapeFixTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278ShapeFixTime"))
	TetrisNotify.BlackHoleFixTime = tonumber(TetrisConfig.getCommonCfgByKey("BlackHoleFixTime"))
	TetrisNotify.shapeMaxFixTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278ShapeMaxFixTime"))
	TetrisNotify.fixEffectShowTime = tonumber(TetrisConfig.getCommonCfgByKey("Act278FixEffectShowTime"))
	TetrisNotify.blackHoleSwallowCount = tonumber(TetrisConfig.getCommonCfgByKey("blackHoleSwallowCount"))
	TetrisNotify.bulletGainScore = tonumber(TetrisConfig.getCommonCfgByKey("Act278BulletGainScore")) or 1
	TetrisNotify.BlackHoleScore = tonumber(TetrisConfig.getCommonCfgByKey("Act278BlackHoleScore")) or 1
	local scoreList = string.split(TetrisConfig.getCommonCfgByKey("Act278ClearRowScoreList"), ",")
	local punishList = string.split(TetrisConfig.getCommonCfgByKey("Act278ClearRowsToPunish"), ",")
	TetrisNotify.clearRowsToPunish = {}
	TetrisNotify.clearRowScoreList = {}
	for i = 1, #scoreList do
		table.insert(TetrisNotify.clearRowScoreList, tonumber(scoreList[i]))
		table.insert(TetrisNotify.clearRowsToPunish, tonumber(punishList[i]))
	end
end

--活动id
function TetrisConfig.getActivityId()
	local info = ActivityModel.instance:getActivityInfo(GameEnum.ActivityEnum.ACTIVITY_278)
	if info then
		activityId = info:getActivityId()
	end
	return activityId
end

function TetrisConfig.getPreloadResList()
	return {}
end
--游戏模式相关配置
function TetrisConfig.getAllRoomDefines()
	local gameType = GameEnum.GameRoomType.TETRIS
	local configs = tetris_room_define:getConfig()[gameType]
	local rooms = {}
	for i = 1, #configs do
		if configs[i].roomType ~= TetrisNotify.gameMode.standalone then
			table.insert(rooms, configs[i])
		end
	end
	return rooms
end
function TetrisConfig.getRoomDefine(roomType)
	local configs = TetrisConfig.getAllRoomDefines()
	for i = 1, #configs do
		if configs[i].roomType == roomType then
			return configs[i]
		end
	end
	return nil
end
function TetrisConfig.getMapSceneId()
	return 61
end
function TetrisConfig.getWaitSceneId()
	return 73
end

--获取所有关卡配置
function TetrisConfig.getAllLevelConfig()
	return act278_level:getConfig()[TetrisConfig.getActivityId()]
end
--获取关卡配置
function TetrisConfig.getLevelConfigById(id)
	return act278_level:getConfig()[TetrisConfig.getActivityId()][id]
end

--获取结算数据
function TetrisConfig.getBattleRewardByWinType(winType)
	return act278_battle_reward:getConfig()[TetrisConfig.getActivityId()][winType]
end

--获取所有目标奖励配置
function TetrisConfig.getAllTargetReward()
	local configs = act278_target_reward:getConfig()[TetrisConfig.getActivityId()]
	return configs
end
--通过关卡id获取目标奖励配置
function TetrisConfig.getTargetRewardByLevelId(levelId)
	local configs = act278_target_reward:getConfig()[TetrisConfig.getActivityId()]
	local tempConfigs = {}
	for i = 1, #configs do
		if configs[i].levelId == levelId then
			table.insert(tempConfigs, configs[i])
		end
	end
	return tempConfigs
end

function TetrisConfig.getSkillLockLevel(skillId)
	local levels = TetrisConfig.getLevelsByType(1)
	for i = 1, #levels do
		if levels[i].unlockSkill == skillId then
			return levels[i]
		end
	end
end

function TetrisConfig.getLevelsByType(type)
	local allLevel = TetrisConfig.getAllLevelConfig()
	local levels = {}
	for i = 1, #allLevel do
		if allLevel[i].gameRoomType == type then
			table.insert(levels, allLevel[i])
		end
	end
	return levels
end

--获取胜场奖励配置
function TetrisConfig.getAllWinReward()
	local configs = act278_win_count_reward:getConfig()[TetrisConfig.getActivityId()]
	return configs
end

--杂项配置
function TetrisConfig.getCommonCfgByKey(key)
	local configs = act278_commconfig:getConfig()[TetrisConfig.getActivityId()]
	return configs[key].value
end

function TetrisConfig.getSkillById(id)
	return skillConfig:getConfig()[id]
end

function TetrisConfig.getAllSkill()
	return skillConfig:getConfig().dataList
end

------------------ 选关界面 ---------------------
function TetrisConfig.getTargetRewardByRewardId(rewardId)
	local cfg = act278_target_reward:getConfig()[TetrisConfig.getActivityId()][rewardId]
	return cfg
end

function TetrisConfig.getWinRewardById(matchRewardId)
	return TetrisConfig.getAllWinReward()[matchRewardId]
end

function TetrisConfig.getLevelCfgByUnlockSkillId(skillId)
	local levelCfgs = TetrisConfig.getAllLevelConfig()
	for i=1,#levelCfgs do
		if levelCfgs[i].unlockSkill == skillId then
			return levelCfgs[i]
		end
	end
	return nil
end
------------------------------------------------

return TetrisConfig
