module("logic.extensions.activity.flyingchess.view.FlyingChessReadyView", package.seeall)

local FlyingChessReadyView = class("FlyingChessReadyView",ViewComponent)

function FlyingChessReadyView:ctor()
    FlyingChessReadyView.super.ctor(self)
end

function FlyingChessReadyView:buildUI()
    self._teamTips = self:getGo("teamTips")

    self.teamIcons = {}
    for i = 1, 4 do
        self.teamIcons[i] = self:getGo("teamTips/img_" .. i)
    end

    self.counterIcons = {}
	for i = 1, 5 do
		self.counterIcons[i] = self:getGo("teamTips/colahelppanel/txtCounter/img" .. i)
	end
	self.aniAdapter = Framework.AnimationAdapter.GetFrom(self.mainGO, "teamTips/colahelppanel/txtCounter")

    self.scrollGo = self:getGo("selectPlayerGo/list")
    self.scrollRect = self.scrollGo:GetComponent(typeof(UnityEngine.UI.ScrollRect))
    self.selectPlayerName = self:getText("selectPlayerGo/txtName")
    self.selectPlayerGo = self:getGo("selectPlayerGo")
    goutil.setActive(self.selectPlayerGo, false)
    self._endItem = self:getGo("selectPlayerGo/endItem")
    self.selectPlayerItem = self:getGo("selectPlayerGo/item")
    self.selectPlayerItemParent = self:getGo("selectPlayerGo/list/content")

end

function FlyingChessReadyView:bindEvents()

end

function FlyingChessReadyView:unbindEvents()

end

function FlyingChessReadyView:onEnter()
    local params = self:getOpenParam()
    self.callbackHandler = params[1]
    self.userId = params[2] or UserInfo.userId
    self.nickName = params[3] or UserInfo.nickname
    self.counter = params[4] or 3
    local selfPosIdx = FlyingChessGameModel.instance:getSelfPosIdx()

    for i = 1, #self.teamIcons do
		self.teamIcons[i]:SetActive(selfPosIdx == i)
	end
    for i = 1, #self.counterIcons do
		self.counterIcons[i]:SetActive(false)
	end
    goutil.setActive(self._teamTips, true)
	self:update()
	settimer(1, self.update, self, true)
end

function FlyingChessReadyView:onExit()
    removetimer(self.update, self)
    if self.selectTween then
        self.selectTween:Kill()
		self.selectTween = nil
    end
end

function FlyingChessReadyView:update()
	if self.counter > 0 then
		if self.counter < #self.counterIcons then
			self.counterIcons[self.counter + 1]:SetActive(false)
		end
		self.counterIcons[self.counter]:SetActive(true)
		self.aniAdapter:Play("colahelppanel_djs", true)
		SoundManager.instance:playEffect(141044)
	else
        self:showSelectPlayer()
	end
	self.counter = self.counter - 1
end

function FlyingChessReadyView:showSelectPlayer()
    removetimer(self.update, self)
    goutil.setActive(self._teamTips, false)
    self:checkWhosTrun()
end

-------- 抽取优先抽牌 ----------------------------------------
function FlyingChessReadyView:checkWhosTrun()
    -- SoundManager.instance:playEffect(141937)
    local roundCount = 5
    self.selectGos = {}
    local players = GameCentreModel.instance:getAllPlayer()
    --初始化头像
    for i=1, roundCount do
        for userId, playerInfo in pairs(players) do
            local selectPgo = {}
            selectPgo.go = goutil.clone(self.selectPlayerItem)
            goutil.addChildToParent(selectPgo.go, self.selectPlayerItemParent)
            selectPgo.selectbg = goutil.findChild(selectPgo.go, "select")
            goutil.setActive(selectPgo.selectbg, false)  
            selectPgo.imgHead = goutil.findChild(selectPgo.go, "imgHead")
            goutil.setActive(selectPgo.go, true)
            table.insert(self.selectGos, selectPgo)

            HeadPortraitHelper.instance:setHeadPortraitWithUserId(selectPgo.imgHead, playerInfo.id)
        end
    end
    -- local endItem = goutil.clone(self._endItem)
    -- goutil.setActive(endItem, true)
    -- goutil.addChildToParent(endItem, self.selectPlayerItemParent)
    SoundManager.instance:playEffect(142169)
    self.selectPlayerName.text = lang("选择中...")
    goutil.setActive(self.selectPlayerGo, true)
    self.cacheSelect = 0
    self.lastID = #self.selectGos
    local whosTurnSpeed = 1
    self.selectTween = DOTweenHelper.UpdateValue(1, self.lastID, whosTurnSpeed, self.showSelect, self):SetEase(DG.Tweening.Ease.OutQuart)
    self.selectTween:OnComplete(function()
        print("PumpTurtle==>:OnComplete")
		self.selectPlayerName.text = self.nickName
        self:onEndSelect()
	end)
end
 

function FlyingChessReadyView:showSelect(index)
    self.scrollRect.horizontalNormalizedPosition =  index / self.lastID
    if self.cacheSelect ~= math.round(index) then
        self.cacheSelect = math.round(index)
        local oldSelect = self.cacheSelect - 1
        if oldSelect >= 1 then
            goutil.setActive(self.selectGos[oldSelect].selectbg, false)
        end
        goutil.setActive(self.selectGos[self.cacheSelect].selectbg, true)
    end
end

function FlyingChessReadyView:onEndSelect()
    settimer(1, function()
        if self.callbackHandler then
            self.callbackHandler()
        end
        self:close()
    end, nil, false)
end

return FlyingChessReadyView