-- {excel:H回流活动(新).xlsx, sheetName:export_回流小岛变化}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_backflow_change", package.seeall)

local title = {tab=1,id=2,title=3,desc=4,img=5,PVUrl=6,startTime=7,endTime=8,gotoParams=9,params=10,goFuncUnlockCheck=11}

local dataList = {
	{1, 1, "熊次元", "", "hgl_img_1_005", "https://www.bilibili.com/video/BV13cKBzZEpG/?spm_id_from=333.337.search-card.all.click&vd_source=e0ddcef6e3a328d9fb51eb47c5d0ec8f", "2025-07-03T05:00:00", "2025-07-23T23:59:00", {activityId=2476}, nil, nil},
	{1, 2, "牵花念", "", "hgl_img_1_004", "", "2025-07-17T05:00:00", "2024-07-31T05:00:00", {activityId=2496}, nil, nil},
	{2, 1, "次元航线", "在小画家收纳盒里沉睡已久的纸飞机，在这个狂欢之夜，载着奥比们前往快乐的新次元，参加永不落幕的狂欢盛会！", "hgl_img_201", "", "2025-07-03T05:00:00", "2025-07-23T23:59:00", {activityId=2430}, nil, nil},
	{2, 2, "真假派对", "奥比们将通过打牌的方式进行演技大比拼！甩出身份牌，力证自己就是店主本人，与其他玩家进行心理博弈战！最终在博弈中获胜的奥比，将获得大家的认可，赢得最终胜利！", "hgl_img_202", "", "2025-07-03T05:00:00", "2025-07-23T23:59:00", {activityId=2448}, nil, nil},
	{2, 3, "开心食肆", "作为影视城的重点店铺之一，开心食肆喜迎八方食客，热情的掌柜发射小人为大家准备了各种美食，欢迎大家前来品尝！", "hgl_img_203", "", "2025-01-22T05:00:00", "2025-02-12T23:59:00", {activityId=2265}, nil, nil},
	{2, 4, "仲夏花开", "为了种出最独一无二的牵牛花，小奥比忙不停！", "hgl_img_204", "", "2025-07-17T05:00:00", "2024-07-31T05:00:00", {activityId=2509}, nil, nil},
	{2, 5, "花灯寻游", "为了庆祝花灯节，影视城也进行了一番特别的改造，那些点缀在街道上的漂亮花灯，都藏着祝福与灯谜，猜中灯谜，收获花灯奖励！", "hgl_img_205", "", "2025-02-10T05:00:00", "2025-02-21T05:00:00", {activityId=2284}, nil, nil},
	{2, 6, "心愿美味", "大家都想在派对上品尝美味，快来制作美食和饮料，帮大家完成这个小小心愿吧！", "hgl_img_206", "", "2024-07-18T05:00:00", "2024-08-02T05:00:00", {activityId=2002}, nil, nil},
}

local t_backflow_change = {
	[1] = {
		[1] = dataList[1],
		[2] = dataList[2],
	},
	[2] = {
		[1] = dataList[3],
		[2] = dataList[4],
		[3] = dataList[5],
		[4] = dataList[6],
		[5] = dataList[7],
		[6] = dataList[8],
	},
}

t_backflow_change.dataList = dataList
local mt
mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_backflow_change