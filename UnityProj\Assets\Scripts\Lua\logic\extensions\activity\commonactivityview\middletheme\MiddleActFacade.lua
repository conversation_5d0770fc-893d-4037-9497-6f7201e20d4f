module("logic.extensions.activity.commonactivityview.middletheme.MiddleActFacade", package.seeall)

local MiddleActFacade = class("MiddleActFacade")

function MiddleActFacade:activityGotoHandler(activityId)
    return MiddleThemeModel.instance:goToActivity(activityId)
end

function MiddleActFacade:openActivityView()
    -- local setting = MiddleThemeModel.instance:getSetting(500)
    -- ViewMgr.instance:open("MeddleActView",setting)
    
end

function MiddleActFacade:closeActivityView()
    ViewMgr.instance:close("MeddleActView")
end

MiddleActFacade.instance = MiddleActFacade.New()
return MiddleActFacade
