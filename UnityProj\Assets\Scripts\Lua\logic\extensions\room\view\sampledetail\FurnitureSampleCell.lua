module("logic.extensions.room.view.sampledetail.FurnitureSampleCell", package.seeall)

local FurnitureSampleCell = class("FurnitureSampleCell", ListBinderCell, BaseLuaComponent)

function FurnitureSampleCell:Awake()
	self._iconContainer = self:getGo("icon")
	self._occupyGo = self:getGo("occupyGo")
	self._replaceGo = self:getGo("replaceGo")
	self._btnResetReplace = self:getBtn("replaceGo/btn")
	self._btnResetReplace:AddClickListener(self._onClickReset, self)
	self._replaceableGo = self:getGo("replaceableGo")
	self._btnReplace = self:getBtn("replaceableGo/btn")
	self._btnReplace:AddClickListener(self._onClickReplace, self)
	self._txtOccupy = self:getText("occupyGo/Text")
	self._btnOccupyReplace = self:getBtn("occupyGo/btn")
	self._btnOccupyReplace:AddClickListener(self._onClickReplace, self)
end

function FurnitureSampleCell:OnDestroy()
	CommonIconMgr.instance:returnCommonIcon(self._icon)
end

function FurnitureSampleCell:onSetMo(mo)
	self._mo = mo
	-- self._occupyGo:SetActive(not mo.hasFree and mo.hasCollect)
	-- self._replaceGo:SetActive(mo.hasReplace and not mo.hasCollect)
	local itemId = mo.itemId
	self._occupyGo:SetActive(mo.isOccupyed and mo.sortState ~= FurnitureSampleView.SortState.state1)
	self._btnOccupyReplace.gameObject:SetActive(mo.isOccupyed and mo.sortState == FurnitureSampleView.SortState.state2)
	
	self._replaceGo:SetActive(mo.sortState == FurnitureSampleView.SortState.state1)
	self._replaceableGo:SetActive(not mo.isOccupyed and mo.sortState == FurnitureSampleView.SortState.state2)
	
	if self._icon == nil then
		self._icon = CommonIconMgr.instance:fetchCommonIconWithParams({id = mo.itemId, rareness = true, parent = self._iconContainer, widthAndHeght = 88})
		self._icon:addIconClickListener(self._conClickSelf, self)
	else
		-- self._icon:buildData({id = mo.itemId})
		self._icon:buildData({id = mo.replaceId})
		
	end
	-- self._icon:setAlpha(mo.hasCollect and 1 or 0.3)
	if mo.isOccupyed or mo.sortState == FurnitureSampleView.SortState.state3 or mo.sortState == FurnitureSampleView.SortState.state1 then
		self._icon:setAlpha(1)
		
	else
		self._icon:setAlpha(0.3)
	end
	self:_canPlayInCurScene()
end

function FurnitureSampleCell:_canPlayInCurScene()
	if self._mo.isOccupyed then
		if self._mo.sortState == FurnitureSampleView.SortState.state1 then
			self._txtOccupy.text = lang("已替换")
		else
			self._txtOccupy.text = lang("被占用")
		end
	end
	if not HouseModel.instance:isInHouseOrIsland() then return end
	local canPlaceType = FurnitureConfig.getFurnitureDefine(self._mo.itemId):getEnableAreas()
	local houseType = HouseModel.instance:getUserProperty(HouseUserProperty.HouseType, 0)
	local islandType = HouseModel.instance:getUserProperty(HouseUserProperty.IslandType, 0)
	local allAreas = RoomConfig.getAllAreaCnofigs(houseType, islandType)
	local canPlace = false
	for i, area in ipairs(allAreas) do
		for _, areaType in ipairs(area.SubType) do
			if table.indexof(canPlaceType, areaType) ~= false then
				canPlace = true
				break
			end
		end
	end
	if not canPlace then
		self._occupyGo:SetActive(true)
		self._txtOccupy.text = lang("无法摆放")
	end
end

function FurnitureSampleCell:_conClickSelf()
	-- if self._listView._canFocusFurniture and self._mo.hasCollect and not self._mo.hasFree then
	--     ViewFacade.showTgNotice(9, handler(self._findFurniture, self))
	-- else
	--     ViewFacade.showItemInfo(self._mo.itemId)
	-- end
	if self._listView._canFocusFurniture and self._mo.isOccupyed then
		-- if self._mo.hasShare then
		-- 	FlyTextManager.instance:showFlyText(lang("该家具已共享到街区"))
		-- elseif self._listView.shareFurniture ~= nil then
		-- 	if self._mo.hasShare == nil then
		-- 		self:_onGetShareFurniture()
		-- 	else
				ViewFacade.showTgNotice(9, handler(self._findFurniture, self))
			-- end
		-- else
		-- 	ShareStreetAgent.instance:sendGetPersonShareFurnitureCountRequest(handler(self._onGetShareFurniture, self))
		-- end
	else
		-- ViewFacade.showItemInfo(self._mo.itemId)
		ViewFacade.showItemInfo(self._mo.replaceId)
	end
end

function FurnitureSampleCell:_onGetShareFurniture(furnitureCounts)
	if furnitureCounts ~= nil then
		self._listView.shareFurniture = furnitureCounts
	end
	self._mo.hasShare = false
	local find = false
	for i, info in ipairs(self._listView.shareFurniture) do
		if info.id == self._mo.itemId and info.num - info.useCount > 0 then
			self._mo.hasShare = true
			FlyTextManager.instance:showFlyText(lang("该家具已共享到街区"))
			info.useCount = info.useCount + 1
			find = true
			break
		end
	end
	if not find then
		ViewFacade.showTgNotice(9, handler(self._findFurniture, self))
	end
end

function FurnitureSampleCell:_findFurniture(isOk)
	if not isOk then return end
	RoomController.instance:localNotify(RoomNotifyName.SearchFurnitureById, self._mo.itemId)
end

-- 
function FurnitureSampleCell:_onClickReset()
	if self._mo.sortState == FurnitureSampleView.SortState.state1 then
		RoomController.instance:localNotify(RoomNotifyName.OnSampleReplaceSingleFurniture, self._mo.id, self._mo.itemId)
	end
end

function FurnitureSampleCell:_onClickReplace()
	if self._mo.sortState == FurnitureSampleView.SortState.state2 then
		local curMissCount = self._listView.curMissCountHash[self._mo.itemId] or 0
		local occupyCount = 0
		local list = self._listView._curDataList
		for k, v in ipairs(list) do
			if v.isOccupyed and(self._mo.itemId == v.itemId) then
				occupyCount = occupyCount + 1
			end
		end
		curMissCount = self._mo.isOccupyed and occupyCount or(curMissCount - occupyCount)
		
		if self._listView:getCurItemReplaceCount(self._mo.itemId) > 0 then
			ViewMgr.instance:open("SampleReplace", self._mo.id, self._mo.itemId, curMissCount, self._listView._replaceMap[self._mo.itemId], self._mo.isOccupyed)
		end
	end
end

return FurnitureSampleCell 