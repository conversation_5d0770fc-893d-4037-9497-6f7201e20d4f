-- {excel:261星际大赛2预热.xlsx, sheetName:export_星际大赛2队伍配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_star_match2_team", package.seeall)

local title = {teamId=1,name=2,npcs=3,slogan=4,introduction=5,bubble=6,npcBubble=7,iconName=8}

local dataList = {
	{1, "王牌搭档队", {17200001,88,161}, "“王牌搭档，乘风破浪！”", "这里有你最合拍最可靠的王牌搭档，别再犹豫快快加入，和他们一起扬帆起航，向着冠军领奖台进发！", "一起经历最棒的大冒险，然后赢得冠军吧！", "王牌搭档，乘风破浪！", "icon1"},
	{2, "鬼灵天才队", {17200002,58,164}, "“用计谋满足野心，只有天才配得冠军！”", "张扬、优雅、充满野心，鬼灵天才队的目标只有冠军。他们等待着志同道合的伙伴加入队伍，并肩作战。", "就等你的加入了，冠军，我们势在必得。", "用计谋满足野心，只有天才配得冠军！", "icon2"},
	{3, "萌心闪闪队", {17200003,25,98}, "“萌心闪闪，能量无限，宇宙最强！”", "可爱的萌心中蕴藏着无限的能量与未来，他们期待着你的加入，一起去争取属于你们的胜利！", "有你这么厉害的队员加入，今年的冠军肯定是我们！", "萌心闪闪，能量无限，宇宙最强！", "icon3"},
	{4, "魔幻仙子队", {17200004,2,145}, "“奇妙魔法守护，仙子无所不能！”", "小叮当认为每个奥比都是拥有神奇魔法的小仙子，只要大家团结一心，奇妙的魔法就会帮助梦想实现。", "我们一起努力施展奇妙魔法，目标是冠军奖杯！", "奇妙魔法守护，仙子无所不能！", "icon4"},
}

local t_star_match2_team = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
}

t_star_match2_team.dataList = dataList
local mt
if StarMatch2TeamDefine then
	mt = {
		__cname =  "StarMatch2TeamDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or StarMatch2TeamDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_star_match2_team