--auto gen by editor
module("logic.extensions.turntable.agent.TurnTableAgent", package.seeall)

local TurnTableAgent = class("TurnTableAgent", BaseAgent)

function TurnTableAgent:sendGetAct161InfoRequest(handler)
	TaskUtil.BlockClick(true, "sendGetAct161InfoRequest")
	local req = Activity161Extension_pb.GetAct161InfoRequest()
	self.getAct161InfoHandler = handler
	self:sendMsg(req)
end

function TurnTableAgent:handleGetAct161InfoReply(status, msg)
    TaskUtil.BlockClick(false, "sendGetAct161InfoRequest")
    if status == 0 then
        local ids = msg.ids
        local drawprocess = msg.drawProcess
        local totallotterycount = msg.totalLotteryCount
        local tiredvalue = msg.tiredValue
        local drawtiredrewardprocess = msg.drawTiredRewardProcess
        local drawscorerewardsprocess = msg.drawScoreRewardsProcess
        self.getAct161InfoHandler(ids, drawprocess, totallotterycount, tiredvalue, drawtiredrewardprocess,
            drawscorerewardsprocess)
    else
        DialogHelper.showErrorMsg(status)
    end
    self.getAct161InfoHandler = nil
end

function TurnTableAgent:sendAct161LotteryRequest(ten, handler)
	TaskUtil.BlockClick(true, "sendAct161LotteryRequest")
	local req = Activity161Extension_pb.Act161LotteryRequest()
	req.ten = ten
	self.act161LotteryHandler = handler
	self:sendMsg(req)
end

function TurnTableAgent:handleAct161LotteryReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161LotteryRequest")
	if status == 0 then
		local ids = msg.ids
		self.act161LotteryHandler(ids, msg.processIds)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.act161LotteryHandler = nil
end

function TurnTableAgent:sendAct161DrawProcessRequest(process, index, handler)
	TaskUtil.BlockClick(true, "sendAct161DrawProcessRequest")
	local req = Activity161Extension_pb.Act161DrawProcessRequest()
	req.process = process
	req.index = index
	self.act161DrawProcessHandler = handler
	self:sendMsg(req)
end

function TurnTableAgent:handleAct161DrawProcessReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161DrawProcessRequest")
	if status == 0 then
		local changesetid = msg.changeSetId
		self.act161DrawProcessHandler(changesetid)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.act161DrawProcessHandler = nil
end

function TurnTableAgent:sendAct161ConvertLotteryItemRequest(count, handler)
	TaskUtil.BlockClick(true, "sendAct161ConvertLotteryItemRequest")
	local req = Activity161Extension_pb.Act161ConvertLotteryItemRequest()
	req.count = count
	self.act161ConvertLotteryItemHandler = handler
	self:sendMsg(req)
end

function TurnTableAgent:handleAct161ConvertLotteryItemReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161ConvertLotteryItemRequest")
	if status == 0 then
		local changesetid = msg.changeSetId
		self.act161ConvertLotteryItemHandler(changesetid)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.act161ConvertLotteryItemHandler = nil
end

function TurnTableAgent:sendAct161DrawFatigueRewardRequest(id, handler)
	TaskUtil.BlockClick(true, "sendAct161DrawFatigueRewardRequest")
	local req = Activity161Extension_pb.Act161DrawFatigueRewardRequest()
	req.id = id
	self.act161DrawFatigueRewardHandler = handler
	self:sendMsg(req)
end

function TurnTableAgent:handleAct161DrawFatigueRewardReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161DrawFatigueRewardRequest")
	if status == 0 then
		local changesetid = msg.changeSetId
		self.act161DrawFatigueRewardHandler(changesetid)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.act161DrawFatigueRewardHandler = nil
end


function TurnTableAgent:sendAct161QueryLotteryRecordsRequest(handler)
	TaskUtil.BlockClick(true, "sendAct161QueryLotteryRecordsRequest")
    local req = Activity161Extension_pb.Act161QueryLotteryRecordsRequest()
	self.act161QueryLotteryRecordsHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161QueryLotteryRecordsReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161QueryLotteryRecordsRequest")
    if status == 0 then
		local lotteryrecords = msg.lotteryRecords
		if self.act161QueryLotteryRecordsHandler then
			self.act161QueryLotteryRecordsHandler(lotteryrecords)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161QueryLotteryRecordsHandler = nil
end


function TurnTableAgent:sendAct161DrawLotteryScoreRewardRequest(id,handler)
	TaskUtil.BlockClick(true, "sendAct161DrawLotteryScoreRewardRequest")
    local req = Activity161Extension_pb.Act161DrawLotteryScoreRewardRequest()
	req.id = id
	self.act161DrawLotteryScoreRewardHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161DrawLotteryScoreRewardReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161DrawLotteryScoreRewardRequest")
    if status == 0 then
		local changesetid = msg.changeSetId
		if self.act161DrawLotteryScoreRewardHandler then
			self.act161DrawLotteryScoreRewardHandler(changesetid)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161DrawLotteryScoreRewardHandler = nil
end

function TurnTableAgent:sendAct161GetRedEnvelopeInfoRequest(handler)
	TaskUtil.BlockClick(true, "sendAct161GetRedEnvelopeInfoRequest")
    local req = Activity161Extension_pb.Act161GetRedEnvelopeInfoRequest()
	self.act161GetRedEnvelopeInfoHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161GetRedEnvelopeInfoReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161GetRedEnvelopeInfoRequest")
    if status == 0 then
		local normalstorageinfos = msg.normalStorageInfos
		local exquisitestorageinfos = msg.exquisiteStorageInfos
		local thanksredenvelopeinfos = msg.thanksRedEnvelopeInfos
		local drawnormalcounttoday = msg.drawNormalCountToday
		local drawexquisitecounttoday = msg.drawExquisiteCountToday
		if self.act161GetRedEnvelopeInfoHandler then
			self.act161GetRedEnvelopeInfoHandler(msg)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161GetRedEnvelopeInfoHandler = nil
end

function TurnTableAgent:sendAct161GetRedEnvelopeDrawRecordsRequest(handler)
	TaskUtil.BlockClick(true, "sendAct161GetRedEnvelopeDrawRecordsRequest")
    local req = Activity161Extension_pb.Act161GetRedEnvelopeDrawRecordsRequest()
	self.act161GetRedEnvelopeDrawRecordsHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161GetRedEnvelopeDrawRecordsReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161GetRedEnvelopeDrawRecordsRequest")
    if status == 0 then
		local drawrecords = msg.drawRecords
		if self.act161GetRedEnvelopeDrawRecordsHandler then
			self.act161GetRedEnvelopeDrawRecordsHandler(drawrecords)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161GetRedEnvelopeDrawRecordsHandler = nil
end

function TurnTableAgent:sendAct161GetRedEnvelopeSendStatisticsRequest(handler)
	TaskUtil.BlockClick(true, "sendAct161GetRedEnvelopeSendStatisticsRequest")
    local req = Activity161Extension_pb.Act161GetRedEnvelopeSendStatisticsRequest()
	self.act161GetRedEnvelopeSendStatisticsHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161GetRedEnvelopeSendStatisticsReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161GetRedEnvelopeSendStatisticsRequest")
    if status == 0 then
		local merolesimpleinfo = msg.meRoleSimpleInfo
		local sendnormalcount = msg.sendNormalCount
		local sendexquisitecount = msg.sendExquisiteCount
		local likescount = msg.likesCount
		if self.act161GetRedEnvelopeSendStatisticsHandler then
			self.act161GetRedEnvelopeSendStatisticsHandler(msg)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161GetRedEnvelopeSendStatisticsHandler = nil
end

function TurnTableAgent:sendAct161DrawRedEnvelopeRequest(sendUserUId,redEnvelopeId,redEnvelopeType,handler)
	TaskUtil.BlockClick(true, "sendAct161DrawRedEnvelopeRequest")
    local req = Activity161Extension_pb.Act161DrawRedEnvelopeRequest()
	req.sendUserUId = sendUserUId
	req.redEnvelopeId = redEnvelopeId
	req.redEnvelopeType = redEnvelopeType
	self.act161DrawRedEnvelopeHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161DrawRedEnvelopeReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161DrawRedEnvelopeRequest")
    if status == 0 then
		local changesetid = msg.changeSetId
		if self.act161DrawRedEnvelopeHandler then
			self.act161DrawRedEnvelopeHandler(changesetid)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161DrawRedEnvelopeHandler = nil
end

function TurnTableAgent:sendAct161LikeRedEnvelopeRequest(sendUserUId,redEnvelopeId,redEnvelopeType,handler)
    local req = Activity161Extension_pb.Act161LikeRedEnvelopeRequest()
	req.sendUserUId = sendUserUId
	req.redEnvelopeId = redEnvelopeId
	req.redEnvelopeType = redEnvelopeType
	self.act161LikeRedEnvelopeHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161LikeRedEnvelopeReply(status, msg)
    if status == 0 then
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161LikeRedEnvelopeHandler = nil
end

function TurnTableAgent:sendAct161GetGlobalLotteryRecordsRequest(handler)
	TaskUtil.BlockClick(true, "sendAct161GetGlobalLotteryRecordsRequest")
    local req = Activity161Extension_pb.Act161GetGlobalLotteryRecordsRequest()
	self.act161GetGlobalLotteryRecordsHandler = handler
    self:sendMsg(req)
end

function TurnTableAgent:handleAct161GetGlobalLotteryRecordsReply(status, msg)
	TaskUtil.BlockClick(false, "sendAct161GetGlobalLotteryRecordsRequest")
    if status == 0 then
		local globallotteryrecords = msg.globalLotteryRecords
		if self.act161GetGlobalLotteryRecordsHandler then
			self.act161GetGlobalLotteryRecordsHandler(globallotteryrecords)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act161GetGlobalLotteryRecordsHandler = nil
end

TurnTableAgent.instance = TurnTableAgent.New()

return TurnTableAgent
