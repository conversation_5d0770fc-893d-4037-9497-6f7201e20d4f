module("logic.extensions.room.model.RoomEditModel", package.seeall)

local RoomEditModel = class("RoomEditModel")

local BlockKey = "RoomEditModelBlockKey"

RoomEditModel.Mode = {
	None = 0,	--不在编辑模式
	Edit = 1,	--普通编辑模式
	Buy = 2,	--购买模式
	Paper = 3,	--墙纸编辑模式
	-- Area = 4,	--整理模式（按区域收回）
	Multi = 5,	--多物品模式
	Sample = 6,	--样版
	Search = 7,	--搜索
	Surface = 8,--地形
	Outside = 9,--外景
	Weather = 10,--天气
	ViewTemplate = 11, -- 预览样板模式
	TemplateForFriend = 12, -- 为好友定投样板
	SavingTemplateForFriend = 13, -- 保存好友样板
	ShareStreet = 14,
}

function RoomEditModel:ctor()
	self._mode = RoomEditModel.Mode.None
	self._itemDict = {}
	self._soldDict = {}
	self._buyList = {}
	self._changeModeFun = {}
	self._putToBagDict = {}
	self._onLoadAllFun = CallbackQueue.New()
	self.canEnterEdit = true
	self._loadAllFurniture = false
	--在RoomCompFurniture设置
	self.isBaseMode = false
	self.baseModeType = 0
	--在RoomCompFurniture设置
	RoomController.instance:registerLocalNotify(RoomNotifyName.AllNotInScreenFurnitureLoaded, self._onLoadAllFurniture, self)
end

function RoomEditModel:decItem(id, isShare)
	if not isShare then
		self._putToBagDict[id] =(self._putToBagDict[id] or 0) - 1
	end
	local key = id
	if isShare then
		key = id .. "_share"
	end
	local item = self._itemDict[key]
	if item.num > 0 then
		item.num = item.num - 1
		local realNum = self:getItemNum(id, isShare)
		RoomController.instance:localNotify(RoomNotifyName.OnEditItemNumChange, item.id, realNum, realNum == 0)
		return true
	else
		return false
	end
end

function RoomEditModel:addItem(id, isShare)
	if not isShare then
		self._putToBagDict[id] =(self._putToBagDict[id] or 0) + 1
	end
	--有可能有衣服
	if ItemType.getTypeById(id) == ItemType.FURNITURE then
		local key = id
		if isShare then
			key = id .. "_share"
		end
		local isListChange = not self._itemDict[key] or self._itemDict[key].num == 0
		local item = self._itemDict[key] or Item.New(id, 0)
		item.isShare = isShare
		item.num = item.num + 1
		self._itemDict[key] = item
		RoomController.instance:localNotify(RoomNotifyName.OnEditItemNumChange, item.id, self:getItemNum(id, isShare), isListChange)
	end
	return true
end

function RoomEditModel:getItemNum(id, isShare)
	if not self:isEdit() then
		return ItemService.instance:getItemNum(id)
	end
	local key = id
	if isShare then key = id .. "_share" end
	local item = self._itemDict[key]
	if item then
		local realNum = item.num
		return realNum
	else
		return 0
	end
end

function RoomEditModel:getItemTotalNum(id)
	return self:getItemNum(id, false) + self:getItemNum(id, true)
end

function RoomEditModel:getMyItems()
	local result = {}
	for _, item in pairs(self._itemDict) do
		if ItemType.getTypeById(item.id) == ItemType.FURNITURE and self:getItemNum(item.id, item.isShare) > 0 then
			table.insert(result, item)
		end
	end
	return result
end

function RoomEditModel:setFriendFurnitures(furnitures)
	self._friendFurintures = furnitures
end

function RoomEditModel:setShareFurnitures(shareFurnitures)
	if HouseModel.instance:isInBlock() and shareFurnitures then
		for i, shareFurniture in ipairs(shareFurnitures) do
			local shareItem = Item.New(shareFurniture.furnitureId, shareFurniture.count - shareFurniture.useCount, shareFurniture.count)
			shareItem.isShare = true
			self._itemDict[shareItem.id .. "_share"] = shareItem
		end
	end
end

function RoomEditModel:resetAllItem(resetSale)
	if resetSale == nil then resetSale = true end
	if SceneManager.instance:getEnterParams().friendId ~= nil then
		for i, info in ipairs(self._friendFurintures) do
			self._itemDict[info.furnitureId] = Item.New(info.furnitureId, info.count, info.count)
		end
	else
		self._itemDict = {}
		local myFurList = ItemService.instance:getTotalItems(ItemType.FURNITURE)
		local capacityEnable = HouseModel.instance:getRoomParams(RoomParams.Editor_Capacity_Enable)
		local canUseNum
		for _, item in ipairs(myFurList) do
			if capacityEnable then
				canUseNum = item.num
			else
				local houseMo = HouseModel.instance.houseMo
				canUseNum = item.totalNum - houseMo:getFurnitureModel(UserInfo.userId):getFurnitureUseCount(item.id) - houseMo.roomModel:getPaperUseCount(item.id)
			end
			self._itemDict[item.id] = Item.New(item.id, canUseNum, item.totalNum, item.gainTime, item.expireTime)
		end
		for funcId, idList in pairs(FurnitureConfig.getFuncUnlock2Furniture()) do
			if FuncUnlockFacade.instance:checkIsUnlocked(funcId, false) then
				for _, id in ipairs(idList) do
					self._itemDict[id] = Item.New(id, 1, 1)
				end
			end
		end
		self._putToBagDict = {}
		self:resetSaleFurniture()
	end
end

function RoomEditModel:checkCapacity(checkItems, showTips)
	local capacityEnable = HouseModel.instance:getRoomParams(RoomParams.Editor_Capacity_Enable)
	if not capacityEnable then return true end
	local items = arrayutil.copy(checkItems)
	for i = #items, 1, -1 do
		if items[i].isShare then
			table.remove(items, i)
		end
	end
	local find
	for id, num in pairs(self._putToBagDict) do
		find = false
		for _, item in ipairs(items) do
			if item.id == id then
				item.num = item.num + num
				find = true
				break
			end
		end
		if not find then
			table.insert(items, {id = id, num = num})
		end
	end
	local canAdd = ItemService.instance:canAdd(items)
	if not canAdd and showTips then
		FlyTextManager.instance:showFlyText(lang("背包已满，无法完全收回物品"))
	end
	return canAdd
end

function RoomEditModel:getCapacity()
	local result = 0
	local allItem
	local allHandler = ItemService.instance._handlerMap
	for _, handler in pairs(allHandler) do
		if handler.type == ItemType.FURNITURE then
			result = result + self:_calFurnitureCapacity(self._itemDict)
		elseif handler.type ~= ItemType.EcoPark then
			allItem = handler:getAllItem()
			for _, item in ipairs(allItem) do
				result = result + item:getUsedSpace()
			end
		end
	end
	return result
end

function RoomEditModel:_calFurnitureCapacity(items)
	local result = 0
	for _, item in pairs(items) do
		if item and not item.isShare then
			result = result + item:getUsedSpace()
		end
	end
	return result
end

function RoomEditModel:clear()
	self:changeMode(RoomEditModel.Mode.None)
	self._itemDict = {}
	self._soldDict = {}
	self._buyList = {}
	self.canEnterEdit = true
	self._loadAllFurniture = false
	self._onLoadAllFun:clear()
end

function RoomEditModel:addSaleFurniture(id)
	if self._soldDict[id] then
		self._soldDict[id].num = self._soldDict[id].num + 1
	else
		self._soldDict[id] = {id = id, num = 1}
	end
	local realNum = self:getItemNum(id)
	RoomController.instance:localNotify(RoomNotifyName.OnEditItemNumChange, id, realNum, realNum == 0)
	self:_notifyCurrencyChange()
end

function RoomEditModel:getSoldList()
	local result = {}
	local hasHightQuality = false
	for _, item in pairs(self._soldDict) do
		for i = 1, item.num do
			table.insert(result, item.id)
		end
		if ItemService.instance:getRank(item.id) > 3 then
			hasHightQuality = true
		end
	end
	return result, hasHightQuality
end

function RoomEditModel:getBuyList()
	return self._buyList
end

function RoomEditModel:saveSaleFurniture()
	self:resetSaleFurniture()
end

function RoomEditModel:addBuyFurniture(id)
	table.insert(self._buyList, id)
	self:_notifyCurrencyChange()
end

function RoomEditModel:removeBuyFurniture(id)
	for i = 1, #self._buyList do
		if self._buyList[i] == id then
			table.remove(self._buyList, i)
			break
		end
	end
	self:_notifyCurrencyChange()
end

function RoomEditModel:_notifyCurrencyChange()
	local change = 0
	for _, item in pairs(self._soldDict) do
		change = change + ItemService.instance:getDefine(item.id).gainCoin * item.num
	end
	local buyItem, plant
	for i = 1, #self._buyList do
		buyItem = ItemService.instance:getDefine(self._buyList[i])
		if buyItem:getPart().parentId == FurnitureType.Production then
			plant = HarvestConfig.getDefineBySeedId(buyItem.id)
			change = change - plant.consumeCurrency.count
		end
	end
	if change ~= 0 then
		GlobalDispatcher:dispatch(GlobalNotify.ShowHUDCoinChange, change)
	else
		GlobalDispatcher:dispatch(GlobalNotify.HideHUDCoinChange)
	end
end

function RoomEditModel:getBuyCurrencyList()
	local currencyDict = {}
	local currency
	for _, id in pairs(self._buyList) do
		currency = self:getBuyCurrency(id)
		if currencyDict[currency.id] then
			currencyDict[currency.id].count = currencyDict[currency.id].count + currency.count
		else
			currencyDict[currency.id] = {id = currency.id, count = currency.count}
		end
	end
	return currencyDict
end

function RoomEditModel:resetSaleFurniture()
	self._soldDict = {}
	self._buyList = {}
	self:_notifyCurrencyChange()
end

function RoomEditModel:getBuyCurrency(id)
	if HarvestConfig.isPlant(id) or HarvestConfig.isEarth(id) then
		local config = HarvestConfig.getDefineBySeedId(id)
		return config.consumeCurrency
	end
end

function RoomEditModel:isEdit()
	return not self:checkMode(RoomEditModel.Mode.None)
end

function RoomEditModel:checkMode(mode)
	return self._mode == mode
end

function RoomEditModel.isChange(mode, lastMode, compareMode)
	return mode == compareMode or lastMode == compareMode
end

function RoomEditModel:changeMode(mode, params)
	if self._mode == mode then return true end
	if not self.canEnterEdit and mode > RoomEditModel.Mode.None then
		FlyTextManager.instance:showFlyText(lang("当前不能进入编辑模式!"))
		return false
	end
	if not HouseModel.instance:getRoomParams(RoomParams.Edit_Enable) then return false end
	if self["_canChangeMode" .. mode] then
		if not self["_canChangeMode" .. mode](self, mode, params) then
			return false
		end
	end
	local lastMode = self._mode
	self._lastMode = lastMode
	self._mode = mode
	self:_callModeChange(lastMode, false)
	if self["_mode" .. lastMode] then
		self["_mode" .. lastMode](self, params, false)
	end
	if self["_mode" .. self._mode] then
		self["_mode" .. self._mode](self, params, true)
	end
	self:_callModeChange(self._mode, true)
	RoomController.instance:localNotify(RoomNotifyName.OnEditModeChange, self._mode, lastMode)
	
	GlobalDispatcher:dispatch(GlobalNotify.OnRoomEdit, self:isEdit())
	return true
end

function RoomEditModel:changeToLastMode(defaultMode)
	if self._lastMode == nil then
		if defaultMode ~= nil then
			self:changeMode(defaultMode)
		end
		return
	end
	self:changeMode(self._lastMode)
	self._lastMode = nil
end

function RoomEditModel:_callModeChange(mode, isEnter)
	local funcList = arrayutil.getOrCreateList(self._changeModeFun, mode)
	for _, func in ipairs(funcList) do
		func.handler(func.handlerObj, isEnter)
	end
end

function RoomEditModel:tryEnterEdit(enterEdit, selectType)
	local mode = enterEdit and RoomEditModel.Mode.Edit or RoomEditModel.Mode.None
	return self:changeMode(mode, selectType)
end

function RoomEditModel:registerModeChange(mode, handler, handlerObj)
	local funcList = arrayutil.getOrCreateList(self._changeModeFun, mode)
	table.insert(funcList, {handler = handler, handlerObj = handlerObj})
end

function RoomEditModel:unregisterModeChange(mode, handler, handlerObj)
	local funcList = arrayutil.getOrCreateList(self._changeModeFun, mode)
	for i = #funcList, 1, - 1 do
		if funcList[i].handler == handler and funcList[i].handlerObj == handlerObj then
			table.remove(funcList, i)
		end
	end
end

function RoomEditModel:setCanEnterEdit(canEnter)
	self.canEnterEdit = canEnter
end
-- None = 0,	--不在编辑模式
-- Edit = 1,	--普通编辑模式
-- Buy = 2,		--购买模式
-- Paper = 3,	--墙纸编辑模式
-- Area = 4,	--整理模式（按区域收回）
--非编辑模式
function RoomEditModel:_mode0(params, enter)
	RoomEditorPresentor.closeView()
	self.longPressFurniture = false
	ViewFacade.delayAndCallWhenIdle()
end
--编辑模式
function RoomEditModel:_mode1(selectType, enter)
	if enter then
		SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.EditFurniture, nil, nil, true)
		print("进入编辑：", selectType, debug.traceback())
		RoomEditorPresentor.showView(selectType)
	end
end
--购买模式
function RoomEditModel:_mode2(params, enter)
	self:_mode1(params, enter)
end
--墙纸模式
function RoomEditModel:_mode3(params, enter)
	self:_mode1(params, enter)
	RoomEdit.instance:tryOkCurEditFurniture(false, true)
end
--整理模式
function RoomEditModel:_mode4(params, enter)
	RoomEdit.instance:tryOkCurEditFurniture(false, true)
end
--多物品回收
function RoomEditModel:_mode5(params, enter)
	RoomEdit.instance:tryOkCurEditFurniture(false, true)
end
--样板模式
function RoomEditModel:_mode6(params, enter)
	if not enter then
		RoomController.instance:localNotify(RoomNotifyName.CancelOpFurniture)
	end
end
--地形
function RoomEditModel:_mode8(params, enter)
	self:_mode1(params, enter)
	-- if enter then
	-- 	FlyTextManager.instance:showFlyText(lang("可点击摆放地形，再次点击选中地形可取消摆放"))
	-- end
end

function RoomEditModel:_canChangeMode1(mode, params)
	if not self._loadAllFurniture then
		self:checkFurniturePrepared(function()
			self:changeMode(mode, params)
		end, self)
	end
	return self._loadAllFurniture
end

function RoomEditModel:_onLoadAllFurniture(factory)
	if SceneManager.instance:getCurScene().unitFactory ~= factory then return end
	LoadingMask.instance:close()
	self._loadAllFurniture = true
	self._onLoadAllFun:invoke()
end

function RoomEditModel:checkFurniturePrepared(callBack, target)
	if self._loadAllFurniture then
		callBack(target)
	else
		LoadingMask.instance:show()
		self._onLoadAllFun:add(callBack, target)
	end
	return self._loadAllFurniture
end

RoomEditModel.instance = RoomEditModel.New()
return RoomEditModel 