module("logic.extensions.sharestreet.view.ShareStreetMemberViewPresentor",package.seeall)
---@class ShareStreetMemberViewPresentor
local ShareStreetMemberViewPresentor = class("ShareStreetMemberViewPresentor",ViewPresentor)

function ShareStreetMemberViewPresentor:ctor()
	ShareStreetMemberViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetMemberViewPresentor:dependWhatResources()
	return {"ui/street/streetpeoplemap.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetMemberViewPresentor:buildViews()
	self._view = ShareStreetMemberView.New()
	return {self._view}
end

--- 配置view所在的ui层
function ShareStreetMemberViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end

function ShareStreetMemberViewPresentor:onClickOutside()
	self._view:close()
end


return ShareStreetMemberViewPresentor