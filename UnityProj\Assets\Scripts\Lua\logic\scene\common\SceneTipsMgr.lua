module("logic.scene.common.SceneTipsMgr", package.seeall)
local SceneTipsMgr = class("SceneTipsMgr", SceneComponentBase)

local UpdateInterval = 0.1
function SceneTipsMgr:onInit()
end

function SceneTipsMgr:onEnterScene(sceneId, bornX, bornZ)
	self.scale = 1
	self.isPaused = false
	SceneController.instance:registerLocalNotify(SceneNotify.ShowMoveTarget, self.onUserStartMove, self)
	SceneController.instance:registerLocalNotify(SceneNotify.UserStopMove, self.onUserEndMove, self)
	FriendController.instance:registerLocalNotify(FriendNotify.OnFriendListChange, self.onFriendChange, self)

	-- SceneController.instance:registerLocalNotify(SceneNotify.CameraZoom, self.onCameraZoom, self)
	-- SceneController.instance:registerLocalNotify(SceneNotify.CameraMove, self.onCameraMove, self)
	ViewMgr.instance:addListener(ViewMgr.FullScreenShow, self._handleViewMgrFullScreenShow, self)
	ViewMgr.instance:addListener(ViewMgr.FullScreenHide, self._handleViewMgrFullScreenHide, self)
	self._sceneTips = {}
	-- self._playerGuides = {}
	-- self._guideContainer = self:_createContainer("guide")

	self.unitGuidesMap = {}
	self._unitGuideContainer = self:_createContainer("unitGuides")
	self._mainCamera = CameraTargetMgr.instance:getMainCameraTarget():getCamera()
end

-- Assets/GameAssets/image/happyicon/hapi_img_3.png
function SceneTipsMgr:_createContainer(name)
	local container = goutil.create(name, true)
	goutil.addChildToParent(container, ViewMgr.instance:getRoot(ViewRootType.Hud))
	local containerTrans = container.transform
	containerTrans.anchorMin = Vector2.New(0, 0)
	containerTrans.anchorMax = Vector2.New(1, 1)
	containerTrans:SetAsFirstSibling()
	tfutil.SetSizeDelta(container, 0, 0)
	return container
end

function SceneTipsMgr:onExitScene()
	self.targetArrow = nil
	SceneController.instance:unregisterLocalNotify(SceneNotify.ShowMoveTarget, self.onUserStartMove, self)
	SceneController.instance:unregisterLocalNotify(SceneNotify.UserStopMove, self.onUserEndMove, self)
	GlobalDispatcher:removeListener(GlobalNotify.PlayerEnter, self.onEnterZone, self)
	GlobalDispatcher:removeListener(GlobalNotify.PlayerLeave, self.onExitZone, self)
	FriendController.instance:unregisterLocalNotify(FriendNotify.OnFriendListChange, self.onFriendChange, self)

	-- SceneController.instance:unregisterLocalNotify(SceneNotify.CameraZoom, self.onCameraZoom, self)
	ViewMgr.instance:removeListener(ViewMgr.FullScreenShow, self._handleViewMgrFullScreenShow, self)
	ViewMgr.instance:removeListener(ViewMgr.FullScreenHide, self._handleViewMgrFullScreenHide, self)

	removetimer(self.onUpdateTips, self)
	removetimer(self.hideSceneName, self)
	goutil.destroy(self.sceneNameGO)
	self.sceneNameGO = nil
	if self.playerIcons then
		goutil.destroy(self.playerIcons.go)
		self.playerIcons = nil
	end

	for i = 1, #self._sceneTips do
		goutil.destroy(self._sceneTips[i])
	end
	self._sceneTips = nil

	-- for i = 1, #self._playerGuides do
	-- 	self._playerGuides[i]:dispose()
	-- end
	-- goutil.destroy(self._guideContainer)
	-- self._guideContainer = nil
	-- self._playerGuides = nil

	for k, guides in pairs(self.unitGuidesMap) do
		for k, v in pairs(guides) do
			v:dispose()
		end
	end
	self.unitGuidesMap = nil
	goutil.destroy(self._unitGuideContainer)
end

function SceneTipsMgr:onEnterSceneFinished(sceneId, bornX, bornZ)
	self.targetArrow = CommonRes.instance:getResInstance(CommonResPath.TargetArrow)
	self.targetArrow:SetActive(false)
	goutil.addChildToParent(self.targetArrow, self._scene.stage.overlap)
	local players = self._scene:getUnitMgr():getUnits(self._scene:getPlayerUnitType())
	for i = 1, #players do
		if FriendService.instance:queryIsFriend(players[i].id) then
			self:addPlayerGuide(players[i])
		end
	end
	self:addPlayerGuide(self._scene:getUserPlayer())
	GlobalDispatcher:addListener(GlobalNotify.PlayerEnter, self.onEnterZone, self)
	GlobalDispatcher:addListener(GlobalNotify.PlayerLeave, self.onExitZone, self)

	settimer(0.06, self.onUpdateTips, self)
	self.curTime = 0
	local go = CommonRes.instance:getResInstance(CommonResPath.PlayerIcons)
	-- self:addItemToNameBar(go)
	self.playerIcons = PjAobi.LuaComponent.Add(go, PlayerIcons)
end

function SceneTipsMgr:showPlayerIcons(playerUnit)
	self.playerIcons:show(playerUnit)
end

function SceneTipsMgr:hidePlayerIcons()
	if self.playerIcons ~= nil then
		self.playerIcons:hide()
	end
end

function SceneTipsMgr:showSceneName()
	if SceneManager.instance:getEnterParams().hideSceneName == true then
		return
	end
	self.sceneNameGO = CommonRes.instance:getResInstance(CommonResPath.SceneName)
	goutil.addChildToParent(self.sceneNameGO, ViewMgr.instance:getRoot(ViewRootType.Hud))
	local config = SceneConfig.getSceneConfig(self._scene:getSceneId())
	if HouseModel.instance:isInBlock() then
		goutil.findChildTextComponent(self.sceneNameGO, "Text").text = ShareStreetSceneModel.instance.streetName
	elseif HouseModel.instance:isInIsland() then
		print(HouseModel.instance:getRoleInfo().roleSimpleInfo.islandRacial)
		goutil.findChildTextComponent(self.sceneNameGO, "Text").text =
			lang(
			"{1}-{2}",
			UserConfig.getIslandRacial(HouseModel.instance:getRoleInfo().roleSimpleInfo.islandRacial).name,
			config.name
		)
	else
		goutil.findChildTextComponent(self.sceneNameGO, "Text").text = config.name
	end

	settimer(3, self.hideSceneName, self, false)
	-- local sequence = DG.Tweening.DOTween.Sequence()
	-- local posX = self.sceneNameGO.transform.localPosition.x
	-- tfutil.SetAnchoredX(self.sceneNameGO, posX-200)
	-- sequence:Append(DOTweenHelper.ChangeAlpha(self.sceneNameGO,0,1,0.4))
	-- sequence:Join(self.sceneNameGO.transform:DOAnchorPosX(posX,0.5,false))
	-- sequence:AppendInterval(2)
	-- sequence:Append(DOTweenHelper.ChangeAlpha(self.sceneNameGO,1,0,0.4))
	-- sequence:Join(self.sceneNameGO.transform:DOAnchorPosX(posX + 200,0.5,false))
	-- sequence:OnComplete(function()
	-- 	goutil.destroy(self.sceneNameGO)
	-- end)
end

function SceneTipsMgr:hideSceneName()
	goutil.destroy(self.sceneNameGO)
end

function SceneTipsMgr:_handleViewMgrFullScreenShow()
	-- UpdateBeat:Remove(self.onUpdate, self)
	self.isPaused = true
	-- self._guideContainer:SetActive(false)
	self._unitGuideContainer:SetActive(false)
end

function SceneTipsMgr:_handleViewMgrFullScreenHide()
	self.isPaused = false
	-- self._guideContainer:SetActive(true)
	self._unitGuideContainer:SetActive(true)
	-- UpdateBeat:Add(self.onUpdate, self)
end

function SceneTipsMgr:_handleAllSceneTipsActiveState(state)
	if not self._sceneTips then
		return
	end
	self.isPaused = not state
	for i = 1, #self._sceneTips do
		local sceneIconCls = PjAobi.LuaComponent.Get(self._sceneTips[i], SceneIcon)
		if sceneIconCls then
			if state then
				sceneIconCls.go:SetActive(true)
				sceneIconCls:setVisible(sceneIconCls._isVisible)
			else 
				sceneIconCls.go:SetActive(false)
			end
			
		end
	end
end

function SceneTipsMgr:onUserStartMove(targetPos)
	goutil.setActive(self.targetArrow, true)
	if VirtualCameraMgr.instance:is3DScene() then
		if targetPos.posZ then
			tfutil.SetLPos(self.targetArrow, targetPos.posX,targetPos.posZ, targetPos.posY)
		else
			z = GameUtils.GetPosYOnGround(targetPos.posX, targetPos.posY)
			tfutil.SetLPos(self.targetArrow, targetPos.posX, z, targetPos.posY)
		end
	else
		-- tfutil.SetLXY(self.targetArrow, targetPos.posX, targetPos.posY)
		tfutil.SetLPos(self.targetArrow, targetPos.posX, targetPos.posY, -490)		
	end
end

function SceneTipsMgr:onUserEndMove()
	goutil.setActive(self.targetArrow, false)
end

function SceneTipsMgr:onCameraZoom(value)
	value = math.max(1, value)
	self.scale = value
	local trs = SceneManager.instance.nameContainer.transform
	local count = trs.childCount
	for i = count, 1, -1 do
		local child = trs:GetChild(i - 1).gameObject
		-- if child.activeSelf then
		tfutil.SetScale(child, value)
		-- end
	end
end

function SceneTipsMgr:addSceneTips(go, x, y, z)
	table.insert(self._sceneTips, go)
	goutil.addChildToParent(go, SceneManager.instance.nameContainer)
	local followComp = GameUtils.addSceneFollowComp(go)
	followComp:SetFollowPos(Vector3(x, y, z))
end

function SceneTipsMgr:removeSceneTips(go)
	table.removebyvalue(self._sceneTips, go)
	goutil.destroy(go)
end

function SceneTipsMgr:onEnterZone(userIdList)
	for i = 1, #userIdList do
		if FriendService.instance:queryIsFriend(userIdList[i]) then
			local player = self._scene.unitFactory:getUnit(self._scene:getPlayerUnitType(), userIdList[i])
			self:addPlayerGuide(player)
		end
	end
end

function SceneTipsMgr:onExitZone(userIdList)
	for i = 1, #userIdList do
		if FriendService.instance:queryIsFriend(userIdList[i]) then
			self:removePlayerGuide(userIdList[i])
		end
	end
end

function SceneTipsMgr:onUpdateTips()
	if self.isPaused then
		return
	end
	-- self:updatePlayerGuide()
	self:updateUnitGuide()
end

local function isInRect(x, y, rect)
	return x > rect.x1 and x < rect.x2 and y > rect.y1 and y < rect.y2
end

function SceneTipsMgr:createUnitGuides(unit, unitType, name, url, bgName)
	if self.unitGuidesMap[unitType] == nil then
		self.unitGuidesMap[unitType] = {}
	end
	return self:_addUnitGuide(unit, unitType, name, url, bgName)
end

function SceneTipsMgr:_addUnitGuide(unit, unitType, name, url, bgName)
	self:removeUnitGuide(unitType, name)

	local guide =
		CommonGuide.New(
		unit,
		function(icon)
			IconLoader.setSpriteToImg(icon, url)
		end,
		self._unitGuideContainer,
		bgName
	)
	self.unitGuidesMap[unitType][name] = guide
	return guide
end

function SceneTipsMgr:updateUnitGuide()
	for k, guides in pairs(self.unitGuidesMap) do
		for i, g in pairs(guides) do
			g:update()
		end
	end
end


--这个方法已经弃用
function SceneTipsMgr:hideUnitGuide(visible)
	for k, guides in pairs(self.unitGuidesMap) do
		for i, g in pairs(guides) do
			g:setVisible(visible)
		end
	end
end

function SceneTipsMgr:showUnitGuide(visible)
	if self.unitGuidesMap then
		for k, guides in pairs(self.unitGuidesMap) do
			for i, g in pairs(guides) do
				g:setVisible(visible)
			end
		end
	end
end

function SceneTipsMgr:removeUnitGuide(unitType, name)
	if self.unitGuidesMap[unitType] == nil then
		return
	end
	if self.unitGuidesMap[unitType][name] == nil then
		return
	end
	self.unitGuidesMap[unitType][name]:dispose()
	self.unitGuidesMap[unitType][name] = nil
end

function SceneTipsMgr:addPlayerGuide(player)
	local playerUnitType = self._scene:getPlayerUnitType()
	if self.unitGuidesMap[playerUnitType] == nil then
		self.unitGuidesMap[playerUnitType] = {}
	end
	self:removeUnitGuide(playerUnitType, player.id)
	local isMe = player.id == UserInfo.userId
	local bgName
	if isMe then
		bgName = "bg1"
	else
		bgName = "bg2"
	end
	local guide =
		CommonGuide.New(
		player,
		function(icon)
			HeadPortraitHelper:setHeadPortraitWithUserId(icon, player.id)
		end,
		self._unitGuideContainer,
		bgName
	)
	self.unitGuidesMap[playerUnitType][player.id] = guide
	return guide
end

function SceneTipsMgr:removePlayerGuide(id)
	-- 	for i = 1, #self._playerGuides do
	-- 		if self._playerGuides[i].player.id == id then
	-- 			self._playerGuides[i]:dispose()
	-- 			table.remove(self._playerGuides, i)
	-- 			break
	-- 		end
	-- 	end
	self:removeUnitGuide(self._scene:getPlayerUnitType(), id)
end

-- function SceneTipsMgr:updatePlayerGuide()
-- 	for i = 1, #self._playerGuides do
-- 		self._playerGuides[i]:update()
-- 	end
-- end

function SceneTipsMgr:onFriendChange(userId, type)
	printInfo("onFriendChange", userId, type)
	if type == FriendNotify.OnFriendAdd then
		local player = self._scene:getUnitMgr():getUnit(self._scene:getPlayerUnitType(), userId)
		if player then
			self:addPlayerGuide(player)
		end
	elseif type == FriendNotify.OnFriendRemove then
		self:removePlayerGuide(userId)
	end
end

return SceneTipsMgr
