module("logic.extensions.tetris.activity.TetrisAnniversaryActViewPresentor",package.seeall)
---@class TetrisAnniversaryActViewPresentor
local TetrisAnniversaryActViewPresentor = class("TetrisAnniversaryActViewPresentor",ViewPresentor)
TetrisAnniversaryActViewPresentor.Url_view = "ui/tetris/tetrislevelview_1.prefab"

function TetrisAnniversaryActViewPresentor:ctor()
	TetrisAnniversaryActViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function TetrisAnniversaryActViewPresentor:dependWhatResources()
	return {TetrisAnniversaryActViewPresentor.Url_view}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function TetrisAnniversaryActViewPresentor:buildViews()
	return {
		TetrisAnniversaryActView.New(),
		ActivityEndTimeUltimateComp.New(GameEnum.ActivityEnum.ACTIVITY_278, "txtGo/txtResidueTime", true, true),
	}
end

--- 配置view所在的ui层
function TetrisAnniversaryActViewPresentor:attachToWhichRoot()
	return ViewRootType.FullScreen
end


return TetrisAnniversaryActViewPresentor