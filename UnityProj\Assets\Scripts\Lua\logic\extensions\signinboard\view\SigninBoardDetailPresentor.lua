module("logic.extensions.signinboard.view.SigninBoardDetailPresentor",package.seeall)

local SigninBoardDetailPresentor = class("SigninBoardDetailPresentor", ViewPresentor)

SigninBoardDetailPresentor.effectPaths = {
    [1] = "prefabs/chat/worldhorn/effect/effect_trumpetpanel_star_01.prefab",
    [3] = "prefabs/chat/worldhorn/effect/effect_trumpetpanel_flower_03.prefab",
    [5] = "prefabs/chat/worldhorn/effect/effect_trumpetpanel_anni_01.prefab",
}

--- 配置view需要的资源列表
function SigninBoardDetailPresentor:dependWhatResources()
	return {
        "ui/anniversary/signinboard/signinboard.prefab",
        SigninBoardDetailPresentor.effectPaths[1],
        SigninBoardDetailPresentor.effectPaths[3],
        SigninBoardDetailPresentor.effectPaths[5],
    }
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function SigninBoardDetailPresentor:buildViews()
    local noshows = {
        "btnclose","btnhelp","prgGo/btnStart","prgGo/btnAward"
    }
    self.shareView = ExternalSharePanel.New(146, noshows)
	return {
        SigninBoardDetailView.New(),
        ActivityEndTimeUltimateComp.New(GameEnum.ActivityEnum.ACTIVITY_466, "prgGo/txtTime", true, true),
        self.shareView
    }
end

--- 配置view所在的ui层
function SigninBoardDetailPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end

return SigninBoardDetailPresentor