module("logic.extensions.turntable.subtable.perfume.PlanetTurnTablePresentor", package.seeall)
local PlanetTurnTablePresentor = class("PlanetTurnTablePresentor", ViewPresentor)


PlanetTurnTablePresentor.Url_View = "ui/planetturntable/planetturntableview.prefab"

function PlanetTurnTablePresentor:ctor()
    PlanetTurnTablePresentor.super.ctor(self)
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function PlanetTurnTablePresentor:buildViews()
    self.itemChangeHelper = ItemChangeHelper.New()
    local views = {DragonTurnTableMainView.New(FengHuaTurntableController.instance.actInfo),
                   DreamFactoryProgressAwardListView.New(FengHuaTurntableController.instance), PlanetTurnTableAnimView.New(),
                   self.itemChangeHelper, StarCookTurnTableAllRecordComponent.New(),DragonTurnTableExRewardsComponent.New()}
    return views
end

--- 配置view需要的资源列表
function PlanetTurnTablePresentor:dependWhatResources()
    local urls = {PlanetTurnTablePresentor.Url_View, DreamFactoryViewPresentor.Award_Item}
    return urls
end

--- 配置view所在的ui层
function PlanetTurnTablePresentor:attachToWhichRoot()
    return ViewRootType.Popup
end

return PlanetTurnTablePresentor
