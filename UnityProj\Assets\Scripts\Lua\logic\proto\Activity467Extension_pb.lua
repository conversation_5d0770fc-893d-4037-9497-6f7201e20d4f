-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity467Extension_pb", package.seeall)


local tb = {}
tb.ACT467TREASURESTATUS_ENUM = protobuf.EnumDescriptor()
tb.ACT467TREASURESTATUS_INITIAL_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT467TREASURESTATUS_OPEN_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT467TREASURESTATUS_GAIN_REWARD_ENUMITEM = protobuf.EnumValueDescriptor()
GETACT467INFOREQUEST_MSG = protobuf.Descriptor()
OPENACT467TREASUREREPLY_MSG = protobuf.Descriptor()
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT467TREASURENO_MSG = protobuf.Descriptor()
tb.ACT467TREASURENO_TREASUREID_FIELD = protobuf.FieldDescriptor()
tb.ACT467TREASURENO_STATUS_FIELD = protobuf.FieldDescriptor()
GETACT467INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT467INFOREPLY_TREASURES_FIELD = protobuf.FieldDescriptor()
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD = protobuf.FieldDescriptor()
OPENACT467TREASUREREQUEST_MSG = protobuf.Descriptor()
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD = protobuf.FieldDescriptor()
GAINACT467TREASUREREWARDREPLY_MSG = protobuf.Descriptor()
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
GAINACT467TREASUREREWARDREQUEST_MSG = protobuf.Descriptor()
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD = protobuf.FieldDescriptor()

tb.ACT467TREASURESTATUS_INITIAL_ENUMITEM.name = "INITIAL"
tb.ACT467TREASURESTATUS_INITIAL_ENUMITEM.index = 0
tb.ACT467TREASURESTATUS_INITIAL_ENUMITEM.number = 0
tb.ACT467TREASURESTATUS_OPEN_ENUMITEM.name = "OPEN"
tb.ACT467TREASURESTATUS_OPEN_ENUMITEM.index = 1
tb.ACT467TREASURESTATUS_OPEN_ENUMITEM.number = 1
tb.ACT467TREASURESTATUS_GAIN_REWARD_ENUMITEM.name = "GAIN_REWARD"
tb.ACT467TREASURESTATUS_GAIN_REWARD_ENUMITEM.index = 2
tb.ACT467TREASURESTATUS_GAIN_REWARD_ENUMITEM.number = 2
tb.ACT467TREASURESTATUS_ENUM.name = "Act467TreasureStatus"
tb.ACT467TREASURESTATUS_ENUM.full_name = ".Act467TreasureStatus"
tb.ACT467TREASURESTATUS_ENUM.values = {tb.ACT467TREASURESTATUS_INITIAL_ENUMITEM,tb.ACT467TREASURESTATUS_OPEN_ENUMITEM,tb.ACT467TREASURESTATUS_GAIN_REWARD_ENUMITEM}
GETACT467INFOREQUEST_MSG.name = "GetAct467InfoRequest"
GETACT467INFOREQUEST_MSG.full_name = ".GetAct467InfoRequest"
GETACT467INFOREQUEST_MSG.filename = "Activity467Extension"
GETACT467INFOREQUEST_MSG.nested_types = {}
GETACT467INFOREQUEST_MSG.enum_types = {}
GETACT467INFOREQUEST_MSG.fields = {}
GETACT467INFOREQUEST_MSG.is_extendable = false
GETACT467INFOREQUEST_MSG.extensions = {}
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.full_name = ".OpenAct467TreasureReply.changeSetId"
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.number = 1
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.index = 0
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.label = 1
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.has_default_value = false
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.default_value = 0
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.type = 5
tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD.cpp_type = 1

OPENACT467TREASUREREPLY_MSG.name = "OpenAct467TreasureReply"
OPENACT467TREASUREREPLY_MSG.full_name = ".OpenAct467TreasureReply"
OPENACT467TREASUREREPLY_MSG.filename = "Activity467Extension"
OPENACT467TREASUREREPLY_MSG.nested_types = {}
OPENACT467TREASUREREPLY_MSG.enum_types = {}
OPENACT467TREASUREREPLY_MSG.fields = {tb.OPENACT467TREASUREREPLY_CHANGESETID_FIELD}
OPENACT467TREASUREREPLY_MSG.is_extendable = false
OPENACT467TREASUREREPLY_MSG.extensions = {}
tb.ACT467TREASURENO_TREASUREID_FIELD.name = "treasureId"
tb.ACT467TREASURENO_TREASUREID_FIELD.full_name = ".Act467TreasureNO.treasureId"
tb.ACT467TREASURENO_TREASUREID_FIELD.number = 1
tb.ACT467TREASURENO_TREASUREID_FIELD.index = 0
tb.ACT467TREASURENO_TREASUREID_FIELD.label = 2
tb.ACT467TREASURENO_TREASUREID_FIELD.has_default_value = false
tb.ACT467TREASURENO_TREASUREID_FIELD.default_value = 0
tb.ACT467TREASURENO_TREASUREID_FIELD.type = 5
tb.ACT467TREASURENO_TREASUREID_FIELD.cpp_type = 1

tb.ACT467TREASURENO_STATUS_FIELD.name = "status"
tb.ACT467TREASURENO_STATUS_FIELD.full_name = ".Act467TreasureNO.status"
tb.ACT467TREASURENO_STATUS_FIELD.number = 2
tb.ACT467TREASURENO_STATUS_FIELD.index = 1
tb.ACT467TREASURENO_STATUS_FIELD.label = 2
tb.ACT467TREASURENO_STATUS_FIELD.has_default_value = false
tb.ACT467TREASURENO_STATUS_FIELD.default_value = nil
tb.ACT467TREASURENO_STATUS_FIELD.enum_type = ACT467TREASURESTATUS_ENUM
tb.ACT467TREASURENO_STATUS_FIELD.type = 14
tb.ACT467TREASURENO_STATUS_FIELD.cpp_type = 8

ACT467TREASURENO_MSG.name = "Act467TreasureNO"
ACT467TREASURENO_MSG.full_name = ".Act467TreasureNO"
ACT467TREASURENO_MSG.filename = "Activity467Extension"
ACT467TREASURENO_MSG.nested_types = {}
ACT467TREASURENO_MSG.enum_types = {}
ACT467TREASURENO_MSG.fields = {tb.ACT467TREASURENO_TREASUREID_FIELD, tb.ACT467TREASURENO_STATUS_FIELD}
ACT467TREASURENO_MSG.is_extendable = false
ACT467TREASURENO_MSG.extensions = {}
tb.GETACT467INFOREPLY_TREASURES_FIELD.name = "treasures"
tb.GETACT467INFOREPLY_TREASURES_FIELD.full_name = ".GetAct467InfoReply.treasures"
tb.GETACT467INFOREPLY_TREASURES_FIELD.number = 1
tb.GETACT467INFOREPLY_TREASURES_FIELD.index = 0
tb.GETACT467INFOREPLY_TREASURES_FIELD.label = 3
tb.GETACT467INFOREPLY_TREASURES_FIELD.has_default_value = false
tb.GETACT467INFOREPLY_TREASURES_FIELD.default_value = {}
tb.GETACT467INFOREPLY_TREASURES_FIELD.message_type = ACT467TREASURENO_MSG
tb.GETACT467INFOREPLY_TREASURES_FIELD.type = 11
tb.GETACT467INFOREPLY_TREASURES_FIELD.cpp_type = 10

tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.name = "gainedTreasureIds"
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.full_name = ".GetAct467InfoReply.gainedTreasureIds"
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.number = 2
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.index = 1
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.label = 3
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.has_default_value = false
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.default_value = {}
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.type = 5
tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD.cpp_type = 1

GETACT467INFOREPLY_MSG.name = "GetAct467InfoReply"
GETACT467INFOREPLY_MSG.full_name = ".GetAct467InfoReply"
GETACT467INFOREPLY_MSG.filename = "Activity467Extension"
GETACT467INFOREPLY_MSG.nested_types = {}
GETACT467INFOREPLY_MSG.enum_types = {}
GETACT467INFOREPLY_MSG.fields = {tb.GETACT467INFOREPLY_TREASURES_FIELD, tb.GETACT467INFOREPLY_GAINEDTREASUREIDS_FIELD}
GETACT467INFOREPLY_MSG.is_extendable = false
GETACT467INFOREPLY_MSG.extensions = {}
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.name = "treasureId"
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.full_name = ".OpenAct467TreasureRequest.treasureId"
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.number = 1
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.index = 0
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.label = 2
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.has_default_value = false
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.default_value = 0
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.type = 5
tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD.cpp_type = 1

OPENACT467TREASUREREQUEST_MSG.name = "OpenAct467TreasureRequest"
OPENACT467TREASUREREQUEST_MSG.full_name = ".OpenAct467TreasureRequest"
OPENACT467TREASUREREQUEST_MSG.filename = "Activity467Extension"
OPENACT467TREASUREREQUEST_MSG.nested_types = {}
OPENACT467TREASUREREQUEST_MSG.enum_types = {}
OPENACT467TREASUREREQUEST_MSG.fields = {tb.OPENACT467TREASUREREQUEST_TREASUREID_FIELD}
OPENACT467TREASUREREQUEST_MSG.is_extendable = false
OPENACT467TREASUREREQUEST_MSG.extensions = {}
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.full_name = ".GainAct467TreasureRewardReply.changeSetId"
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GAINACT467TREASUREREWARDREPLY_MSG.name = "GainAct467TreasureRewardReply"
GAINACT467TREASUREREWARDREPLY_MSG.full_name = ".GainAct467TreasureRewardReply"
GAINACT467TREASUREREWARDREPLY_MSG.filename = "Activity467Extension"
GAINACT467TREASUREREWARDREPLY_MSG.nested_types = {}
GAINACT467TREASUREREWARDREPLY_MSG.enum_types = {}
GAINACT467TREASUREREWARDREPLY_MSG.fields = {tb.GAINACT467TREASUREREWARDREPLY_CHANGESETID_FIELD}
GAINACT467TREASUREREWARDREPLY_MSG.is_extendable = false
GAINACT467TREASUREREWARDREPLY_MSG.extensions = {}
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.name = "treasureId"
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.full_name = ".GainAct467TreasureRewardRequest.treasureId"
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.number = 1
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.index = 0
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.label = 2
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.has_default_value = false
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.default_value = 0
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.type = 5
tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD.cpp_type = 1

GAINACT467TREASUREREWARDREQUEST_MSG.name = "GainAct467TreasureRewardRequest"
GAINACT467TREASUREREWARDREQUEST_MSG.full_name = ".GainAct467TreasureRewardRequest"
GAINACT467TREASUREREWARDREQUEST_MSG.filename = "Activity467Extension"
GAINACT467TREASUREREWARDREQUEST_MSG.nested_types = {}
GAINACT467TREASUREREWARDREQUEST_MSG.enum_types = {}
GAINACT467TREASUREREWARDREQUEST_MSG.fields = {tb.GAINACT467TREASUREREWARDREQUEST_TREASUREID_FIELD}
GAINACT467TREASUREREWARDREQUEST_MSG.is_extendable = false
GAINACT467TREASUREREWARDREQUEST_MSG.extensions = {}

Act467TreasureNO = protobuf.Message(ACT467TREASURENO_MSG)
GAIN_REWARD = 2
GainAct467TreasureRewardReply = protobuf.Message(GAINACT467TREASUREREWARDREPLY_MSG)
GainAct467TreasureRewardRequest = protobuf.Message(GAINACT467TREASUREREWARDREQUEST_MSG)
GetAct467InfoReply = protobuf.Message(GETACT467INFOREPLY_MSG)
GetAct467InfoRequest = protobuf.Message(GETACT467INFOREQUEST_MSG)
INITIAL = 0
OPEN = 1
OpenAct467TreasureReply = protobuf.Message(OPENACT467TREASUREREPLY_MSG)
OpenAct467TreasureRequest = protobuf.Message(OPENACT467TREASUREREQUEST_MSG)

return _G["logic.proto.Activity467Extension_pb"]
