module("logic.scene.common.SceneActionMgr",package.seeall)
local SceneActionMgr = class("SceneActionMgr", SceneComponentBase)

function SceneActionMgr:onInit()
	self.curActions = {}
	self.needStopCount = 0
	self._callbackMap = {}
end

--进入场景时的处理回调
function SceneActionMgr:onEnterScene(sceneId,bornX,bornZ)
	GlobalDispatcher:addListener(GlobalNotify.LeaveScene, self.onLeaveScene, self)
end


function SceneActionMgr:onLeaveScene()
	GlobalDispatcher:removeListener(GlobalNotify.LeaveScene, self.onLeaveScene, self)
	for i=#self.curActions, 1,-1 do
		self.curActions[i]:stop()
	end 
	self._nextAction = nil
	self.curActions = {}
	self.needStopCount = 0
end

-- function SceneActionMgr:DisposeAction()
-- 	for i=#self.curActions, 1,-1 do
-- 		self.curActions[i]:dispose()
-- 	end 
-- 	self._nextAction = nil
-- 	self.curActions = {}
-- end

function SceneActionMgr:canStart(type, showTips)
	if self.needStopCount > 0 and showTips then
		FlyTextManager.instance:showFlyText(lang("正在停止动作。"))
		return false
	end
	local needStopCount = 0
	for i = #self.curActions, 1, - 1 do
		local executor = self.curActions[i]
		if not executor:canOverlap(type) then
			if executor:canInterrupt(type) or executor:checkBlock() then
				needStopCount = needStopCount + 1
			else
				if showTips then
					FlyTextManager.instance:showFlyText(lang("正在做其他动作。"))
				end
				return false
			end
		end
	end
	return true, needStopCount
end 

function SceneActionMgr:startAction(type, params, callback, preventTips)
	local can, needStopCount = self:canStart(type, not preventTips)
	if not can then return false end
	if needStopCount > 0 then
		self.needStopCount = needStopCount
		self._nextAction = {type=type, params=params, callback=callback}
		for i=#self.curActions, 1,-1 do
			local executor = self.curActions[i]
			if not executor:canOverlap(type) then
				executor:stop()
			end
		end 
	else
		self:_startActoin(type, params, callback)
	end
	return true
end

function SceneActionMgr:_startActoin(type, params, callback)
	local newAction = ActionExecutor.New(type, params, handler(self.onFinish, self))
	if callback then
		printInfo("add finish action callback", callback)
		self._callbackMap[newAction] = callback
	end
	table.insert(self.curActions, newAction)
	GlobalDispatcher:dispatch(GlobalNotify.StartAction, type)
	newAction:start()	
end

function SceneActionMgr:onFinish(action, isSuc)
	-- printInfo("action finsh", action:getType())	
	table.removebyvalue(self.curActions, action)
	if self.needStopCount > 0 then
		self.needStopCount = self.needStopCount - 1
		if self.needStopCount == 0 and self._nextAction then
			self:_startActoin(self._nextAction.type, self._nextAction.params, self._nextAction.callback)
			self._nextAction = nil
		end		
	end
	printInfo("finish action callback", self._callbackMap[action])
	if self._callbackMap[action] then
		self._callbackMap[action](isSuc)
		self._callbackMap[action] = nil		
	end
end

function SceneActionMgr:stopAction(type, callback)
	if not type then
		return self:startAction(SceneActionType.CancelAction, nil, callback)
	end
	if self.needStopCount > 0 then
		FlyTextManager.instance:showFlyText(lang("正在停止动作。"))
		return false
	end
	
	for i=#self.curActions, 1,-1 do
		local executor = self.curActions[i]
		if executor.actionType == type and (executor:canInterrupt(SceneActionType.CancelAction) or executor:checkBlock()) then
			self.needStopCount = 1
			self._nextAction = {type=SceneActionType.CancelAction, params=params, callback=callback}
			executor:stop()
			return true
		end
	end
	self:_startActoin(SceneActionType.CancelAction, params, callback)
	return true
end

function SceneActionMgr:hasAction(type)
	for i=#self.curActions, 1,-1 do
		if self.curActions[i]:getType() == type then
			return true
		end
	end	
	return false
end

function SceneActionMgr:isIdle()
	return #self.curActions == 0
end

function SceneActionMgr:getNextAction()
	return self._nextAction
end

return SceneActionMgr