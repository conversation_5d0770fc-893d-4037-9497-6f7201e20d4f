module("logic.extensions.room.view.administration.MySeabedPresentor",package.seeall)

local MySeabedPresentor = class("MySeabedPresentor", MyIslandPresentor)

function MySeabedPresentor:dependWhatResources()
	return {
		"ui/scene/room/myislandview_sea.prefab",
		MyIslandHouseCell.ItemUrl,
		MyIslandHouseList.DragHouseGoUrl
	}
end

function MySeabedPresentor:isMyHouse()
	return self.mainView.houseData:isMyHouse()
end

return MySeabedPresentor