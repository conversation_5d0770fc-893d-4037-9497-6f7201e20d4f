module("logic.scene.common.action.TeleportAction",package.seeall)
local TeleportAction = class("TeleportAction", SceneActionBase)
function TeleportAction:onStart()
	local targetPos = self.params.targetPos
	local forceJump = self.params.forceJump
	local canJump = forceJump or SceneManager.instance:getCurScene().mapMgr:isWalkable(targetPos.posX, targetPos.posY, targetPos.posZ)
	if canJump then
		local userPlayer = SceneManager.instance:getCurScene():getUserPlayer()
		userPlayer:teleport(targetPos.posX, targetPos.posY, targetPos.posZ)
		SceneAgent.instance:sendMoveRequest({targetPos}, 1)
	end
	printInfo("jump", canJump)
	self:finish(canJump)
end

-- function TeleportAction:canOverlap(type)
-- 	return false
-- end

-- function TeleportAction:canInterrupt(type)
-- 	return false
-- end

function TeleportAction:getType()
	return SceneActionType.Teleport
end

return TeleportAction