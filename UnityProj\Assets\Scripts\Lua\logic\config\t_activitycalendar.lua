-- {excel:H活动配置表.xlsx, sheetName:export_活动日历}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_activitycalendar", package.seeall)

local title = {id=1,sortId=2,firstTabId=3,ShowGoHandler=4,ActivityFirstClick=5,shortName=6,FuncId=7,ActivitID=8,configId=9,tabIconPath=10,reward=11,viewName=12,channel=13,barColor=14,starColor=15,btnColor=16}

local dataList = {
	{2, 1, 2, "true", false, "七日", nil, 0, 0, "image/activity/commonactivityicon/activity_icon_qiri.png", nil, "SevenDayView", nil, "#304b1e", "#5f7451", "#021000"},
	{3, 2, 2, "levelAward", false, "等级", nil, 1018, 0, "image/activity/commonactivityicon/dengji_ico_dengji.png", nil, "ActivityLevelAwardPanel", nil, "#2f3b77", "#434e87", "#222c5e"},
	{4, 3, 1, "true", false, "投资", {14}, 1002, 0, "image/activity/commonactivityicon/activity_icon_touzi.png", nil, "AobiBank", nil, "#492b23", "#826962", "#260d06"},
	{5, 4, 3, "TRUE", true, "秘境", nil, 0, 0, "image/activity/commonactivityicon/dreamland_icon_mijing.png", {{itemId=********,count=0},{itemId=********,count=0},{itemId=********,count=0},{itemId=********,count=0},{itemId=********,count=0},{itemId=********,count=0}}, "DreamlikePoster", nil, "#08102d", "#000518", "#4a67b7"},
	{6, 1, 3, "FALSE", false, "特惠礼包", nil, 1094, 0, "image/activity/commonactivityicon/activity_icon_libao.png", {{itemId=********,count=0},{itemId=2,count=0},{itemId=********,count=0}}, "GiftBagSaleView", nil, "#6c423e", "#6c484a", "#63160e"},
	{7, 2, 3, "levelAward", true, "小游戏时光", nil, 2051, 0, "image/activity/commonactivityicon/xiaoyouxi_ico_youxi.png", {{itemId=********,count=0}}, "ActivityTinyGamePanel", nil, "#144670", "#00101d", "#00144f"},
	{8, 2, 3, "true", false, "花神", nil, 2455, 0, "image/activity/commonactivityicon/huashen_ico_huashen.png", {{itemId=********,count=0},{itemId=********,count=0},{itemId=8,count=0}}, "LegendOfGarden", nil, "#0e597b", "#064460", "#001e3a"},
	{9, 2, 3, "true", true, "大富翁", nil, 2460, 0, "image/activity/commonactivityicon/activity_icon_monopoly.png", {{itemId=13002027,count=0},{itemId=13002028,count=0},{itemId=13002026,count=0}}, "MonopolyView", nil, "#1e4c10", "#1e4c10", "#060d02"},
	{10, 3, 2, "true", true, "火锅温泉", nil, 1003, 0, "image/activity/commonactivityicon/huoguo_icon_huoguo.png", {{itemId=3,count=0}}, "HotSpringPanel", nil, "#144266", "#314759", "#001549"},
	{12, 1, 3, "true", false, "连充", {34}, 1281, 2, "image/activity/commonactivityicon/lianchong_ico_lianchong.png", {{itemId=********,count=0},{itemId=2,count=0}}, "SevenDayRecharge", nil, "#0b4a81", "#324f68", "#00284b"},
	{13, 2, 2, "FALSE", false, "周末福利", nil, 1026, 0, "image/activity/commonactivityicon/denglv_icon_denglv.png", {{itemId=16000074,count=0},{itemId=16000075,count=0},{itemId=********,count=0}}, "WeekEndView", nil, "#103656", "#00213d", "#00182d"},
	{15, 1, 3, "true", false, "连充", {34}, 1148, 2, "image/activity/commonactivityicon/lianchong_ico_lianchong.png", {{itemId=********,count=0},{itemId=2,count=0}}, "SevenDayRecharge", nil, "#0b4a81", "#00284b", "#030054"},
	{16, 1, 1, "true", false, "支付宝", {40}, 1046, 0, "image/activity/commonactivityicon/zfbzxl_icon_zhifubao.png", {{itemId=********,count=0},{itemId=********,count=0},{itemId=2,count=0}}, "ActivityExclusiveGiftView", {210009}, "", "", ""},
	{17, 2, 3, "TRUE", true, "折扣日", nil, 2329, 0, "image/activity/commonactivityicon/aobidazhe_icon.png", {{itemId=16000040,count=0},{itemId=16000033,count=0},{itemId=16000037,count=0},{itemId=16000067,count=0}}, "RechargeDiscountDay", nil, "#4d201a", "#4f2c27", "#1b0300"},
	{18, 3, 3, "TRUE", true, "烟花盛会", nil, 2469, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12005416,count=0},{itemId=12005415,count=0},{itemId=16000627,count=0}}, "FireworkView135", nil, "#0e1d35", "#395175", "#afcef3"},
	{19, 2, 1, "TRUE", true, "游戏狂欢周", nil, 2466, 0, "image/activity/commonactivityicon/xiaoyouxi_ico_youxi.png", {{itemId=2,count=0}}, "TinyGameTimeView", nil, "#1f4a1b", "#364737", "#0a1c0b"},
	{20, 2, 1, "FALSE", false, "时装周", nil, 1614, 0, "image/activity/commonactivityicon/abszz_icon_shizhuanzhou.png", {{itemId=16000439,count=0},{itemId=26010041,count=0}}, "ActivityDressRaceView", nil, "#3b0c0c", "#3e0202", "#190000"},
	{21, 1, 1, "true", true, "星愿卡", nil, 1081, 0, "image/activity/commonactivityicon/icon_xinyuanka.png", {{itemId=11000281,count=0},{itemId=11000286,count=0}}, "StarWishCardView", nil, "", "", ""},
	{22, 2, 1, "TRUE", true, "限时宠物", nil, 2234, 0, "image/activity/commonactivityicon/xjsj_icon_zhuayin.png", {{itemId=2,count=0}}, "LimitTimePetView_104", nil, "#382125", "#241114", "#241114"},
	{23, 2, 3, "starRewards", false, "星际礼遇", nil, 1073, 0, "image/activity/commonactivityicon/xingjiliyu_icon_feidie.png", {{itemId=2,count=0}}, "StarSpecialProductionView", nil, "", "", ""},
	{24, 2, 4, "TRUE", true, "美图秀秀", nil, 1139, 0, "image/activity/commonactivityicon/icon_xingxing.png", {{itemId=3,count=0}}, "AppleInvite", nil, "", "", ""},
	{26, 2, 1, "TRUE", true, "限时宠物", nil, 1346, 0, "image/activity/commonactivityicon/xjsj_icon_zhuayin.png", {{itemId=2,count=0}}, "LimitTimePetView_100", nil, "#c1723d", "#d4491f", "#b13e0e"},
	{27, 3, 3, "TRUE", true, "烟花欢聚", nil, 1013, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12000672,count=0},{itemId=16000115,count=0}}, "FireworkView", nil, "", "", ""},
	{28, 3, 4, "TRUE", true, "UCG投票", {44}, 1104, 0, "image/activity/commonactivityicon/hmab_icon_toupiao.png", {{itemId=1,count=0}}, "UCGVote", nil, "", "", ""},
	{30, 3, 1, "TRUE", true, "小熊钱罐", nil, 2457, 0, "image/activity/commonactivityicon/icon_xiaoxiongqianguan.png", {{itemId=3,count=0}}, "ActMultiRechargePanel", nil, "#a9581c", "#ae5603", "#540a08"},
	{31, 2, 4, "TRUE", true, "福利站", nil, 2504, 0, "image/activity/commonactivityicon/icon_flz.png", {{itemId=3,count=0}}, "CommonCalendarUGC_1", {110001,210009,610001,610002}, "#13338e", "#000ca3", "#002480"},
	{32, 2, 4, "TRUE", true, "全民创作大赛", nil, 2503, 0, "image/activity/commonactivityicon/icon_huihua.png", {{itemId=3,count=0}}, "CommonCalendarUGC_2", {110001,210009,610001,610002}, "#b8662a", "#ab340b", "#bb1e0e"},
	{33, 2, 1, "FALSE", true, "限时宠物", nil, 1206, 0, "image/activity/commonactivityicon/xjsj_icon_zhuayin.png", {{itemId=2,count=0}}, "LimitTimePetView_101", nil, "", "", ""},
	{34, 3, 3, "FALSE", false, "超值自选", nil, 1213, 0, "image/activity/commonactivityicon/icon_chaozhizixuan.png", {{itemId=2,count=0}}, "OptionalGiftBagView", nil, "#6c423e", "#6c484a", "#63160e"},
	{35, 2, 1, "TRUE", true, "绘声演绎", nil, 2248, 0, "image/activity/commonactivityicon/icon_yinyue.png", nil, "ActivityRabbitSpeakersView", nil, "#1e0b18", "#170712", "#170712"},
	{36, 2, 1, "TRUE", true, "限时龙鱼", nil, 2255, 0, "image/activity/commonactivityicon/icon_yu.png", {{itemId=2,count=0}}, "LimitTimePetView_102", nil, "#025777", "#000000", "#17171f"},
	{37, 3, 3, "TRUE", true, "烟花盛会", nil, 2470, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12000673,count=0},{itemId=12005415,count=0},{itemId=16000627,count=0}}, "FireworkView135", nil, "#0e1d35", "#395175", "#afcef3"},
	{38, 3, 3, "TRUE", true, "烟花盛会", nil, 2471, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12000673,count=0},{itemId=12005415,count=0},{itemId=16000627,count=0}}, "FireworkView135", nil, "#0e1d35", "#395175", "#afcef3"},
	{39, 2, 1, "FALSE", true, "限时宠物探险", nil, 1255, 0, "image/activity/commonactivityicon/xscwtx_icon_shizhong.png", nil, "ActivityPetExpediton", nil, "", "", ""},
	{41, 2, 1, "TRUE", true, "回归邀请", nil, 1805, 0, "image/activity/commonactivityicon/zhhd_icon.png", nil, "AobiReturn2View", nil, "#4a2861", "#4a2861", "#2d0648"},
	{42, 2, 2, "TRUE", false, "扭蛋机", nil, 1303, 0, "image/activity/commonactivityicon/hlndj_icon.png", nil, "ReturnLottery", nil, "#16133a", "#2c2856", "#828bd8"},
	{43, 2, 3, "TRUE", true, "音乐派对", nil, 1297, 0, "image/activity/commonactivityicon/icon_musicparty.png", {{itemId=3,count=0}}, "MusicPartyCalendarPreView", nil, "", "", ""},
	{44, 2, 4, "TRUE", true, "赢好礼", nil, 1436, 0, "image/activity/commonactivityicon/icon_yifu.png", {{itemId=3,count=0}}, "CommonCalendarUGC_4", {110001,210009,610001,610002}, "", "", ""},
	{45, 2, 3, "TRUE", true, "七日酬宾", nil, 1331, 0, "image/activity/commonactivityicon/hlndj_icon.png", {{itemId=3,count=0}}, "RechargeTurntableView", nil, "#4d201a", "#4f2c27", "#1b0300"},
	{46, 2, 4, "FALSE", false, "小程序", nil, 1571, 0, "", nil, "", {110001,210009,610001,610002}, "", "", ""},
	{47, 2, 1, "TRUE", true, "限时宠物", nil, 1347, 0, "image/activity/commonactivityicon/xjsj_icon_zhuayin.png", {{itemId=2,count=0}}, "LimitTimePetView_103", nil, "#236378", "#063655", "#0d4151"},
	{48, 2, 4, "FALSE", false, "更新礼", nil, 1358, 0, "", nil, "", nil, "", "", ""},
	{49, 2, 2, "TRUE", false, "功能前瞻", nil, 0, 0, "image/activity/commonactivityicon/icon_gongnengqianzhan.png", nil, "ActivityPlayerGuideView", nil, "#193c51", "#1f4a64", "#042e48"},
	{50, 3, 4, "TRUE", true, "关注礼", nil, 1382, 0, "image/activity/commonactivityicon/icon_gzyl.png", nil, "PayAttentionToAppView", {110001,210009,610001,610002}, "#0f332a", "#325d3b", "#002c15"},
	{51, 1, 1, "TRUE", true, "0元购", nil, 1957, 0, "image/activity/commonactivityicon/yckj_icon_0yuangou.png", nil, "BargainView", nil, "#553a33", "#553533", "#0f0401"},
	{52, 2, 1, "TRUE", true, "织梦乐章", nil, 2335, 0, "image/activity/commonactivityicon/icon_yinyue.png", nil, "ActivityRabbitGiftView", nil, "#275b44", "#1a5242", "#00150b"},
	{53, 2, 4, "TRUE", true, "来伊份联动", nil, 1661, 0, "image/activity/commonactivityicon/cbdlm_icon_2.png", nil, "CalendarLinkage", nil, "#3a1000", "#290900", "#200200"},
	{54, 3, 3, "TRUE", true, "祝福之地", nil, 1490, 0, "", nil, "WeekPollView", nil, "#11607a", "#002c3b", "#003343"},
	{55, 3, 3, "TRUE", true, "一周祝福", {15}, 2493, 0, "", nil, "ActivityWeekLifePanel", nil, "#161f49", "#294623", "#000e26"},
	{56, 2, 4, "FALSE", false, "小程序", nil, 2505, 0, "", nil, "", {110001,210009,610001,610002}, "", "", ""},
	{57, 2, 2, "weekendSignUp", false, "周末福利", nil, 1550, 0, "image/activity/commonactivityicon/denglv_icon_denglv.png", nil, "WeekendSignUpView", nil, "#103656", "#00213d", "#00182d"},
	{58, 5, 3, "TRUE", true, "熊熊摇摇船", nil, 2333, 0, "", nil, "RockingboatMainView", nil, "#4f332a", "#38231a", "#110000"},
	{59, 3, 1, "TRUE", true, "小熊宝", nil, 1534, 0, "image/activity/commonactivityicon/icon_xiaoxiongqianguan.png", {{itemId=3,count=0}}, "NewHandRechargeView", nil, "#3a65a5", "#3a65a5", "#123d7e"},
	{60, 3, 3, "TRUE", true, "精灵游学", {15}, 2492, 0, "", nil, "ActivityElfSigninPanel", nil, "#445929", "#36491c", "#091400"},
	{61, 4, 4, "weCom", true, "微信好礼", nil, 1593, 0, "", nil, "PayAttentionToWeComView", {110001,210009,610001,610002}, "#1c4769", "#1255a0", "#081e50"},
	{62, 4, 1, "TRUE", false, "魔力晶钻瓶", nil, 2459, 0, "", nil, "MagicCrystalBottleView", nil, "#05275d", "#3f639a", "#00182d"},
	{63, 2, 4, "TRUE", true, "企微送宠", nil, 2293, 0, "image/activity/commonactivityicon/icon_flz.png", nil, "CommonCalendarUGC_3", {110001,210009,610001,610002}, "#160400", "#000000", "#0f0200"},
	{64, 2, 4, "TRUE", true, "直播征集", nil, 2292, 0, "image/activity/commonactivityicon/icon_flz.png", nil, "CommonCalendarUGC_5", {110001,210009,610001,610002}, "#ae1f20", "#bb0000", "#bf0000"},
	{65, 5, 1, "TRUE", false, "万物回馈", nil, 1677, 0, "", nil, "EcoParkRechargeView", nil, "#104c2a", "#1e4c10", "#060d02"},
	{66, 2, 1, "returnPlayer", true, "寻宝冒险", nil, 0, 0, "", nil, "AobiReturnPersonal", nil, "", "", ""},
	{67, 1, 3, "true", true, "博览商店", nil, 2512, 0, "", nil, "returnShopView", nil, "#3f150d", "#290704", "#290704"},
	{68, 6, 1, "true", true, "限时挖宝", nil, 1767, 0, "", nil, "LimitTimePetView", nil, "#553a33", "#553533", "#0f0401"},
	{69, 1, 4, "TRUE", true, "周边上新", nil, 1773, 0, "image/activity/commonactivityicon/icon_flz.png", nil, "CommonCalendarUGC_4", nil, "#982b23", "#510e0e", "#3f0706"},
	{70, 5, 1, "TRUE", false, "优米鹿藏品", nil, 2464, 0, "", nil, "SeasonChainBagView", nil, "#145542", "#00494c", "#002216"},
	{71, 3, 1, "true", true, "梦境之门", nil, 1857, 0, "", nil, "CalendarTransportDressView", nil, "#216a9d", "#5084ac", "#0b2b46"},
	{72, 4, 4, "true", true, "活动日历", nil, 1908, 0, "image/activity/commonactivityicon/icon_flz.png", nil, "ActivityOverView", nil, "", "", ""},
	{73, 2, 4, "TRUE", true, "卡片交换", nil, 2372, 0, "image/activity/commonactivityicon/icon_flz.png", nil, "CommonCalendarUGC_4", {110001,210009,610001,610002}, "#41610f", "#324e07", "#4f7516"},
	{74, 3, 3, "TRUE", true, "烟花盛会", nil, 2472, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12000673,count=0},{itemId=12005415,count=0},{itemId=16000627,count=0}}, "FireworkView135", nil, "#0e1d35", "#395175", "#afcef3"},
	{75, 3, 3, "TRUE", true, "烟花盛会", nil, 2473, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12000673,count=0},{itemId=12005415,count=0},{itemId=16000627,count=0}}, "FireworkView135", nil, "#0e1d35", "#395175", "#afcef3"},
	{76, 3, 3, "TRUE", true, "烟花盛会", nil, 2474, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12000673,count=0},{itemId=12005415,count=0},{itemId=16000627,count=0}}, "FireworkView135", nil, "#0e1d35", "#395175", "#afcef3"},
	{77, 3, 3, "TRUE", true, "烟花盛会", nil, 1972, 0, "image/activity/commonactivityicon/yhsh_icon_yanhua.png", {{itemId=12000673,count=0},{itemId=12005415,count=0},{itemId=16000627,count=0}}, "FireworkView135", nil, "#0e1d35", "#395175", "#afcef3"},
	{78, 2, 4, "TRUE", true, "服装投票", nil, 2040, 0, "image/activity/commonactivityicon/icon_flz.png", nil, "CommonCalendarUGC_3", {110001,210009,610001,610002}, "#7c6450", "#280f02", "#684326"},
	{79, 4, 3, "TRUE", true, "安抚疯人鱼", nil, 2041, 0, "image/activity/commonactivityicon/dreamland_icon_mijing.png", {{itemId=16000654,count=0},{itemId=14000783,count=0},{itemId=16000648,count=0},{itemId=16000647,count=0}}, "ActivitySeaBossView", nil, "#0c667c", "#121043", "#144a60"},
	{80, 3, 3, "TRUE", true, "精灵签到", nil, 2109, 0, "", nil, "HappySigninView", nil, "#692f10", "#70382b", "#6f2b16"},
	{81, 4, 3, "TRUE", true, "观光车道", nil, 2115, 0, "", nil, "ActivitySeaBusView", nil, "#102e60", "#030a15", "#030f26"},
	{82, 10, 4, "TRUE", true, "资讯", nil, 2116, 0, "", nil, "CalendarLinkage", {130005,130066,130008,130002,110006,130071,130009,130019,110003,120001,130068,130061}, "#004467", "#315271", "#00273b"},
	{83, 4, 3, "TRUE", true, "捉鱼比赛", nil, 2130, 0, "", nil, "ActivitySeaFishTaskView", nil, "#102e60", "#030a15", "#030f26"},
	{84, 2, 1, "TRUE", true, "捕鱼狂欢周", nil, 2465, 0, "image/activity/commonactivityicon/xiaoyouxi_ico_youxi.png", {{itemId=2,count=0}}, "ActivitySeaShopView", nil, "#13315f", "#00101d", "#00144f"},
	{86, 4, 3, "TRUE", true, "竞速捉鱼", nil, 2209, 0, "", nil, "ActivitySeaRaceView", nil, "#102e60", "#030a15", "#030f26"},
	{87, 4, 3, "TRUE", true, "观光车道", nil, 2210, 0, "", nil, "ActivitySeaBusView", nil, "#102e60", "#030a15", "#030f26"},
	{88, 4, 3, "TRUE", true, "捉鱼比赛", nil, 2211, 0, "", nil, "ActivitySeaFishTaskView", nil, "#102e60", "#030a15", "#030f26"},
	{89, 1, 1, "TRUE", true, "奥比新友谊", nil, 2407, 0, "", nil, "ActivityTaskADView", nil, "#5f4839", "#4f3527", "#1b0900"},
	{90, 4, 3, "TRUE", true, "观光车道", nil, 2228, 0, "", nil, "ActivitySeaBusView", nil, "#102e60", "#030a15", "#030f26"},
	{91, 4, 3, "TRUE", true, "观光车道", nil, 2229, 0, "", nil, "ActivitySeaBusView", nil, "#102e60", "#030a15", "#030f26"},
	{92, 4, 3, "TRUE", true, "观光车道", nil, 2230, 0, "", nil, "ActivitySeaBusView", nil, "#102e60", "#030a15", "#030f26"},
	{93, 2, 1, "TRUE", true, "限时宠物", nil, 2300, 0, "image/activity/commonactivityicon/xjsj_icon_zhuayin.png", {{itemId=2,count=0}}, "LimitTimePetView_105", nil, "#23233b", "#0a0a1d", "#17171f"},
	{94, 4, 3, "TRUE", true, "海洋筑梦", nil, 2286, 0, "", nil, "ActivitySeaHomeTaskPresentor", nil, "#23233b", "#0a0a1d", "#17171f"},
	{95, 3, 1, "TRUE", true, "小熊节", nil, 2348, 0, "", nil, "ActivityWelfareView", nil, "#23233b", "#0a0a1d", "#17171f"},
	{96, 1, 3, "true", true, "特惠礼包", nil, 2376, 0, "", nil, "GiftBagSaleSelect", nil, "#6c423e", "#6c484a", "#63160e"},
	{97, 2, 1, "TRUE", true, "折扣日", nil, 2456, 0, "", nil, "Act457MainView", nil, "#4d201a", "#4f2c27", "#1b0300"},
	{98, 3, 3, "TRUE", true, "超值自选", nil, 2377, 0, "", nil, "Act459MainView", nil, "#4d201a", "#4f2c27", "#1b0300"},
	{99, 2, 1, "TRUE", true, "熊熊好运来", nil, 2442, 0, "", nil, "ActivityLotteryView", nil, "#043f8a", "#3e73b9", "#002056"},
	{100, 1, 1, "TRUE", true, "夏夜萤火", nil, 2447, 0, "", nil, "ActivityDirectSaleView", nil, "#05182d", "#000000", "#64a5dd"},
	{101, 1, 1, "TRUE", true, "关系进阶", nil, 2495, 0, "", nil, "ActivityTaskADView", nil, "#6e3519", "#281206", "#32160a"},
}

local t_activitycalendar = {
	[2] = dataList[1],
	[3] = dataList[2],
	[4] = dataList[3],
	[5] = dataList[4],
	[6] = dataList[5],
	[7] = dataList[6],
	[8] = dataList[7],
	[9] = dataList[8],
	[10] = dataList[9],
	[12] = dataList[10],
	[13] = dataList[11],
	[15] = dataList[12],
	[16] = dataList[13],
	[17] = dataList[14],
	[18] = dataList[15],
	[19] = dataList[16],
	[20] = dataList[17],
	[21] = dataList[18],
	[22] = dataList[19],
	[23] = dataList[20],
	[24] = dataList[21],
	[26] = dataList[22],
	[27] = dataList[23],
	[28] = dataList[24],
	[30] = dataList[25],
	[31] = dataList[26],
	[32] = dataList[27],
	[33] = dataList[28],
	[34] = dataList[29],
	[35] = dataList[30],
	[36] = dataList[31],
	[37] = dataList[32],
	[38] = dataList[33],
	[39] = dataList[34],
	[41] = dataList[35],
	[42] = dataList[36],
	[43] = dataList[37],
	[44] = dataList[38],
	[45] = dataList[39],
	[46] = dataList[40],
	[47] = dataList[41],
	[48] = dataList[42],
	[49] = dataList[43],
	[50] = dataList[44],
	[51] = dataList[45],
	[52] = dataList[46],
	[53] = dataList[47],
	[54] = dataList[48],
	[55] = dataList[49],
	[56] = dataList[50],
	[57] = dataList[51],
	[58] = dataList[52],
	[59] = dataList[53],
	[60] = dataList[54],
	[61] = dataList[55],
	[62] = dataList[56],
	[63] = dataList[57],
	[64] = dataList[58],
	[65] = dataList[59],
	[66] = dataList[60],
	[67] = dataList[61],
	[68] = dataList[62],
	[69] = dataList[63],
	[70] = dataList[64],
	[71] = dataList[65],
	[72] = dataList[66],
	[73] = dataList[67],
	[74] = dataList[68],
	[75] = dataList[69],
	[76] = dataList[70],
	[77] = dataList[71],
	[78] = dataList[72],
	[79] = dataList[73],
	[80] = dataList[74],
	[81] = dataList[75],
	[82] = dataList[76],
	[83] = dataList[77],
	[84] = dataList[78],
	[86] = dataList[79],
	[87] = dataList[80],
	[88] = dataList[81],
	[89] = dataList[82],
	[90] = dataList[83],
	[91] = dataList[84],
	[92] = dataList[85],
	[93] = dataList[86],
	[94] = dataList[87],
	[95] = dataList[88],
	[96] = dataList[89],
	[97] = dataList[90],
	[98] = dataList[91],
	[99] = dataList[92],
	[100] = dataList[93],
	[101] = dataList[94],
}

t_activitycalendar.dataList = dataList
local mt
if ActivityCalendarDefine then
	mt = {
		__cname =  "ActivityCalendarDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or ActivityCalendarDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_activitycalendar