module("logic.extensions.tetris.game.skill.TetrisSkillComp", package.seeall)

local TetrisSkillComp = class("TetrisSkillComp")

function TetrisSkillComp:ctor(mgr, skillKey)
    self._mgr = mgr
    self._skillKey = skillKey
    --技能的所有状态
    self.prefix = "skill" .. self._skillKey
    self._skillState = TetrisSkillState.New(self.prefix)
    TetrisGameViewModel.instance:setValue(self.prefix .. "Enable", false)
    TetrisGameViewModel.instance:setValue(self.prefix .. "Id", 1)
    self.skillSetting = {}
    self.skillHandler = {}
    GlobalDispatcher:addListener(TetrisNotify.curShapeTypeChange, self._curShapeTypeChange, self)

    local playerInfo = GameCentreModel.instance:getPlayerInfo(UserInfo.userId)

    if TetrisController.instance._isDebug == true then
        --self:activeSkill(7)
        --self:setActive(true)
    else
        if self._mgr._isStandalone then
            local levelId = TetrisUIModel.instance:getNowLevelId()
            local levelCfg = TetrisConfig.getLevelConfigById(levelId)
            if levelCfg then
                if levelCfg.isPlot then -- 剧情
                    self:activeSkill(levelCfg.unlockSkill)
                    self:setActive(true)
                else
                    local skillId = playerInfo.tetris_skill
                    --printError(tostring(skillId))
                    if skillId ~= 0 then
                        self:activeSkill(skillId)
                        self:setActive(true)
                    else
                        self:setActive(false)
                    end
                end
            else
                self:setActive(false)
            end
        else
            self:setActive(false)
        end
    end
    self:setActive(false)
    self:updateGameViewSkillState()
end

function TetrisSkillComp:setActive(value)
    self.isEnabled = value
    TetrisGameViewModel.instance:setValue(self.prefix .. "Enable", self.isEnabled)
end

--游戏逻辑帧
function TetrisSkillComp:lateUpdate()
    if self.isEnabled ~= true then
        return
    end
    self._curShape = self._mgr:getCurShape()
    if self._curShape then
        if self._activitySkill then
            self._activitySkill:lateUpdate()
        end
    end
end

function TetrisSkillComp:addSkillPoint(value)
    self._skillState:addSkillPoint(value)
    if self._activitySkill then
        self._activitySkill:updateProgress()
    end
end

function TetrisSkillComp:clearSkillPoint()
    self._skillState:clearSkillPoint()
    if self._activitySkill then
        self._activitySkill:updateProgress()
    end
end

function TetrisSkillComp:getSkillProgress()
    if self._activitySkill then
        return self._activitySkill:getProgress()
    end
    return 0
end

function TetrisSkillComp:activeSkill(skillId)
    local skill = "skill" .. skillId
    local params = CommonConfig.getConfig("act278_skill")[skillId].params
    local config = CommonConfig.getConfig("act278_skill")[skillId]
    self.skillSetting[params.origin] = config
    if self.skillHandler[skillId] == nil then
        local GGG = getglobal("_G")
        local handler = GGG["TetrisSkill_" .. config.handler]
        if handler then
            self.skillHandler[skillId] = handler.New(self._mgr, self._skillState, config, self.prefix)
        else
            printError("handler is nil")
        end
    else
        printError("handler is nil")
    end
    if self.curSkill == nil then
        self.curSkill = skillId
    end
end

function TetrisSkillComp:execute()
    if self._activitySkill then
        local result = self._activitySkill:execute()
        if result == true then
            self._skillState:clearSkillPoint()
            self._activitySkill:updateProgress()
            return true
        end
    else
        printError("skill is nil")
    end
    return false
end

function TetrisSkillComp:checkInvalid()
    if self._activitySkill then
        return self._activitySkill:checkInvalid()
    end
    return false
end

function TetrisSkillComp:checkSkillPointEnough()
    if self._activitySkill then
        return self._activitySkill:checkSkillPointEnough()
    end
    return false
end

function TetrisSkillComp:_curShapeTypeChange()
    local defaultSkillId = 0
    for k, handler in pairs(self.skillHandler) do
        defaultSkillId = k
        if handler:checkInvalid() then
            self.curSkill = k
            self._activitySkill = handler
            TetrisGameViewModel.instance:setValue(self.prefix .. "Id", k)
            return
        end
    end
    --如果最后一个技能也无效,那么就拿其中一个显示
    self.curSkill = defaultSkillId
    self._activitySkill = self.skillHandler[defaultSkillId]
    TetrisGameViewModel.instance:setValue(self.prefix .. "Id", defaultSkillId)
end

function TetrisSkillComp:updateGameViewSkillState()
    local defaultSkillId = 0
    for k, handler in pairs(self.skillHandler) do
        -- printError(tostring(k))
        defaultSkillId = k
        self._activitySkill = handler
        TetrisGameViewModel.instance:setValue(self.prefix .. "Id", k)
        break
    end
    if defaultSkillId == 0 then
        self:setActive(false)
    end
end

function TetrisSkillComp:dispose()
    GlobalDispatcher:removeListener(TetrisNotify.curShapeTypeChange, self._curShapeTypeChange, self)
    if TetrisController.instance._isDebug == true then
    end
end

return TetrisSkillComp
