module("logic.extensions.sharestreet.model.ds.ShareStreetInfo",package.seeall)

local ShareStreetInfo = class("ShareStreetInfo")

function ShareStreetInfo:ctor()
	
end

-- ShareStreetInfoNO: |-
-- optional string               ownerId     = 1;    //创建者
-- optional string               name        = 2;    //街区名称
-- optional string               desc        = 3;    //街区描述
-- repeated ShareStreetMemberNO  members     = 4;    //成员
-- repeated int32                areaId      = 5;    //解锁区域
-- optional string               picture     = 6;    //街区图片
-- optional int32                defaultPic  = 7;    //默认图片Id
-- optional int32                likeCount   = 8;    //点赞数
-- optional int32                visitCount  = 9;    //访问数
function ShareStreetInfo:setInfo(info)
    self._ownerId = info.ownerId
    self._name = info.name
    self._desc = info.desc
    self._members = info.members
    if self._members and #self._members > 1 then
        table.sort(self._members, function(a, b) 
            return b.areaId > a.areaId
        end)
    end
    self._areaId2Member = {}
    for _, member in ipairs(self._members) do
        self._areaId2Member[member.areaId] = member
    end
    self._areaIdList = info.areaId
    self._picture = info.picture
    self._defaultPic = info.defaultPic
    self._likeCount = info.likeCount
    self._visitCount = info.visitCount
    self._displayId = info.displayId
end

function ShareStreetInfo:unlockArea()
    local upgradeAreaId = self:getLastCanUpgradeAreaId()
    local upgradeCfg = ShareStreetCfg.instance:getUpgradeConfigById(upgradeAreaId)
    if upgradeCfg then
        for __, areaId in ipairs(upgradeCfg.areaIds) do
            table.insert(self._areaIdList, areaId)
        end
    else
        printError("错误, upgradeCfg不能为空")        
    end
end
---是否可以加入该街区
function ShareStreetInfo:isCanJoin()
    local myStreetInfo = ShareStreetModel.instance:getUserInfo(UserInfo.userId)
    if not myStreetInfo then
        return false ---玩家自己没街区不能加入
    end
    if myStreetInfo:getMemberCount() > 1 then
        return false ---玩家所在街区已经有成员不能加入
    end
    return self:getUnlockedAreaIdCount() > self:getMemberCount()
end

function ShareStreetInfo:getOwnerId()
    return self._ownerId
end

-- ShareStreetMemberNO: |-
-- optional RoleSimpleInfoNO roleInfo    = 1;
-- optional int32            areaId      = 2;
function ShareStreetInfo:getOwner()
    return self._members and self._members[1]
end

-- RoleSimpleInfoNO
-- optional string id = 1;                             //userId
-- optional string nickname = 2;                       //昵称
-- optional int32 sex = 3;                             //性别0:男1:女
-- optional int32 modelId = 4;                         //人模Id
-- optional int32 level = 5;                           //等级
-- optional int32 exp = 6;                             //经验值
-- optional int32 vipLevel = 7;                        //VIP等级
-- repeated int32 clothes = 8;                         //装扮信息
-- repeated int32 defaultClothes = 9;                  //选择的所有默认装扮Id
-- optional string headIcon = 10;                      //图标Id
-- optional string showId = 11;                        //角色的展示的UserId
-- optional int32 headWindows = 12;                    //头像框
-- optional int32 friendshipPower = 13;                //亲和力
-- optional int32 vipCardType = 14;                    //Vip卡类型(1:月卡2:季卡3:年卡)
-- ...
function ShareStreetInfo:getOwnerRoleInfo()
    local owner = self:getOwner()
    return owner and owner.roleInfo
end

function ShareStreetInfo:getName()
    return self._name
end

function ShareStreetInfo:setName(str)
    self._name = str
end

function ShareStreetInfo:getDesc()
    return self._desc
end

function ShareStreetInfo:setDesc(str)
    self._desc = str
end

-- ShareStreetMemberNO: |-
-- optional RoleSimpleInfoNO roleInfo    = 1;
-- optional int32            areaId      = 2;
function ShareStreetInfo:getMemberList()
    return self._members
end

function ShareStreetInfo:getMemberCount()
    return #self._members
end

function ShareStreetInfo:getMemberByAreaId(areaId)
    return self._areaId2Member[areaId]
end

function ShareStreetInfo:getUnlockedAreaIdList()
    return self._areaIdList
end

function ShareStreetInfo:getLastCanUpgradeAreaId()
    for _, cfg in ipairs(ShareStreetCfg.instance:getUpgradeConfigList()) do
        for __, areaId in ipairs(cfg.areaIds) do
            if not table.indexof(self._areaIdList, areaId) then
                return cfg.id
            end
        end
    end
    return nil
end

function ShareStreetInfo:getUnlockedAreaIdCount()
    return #self._areaIdList
end

function ShareStreetInfo:getPicture()
    return self._picture
end

function ShareStreetInfo:setPicture(str)
    self._picture = str
end

function ShareStreetInfo:getLikeCount()
    return self._likeCount
end

function ShareStreetInfo:getVisitCount()
    return self._visitCount
end

function ShareStreetInfo:getDisplayId()
    return self._displayId
end

return ShareStreetInfo