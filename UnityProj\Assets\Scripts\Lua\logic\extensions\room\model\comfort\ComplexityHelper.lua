module("logic.extensions.room.model.comfort.ComplexityHelper", package.seeall)

local ComplexityHelper = class("ComplexityHelper")

function ComplexityHelper.getMaxComplexity()
	if not HouseModel.instance:getRoomParams(RoomParams.Editor_Complexity_Enable) then return 0 end
	local totalCount = HouseModel.instance:getUserProperty(HouseUserProperty.MaxCoordCount)
	if totalCount == nil then
		totalCount = ComplexityHelper.resetMaxComplexity()
	end
	local maxComplexity = math.floor(totalCount * tonumber(GameUtils.getCommonConfig("RoomCanUseRate")))
	printInfo(string.format("--------可摆放总数：%s", maxComplexity))
	return maxComplexity
end

function ComplexityHelper.getCurrentComplexity()
	if not HouseModel.instance:getRoomParams(RoomParams.Editor_Complexity_Enable) then return 0 end
	local complexity = HouseModel.instance:getUserProperty(HouseUserProperty.CurCoordCount)
	if complexity == nil then
		local model = SceneManager.instance:getCurScene().unitFactory:getHouseMo()
		complexity = ComplexityHelper._getTotalComplexity(model:getRoomFurnitureList()) + ComplexityHelper._getTotalComplexity(model:getRoomSurfaceList())
		HouseModel.instance:setUserProperty(HouseUserProperty.CurCoordCount, complexity)
	end
	printInfo("--当前保存已用的格子数：", complexity)
	return complexity
end

function ComplexityHelper.enoughComplexityToPlace(furnitureIds, showTips)
	if RoomSceneStage.TestPlaceFurniture then return true end
	if furnitureIds == nil then return true end
	if not HouseModel.instance:getRoomParams(RoomParams.Editor_Complexity_Enable) then return true end
	--不在自己小屋小岛，无法知道复杂度
	if not HouseModel.instance:isMyHouse() or not HouseModel.instance:isInHouseOrIsland() then
		return true
	end
	showTips = showTips == nil and true or showTips
	local curCount = ComplexityHelper.getEditComplexity()
	local change = 0
	for _, furnitureId in ipairs(furnitureIds) do
		if not RoomConfig.isPaper(furnitureId) then
			local w, h = RoomConfig.getFurnitureSize(furnitureId)
			change = w * h + change
		end
	end
	local maxCount = ComplexityHelper.getMaxComplexity()
	local enough = curCount + change <= maxCount
	
	printInfo(string.format("--------尝试摆放的格子数：%s，当前摆放格子总数：%s， 可摆放总数：%s，是否通过：%s", change, curCount, maxCount, enough and "是" or "否"))
	if showTips and not enough then
		FlyTextManager.instance:showFlyText(lang("拥挤度已满，无法摆放。"))
	end
	return enough
end

function ComplexityHelper.resetMaxComplexity()
	if not HouseModel.instance:isMyHouse() or not HouseModel.instance:getRoomParams(RoomParams.Editor_Complexity_Enable) then
		if enableDebug then
			ComplexityHelper._getTotalComplexity(HouseModel.instance.houseMo:getRoomFurnitureList(), true)
			ComplexityHelper._getTotalComplexity(HouseModel.instance.houseMo:getRoomSurfaceList(), true)
			local houseType = HouseModel.instance:getUserProperty(HouseUserProperty.HouseType)
			if houseType == nil then return end
			-- local mapData = require(RoomConfig.getHouseMapDataClsName(houseType))
			local totalCount = ComplexityHelper._calCoord(houseType, HouseModel.instance:getAreaModel():getUnlockAreaIds())
			printInfo(string.format("--------可摆放格子总数：%s", math.floor(totalCount * tonumber(GameUtils.getCommonConfig("RoomCanUseRate")))))
		end
		return
	end
	local houseType = HouseModel.instance:getUserProperty(HouseUserProperty.HouseType)
	if houseType == nil then return end
	-- local mapData = require(RoomConfig.getHouseMapDataClsName(houseType))
	local unlockAreas = HouseModel.instance:getAreaModel():getUnlockAreaIds()
	local totalCount = ComplexityHelper._calCoord(houseType, unlockAreas)
	-- if #unlockAreas >= 26 and HouseModel.instance:isInIsland() then
	-- 	totalCount = totalCount - 16966
	-- end
	HouseModel.instance:setUserProperty(HouseUserProperty.MaxCoordCount, totalCount)
	printInfo(string.format("--------刷新地图格子总数：%s", totalCount))
	return totalCount
end

function ComplexityHelper.getEditComplexity(needPrint)
	if RoomEditModel.instance:isEdit() then
		local all = ComplexityHelper._getTotalComplexity(RoomEditFurnitureModel.instance:getEditFurnitureList(), needPrint)
		all = all + ComplexityHelper._getTotalComplexity(RoomEditFurnitureModel.instance:getEditSurfaceList(), needPrint)
		print("--------当前已摆放格子数：", all)
		return all
	else
		return ComplexityHelper.getCurrentComplexity()
	end
end

function ComplexityHelper.resetCurComplexity()
	if not HouseModel.instance:getRoomParams(RoomParams.Editor_Complexity_Enable) then return end
	HouseModel.instance:setUserProperty(HouseUserProperty.CurCoordCount)
	local curent = ComplexityHelper.getEditComplexity(true)
	HouseModel.instance:setUserProperty(HouseUserProperty.CurCoordCount, curent)
	printInfo(string.format("--------刷新已摆放格子总数：%s", curent))
end

function ComplexityHelper._calCoord(houseType, areaIds)
	local mapData = require(RoomConfig.getHouseMapDataClsName(houseType))
	local floorCount = ComplexityHelper._calAreaCoord(mapData.floorAreaEndIndexs, areaIds, houseType)
	local leftWallCount = ComplexityHelper._calAreaCoord(mapData.wallLeftEndIndexs, areaIds, houseType)
	local rightWallCount = ComplexityHelper._calAreaCoord(mapData.wallRightEndIndexs, areaIds, houseType)
	return floorCount + leftWallCount + rightWallCount
end

function ComplexityHelper._calAreaCoord(data, areaIds, houseType)
	local count = 0
	for i = 1, #areaIds do
		if data[i] == nil or RoomConfig.getAreaConfig(houseType, i).complexity == false then
		elseif areaIds[i] == 1 then
			count = count + data[i]
		elseif data[i - 1] ~= nil then
			count = count + data[i] - data[i - 1]
		end
	end
	return count
end

function ComplexityHelper.getEditComplexityChange()
	if RoomEditModel.instance:isEdit() and HouseModel.instance:getRoomParams(RoomParams.Editor_Complexity_Enable) then
		local model = SceneManager.instance:getCurScene().unitFactory:getHouseMo()
		local saveComplexity = ComplexityHelper._getTotalComplexity(model:getRoomFurnitureList()) + ComplexityHelper._getTotalComplexity(model:getRoomSurfaceList())
		local editComplexity = ComplexityHelper._getTotalComplexity(RoomEditFurnitureModel.instance:getEditFurnitureList()) + ComplexityHelper._getTotalComplexity(RoomEditFurnitureModel.instance:getEditSurfaceList())
		return editComplexity - saveComplexity
	else
		return 0
	end
end

function ComplexityHelper._getTotalComplexity(list, needPrint)
	local result = 0
	for _, furnitureMO in ipairs(list) do
		if furnitureMO and ComplexityHelper.isEnable(furnitureMO.id) then
			if enableDebug and needPrint then
				print("--------家具占位数：", furnitureMO.id, furnitureMO.cfg.name, ComplexityHelper.getFurnitureComlexity(furnitureMO.id))
			end
			result = result + ComplexityHelper.getFurnitureComlexity(furnitureMO.id)
		end
	end
	print("--------家具占位总数：", result)
	return result
end

function ComplexityHelper.getFurnitureComlexity(furnitureId)
	local building = RoomConfig.getBuildUnlockByItemId(furnitureId)
	if building and not HouseModel.instance:getAreaModel():isBuildingUnlock(building.id) then
		return 0
	else
		local width, height = RoomConfig.getFurnitureSize(furnitureId)
		return width * height
	end
end

function ComplexityHelper.isEnable(id)
	return not(RoomConfig.isPaper(id) or RoomConfig.isHouse(id))
end

return ComplexityHelper 