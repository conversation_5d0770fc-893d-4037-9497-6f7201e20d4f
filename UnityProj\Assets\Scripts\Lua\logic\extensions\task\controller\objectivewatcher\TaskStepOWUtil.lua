module("logic.extensions.task.controller.objectivewatcher.TaskStepOWUtil", package.seeall)

local TaskStepOWUtil = class("TaskStepOWUtil")

function TaskStepOWUtil.getTypeDesc(typeParam, typeDefine)
    -- print("TaskStepOWUtil.getTypeDesc", typeParam, type(typeParam), typeDefine)
    if typeDefine then
        if typeDefine == "LotteryEgg" then
            return LotteryConfig.getLotteryTypeName(typeParam)
        end
        if typeDefine == "Insect" then
            return CatchingInsectConfig.getTypeInfo(typeParam)
        end
    else
        return ""
    end
end

function TaskStepOWUtil.getCommonDesc(srcDesc, targetParams, objectiveDefine)
    local desc = srcDesc or objectiveDefine.descPattern
    if targetParams ~= nil then
        if targetParams.num then
            desc = string.gsub(desc, "{num}", targetParams.num)
        end
        if targetParams.count then
            desc = string.gsub(desc, "{count}", targetParams.count)
        end
        if targetParams.type then
            desc =
                string.gsub(
                desc,
                "{type}",
                TaskStepOWUtil.getTypeDesc(targetParams.type, objectiveDefine.params.typeDefine)
            )
        end
        if objectiveDefine.params and objectiveDefine.params.numTypeList then
            for _, numTypeKey in ipairs(objectiveDefine.params.numTypeList) do
                local pattern = string.format("{%s}", numTypeKey)
                print("pattern", pattern)
                desc = string.gsub(desc, pattern, targetParams[numTypeKey])
            end
        end
        if targetParams.id then
            local name
            if objectiveDefine.params and objectiveDefine.params.idType then
                local idType = objectiveDefine.params.idType
                if idType == "Building" then
                    name = RoomConfig.getBuildUnlock(targetParams.id).name
                elseif idType == "pose" then
                    name = PoseConfig.getPoseDefine(targetParams.id).name
                elseif idType == "SeaTech" then
                    name = SeaTechTreeConfig.getTechTree(targetParams.id).name
                elseif idType == "BreakoutLevel" then
                    name = BreakoutConfig.getLevelById(targetParams.id).levelName
                elseif idType == "task" then
                    name = TaskConfig.instance:getTaskCO(targetParams.id).name
                end
            else
                local define = ItemService.instance:getDefine(targetParams.id)
                if define then
                    name = define.name
                else
                    name = lang("未知物品") .. tostring(targetParams.id)
                end
            end
            desc = string.gsub(desc, "{id}", name)
        end
        local level = targetParams.level
        if level then
            desc = string.gsub(desc, "{level}", level)
        end
        local npcId = targetParams.npcId
        if npcId then
            local npcDefine = NpcConfig.getNpcDefine(npcId)
            local npcName
            if npcDefine then
                npcName = npcDefine.name
            else
                npcName = lang("未知NPC") .. tostring(npcId)
            end
            desc = string.gsub(desc, "{npcId}", npcName)
        end
        local groceryId = targetParams.groceryId
        if groceryId then
            desc = string.gsub(desc, "{groceryId}", GroceryConfig.getGroceryDefine(groceryId).name)
        end
        local chapterId = targetParams.chapterId
        if chapterId then
            local chapterCO = TaskChapterConfig.instance:getChapterCO(chapterId[1])
            local coList = TaskChapterModel.instance:getChapterCOList(TaskChapterType.Story)
            local index = table.indexof(coList, chapterCO)
            desc = string.gsub(desc, "{chapterId}", PjAobi.CSGameUtils.ConvertNumber2Chinese(index))
        end
        local goodsId = targetParams.goodsId
        if goodsId then
            if objectiveDefine.params then
                local goodsName
                if objectiveDefine.params.goodsIdType == "RechargeStore" then
                    goodsName = RechargeConfig.getGoodsConfig(goodsId).name
                elseif objectiveDefine.params.goodsIdType == "SuitStore" then
                    goodsName = TimeStoreConfig.getSuitCfgById(goodsId).name
                end
                desc = string.gsub(desc, "{goodsId}", goodsName)
            end
        end
        local actId = targetParams.actId
        if actId then
            desc = string.gsub(desc, "{actId}",  ActivityConfig.getActivityConfig(actId).activityName)
        end
        local funcId = targetParams.funcId
        if funcId then
            local co = FuncUnlockConfig.getUnlockInfoCO(funcId)
            desc = string.gsub(desc, "{funcId}",  co.name)
        end
        local sceneId = targetParams.sceneId
        if sceneId then
            local sceneName = SceneConfig.getSceneConfig(sceneId).name
            desc = string.gsub(desc, "{sceneId}", sceneName)
        end
        local insectId = targetParams.insectId
        if insectId then
            local insectCfg = CatchingInsectConfig.getInsectConfigById(insectId)
            desc = string.gsub(desc, "{insectId}", insectCfg.name)
        end
        if targetParams.houseAreaId then
            desc = string.gsub(desc, "{houseAreaId}", RoomConfig.getShowAreaConfig(targetParams.houseAreaId).defaultName)
        end
        if targetParams.fishId then
            desc = string.gsub(desc, "{fishId}", ItemService.instance:getDefine(targetParams.fishId).name)
        end
    end
    return desc
end

function TaskStepOWUtil.checkInitCommonGoMap()
    if TaskStepOWUtil.goMap == nil then
        TaskStepOWUtil.goMap = {}
        TaskStepOWUtil.goMap["EnterSeaIslandAndPointToEdit"] = TaskStepOWUtil.enterSeaIslandAndPointToEdit
        TaskStepOWUtil.goMap["ShowMapPanel"]  = TaskStepOWUtil.showMapPanel
        TaskStepOWUtil.goMap["ShowGuideArrow"] = TaskStepOWUtil.showGuideArrow
    end
end

function TaskStepOWUtil.handleCommonGo(targetParams, objectiveDefine, clsParams)
    if objectiveDefine.goFuncUnlockCheck then
        for _, funcId in ipairs(objectiveDefine.goFuncUnlockCheck) do
            local isUnlock = FuncUnlockFacade.instance:checkIsUnlocked(funcId, true)
            if not isUnlock then
                return
            end
        end
    end
    clsParams = clsParams or objectiveDefine.params
    local checkParam = clsParams["checkParam"]
    if checkParam then
        for k, v in ipairs(checkParam) do
            if v.key == "JoinCouncil" then
                if not CouncilService.instance:isUserJoinedCouncil() then
                    ViewMgr.instance:clearBackStack()
                    CouncilGotoUtil.gotoCouncilHall()
                    return
                end
            elseif v.key == "ElfEgg" then
                if ElfModel.instance:getMyElfInfo() == nil or ElfModel.instance:getMyElfInfo():isEgg() then
                    ViewMgr.instance:clearBackStack()
                    FarmFacade.instance:enterAndPoint({13000707})
                    return
                end
            elseif v.key == "ElfRoom" then
                local item = ItemService.instance:getItem(13000729) --精灵的家
                if item and item.num > 0 and item:getUseNum() == 0 then
                    ViewFacade.openBackPack(13000729)
                    return
                end
                item = ItemService.instance:getItem(16000064) --时空宝箱
                if item and item.num > 0 and item:getUseNum() == 0 then
                    ViewFacade.openBackPack(16000064)
                    return
                end
            end
        end
    end
    local goType = clsParams["goType"]
    local goParam = clsParams["goParam"]
    local goParam2 = clsParams["goParam2"]
    local goParam3 = clsParams["goParam3"]

    TaskStepOWUtil.checkInitCommonGoMap()
    if TaskStepOWUtil.goMap then
        local goFunc = TaskStepOWUtil.goMap[goType]
        if goFunc then
            goFunc(targetParams, objectiveDefine, clsParams)
            return
        end
    end --使用map匹配效率高一些

    if goType == "GotoLocation" then
        ViewMgr.instance:clearBackStack()
        local arriveHandler
        if goParam.onEnd then
            arriveHandler =
                handler(
                function()
                    TaskStepOWUtil.handleCommonGo(targetParams, objectiveDefine, goParam.onEnd)
                end
            )
        end
        TaskSceneCmdHelper.gotoLocation(goParam.sceneId, goParam.posX, goParam.posY, arriveHandler)
    elseif goType == "GotoTrigger" then
        ViewMgr.instance:clearBackStack()
        TaskSceneCmdHelper.walkToTrigger(goParam.sceneId, goParam.name)
    elseif goType == "GotoNPC" then
        ViewMgr.instance:clearBackStack()
        local npcId = goParam and goParam.npcId or NpcConfig.getSceneNpcId(targetParams.npcId)
        npcId = NpcConfig.getSceneNpcId(npcId)
        NpcService.instance:gotoNpc(npcId, nil, true)
    elseif goType == "GotoSceneNPC" then
        --- 上面的GotoNpc会把假身的npcId转换成原来的npcId，所以这里加一个新的
        ViewMgr.instance:clearBackStack()
        local npcId = goParam and goParam.npcId or targetParams.npcId
        NpcService.instance:gotoNpc(npcId, nil, true)
    elseif goType == "GotoElf" then
        ViewMgr.instance:clearBackStack()
        ElfFacade.instance:gotoElf()
    elseif goType == "GotoElfDress" then
        ViewMgr.instance:clearBackStack()
        ElfFacade.instance:openDressPanel()
    elseif goType == "GotoFurniture" then
        ViewMgr.instance:clearBackStack()
        RoomFacade.instance:enterAndPoint(goParam)
    elseif goType == "GotoGrocery" then
        local co = GroceryConfig.getGroceryDefine(targetParams.groceryId)
        if co.sceneId and co.sceneId > 0 then
            ViewMgr.instance:clearBackStack()
            TaskSceneCmdHelper.gotoLocation(co.sceneId, co.pos[1], co.pos[2])
        else
            GroceryFacade.instance:showView(co.groceryId)
        end
    elseif goType == "GotoUnlockArea" then
        ViewMgr.instance:clearBackStack()
        FarmFacade.instance:enterAndPointBuilding(targetParams.id)
    elseif goType == "ShowView" then
        ViewMgr.instance:clearBackStack()
        local viewName = goParam
        if viewName == "Lottery" then
            LotteryController.instance:openLotteryMainView(targetParams.type)
        elseif viewName == "InfoCardPanel" then
            ViewFacade.showUserInfo(UserInfo.userId)
        elseif viewName == "PetHome" then
            PetFacade.instance:showPetHome(UserInfo.userId, goParam2)
        elseif viewName == "OrderMain" then
            OrderController.instance:showOrderMain(targetParams.id, true)
        elseif viewName == "FashionRace" then
            FashionRaceController.instance:openMainPanel()
        elseif viewName == "TravellerMainPanel" and targetParams.npcId then
            ViewMgr.instance:open(viewName, TravellerConfig.getTravellerByNpcId(targetParams.npcId).id, goParam2)
        elseif viewName == "TravellerLibrary" then
            FarmFacade.instance:enterAndOpenLibrary()
        elseif viewName == "Aquarium" then
            AquariumController.instance:showMainView()
        elseif viewName == "TimeStore" then
            RechargeFacade.showRechargeView(CurrencyId.AoBi)
        elseif viewName == "TalentMainView" then
            TalentUtil.doOpenMainView()
        elseif viewName == "GameMachineView" then
            GameRoomFacade.openGameMachinePanel(false, goParam2.page, goParam2.gameId)
        -- elseif viewName == "ActivityCalendar" then
        --     ViewMgr.instance:open(viewName, {id = goParam2})
        elseif viewName == "TaskObjectivePanel" and clsParams["idType"] == "task" then
            ViewMgr.instance:open(viewName, goParam2, targetParams.id)
        else
            print(viewName, goParam2, goParam3)
            ViewMgr.instance:open(viewName, goParam2, goParam3)
        end
    elseif goType == "GoFishing" then
        ViewMgr.instance:clearBackStack()
        print("GoFishing", FishingConfig.getAllFishConfig()[1].award[1].id)
        CollectService.instance:gotoCollect(FishingConfig.getAllFishConfig()[1].award[1].id)
    elseif goType == "ShowCollect" then
        ViewMgr.instance:clearBackStack()
        CollectService.instance:gotoCollect(tonumber(goParam))
    elseif goType == "ShowWorkshop" then
        -- ViewMgr.instance:clearBackStack()
        local workShopId =
            clsParams and clsParams["workShopId"] or
            FurnitureConfig.getWorkShopTypeByFurniture(tonumber(targetParams.id))
        local isUpgrade = clsParams and clsParams["isUpgrade"] == true
        WorkshopService.instance:openAppointWorkshop(workShopId, isUpgrade, true)
    elseif goType == "EnterNPCRoomScene" then
        ViewMgr.instance:clearBackStack()
        -- print(targetParams.npcId, goParam)
        RoomFacade.instance:enterNpcRoomScene(tostring(targetParams.npcId))
    elseif goType == "HouseUpgrade" then
        ViewMgr.instance:clearBackStack()
        FarmFacade.instance:enterAndPointUpgradeByFurniture(targetParams.id)
    elseif goType == "MyHouse" then
        ViewMgr.instance:clearBackStack()
        local onEnd
        if goParam.arrowKey then
            onEnd =
                handler(
                function()
                    GuideArrowManager.instance:setToShowKey(goParam.arrowKey, goParam.uiOffset, goParam.scale)
                end
            )
        end
        RoomFacade.instance:enterRoomScene(UserInfo.userId, nil, nil, nil, nil, onEnd)
    elseif goType == "MyIsland" then
        ViewMgr.instance:clearBackStack()
        if goParam and goParam.pointId then
            FarmFacade.instance:enterAndPoint(goParam.pointId)
        else
            FarmFacade.instance:enterOthersIsland(UserInfo.userId)
        end
    elseif goType == "MyElfRoom" then
        ViewMgr.instance:clearBackStack()
        RoomFacade.instance:enterElfRoom()
    elseif goType == "OpenMyIsland" then
        ViewMgr.instance:clearBackStack()
        FarmFacade.instance:enterAndOpenMyIsland(goParam)
    elseif goType == "DaoDanGui" then
        ViewMgr.instance:clearBackStack()
        ActivityGotoUtil.showDaoDanDaHuiView(goParam)
    elseif goType == "LoadScene" then
        ViewMgr.instance:clearBackStack()
        SceneManager.instance:loadSceneByAliasId(tonumber(goParam))
    elseif goType == "RechargeFacade.openStoreByGoodsId" then
        RechargeFacade.openStoreByGoodsId(targetParams.goodsId)
    elseif goType == "RechargeFacade.openStoreBySuiteId" then
        RechargeFacade.openStoreBySuiteId(targetParams.goodsId)
    elseif goType == "GotoFarmAndOpenEdit" then
        FarmFacade.instance:enterAndOpenEdit(goParam.subType)
    elseif goType == "GOF" then
        ViewMgr.instance:clearBackStack()
        GOFFacade.instance:open(goParam.id, true)
    elseif goType == "Archive" then
        ViewMgr.instance:clearBackStack()
        ArchiveFacade.instance:open(goParam, goParam2)
    elseif goType == "Activity" then
        ViewMgr.instance:clearBackStack()
        ActivityGotoUtil.gotoActivity(goParam or targetParams.actId, goParam2 or false)
    elseif goType == "MultiActivity" then
        ViewMgr.instance:clearBackStack()
        local list = string.splitToNumber(goParam or targetParams.actId, ",")
        for _, id in ipairs(list) do
            if ActivityGotoUtil.gotoActivity(id, false, false) then
                return
            end
        end
    elseif goType == "NewspaperDetail" then
        ViewMgr.instance:clearBackStack()
        local list = AobiNewspaperConfig.getAllNewspaperDefines()
        ViewMgr.instance:open("NewspaperDetail", list[#list])
    elseif goType == "EcoParkEdit" then
        ViewMgr.instance:clearBackStack()
        EcoParkFacade.jumpToEditView()
    elseif goType == "EcoParkStore" then
        ViewMgr.instance:clearBackStack()
        EcoParkFacade.jumpToStore()
    elseif goType == "gameRoomWalkToTrigger" then
        ViewMgr.instance:clearBackStack()
        GameRoomFacade.walkToTriggerAndOpenEntryPanel(goParam)
    elseif goType == "GotoSeaAndCatchFish" then
        ViewMgr.instance:clearBackStack()
        local depth = targetParams.depth or 1
        SeaFacade.gotoSea(depth)
    elseif goType == "ShowSeaOrder" then
        ViewMgr.instance:clearBackStack()
        OrderController.instance:showOrderMain(nil, nil, 2)
    elseif goType == "UnlockSeaArea" then
        ViewMgr.instance:clearBackStack()
        SeaFacade.gotoSea()
    elseif goType == "GotoSeaAndOpenBox" then
        ViewMgr.instance:clearBackStack()
        SeaFacade.gotoSea()
    elseif goType == "EnterAndPointArea" then
        ViewMgr.instance:clearBackStack()
        FarmFacade.instance:enterAndPointShowArea(targetParams.houseAreaId)
    elseif goType == "EnterAndOpenTech" then
        ViewMgr.instance:clearBackStack()
        FarmFacade.instance:enterAndOpenTech()
    elseif goType == "EnterAndOpenOrder" then
        ViewMgr.instance:clearBackStack()
        FarmFacade.instance:enterAndOpenOrder()
    elseif goType == "EnterAndOpenTechUpgrade" then
        ViewMgr.instance:clearBackStack()
        FarmFacade.instance:enterAndOpenTechUpgrade()
    else
        printError("未处理的类型", goType)
    end
end

function TaskStepOWUtil.handleItemGo(viewName, itemId)
    if viewName == "DressPanel" then
        ViewFacade.openDressPanel(itemId)
        return
    end
    ViewMgr.instance:open(viewName, itemId)
end

function TaskStepOWUtil.enterSeaIslandAndPointToEdit(targetParams, objectiveDefine, clsParams)
    ViewMgr.instance:clearBackStack()
    
    local sceneId = 79
    local enterFinishHandler = handler(function()
        settimer(2, function()
            local arrowGo = goutil.find("commonhud(Clone)/mid/Room/bottom/btnGroup/btnEdit")
            if not goutil.isNil(arrowGo) then
                GuideArrowManager.instance:addArrowAtGO(arrowGo)
            end
        end, nil, false)
    end)

    if not FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.SeaIsland, false) then
        RoomPointer.instance:pointTo(RoomPointer.OpenSeabedTech, {upgrade = true}, SceneType.Island) --用来跳转指向山洞
        return
    end
    if SceneConfig.checkEnter(sceneId) then
        SceneManager.instance:loadScene(sceneId, nil, nil, nil, enterFinishHandler)
    end
end

function TaskStepOWUtil.showMapPanel(targetParams, objectiveDefine, clsParams)
    ViewMgr.instance:clearBackStack()
    local goParam = clsParams["goParam"]
    GuideArrowManager.instance:setToShowKey(goParam.arrowKey, goParam.uiOffset, goParam.scale)
    ViewMgr.instance:open("MapPanel")
end

function TaskStepOWUtil.showGuideArrow(targetParams, objectiveDefine, clsParams)
    ViewMgr.instance:clearBackStack()
    GuideArrowManager.instance:removeAllArrow()
    local goParam = clsParams["goParam"]
    local arrowKeyList = goParam.arrowKeyList
    for _, arrowKey in ipairs(arrowKeyList) do
        GuideArrowManager.instance:setToShowKey(arrowKey)
    end
end

return TaskStepOWUtil
