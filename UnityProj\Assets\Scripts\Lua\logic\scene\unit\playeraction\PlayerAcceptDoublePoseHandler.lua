module("logic.scene.unit.playeraction.PlayerAcceptDoublePoseHandler",package.seeall)
local PlayerAcceptDoublePoseHandler = class("PlayerAcceptDoublePoseHandler", PlayerShortActionHandler)

function PlayerAcceptDoublePoseHandler:onStart()
	local poseId = tonumber(self.info.params[1])
	local cfg = PoseConfig.getDoublePose(poseId)	
	self.targetId =  self.info.params[2]
	self.unit.skinView:loadAnimation({"anif_shuangrendongzuo","anib_shuangrendongzuo"}, self._playPose, self)
end

function PlayerAcceptDoublePoseHandler:_playPose()
	local targetUnit = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SceneManager.instance:getCurScenePlayerUnitType(), self.targetId)
	if targetUnit then
		GlobalDispatcher:addListener(GlobalNotify.OnSceneCameraUpdate, self.onSceneCameraUpdate, self)
		GlobalDispatcher:addListener(GlobalNotify.PlayerLeave, self.onPlayerLeave, self)
		self.lastX, self.lastY = targetUnit:getPos()
		if not VirtualCameraMgr.instance:is3DScene() then
			self.unit.notSort = true
			targetUnit.notSort = true
		end
		self.unit:setFace(true)
		self.targetUnit = targetUnit
		targetUnit:setPos(self:_getPosePos())
		local cfg = PoseConfig.getDoublePose(tonumber(self.info.params[1]))
		local dir = self.unit:getDirection()
		local isReverse = UnitDirection.isFaceLeft(dir)
		if isReverse then
			dir = cfg.mainDir
			local leftOrRight = UnitDirection.isFaceLeft(dir)
			self.unit:setDirection(UnitDirection.setLeftRight(dir, not leftOrRight))
			dir = cfg.subDir
			leftOrRight = UnitDirection.isFaceLeft(dir)
			targetUnit:setDirection(UnitDirection.setLeftRight(dir, not leftOrRight))
		else
			self.unit:setDirection(cfg.mainDir)
			targetUnit:setDirection(cfg.subDir)
		end
		if cfg.mainHideHandItem >= 0 then
			self.unit.skinView:hideHandItem("pose", cfg.mainHideHandItem)
		end
		if cfg.subHideHandItem >= 0 then
			targetUnit.skinView:hideHandItem("pose", cfg.subHideHandItem)
		end
		self.unit.skinView:getAnimHelper():setAnimation(nil, false, 3)
		targetUnit.skinView:getAnimHelper():setAnimation(nil, false, 3)
		targetUnit.skinView:setAnimation(cfg.subAniName, false, true)
		targetUnit.idleComp:setEnable(false)
		self.unit.skinView:setAnimation(cfg.mainAniName, false, true)
		self.unit.skinView:getAnimHelper():addCompleteCallback(cfg.mainAniName, self.onEatFinish, self)
		if cfg.mainClothes > 0 then
			self.unit.skinView:addTempClothes(cfg.mainClothes)
		end
		if not string.nilorempty(cfg.mainEffect) then
			local slot = self.unit.effectLoader:getSlot(EffectLoader.Pose)
			slot:load(GameUrl.getPoseURL(cfg.mainEffect))
		end
		if cfg.subClothes > 0 then
			targetUnit.skinView:addTempClothes(cfg.subClothes)
		end	
		if not string.nilorempty(cfg.subEffect) then
			local slot = targetUnit.effectLoader:getSlot(EffectLoader.Pose)
			slot:load(GameUrl.getPoseURL(cfg.subEffect))
		end
		if cfg.sound then
			if iskindof(self.unit, "UIModel") then
				SoundManager.instance:playEffect(cfg.sound)
			else
				SoundManager.instance:playEffect(cfg.sound, self.unit.go)
			end
		end		
	else
		self:onEatFinish(true)
	end
end

function PlayerAcceptDoublePoseHandler:_getPosePos()
    local pos = GameUtils.getPosInScene(self.unit.go)
    local cfg = PoseConfig.getDoublePose(tonumber(self.info.params[1]))
    local dir = self.unit:getDirection()
    local isReverse = UnitDirection.isFaceLeft(dir)
    local offsetX = isReverse and -cfg.subOffset[1] or cfg.subOffset[1]
    local offsetY = cfg.subOffset[2]
	if SceneManager.instance:isInFree3DScene() then
		local p = self.unit.go.transform:TransformPoint(offsetX, 0, offsetY)
		return p.x, p.z, p.y
	elseif VirtualCameraMgr.instance:is3DScene() then
		return pos.x + offsetX, pos.y + offsetY, pos.z
	else
        return pos.x + offsetX, pos.y, pos.z + offsetY * 0.1
	end
end

function PlayerAcceptDoublePoseHandler:onPlayerLeave(userList)
	for i=1,#userList do
		if userList[i] == self.targetId then
			self:finish()
		end
	end
end

function PlayerAcceptDoublePoseHandler:onSceneCameraUpdate()
	GameUtils.setPosInScene(self.targetUnit.go, self:_getPosePos())
end

function PlayerAcceptDoublePoseHandler:onStop()
	local cfg = PoseConfig.getDoublePose(tonumber(self.info.params[1]))
	GlobalDispatcher:removeListener(GlobalNotify.OnSceneCameraUpdate, self.onSceneCameraUpdate, self)
	GlobalDispatcher:removeListener(GlobalNotify.PlayerLeave, self.onPlayerLeave, self)
	local targetUnit = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SceneManager.instance:getCurScenePlayerUnitType(), self.targetId)	
	if targetUnit then
		if cfg.subHideHandItem >= 0 then
			targetUnit.skinView:showHandItem("pose", cfg.subHideHandItem)
		end
		self.unit.skinView:setAnimation(self.unit:getIdleAniName(), true)		
		self.unit.idleComp:setEnable(true)
		targetUnit:setPos(self.lastX, self.lastY)
		if cfg.subClothes > 0 then
			targetUnit.skinView:removeTempClothes(cfg.subClothes)
		end	
		if not string.nilorempty(cfg.subEffect) then
			local slot = targetUnit.effectLoader:getSlot(EffectLoader.Pose)
			slot:clear()
		end			
		targetUnit.notSort = nil			
		self.unit.notSort = nil			
	end
	self.unit.skinView:getAnimHelper():removeCompleteCallback(nil, self.onEatFinish, self)
	if self.unit.isUser or self.targetId == UserInfo.userId then
		SceneController.instance:localNotify(SceneNotify.DoublePoseFinish)
	end
	if cfg.mainHideHandItem >= 0 then
		self.unit.skinView:showHandItem("pose", cfg.mainHideHandItem)
	end	
	if cfg.mainClothes > 0 then
		self.unit.skinView:removeTempClothes(cfg.mainClothes)
	end
	if not string.nilorempty(cfg.mainEffect) then
		local slot = self.unit.effectLoader:getSlot(EffectLoader.Pose)
		slot:clear()
	end
end

function PlayerAcceptDoublePoseHandler:onEatFinish(isComplete)
	self.unit.skinView:removeLoadAnimationCallback(self._playPose, self)
	if isComplete then
		self:finish()
	end
end
return PlayerAcceptDoublePoseHandler