-- {excel:W物品配置.xlsx, sheetName:export_道具}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_prop", package.seeall)

local title = {id=1,subType=2,name=3,desc=4,quality=5,canSell=6,canTrade=7,gainCoin=8,diamondValue=9,limit=10,hasCapacity=11,properties=12,source=13,exchangeItems=14,showInBag=15,icon=16}

local dataList = {
	{16000001, 1, "小麦汁", "和朋友一起来杯小麦汁，干杯！", 1, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=2,randomDrop={3,1}}, "", nil, 0, 0},
	{16000002, 1, "丸子", "美味的章鱼丸子，适合一人一颗分着吃。", 1, true, false, 185, 5, 99, false, {randomRate=0.2,fixedDrop={3,1},count=3,action=1,randomDrop={3,1}}, "", nil, 0, 0},
	{16000003, 1, "炸鸡", "金灿灿的炸鸡！闻到香味了吗？", 1, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=1,randomDrop={3,1}}, "", nil, 0, 0},
	{16000004, 1, "烟花", "挥舞着烟花棒，去度过美好的时间吧。", 1, true, false, 185, 5, 99, false, {randomRate=0.2,fixedDrop={3,1},count=3,action=3,randomDrop={3,1}}, "", nil, 0, 0},
	{16000005, 7, "金币服装彩蛋锤", "夜西制作，用于砸击金币服装彩蛋，祝你好运。", 4, false, false, 100, 40, 99, false, {type=2}, "", nil, 0, 0},
	{16000006, 7, "晶钻服装彩蛋锤", "夜西制作，用于砸击晶钻服装彩蛋，祝你好运。", 4, false, false, 100, 60, 99, false, {type=3}, "", nil, 0, 0},
	{16000007, 7, "爱心服装彩蛋锤", "夜西制作，用于砸击爱心服装彩蛋，祝你好运。", 4, false, false, 100, 50, 99, false, {type=4}, "", nil, 0, 0},
	{16000008, 3, "渔夫金鱼宝箱", "被神奇锦鲤守护着的的宝箱，里面一定藏着巨额财富。", 3, false, false, 400, 10, 99, false, {type=1,rewards={2}}, "", nil, 0, 0},
	{16000009, 3, "星空套装自选包", "打开后可选择一套获得：星空幻想套装或星河漫游套装", 5, false, false, 10, 1, 1, false, {count=1,type=2,rewards={37,38}}, "", nil, 0, 0},
	{16000010, 2, "摇汽水", "超级巨型的汽水，会被哪个倒霉蛋摇爆呢？", 1, true, false, 500, 13, 99, false, {gameId=1}, "", nil, 0, 0},
	{16000011, 8, "晴天泡泡", "一次性使用，巨大作物概率加50%，对藜麦、亚麻、甜菜、芹菜、黄金蓖麻有效。", 2, true, false, 100, 1, 99, false, {harvest={15000001,15000004,15000007,15000010,15000013},icon=16000011,id=11,value=5000}, "", nil, 0, 0},
	{16000012, 8, "雨天泡泡", "一次性使用，巨大作物概率加50%，对蓝莓、圣女果、洋葱、香蜂草、彩虹蔷薇有效。", 2, true, false, 100, 1, 99, false, {harvest={15000002,15000005,15000008,15000011,15000014},icon=16000012,id=11,value=5000}, "", nil, 0, 0},
	{16000013, 8, "彩虹泡泡", "一次性使用，巨大作物概率加50%，对熊瓜、甜薯、葡萄、曼德拉草有效。", 2, true, false, 100, 1, 99, false, {harvest={15000003,15000006,15000009,15000012},icon=16000013,id=11,value=5000}, "", nil, 0, 0},
	{16000014, 15, "初级变异肥料", "一次性使用，使魔法植物变异的概率增加10%。", 2, true, false, 100, 10, 99, false, {id=10,value=1000}, "", nil, 0, 0},
	{16000015, 15, "中级变异肥料", "一次性使用，使魔法植物变异的概率增加30%。", 3, true, false, 100, 20, 99, false, {id=10,value=3000}, "", nil, 0, 0},
	{16000016, 15, "高级变异肥料", "一次性使用，使魔法植物变异的概率增加100%。", 4, true, false, 100, 50, 99, false, {id=10,value=10000}, "", nil, 0, 0},
	{16000017, 9, "动物buff药水", "", 1, true, false, 10, 1, 99, false, {id=6,value=3000}, "", nil, 0, 0},
	{16000018, 5, "小制作经验魔药", "使用后30分钟内，工坊制作经验增加10%。", 2, true, false, 100, 2, 99, false, {duration=30,id=1,value=1000}, "", nil, 0, 0},
	{16000019, 5, "小生产加速魔药", "使用后30分钟内，新种植的作物、新一轮生产周期的动物和果树生产时间减少10%。", 2, true, false, 100, 2, 99, false, {duration=30,id=6,value=1000}, "", nil, 0, 0},
	{16000020, 5, "小制作优惠魔药", "使用后30分钟内，工坊制作费用优惠10%。", 2, true, false, 100, 2, 99, false, {duration=30,id=2,value=1000}, "", nil, 0, 0},
	{16000021, 5, "小订单经验魔药", "使用后30分钟内，订单经验奖励增加10%。", 2, true, false, 100, 2, 99, false, {duration=30,id=7,value=1000}, "", nil, 0, 0},
	{16000022, 5, "中制作经验魔药", "使用后1小时内，工坊制作经验增加50%。", 3, true, false, 100, 10, 99, false, {duration=60,id=1,value=5000}, "", nil, 0, 0},
	{16000023, 5, "中生产加速魔药", "使用后1小时内，新种植的作物、新一轮生产周期的动物和果树生产时间减少30%。", 3, true, false, 100, 10, 99, false, {duration=60,id=6,value=3000}, "", nil, 0, 0},
	{16000024, 5, "中制作优惠魔药", "使用后1小时内，工坊制作费用优惠30%。", 3, true, false, 100, 10, 99, false, {duration=60,id=2,value=3000}, "", nil, 0, 0},
	{16000025, 5, "中订单经验魔药", "使用后1小时内，订单经验奖励增加50%。", 3, true, false, 100, 10, 99, false, {duration=60,id=7,value=5000}, "", nil, 0, 0},
	{16000026, 5, "大制作经验魔药", "使用后2小时内，工坊制作经验增加100%。", 4, true, false, 100, 30, 99, false, {duration=120,id=1,value=10000}, "", nil, 0, 0},
	{16000027, 5, "大生产加速魔药", "使用后2小时内，新种植的作物、新一轮生产周期的动物和果树生产时间减少50%。", 4, true, false, 100, 30, 99, false, {duration=120,id=6,value=5000}, "", nil, 0, 0},
	{16000028, 5, "大制作优惠魔药", "使用后2小时内，工坊制作费用优惠60%。", 4, true, false, 100, 30, 99, false, {duration=120,id=2,value=6000}, "", nil, 0, 0},
	{16000029, 5, "大订单经验魔药", "使用后2小时内，订单经验奖励增加100%。", 4, true, false, 100, 30, 99, false, {duration=120,id=7,value=10000}, "", nil, 0, 0},
	{16000030, 10, "改名卡", "改名卡一枚。", 4, false, false, 100, 100, 99, false, nil, "", nil, 0, 0},
	{16000031, 11, "初级宠物笛", "使用后可以遇见并捕捉1-3星宠物。", 2, true, false, 1000, 10, 99, false, nil, "", nil, 0, 0},
	{16000032, 11, "中级宠物笛", "使用后可以遇见并捕捉2-4星宠物。", 3, true, false, 200, 40, 99, false, nil, "", nil, 0, 0},
	{16000033, 11, "高级宠物笛", "使用后可以遇见并捕捉4-5星宠物。", 4, true, false, 300, 50, 99, false, nil, "", nil, 0, 0},
	{16000034, 12, "宠物干粮", "宠物爱吃，可以增加50经验和50心情。", 2, true, false, 500, 10, 99, false, nil, "", nil, 0, 0},
	{16000035, 12, "宠物饼干", "宠物爱吃，可以增加200经验和100心情。", 3, true, false, 200, 20, 99, false, nil, "", nil, 0, 0},
	{16000036, 12, "宠物布丁", "宠物爱吃，可以增加1000经验和500心情。", 4, true, false, 300, 50, 99, false, nil, "", nil, 0, 0},
	{16000037, 13, "觉醒糖果", "宠物进化需要用到，放生宠物概率获得。", 4, true, false, 100, 20, 99, false, nil, "", nil, 0, 0},
	{16000038, 14, "初级探宝仪", "使用进行探宝，完成后获得宝箱（宝箱开启可获得挖宝树屋兑换材料）。", 2, true, false, 100, 10, 99, false, {id=16000041}, "", nil, 0, 0},
	{16000039, 14, "中级探宝仪", "使用进行探宝，完成后获得宝箱（宝箱开启可获得挖宝树屋兑换材料）。", 3, true, false, 100, 20, 99, false, {id=16000042}, "", nil, 0, 0},
	{16000040, 14, "高级探宝仪", "使用进行探宝，完成后获得宝箱（宝箱开启可获得挖宝树屋兑换材料）。", 4, true, false, 100, 50, 99, false, {id=16000043}, "", nil, 0, 0},
	{16000041, 3, "初级古老宝箱", "开启后可获得挖宝树屋兑换材料。", 2, false, false, 10, 10, 99, false, {type=1,rewards={3}}, "", nil, 0, 0},
	{16000042, 3, "中级古老宝箱", "开启后可获得挖宝树屋兑换材料。", 3, false, false, 10, 20, 99, false, {type=1,rewards={4}}, "", nil, 0, 0},
	{16000043, 3, "高级古老宝箱", "开启后可获得挖宝树屋兑换材料。", 4, false, false, 10, 50, 99, false, {type=1,rewards={5}}, "", nil, 0, 0},
	{16000044, 3, "来自远方的宝箱", "开启后有几率获得晶钻或珍贵材料。", 3, false, false, 10, 10, 99, false, {type=1,rewards={6}}, "", nil, 0, 0},
	{16000045, 3, "黑莲的礼物", "黑莲留下的谢礼，开启后可随机获得小惊喜。", 2, false, false, 10, 10, 99, false, {type=1,rewards={7}}, "", nil, 0, 0},
	{16000046, 10, "失落水晶", "被掩埋在历史尘埃中的珍贵晶石，可在挖宝树屋兑换商品。", 2, false, false, 100, 3, 99999, false, nil, "", nil, 0, 0},
	{16000047, 10, "评选赛奖杯", "可在评选赛商店兑换奖品。", 2, false, false, 100, 3, 99999, false, nil, "", nil, 0, 0},
	{16000048, 3, "石巨人的礼物", "开启后可随机获得小惊喜。", 2, false, false, 10, 10, 99, false, {type=1,rewards={7}}, "", nil, 0, 0},
	{16000049, 3, "铃当的礼物", "铃当给的小礼物，开启后可随机获得小惊喜。", 2, false, false, 10, 10, 99, false, {type=1,rewards={7}}, "", nil, 0, 0},
	{16000050, 3, "水颜的礼物", "水颜送的礼物，开启后可随机获得小惊喜。", 2, false, false, 10, 10, 99, false, {type=1,rewards={7}}, "", nil, 0, 0},
	{16000051, 10, "航海投资凭证", "龙娃航海回航后，可以用该凭证领奖励。", 1, false, false, 100, 1, 99, false, nil, "", nil, 0, 0},
	{16000052, 1, "月饼", "【分享物】满月节必备，可以和好朋友一起分享的美味。", 3, true, false, 500, 13, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=1,randomDrop={3,1}}, "", nil, 0, 0},
	{16000053, 7, "金币家具彩蛋锤", "夜西制作，用于砸击金币家具彩蛋，祝你好运。", 4, false, false, 100, 30, 99, false, {type=8}, "", nil, 0, 0},
	{16000054, 7, "晶钻家具彩蛋锤", "夜西制作，用于砸击晶钻家具彩蛋，祝你好运。", 4, false, false, 100, 40, 99, false, {type=9}, "", nil, 0, 0},
	{16000055, 7, "爱心家具彩蛋锤", "夜西制作，用于砸击爱心家具彩蛋，祝你好运。", 4, false, false, 100, 35, 99, false, {type=10}, "", nil, 0, 0},
	{16000056, 3, "日常礼盒", "开启后可随机获得小惊喜。", 3, false, false, 10, 10, 99, false, {type=1,rewards={13}}, "", nil, 0, 0},
	{16000057, 16, "初级魔法书", "宠物探险可获得，用作精灵魔法升级。", 2, true, false, 100, 10, 99, false, {id=16000041}, "", nil, 0, 0},
	{16000058, 16, "中级魔法书", "宠物探险可获得，用作精灵魔法升级。", 3, true, false, 300, 30, 99, false, {id=16000042}, "", nil, 0, 0},
	{16000059, 16, "高级魔法书", "可用作精灵魔法升级。", 4, true, false, 1000, 100, 99, false, {id=16000043}, "", nil, 0, 0},
	{16000060, 3, "漂流瓶", "里面仿佛藏着一张小纸条，要不要打开看看呢。", 3, false, false, 10, 10, 99, false, {type=1,rewards={14}}, "", nil, 0, 0},
	{16000061, 10, "雪花", "可在冰雪市集兑换商品。", 1, false, false, 100, 3, 99999, false, nil, "", nil, 0, 0},
	{16000062, 1, "棉花糖", "甜丝丝的棉花糖，记忆又回到童年的那一天。", 1, true, false, 185, 13, 99, false, {randomRate=0.2,fixedDrop={3,1},count=3,action=1,randomDrop={3,1}}, "20", nil, 0, 0},
	{16000063, 3, "友情礼物盒", "作为答谢帮忙打理友情树的回礼。", 2, false, false, 10, 10, 99, false, {type=1,rewards={21}}, "", nil, 0, 0},
	{16000064, 3, "时空宝箱", "开启后可获得小精灵纪念品。", 3, false, false, 10, 10, 99, false, {type=1,rewards={16}}, "", nil, 0, 0},
	{16000065, 3, "玄霜的礼物", "玄霜送的礼物，开启后可随机获得小惊喜。", 2, false, false, 10, 10, 99, false, {type=1,rewards={7}}, "", nil, 0, 0},
	{16000066, 3, "小耶的宝藏", "小耶送的礼物，开启后可获得小惊喜哦。", 2, false, false, 10, 10, 99, false, {type=1,rewards={18}}, "", nil, 0, 0},
	{16000067, 23, "宠物变异药水", "用于喂食进化到最终态的变异宠物，喂食后概率激活变异形态。", 4, true, false, 1000, 25, 99, false, nil, "", nil, 0, 0},
	{16000068, 1, "哈皮搞怪盒", "哈皮精心制作的搞怪盒子，可以分享吓唬一下大家哦！", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,1},count=1,action=5,randomDrop={3,1}}, "", nil, 0, 0},
	{16000069, 1, "爱心礼物盒", "奥比快乐节特供的礼物盒子，可以分享给大家小心心哦！", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,5},count=1,action=4,randomDrop={3,5}}, "", nil, 0, 0},
	{16000070, 7, "摇奖券", "用于奥比快乐节摇摇乐抽取奖品。", 5, false, false, 100, 60, 99, false, {type=108}, "", nil, 0, 0},
	{16000071, 10, "快乐徽章", "奥比快乐节专属荣誉，可在兑换小摊兑换物品。", 4, false, false, 100, 3, 99999, false, {rewardId=35,actId=112}, "", nil, 0, 0},
	{16000072, 10, "大喇叭", "可让发言被更多小奥比注意到。", 4, false, false, 200, 10, 99, false, nil, "", nil, 0, 0},
	{16000073, 28, "奥力宝", "阿拉斯在研制魔药时意外产出的功能型饮料，使用后消除10点疲劳。", 2, false, false, 200, 5, 99, false, {num=10}, "", nil, 0, 0},
	{16000074, 28, "抗疲劳魔药", "基于大贤者的创造术而研制出的魔药，使用后消除30点疲劳。", 3, false, false, 200, 15, 99, false, {num=30}, "", nil, 0, 0},
	{16000075, 28, "活力月露膏", "阿拉斯为数不多的实验成品之一，使用后消除50点疲劳。", 4, false, false, 200, 25, 99, false, {num=50}, "", nil, 0, 0},
	{16000076, 28, "舒筋活络丸", "活泼小精灵沐浴魔药后从身上搓下来的丸子，使用后消除100点疲劳。", 4, false, false, 200, 50, 99, false, {num=100}, "", nil, 0, 0},
	{16000077, 28, "凤凰之泪", "神殿的珍藏，存世量极少的稀有物品，使用后消除200点疲劳。", 4, false, false, 200, 100, 99, false, {num=200}, "", nil, 0, 0},
	{16000078, 25, "时空纸页", "蕴含时空之力的纸页，用于修补记忆链接，可提供30点经验。", 2, false, false, 100, 3, 99, false, {offerExp=30}, "", nil, 0, 0},
	{16000079, 25, "时空彩墨", "蕴含时空之力的彩墨，用于修补记忆链接，可提供50点经验。", 3, false, false, 200, 5, 99, false, {offerExp=50}, "", nil, 0, 0},
	{16000080, 25, "时空金粉", "蕴含时空之力的金粉，用于修补记忆链接，可提供100点经验。", 4, false, false, 300, 10, 99, false, {offerExp=100}, "", nil, 0, 0},
	{16000081, 26, "异世笔刀", "来自异世界的笔刀，可用于翻新记忆链接，升星2-3星。", 2, false, false, 100, 10, 99, false, nil, "", nil, 0, 0},
	{16000082, 26, "异世魔刷", "来自异世界的魔刷，可用于翻新记忆链接，升星2-3星。", 3, false, false, 200, 30, 99, false, nil, "", nil, 0, 0},
	{16000083, 26, "异世金箔", "来自异世界的金箔，可用于翻新记忆链接，升星3星。", 4, false, false, 300, 50, 99, false, nil, "", nil, 0, 0},
	{16000084, 10, "铜星券", "在奥比之间十分流行的铜质奖券,可用于兑换派对道具。", 1, false, false, 100, 3, 99999, false, nil, "", nil, 0, 0},
	{16000085, 10, "银星券", "在奥比之间十分流行的银质奖券,可用于兑换派对道具。", 1, false, false, 100, 3, 99999, false, nil, "", nil, 0, 0},
	{16000086, 10, "金星券", "在奥比之间十分流行的金质奖券,可用于兑换派对道具。", 1, false, false, 100, 3, 99999, false, nil, "", nil, 0, 0},
	{16000087, 29, "万能语录", "散落在故事中的语录，可以与旅行者产生共鸣，用于天赋升级。", 4, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16000088, 7, "传记借阅证", "用于在异世图书馆中借阅旅行者传记。", 4, false, false, 200, 80, 99, false, {type=5}, "", nil, 0, 0},
	{16000089, 7, "图典借阅证", "用于在异世图书馆中借阅记忆链接图典。", 4, false, false, 200, 80, 99, false, {type=6}, "", nil, 0, 0},
	{16000090, 2, "打年糕", "打啊打年糕", 1, true, false, 500, 13, 99, false, {gameId=3}, "", nil, 0, 0},
	{16000091, 5, "测试香水", "使用后30分钟内，工坊制作经验增加10%。", 2, true, false, 100, 2, 99, false, {duration=30,id=1,value=1000}, "", nil, 0, 0},
	{16000092, 10, "鳄鱼的牙齿", "大乐斗特殊荣誉，可获得“快乐小熊”荣誉称号。", 4, false, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000093, 2, "跟随金鹅", "神奇的金色大鹅，摸一下就会跟随在它身后。", 1, true, false, 1000, 13, 99, false, {gameId=4}, "", nil, 0, 0},
	{16000094, 2, "方向旗", "猜猜我旗子的反方向！", 1, true, false, 500, 13, 99, false, {gameId=5}, "", nil, 0, 0},
	{16000095, 30, "橘子泡泡", "“橘子般的清新盛开在你我四季的回忆里，历久弥新。”", 1, true, false, 1200, 30, 99, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000096, 30, "蜜桃熟啦", "“许诺在蜜桃成熟时的约定，你还记得吗？”", 1, true, false, 1200, 30, 99, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000097, 30, "荔枝啾咪", "“荔枝是夏风的秘密，你的一言一语是我整个夏日的甜蜜。”", 1, true, false, 1200, 30, 99, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000098, 30, "草莓快跑", "“想在你心上种一株草莓，每一天都开出清甜的回味。”", 1, true, false, 1200, 30, 99, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000099, 30, "想要一起牵手手", "“嘘，闭眼，把手给我。”", 2, true, false, 4800, 120, 99, false, {friendship=10,limit=1}, "", nil, 0, 0},
	{16000100, 30, "永远守在你身后", "“我一直都在你身后，你一回头就看得见的地方。”", 2, true, false, 4800, 120, 99, false, {friendship=10,limit=1}, "", nil, 0, 0},
	{16000101, 30, "我们一起拉勾勾", "“拉勾上吊一百年不许变！盖章！”", 2, true, false, 4800, 120, 99, false, {friendship=10,limit=1}, "", nil, 0, 0},
	{16000102, 30, "朋友一生一起走", "“说好从今以后都牵着手，不管要走多远。”", 2, true, false, 4800, 120, 99, false, {friendship=10,limit=1}, "", nil, 0, 0},
	{16000103, 30, "语笑嫣然", "“见花如见人，一语笑嫣然。”", 3, true, false, 8800, 220, 99, false, {friendship=20,limit=1}, "", nil, 0, 0},
	{16000104, 30, "流年怯梦", "“我们仍未知道在岁月中流逝的是青春，还是一场蒙着怯意的梦。”", 3, true, false, 8800, 220, 99, false, {friendship=20,limit=1}, "", nil, 0, 0},
	{16000105, 30, "月淌百香", "“明月落在花丛中，你落在我心里。”", 4, true, false, 4800, 120, 99, false, {friendship=30,limit=0}, "", nil, 0, 0},
	{16000106, 30, "星夜恋语", "“流星划过你眼睛，我的世界变成你呢喃的耳语。”", 4, true, false, 20800, 520, 99, false, {friendship=200,limit=0}, "", nil, 0, 0},
	{16000107, 3, "勇敢者的奖励", "抓住小偷后，给予勇敢奥比的奖励。", 2, false, false, 10, 10, 99, false, {type=1,rewards={7}}, "", nil, 0, 0},
	{16000108, 3, "哈根的礼物", "哈根送的礼物，开启后可随机获得小惊喜。", 2, false, false, 10, 10, 99, false, {type=1,rewards={7}}, "", nil, 0, 0},
	{16000109, 3, "多多号礼包", "开启后可随机获得精美称号和家具。", 2, false, false, 10, 10, 99, false, {type=1,rewards={34}}, "", nil, 0, 0},
	{16000110, 3, "奥比服装礼包", "开启后可随机获得精美服装。", 2, false, false, 10, 10, 99, false, {type=1,rewards={36}}, "", nil, 0, 0},
	{16000111, 2, "啦啦队", "带上花球球，加油打气的劲儿能绕场十圈！", 1, true, false, 1000, 13, 99, false, {gameId=6}, "", nil, 0, 0},
	{16000112, 1, "13周年蛋糕", "草莓味的奥比岛13周年蛋糕，和朋友一起分享吧！", 1, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=1,randomDrop={3,1}}, "", nil, 0, 0},
	{16000113, 2, "鼓乐队", "您的需求，就是我们的工作，鼓乐队在线承接各项庆祝业务！", 1, true, false, 1000, 13, 99, false, {gameId=7}, "", nil, 0, 0},
	{16000114, 10, "游戏时光点数", "通过玩游戏获得，每一个点数都体现着超凡的游戏实力！", 1, false, false, 0, 0.1, 99999, false, {rewardId=52,actId=2051}, "", nil, 0, 0},
	{16000115, 34, "烟火棒", "【氛围物道具】点燃烟火，照亮夜空。", 3, true, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000116, 7, "宝石之钥", "使用创造术锤炼宝石制作而成的钥匙。", 5, false, false, 100, 60, 99, false, {rewardId=44,actId=137}, "", nil, 0, 0},
	{16000117, 7, "大富翁骰子", "奥比大富翁专用骰子。", 4, false, false, 100, 80, 99, false, {type=108}, "", nil, 0, 0},
	{16000118, 30, "蔷薇心语", "我们的友情就像红蔷薇，绚烂绽放。", 3, true, false, 8800, 220, 99, false, nil, "", nil, 0, 0},
	{16000119, 30, "月下蔷薇", "友情是两颗心的真诚相待。", 4, true, false, 20800, 520, 99, false, nil, "", nil, 0, 0},
	{16000120, 3, "骑士套装礼包", "打开后可获得：骑士守护套装和骑士信仰套装", 4, false, false, 10, 1, 1, false, {type=1,rewards={70}}, "", nil, 0, 0},
	{16000121, 10, "宝藏币", "可在迷雾森林商店兑换奖品。", 2, false, false, 100, 0.1, 99999, false, nil, "", nil, 0, 0},
	{16000122, 10, "郁金花朵", "郁香季专属兑换币，可在郁香商店兑换物品。", 4, false, false, 100, 3, 99999, false, {rewardId=35,actId=203}, "", nil, 0, 0},
	{16000123, 10, "尘世贝", "历经尘世的洗练却仍光洁如新，可在挖宝树屋兑换商品。", 1, false, false, 100, 1.5, 99999, false, nil, "", nil, 0, 0},
	{16000124, 10, "遗迹石", "石头上纹路似乎在诉说山体中的秘密，可在挖宝树屋兑换商品。", 1, false, false, 100, 1.5, 99999, false, nil, "", nil, 0, 0},
	{16000125, 10, "月白螺", "多现于森林雪山一带、洁白的海螺，可在挖宝树屋兑换商品。", 1, false, false, 100, 1.5, 99999, false, nil, "", nil, 0, 0},
	{16000126, 3, "初级原始宝箱", "开启后可获得挖宝树屋兑换材料。", 2, false, false, 10, 10, 99, false, {type=1,rewards={45}}, "", nil, 0, 0},
	{16000127, 3, "中级原始宝箱", "开启后可获得挖宝树屋兑换材料。", 3, false, false, 10, 20, 99, false, {type=1,rewards={46}}, "", nil, 0, 0},
	{16000128, 3, "高级原始宝箱", "开启后可获得挖宝树屋兑换材料。", 4, false, false, 10, 50, 99, false, {type=1,rewards={47}}, "", nil, 0, 0},
	{16000129, 3, "初级远古宝箱", "开启后可获得挖宝树屋兑换材料。", 2, false, false, 10, 10, 99, false, {type=1,rewards={48}}, "", nil, 0, 0},
	{16000130, 3, "中级远古宝箱", "开启后可获得挖宝树屋兑换材料。", 3, false, false, 10, 20, 99, false, {type=1,rewards={49}}, "", nil, 0, 0},
	{16000131, 3, "高级远古宝箱", "开启后可获得挖宝树屋兑换材料。", 4, false, false, 10, 50, 99, false, {type=1,rewards={50}}, "", nil, 0, 0},
	{16000132, 1, "花茶", "被花魇偷偷藏起来的花茶，散发出郁金香独特的香气，可以和其他人一起分享。", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=2,randomDrop={3,1}}, "", nil, 0, 0},
	{16000133, 3, "远方旅者宝箱", "开启后有几率获得传记借阅证或旅行者好感度礼物。", 4, false, false, 10, 80, 99, false, {type=1,rewards={56}}, "", nil, 0, 0},
	{16000134, 10, "时之沙", "可以变成各种抽奖道具的神奇沙漏，当然，不同的抽奖道具也能变成它。", 4, false, false, 0, 1.5, 99999, false, {rewardId=98,actId=169}, "", nil, 0, 0},
	{16000135, 34, "日常生活(赠送)", "文案待补", 4, false, false, 0, 680, 99, false, nil, "", nil, 0, 0},
	{16000136, 36, "代金券", "1代金券可抵扣1元人民币，不可购买高于总拥有代金券价值的商品。", 4, false, false, 0, 10, 99999, false, nil, "", nil, 0, 0},
	{16000137, 3, "庆典套装自选包", "打开后可选择一套获得：欢趣庆典套装或欢乐庆典套装", 5, false, false, 10, 1, 1, false, {count=1,type=2,rewards={53,54}}, "", nil, 0, 0},
	{16000138, 3, "额外礼物", "打开后可随机获得一份奖励", 3, false, false, 0, 1, 999, false, {type=1,rewards={57}}, "", nil, 0, 0},
	{16000139, 3, "魔法种子礼包", "打开后可随机获得一个魔法植物种子", 1, false, false, 0, 1, 999, false, {type=1,rewards={58}}, "", nil, 0, 0},
	{16000140, 10, "神秘装扮", "达成成就后可获得装扮。", 1, false, false, 0, 0, 1, false, nil, "", nil, 0, 0},
	{16000141, 3, "初级资源礼包", "打开后可选择一种材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={60}}, "", nil, 0, 0},
	{16000142, 3, "中级资源礼包", "打开后可选择一种材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={59}}, "", nil, 0, 0},
	{16000143, 10, "狮鹫币", "星际大赛专属兑换币，可在星际大赛商店兑换物品。", 4, false, false, 100, 3, 99999, false, {rewardId=35,actId=140}, "", nil, 0, 0},
	{16000144, 7, "星碎", "用于开启星际祈愿的神奇碎片，可向星空祈愿，实现愿望。", 5, false, false, 100, 60, 999, false, {rewardId=97,actId=140}, "", nil, 0, 0},
	{16000145, 3, "岛民好感度宝箱", "打开后可随机获得一个岛民好感度道具", 3, false, false, 0, 20, 999, false, {type=1,rewards={67}}, "", nil, 0, 0},
	{16000146, 3, "旅行者好感度宝箱", "打开后可随机获得一个旅行者好感度道具", 3, false, false, 0, 20, 999, false, {type=1,rewards={68}}, "", nil, 0, 0},
	{16000147, 1, "鲜花糕", "用食食花粉做成的糕点，美容养颜，可以分享给小伙伴吃。", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,10},count=4,action=1,randomDrop={3,10}}, "", nil, 0, 0},
	{16000148, 10, "童趣螺丝", "可在庆典筹备活动中进行捐赠并获得奖励", 1, false, false, 100, 1, 999, false, {rewardId=35,actId=153}, "", nil, 0, 0},
	{16000149, 10, "梦想之心", "庆典专属兑换道具，可在庆典商店中兑换物品", 1, false, false, 100, 1, 9999, false, {rewardId=35,actId=153}, "", nil, 0, 0},
	{16000150, 41, "邀请函碎片1", "飞船邀请的第一块碎片，集齐四块即可合成完整的飞船邀请函。", 1, false, false, 0, 0, 1, false, {rewardId=69,actId=153}, "", nil, 0, 0},
	{16000151, 41, "邀请函碎片2", "飞船邀请的第二块碎片，集齐四块即可合成完整的飞船邀请函。", 1, false, false, 0, 0, 1, false, {rewardId=69,actId=153}, "", nil, 0, 0},
	{16000152, 41, "邀请函碎片3", "飞船邀请的第三块碎片，集齐四块即可合成完整的飞船邀请函。", 1, false, false, 0, 0, 1, false, {rewardId=69,actId=153}, "", nil, 0, 0},
	{16000153, 41, "邀请函碎片4", "飞船邀请的第四块碎片，集齐四块即可合成完整的飞船邀请函。", 1, false, false, 0, 0, 1, false, {rewardId=69,actId=153}, "", nil, 0, 0},
	{16000154, 10, "声援投票券", "星际声援专属投票券，可以为你心仪的队伍投票支持。", 1, false, false, 0, 0, 99999, false, {rewardId=35,actId=140}, "", nil, 0, 0},
	{16000155, 34, "庆典花篮", "【氛围物道具】花瓣飞舞，献上祝福。", 3, true, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000156, 1, "幸运香薰", "由创造堂发放给参与者的礼物，使用后能增加好运，协助大家更快破解谜题。", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,10},count=4,action=6,randomDrop={3,10}}, "", nil, 0, 0},
	{16000157, 7, "铃兰花朵", "用于繁花颂抽取奖品。", 5, false, false, 100, 80, 999, false, {rewardId=99,actId=1063}, "", nil, 0, 0},
	{16000158, 10, "欢乐券", "快乐节专属兑换币，可在兑换小摊兑换物品。", 4, false, false, 100, 3, 99999, false, nil, "", nil, 0, 0},
	{16000159, 7, "雾影晶片", "用于开启雾隐实验室的神奇晶片。", 5, false, false, 100, 60, 999, false, {rewardId=97,actId=169}, "", nil, 0, 0},
	{16000160, 10, "创造师胸针", "影之谜专属兑换币，可在雾尽商店兑换物品。", 4, false, false, 100, 1, 99999, false, {rewardId=35,actId=169}, "", nil, 0, 0},
	{16000161, 3, "家具装饰福袋", "里面包含4件家具。", 4, false, false, 10, 250, 99, false, {type=1,rewards={71}}, "", nil, 0, 0},
	{16000162, 7, "星愿晶片", "用于星愿卡抽取奖品。", 5, false, false, 100, 248, 999, false, {rewardId=75,actId=160}, "", nil, 0, 0},
	{16000163, 10, "月桂花币", "满月节专属兑换币，可在满月商店中兑换物品", 4, false, false, 100, 1, 9999, false, {rewardId=35,actId=165}, "", nil, 0, 0},
	{16000164, 30, "明月寄相思", "【花束】月圆年年相似，你我岁岁相盼。", 2, true, false, 100, 60, 99, false, {friendship=5,limit=0}, "", nil, 0, 0},
	{16000165, 1, "大闸蟹", "秋高气爽，正是大闸蟹最美味的时候。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000166, 34, "微光祝福", "【氛围物道具】祝福化作微光，飞往你的身边。", 3, true, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000167, 3, "小耶的赠礼", "欢迎回归奥比岛，这是小耶送的礼物。", 4, false, false, 10, 10, 99, false, {type=1,rewards={72}}, "", nil, 0, 0},
	{16000168, 3, "祈愿自选礼包", "可任选“星际祈愿”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={73}}, "", nil, 0, 0},
	{16000169, 3, "生活资源礼包", "打开后可选择一种材料获得。", 3, false, false, 0, 20, 999, false, {type=3,rewards={74}}, "", nil, 0, 0},
	{16000170, 18, "居民生日蛋糕", "奥比岛居民专属生日蛋糕，仅在居民生日时送给居民，不可摆放在小岛上。", 3, false, false, 10, 1, 99, false, nil, "", nil, 0, 0},
	{16000171, 1, "14周年蛋糕", "柠檬味的奥比岛14周年蛋糕，和朋友一起分享吧！", 2, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=1,randomDrop={3,1}}, "", nil, 0, 0},
	{16000172, 7, "快乐币", "用于“快乐祈愿”彩蛋获得主题装扮内容", 4, false, false, 100, 60, 999, false, {rewardId=97,actId=119}, "", nil, 0, 0},
	{16000173, 3, "花朝月夕福袋", "打开后可获得：称号（花朝月夕）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={76}}, "", nil, 0, 0},
	{16000174, 3, "明月佳期福袋", "打开后可获得：头像框（明月佳期）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={77}}, "", nil, 0, 0},
	{16000175, 3, "双星拢月福袋", "打开后可获得：双人动作*1（双星拢月）和高级探宝仪", 4, false, false, 0, 248, 999, false, {type=1,rewards={78}}, "", nil, 0, 0},
	{16000176, 3, "满月分享福袋", "打开后可获得：分享物（大闸蟹）和传记借阅证", 4, false, false, 0, 248, 999, false, {type=1,rewards={79}}, "", nil, 0, 0},
	{16000177, 34, "领航", "我领航，我闪亮！", 3, false, false, 0, 1, 1, false, nil, "", nil, 1, 0},
	{16000178, 10, "黑胶唱片", "龙鱼会随着动听的旋律欢快起舞，可在青森小屋兑换音箱。", 2, false, false, 100, 6, 99999, false, nil, "", nil, 0, 0},
	{16000179, 3, "花之诗", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 4, false, false, 10, 1, 999, false, {type=4,rewards={81}}, "", nil, 0, 0},
	{16000180, 3, "花之词", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 4, false, false, 10, 1, 999, false, {type=4,rewards={81}}, "", nil, 0, 0},
	{16000181, 3, "花之歌", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=4,rewards={80}}, "", nil, 0, 0},
	{16000182, 3, "花之舞", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 4, false, false, 10, 1, 999, false, {type=4,rewards={81}}, "", nil, 0, 0},
	{16000183, 3, "花开有时", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=4,rewards={82}}, "", nil, 0, 0},
	{16000184, 3, "丰收宝箱", "丰收积分达到500可获得", 3, false, false, 10, 1, 999, false, {type=1,rewards={83}}, "", nil, 0, 0},
	{16000185, 3, "丰饶宝箱", "丰收积分达到1500可获得", 3, false, false, 10, 1, 999, false, {type=1,rewards={84}}, "", nil, 0, 0},
	{16000186, 3, "丰登宝箱", "丰收积分达到2500可获得", 3, false, false, 10, 1, 999, false, {type=1,rewards={85}}, "", nil, 0, 0},
	{16000187, 3, "丰产宝箱", "丰收积分达到4000可获得", 4, false, false, 10, 1, 999, false, {type=1,rewards={86}}, "", nil, 0, 0},
	{16000188, 3, "丰裕宝箱", "丰收积分达到6000可获得", 4, false, false, 10, 1, 999, false, {type=1,rewards={87}}, "", nil, 0, 0},
	{16000189, 3, "花开有时", "“繁花颂”活动的进度奖励自选宝箱。", 5, false, false, 10, 1, 999, false, {type=3,rewards={88}}, "", nil, 0, 0},
	{16000190, 3, "花开有时", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={89}}, "", nil, 0, 0},
	{16000191, 3, "花开有时", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={90}}, "", nil, 0, 0},
	{16000192, 3, "花开有时", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={91}}, "", nil, 0, 0},
	{16000193, 3, "花开有时", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={92}}, "", nil, 0, 0},
	{16000194, 3, "花开有时", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={93}}, "", nil, 0, 0},
	{16000195, 3, "花开有时", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={81}}, "", nil, 0, 0},
	{16000196, 3, "花之歌", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={94}}, "", nil, 0, 0},
	{16000197, 3, "花之歌", "可任选“繁花颂”活动奖池里的任一服装部件或任一家具部件。", 5, false, false, 10, 1, 999, false, {type=3,rewards={95}}, "", nil, 0, 0},
	{16000198, 7, "梦境之钥", "一把据说能打开真正的神奇橱柜的钥匙，可用于甜蜜梦工场抽取奖品。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=161}, "", nil, 0, 0},
	{16000199, 1, "黑莓椰汁糕", "甜甜的味道中带有一丝的酸感，可以分享给您的小伙伴试试。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000200, 10, "美食勋章", "岛务厅颁发的美食勋章，凭借此勋章可以在奇食商店中兑换奖品。", 4, false, false, 0, 3, 999, false, {rewardId=35,actId=201}, "", nil, 0, 0},
	{16000201, 7, "翰墨笔", "取材自龙鳞与神凤尾羽的毛笔，凝聚帝君之力，能绘世间万物。可用于风华秀中抽取奖品。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=1184}, "", nil, 0, 0},
	{16000202, 7, "奇异餐叉", "一柄奇异的餐叉，可用来开启夜西准备的奇异食盒，获得其中奖品。", 5, false, false, 0, 240, 999, false, {rewardId=108,actId=194}, "", nil, 0, 0},
	{16000203, 3, "常规材料礼包", "打开后可选择一种材料获得。", 3, false, false, 0, 20, 999, false, {type=3,rewards={101}}, "", nil, 0, 0},
	{16000204, 3, "珍稀材料礼包", "打开后可选择一种珍稀材料获得。", 4, false, false, 0, 30, 999, false, {type=3,rewards={102}}, "", nil, 0, 0},
	{16000205, 3, "软糖进度宝箱", "可任选“甜蜜梦工场”活动进度“软糖”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={103}}, "", nil, 0, 0},
	{16000206, 3, "奶糖进度宝箱", "可任选“甜蜜梦工场”活动进度“奶糖”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={103}}, "", nil, 0, 0},
	{16000207, 3, "硬糖进度宝箱", "可任选“甜蜜梦工场”活动进度“硬糖”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={104}}, "", nil, 0, 0},
	{16000208, 3, "酥糖进度宝箱", "可任选“甜蜜梦工场”活动进度“酥糖”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={103}}, "", nil, 0, 0},
	{16000209, 3, "夹心糖进度宝箱", "可任选“甜蜜梦工场”活动进度“夹心糖”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={105}}, "", nil, 0, 0},
	{16000210, 1, "奇异烧仙草", "将雪山仙草晒干，熬制99天而成的珍贵仙草冻，令人透心清凉。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000211, 3, "黄宝石宫殿套装", "打开后可获得黄宝石宫殿家具套装。", 6, false, false, 10, 1, 999, false, {type=1,rewards={106}}, "", nil, 0, 0},
	{16000212, 7, "时空之钥", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 0},
	{16000213, 3, "食盒家具福袋", "里面包含4件家具。", 4, false, false, 10, 250, 99, false, {type=1,rewards={107}}, "", nil, 0, 0},
	{16000214, 28, "元气瓶", "一个神秘商人研制的白桃味气泡水，轻松消除10点疲劳值，让元气up up！", 2, false, false, 200, 5, 99, false, {num=10}, "", nil, 0, 0},
	{16000215, 1, "元气森林气泡水", "0糖0脂0卡的元气森林白桃味气泡水，一起分享，让快乐传递。", 3, true, false, 1250, 5, 99, false, {randomRate=0.2,fixedDrop={3,5},count=4,action=2,randomDrop={3,5}}, "", nil, 0, 0},
	{16000216, 1, "龙爪布丁", "萌萌的龙爪造型小布丁，分享后可获得爱心。", 3, true, false, 925, 5, 99, false, {randomRate=0.2,fixedDrop={3,5},count=3,action=1,randomDrop={3,5}}, "", nil, 0, 0},
	{16000217, 10, "蜂蜜棒棒", "嗡嗡运动会特发的纪念物，凭借此纪念物可以在蜂蜂商店中兑换奖品。", 4, false, false, 0, 3, 999, false, {rewardId=35,actId=203}, "", nil, 0, 0},
	{16000218, 10, "涂鸦画笔", "可以用于活动-蜂王涂鸦的画笔。", 1, true, false, 10, 1, 999, false, {rewardId=35,actId=205}, "", nil, 0, 0},
	{16000219, 3, "回归自选礼包", "可选择领取以下其中一种道具：时空之钥×1或晶钻服装彩蛋锤×1", 4, false, false, 10, 60, 999, false, {type=3,rewards={109}}, "", nil, 0, 0},
	{16000220, 3, "小耶的赠礼", "欢迎回归奥比岛，这是小耶送的礼物。", 4, false, false, 10, 200, 99, false, {type=1,rewards={110}}, "", nil, 0, 0},
	{16000221, 1, "炉煮奶茶", "寒冷的冬天来一杯炉煮奶茶取取暖吧~", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=2,randomDrop={2,2}}, "", nil, 0, 0},
	{16000222, 10, "雪花徽章", "由凛冬降落的雪花制成，可以在冰雪商店里兑换物品。", 4, false, false, 0, 3, 999, false, {rewardId=35,actId=206}, "", nil, 0, 0},
	{16000223, 3, "音箱+20晶钻", "里面包含贝斯蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={112}}, "", nil, 0, 0},
	{16000224, 3, "音箱+20晶钻", "里面包含单簧管蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={113}}, "", nil, 0, 0},
	{16000225, 3, "音箱+20晶钻", "里面包含指挥家蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={114}}, "", nil, 0, 0},
	{16000226, 3, "音箱+20晶钻", "里面包含架子鼓蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={115}}, "", nil, 0, 0},
	{16000227, 3, "音箱+20晶钻", "里面包含木吉他蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={116}}, "", nil, 0, 0},
	{16000228, 3, "音箱+20晶钻", "里面包含手风琴蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={117}}, "", nil, 0, 0},
	{16000229, 3, "音箱+20晶钻", "里面包含小提琴蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={118}}, "", nil, 0, 0},
	{16000230, 3, "音箱+20晶钻", "里面包含长笛蜜蜂音箱和20晶钻。", 4, false, false, 10, 200, 99, false, {type=1,rewards={119}}, "", nil, 0, 0},
	{16000231, 3, "材料自选礼包", "打开后可选择一种材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={120}}, "", nil, 0, 0},
	{16000232, 3, "中级材料自选礼包", "打开后可选择一种材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={121}}, "", nil, 0, 0},
	{16000234, 10, "时间之镜", "可在时空商店兑换奖励。", 4, false, false, 40, 1, 99999, false, nil, "", nil, 0, 0},
	{16000235, 3, "蓝玫瑰宝石套装", "打开后可获得蓝玫瑰宝石家具套装。", 6, false, false, 10, 1, 999, false, {type=1,rewards={122}}, "", nil, 0, 0},
	{16000236, 1, "福袋年馍", "红色的福袋年馍，吃掉福袋获得福气，快把福气分享给亲朋好友吧。", 3, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,5},count=4,action=1,randomDrop={3,5}}, "", nil, 0, 0},
	{16000237, 10, "太阳徽章", "向居民送祝福获得的谢礼，凭借太阳徽章可以在祈福商店中兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=216}, "", nil, 0, 0},
	{16000238, 7, "祝福彩球", "蕴藏祝福的装饰彩球，可用来参与夜西准备的新年祝福抽奖，获得其中奖品。", 5, false, false, 100, 248, 999, false, {rewardId=75,actId=160}, "", nil, 0, 0},
	{16000239, 3, "铃儿响铃铛福袋", "打开后可获得：称号（铃儿响铃铛）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={124}}, "", nil, 0, 0},
	{16000240, 3, "如诗如歌福袋", "打开后可获得：头像框（如诗如歌）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={125}}, "", nil, 0, 0},
	{16000241, 3, "手捧落雪福袋", "打开后可获得：双人动作*1（手捧落雪）和高级探宝仪", 4, false, false, 0, 248, 999, false, {type=1,rewards={126}}, "", nil, 0, 0},
	{16000242, 3, "祈福分享福袋", "打开后可获得：分享物（炉煮奶茶）和传记借阅证", 4, false, false, 0, 248, 999, false, {type=1,rewards={127}}, "", nil, 0, 0},
	{16000243, 3, "祈福家具福袋", "里面包含4件家具。", 4, false, false, 0, 248, 999, false, {type=1,rewards={128}}, "", nil, 0, 0},
	{16000244, 2, "龙翔于天", "可以和小伙伴们一起进行传统的“舞龙”活动。", 4, true, false, 1000, 13, 99, false, {gameId=9}, "", nil, 0, 0},
	{16000245, 2, "百兽率舞", "可以和小伙伴们一起进行传统的“舞狮”活动。", 3, true, false, 1000, 13, 99, false, {gameId=10}, "", nil, 0, 0},
	{16000246, 30, "幸福来敲门", "【花束】当悦耳的铃铛声响起，那是幸福在敲门，带来健康与平安。", 2, true, false, 1430, 60, 99, false, {friendship=5,limit=0}, "", nil, 0, 0},
	{16000247, 30, "天空赠礼", "【花束】云朵从天空带来祝福，新年快乐也常伴身边。", 2, true, false, 1925, 60, 99, false, {friendship=5,limit=0}, "", nil, 0, 0},
	{16000248, 30, "金灿阳光", "【花束】金灿花束送来昌盛财运，新年祝你金光满满。", 2, true, false, 1155, 60, 99, false, {friendship=5,limit=0}, "", nil, 0, 0},
	{16000249, 30, "硕果丰年", "【花束】沉甸甸的篮子里装着满满的收获，明年也是丰收年。", 2, true, false, 1925, 60, 99, false, {friendship=5,limit=0}, "", nil, 0, 0},
	{16000250, 10, "团年结", "用红色丝线手工编织的团年结，每一根线条都是一个祝福，可在团圆商店兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1189}, "", nil, 0, 0},
	{16000251, 3, "立新意进度宝箱", "可任选“风华秀”活动进度“立新意”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={129}}, "", nil, 0, 0},
	{16000252, 3, "裁生宣进度宝箱", "可任选“风华秀”活动进度“裁生宣”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={129}}, "", nil, 0, 0},
	{16000253, 3, "制凤砚进度宝箱", "可任选“风华秀”活动进度“制凤砚”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={130}}, "", nil, 0, 0},
	{16000254, 3, "挥龙毫进度宝箱", "可任选“风华秀”活动进度“挥龙毫”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={129}}, "", nil, 0, 0},
	{16000255, 3, "兰叶描进度宝箱", "可任选“风华秀”活动进度“兰叶描”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={131}}, "", nil, 0, 0},
	{16000256, 34, "风华烟花筒", "【氛围物道具】烟花绽放，愿新年胜旧年。", 3, true, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000257, 10, "游仙币", "来自蟠桃园中刚采摘的新鲜蟠桃，可以在游仙商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1195}, "", nil, 0, 0},
	{16000258, 43, "吉祥晶钻红包", "内含5个拼手气红包，总额共28晶钻。可以前往聊天频道使用。", 5, false, false, 0, 60, 999, false, nil, "", nil, 0, 0},
	{16000259, 43, "幸运爱心红包", "内含10个拼手气红包，总额共100爱心。可以前往聊天频道使用。", 4, false, false, 0, 60, 999, false, nil, "", nil, 0, 0},
	{16000260, 43, "快乐金币红包", "内含20个拼手气红包，总额共10000金币。可以前往聊天频道使用。", 4, false, false, 0, 60, 999, false, nil, "", nil, 0, 0},
	{16000261, 44, "孙悟空游仙卡", "干脆面小零食里的孙悟空小卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000262, 44, "猪八戒游仙卡", "干脆面小零食里的猪八戒小卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000263, 44, "唐僧游仙卡", "干脆面小零食里的唐僧小卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000264, 44, "沙和尚游仙卡", "干脆面小零食里的沙和尚小卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000265, 44, "哪吒游仙卡", "干脆面小零食里的哪吒小卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000266, 44, "李靖游仙卡", "干脆面小零食里的李靖小卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000267, 44, "泡泡游仙卡", "干脆面小零食里的小鲤鱼泡泡小卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000268, 3, "请仙卡", "使用后会得到任意一张游仙卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197,type=3,rewards={132}}, "", nil, 0, 0},
	{16000269, 45, "小鲤鱼干脆面", "小鲤鱼泡泡独家出品的方便面，非常美味，还可以开出任意一张游仙卡或请仙卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1197}, "", nil, 0, 0},
	{16000270, 1, "招财进宝饺", "听说只要吃到有晶钻的饺子，一整年都会走运哦。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000271, 1, "新年利是", "熊爪一抖，红包我有！", 4, true, false, 100, 12, 99, false, {randomRate=0.2,fixedDrop={2,1},count=6,action=7,randomDrop={2,1}}, "", nil, 0, 0},
	{16000272, 10, "花灯", "只有用真正的火种才能点亮的祝福元灯，可以在花灯商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1202}, "", nil, 0, 0},
	{16000273, 3, "居民红包", "给居民拜年时获得的红包。", 4, false, false, 0, 8, 999, false, {type=1,rewards={142}}, "", nil, 0, 0},
	{16000274, 44, "万能卡", "一枚纪念徽章，中间有马露的个性标识，可在爱神商店兑换物品。", 4, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000275, 3, "哪吒同款系列服装", "包含哪吒莲花祥衣*1、哪吒乾坤圈*1、哪吒混天绫*1。", 3, false, false, 0, 386, 999, false, {type=1,rewards={143}}, "", nil, 0, 0},
	{16000276, 3, "沙僧同款系列服装", "包含沙僧行者头箍*1、沙僧九珠布衣*1、沙僧苦旅布鞋*1、沙僧降妖宝杖*1。", 3, false, false, 0, 415, 999, false, {type=1,rewards={144}}, "", nil, 0, 0},
	{16000277, 3, "悟空同款系列服装", "包含悟空金箍僧帽*1、悟空虎皮直裰*1、悟空行者布鞋*1、悟空如意金箍棒*1。", 3, false, false, 0, 415, 999, false, {type=1,rewards={145}}, "", nil, 0, 0},
	{16000278, 3, "三藏同款系列服装", "包含三藏慈悲僧帽*1、三藏锦襕袈裟*1、三藏修行僧鞋*1、三藏紫檀念珠*1。", 3, false, false, 0, 415, 999, false, {type=1,rewards={146}}, "", nil, 0, 0},
	{16000279, 3, "染辰砂进度宝箱", "可任选“风华秀”活动进度“染辰砂”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={131}}, "", nil, 0, 0},
	{16000280, 3, "研佳墨进度宝箱", "可任选“风华秀”活动进度“研佳墨”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={129}}, "", nil, 0, 0},
	{16000281, 3, "落金纸进度宝箱", "可任选“风华秀”活动进度“落金纸”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={129}}, "", nil, 0, 0},
	{16000282, 3, "勾银线进度宝箱", "可任选“风华秀”活动进度“勾银线”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={129}}, "", nil, 0, 0},
	{16000283, 3, "点睛题进度宝箱", "可任选“风华秀”活动进度“点睛题”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={147}}, "", nil, 0, 0},
	{16000284, 3, "镌风华进度宝箱", "可任选“风华秀”活动进度“镌风华”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={147}}, "", nil, 0, 0},
	{16000285, 10, "爱神徽章", "一枚纪念徽章，中间有马露的个性标识，可在爱神商店兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1234}, "", nil, 0, 0},
	{16000286, 10, "海神螺", "据说它曾是最受海神喜爱的一种海螺，曾被海神捧在手心亲吻。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1253}, "", nil, 0, 0},
	{16000287, 1, "爱宠公益宣传册", "奥比期刊与首爱协会合作制作的宣传手册，封面是可爱的橘子和毛毛。", 3, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,5},count=5,action=4,randomDrop={3,5}}, "", nil, 0, 0},
	{16000288, 3, "奇趣梦想家礼盒", "打开后可获得：称号（奇趣梦想家）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={149}}, "", nil, 0, 0},
	{16000289, 3, "造梦之旅礼盒", "打开后可获得：头像框（造梦之旅）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={150}}, "", nil, 0, 0},
	{16000290, 3, "惊喜而至礼盒", "打开后可获得：双人动作*1（惊喜而至）和高级探宝仪", 4, false, false, 0, 248, 999, false, {type=1,rewards={151}}, "", nil, 0, 0},
	{16000291, 3, "奇趣分享礼盒", "打开后可获得：分享物（奇趣小蛋糕）和传记借阅证", 4, false, false, 0, 248, 999, false, {type=1,rewards={152}}, "", nil, 0, 0},
	{16000292, 3, "梦境家具礼盒", "里面包含4件家具。", 4, false, false, 0, 248, 999, false, {type=1,rewards={153}}, "", nil, 0, 0},
	{16000293, 3, "爱心护宠礼包", "包含内容：爱宠公益暖心屋*1,晶钻*60", 4, false, false, 0, 0, 999, false, {type=1,rewards={154}}, "", nil, 0, 0},
	{16000294, 3, "爱心护宠服装", "", 4, false, false, 0, 0, 999, false, {type=1,rewards={155}}, "", nil, 0, 0},
	{16000295, 7, "魔方", "转动魔方，寻找新的梦境世界。", 5, false, false, 100, 248, 999, false, {rewardId=75,actId=1276}, "", nil, 0, 0},
	{16000296, 10, "入梦卡", "每位拥有入梦卡的“玩家”，都能在造梦商店兑换商品。", 4, false, false, 100, 1, 9999, false, {rewardId=35,actId=1273}, "", nil, 0, 0},
	{16000297, 1, "奇趣小蛋糕", "奇趣美味的小蛋糕，最适合和小伙伴们一起分享！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000298, 10, "音符咖啡", "来自音之咖的主打咖啡，可以在音符商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1288}, "", nil, 0, 0},
	{16000299, 7, "星之话筒", "唱响舞台的星之话筒，可以用于星歌会的抽奖活动，获得其中奖品。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=1287}, "", nil, 0, 0},
	{16000300, 3, "造梦餐桌奖励", "造梦餐桌活动中完成帮厨订单的奖励。", 3, false, false, 0, 0, 999, false, nil, "", nil, 0, 0},
	{16000301, 1, "星星甜甜圈", "香浓松软的甜甜圈，尝一口就能治愈心情。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000302, 3, "华丽登场进度宝箱", "可任选“星歌会”活动进度“华丽登场”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={156}}, "", nil, 0, 0},
	{16000303, 3, "舒展歌喉进度宝箱", "可任选“星歌会”活动进度“舒展歌喉”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={156}}, "", nil, 0, 0},
	{16000304, 3, "畅意舞姿进度宝箱", "可任选“星歌会”活动进度“畅意舞姿”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={157}}, "", nil, 0, 0},
	{16000305, 3, "迎接掌声进度宝箱", "可任选“星歌会”活动进度“迎接掌声”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={156}}, "", nil, 0, 0},
	{16000306, 3, "完美谢幕进度宝箱", "可任选“星歌会”活动进度“完美谢幕”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={158}}, "", nil, 0, 0},
	{16000307, 10, "灵感碎片", "奥比在日常生活中会产生与音乐相关的灵感碎片，可用于解锁音乐海报。", 1, true, false, 10, 1, 999, false, {rewardId=35,actId=1296}, "", nil, 0, 0},
	{16000308, 7, "小熊扭蛋币", "奥比用日常积分和机器熊换取的扭蛋币，可用于活力扭蛋机，获得其中礼物。", 4, false, false, 0, 0, 999, false, nil, "", nil, 0, 0},
	{16000309, 3, "旅行者赠礼", "打开后可获得：晶钻服装彩蛋锤*3,灵感提灯*15", 4, false, false, 0, 480, 999, false, {type=1,rewards={159}}, "", nil, 0, 0},
	{16000310, 3, "旅行者赠礼", "打开后可获得：晶钻服装彩蛋锤*3,效率沙漏*15", 4, false, false, 0, 480, 999, false, {type=1,rewards={160}}, "", nil, 0, 0},
	{16000311, 3, "旅行者赠礼", "打开后可获得：晶钻服装彩蛋锤*3,金斧子*15", 4, false, false, 0, 480, 999, false, {type=1,rewards={161}}, "", nil, 0, 0},
	{16000312, 3, "旅行者馈赠", "打开后可获得：晶钻*10,灵感提灯*20", 4, false, false, 0, 410, 999, false, {type=1,rewards={162}}, "", nil, 0, 0},
	{16000313, 3, "旅行者馈赠", "打开后可获得：晶钻*10,效率沙漏*20", 4, false, false, 0, 410, 999, false, {type=1,rewards={163}}, "", nil, 0, 0},
	{16000314, 3, "旅行者馈赠", "打开后可获得：晶钻*10,金斧子*20", 4, false, false, 0, 410, 999, false, {type=1,rewards={164}}, "", nil, 0, 0},
	{16000315, 10, "造物证书", "造物师身份证明的证书，凭借证书可到庆典商店兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1315}, "", nil, 0, 0},
	{16000316, 3, "矿物自选材料包", "打开后可选择一种矿物材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={165}}, "", nil, 0, 0},
	{16000317, 3, "市集自选材料包", "打开后可选择一种市集材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={166}}, "", nil, 0, 0},
	{16000318, 3, "青森自选材料包", "打开后可选择一种青森材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={167}}, "", nil, 0, 0},
	{16000319, 3, "动物自选材料包", "打开后可选择一种动物材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={168}}, "", nil, 0, 0},
	{16000320, 3, "果树自选材料包", "打开后可选择一种果树材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={169}}, "", nil, 0, 0},
	{16000321, 3, "作物自选材料包", "打开后可选择一种作物材料获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={170}}, "", nil, 0, 0},
	{16000326, 30, "庆典之花", "用魔法辣辣扎成的庆典花束，具有十分刺激的香味。", 2, true, false, 1881, 60, 99, false, {friendship=3,limit=0}, "", nil, 0, 0},
	{16000327, 30, "造物之心", "装着无限灵感的造物之心，深受造物师们的喜爱。", 2, true, false, 2541, 60, 99, false, {friendship=5,limit=0}, "", nil, 0, 0},
	{16000328, 1, "香醇马卡龙", "五彩缤纷的马卡龙，正适合搭配下午茶食用！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000329, 10, "玩具发条", "转动柜子上的发条，就能打开柜子，获得里面的宝贝。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1322}, "", nil, 0, 0},
	{16000330, 10, "游园徽章", "由守护精灵EMMA特别设计的限定徽章，可在游园商店兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1336}, "", nil, 0, 0},
	{16000331, 7, "心愿泡泡", "包裹着最真诚愿望的泡泡，是祈祷树和堡母树最喜欢的养料。", 5, false, false, 100, 100, 999, false, {rewardId=171,actId=1338}, "", nil, 0, 0},
	{16000332, 10, "捕虫网", "结实耐用的捕虫网，是捕虫能手必不可少的得力工具！", 1, false, false, 1, 1, 9999, false, {rewardId=123,actId=1344}, "", nil, 0, 0},
	{16000333, 3, "游园随机拼图", "可随机获得一块游园集章活动的碎片。", 2, false, false, 10, 1, 999, false, {rewardId=35,actId=1320,type=3,rewards={172}}, "", nil, 0, 0},
	{16000334, 3, "游园茶会奖励", "游园茶会活动中完成订单的奖励。", 3, false, false, 0, 0, 999, false, nil, "", nil, 0, 0},
	{16000335, 10, "博物徽章", "奇妙博物馆出品的纪念徽章，可在奇游商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1360}, "", nil, 0, 0},
	{16000336, 10, "绘彩画笔", "可以用于活动—奇游绘彩的画笔。", 1, false, false, 1, 1, 9999, false, {rewardId=123,actId=1368}, "", nil, 0, 0},
	{16000337, 3, "蜜豆甜心福袋", "打开后可获得：称号（蜜豆甜心）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={173}}, "", nil, 0, 0},
	{16000338, 3, "甜甜蜜豆福袋", "打开后可获得：头像框（甜甜蜜豆）和晶钻服装彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={174}}, "", nil, 0, 0},
	{16000339, 3, "恶作剧蛋糕福袋", "打开后可获得：双人动作*1（恶作剧蛋糕）和高级探宝仪", 4, false, false, 0, 248, 999, false, {type=1,rewards={175}}, "", nil, 0, 0},
	{16000340, 3, "蜜豆水晶粽福袋", "打开后可获得：分享物（蜜豆水晶粽）和传记借阅证", 4, false, false, 0, 248, 999, false, {type=1,rewards={176}}, "", nil, 0, 0},
	{16000341, 3, "红小豆表情福袋", "打开后可获得：聊天表情包（红小豆与奥比）和晶钻家具彩蛋锤", 4, false, false, 0, 248, 999, false, {type=1,rewards={177}}, "", nil, 0, 0},
	{16000342, 3, "甜蜜家具礼盒", "里面包含4件家具。", 4, false, false, 0, 248, 999, false, {type=1,rewards={178}}, "", nil, 0, 0},
	{16000343, 3, "甜蜜服装礼盒", "里面包含4件服装。", 4, false, false, 0, 248, 999, false, {type=1,rewards={179}}, "", nil, 0, 0},
	{16000344, 10, "蜜粽勋章", "只有优秀食材才能获得的限定奖励，可在蜜粽小店兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1377}, "", nil, 0, 0},
	{16000345, 7, "奇迹之勺", "所有食材梦寐以求的奇迹之勺，能够实现最甜蜜的愿望。", 5, false, false, 100, 248, 999, false, {rewardId=75,actId=1378}, "", nil, 0, 0},
	{16000346, 1, "蜜豆水晶粽", "冰凉软糯的水晶粽，非常适合在夏天食用。快和小伙伴们一起分享吧！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000347, 10, "惊喜订单券", "可用于完成惊喜订单。", 3, false, false, 100, 6, 999, false, nil, "", nil, 0, 0},
	{16000348, 3, "蜜粽餐桌奖励", "蜜粽餐桌活动中完成订单的奖励。", 3, false, false, 0, 0, 999, false, nil, "", nil, 0, 0},
	{16000349, 7, "羊村遥控器", "按下遥控器，选择想看的“电视栏目”，获取栏目互动奖品。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=1394}, "", nil, 0, 0},
	{16000350, 10, "调色盘", "汇聚了无限色彩的调色盘，可以在炫彩商店兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1395}, "", nil, 0, 0},
	{16000351, 10, "周年徽章", "周年庆的纪念徽章，可以在纪念商店兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1404}, "", nil, 0, 0},
	{16000352, 10, "初级彩虹星星", "可解锁部分服装的基础色盘。", 4, false, false, 0, 5, 999, false, nil, "22,23", nil, 0, 0},
	{16000353, 10, "高级彩虹星星", "可解锁部分服装的高级色盘。", 5, false, false, 0, 5, 999, false, nil, "23", nil, 0, 0},
	{16000354, 10, "炫彩彩虹星星", "可解锁祈愿中服装的高级色盘。", 5, false, false, 0, 5, 999, false, nil, "21", nil, 0, 0},
	{16000355, 3, "栏目精选宝箱", "可任选“小羊村”活动进度“栏目精选”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={180}}, "", nil, 0, 0},
	{16000356, 3, "美食选编宝箱", "可任选“小羊村”活动进度“美食选编”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={181}}, "", nil, 0, 0},
	{16000357, 3, "羊村专辑宝箱", "可任选“小羊村”活动进度“羊村专辑”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={182}}, "", nil, 0, 0},
	{16000358, 3, "", "废弃", 4, false, false, 0, 20, 999, false, {type=4,rewards={180}}, "", nil, 0, 0},
	{16000359, 3, "", "废弃", 5, false, false, 0, 20, 999, false, {type=4,rewards={180}}, "", nil, 0, 0},
	{16000360, 1, "一周年炫彩蛋糕", "一周年的炫彩蛋糕，适合邀请小伙伴们共度周年。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000361, 10, "羊毛线团", "用掉落的羊毛做成羊毛线团，可以在羊毛商店兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1409}, "", nil, 0, 0},
	{16000362, 44, "喜羊羊照片", "喜羊羊是跑得最快的小羊，是个急性子，聪明活泼，永远带着微笑。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000363, 44, "美羊羊照片", "可爱的美羊羊，是营养学家、美容师、模特、服装师……精通与“美”有关的一切。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000364, 44, "懒羊羊照片", "懒羊羊是一只喜欢吃、喜欢睡觉的小羊，运气很好，是一个欢乐的美食家。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000365, 44, "灰太狼照片", "曾经的灰太狼总是骚扰羊村、喜欢偷羊吃，但现在他是羊村的好朋友。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000366, 44, "羊村大门建筑照", "羊村大门的铁门可以通电，上方还有大气的木制羊牌，是整个羊村的门面。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000367, 44, "平底锅照片", "平底锅，是狼堡里使用频率很高的物品，是红太狼的趁手武器。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000368, 44, "狼堡建筑照", "狼堡，是灰太狼一家居住的地方，外观恢弘大气，里面布满各种机关陷阱。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000369, 3, "照相机", "使用后会得到任意一张照片。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411,type=3,rewards={184}}, "", nil, 0, 0},
	{16000370, 45, "羊羊胶卷", "来自羊村的胶卷，可以洗出任意一张照片或照相机卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1411}, "", nil, 0, 0},
	{16000371, 1, "彩虹棒棒糖", "彩色的熊型棒棒糖，最适合小奥比们食用。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000372, 3, "喜羊羊同款系列服装", "包含喜羊羊绒绒头套*1、喜羊羊铃铛项圈*1、喜羊羊的休闲鞋*1。", 3, false, false, 0, 146, 999, false, {type=1,rewards={185}}, "", nil, 0, 0},
	{16000373, 3, "美羊羊同款系列服装", "包含美羊羊绒绒头套*1、美羊羊粉色围巾*1、美羊羊的休闲鞋*1。", 3, false, false, 0, 146, 999, false, {type=1,rewards={186}}, "", nil, 0, 0},
	{16000374, 3, "懒羊羊同款系列服装", "包含懒羊羊绒绒头套*1、懒羊羊的小围兜*1、羊羊的毛绒装*1。", 3, false, false, 0, 326, 999, false, {type=1,rewards={187}}, "", nil, 0, 0},
	{16000375, 3, "喜羊羊与灰太狼", "包含喜羊羊欢乐头饰、灰太狼开心头饰。", 3, false, false, 0, 118, 999, false, {type=1,rewards={188}}, "", nil, 0, 0},
	{16000376, 1, "冰镇西瓜汁", "冰镇西瓜汁，在炎炎夏日里来一杯，冰爽一下！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=2,randomDrop={2,2}}, "", nil, 0, 0},
	{16000377, 7, "冻柠冰块", "将奥比从炎热中拯救的法宝，可用于抽取冰柠有礼。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1417}, "", nil, 0, 0},
	{16000378, 10, "消暑凉扇", "消暑集市派发的纪念凉扇，可在冰柠商店兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1416}, "", nil, 0, 0},
	{16000379, 10, "饮品小伞", "为饮品遮风挡雨的可靠小伞，少了它就没那味啦！", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1418}, "", nil, 0, 0},
	{16000388, 10, "星际币", "星际大赛专属兑换币，可在星际大赛商店兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1442}, "", nil, 0, 0},
	{16000389, 10, "招牌冰柠茶", "清爽的青柠茶，对抗炎炎夏日的最佳选择，买它！", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1419}, "", nil, 0, 0},
	{16000390, 10, "熊瓜冰柠茶", "新鲜熊瓜搭配冰爽青柠，富有奥比岛特色的奇妙搭配。", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1419}, "", nil, 0, 0},
	{16000391, 10, "落星冰柠茶", "超人气单品之一，融入落星蘑菇的魅力，这还拿不下你？", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1419}, "", nil, 0, 0},
	{16000392, 34, "乞巧诉情", "【氛围物道具】仙衣染得天边碧，乞与人间向晓看。", 3, true, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000393, 7, "长明灯", "长明不灭的灯火，能将相思之意送到银河上。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1478}, "", nil, 0, 0},
	{16000394, 10, "乞巧结", "乞巧灯会上的特色纪念品，可在游梦商铺兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1477}, "", nil, 0, 0},
	{16000395, 1, "桂花红豆糕", "松软香甜的桂花红豆糕，每一口都清香四溢！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000396, 18, "居民生日礼盒", "奥比岛居民专属生日礼盒。只能用于送给过生日的居民噢！", 3, false, false, 10, 1, 99, false, nil, "", nil, 0, 0},
	{16000397, 3, "开锣戏进度宝箱", "可任选“芳华曲”活动进度“开锣戏”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={189}}, "", nil, 0, 0},
	{16000398, 3, "早轴进度宝箱", "可任选“芳华曲”活动进度“早轴”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={189}}, "", nil, 0, 0},
	{16000399, 3, "中轴进度宝箱", "可任选“芳华曲”活动进度“中轴”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={190}}, "", nil, 0, 0},
	{16000400, 3, "压轴进度宝箱", "可任选“芳华曲”活动进度“压轴”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={189}}, "", nil, 0, 0},
	{16000401, 3, "大轴进度宝箱", "可任选“芳华曲”活动进度“大轴”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={191}}, "", nil, 0, 0},
	{16000402, 10, "丰收橡果", "蕴含着丰收祝福的橡果，可在快乐商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1496}, "", nil, 0, 0},
	{16000403, 7, "戏折子", "贵客点戏专用的戏折子，可以用于芳华曲的抽奖活动，获得其中奖品。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=1507}, "", nil, 0, 0},
	{16000404, 10, "精灵硬币", "可以在淘宝街-凤娃处购买精灵相关的物品，通过精灵魔法杜尔、精灵互动获得。", 1, false, false, 100, 1.5, 99999, false, nil, "", nil, 0, 0},
	{16000405, 1, "坚果饼干", "香脆的饼干配上奶香的坚果，每一口都是酥香四溢！", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000406, 1, "哈皮惊喜盒", "哈皮专门为小奥比们准备的惊喜盒子，准备好迎接惊喜吧！", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,1},count=1,action=5,randomDrop={3,1}}, "", nil, 0, 0},
	{16000407, 1, "爱心惊喜盒", "“啊，可恶，别打了，我把爱心盒给你们总行了吧！”——哈皮。", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,5},count=1,action=4,randomDrop={3,5}}, "", nil, 0, 0},
	{16000408, 10, "秋叶画笔", "可以用于活动—秋叶入画的画笔。", 1, false, false, 1, 1, 9999, false, {rewardId=123,actId=1504}, "", nil, 0, 0},
	{16000409, 3, "精灵果自选礼盒", "打开后可选择一种精灵果获得。", 3, false, false, 0, 0, 999, false, {type=3,rewards={192}}, "", nil, 0, 0},
	{16000410, 3, "丰收宝箱", "丰收积分达到500可获得", 3, false, false, 10, 1, 999, false, {type=1,rewards={193}}, "", nil, 0, 0},
	{16000411, 3, "丰饶宝箱", "丰收积分达到1500可获得", 3, false, false, 10, 1, 999, false, {type=1,rewards={194}}, "", nil, 0, 0},
	{16000412, 3, "丰登宝箱", "丰收积分达到2500可获得", 3, false, false, 10, 1, 999, false, {type=1,rewards={195}}, "", nil, 0, 0},
	{16000413, 3, "丰产宝箱", "丰收积分达到4000可获得", 4, false, false, 10, 1, 999, false, {type=1,rewards={196}}, "", nil, 0, 0},
	{16000414, 3, "丰裕宝箱", "丰收积分达到6000可获得", 4, false, false, 10, 1, 999, false, {type=1,rewards={197}}, "", nil, 0, 0},
	{16000415, 10, "大耳精灵纪念币", "象征着大耳精灵的祝福，用于活动-祝福之地。", 3, false, false, 0, 0, 9999, false, nil, "", nil, 0, 0},
	{16000416, 10, "人鱼纪念币", "象征着人鱼的祝福，用于活动-祝福之地。", 3, false, false, 0, 0, 9999, false, nil, "", nil, 0, 0},
	{16000417, 10, "石巨人纪念币", "象征着石巨人的祝福，用于活动-祝福之地。", 3, false, false, 0, 0, 9999, false, nil, "", nil, 0, 0},
	{16000418, 10, "地精纪念币", "象征着地精的祝福，用于活动-祝福之地。", 3, false, false, 0, 0, 9999, false, nil, "", nil, 0, 0},
	{16000419, 3, "生活资源礼包", "打开后可选择一种材料获得。", 3, false, false, 0, 20, 999, false, {type=3,rewards={198}}, "", nil, 0, 0},
	{16000420, 3, "珍稀矿石自选箱", "打开后可选择一种珍稀矿石获得。", 3, false, false, 0, 20, 999, false, {type=3,rewards={199}}, "", nil, 0, 0},
	{16000421, 3, "珍稀材料自选箱", "打开后可选择一种珍稀材料获得。", 3, false, false, 0, 20, 999, false, {type=3,rewards={200}}, "", nil, 0, 0},
	{16000422, 1, "15周年蛋糕", "蝴蝶飞舞的奥比岛15周年蛋糕，和朋友一起分享吧！", 2, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=1,randomDrop={3,1}}, "", nil, 0, 0},
	{16000423, 7, "萌兔月饼", "精心制作的月饼，可以将其献祭给月神。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1531}, "", nil, 0, 0},
	{16000424, 10, "月亮徽章", "月亮形状的徽章，拿着它去满月商店兑换小礼品吧！", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1530}, "", nil, 0, 0},
	{16000425, 1, "玉兔雪酥饼", "雪白小巧的玉兔酥，一口一个刚刚好！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000426, 3, "红薯家具礼包", "打开后可获得红薯假日墙饰、红薯假日芦苇丛、红薯集章台、薯队长玩偶。", 3, false, false, 0, 0, 999, false, {type=1,rewards={201}}, "", nil, 0, 0},
	{16000427, 3, "红薯服装礼包", "打开后可获得红薯秋日挎包、红薯秋日水杯、红薯秋叶趴趴、红薯秋果枝。", 3, false, false, 0, 0, 999, false, {type=1,rewards={202}}, "", nil, 0, 0},
	{16000428, 3, "游览随机印章碎片", "可随机获得一块打卡奥比岛活动的印章碎片。", 2, false, false, 10, 1, 999, false, {rewardId=35,actId=1540,type=3,rewards={203}}, "", nil, 0, 0},
	{16000429, 30, "桂花冰皮月饼", "浓郁的桂花香气萦绕在唇齿间，就像是将秋天也一起吃进去了！", 4, false, false, 0, 0, 999, false, {friendship=5,limit=1}, "", nil, 0, 0},
	{16000430, 30, "咖啡流心月饼", "咖啡豆也能做月饼，不知道吃完后会不会精神一整天！", 3, false, false, 0, 0, 999, false, {friendship=3,limit=1}, "", nil, 0, 0},
	{16000431, 30, "相思豆沙月饼", "由相思豆做成的月饼，能否让你见到思念的人？", 3, false, false, 0, 0, 999, false, {friendship=3,limit=1}, "", nil, 0, 0},
	{16000432, 30, "莲蓉月饼", "用莲子也能做出香香软软的月饼哦！", 3, false, false, 0, 0, 999, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000433, 30, "五仁月饼", "五种果仁制成的月饼，咬起来都能听到“嘎嘣嘎嘣”的声音呢！", 3, false, false, 0, 0, 999, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000434, 10, "桂枝", "将桂枝收集起来，玉兔会把它们做成祈愿牌！", 2, false, false, 0, 0, 999, false, {rewardId=123,actId=1533}, "", nil, 0, 0},
	{16000435, 3, "岛民礼物自选箱", "可选择领取一种岛民礼物", 4, false, false, 10, 60, 999, false, {type=3,rewards={204}}, "", nil, 0, 0},
	{16000436, 7, "购书券", "用于购买限定的旅行者传记。", 4, false, false, 200, 80, 99, false, {type=401}, "", nil, 0, 0},
	{16000437, 10, "月灵手电", "能够存储月光的手电筒。", 1, false, false, 1, 1, 9999, false, {rewardId=123,actId=1537}, "", nil, 0, 0},
	{16000438, 10, "月灵积分", "与其他奥比结伴出游，认识更多月灵的奖励。", 1, false, false, 1, 1, 9999, false, {actId=1537}, "", nil, 1, 0},
	{16000439, 10, "月见花", "只在月光下盛开的花朵，可以在月灵商店兑换商品。", 4, false, false, 0, 1, 9999, false, {rewardId=35,actId=1551}, "", nil, 0, 0},
	{16000440, 1, "曼珠幽冥粿", "只有使用幽冥族特殊的烹饪技术，才能将曼珠沙华做成食物。小奥比们可不要轻易模仿哦！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000441, 7, "月光笔", "使用月光笔，在《月光用法百科全书》这部旷世巨著上留下自己的印记。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1578}, "", nil, 0, 0},
	{16000442, 10, "月铃", "能存储月光的铃铛，可以在月典商店兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1577}, "", nil, 0, 0},
	{16000443, 45, "月光宝盒", "月光宝盒里藏着月亮的秘密，打开可随机获得一张月光卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1573}, "", nil, 0, 0},
	{16000444, 3, "月光辞典", "使用一次辞典，就能获得一条和月光有关的知识。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1573,type=3,rewards={205}}, "", nil, 0, 0},
	{16000445, 44, "月灵手电", "梅尔改造后的手电，可以存储月光。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1573}, "", nil, 0, 0},
	{16000446, 44, "月光衣", "用月光缝制出的一套月光衣，据说穿上它可能会在月光下隐形。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1573}, "", nil, 0, 0},
	{16000447, 44, "月夜料理台", "根据小幽冥的提示，温蒂尝试在料理中使用月光作为新食材。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1573}, "", nil, 0, 0},
	{16000448, 44, "幽冥眼镜", "一副来自幽冥族的眼镜，据说是为了更方便他们观测月亮和月光的秘密。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1573}, "", nil, 0, 0},
	{16000449, 44, "月光魔药", "阿拉斯特地加入月光研制出的魔药，喝了会发光吗？", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1573}, "", nil, 0, 0},
	{16000450, 3, "乐园家具自选箱", "可选择领取一个游乐园家具。", 4, false, false, 10, 60, 999, false, {type=3,rewards={206}}, "", nil, 0, 0},
	{16000451, 3, "欢乐家具自选箱", "可选择领取一个游乐园家具。", 4, false, false, 10, 60, 999, false, {type=3,rewards={207}}, "", nil, 0, 0},
	{16000452, 3, "游乐家具自选箱", "可选择领取一个游乐园家具。", 4, false, false, 10, 60, 999, false, {type=3,rewards={208}}, "", nil, 0, 0},
	{16000453, 3, "游园家具自选箱", "可选择领取一个游乐园家具。", 4, false, false, 10, 60, 999, false, {type=3,rewards={209}}, "", nil, 0, 0},
	{16000454, 7, "摇摇船船票", "熊熊摇摇船专用船票。", 4, false, false, 100, 80, 99, false, {type=108}, "", nil, 0, 0},
	{16000455, 3, "月典餐桌奖励", "月典餐桌活动中完成订单的奖励。", 3, false, false, 0, 0, 999, false, nil, "", nil, 0, 0},
	{16000456, 10, "雾月票根", "印有雾月剧团标志的票根。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1599}, "", nil, 0, 0},
	{16000457, 7, "阿鼠徽章", "据说握着阿鼠徽章诚心许愿，一切愿望都可以实现。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1608}, "", nil, 0, 0},
	{16000458, 10, "电视遥控", "有魔法的特殊遥控器，使用它才能打开特殊的魔法节目。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1611}, "", nil, 0, 0},
	{16000459, 1, "焦糖奶油布丁", "超级可爱的焦糖布丁，熊熊都有点舍不得吃掉它呢。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000460, 10, "魔法门票", "一张画着酥酥米的门票，用于兑换魔法游记的贴纸。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1610}, "", nil, 0, 0},
	{16000461, 10, "凛冬星星", "星光与雪花铸成的徽章，可以在星之商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1642}, "", nil, 0, 0},
	{16000462, 10, "烟花筒", "烟花筒，用于雪山燃放烟花。", 3, true, false, 100, 10, 999, false, nil, "", nil, 0, 0},
	{16000463, 1, "姜饼曲奇", "让我想想，是先吃掉这根小拐杖，还是先吃掉这个姜饼人呢？", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000464, 7, "姜饼糖", "姜饼饼亲手制作的糖果，每一丝甜味里都藏着一种美好的祝福。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1655}, "", nil, 0, 0},
	{16000465, 10, "平安果", "代表平安的果子，可以在饼饼小屋和姜饼饼交换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1654}, "", nil, 0, 0},
	{16000466, 34, "绚烂烟火棒", "【氛围物道具】在新的一年里，让我们像烟火棒一样，熠熠生辉！", 3, true, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000467, 10, "烤火鸡", "在寒冷的冬天烤上一只热乎乎的火鸡，啊呜一口幸福满满。", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1657}, "", nil, 0, 0},
	{16000468, 10, "冬青蛋糕", "造型有点特别的蛋糕，因为加入了可可豆而微微带些苦味。", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1657}, "", nil, 0, 0},
	{16000469, 10, "姜饼小屋", "仿造姜饼饼的小屋制作出来的一种特色饼干，是姜饼饼的独创美食。", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1657}, "", nil, 0, 0},
	{16000470, 10, "居民订单券", "可用于完成居民订单。", 3, false, false, 100, 6, 999, false, nil, "", nil, 0, 0},
	{16000471, 10, "光影徽章", "光与影铸成的徽章，可以在光影商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1686}, "", nil, 0, 0},
	{16000472, 1, "小熊麻薯杯", "软糯香甜的小熊麻薯，一杯根本吃不够！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000473, 3, "初级VIP自选箱", "打开后可选择一种道具获得。", 5, false, false, 0, 50, 999, false, {type=3,rewards={212}}, "", nil, 0, 0},
	{16000474, 3, "中级VIP自选箱", "打开后可选择一种道具获得。", 5, false, false, 0, 50, 999, false, {type=3,rewards={213}}, "", nil, 0, 0},
	{16000475, 3, "高级VIP自选箱", "打开后可选择一种道具获得。", 5, false, false, 0, 50, 999, false, {type=3,rewards={214}}, "", nil, 0, 0},
	{16000476, 3, "特级VIP自选箱", "打开后可选择一种道具获得。", 5, false, false, 0, 50, 999, false, {type=3,rewards={215}}, "", nil, 0, 0},
	{16000477, 3, "顶级VIP自选箱", "打开后可选择一种道具获得。", 5, false, false, 0, 50, 999, false, {type=3,rewards={216}}, "", nil, 0, 0},
	{16000478, 10, "赤红石", "寻宝大冒险活动中获得的宝石，可以在寻宝秘藏商店里兑换物品。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 0},
	{16000479, 7, "小鱼灯", "燃着烛火的小鱼灯，能够承载奥比的心愿。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1757}, "", nil, 0, 0},
	{16000480, 10, "傩面徽章", "印着傩面图案的徽章，可以在元灯小铺中兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1756}, "", nil, 0, 0},
	{16000481, 3, "豪华VIP自选箱", "打开后可选择一种道具获得。", 5, false, false, 0, 50, 999, false, {type=3,rewards={217}}, "", nil, 0, 0},
	{16000482, 7, "糖水铃", "糖水铺里的点餐铃，按下它就能获得美味的糖水啦。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1700}, "", nil, 0, 0},
	{16000483, 10, "糖水优惠券", "糖水铺专用优惠券，可用于在糖水货架兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1699}, "", nil, 0, 0},
	{16000484, 3, "蜜蜂音箱自选包", "可以选择8种蜜蜂音箱其中1种获得，内含20晶钻。", 4, false, false, 10, 200, 99, false, {type=3,rewards={111}}, "", nil, 0, 0},
	{16000485, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1694}, "", nil, 0, 0},
	{16000486, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1822}, "", nil, 0, 0},
	{16000487, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1871}, "", nil, 0, 0},
	{16000488, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1907}, "", nil, 0, 0},
	{16000489, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1965}, "", nil, 0, 0},
	{16000490, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2010}, "", nil, 0, 0},
	{16000491, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2063}, "", nil, 0, 0},
	{16000492, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2134}, "", nil, 0, 0},
	{16000493, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2165}, "", nil, 0, 0},
	{16000494, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2214}, "", nil, 0, 0},
	{16000495, 30, "甜甜糖水花", "拥有这束花，冬季的每一天都会比糖水还甜。", 2, true, false, 3184, 60, 99, false, {friendship=5,limit=0}, "", nil, 0, 0},
	{16000496, 1, "柿柿如意", "吃下这口好运柿，就能让你在新的一年里事事称心如意。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000497, 3, "团龙年进度宝箱", "可任选“神龙谱”活动进度“团龙年”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={218}}, "", nil, 0, 0},
	{16000498, 14, "挖宝罗盘", "使用进行探宝，完成后获得宝箱（宝箱开启可获得挖宝树屋兑换材料）。", 3, true, false, 100, 20, 99, false, {rewardId=35,actId=1767,id=16000499}, "", nil, 0, 0},
	{16000499, 3, "猫猫宝箱", "开启后可获得挖宝树屋兑换材料。", 3, false, false, 10, 20, 99, false, {rewardId=35,actId=1767,type=1,rewards={222}}, "", nil, 0, 0},
	{16000500, 10, "猫头玉雕", "用和田玉制作的猫头玉雕，可以在挖宝商店里兑换物品。", 3, false, false, 100, 1.5, 99999, false, {rewardId=123,actId=1767}, "", nil, 0, 0},
	{16000501, 3, "飞龙吟进度宝箱", "可任选“神龙谱”活动进度“飞龙吟”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={218}}, "", nil, 0, 0},
	{16000502, 2, "紫禁礼仪队", "外面怪喜庆的，走，咱凑热闹去。", 3, true, false, 1000, 13, 99, false, {gameId=11}, "", nil, 0, 0},
	{16000503, 2, "紫禁护卫队", "越是热闹的地方，越是容易隐藏危险，走，该咱们上场了。", 3, true, false, 1000, 13, 99, false, {gameId=12}, "", nil, 0, 0},
	{16000504, 1, "花灯糖画", "用糖浆制成的花灯，香甜得想一口吃下去，可太精致了又不忍心下口，好纠结啊。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000505, 10, "联欢奖券", "联欢晚会专有的奖券，可以在联欢商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1764}, "", nil, 0, 0},
	{16000506, 3, "龙贺岁进度宝箱", "可任选“神龙谱”活动进度“龙贺岁”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={219}}, "", nil, 0, 0},
	{16000507, 3, "龙迎春进度宝箱", "可任选“神龙谱”活动进度“龙迎春”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={218}}, "", nil, 0, 0},
	{16000508, 3, "龙呈祥进度宝箱", "可任选“神龙谱”活动进度“龙呈祥”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={218}}, "", nil, 0, 0},
	{16000509, 3, "龙招财进度宝箱", "可任选“神龙谱”活动进度“龙招财”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={218}}, "", nil, 0, 0},
	{16000510, 3, "祥龙运进度宝箱", "可任选“神龙谱”活动进度“祥龙运”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={220}}, "", nil, 0, 0},
	{16000511, 3, "金龙喜进度宝箱", "可任选“神龙谱”活动进度“金龙喜”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={218}}, "", nil, 0, 0},
	{16000512, 3, "升龙意进度宝箱", "可任选“神龙谱”活动进度“升龙意”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={220}}, "", nil, 0, 0},
	{16000513, 3, "万物新进度宝箱", "可任选“神龙谱”活动进度“万物新”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={221}}, "", nil, 0, 0},
	{16000514, 3, "神龙谱进度宝箱", "可任选“神龙谱”活动进度“神龙谱”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={221}}, "", nil, 0, 0},
	{16000515, 7, "神龙墨", "凝聚神龙之力的墨条，能为神龙点睛。可用于神龙谱中抽取奖品。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=1733}, "", nil, 0, 0},
	{16000516, 10, "奏折", "一封御笔朱批的奏折，可以在翊坤宫兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1722}, "", nil, 0, 0},
	{16000517, 10, "摇铃逗猫棒", "晃动铃铛，发出声音吸引御猫吧。", 1, false, false, 1, 1, 9999, false, {rewardId=123,actId=1704}, "", nil, 0, 0},
	{16000518, 10, "喵喵积分", "与其他奥比结伴出游，结交更多御猫的奖励。", 1, false, false, 1, 1, 9999, false, {actId=1704}, "", nil, 1, 0},
	{16000519, 10, "好学生贴纸", "努力学习的小奥比，都能得到好学生贴纸。", 4, false, false, 0, 3, 9999, false, {rewardId=123,actId=1750}, "", nil, 0, 0},
	{16000520, 10, "永生花", "被封存的永开不败的花，可以在春风商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1782}, "", nil, 0, 0},
	{16000521, 3, "福缘晶钻袋", "财神喵赠送的晶钻袋子，开启后可随缘获得68~88晶钻", 5, false, false, 0, 88, 999, false, {type=1,rewards={223}}, "", nil, 0, 0},
	{16000522, 1, "枣泥好运酥", "吃下这口好运酥，将好运带到你身边！", 3, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,5},count=4,action=1,randomDrop={3,5}}, "", nil, 0, 0},
	{16000523, 44, "芹芹", "活泼爱笑，却又调皮捣蛋；点子多多，又爱助人为乐，这个姑娘就是芹芹！", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000524, 44, "八哥", "八哥饱读诗书，温文尔雅，经常不求回报地去帮助别人，所以人缘很好。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000525, 44, "十四哥", "十四哥机智过人，出口成章，最想当个富贵闲人。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000526, 44, "素素", "素素是有着最曼妙舞姿的姑娘，她温柔又勇敢，笑容如春风般温暖。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000527, 44, "枝枝", "枝枝最喜欢的就是读书，每当翻开书页，那双柔静的眼睛就会闪烁出夺目的光彩。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000528, 44, "白马", "挂着大红花的白马，任何奥比骑上去都是街上最靓的仔。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000529, 44, "蝴蝶", "那一只只美丽的蝴蝶，都将化成最美的回忆。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000530, 3, "魔术卡", "使用后会得到任意一张联欢卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766,type=3,rewards={224}}, "", nil, 0, 0},
	{16000531, 45, "联欢宝箱", "在小品《穿越古井》中使用过的道具，打开能获得任意一张联欢卡或魔术卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1766}, "", nil, 0, 0},
	{16000532, 10, "花灯烛", "猜对谜题就可以得到花灯烛，点燃美丽的花灯。", 2, false, false, 0, 0, 999, false, {rewardId=123,actId=1759}, "", nil, 0, 0},
	{16000533, 30, "冰糖芝麻元宵", "肥肥胖胖的芝麻元宵漂浮在热汤里，一嘴下去，满口芝麻浓香。", 3, false, false, 0, 0, 999, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000534, 30, "冰晶鲜花元宵", "晶莹剔透的鲜花元宵，吃下去的不仅是美味，还有花朵的浪漫。", 3, false, false, 0, 0, 999, false, {friendship=2,limit=1}, "", nil, 0, 0},
	{16000535, 30, "柿柿如意元宵", "轻轻咬开元宵的皮，里面的柿子果酱喷涌而出，这滋味简直让人停不下来。", 3, false, false, 0, 0, 999, false, {friendship=3,limit=1}, "", nil, 0, 0},
	{16000536, 30, "小兔山楂元宵", "兔兔元宵再可爱，也要一口把它吃掉！", 3, false, false, 0, 0, 999, false, {friendship=3,limit=1}, "", nil, 0, 0},
	{16000537, 30, "糯叽叽元宝元宵", "一口一个大元宝，以后天天赚元宝！", 4, false, false, 0, 0, 999, false, {friendship=5,limit=1}, "", nil, 0, 0},
	{16000538, 3, "金枝玉叶同款系列服装", "包含清新可人枝枝装*1、蓝粉枝枝丝帕*1、蓝粉枝枝发饰*1。", 3, false, false, 0, 300, 999, false, {type=1,rewards={225}}, "", nil, 0, 0},
	{16000539, 3, "八阿哥同款系列服装", "包含才俊非凡蓝袍*1、才俊非凡圆帽*1、才俊非凡扇子*1。", 3, false, false, 0, 300, 999, false, {type=1,rewards={226}}, "", nil, 0, 0},
	{16000540, 3, "素素同款系列服装", "包含温柔大方素素发*1、蓝绿素素发饰*1、温柔大方素素装*1。", 3, false, false, 0, 300, 999, false, {type=1,rewards={227}}, "", nil, 0, 0},
	{16000541, 3, "初级馈赠自选箱", "打开后可选择一种自然馈赠获得。", 3, false, false, 0, 50, 999, false, {type=3,rewards={228}}, "", nil, 0, 0},
	{16000542, 3, "中级馈赠自选箱", "打开后可选择一种自然馈赠获得。", 3, false, false, 0, 50, 999, false, {type=3,rewards={229}}, "", nil, 0, 0},
	{16000543, 3, "高级馈赠自选箱", "打开后可选择一种自然馈赠获得。", 3, false, false, 0, 50, 999, false, {type=3,rewards={230}}, "", nil, 0, 0},
	{16000544, 3, "特级馈赠自选箱", "打开后可选择一种自然馈赠获得。", 3, false, false, 0, 50, 999, false, {type=3,rewards={231}}, "", nil, 0, 0},
	{16000545, 3, "顶级馈赠自选箱", "打开后可选择一种自然馈赠获得。", 4, false, false, 0, 50, 999, false, {type=3,rewards={232}}, "", nil, 0, 0},
	{16000546, 3, "初级之灵自选箱", "打开后可选择一种自然之灵获得。", 4, false, false, 0, 0, 999, false, {type=3,rewards={233}}, "", nil, 0, 0},
	{16000547, 3, "中级之灵自选箱", "打开后可选择一种自然之灵获得。", 4, false, false, 0, 0, 999, false, {type=3,rewards={234}}, "", nil, 0, 0},
	{16000548, 3, "高级之灵自选箱", "打开后可选择一种自然之灵获得。", 4, false, false, 0, 0, 999, false, {type=3,rewards={235}}, "", nil, 0, 0},
	{16000549, 3, "特级之灵自选箱", "打开后可选择一种自然之灵获得。", 4, false, false, 0, 0, 999, false, {type=3,rewards={236}}, "", nil, 0, 0},
	{16000550, 3, "顶级之灵自选箱", "打开后可选择一种自然之灵获得。", 5, false, false, 0, 0, 999, false, {type=3,rewards={237}}, "", nil, 0, 0},
	{16000551, 10, "茶会邀请函", "拥有茶会邀请函的奥比，可以在茶会小铺上买到秘境森林的特色商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1799}, "", nil, 0, 0},
	{16000552, 7, "精灵饼干", "LUCKY EMMA精心制作的饼干，充满幸运的能量，能帮助奥比实现心中所愿。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1798}, "", nil, 0, 0},
	{16000553, 1, "缤纷甜心豆", "酸甜美味、缤纷可口，让春天的美味在口中绽放！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000554, 1, "甜杏巧克力蛋", "超甜的甜杏，遇上微苦的巧克力，碰撞出奇妙的味道。", 4, false, false, 100, 15, 99, false, {randomRate=0.2,fixedDrop={3,5},count=3,action=1,randomDrop={3,5}}, "", nil, 0, 0},
	{16000555, 1, "抹茶巧克力蛋", "春日的一抹绿意，舌尖的十分幸福。", 4, false, false, 100, 15, 99, false, {randomRate=0.2,fixedDrop={3,5},count=3,action=1,randomDrop={3,5}}, "", nil, 0, 0},
	{16000556, 1, "榛子巧克力蛋", "既有脆脆的榛子，又有细腻的巧克力，吃一口整个春日都丰富起来了。", 4, false, false, 100, 15, 99, false, {randomRate=0.2,fixedDrop={3,5},count=3,action=1,randomDrop={3,5}}, "", nil, 0, 0},
	{16000557, 10, "空间晶石", "凝聚了空间能量的晶石，可用于扩充永恒乐园的收纳空间。", 3, false, false, 100, 2, 999, false, nil, "", nil, 0, 0},
	{16000558, 10, "清新泉水", "一捧清新的泉水，可在永恒乐园结缘时使用，刷新可结缘的自然之灵或自然馈赠。", 3, false, false, 100, 10, 999, false, nil, "", nil, 0, 0},
	{16000559, 55, "稀有幽灵卡盒", "稀有级幽灵酒店契约卡卡盒，可随机开出1张1~3星的幽灵契约卡。", 3, false, false, 100, 15, 999, false, {skin=2}, "", nil, 1, 0},
	{16000560, 55, "典藏幽灵卡盒", "典藏级幽灵酒店契约卡卡盒，可随机开出1张2~4星的幽灵契约卡。", 4, false, false, 100, 40, 999, false, {skin=3}, "", nil, 1, 0},
	{16000561, 55, "晨星幽灵卡盒", "晨星级幽灵酒店契约卡卡盒，可随机开出1张3~5星的幽灵契约卡。", 5, false, false, 100, 80, 999, false, {skin=1}, "", nil, 1, 0},
	{16000562, 10, "星星徽章", "特殊材料制作的小熊星星，可以用于兑换契约卡盒。", 2, false, false, 0, 0.1, 99999, false, {rewardId=35,actId=1806}, "", nil, 0, 0},
	{16000563, 55, "精致幽灵自选盒", "精致级幽灵酒店契约卡自选卡盒，可自选1张2星的幽灵契约卡。", 2, false, false, 100, 60, 999, false, {skin=7}, "", nil, 1, 0},
	{16000564, 55, "稀有幽灵自选盒", "稀有级幽灵酒店契约卡自选卡盒，可自选1张3星的幽灵契约卡。", 3, false, false, 100, 120, 999, false, {skin=4}, "", nil, 1, 0},
	{16000565, 55, "典藏幽灵自选盒", "典藏级幽灵酒店契约卡自选卡盒，可自选1张4星的幽灵契约卡。", 4, false, false, 100, 360, 999, false, {skin=5}, "", nil, 1, 0},
	{16000566, 55, "晨星幽灵自选盒", "晨星级幽灵酒店契约卡自选卡盒，可自选1张5星的幽灵契约卡。", 5, false, false, 100, 720, 999, false, {skin=6}, "", nil, 1, 0},
	{16000567, 10, "狂欢游戏机", "参与游戏狂欢周获得的游戏机，可用于兑换岛务厅准备的奖品。", 1, false, false, 0, 0.1, 99999, false, nil, "", nil, 0, 0},
	{16000568, 1, "海鸥薯条", "在城市漫步的同时，尝一口美味的薯条吧，还要小心海鸥哦。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000569, 10, "海鸥徽章", "海鸥赠送的徽章，可以在城市纪念商店里兑换礼品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1832}, "", nil, 0, 0},
	{16000570, 3, "开胃菜进度宝箱", "可任选“星厨宴”活动进度“开胃菜”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={238}}, "", nil, 0, 0},
	{16000571, 3, "前菜进度宝箱", "可任选“星厨宴”活动进度“前菜”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={238}}, "", nil, 0, 0},
	{16000572, 3, "副菜进度宝箱", "可任选“星厨宴”活动进度“副菜”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={239}}, "", nil, 0, 0},
	{16000573, 3, "主菜进度宝箱", "可任选“星厨宴”活动进度“主菜”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={238}}, "", nil, 0, 0},
	{16000574, 3, "甜品进度宝箱", "可任选“星厨宴”活动进度“甜品”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={240}}, "", nil, 0, 0},
	{16000575, 7, "奶酪骰子", "香醇美味的奶酪骰子，点数绝对标准。可用于星厨宴中抽取奖品。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=1825}, "", nil, 0, 0},
	{16000576, 10, "神庙古币", "镌刻着神庙纹样的古币，可以在神庙集市中兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1842}, "", nil, 0, 0},
	{16000577, 7, "衔愿之鸽", "能够穿越时间长河的白鸽，能够帮助奥比将愿望送达至遥远的曾经。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1841}, "", nil, 0, 0},
	{16000578, 1, "亮闪闪金黄苹果", "如黄金一般诱人的苹果，能为你带来难以想象的好运气。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000579, 56, "绿史莱姆变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成绿史莱姆，持续5分钟。", 4, false, false, 0, 8, 999, false, nil, "", nil, 0, 0},
	{16000580, 56, "蓝史莱姆变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成蓝史莱姆，持续5分钟。", 4, false, false, 0, 8, 999, false, nil, "", nil, 0, 0},
	{16000581, 56, "红史莱姆变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成红史莱姆，持续5分钟。", 4, false, false, 0, 8, 999, false, nil, "", nil, 0, 0},
	{16000582, 56, "奇形方块变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成奇形方块，持续5分钟。", 3, false, false, 0, 3, 999, false, nil, "", nil, 0, 0},
	{16000583, 56, "小精灵变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成小精灵，持续5分钟。", 3, false, false, 0, 3, 999, false, nil, "", nil, 0, 0},
	{16000584, 56, "巨大大变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变大，持续5分钟。", 3, false, false, 0, 3, 999, false, nil, "", nil, 0, 0},
	{16000585, 56, "缩小小变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变小，持续5分钟。", 3, false, false, 0, 3, 999, false, nil, "", nil, 0, 0},
	{16000586, 10, "闪星徽章", "特殊材料制作的小熊星星，可以用于兑换契约自选卡盒。", 4, false, false, 0, 5, 99999, false, {rewardId=35,actId=1806}, "", nil, 0, 0},
	{16000587, 3, "幽灵契约自选箱", "打开后可选择晨星幽灵自选盒或时空之钥其中一种获得。", 5, false, false, 0, 0, 999, false, {type=3,rewards={242}}, "", nil, 0, 0},
	{16000588, 10, "造物奖状", "每位参加造物赛的奥比都能获得一张纪念奖状。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1855}, "", nil, 0, 0},
	{16000589, 10, "童趣积木", "充满童趣的小积木，可以在玩具商店里兑换玩具。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1892}, "", nil, 0, 0},
	{16000590, 7, "童心泡泡", "用童心凝结的童心泡泡，能够唤醒玩具盒中的小精灵，实现心中所愿。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1891}, "", nil, 0, 0},
	{16000591, 1, "小黄鸭冰淇淋", "别因为冰淇淋太可爱而舍不得吃，不然它们就要融化了鸭！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000592, 10, "快乐电池", "可以为游戏世界供能，可以在游戏商店里兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1881}, "", nil, 0, 0},
	{16000593, 10, "侦探烟斗", "象征着推理能力的标志物，可以在探案有礼中兑换物品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1918}, "", nil, 0, 0},
	{16000594, 10, "校园纪念币", "奥比学院出品的校园纪念币，可在毕业集市上兑换校园纪念品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1936}, "", nil, 0, 0},
	{16000595, 7, "青春签字笔", "受到校园精灵祝福的签字笔，用它书写的青春心愿都会实现。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1935}, "", nil, 0, 0},
	{16000596, 1, "毕业饼干", "盛满祝福心意的饼干，送给最真挚的朋友。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000597, 10, "星星瓶", "用星星装满瓶子，即可获得对应奖励。", 4, false, false, 0, 0, 0, false, nil, "", nil, 1, 0},
	{16000598, 55, "稀有萌二卡盒", "稀有级萌二写真卡盒，可随机开出1张1~3星的萌二写真。", 3, false, false, 100, 15, 999, false, {skin=2}, "", nil, 1, 0},
	{16000599, 55, "典藏萌二卡盒", "典藏级萌二写真卡盒，可随机开出1张2~4星的萌二写真。", 4, false, false, 100, 40, 999, false, {skin=3}, "", nil, 1, 0},
	{16000600, 55, "晨星萌二卡盒", "晨星级萌二写真卡盒，可随机开出1张3~5星的萌二写真。", 5, false, false, 100, 80, 999, false, {skin=1}, "", nil, 1, 0},
	{16000601, 10, "萌二徽章", "特殊材料制作的萌二徽章，可以用于兑换萌二写真卡盒。", 2, false, false, 0, 0.1, 99999, false, {rewardId=35,actId=1949}, "", nil, 0, 0},
	{16000602, 10, "萌星徽章", "特殊材料制作的萌星徽章，可以用于兑换萌二写真自选卡盒。", 4, false, false, 0, 5, 99999, false, {rewardId=35,actId=1949}, "", nil, 0, 0},
	{16000603, 55, "精致萌二自选盒", "精致级萌二写真自选卡盒，可自选1张2星的萌二写真。", 2, false, false, 100, 60, 999, false, {skin=7}, "", nil, 1, 0},
	{16000604, 55, "稀有萌二自选盒", "稀有级萌二写真自选卡盒，可自选1张1~3星的萌二写真。", 3, false, false, 100, 120, 999, false, {skin=4}, "", nil, 1, 0},
	{16000605, 55, "典藏萌二自选盒", "典藏级萌二写真自选卡盒，可自选1张4星的萌二写真。", 4, false, false, 100, 360, 999, false, {skin=5}, "", nil, 1, 0},
	{16000606, 55, "晨星萌二自选盒", "晨星级萌二写真自选卡盒，可自选1张5星的萌二写真。", 5, false, false, 100, 720, 999, false, {skin=6}, "", nil, 1, 0},
	{16000607, 1, "章鱼冰淇淋", "欢乐盛夏，共享章鱼冰淇淋，每一口都让你感受到阳光和海浪的味道！", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000608, 3, "能量盒魔法宝箱", "可任选“巴啦啦”活动进度“能量盒”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={243}}, "", nil, 0, 0},
	{16000609, 3, "黑蜂阵魔法宝箱", "可任选“巴啦啦”活动进度“黑蜂阵”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={243}}, "", nil, 0, 0},
	{16000610, 3, "全身变魔法宝箱", "可任选“巴啦啦”活动进度“全身变”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={244}}, "", nil, 0, 0},
	{16000611, 3, "黑动术魔法宝箱", "可任选“巴啦啦”活动进度“黑动术”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={243}}, "", nil, 0, 0},
	{16000612, 3, "魔法鞋魔法宝箱", "可任选“巴啦啦”活动进度“魔法鞋”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={243}}, "", nil, 0, 0},
	{16000613, 3, "黑水晶魔法宝箱", "可任选“巴啦啦”活动进度“黑水晶”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={243}}, "", nil, 0, 0},
	{16000614, 3, "百宝书魔法宝箱", "可任选“巴啦啦”活动进度“百宝书”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={245}}, "", nil, 0, 0},
	{16000615, 3, "黑魔洞魔法宝箱", "可任选“巴啦啦”活动进度“黑魔洞”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={243}}, "", nil, 0, 0},
	{16000616, 3, "光能量魔法宝箱", "可任选“巴啦啦”活动进度“光能量”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={245}}, "", nil, 0, 0},
	{16000617, 3, "黑风阵魔法宝箱", "可任选“巴啦啦”活动进度“黑风阵”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={246}}, "", nil, 0, 0},
	{16000618, 3, "魔仙箭魔法宝箱", "可任选“巴啦啦”活动进度“魔仙箭”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={246}}, "", nil, 0, 0},
	{16000619, 7, "魔仙蝴蝶", "凝聚着友爱快乐的魔仙能量，能够启动魔仙八音盒。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=1948}, "", nil, 0, 0},
	{16000620, 10, "青春糖果", "吃一颗糖果，就能想起那些青涩又甜蜜的青春回忆", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1939}, "", nil, 0, 0},
	{16000621, 10, "校园八音盒", "八音盒动人的旋律中，流转着对校园时光的深深喜爱。", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1939}, "", nil, 0, 0},
	{16000622, 10, "毕业花束", "缤纷的毕业花束，绽放着青春的欢笑和未来的无限美好。", 3, false, false, 0, 0, 999, false, {rewardId=123,actId=1939}, "", nil, 0, 0},
	{16000623, 1, "二周年星海蛋糕", "二周年的星海蛋糕，适合邀请小伙伴们共度周年。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000624, 10, "派对帽", "戴上这顶帽子，就说明你已经加入了快乐的派对，可以拿到派对上的礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1999}, "", nil, 0, 0},
	{16000625, 7, "甜蜜蛋糕", "最甜蜜的小蛋糕，诚心向它许愿，蛋糕精灵就会听到你的愿望。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=1998}, "", nil, 0, 0},
	{16000626, 10, "幸运海星", "bling的幸运海星，捞到它会得到大海的祝福！", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=1981}, "", nil, 0, 0},
	{16000627, 34, "缤纷礼花", "【氛围物道具】点燃礼花，绽放缤纷！", 3, true, false, 100, 3, 99, false, nil, "", nil, 0, 0},
	{16000628, 44, "凌美琪", "活泼又开朗的小魔仙凌美琪，总是能想出奇奇怪怪的鬼主意。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000629, 44, "凌美雪", "聪明又冷静的小魔仙凌美雪，做事沉稳，擅长学习。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000630, 44, "魔仙小蓝魔法棒", "不管是家务活还是黑魔仙都能应对，是居家旅行必备的法宝。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000631, 44, "游乐王子魔动枪", "游乐王子哪边都不帮，但是魔动枪好像有它自己的想法。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000632, 44, "小黑魔仙黑魔琴", "就算曾经演奏黑暗的乐章，现在也能重新追寻光明。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000633, 44, "魔仙女王法杖", "强大而温柔，就如同主人一般，默默地守护着魔仙堡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000634, 44, "黑魔仙魔法棒", "当心灵被黑暗侵蚀，再强大的力量也会失去意义。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000635, 3, "巴啦啦能量卡", "神奇的魔仙八音盒，能够召唤任意一张法宝卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985,type=3,rewards={247}}, "", nil, 0, 0},
	{16000636, 45, "魔仙百宝书", "打开魔仙百宝书，可以随机获得一张法宝卡或巴啦啦能量卡。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=1985}, "", nil, 0, 0},
	{16000637, 1, "幸福奶油果冻", "分享一块奶油果冻，给小伙伴带来甜蜜的幸福吧！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000638, 10, "星际币", "星际大赛专属兑换币，可在星际大赛商店兑换物品。", 3, false, false, 0, 3, 9999, false, {rewardId=35,actId=2031}, "", nil, 0, 0},
	{16000639, 10, "回忆碎片", "奥比在探海之旅中产生的回忆碎片，可用于解锁忆海拼图。", 3, false, false, 0, 3, 9999, false, {rewardId=35,actId=1986}, "", nil, 0, 0},
	{16000640, 3, "随机晶钻福袋", "开启后随机获得60-65晶钻。", 4, false, false, 0, 60, 999, false, {type=1,rewards={248}}, "", nil, 0, 0},
	{16000641, 3, "小蓝同款系列服装", "包含小蓝的黄色长发*1、小蓝的连衣裙*1、小蓝的太阳花帽*1、小蓝的粉色凉鞋*1。", 3, false, false, 0, 146, 999, false, {type=1,rewards={269}}, "", nil, 0, 0},
	{16000642, 3, "严莉莉同款系列服装", "包含严莉莉的黑短发*1、严莉莉的黑短发*1、严莉莉的迷你裙*1、严莉莉的短靴*1。", 3, false, false, 0, 146, 999, false, {type=1,rewards={270}}, "", nil, 0, 0},
	{16000644, 10, "特训饮料", "每消耗1个，可以在星际赛期间，为参加萌宠竞技的1只宠物，增加1项五维若干值。", 3, false, false, 0, 3, 9999, false, {rewardId=35,actId=1722}, "", nil, 0, 0},
	{16000645, 10, "奶油裱花袋", "将盛满心意的奶油，注入奶油喷泉里，蛋糕精灵就能感受到你的真心。", 3, false, false, 0, 3, 9999, false, {rewardId=35,actId=2003}, "", nil, 0, 0},
	{16000646, 7, "仙女棒", "短暂而美丽的仙女棒，快将心愿说给它听。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2019}, "", nil, 0, 0},
	{16000647, 10, "海贸订单券", "可用于完成普通海贸订单。", 3, false, false, 100, 6, 999, false, nil, "", nil, 0, 0},
	{16000648, 10, "海贸惊喜订单券", "可用于完成海贸惊喜订单。", 3, false, false, 100, 6, 999, false, nil, "", nil, 0, 0},
	{16000649, 1, "恋恋苹果糖", "甜蜜的糖浆，包裹着酸涩的苹果，每咬一口，都仿佛在舌尖点燃了心动的烟花。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000650, 10, "夏夜兑换券", "在夏夜转转乐中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2019}, "", nil, 0, 0},
	{16000651, 57, "110星际竞技高手", "110星际大赛-萌宠竞技，最终累计得分排行1001~10000，且成功参与过匹配的宠物可佩戴。", 3, false, false, 0, 0, 999, false, {petSportsActId=2020}, "", nil, 1, 0},
	{16000652, 57, "110星际竞技王者", "110星际大赛-萌宠竞技，最终累计得分排行101~1000，且成功参与过匹配的宠物可佩戴。", 3, false, false, 0, 0, 999, false, {petSportsActId=2020}, "", nil, 1, 0},
	{16000653, 57, "110星际竞技大师", "110星际大赛-萌宠竞技，最终累计得分排行1~100，且成功参与过匹配的宠物可佩戴。", 4, false, false, 0, 0, 999, false, {petSportsActId=2020}, "", nil, 1, 0},
	{16000654, 3, "海底材料自选箱", "打开后可选择一种海底材料获得。", 3, false, false, 0, 5, 999, false, {type=3,rewards={272}}, "", nil, 0, 0},
	{16000655, 7, "数字骰子", "可在夏夜游园中使用，使用后随机前进1-6步。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=2018}, "", nil, 0, 0},
	{16000656, 7, "金色骰子", "可在夏夜游园中使用，使用后可指定前进步数。", 4, false, false, 0, 0, 999, false, {rewardId=35,actId=2018}, "", nil, 0, 0},
	{16000657, 10, "苹果糖", "甜甜的苹果糖，可在夏夜小摊上兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2058}, "", nil, 0, 0},
	{16000658, 5, "小海贸材料魔药", "使用后，下1次提交海贸订单获得材料奖励增加100%。", 2, true, false, 100, 10, 99, false, {effectTimes=1,id=49,value=10000}, "", nil, 0, 0},
	{16000659, 5, "中海贸材料魔药", "使用后，下2次提交海贸订单获得材料奖励增加100%。", 3, true, false, 100, 20, 99, false, {effectTimes=2,id=49,value=10000}, "", nil, 0, 0},
	{16000660, 5, "大海贸材料魔药", "使用后，下3次提交海贸订单获得材料奖励增加100%。", 4, true, false, 100, 30, 99, false, {effectTimes=3,id=49,value=10000}, "", nil, 0, 0},
	{16000661, 10, "学院徽章", "代表优秀学生身份的徽章，能够在学院奖励中兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2075}, "", nil, 0, 0},
	{16000662, 10, "奖励花花贴纸", "参与知识课堂获得，可在开卷有益中换取奖励。", 4, false, false, 0, 3, 9999, false, {rewardId=123,actId=2067}, "", nil, 0, 0},
	{16000663, 1, "旋转茶杯蛋糕", "兔兔小蛋糕想玩旋转茶杯，想带着快乐又美味的味道，旋转进奥比的嘴巴里！", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000664, 1, "莓莓奶昔", "梦幻草莓，遇上浓郁奶昔，每一口都甜到你的心间！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=2,randomDrop={2,2}}, "", nil, 0, 0},
	{16000665, 10, "乐园门票", "拥有乐园门票的游客，可以在奇妙游乐园的快乐商店中选购乐园纪念品。", 3, false, false, 0, 3, 9999, false, {rewardId=35,actId=2099}, "", nil, 0, 0},
	{16000666, 10, "银杏叶", "参与灯谜活动就可以得到银杏叶，点亮祈愿牌。", 2, false, false, 0, 0, 999, false, {rewardId=123,actId=2056}, "", nil, 0, 0},
	{16000667, 1, "16周年蛋糕", "庆祝奥比岛16周年的红丝绒蛋糕，和朋友一起分享吧！", 2, true, false, 250, 7, 99, false, {randomRate=0.2,fixedDrop={3,1},count=4,action=1,randomDrop={3,1}}, "", nil, 0, 0},
	{16000668, 7, "快乐因子", "从快乐情绪中产生的因子，能消解所有的负面情绪。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=2088}, "", nil, 0, 0},
	{16000669, 18, "居民生日礼盒", "奥比岛居民专属生日礼盒。只能用于送给过生日的居民噢！", 3, false, false, 10, 1, 99, false, nil, "", nil, 0, 0},
	{16000670, 3, "荆棘藤进度宝箱", "可任选“镜之国”活动进度“荆棘藤”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={274}}, "", nil, 0, 0},
	{16000671, 3, "镜之国进度宝箱", "可任选“镜之国”活动进度“镜之国”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={274}}, "", nil, 0, 0},
	{16000672, 3, "白棋镜进度宝箱", "可任选“镜之国”活动进度“白棋镜”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={275}}, "", nil, 0, 0},
	{16000673, 3, "红桃心进度宝箱", "可任选“镜之国”活动进度“红桃心”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={274}}, "", nil, 0, 0},
	{16000674, 3, "快乐梦进度宝箱", "可任选“镜之国”活动进度“快乐梦”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={276}}, "", nil, 0, 0},
	{16000675, 1, "哈皮奇趣盒", "哈皮精心制作的趣味盒子，可以分享给大家！", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,1},count=1,action=5,randomDrop={3,1}}, "", nil, 0, 0},
	{16000676, 1, "爱心礼品盒", "奥比快乐节特供的礼物盒子，可以将爱心分享给大家。", 3, true, false, 100, 1, 99, false, {randomRate=0.2,fixedDrop={3,5},count=1,action=4,randomDrop={3,5}}, "", nil, 0, 0},
	{16000677, 7, "梦幻草莓", "传说中最好吃的梦幻草莓，寄托着奥比们对于美味的向往。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2104}, "", nil, 0, 0},
	{16000678, 10, "甜莓兑换券", "在甜莓萌粒购中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2104}, "", nil, 0, 0},
	{16000679, 10, "草莓牛奶", "甜甜的草莓牛奶，可在草莓货架中兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2105}, "", nil, 0, 0},
	{16000680, 10, "绿橘徽章", "特殊材料制作的小泡芙绿橘徽章，可以用于兑换小泡芙画册卡盒。", 2, false, false, 0, 0.1, 99999, false, {rewardId=35,actId=2126}, "", nil, 0, 0},
	{16000681, 10, "橙橘徽章", "特殊材料制作的小泡芙橙橘徽章，可以用于兑换小泡芙画册自选卡盒。", 4, false, false, 0, 5, 99999, false, {rewardId=35,actId=2126}, "", nil, 0, 0},
	{16000682, 10, "庄园密钥", "刻有收藏家印记的钥匙，能打开庄园大门，兑换限定商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2143}, "", nil, 0, 0},
	{16000683, 10, "遗歌之引", "凝聚着弗尔帕斯幻梦与歌声的镜界之引，能够增加墟梦遗歌镜界的探索值。", 4, false, false, 0, 0, 9999, false, nil, "", nil, 0, 0},
	{16000684, 7, "订单小票", "记录了订单详情的小票，可在草莓大促销中生成物品。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=2108}, "", nil, 0, 0},
	{16000685, 55, "稀有泡芙卡盒", "稀有级小泡芙画册卡盒，可随机开出1张1~3星的小泡芙画册。", 3, false, false, 100, 15, 999, false, {skin=2}, "", nil, 1, 0},
	{16000686, 55, "典藏泡芙卡盒", "典藏级小泡芙画册卡盒，可随机开出1张2~4星的小泡芙画册。", 4, false, false, 100, 40, 999, false, {skin=3}, "", nil, 1, 0},
	{16000687, 55, "晨星泡芙卡盒", "晨星级小泡芙画册卡盒，可随机开出1张3~5星的小泡芙画册。", 5, false, false, 100, 80, 999, false, {skin=1}, "", nil, 1, 0},
	{16000688, 55, "稀有泡芙自选盒", "稀有级小泡芙画册自选卡盒，可自选1张1~3星的小泡芙画册。", 3, false, false, 100, 120, 999, false, {skin=4}, "", nil, 1, 0},
	{16000689, 55, "典藏泡芙自选盒", "典藏级小泡芙画册自选卡盒，可自选1张4星的小泡芙画册。", 4, false, false, 100, 360, 999, false, {skin=5}, "", nil, 1, 0},
	{16000690, 55, "晨星泡芙自选盒", "晨星级小泡芙画册自选卡盒，可自选1张5星的小泡芙画册。", 5, false, false, 100, 720, 999, false, {skin=6}, "", nil, 1, 0},
	{16000691, 1, "香脆蝴蝶酥", "金黄香脆的蝴蝶酥，一吃就停不下来，就连盒子里的酥皮碎也被吃得干干净净。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=2,randomDrop={2,2}}, "", nil, 0, 0},
	{16000692, 10, "雾月票根", "印有雾月剧团标志的票根，可以在雾月商店中兑换礼品。", 3, false, false, 0, 3, 9999, false, {rewardId=35,actId=2178}, "", nil, 0, 0},
	{16000693, 10, "影楼相片", "幽梦影楼拍摄的相片，凭相片可以在影楼柜台选购商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2152}, "", nil, 0, 0},
	{16000694, 7, "影楼胶卷", "幽梦影楼照相机的专属胶卷，只有用它才能拍摄特殊的相片。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2146}, "", nil, 0, 0},
	{16000695, 10, "幽梦影券", "幽梦影楼特有的票券，可以用于兑换心愿之物，化解心中的执念。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2146}, "", nil, 0, 0},
	{16000696, 10, "老式手绢", "用手绢擦干净玻璃橱窗上的雾，才能看清幽梦影楼的内景。", 3, false, false, 0, 0, 9999, false, {rewardId=35,actId=2145}, "", nil, 0, 0},
	{16000697, 1, "LAURA小蛋糕", "爆米花味的小蛋糕，是LAURA喜欢和朋友分享的美味。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000698, 10, "小丑气球", "象征着快乐的小丑气球，可在小丑商店中兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2180}, "", nil, 0, 0},
	{16000699, 7, "马戏魔术帽", "蕴含快乐魔力的魔术帽，帽子里似乎藏着什么。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2183}, "", nil, 0, 0},
	{16000700, 10, "小丑兑换券", "在马戏萌粒购中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2183}, "", nil, 0, 0},
	{16000701, 56, "蓝地旋烟花变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成蓝色的地旋烟花，持续5分钟。", 4, false, false, 0, 8, 999, false, nil, "", nil, 0, 0},
	{16000702, 10, "狂欢捕鱼币", "参与捕鱼狂欢周获得的捕鱼币，可用于兑换岛务厅准备的奖品。", 1, false, false, 0, 0.1, 99999, false, nil, "", nil, 0, 0},
	{16000703, 3, "表情宠物蛋", "使用后可以随机获得发射小人、焦绿猫、醒神仔、卡皮巴拉小黄豚中的1只（幼态，属性随机）。", 6, false, false, 0, 1500, 999, false, {type=5,rewards={281}}, "", nil, 0, 0},
	{16000704, 3, "艺展盲盒", "开启盲盒后，可在10款家具中随机获得一款，已获得的家具不会再次获得。", 5, false, false, 0, 0, 999, false, {type=6,rewards={283}}, "", nil, 0, 0},
	{16000705, 1, "暖冬热饮", "热腾腾的饮品，不仅温暖了双手，也温暖了心。快和朋友们共享这份暖意吧！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=2,randomDrop={2,2}}, "", nil, 0, 0},
	{16000706, 7, "莲花香炉", "袅袅香气中，柳仙下凡来。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=2242}, "", nil, 0, 0},
	{16000707, 10, "恋冬手套", "在冬日温暖你的手套，可在恋冬小铺中兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2186}, "", nil, 0, 0},
	{16000708, 7, "恋冬雪花球", "将雪花与爱封存，能否就此获得永恒。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2189}, "", nil, 0, 0},
	{16000709, 10, "恋冬兑换券", "在恋冬萌粒购中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2189}, "", nil, 0, 0},
	{16000710, 7, "数字骰子", "可在马戏奇妙夜中使用，使用后随机前进1-6步。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=2184}, "", nil, 0, 16000655},
	{16000711, 7, "金色骰子", "可在马戏奇妙夜中使用，使用后可指定前进步数。", 4, false, false, 0, 0, 999, false, {rewardId=35,actId=2184}, "", nil, 0, 16000656},
	{16000712, 10, "雪粒粒", "", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2218}, "", nil, 0, 0},
	{16000713, 1, "阿鼠樱花果冻", "吃上一大口粉嫩的果冻，口腔里满满的花香，幸福感瞬间爆棚！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000714, 1, "灯笼糕", "与亲朋好友共赏花灯之时，不妨分享这份灯笼糕，分享团聚的喜悦。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000715, 1, "发财元宝酥", "吃的时候别忘记许愿啊！心中默念：“发财，发财，财运来！”", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000716, 10, "绿苹徽章", "特殊材料制作的Starbliss绿苹徽章，可以用于兑换Starbliss卡盒。", 2, false, false, 0, 0.1, 99999, false, {rewardId=35,actId=2243}, "", nil, 0, 0},
	{16000717, 10, "红苹徽章", "特殊材料制作的Starbliss红苹徽章，可以用于兑换Starbliss自选卡盒。", 4, false, false, 0, 5, 99999, false, {rewardId=35,actId=2243}, "", nil, 0, 0},
	{16000718, 55, "稀有Starbliss卡盒", "稀有级Starbliss卡盒，可随机开出1张1~3星的Starbliss观察卡。", 3, false, false, 100, 15, 999, false, {skin=2}, "", nil, 1, 0},
	{16000719, 55, "典藏Starbliss卡盒", "典藏级Starbliss卡盒，可随机开出1张2~4星的Starbliss观察卡。", 4, false, false, 100, 40, 999, false, {skin=3}, "", nil, 1, 0},
	{16000720, 55, "晨星Starbliss卡盒", "晨星级Starbliss卡盒，可随机开出1张3~5星的Starbliss观察卡。", 5, false, false, 100, 80, 999, false, {skin=1}, "", nil, 1, 0},
	{16000721, 55, "稀有Starbliss自选盒", "稀有级Starbliss自选卡盒，可自选1张1~3星的Starbliss观察卡。", 3, false, false, 100, 120, 999, false, {skin=4}, "", nil, 1, 0},
	{16000722, 55, "典藏Starbliss自选盒", "典藏级Starbliss自选卡盒，可自选1张4星的Starbliss观察卡。", 4, false, false, 100, 360, 999, false, {skin=5}, "", nil, 1, 0},
	{16000723, 55, "晨星Starbliss自选盒", "晨星级Starbliss自选卡盒，可自选1张5星的Starbliss观察卡。", 5, false, false, 100, 720, 999, false, {skin=6}, "", nil, 1, 0},
	{16000724, 3, "寻朔漠进度宝箱", "可任选“蛇仙缘”活动进度“寻朔漠”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={277}}, "", nil, 0, 16000608},
	{16000725, 3, "登九层进度宝箱", "可任选“蛇仙缘”活动进度“登九层”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={277}}, "", nil, 0, 16000609},
	{16000726, 3, "绘千壁进度宝箱", "可任选“蛇仙缘”活动进度“绘千壁”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={278}}, "", nil, 0, 16000610},
	{16000727, 3, "梦千年进度宝箱", "可任选“蛇仙缘”活动进度“梦千年”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={277}}, "", nil, 0, 16000611},
	{16000728, 3, "飞天现进度宝箱", "可任选“蛇仙缘”活动进度“飞天现”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={277}}, "", nil, 0, 16000612},
	{16000729, 3, "奏仙乐进度宝箱", "可任选“蛇仙缘”活动进度“奏仙乐”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={277}}, "", nil, 0, 16000613},
	{16000730, 3, "舞霓裳进度宝箱", "可任选“蛇仙缘”活动进度“舞霓裳”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={279}}, "", nil, 0, 16000614},
	{16000731, 3, "拈莲花进度宝箱", "可任选“蛇仙缘”活动进度“拈莲花”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={277}}, "", nil, 0, 16000615},
	{16000732, 3, "焕神光进度宝箱", "可任选“蛇仙缘”活动进度“焕神光”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={279}}, "", nil, 0, 16000616},
	{16000733, 3, "极乐国进度宝箱", "可任选“蛇仙缘”活动进度“极乐国”中的一种奖励。", 6, false, false, 0, 20, 999, false, {type=4,rewards={280}}, "", nil, 0, 16000617},
	{16000734, 3, "大敦煌进度宝箱", "可任选“蛇仙缘”活动进度“大敦煌”中的一种奖励。", 6, false, false, 0, 20, 999, false, {type=4,rewards={280}}, "", nil, 0, 16000618},
	{16000735, 10, "恋冬币", "恋冬街的唯一有效流通货币，可用于恋冬街店铺的开业或装修扩建。", 3, false, false, 0, 3, 9999, false, nil, "", nil, 1, 0},
	{16000736, 10, "武林秘籍", "一本神秘的武林秘籍，可以在同福客栈兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2262}, "", nil, 0, 0},
	{16000737, 10, "熊爪贝壳", "3.8海洋家园计划代币", 3, false, false, 0, 0, 9999, false, nil, "", nil, 0, 0},
	{16000738, 56, "红地旋烟花变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成红色的地旋烟花，持续5分钟。", 4, false, false, 0, 8, 999, false, nil, "", nil, 0, 0},
	{16000739, 56, "风华影视变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成影视城员工，持续5分钟。", 4, false, false, 0, 8, 999, false, nil, "", nil, 0, 0},
	{16000740, 10, "福运贴", "一张写有福字的好运贴，可以在花灯商店兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2279}, "", nil, 0, 0},
	{16000741, 7, "赏味食盒", "精致食盒装美味，茶楼邀您一起尽享美食。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2277}, "", nil, 0, 0},
	{16000742, 10, "赏味兑换券", "在赏味萌粒购中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2277}, "", nil, 0, 0},
	{16000743, 3, "SUSUMI眼妆宝箱", "开启宝箱后，可在6款SUSUMI眼妆中随机获得一款，已获得的眼妆不会再次获得。", 4, false, false, 0, 20, 999, false, {type=6,rewards={282}}, "", nil, 0, 0},
	{16000744, 7, "擀面杖", "制作茶点必备的擀面杖，可在赏味时刻中生成物品。", 3, false, false, 0, 0, 999, false, {rewardId=35,actId=2285}, "", nil, 0, 0},
	{16000745, 10, "花之信", "来自不同时空花朵的信，可以在百花商店兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2297}, "", nil, 0, 0},
	{16000746, 10, "阿鼠徽章", "代表参加SUSUMI漫展的徽章，可以凭徽章在漫展摊位上兑换纪念品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2316}, "", nil, 0, 0},
	{16000747, 7, "漫展硬币", "蕴含快乐魔力的硬币，可以在漫展购买SUSUMI周边。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2312}, "", nil, 0, 0},
	{16000748, 10, "漫展兑换券", "在漫展萌粒购中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2312}, "", nil, 0, 0},
	{16000749, 10, "【废弃】", "", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 0},
	{16000750, 10, "团圆花灯", "参与花灯寻游活动就可以得到团圆花灯，点亮灯笼。", 2, false, false, 0, 0, 999, false, {rewardId=123,actId=2284}, "", nil, 0, 16000272},
	{16000751, 1, "心森星星糖", "和最真挚的朋友，分享来自心之森的甜蜜吧。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000752, 10, "巡游纪念币", "缤纷巡游的特制纪念币，可以在巡游小摊上兑换纪念品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2352}, "", nil, 0, 0},
	{16000753, 7, "香水分装瓶", "决定今天搭配什么香味了吗，快把香水装进分装瓶里吧。", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=2334}, "", nil, 0, 0},
	{16000754, 1, "兔耳巧克力蛋", "甜蜜的巧克力蛋，加上可爱的兔耳造型，谁看了都想一口吃掉！", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000755, 10, "缤纷徽章", "拥有缤纷徽章，才可以在巡游中制作美食。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2356}, "", nil, 0, 0},
	{16000756, 3, "花香浸心进度宝箱", "可任选“馥郁香”活动进度“花香浸心”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={284}}, "", nil, 0, 16000670},
	{16000757, 3, "香橙萃芳进度宝箱", "可任选“馥郁香”活动进度“香橙萃芳”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={284}}, "", nil, 0, 16000671},
	{16000758, 3, "椰奶调配进度宝箱", "可任选“馥郁香”活动进度“椰奶调配”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={285}}, "", nil, 0, 16000672},
	{16000759, 3, "木质撷华进度宝箱", "可任选“馥郁香”活动进度“木质撷华”中的一种奖励。", 4, false, false, 0, 20, 999, false, {type=4,rewards={284}}, "", nil, 0, 16000673},
	{16000760, 3, "东方香韵进度宝箱", "可任选“馥郁香”活动进度“东方香韵”中的一种奖励。", 5, false, false, 0, 20, 999, false, {type=4,rewards={286}}, "", nil, 0, 16000674},
	{16000761, 10, "戳戳手指棒", "用手指棒戳开盲盒上的纸片，找到SUSUMI藏在盒子里的玩具吧！", 3, false, false, 0, 0, 9999, false, {rewardId=35,actId=2320}, "", nil, 0, 0},
	{16000762, 7, "缤纷气球", "五彩斑斓的气球，承载着美妙的愿望。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2355}, "", nil, 0, 0},
	{16000763, 10, "巡游兑换券", "在巡游萌粒购中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2355}, "", nil, 0, 0},
	{16000764, 10, "艺展兑换券", "一张来自心森艺展的兑换券，可以兑换潮玩家具。", 4, false, false, 0, 10, 9999, false, {rewardId=287,actId=2364}, "", nil, 0, 0},
	{16000765, 55, "稀有米花卡盒", "稀有级米花卡盒，可随机开出1张1~3星的米花照片卡。", 3, false, false, 100, 15, 999, false, {skin=2}, "", nil, 1, 0},
	{16000766, 55, "典藏米花卡盒", "典藏级米花卡盒，可随机开出1张2~4星的米花照片卡。", 4, false, false, 100, 40, 999, false, {skin=3}, "", nil, 1, 0},
	{16000767, 55, "晨星米花卡盒", "晨星级米花卡盒，可随机开出1张3~5星的米花照片卡。", 5, false, false, 100, 80, 999, false, {skin=1}, "", nil, 1, 0},
	{16000768, 55, "稀有米花自选盒", "稀有级米花自选卡盒，可自选1张1~3星的米花照片卡。", 3, false, false, 100, 120, 999, false, {skin=4}, "", nil, 1, 0},
	{16000769, 55, "典藏米花自选盒", "典藏级米花自选卡盒，可自选1张4星的米花照片卡。", 4, false, false, 100, 360, 999, false, {skin=5}, "", nil, 1, 0},
	{16000770, 55, "晨星米花自选盒", "晨星级米花自选卡盒，可自选1张5星的米花照片卡。", 5, false, false, 100, 720, 999, false, {skin=6}, "", nil, 1, 0},
	{16000771, 10, "猪米粉花", "特殊材料制作的猪米粉花，可以用于兑换米花卡盒。", 2, false, false, 0, 0.1, 99999, false, {rewardId=35,actId=2371}, "", nil, 0, 0},
	{16000772, 10, "猪米红花", "特殊材料制作的猪米红花，可以用于兑换米花自选卡盒。", 4, false, false, 0, 5, 99999, false, {rewardId=35,actId=2371}, "", nil, 0, 0},
	{16000773, 10, "许愿石", "能够实现愿望的神奇石头，能够兑换限定商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2345}, "", nil, 0, 0},
	{16000774, 7, "香水分装瓶·限时", "决定今天搭配什么香味了吗，快把香水装进分装瓶里吧。(活动结束后将自动转换为100金币)", 4, false, false, 100, 80, 999, false, {rewardId=69,actId=2334}, "", nil, 0, 0},
	{16000775, 10, "初心碎片", "MiMiA公主遗忘的初心碎片，可用于解锁寻心拼趣。", 3, false, false, 0, 3, 9999, false, {rewardId=35,actId=2347}, "", nil, 0, 0},
	{16000776, 10, "灵感值", "日常生活中不时出现的小灵感，用于解锁新领域或设备更新。", 3, false, false, 0, 3, 9999, false, nil, "", nil, 1, 0},
	{16000779, 10, "港乐磁带", "记录着黄金时代港乐的磁带，能够兑换港城纪念品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2386}, "", nil, 0, 0},
	{16000781, 10, "nono蝴蝶结", "使用nono同款蝴蝶结，可在市集商店兑换限定商品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2411}, "", nil, 0, 0},
	{16000782, 10, "日常回溯器", "可以用于日常任务回溯。", 4, false, false, 0, 10, 9999, false, nil, "", nil, 0, 0},
	{16000783, 10, "奥比贴纸", "手帐er必入的基础款贴纸，可以在素材商店中换购其他素材。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2437}, "", nil, 0, 0},
	{16000784, 56, "周年蛋糕变身弹", "摆放在场景中有效期5分钟的神奇变身烟雾弹，一碰就爆且会把小奥比变成周年蛋糕，持续5分钟。", 4, false, false, 0, 8, 999, false, nil, "", nil, 0, 0},
	{16000785, 7, "除尘刷", "使用除尘刷，和负面情绪说拜拜。", 5, false, false, 0, 99, 999, false, {rewardId=171,actId=2418}, "", nil, 0, 0},
	{16000786, 10, "星梦之晶", "如星辰般璀璨的梦化作的晶石，可以在奇想商店兑换纪念品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2479}, "", nil, 0, 0},
	{16000787, 55, "稀有鹿鹿卡盒", "稀有级鹿鹿卡盒，可随机开出1张1~3星的鹿鹿照片卡。", 3, false, false, 100, 15, 999, false, {skin=2}, "", nil, 1, 0},
	{16000788, 55, "典藏鹿鹿卡盒", "典藏级鹿鹿卡盒，可随机开出1张2~4星的鹿鹿照片卡。", 4, false, false, 100, 40, 999, false, {skin=3}, "", nil, 1, 0},
	{16000789, 55, "晨星鹿鹿卡盒", "晨星级鹿鹿卡盒，可随机开出1张3~5星的鹿鹿照片卡。", 5, false, false, 100, 80, 999, false, {skin=1}, "", nil, 1, 0},
	{16000790, 55, "稀有鹿鹿自选盒", "稀有级鹿鹿自选卡盒，可自选1张1~3星的鹿鹿照片卡。", 3, false, false, 100, 120, 999, false, {skin=4}, "", nil, 1, 0},
	{16000791, 55, "典藏鹿鹿自选盒", "典藏级鹿鹿自选卡盒，可自选1张4星的鹿鹿照片卡。", 4, false, false, 100, 360, 999, false, {skin=5}, "", nil, 1, 0},
	{16000792, 55, "晨星鹿鹿自选盒", "晨星级鹿鹿自选卡盒，可自选1张5星的鹿鹿照片卡。", 5, false, false, 100, 720, 999, false, {skin=6}, "", nil, 1, 0},
	{16000793, 55, "闪耀鹿鹿卡盒", "闪耀鹿鹿卡盒，可随机开出1张4~5星的鹿鹿特殊照片卡。", 5, false, false, 100, 360, 999, false, {skin=1}, "", nil, 1, 0},
	{16000794, 10, "粉猫猫章", "特殊材料制作的粉猫猫章，可以用于兑换鹿鹿卡盒。", 2, false, false, 0, 0.1, 99999, false, {rewardId=35,actId=2462}, "", nil, 0, 0},
	{16000795, 10, "黄猫猫章", "特殊材料制作的黄猫猫章，可以用于兑换鹿鹿自选卡盒。", 4, false, false, 0, 5, 99999, false, {rewardId=35,actId=2462}, "", nil, 0, 0},
	{16000796, 1, "萌萌铃兰果冻", "来自另一个次元的特别果冻，每一口都是新奇的美味邂逅。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000797, 59, "太阳进度宝箱", "可任选“行星记”活动进度“太阳”中的一种奖励。", 4, false, false, 0, 20, 999, false, {activityId=2461,process=1}, "", nil, 0, 16000608},
	{16000798, 59, "水星进度宝箱", "可任选“行星记”活动进度“水星”中的一种奖励。", 4, false, false, 0, 20, 999, false, {activityId=2461,process=2}, "", nil, 0, 16000609},
	{16000799, 59, "金星进度宝箱", "可任选“行星记”活动进度“金星”中的一种奖励。", 5, false, false, 0, 20, 999, false, {activityId=2461,process=3}, "", nil, 0, 16000610},
	{16000800, 59, "地球进度宝箱", "可任选“行星记”活动进度“地球”中的一种奖励。", 4, false, false, 0, 20, 999, false, {activityId=2461,process=4}, "", nil, 0, 16000611},
	{16000801, 59, "火星进度宝箱", "可任选“行星记”活动进度“火星”中的一种奖励。", 4, false, false, 0, 20, 999, false, {activityId=2461,process=5}, "", nil, 0, 16000612},
	{16000802, 59, "木星进度宝箱", "可任选“行星记”活动进度“木星”中的一种奖励。", 4, false, false, 0, 20, 999, false, {activityId=2461,process=6}, "", nil, 0, 16000613},
	{16000803, 59, "土星进度宝箱", "可任选“行星记”活动进度“土星”中的一种奖励。", 5, false, false, 0, 20, 999, false, {activityId=2461,process=7}, "", nil, 0, 16000614},
	{16000804, 59, "天王星进度宝箱", "可任选“行星记”活动进度“天王星”中的一种奖励。", 4, false, false, 0, 20, 999, false, {activityId=2461,process=8}, "", nil, 0, 16000615},
	{16000805, 59, "海王星进度宝箱", "可任选“行星记”活动进度“海王星”中的一种奖励。", 5, false, false, 0, 20, 999, false, {activityId=2461,process=9}, "", nil, 0, 16000616},
	{16000806, 59, "天狼星进度宝箱", "可任选“行星记”活动进度“天狼星”中的一种奖励。", 6, false, false, 0, 20, 999, false, {activityId=2461,process=10}, "", nil, 0, 16000617},
	{16000807, 59, "北极星进度宝箱", "可任选“行星记”活动进度“北极星”中的一种奖励。", 6, false, false, 0, 20, 999, false, {activityId=2461,process=11}, "", nil, 0, 16000618},
	{16000808, 7, "星际宇航船", "目标星球已定位，星际宇航船，出发！", 4, false, false, 100, 80, 999, false, {rewardId=99,actId=2461}, "", nil, 0, 0},
	{16000809, 10, "虹光露珠", "蕴藏着七彩虹光的露珠，能够在朝颜小铺中兑换礼物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2386}, "", nil, 0, 0},
	{16000810, 10, "油漆刷", "不仅能粉刷出梦想家园，还可以在商店兑换奖品。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2489}, "", nil, 0, 0},
	{16000811, 1, "牵牛花糕点", "拈一块淡雅的牵牛花糕点，就着清茶细啜，品味悠悠夏日。", 4, true, false, 100, 20, 99, false, {randomRate=0.2,fixedDrop={2,2},count=5,action=1,randomDrop={2,2}}, "", nil, 0, 0},
	{16000812, 7, "星际宇航船·限时", "目标星球已定位，星际宇航船，出发！。(活动结束后将自动转换为100金币)", 4, false, false, 100, 80, 999, false, {rewardId=69,actId=2461}, "", nil, 0, 0},
	{16000813, 7, "牵牛花", "夏日灿烂盛开的牵牛花，倾听着奥比们的愿望。", 5, false, false, 0, 60, 999, false, {rewardId=96,actId=2496}, "", nil, 0, 0},
	{16000814, 10, "夏花兑换券", "在夏花萌粒购中获得的兑换券，能够在兑换商店中兑换商品。", 4, false, false, 0, 3, 9999, false, {rewardId=69,actId=2496}, "", nil, 0, 0},
	{16000815, 1, "三周年次元蛋糕", "三周年的次元蛋糕，适合邀请小伙伴们共度周年。", 4, true, false, 100, 16, 99, false, {randomRate=0.2,fixedDrop={2,2},count=4,action=1,randomDrop={2,2}}, "", nil, 0, 16000815},
	{16001001, 8, "初级植物加速剂", "一次性使用，作物、果树生产时间缩短5分钟。", 2, true, false, 100, 1, 99, false, {stacked=true,id=8,value=5}, "", nil, 0, 0},
	{16001002, 8, "初级植物经验剂", "一次性使用，作物、果树生产经验增加50%。", 2, true, false, 100, 1, 99, false, {id=4,value=5000}, "", nil, 0, 0},
	{16001003, 8, "初级植物成长剂", "一次性使用，作物、果树生产量增加1个。", 2, true, false, 100, 1, 99, false, {stacked=true,id=17,value=1}, "", nil, 0, 0},
	{16001004, 8, "初级植物幸运剂", "一次性使用，作物、果树获得幸运物品概率加15%。", 2, true, false, 100, 1, 99, false, {id=9,value=1500}, "", nil, 0, 0},
	{16001011, 8, "中级植物加速剂", "一次性使用，作物、果树生产时间缩短30分钟。", 3, true, false, 100, 6, 99, false, {stacked=true,id=8,value=30}, "", nil, 0, 0},
	{16001012, 8, "中级植物经验剂", "一次性使用，作物、果树生产经验增加100%。", 3, true, false, 100, 6, 99, false, {id=4,value=10000}, "", nil, 0, 0},
	{16001013, 8, "中级植物成长剂", "一次性使用，作物、果树生产量增加5个。", 3, true, false, 100, 6, 99, false, {stacked=true,id=17,value=5}, "", nil, 0, 0},
	{16001014, 8, "中级植物幸运剂", "一次性使用，作物、果树获得幸运物品概率加50%。", 3, true, false, 100, 6, 99, false, {id=9,value=5000}, "", nil, 0, 0},
	{16001021, 8, "高级植物加速剂", "一次性使用，作物、果树生产时间缩短1小时。", 4, true, false, 100, 12, 99, false, {stacked=true,id=8,value=60}, "", nil, 0, 0},
	{16001022, 8, "高级植物经验剂", "一次性使用，作物、果树生产经验增加200%。", 4, true, false, 100, 12, 99, false, {id=4,value=20000}, "", nil, 0, 0},
	{16001023, 8, "高级植物成长剂", "一次性使用，作物、果树生产量增加20个。", 4, true, false, 100, 12, 99, false, {stacked=true,id=17,value=20}, "", nil, 0, 0},
	{16001024, 8, "高级植物幸运剂", "一次性使用，作物、果树获得幸运物品概率加100%。", 4, true, false, 100, 12, 99, false, {id=9,value=10000}, "", nil, 0, 0},
	{16001031, 8, "特级植物加速剂", "一次性使用，作物、果树生产时间缩短2小时。", 4, true, false, 100, 24, 99, false, {stacked=true,id=8,value=120}, "", nil, 0, 0},
	{16001041, 8, "超级植物加速剂", "一次性使用，作物、果树生产时间缩短3小时。", 4, true, false, 100, 36, 99, false, {stacked=true,id=8,value=180}, "", nil, 0, 0},
	{16001051, 8, "顶级植物加速剂", "一次性使用，作物、果树生产时间缩短6小时。", 4, true, false, 100, 72, 99, false, {stacked=true,id=8,value=360}, "", nil, 0, 0},
	{16002001, 9, "初级动物加速剂", "一次性使用，动物生产时间缩短5分钟。", 2, true, false, 100, 1, 99, false, {stacked=true,id=8,value=5}, "", nil, 0, 0},
	{16002002, 9, "初级动物经验剂", "一次性使用，动物生产经验增加50%。", 2, true, false, 100, 1, 99, false, {id=4,value=5000}, "", nil, 0, 0},
	{16002003, 9, "初级动物成长剂", "一次性使用，动物生产量增加1个。", 2, true, false, 100, 1, 99, false, {stacked=true,id=17,value=1}, "", nil, 0, 0},
	{16002004, 9, "初级动物幸运剂", "一次性使用，动物获得幸运物品概率增加15%。", 2, true, false, 100, 1, 99, false, {id=9,value=1500}, "", nil, 0, 0},
	{16002011, 9, "中级动物加速剂", "一次性使用，动物生产时间缩短30分钟。", 3, true, false, 100, 6, 99, false, {stacked=true,id=8,value=30}, "", nil, 0, 0},
	{16002012, 9, "中级动物经验剂", "一次性使用，动物生产经验增加100%。", 3, true, false, 100, 6, 99, false, {id=4,value=10000}, "", nil, 0, 0},
	{16002013, 9, "中级动物成长剂", "一次性使用，动物生产量增加5个。", 3, true, false, 100, 6, 99, false, {stacked=true,id=17,value=5}, "", nil, 0, 0},
	{16002014, 9, "中级动物幸运剂", "一次性使用，动物获得幸运物品概率增加50%。", 3, true, false, 100, 6, 99, false, {id=9,value=5000}, "", nil, 0, 0},
	{16002021, 9, "高级动物加速剂", "一次性使用，动物生产时间缩短1小时。", 4, true, false, 100, 12, 99, false, {stacked=true,id=8,value=60}, "", nil, 0, 0},
	{16002022, 9, "高级动物经验剂", "一次性使用，动物生产经验增加200%。", 4, true, false, 100, 12, 99, false, {id=4,value=20000}, "", nil, 0, 0},
	{16002023, 9, "高级动物成长剂", "一次性使用，动物生产量增加20个。", 4, true, false, 100, 12, 99, false, {stacked=true,id=17,value=20}, "", nil, 0, 0},
	{16002024, 9, "高级动物幸运剂", "一次性使用，动物获得幸运物品概率增加100%。", 4, true, false, 100, 12, 99, false, {id=9,value=10000}, "", nil, 0, 0},
	{16002031, 9, "特级动物加速剂", "一次性使用，动物生产时间缩短2小时。", 4, true, false, 100, 24, 99, false, {stacked=true,id=8,value=120}, "", nil, 0, 0},
	{16002041, 9, "超级动物加速剂", "一次性使用，动物生产时间缩短3小时。", 4, true, false, 100, 36, 99, false, {stacked=true,id=8,value=180}, "", nil, 0, 0},
	{16002051, 9, "顶级动物加速剂", "一次性使用，动物生产时间缩短6小时。", 4, true, false, 100, 72, 99, false, {stacked=true,id=8,value=360}, "", nil, 0, 0},
	{16003001, 17, "测试用物品1", "", 1, true, false, 10, 0, 99, false, {url="image/picture/1.png"}, "", nil, 0, 0},
	{16003002, 17, "测试用物品2", "", 1, true, false, 10, 0, 99, false, {url="image/picture/2.png"}, "", nil, 0, 0},
	{16003003, 20, "星纳的来信", "星纳给你寄的信。", 1, false, false, 0, 0, 99, false, {id=1}, "", nil, 0, 0},
	{16003004, 20, "贝加尔的来信", "贝加尔给你寄的信。", 1, false, false, 0, 0, 99, false, {id=2}, "", nil, 0, 0},
	{16003005, 20, "大贤者第三封信", "大贤者给你寄来的第三封信，指引你寻找第三枚碎片。", 1, false, false, 0, 0, 99, false, {id=3}, "", nil, 0, 0},
	{16003006, 20, "大贤者第四封信", "大贤者给你寄来的第四封信，指引你寻找第四枚碎片。", 1, false, false, 0, 0, 99, false, {id=4}, "", nil, 0, 0},
	{16003007, 20, "大贤者第五封信", "大贤者给你寄来的第五封信，指引你寻找第五枚碎片。", 1, false, false, 0, 0, 99, false, {id=5}, "", nil, 0, 0},
	{16003008, 20, "大贤者第六封信", "大贤者给你寄来的第六封信，指引你寻找第六枚碎片。", 1, false, false, 0, 0, 99, false, {id=6}, "", nil, 0, 0},
	{16003009, 20, "大贤者第七封信", "大贤者给你寄来的第七封信，指引你寻找第七枚碎片。", 1, false, false, 0, 0, 99, false, {id=7}, "", nil, 0, 0},
	{16003010, 20, "大贤者第八封信", "大贤者给你寄来的第八封信，指引你寻找第八枚碎片。", 1, false, false, 0, 0, 99, false, {id=8}, "", nil, 0, 0},
	{16004001, 18, "奥比徽章", "【岛民的好感礼物】好感度+1，奥比岛特色纪念装饰品。", 2, true, false, 400, 10, 99, false, nil, "", nil, 0, 0},
	{16004002, 18, "时光碎片", "【岛民的好感礼物】好感度+1，传说中由大贤者亲手打造的时钟碎片。", 2, true, false, 400, 10, 99, false, nil, "", nil, 0, 0},
	{16004003, 18, "仙人掌摆件", "【小耶的好感礼物】好感度+3，可爱的仙人掌摆件，可以送给小耶当装饰。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004004, 18, "大师手稿", "【小耶的好感礼物】好感度+10，顶尖时装设计大师的亲笔手稿，可以送给小耶。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004005, 18, "向日葵盆栽", "【温蒂的好感礼物】好感度+3，精致热烈的向日葵盆栽，很适合放在料理店。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004006, 18, "定制料理台", "【温蒂的好感礼物】好感度+10，能满足多种烹饪需求的专属定制料理台。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004007, 18, "怀表", "【奥利的好感礼物】好感度+3，听说里面藏着温蒂的照片。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004008, 18, "定制渔具", "【奥利的好感礼物】好感度+10，每个钓鱼爱好者都喜欢的手工定制渔具。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004009, 18, "手冲咖啡套装", "【潘潘的好感礼物】好感度+3，高级手冲咖啡套装，可以享受手冲咖啡的美味。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004010, 18, "家具图鉴", "【潘潘的好感礼物】好感度+10，被誉为“家居设计师的圣经”。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004011, 18, "速记笔", "【索娅秘书的好感礼物】好感度+3，使用感很好，非常方便携带的笔。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004012, 18, "八音盒", "【索娅秘书的好感礼物】好感度+10，送给索娅秘书或许会触发惊喜。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004013, 18, "储蓄罐", "【金块的好感礼物】好感度+3，放着金币的存钱罐，摇起来叮当响。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004014, 18, "纪念金币", "【金块的好感礼物】好感度+10，奥比岛发行的第一版金币，价值不菲。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004015, 18, "乐谱架", "【希尔达的好感礼物】好感度+3，轻便又好看感的乐谱架，是演出时的必需品。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004016, 18, "古董小提琴", "【希尔达的好感礼物】好感度+10，传闻世上仅此一把。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004017, 18, "黑猫摆件", "【黑古拉的好感礼物】好感度+3，神秘的黑猫，看上去就自带高贵的气息。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004018, 18, "胸针", "【黑古拉的好感礼物】好感度+10，绘有奥柏集团标志的胸针，搭配西装刚刚好。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004019, 18, "红茶杯", "【夜西的好感礼物】好感度+3，红茶骨瓷杯，是夜西从大炎国带回的特产之一。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004020, 18, "牛皮记账本", "【夜西的好感礼物】好感度+10，手感很好的记账本，精明商人的必备品。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004021, 18, "打火机", "备用", 4, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004022, 18, "泡面碗", "【梅尔的好感度礼物】好感度+3，可以用来泡面吃的大碗，是梅尔最常用的餐具。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004023, 18, "小狗抱枕", "【梅尔的好感度礼物】好感度+10，梅尔呆在家里打游戏时，最喜欢靠在抱枕上。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004024, 18, "领带", "【维克多的好感度礼物】好感度+3，一条适合出席任何场合时佩戴的领带。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004025, 18, "发财树", "【维克多的好感度礼物】好感度+10，从大炎国进口的植物，是维克多的最爱。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004026, 18, "七色花", "【阿拉斯的好感度礼物】好感度+3，阿拉斯很喜欢的花。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004027, 18, "魔药箱", "【阿拉斯的好感度礼物】好感度+10，阿拉斯很需要这样一只魔药箱。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004028, 18, "蓝莓馅饼", "【薇丽塔的好感度礼物】好感度+3，薇丽塔最喜欢的料理。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004029, 18, "老相册", "【薇丽塔的好感度礼物】好感度+10，一本被经常翻看的相册，里面保存着薇丽塔的思念。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004030, 18, "小蜜蜂台灯", "【W阿姨的好感度礼物】好感度+3，有了小蜜蜂的陪伴，W阿姨也能在晚上阅读小奥比的信。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004031, 18, "奥比期刊", "【W阿姨的好感度礼物】好感度+10，W阿姨经常阅读的刊物。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004032, 18, "棕榈树盆栽", "【马古力的好感度礼物】好感度+3，马古力先生坚信，在办公室里摆绿植能缓解眼部疲劳。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004033, 18, "小狗胸针", "【马古力的好感度礼物】好感度+10，马古力先生很喜欢但却绝不会在正式场合佩戴的胸针。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004034, 18, "运转核心", "【柯莱的好感度礼物】好感度+3，对于机器人来说，或许运转核心是最重要的。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004035, 18, "失落水晶", "【柯莱的好感度礼物】好感度+10，因为理真喜欢，所以柯莱喜欢。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004036, 18, "银餐具", "【莱昂的好感度礼物】好感度+3，莱昂的一个小习惯：吃鸡蛋一定要用专门的工具。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004037, 18, "三色堇花束", "【莱昂的好感度礼物】好感度+10，紫色三色堇的花语为：沉默不语和无条件的爱。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004038, 18, "水培薄荷", "【马露的好感度礼物】好感度+3，马露很喜欢薄荷这种健康又能提神的植物。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004039, 18, "哑铃", "【马露的好感度礼物】好感度+10，对于热爱健身的马露先生来说，哑铃是必不可少的器械。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004040, 18, "茉莉花茶", "【大胡子叔叔的好感度礼物】好感度+3，大胡子叔叔偶尔也会用随身携带的茶杯泡茉莉花茶。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004041, 18, "南瓜沙发", "【大胡子叔叔的好感度礼物】好感度+10，据说大胡子叔叔在家很喜欢坐在南瓜沙发上喝茶。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004042, 18, "甜杏蛋糕", "【理真的好感度礼物】好感度+3，一份加了满满甜杏果肉的蛋糕，是理真最喜欢的口味。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004043, 18, "水晶礼盒", "【理真的好感度礼物】好感度+10，失落水晶，理真的最爱。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004044, 18, "练习水晶球", "【索菲亚的好感度礼物】好感度+3，索菲亚推广创造术必要道具。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004045, 18, "信使雪鸮", "【索菲亚的好感度礼物】好感度+10，曾经的创造师们会通过雪鸮传递信件。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004046, 18, "花蜜", "【风儿的好感度礼物】好感度+3，早上新鲜采集的花蜜，味道一级棒。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004047, 18, "郁金香盆栽", "【风儿的好感度礼物】好感度+10，装满郁金香的盆栽，寄托着风儿对友人的思念。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004048, 18, "营养罐头", "【艾拉的好感度礼物】好感度+3，含有丰富营养的罐头，深受小动物们的喜爱。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004049, 18, "岛屿动物图鉴", "【艾拉的好感度礼物】好感度+10，记录着动物的精美图片和科普知识，是艾拉最喜欢的书。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004050, 18, "家庭合影", "【莫顿的好感度礼物】好感度+3，莫顿最珍惜的家庭合影。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004051, 18, "温妮亚歌声唱片", "【莫顿的好感度礼物】好感度+10，刻录着温妮亚歌声的唱片，能够抚平莫顿心中的哀伤。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004052, 18, "手电筒", "【弗里奇的好感度礼物】好感度+3，能够照亮夜路的巡逻必备单品。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004053, 18, "锦旗", "【弗里奇的好感度礼物】好感度+10，奥比居民赠送的锦旗，是对弗里奇工作的最大肯定。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004501, 18, "羽毛笔", "使用稀有翎羽精心制作的羽毛笔，深得旅行者们的喜爱。", 2, true, false, 400, 10, 99, false, nil, "", nil, 0, 0},
	{16004502, 18, "精装墨水", "使用高级染料配制的墨水，深得旅行者们的喜爱。", 2, true, false, 400, 10, 99, false, nil, "", nil, 0, 0},
	{16004503, 18, "复古信纸", "使用复古工艺制作的信纸，深得旅行者们的喜爱。", 2, true, false, 400, 10, 99, false, nil, "", nil, 0, 0},
	{16004504, 18, "火漆印章", "带有奥比特色的精致火漆印章，深得旅行者们的喜爱。", 2, true, false, 400, 10, 99, false, nil, "", nil, 0, 0},
	{16004601, 18, "奥比伴手礼", "奥比们精心制作的奥比特色熊型公仔，在旅行者之间十分流行。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004602, 18, "奥比明信片", "奥比们精心制作的奥比明信片，在旅行者之间十分流行。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004603, 18, "奥比熊风铃", "奥比们精心制作的奥比特色熊风铃，在旅行者之间十分流行。", 3, true, false, 2000, 50, 99, false, nil, "", nil, 0, 0},
	{16004701, 18, "永生之花", "神殿制作的稀有永生花礼盒，据说可以给旅行者们带来祝福。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16004702, 18, "时空之环", "神殿制作的环形纪念品，据说可以给旅行者们带来祝福。", 4, true, false, 10000, 250, 99, false, nil, "", nil, 0, 0},
	{16005001, 19, "友情树灌溉水", "友情正如同水，淡而回味良久。使用后友情树成长+3。", 1, true, false, 50, 1, 99, false, {rate=1,type=1,value=3}, "", nil, 0, 0},
	{16005002, 19, "友情树专用肥", "满含友谊的互助，让树苗茁壮成长。使用后友情树成长+10", 2, true, false, 200, 5, 99, false, {rate=1,type=2,value=10}, "", nil, 0, 0},
	{16006001, 21, "水球", "小心使用水球哦", 1, false, false, 0, 0, 99, false, {forceShowInThrowItemPanel=true}, "", nil, 0, 0},
	{16006002, 21, "拖鞋", "哈皮最讨厌可爱的拖鞋，这可不能被绅士所能接受。", 1, true, false, 10, 1, 999, false, {actId=1008,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16006003, 21, "花神之泪", "清晨落在花瓣上的第一滴露珠，可以净化花魇。", 1, true, false, 10, 1, 999, false, {actId=1035,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16006004, 21, "狗尾草箭", "狗尾巴草，可造成少量伤害", 1, false, false, 0, 0, 99, false, {endSound=141465,startSoundGirl=141464,startSoundBoy=141464}, "", nil, 0, 0},
	{16006005, 21, "提神魔药", "神奇魔药，快速回血", 1, false, false, 0, 0, 99, false, {endSound=141467,startSoundGirl=141466,startSoundBoy=141466}, "", nil, 0, 0},
	{16006006, 21, "蒲公英肉丸", "将蒲公英叶子捣碎成泥后捏成的肉丸子，入口后会带来难以忍受的超苦味道！投掷年兽可增加150-250投喂量。", 1, true, false, 10, 1, 999, false, {rewardId=123,inAction=46,actId=1205,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16006007, 21, "怪味鱼罐头", "世界上最臭的食物之一，光是打开它就需要万分的勇气！投掷年兽可增加200-350投喂量。", 1, true, false, 10, 1, 999, false, {rewardId=123,inAction=46,actId=1205,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16006008, 21, "礼花", "庆典上使用的玩乐投掷道具，似乎在很久之前就有了，能为庆典派对增加氛围。", 1, true, false, 10, 1, 999, false, {}, "", nil, 0, 0},
	{16006009, 21, "拖鞋果实", "哈皮最讨厌可爱的拖鞋，即便是拖鞋形状的果实也不行！", 1, true, false, 10, 1, 999, false, {actId=1505,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16006010, 21, "红油麻辣冰淇淋", "甜甜的奶香味与香辣的红油味交织，为你带来不一样的味觉体验。投掷年兽可增加150-250投喂量。", 1, true, false, 10, 1, 999, false, {rewardId=123,inAction=46,actId=1708,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16006011, 21, "香辣水果切", "酸酸的水果搭配上辣椒粉，看似奇怪的组合，实际上却相当美味！投掷年兽可增加200-350投喂量。", 1, true, false, 10, 1, 999, false, {rewardId=123,inAction=46,actId=1708,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16006012, 21, "趣味拖鞋", "哈皮最讨厌可爱的拖鞋，就算是游乐园限定版拖鞋也不行！", 1, true, false, 10, 1, 999, false, {actId=2079,endSound=141148,startSoundGirl=141149,startSoundBoy=141150}, "", nil, 0, 0},
	{16007001, 31, "月亮结晶", "具有魔法力量的黄色晶体，用于在梦幻国度商店兑换奖励。", 1, false, false, 40, 1, 99999, false, nil, "", nil, 0, 0},
	{16007002, 31, "梦幻国度门票", "神殿发放的魔法门票，可以维持梦幻国度的稳定，保护冒险的奥比。", 4, false, false, 100, 50, 99, false, nil, "", nil, 0, 0},
	{16007003, 31, "梦幻绿晶碎片", "梦幻绿晶的碎片，用于修复梦幻绿晶。", 2, false, false, 100, 10, 99, false, nil, "", nil, 1, 0},
	{16007004, 31, "梦幻钥匙", "很精致的钥匙，似乎能打开某种宝箱。", 2, false, false, 100, 10, 99, false, nil, "", nil, 0, 0},
	{16007005, 31, "神秘的金斧", "平平无奇的金色斧头，似乎是大贤者的信物。", 2, false, false, 100, 10, 99, false, nil, "", nil, 0, 0},
	{16007006, 31, "水晶莲子", "具有魔法力量的水晶莲子，用于在梦幻国度第二宫商店兑换奖励。", 1, false, false, 40, 1, 99999, false, nil, "", nil, 0, 0},
	{16007007, 31, "梦幻蓝晶碎片", "梦幻蓝晶的碎片，用于修复梦幻蓝晶。", 2, false, false, 100, 10, 99, false, nil, "", nil, 1, 0},
	{16007008, 31, "水晶草莓", "具有魔法力量的水晶草莓，用于在梦幻国度第三宫商店兑换奖励。", 1, false, false, 40, 1, 99999, false, nil, "", nil, 0, 0},
	{16007009, 31, "梦幻紫晶碎片", "梦幻紫晶的碎片，用于修复梦幻紫晶。", 2, false, false, 100, 10, 99, false, nil, "", nil, 1, 0},
	{16007010, 31, "水晶白菜", "具有魔法力量的水晶白菜，用于在梦幻国度第四宫商店兑换奖励。", 1, false, false, 40, 1, 99999, false, nil, "", nil, 0, 0},
	{16007011, 31, "梦幻红晶碎片", "梦幻红晶的碎片，用于修复梦幻红晶。", 2, false, false, 100, 10, 99, false, nil, "", nil, 1, 0},
	{16007012, 31, "水晶果实", "具有魔法力量的水晶果实，用于在梦幻国度第五宫商店兑换奖励。", 1, false, false, 40, 1, 99999, false, nil, "", nil, 0, 0},
	{16007013, 31, "梦幻黄晶碎片", "梦幻黄晶的碎片，用于修复梦幻黄晶。", 2, false, false, 100, 10, 99, false, nil, "", nil, 1, 0},
	{16009000, 40, "小耶初级神秘制作", "", 3, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009001, 40, "小耶中级神秘制作", "", 4, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009002, 40, "小耶高级神秘制作", "", 5, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009003, 40, "小耶特级神秘制作", "", 5, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009010, 40, "温蒂初级神秘制作", "", 3, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009011, 40, "温蒂中级神秘制作", "", 4, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009012, 40, "温蒂高级神秘制作", "", 5, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009013, 40, "温蒂特级神秘制作", "", 5, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009020, 40, "潘潘初级神秘制作", "", 3, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009021, 40, "潘潘中级神秘制作", "", 4, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009022, 40, "潘潘高级神秘制作", "", 5, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16009023, 40, "潘潘特级神秘制作", "", 5, false, false, 0, 0, 99, false, nil, "", nil, 0, 0},
	{16010001, 24, "火凤语录", "散落在故事中的语录，拥有跨越时空的力量，可与火凤产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010002, 24, "龙三太子语录", "散落在故事中的语录，拥有跨越时空的力量，可与龙三太子产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010003, 24, "龙妮语录", "散落在故事中的语录，拥有跨越时空的力量，可与龙妮产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010004, 24, "彩衣语录", "散落在故事中的语录，拥有跨越时空的力量，可与彩衣产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010005, 24, "华大夫语录", "散落在故事中的语录，拥有跨越时空的力量，可与华大夫产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010006, 24, "雷欧语录", "散落在故事中的语录，拥有跨越时空的力量，可与雷欧产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010007, 24, "纳多王子语录", "散落在故事中的语录，拥有跨越时空的力量，可与纳多王子产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010008, 24, "白龙长老语录", "散落在故事中的语录，拥有跨越时空的力量，可与白龙长老产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010009, 24, "土狼语录", "散落在故事中的语录，拥有跨越时空的力量，可与土狼产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010010, 24, "金笛语录", "散落在故事中的语录，拥有跨越时空的力量，可与金笛产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010011, 24, "木阳语录", "散落在故事中的语录，拥有跨越时空的力量，可与木阳产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010012, 24, "阿宅语录", "散落在故事中的语录，拥有跨越时空的力量，可与阿宅产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010013, 24, "雷文语录", "散落在故事中的语录，拥有跨越时空的力量，可与雷文产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010014, 24, "缇亚语录", "散落在故事中的语录，拥有跨越时空的力量，可与缇亚产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010015, 24, "卡布语录", "散落在故事中的语录，拥有跨越时空的力量，可与卡布产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010016, 24, "红猪语录", "散落在故事中的语录，拥有跨越时空的力量，可与红猪产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010017, 24, "可比语录", "散落在故事中的语录，拥有跨越时空的力量，可与可比产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010018, 24, "大脚雪怪语录", "散落在故事中的语录，拥有跨越时空的力量，可与大脚雪怪产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010019, 24, "浩天语录", "散落在故事中的语录，拥有跨越时空的力量，可与浩天产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010020, 24, "云阳语录", "散落在故事中的语录，拥有跨越时空的力量，可与云阳产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010021, 24, "天狼语录", "散落在故事中的语录，拥有跨越时空的力量，可与天狼产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010022, 24, "史瓦西语录", "散落在故事中的语录，拥有跨越时空的力量，可与史瓦西产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010023, 24, "莉卡语录", "散落在故事中的语录，拥有跨越时空的力量，可与莉卡产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010501, 24, "孙悟空语录", "散落在故事中的语录，拥有跨越时空的力量，可与孙悟空产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010502, 24, "美乐蒂语录", "散落在故事中的语录，拥有跨越时空的力量，可与美乐蒂产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16010503, 24, "酷洛米语录", "散落在故事中的语录，拥有跨越时空的力量，可与酷洛米产生共鸣。", 5, false, false, 100, 10, 99, false, {offerExp=1}, "", nil, 0, 0},
	{16020001, 27, "浴火成凤", "烈火与凤凰，重生与救赎。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020002, 27, "白焰幻想", "白色火焰，白色的幻想。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020003, 27, "金龙玉鳞", "金龙御天，越云入海。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020004, 27, "墨浪沉沙", "墨浪折戟，锦梦沉沙。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020005, 27, "青伶旧梦", "伶仃海里叹零丁，相思不离旧梦人。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020006, 27, "珊瑚玉珏", "一分为二的羁绊，珊瑚约定的想念。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020007, 27, "珊瑚锦衣", "采一簇珊瑚，挽一缕夕阳。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020008, 27, "海青绾纱", "回荡在深海里的幽思。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020009, 27, "医者仁心", "致妙手回春的华大夫。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020010, 27, "空谷幽兰", "医责放在手上，幽兰系与心中。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020011, 27, "滥觞之勇", "勇气，从奥比远征军团的甲胄伊始。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020012, 27, "星火燎原", "燎原之火，从勇气伊始。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020013, 27, "快乐王子", "致快乐星球王子——纳多。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020014, 27, "忧郁王子", "与快乐相伴的蓝色忧思。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020015, 27, "白龙华装", "承载白龙族命途之坎坷。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020016, 27, "翠玉鳞心", "一颗赤诚的龙心，百余年的期盼。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020017, 27, "童心未泯", "能够直接与快乐沟通的，是童心。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020018, 27, "卡布奇诺", "卡布奇诺的幻想与回味。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020019, 27, "金属狂潮", "宝矿星战力的来源——金属。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020020, 27, "蓝天之忆", "宝矿星的蓝天，在回忆里搁浅。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020021, 27, "一腔孤勇", "一个人的勇敢，守望两个人的温柔。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020022, 27, "郁色忧思", "回忆里的颜色，是忧愁还是欢乐？", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020023, 27, "功夫巨侠", "执仗，走天涯。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020024, 27, "竹织侠梦", "竹编的颜色，竹剑的侠情。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020025, 27, "白夜幻境", "一半的天使，一半的恶魔。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020026, 27, "魔鬼步伐", "自信和高傲，才是魔鬼的姿态。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020027, 27, "灵境皎月", "灵境中的天使，云雾中的明月。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020028, 27, "秘之风语", "幽林中飞舞，倾听风的秘密。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020029, 27, "冲阵先锋", "冲锋在前，是队长的职责。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020030, 27, "草莓诱惑", "谁能拒绝草莓的诱惑。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020031, 27, "糖果超甜", "甜甜的糖果是美美的祝愿。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020032, 27, "果汁气泡", "甜美的果汁气泡撞击你的小心心。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020033, 27, "训练达人", "你也是来训练的吗？", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020034, 27, "暴雪之巅", "暴风雪过后，才知道站在巅峰的人是谁。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020035, 27, "截球高手", "虽然大只，但是灵活。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020036, 27, "雪白派对", "棒球就是雪地里最好的派对主题。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020037, 27, "魂之守护", "守护你，哪怕献祭灵魂。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020038, 27, "白夜残阳", "撕开暗夜的阳光。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020039, 27, "刻骨铭魂", "打破禁忌，与你灵魂共生。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020040, 27, "月夜蔷薇", "朦胧的月色下，是蔷薇的暗香。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020041, 27, "海之船长", "大海的征程，从现在开始。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020042, 27, "骑士舞曲", "骑士的优雅是浪漫的舞曲。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020043, 27, "银铃景", "银色铃铛，自成一景。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020044, 27, "糖果之约", "来一场与糖果的约会吧！", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020045, 27, "孔雀王子", "终归是一人，踏上心中的路。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020046, 27, "冰雪之路", "一颗孤勇之心，破开冰雪之路。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020047, 27, "暗影重重", "我是你，你是谁。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020048, 27, "海蓝潮汐", "浓浓的思念，在潮汐之间。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020049, 27, "绿色童年", "童年的欢乐，翻滚在绿油油的草坪上。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020050, 27, "蒸汽曙光", "前进的动力来自于源源不断的蒸汽。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020051, 27, "山梦幻想", "山间幽径，响起铃铛的清脆。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020052, 27, "魔幻绅士", "忽而消失，忽而出现。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020053, 27, "机械使命", "机械之心的使命，是永恒。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020054, 27, "未来之誓", "不论相隔多少年，仍记那年花开的誓言。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020055, 27, "雪花点点", "抖一抖，散落雪花点点。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020056, 27, "小拳击手", "赛场上的最强拳击手是也。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020057, 27, "黑色执事", "穿着黑色燕尾服，表达最高的敬意。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020058, 27, "糖果镖客", "发射一颗糖果，就能获得一个灿烂笑容。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020059, 27, "神秘探长", "你我的密语，在不经意之间传递。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020060, 27, "天竺行歌", "一路歌唱，一路花开。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020061, 27, "孙行者", "想吃俺老孙的师傅，得先问问俺老孙手里的金箍棒！", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020062, 27, "我心慈悲", "心存慈悲，我即是佛，佛即是我。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020063, 27, "斗战胜佛", "以战胜佛，或是以佛胜战，皆是本心。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020064, 27, "粉色糖心", "一颗糖，就能融化出一片粉色的甜美哦！", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020065, 27, "黑糖蜜语", "甜甜酷酷，是黑糖的专属密语！", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020066, 27, "调查制服", "银河安全局为时空调查官量身打造的制服。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020067, 27, "实验幕后", "曾经的试验品终有一日会成为新的实验操作者。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020068, 27, "锦衣夜行", "跨越时空，执行任务。", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020069, 27, "幻光溢彩", "打造史瓦西的工程师，亲自为她设计的衣服。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020070, 27, "警报时刻", "病毒是甜品，警报声是欢歌，欢迎来到史瓦西的派对。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020071, 27, "赤狐危情", "入侵时空穿梭系统，去古代当一只肆意的狐妖吧？", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020072, 27, "恶龙化身", "三头龙蜕变为人，身上的龙鳞也幻化成她的衣装。", 1, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020073, 27, "甜蜜居家", "窝在幸福的小屋中，沉浸在爱的童话里。", 3, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16020074, 27, "糖心馋龙", "一头扎进大炎国的美食世界里，做一只饱饱的小馋龙吧！", 4, false, false, 0, 880, 99, false, nil, "", nil, 0, 0},
	{16030001, 32, "月卡", "月卡", 4, false, false, 0, 250, 99, false, nil, "", nil, 0, 0},
	{16030002, 32, "季卡", "季卡", 5, false, false, 0, 680, 99, false, nil, "", nil, 0, 0},
	{16030003, 32, "年卡", "年卡", 6, false, false, 0, 1980, 99, false, nil, "", nil, 0, 0},
	{16030004, 33, "月卡（三人成行）", "获赠月卡对应的金额，将不累计至愿望值中", 4, false, false, 0, 250, 99, false, {rewardId=16030001}, "", nil, 0, 0},
	{16030005, 33, "季卡（三人成行）", "获赠季卡对应的金额，将不累计至愿望值中", 5, false, false, 0, 680, 99, false, {rewardId=16030002}, "", nil, 0, 0},
	{16030006, 33, "年卡（三人成行）", "获赠年卡对应的金额，将不累计至愿望值中", 6, false, false, 0, 1980, 99, false, {rewardId=16030003}, "", nil, 0, 0},
	{16030007, 32, "1天体验卡", "自用体验卡，体验1天红宝石特权。", 1, false, false, 0, 8, 99, false, nil, "", nil, 0, 0},
	{16030008, 32, "3天体验卡", "自用体验卡，体验3天红宝石特权。", 2, false, false, 0, 8, 99, false, nil, "", nil, 0, 0},
	{16030009, 47, "1天体验卡（赠送）", "送礼体验卡，让好友也体验1天红宝石的感觉。", 1, false, false, 0, 8, 99, false, {rewardId=16030007}, "", nil, 0, 0},
	{16030010, 47, "3天体验卡（赠送）", "送礼体验卡，让好友也体验3天红宝石的感觉。", 2, false, false, 0, 8, 99, false, {rewardId=16030008}, "", nil, 0, 0},
	{16030011, 37, "精致生活解锁卡", "可以体验奥比精致生活。", 5, false, false, 0, 680, 99, false, nil, "", nil, 0, 0},
	{16030012, 37, "豪华礼包解锁卡", "可以体验奥比豪华生活。", 6, false, false, 0, 980, 99, false, nil, "", nil, 0, 0},
	{16030013, 37, "豪华礼包升级卡", "可以从精致生活升级到豪华生活。", 4, false, false, 0, 300, 99, false, nil, "", nil, 0, 0},
	{16030014, 38, "精致生活（赠送）", "用于赠送，让好友也体验奥比精致生活的感觉。", 5, false, false, 0, 680, 99, false, {rewardId=16030011}, "", nil, 0, 0},
	{16030015, 38, "豪华礼包（赠送）", "用于赠送，让好友也获得豪华生活体验的感觉。", 6, false, false, 0, 980, 99, false, {rewardId=16030012}, "", nil, 0, 0},
	{16030016, 38, "豪华升级（赠送）", "用于赠送，让好友从精致生活升级到豪华生活体验的感觉。", 4, false, false, 0, 300, 99, false, {rewardId=16030013}, "", nil, 0, 0},
	{16030017, 47, "时空之钥（赠礼）", "用于赠送，让好友获得1个时空之钥。", 4, false, false, 0, 60, 99, false, {rewardId=16000212}, "", nil, 0, 0},
	{16030018, 47, "服装彩蛋锤（赠礼）", "用于赠送，让好友获得1个晶钻服装彩蛋锤。", 4, false, false, 0, 60, 99, false, {rewardId=16000006}, "", nil, 0, 0},
	{16030019, 47, "家具彩蛋锤（赠礼）", "用于赠送，让好友获得1个晶钻家具彩蛋锤。", 4, false, false, 0, 40, 99, false, {rewardId=16000054}, "", nil, 0, 0},
	{16030020, 47, "传记借阅证（赠礼）", "用于赠送，让好友获得1个传记借阅证。", 4, false, false, 0, 80, 99, false, {rewardId=16000088}, "", nil, 0, 0},
	{16030021, 47, "高级宠物笛（赠礼）", "用于赠送，让好友获得1个高级宠物笛。", 4, false, false, 0, 50, 99, false, {rewardId=16000033}, "", nil, 0, 0},
	{16030022, 3, "十连主题礼包", "好友赠与给您的礼包，内含10个时空之钥。", 4, false, false, 0, 60, 99, false, {type=1,rewards={137}}, "", nil, 0, 0},
	{16030023, 3, "十连装扮礼包", "好友赠与给您的礼包，内含10个晶钻服装彩蛋锤。", 4, false, false, 0, 60, 99, false, {type=1,rewards={138}}, "", nil, 0, 0},
	{16030024, 3, "十连家具礼包", "好友赠与给您的礼包，内含10个晶钻家具彩蛋锤。", 4, false, false, 0, 40, 99, false, {type=1,rewards={139}}, "", nil, 0, 0},
	{16030025, 3, "十连旅者礼包", "好友赠与给您的礼包，内含10个传记借阅证。", 4, false, false, 0, 80, 99, false, {type=1,rewards={140}}, "", nil, 0, 0},
	{16030026, 3, "十连宠物礼包", "好友赠与给您的礼包，内含10个高级宠物笛。", 4, false, false, 0, 50, 99, false, {type=1,rewards={141}}, "", nil, 0, 0},
	{16030027, 47, "十连主题礼包（赠礼）", "用于赠送，让好友获得1个十连主题礼包，内含10个时空之钥。", 4, false, false, 0, 60, 99, false, {rewardId=16030022}, "", nil, 0, 0},
	{16030028, 47, "十连装扮礼包（赠礼）", "用于赠送，让好友获得十连装扮礼包，内含10个晶钻服装彩蛋锤。", 4, false, false, 0, 60, 99, false, {rewardId=16030023}, "", nil, 0, 0},
	{16030029, 47, "十连家具礼包（赠礼）", "用于赠送，让好友获得十连家具礼包，内含10个晶钻家具彩蛋锤。", 4, false, false, 0, 40, 99, false, {rewardId=16030024}, "", nil, 0, 0},
	{16030030, 47, "十连旅者礼包（赠礼）", "用于赠送，让好友获得十连旅者礼包，内含10个传记借阅证。", 4, false, false, 0, 80, 99, false, {rewardId=16030025}, "", nil, 0, 0},
	{16030031, 47, "十连宠物礼包（赠礼）", "用于赠送，让好友获得十连宠物礼包，内含10个高级宠物笛。", 4, false, false, 0, 50, 99, false, {rewardId=16030026}, "", nil, 0, 0},
	{16030032, 47, "月卡（赠礼）", "获赠月卡对应的金额，将不累计至愿望值中", 4, false, false, 0, 250, 99, false, {rewardId=16030001}, "", nil, 0, 0},
	{16030033, 47, "季卡（赠礼）", "获赠季卡对应的金额，将不累计至愿望值中", 5, false, false, 0, 680, 99, false, {rewardId=16030002}, "", nil, 0, 0},
	{16030034, 47, "年卡（赠礼）", "获赠年卡对应的金额，将不累计至愿望值中", 6, false, false, 0, 1980, 99, false, {rewardId=16030003}, "", nil, 0, 0},
	{16030035, 47, "精致生活（赠礼）", "用于赠送，让好友也体验奥比精致生活的感觉。", 5, false, false, 0, 680, 99, false, {rewardId=16030011}, "", nil, 0, 0},
	{16030036, 47, "豪华礼包（赠礼）", "用于赠送，让好友也获得豪华生活体验的感觉。", 6, false, false, 0, 980, 99, false, {rewardId=16030012}, "", nil, 0, 0},
	{16030037, 47, "豪华升级（赠礼）", "用于赠送，让好友从精致生活升级到豪华生活体验的感觉。", 4, false, false, 0, 300, 99, false, {rewardId=16030013}, "", nil, 0, 0},
	{16030038, 32, "5天体验卡", "自用体验卡，体验5天红宝石特权。", 2, false, false, 0, 2.7, 99, false, nil, "", nil, 0, 0},
	{16030039, 32, "10天体验卡", "自用体验卡，体验10天红宝石特权。", 3, false, false, 0, 5.4, 99, false, nil, "", nil, 0, 0},
	{16030040, 32, "100天体验卡", "自用体验卡，体验100天红宝石特权。", 4, false, false, 0, 54, 99, false, nil, "", nil, 0, 0},
	{16030041, 32, "500天体验卡", "自用体验卡，体验500天红宝石特权。", 5, false, false, 0, 270, 99, false, nil, "", nil, 0, 0},
	{16030042, 32, "7天体验卡", "自用体验卡，体验7天红宝石特权。", 2, false, false, 0, 3.78, 99, false, nil, "", nil, 0, 16030038},
	{16040001, 35, "瑞雪鸟", "瑞雪降临的时候，常有这种小鸟在枝头歌唱。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040002, 35, "蓝背金颈雀", "一种很古老的雀，传说大贤者曾经饲养了一只。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040003, 35, "方糖蚂蚁", "只喜欢方糖，不喜欢圆糖球。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040004, 35, "土拨鼠", "又被称作“森林土坑兽”，擅长挖洞。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040005, 35, "北长尾山雀", "喜欢啾啾叫的胖毛球鸟，大家平日里喜欢叫它肥啾。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040006, 35, "呱呱蛙", "呱呱，呱呱呱，呱呱呱呱。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040007, 35, "红宝石瓢虫", "在阳光下如同红宝石一样闪烁的瓢虫，是小孩子们很喜欢的昆虫。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040008, 35, "千里蚂蚱", "传说可以一蹦千里的蚂蚱，这身绝技只有在星月同线的时候才会展露。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040009, 35, "蓝缎蜂鸟", "全身如同蓝色绸缎一样顺滑的蜂鸟，是森林中非常活跃的小精灵。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040010, 39, "雷文写真", "雷文亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211101}, "", nil, 1, 0},
	{16040011, 39, "缇亚写真", "缇亚亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211102}, "", nil, 1, 0},
	{16040012, 39, "可比写真", "可比亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211103}, "", nil, 1, 0},
	{16040013, 39, "大脚雪怪写真", "大脚雪怪亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211104}, "", nil, 1, 0},
	{16040014, 39, "卡布写真", "卡布亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211105}, "", nil, 1, 0},
	{16040015, 39, "红猪写真", "红猪亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211106}, "", nil, 1, 0},
	{16040016, 39, "索隆•杰德写真", "索隆•杰德亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211107}, "", nil, 1, 0},
	{16040017, 39, "迦尔•杰德写真", "迦尔•杰德亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211108}, "", nil, 1, 0},
	{16040018, 39, "黑元素师写真", "黑元素师亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211109}, "", nil, 1, 0},
	{16040019, 39, "艾丽斯•娜娜写真", "艾丽斯•娜娜亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211110}, "", nil, 1, 0},
	{16040020, 39, "迦文写真", "迦文亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211111}, "", nil, 1, 0},
	{16040021, 39, "刺刺写真", "刺刺亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211112}, "", nil, 1, 0},
	{16040022, 39, "妮法写真", "妮法亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211113}, "", nil, 1, 0},
	{16040023, 39, "奥泥尔写真", "奥泥尔亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211114}, "", nil, 1, 0},
	{16040024, 39, "豆豆写真", "豆豆亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211115}, "", nil, 1, 0},
	{16040025, 39, "特伊•杰德写真", "特伊•杰德亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211116}, "", nil, 1, 0},
	{16040026, 39, "5202•贝尔写真", "5202•贝尔亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211117}, "", nil, 1, 0},
	{16040027, 39, "达尔文•舒塔写真", "达尔文•舒塔亲自盖章的写真明信片。", 1, false, false, 0, 0, 99, false, {rewardId=69,actId=152,photoAlbumGroup=211118}, "", nil, 1, 0},
	{16040028, 58, "企鹅梦之队", "在某个特定的时节，你可能会在雪山上发现一群可爱的小企鹅，它们嘿咻嘿咻排好队，第一只企鹅跳冰块，第二只企鹅跳……扑通扑通水花溅。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040029, 58, "松鼠采摘队", "在某个特定的时节，你可能会在青木森林中发现一群可爱的小松鼠，忙忙碌碌采摘队，嘿哟嘿哟摘果子，调皮伙伴拿果果，小腿短短跑得快。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040030, 58, "松鼠运输队", "在某个特定的时节，你可能会在青木森林中发现一群可爱的小松鼠，井然有序运苹果，调皮伙伴有点困，偷偷瞌睡被发现。", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040031, 58, "鹦鹉求偶记I", "在某个特定的时节，你可能会在奥比斯山脚发现一只正在花丛中歌唱的白鹦鹉，但它似乎总是铩羽而归……", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040032, 58, "鹦鹉求偶记II", "在某个特定的时节，你可能会在奥比斯山脚发现一只正在花丛中歌唱的白鹦鹉，但它似乎又铩羽而归了……", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040033, 58, "海边历险", "在某个特定的时节，你可能会在奥比广场码头边发现三只谈兴正浓的海鸥，但它们之中有一只似乎发生了什么意外……", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040034, 58, "鹦鹉求偶记III", "在某个特定的时节，你可能会在奥比斯山脚发现一只正在花丛中歌唱的白鹦鹉，正所谓一鼓作气，再而衰，三而竭……", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040035, 58, "鹦鹉求偶记V", "在某个特定的时节，你可能会在奥比斯山脚发现一只垂头丧气的鹦鹉，但它似乎获得了幸运之神的青睐……", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040036, 58, "甜蜜诱惑", "在某个特定的时节，你可能会在奥比斯山脚发现两只鬼鬼祟祟的獾，为了一罐蜂蜜的所有权，它们似乎产生了一点争执……", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040037, 58, "甜蜜冒险", "在某个特定的时节，你可能会在青木森林的某棵树下发现一只鬼鬼祟祟的獾，为了能吃到最新鲜的蜂蜜，它似乎下了一个很大的决心……", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16040038, 58, "宝宝巴士", "在某个特定的时节，你可能会在青木森林发现妈妈熊带着宝宝熊在散步，但宝宝熊看上去更想搭乘“宝宝巴士”游览森林风光", 0, false, false, 0, 0, 0, false, nil, "", nil, 0, 0},
	{16050001, 46, "红包雨·AR", "红包雨的AR效果，可在拍照中使用。", 2, false, false, 0, 0, 99, false, nil, "", nil, 1, 0},
	{16050002, 46, "元宝雨·AR", "元宝雨的AR效果，可在拍照中使用。", 2, false, false, 0, 0, 99, false, nil, "", nil, 1, 0},
	{16060101, 48, "发射光波（展示）", "星乐咖专属舞蹈道具，初始展示动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060102, 48, "俯身跳跃（展示）", "星乐咖专属舞蹈道具，获得后增加可选择的展示动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060103, 48, "展翅飞翔（展示）", "星乐咖专属舞蹈道具，获得后增加可选择的展示动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060104, 48, "车轮手势（展示）", "星乐咖专属舞蹈道具，获得后增加可选择的展示动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060105, 48, "拔剑指天（展示）", "星乐咖专属舞蹈道具，获得后增加可选择的展示动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060106, 48, "乘风破浪（展示）", "星乐咖专属舞蹈道具，获得后增加可选择的展示动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060201, 48, "挥手摇晃（结尾）", "星乐咖专属舞蹈道具，初始结尾动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060202, 48, "海草摇晃（结尾）", "星乐咖专属舞蹈道具，获得后增加可选择的结尾动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060203, 48, "笑容摇晃（结尾）", "星乐咖专属舞蹈道具，获得后增加可选择的结尾动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060204, 48, "眨眼飞吻（结尾）", "星乐咖专属舞蹈道具，获得后增加可选择的结尾动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060205, 48, "波浪起舞（结尾）", "星乐咖专属舞蹈道具，获得后增加可选择的结尾动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16060206, 48, "欢乐蹦跳（结尾）", "星乐咖专属舞蹈道具，获得后增加可选择的结尾动作。", 2, false, false, 0, 0, 1, false, nil, "", nil, 1, 0},
	{16070100, 49, "奥比手风琴", "奥比的表情仿佛伴随手风琴的展开露出了笑脸。", 4, false, false, 0, 0, 1, false, {instrumentId=8,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070101, 50, "手风琴•森林乐队", "小鸟带来祝福的花环，和松鼠鼓乐队一起欢乐演奏。", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=8,noNightFactor=true,effectUrl="ui/playingmusic/shoufengqin/prefab/accordion_playing_effect_001.prefab",animName="yzyq_1.11_shoufengqin_teshu2"}, "", nil, 1, 0},
	{16070102, 50, "手风琴•庆祝彩带", "嘿嘿！今天是值得庆贺的一天！", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=8,noNightFactor=true,effectUrl="ui/playingmusic/shoufengqin/prefab/accordion_playing_effect_02.prefab",animName="yzyq_1.11_shoufengqin_teshu1"}, "", nil, 1, 0},
	{16070200, 49, "竖琴", "古老的拨弦乐器，仿佛听到了公元四千年前古波斯的声音。", 3, false, false, 0, 0, 1, false, {instrumentId=1,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070300, 49, "奥比麦克风", "奥比~从麦克风传递出奥比的声音。", 4, false, false, 0, 0, 1, false, {instrumentId=9,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070301, 50, "麦克风•万花硬糖", "水果硬糖，这是糖果的主题曲！", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=9,noNightFactor=true,animName="yzyq_2.0_maikefeng_teshu2"}, "", nil, 1, 0},
	{16070302, 50, "麦克风•欢乐彩球", "来吧，和奥比一起欢唱吧！", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=9,noNightFactor=true,animName="yzyq_2.0_maikefeng_teshu1"}, "", nil, 1, 0},
	{16070400, 49, "奥比电吉他", "拥着它，在旋律中，仿佛与它共舞。", 4, false, false, 0, 0, 1, false, {instrumentId=10,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070401, 50, "电吉他•六零年代", "别沮丧，找一首哀伤的歌把它唱得更快乐。", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=10,noNightFactor=true,animName="2.1_yzyq_liulingniandai"}, "", nil, 1, 0},
	{16070402, 50, "电吉他•未来之门", "那会是你所期待的未来吗，不管如何，未来将至。", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=10,noNightFactor=true,effectUrl="ui/playingmusic/jita/prefab/act_1670402_effect_01.prefab",animName="2.1_yzyq_weilaizhimen"}, "", nil, 1, 0},
	{16070500, 49, "奥比小提琴", "琴弓上跳跃着符号，琴弦上洒满了月光。", 4, false, false, 0, 0, 1, false, {instrumentId=11,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070501, 50, "小提琴•幽幽月见", "小提琴独特的演奏曲目，让月见花在月光下尽情绽放。", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=11,noNightFactor=true,effectUrl="ui/playingmusic/aobixiaotiqin/prefab/effect_1670501.prefab",animName="2.3_yzyq_xiaotiqin_teshu2"}, "", nil, 1, 0},
	{16070502, 50, "小提琴•月灵心弦", "旋律飘进了月灵的心里，它们也开始欢快舞动！", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=11,noNightFactor=true,effectUrl="ui/playingmusic/aobixiaotiqin/prefab/effect_1670502.prefab",animName="2.3_yzyq_xiaotiqin_teshu1"}, "", nil, 1, 0},
	{16070600, 49, "奥比大竖琴", "那些被封印在寒冰中的秘密，此刻化作音符倾泻而出。", 4, false, false, 0, 0, 1, false, {instrumentId=12,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070601, 50, "大竖琴•星雪之歌", "随着冬日的旋律响起，雪落悠悠，星光闪烁。", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=12,noNightFactor=true,effectUrl="ui/playingmusic/aobishuqin/prefab/act_16070601_effect_01.prefab",animName="2.5_yzyq_shuqin_teshu2"}, "", nil, 1, 0},
	{16070602, 50, "大竖琴•欢乐雪人", "可爱的冬日乐曲，让小雪人情不自禁地欢乐起舞。", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=12,noNightFactor=true,animName="2.5_yzyq_shuqin_teshu1"}, "", nil, 1, 0},
	{16070700, 49, "奥比玉笛", "风为诗，笛为歌，字字句句迎喜来。", 4, false, false, 0, 0, 1, false, {instrumentId=13,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070701, 50, "玉笛·金竹高升", "金竹抖落千层喜，笛声阵阵贺高升。", 5, false, false, 0, 268, 1, false, {soundId=23,instrumentId=13,noNightFactor=true,effectUrl="ui/playingmusic/dizi/act_16070701_effect_01.prefab",animName="2.7_yzyq_dizi_teshu2"}, "", nil, 1, 0},
	{16070702, 50, "玉笛·纸燕报春", "笛声起，纸燕飞，幽幽春风惹人醉。", 4, false, false, 0, 148, 1, false, {soundId=24,instrumentId=13,noNightFactor=true,effectUrl="ui/playingmusic/dizi/act_16070702_effect_01.prefab",animName="2.7_yzyq_dizi_teshu1"}, "", nil, 1, 0},
	{16070703, 50, "玉笛·鱼龙绽光", "玉笛横吹，清音悠扬，鱼龙绽光，如诗如画人陶醉。", 6, false, false, 0, 648, 1, false, {soundId=25,canMove=true,instrumentId=13,noNightFactor=true,effectUrl="ui/playingmusic/dizi/act_16070703_effect_01.prefab",animName="2.7_yzyq_dizi_teshu3"}, "", nil, 1, 0},
	{16070800, 49, "奥比萨克斯", "美妙的音符从萨克斯里跃出，轻轻落入摇晃的高脚杯中。", 4, false, false, 0, 0, 1, false, {instrumentId=14,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070801, 50, "萨克斯•城市漫步", "在萨克斯浪漫的曲调中，都市生活的璀璨之夜拉开了序幕。", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=14,noNightFactor=true,effectUrl="ui/playingmusic/sakesi/act_16070801_effect_01.prefab",animName="2.9_yzyq_sakesi_teshu2"}, "", nil, 1, 0},
	{16070802, 50, "萨克斯•薯条盛宴", "会喷出音乐和薯条的奇妙萨克斯，是海鸥们心中的宝物。", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=14,noNightFactor=true,animName="2.9_yzyq_sakesi_teshu1"}, "", nil, 1, 0},
	{16070900, 49, "奥比星海螺", "海螺声随着海浪轻轻荡开，把美好传递到每个角落。", 4, false, false, 0, 0, 1, false, {instrumentId=15,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16070901, 50, "星海螺·深海邂逅", "珊瑚轻舞，轻歌悠扬，跟音乐来一场唯美的深海邂逅吧！", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=15,noNightFactor=true,effectUrl="ui/playingmusic/xiaohailuo/act_16070901_effect_01.prefab",animName="3.0_yzyq_xiaohailuo_teshu2"}, "", nil, 1, 0},
	{16070902, 50, "星海螺·夏日泡泡", "夏日恋歌，泡泡带着神秘的旋律，进入了谁的梦中？", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=15,noNightFactor=true,animName="3.0_yzyq_xiaohailuo_teshu1"}, "", nil, 1, 0},
	{16070903, 50, "星海螺·幻海瑰梦", "他们在深海初见的那一刻，游鱼起舞，幻海花开。", 6, false, false, 0, 648, 1, false, {soundId=25,canMove=true,instrumentId=15,noNightFactor=true,effectUrl="ui/playingmusic/xiaohailuo/act_16070903_effect_01.prefab",animName="3.0_yzyq_xiaohailuo_teshu3"}, "", nil, 1, 0},
	{16071000, 49, "奥比八音盒", "诡异的旋律响起，把哀伤和藏在八音盒里的秘密，一同在黑夜中慢慢诉说。", 4, false, false, 0, 0, 1, false, {instrumentId=16,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16071001, 50, "八音盒•人偶庄园", "看，今晚月光真美，蔷薇花也再次绽放，现在，轮到你陪我跳完这支夜之圆舞曲了哦！", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=16,noNightFactor=true,effectUrl="ui/playingmusic/bayinhe/act_16071001_effect_01.prefab",animName="3.4_yzyq_bayinhe_teshu2"}, "", nil, 1, 0},
	{16071002, 50, "八音盒•黑鸦悲歌", "奇怪，有这么多黑鸦陪我，为什么我还会寂寞，还会……落泪呢？", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=16,noNightFactor=true,animName="3.4_yzyq_bayinhe_teshu1"}, "", nil, 1, 0},
	{16071100, 49, "奥比电钢琴", "当指尖跳跃在琴键上，所有的烦恼都随着音符飘散。", 4, false, false, 0, 0, 1, false, {instrumentId=17,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16071101, 50, "电钢琴•云梦星河", "谁用梦幻的旋律，为你编织了一场铺满星光的梦？", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=17,noNightFactor=true,effectUrl="ui/playingmusic/gangqin/act_16071101_effect_01.prefab",animName="3.6_yzyq_gangqin_teshu2"}, "", nil, 1, 0},
	{16071102, 50, "电钢琴•温柔之音", "有情绪小怪兽的温柔陪伴，弹奏的每一个音符都不再孤单。", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=17,noNightFactor=true,animName="3.6_yzyq_gangqin_teshu1"}, "", nil, 1, 0},
	{16071200, 49, "奥比阮琴", "琴弦轻拨，音韵绕梁，一曲毕，余味绵长。", 4, false, false, 0, 0, 1, false, {instrumentId=18,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16071201, 50, "阮琴·寒梅听雪", "梅觉雪来意，雪知寒中香，一粉一白一浪漫。", 5, false, false, 0, 268, 1, false, {soundId=24,instrumentId=18,noNightFactor=true,effectUrl="ui/playingmusic/ruanqin/act_16071201_effect_01.prefab",animName="3.7_yzyq_ruanqin_teshu2"}, "", nil, 1, 0},
	{16071202, 50, "阮琴·古韵潮灯", "摇摆起来，灯牌闪烁，照亮我的致富之路！", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=18,noNightFactor=true,animName="3.7_yzyq_ruanqin_teshu1"}, "", nil, 1, 0},
	{16071203, 50, "阮琴·江山如画", "霞光缱绻，香烟袅袅，画卷轻展，是万里山河盛世。", 6, false, false, 0, 648, 1, false, {soundId=25,canMove=true,instrumentId=18,noNightFactor=true,effectUrl="ui/playingmusic/ruanqin/act_16071203_effect_01.prefab",animName="3.7_yzyq_ruanqin_teshu3"}, "", nil, 1, 0},
	{16071300, 49, "奥比马林巴琴", "轻轻敲击，音符就在马林巴琴上跳跃，跳出幸福和快乐！", 4, false, false, 0, 0, 1, false, {instrumentId=19,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16071301, 50, "马林巴琴·春画", "春光如画，一池莲叶，一池幽梦，阵阵蝶香，引暖意。", 5, false, false, 0, 268, 1, false, {soundId=24,offsetY=2.1,instrumentId=19,noNightFactor=true,effectUrl="ui/playingmusic/malinbaqin/act_16071301_effect_01.prefab",animName="3.8_yzyq_malinbaqin_teshu2"}, "", nil, 1, 0},
	{16071302, 50, "马林巴琴·睡莲", "琴声起，睡莲伴随着和煦的风，盛开出一片清雅和宁静。", 4, false, false, 0, 148, 1, false, {soundId=23,offsetY=1.9,instrumentId=19,noNightFactor=true,effectUrl="ui/playingmusic/malinbaqin/act_16071302_effect_01.prefab",animName="3.8_yzyq_malinbaqin_teshu1"}, "", nil, 1, 0},
	{16071400, 49, "奥比卡林巴", "用轻灵的音乐，将那些埋藏在心底的秘密……慢慢诉说。", 4, false, false, 0, 0, 1, false, {instrumentId=20,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16071401, 50, "卡林巴·蝶湖幻歌", "星星坠入蝶湖，蝴蝶随风飞舞，你从迷雾中来，奏出一曲静谧之歌。", 5, false, false, 0, 268, 1, false, {soundId=24,offsetY=2.1,instrumentId=20,noNightFactor=true,effectUrl="ui/playingmusic/kalinba/act_16071401_effect_01.prefab",animName="3.9_yzyq_kalinba_teshu2"}, "", nil, 1, 0},
	{16071402, 50, "卡林巴·繁叶之星", "在最美的时光里，在最美的音乐里，两颗星星跳的这支舞，只属于你哟！", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=20,noNightFactor=true,animName="3.9_yzyq_kalinba_teshu1"}, "", nil, 1, 0},
	{16071403, 50, "卡林巴·月光梦境", "一段旋律，唤醒彩云，点亮星光，与月亮共同编织出一个最浪漫、最美的梦境。", 6, false, false, 0, 648, 1, false, {soundId=25,canMove=true,offsetY=2.1,instrumentId=20,noNightFactor=true,effectUrl="ui/playingmusic/kalinba/act_16071403_effect_01.prefab",animName="3.9_yzyq_kalinba_teshu3"}, "", nil, 1, 0},
	{16071500, 49, "奥比次元琴", "指尖在琴键上跳跃，音乐是灵魂的呐喊。", 4, false, false, 0, 0, 1, false, {instrumentId=21,size2d=3.75,size3d=16.3}, "", nil, 1, 0},
	{16071501, 50, "次元琴·青春舞曲", "让青春化作指尖流淌的即兴旋律，在未来的每个章节里，都能鼓舞着我们勇敢前进。", 5, false, false, 0, 268, 1, false, {soundId=24,offsetY=2.1,instrumentId=21,noNightFactor=true,effectUrl="ui/playingmusic/ciyuanqin/act_16071501_effect_01.prefab",animName="4.0_yzyq_ciyuanqin_teshu2"}, "", nil, 1, 0},
	{16071502, 50, "次元琴·童梦风车", "嗨起来，把勇气融进音乐里，化成风，吹动梦想的风车吧！", 4, false, false, 0, 148, 1, false, {soundId=23,instrumentId=21,noNightFactor=true,animName="4.0_yzyq_ciyuanqin_teshu1"}, "", nil, 1, 0},
	{16071503, 50, "次元琴·奇幻旋律", "彩虹从屏幕里溢出来，气球牵着旋律向上飘，星星随着节拍闪烁，童话在这一刻来到现实。", 6, false, false, 0, 648, 1, false, {soundId=25,canMove=true,offsetY=2.5,instrumentId=21,noNightFactor=true,effectUrl="ui/playingmusic/ciyuanqin/act_16071503_effect_01.prefab",animName="4.0_yzyq_ciyuanqin_teshu3"}, "", nil, 1, 0},
	{16080001, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, {rewardId=69,actId=2175}, "", nil, 0, 16080001},
	{16080002, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, {rewardId=69,actId=2274}, "", nil, 0, 16080001},
	{16080003, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, {rewardId=69,actId=2350}, "", nil, 0, 16080001},
	{16080004, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, {rewardId=69,actId=2390}, "", nil, 0, 16080001},
	{16080005, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, {rewardId=69,actId=2417}, "", nil, 0, 16080001},
	{16080006, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, {rewardId=69,actId=2431}, "", nil, 0, 16080001},
	{16080007, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, {rewardId=69,actId=2487}, "", nil, 0, 16080001},
	{16080008, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080009, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080010, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080011, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080012, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080013, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080014, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080015, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080016, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080017, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080018, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080019, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16080020, 7, "时空之钥·限时", "时光锤炼宝石，凝聚成钥匙，可以开启神奇的宝藏。(活动结束后将自动转换为100金币)", 4, false, false, 100, 60, 999, false, nil, "", nil, 0, 16080001},
	{16081001, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2253}, "", nil, 0, 16000485},
	{16081002, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2301}, "", nil, 0, 16000485},
	{16081003, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2323}, "", nil, 0, 16000485},
	{16081004, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2360}, "", nil, 0, 16000485},
	{16081005, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2402}, "", nil, 0, 16000485},
	{16081006, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, {rewardId=35,actId=2433}, "", nil, 0, 16000485},
	{16081007, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081008, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081009, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081010, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081011, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081012, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081013, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081014, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081015, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081016, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081017, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081018, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081019, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16081020, 10, "博览门票", "拿到博览门票，就能在经典博览会上买到心仪之物。", 4, false, false, 0, 3, 9999, false, nil, "", nil, 0, 16000485},
	{16090001, 51, "红小豆的日常", "“红小豆的日常”表情包系列", 3, false, false, 0, 88, 1, false, nil, "", nil, 1, 0},
	{16090002, 51, "红小豆与奥比", "“红小豆与奥比”表情包系列", 4, false, false, 0, 128, 1, false, nil, "", nil, 1, 0},
	{16090003, 51, "囡茜表情包", "“囡茜”表情包系列", 3, false, false, 0, 88, 1, false, nil, "", nil, 1, 0},
	{16090004, 51, "囡茜与奥比", "“囡茜与奥比”表情包系列", 4, false, false, 0, 128, 1, false, nil, "", nil, 1, 0},
	{16090005, 51, "萌二与奥比", "“萌二与奥比”表情包系列", 3, false, false, 0, 88, 1, false, nil, "", nil, 1, 0},
	{16090006, 51, "醒神仔与奥比", "“醒神仔与奥比”表情包系列", 3, false, false, 0, 88, 1, false, nil, "", nil, 1, 0},
	{16090007, 51, "小黄豚与奥比", "“小黄豚与奥比”表情包系列", 3, false, false, 0, 88, 1, false, nil, "", nil, 1, 0},
	{16090008, 51, "nono狗的日常", "“nono狗的日常”表情包系列", 4, false, false, 0, 128, 1, false, nil, "", nil, 1, 0},
	{16090009, 51, "萌动nono狗", "“萌动nono狗”表情包系列", 4, false, false, 0, 128, 1, false, nil, "", nil, 1, 0},
	{16091001, 52, "沙尘天气氛围球", "装满沙尘的神秘晶球，使用后可增加1小时的小岛沙尘天气氛围使用期限（可叠加）。", 5, true, false, 12000, 30, 9999, false, {activeTime=3600,furnitureId=13003289}, "", nil, 0, 0},
	{16091002, 52, "高温天气氛围球", "饱含温度的神秘晶球，使用后可增加1小时的小岛高温天气氛围使用期限（可叠加）。", 5, true, false, 12000, 30, 9999, false, {activeTime=3600,furnitureId=13003290}, "", nil, 0, 0},
	{16091003, 52, "暴雪天气氛围球", "雪花弥漫的神秘晶球，使用后可增加1小时的小岛暴雪天气氛围使用期限（可叠加）。", 5, true, false, 12000, 30, 9999, false, {activeTime=3600,furnitureId=13003291}, "", nil, 0, 0},
	{16091004, 52, "暴雨天气氛围球", "注入暴雨的神秘晶球，使用后可增加1小时的小岛暴雨天气氛围使用期限（可叠加）。", 5, true, false, 12000, 30, 9999, false, {activeTime=3600,furnitureId=13003292}, "", nil, 0, 0},
	{16091005, 52, "花叶飞舞氛围球", "注满花叶的神秘晶球，使用后可增加1小时的小岛花叶飞舞氛围使用期限（可叠加）。", 5, true, false, 12000, 30, 9999, false, {activeTime=3600,furnitureId=13003632}, "", nil, 0, 0},
	{16100011, 53, "心动应急箱", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100012, 53, "嗜爱如命", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100013, 53, "危险关系", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100014, 53, "甜蜜陷阱", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100015, 53, "吊打秋千", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100016, 53, "火炉贴贴", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100017, 53, "你最珍贵", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100018, 53, "熊熊频道", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100019, 53, "蛋糕杀手", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100021, 53, "奶油蛋糕", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100022, 53, "巧克力蛋糕", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100023, 53, "曲奇饼干", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100024, 53, "生日贺卡", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100025, 53, "小熊糖果", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100026, 53, "生日蜡烛", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100027, 53, "冰淇淋蛋糕", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100028, 53, "双重礼物", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100029, 53, "小熊奶茶", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100031, 53, "童话助眠师", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100032, 53, "客房管家", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100033, 53, "门童熊", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100034, 53, "闹钟破坏者", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100035, 53, "电话破坏者", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100036, 53, "酒店经理", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100037, 53, "梦游旅客", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100038, 53, "请勿打扰", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100039, 53, "行李箱守护者", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100041, 53, "爱与死亡", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100042, 53, "燃烧", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100043, 53, "消融", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100044, 53, "爱情灵药", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100045, 53, "情圣", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100046, 53, "护士", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100047, 53, "医生", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100048, 53, "单向的爱", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100049, 53, "心碎", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100051, 53, "失踪的彩球", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100052, 53, "委屈的雪熊", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100053, 53, "圣诞惊喜", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100054, 53, "危险卡路里", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100055, 53, "圣诞破坏王", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100056, 53, "冒失精灵", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100057, 53, "贪吃驯鹿", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100058, 53, "偷袭圣诞树", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100059, 53, "冒牌圣诞老人", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100061, 53, "梦境之地", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=3}, "", nil, 1, 0},
	{16100062, 53, "美梦驿站", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=3}, "", nil, 1, 0},
	{16100063, 53, "向左熊转", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=3}, "", nil, 1, 0},
	{16100064, 53, "漫步小熊星", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=3}, "", nil, 1, 0},
	{16100065, 53, "捕梦娃娃机", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=3}, "", nil, 1, 0},
	{16100066, 53, "旋转木熊", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=3}, "", nil, 1, 0},
	{16100067, 53, "星星骑士", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=3}, "", nil, 1, 0},
	{16100068, 53, "银河飞车", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=3}, "", nil, 1, 0},
	{16100069, 53, "欢迎光临", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=3}, "", nil, 1, 0},
	{16100071, 53, "永恒停滞", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100072, 53, "给你的信", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100073, 53, "我的盆栽", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100074, 53, "守夜人", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100075, 53, "欧若拉", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100076, 53, "迷糊邦尼", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100077, 53, "星际迷失熊", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100078, 53, "月球橡皮擦", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100079, 53, "我的星星", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100081, 53, "爱情绝缘体", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=2}, "", nil, 1, 0},
	{16100082, 53, "爱情标本", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=2}, "", nil, 1, 0},
	{16100083, 53, "渴望爱", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=2}, "", nil, 1, 0},
	{16100084, 53, "心碎蒸馏瓶", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=2}, "", nil, 1, 0},
	{16100085, 53, "爱的供养", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=2}, "", nil, 1, 0},
	{16100086, 53, "幽灵熊博士", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=2}, "", nil, 1, 0},
	{16100087, 53, "科学怪熊", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=2}, "", nil, 1, 0},
	{16100088, 53, "爱意指数", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=2}, "", nil, 1, 0},
	{16100089, 54, "疑心病", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=2}, "", nil, 1, 0},
	{16100091, 53, "行动派", "幽灵酒店契约卡。", 1, false, false, 10, 30, 9999, false, {back=1}, "", nil, 1, 0},
	{16100092, 53, "蓝色月光", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100093, 53, "月球保镖", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100094, 53, "心动派", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100095, 53, "星星特派员", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100096, 53, "流星狙击手", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100097, 53, "机器兔的告白", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100098, 53, "月球矿工", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100099, 54, "复仇流星", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100101, 53, "超负荷拥抱", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=4}, "", nil, 1, 0},
	{16100102, 53, "困境迷宫", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=4}, "", nil, 1, 0},
	{16100103, 53, "毒菇熊", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=4}, "", nil, 1, 0},
	{16100104, 53, "庄园怪盗", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=4}, "", nil, 1, 0},
	{16100105, 53, "好奇花匠", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=4}, "", nil, 1, 0},
	{16100106, 53, "秘密保险箱", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=4}, "", nil, 1, 0},
	{16100107, 53, "雏菊的答案", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=4}, "", nil, 1, 0},
	{16100108, 54, "秘密拼图", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=4}, "", nil, 1, 0},
	{16100109, 53, "花的告别", "幽灵酒店契约卡。", 5, false, false, 10, 480, 9999, false, {back=4}, "", nil, 1, 0},
	{16100111, 53, "幽灵猎手", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100112, 53, "奶酪陷阱", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100113, 53, "织入我心", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100114, 53, "调皮熨斗", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100115, 53, "喵管家", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100116, 53, "吞没灯泡", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100117, 54, "捉迷藏", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100118, 53, "别离开我", "幽灵酒店契约卡。", 5, false, false, 10, 480, 9999, false, {back=1}, "", nil, 1, 0},
	{16100119, 54, "欢迎回家", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16100121, 53, "放气，别动", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100122, 53, "派对天敌", "幽灵酒店契约卡。", 2, false, false, 10, 60, 9999, false, {back=1}, "", nil, 1, 0},
	{16100123, 53, "非熊勿扰", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100124, 53, "礼物呢", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100125, 53, "无人光临", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100126, 54, "孤独生日", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100127, 54, "生日倒计时", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100128, 53, "别伤我心", "幽灵酒店契约卡。", 5, false, false, 10, 480, 9999, false, {back=1}, "", nil, 1, 0},
	{16100129, 54, "祝我生日快乐", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16100131, 53, "爱不释手", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100132, 53, "嘣嘣小熊", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100133, 53, "兔兔无罪", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100134, 53, "自导自演", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100135, 54, "爱情捕手", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100136, 54, "恶魔法官", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100137, 53, "粉熊警长", "幽灵酒店契约卡。", 5, false, false, 10, 480, 9999, false, {back=1}, "", nil, 1, 0},
	{16100138, 54, "小喵怪盗", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16100139, 54, "银河小偷", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16100141, 53, "来杯能子", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100142, 53, "熊茄巨无霸", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100143, 53, "迷幻蘑菇圈", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100144, 53, "汁汁熊堡", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100145, 54, "调味甜心", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100146, 54, "白日梦甜筒", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100147, 53, "心动辣酱", "幽灵酒店契约卡。", 5, false, false, 10, 480, 9999, false, {back=1}, "", nil, 1, 0},
	{16100148, 54, "恶魔意面", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16100149, 54, "熊熊威特", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16100151, 53, "浪漫出逃", "幽灵酒店契约卡。", 3, false, false, 10, 120, 9999, false, {back=1}, "", nil, 1, 0},
	{16100152, 53, "爱情逃犯", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100153, 53, "等待救援", "幽灵酒店契约卡。", 4, false, false, 10, 240, 9999, false, {back=1}, "", nil, 1, 0},
	{16100154, 54, "小熊闭嘴", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100155, 54, "喵喵拳", "幽灵酒店契约卡。", 4, false, false, 10, 360, 9999, false, {back=1}, "", nil, 1, 0},
	{16100156, 53, "时令奶昔", "幽灵酒店契约卡。", 5, false, false, 10, 480, 9999, false, {back=1}, "", nil, 1, 0},
	{16100157, 53, "魔幻拿铁", "幽灵酒店契约卡。", 5, false, false, 10, 480, 9999, false, {back=1}, "", nil, 1, 0},
	{16100158, 54, "萌熊贴贴", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16100159, 54, "一起跳舞吧", "幽灵酒店契约卡。", 5, false, false, 10, 720, 9999, false, {back=1}, "", nil, 1, 0},
	{16101011, 53, "吹卷哨", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101012, 53, "上菜", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101013, 53, "吹笛子", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101014, 53, "睡觉觉啦", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101015, 53, "加油加油", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101016, 53, "打电话", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101017, 53, "下雨了", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101018, 53, "天鹅舞", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101019, 53, "吃吃睡睡", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101021, 53, "郁金香物语", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101022, 53, "云裳仙子", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101023, 53, "绮梦玫瑰", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101024, 53, "缤纷花环", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101025, 53, "自由圆舞曲", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101026, 53, "铃兰幽香", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101027, 53, "唯爱朝颜", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101028, 53, "漫卷流光", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101029, 53, "满园花色", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101031, 53, "棒球", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101032, 53, "乒乓球", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101033, 53, "篮球", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101034, 53, "呼啦圈", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101035, 53, "艺术体操", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101036, 53, "滑板", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101037, 53, "桌球", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101038, 53, "羽毛球", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101039, 53, "网球", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101041, 53, "柠檬风情", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101042, 53, "草莓物语", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101043, 53, "甜蜜之恋", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101044, 53, "您的咖啡", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101045, 53, "方糖来也", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101046, 53, "想喝什么", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101047, 53, "超大咖啡", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101048, 53, "一口冰爽", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101049, 53, "拉花秀", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101051, 53, "歌神", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101052, 53, "欧耶~欧耶~", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101053, 53, "摇滚时代", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101054, 53, "锡铁小兵", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101055, 53, "铁皮青蛙", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101056, 53, "泡泡胶", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101057, 53, "俄罗斯套娃", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101058, 53, "铁皮飞机", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101059, 53, "发条小鸭", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101061, 53, "锦上添花", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101062, 53, "鱼跃龙门", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101063, 53, "金玉满堂", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101064, 53, "福气临门", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101065, 53, "财源广进", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101066, 53, "万事如意", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101067, 53, "文曲星下凡", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101068, 53, "欢喜新年", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101069, 53, "龙马精神", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101071, 53, "幼稚园学生", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101072, 53, "侦探", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101073, 53, "小画家", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101074, 53, "水手", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101075, 53, "甜品师", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101076, 53, "挤奶工", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101077, 53, "宇航员", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101078, 53, "占星师", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101079, 53, "小丑", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101081, 53, "来口冰淇淋", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101082, 53, "甜点服务", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101083, 53, "西瓜冰沙", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101084, 53, "狠狠吸果汁", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101085, 53, "甜点橡皮泥", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101086, 53, "浇点美味", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101087, 53, "变身甜点", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101088, 53, "水晶蛋糕", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101089, 54, "美滋滋晕乎乎", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101091, 53, "摇摆花朵", "萌二写真卡。", 1, false, false, 10, 30, 9999, false, {back=5}, "", nil, 1, 0},
	{16101092, 53, "拥抱花花", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101093, 53, "演奏之树", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101094, 53, "随歌起舞", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101095, 53, "蝶舞之乐", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101096, 53, "曲谱吟唱", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101097, 53, "彩虹之歌", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101098, 53, "熊熊歌神", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101099, 54, "躺平听曲", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101101, 53, "两颗小樱桃", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101102, 53, "一束爱心花", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101103, 53, "萌二羞羞", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101104, 53, "爱心，爱心", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101105, 53, "幸福气球", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101106, 53, "爱心带回家", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101107, 53, "谁最可爱", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101108, 54, "陷入爱里", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101109, 53, "脸蛋贴贴", "萌二写真卡。", 5, false, false, 10, 480, 9999, false, {back=5}, "", nil, 1, 0},
	{16101111, 53, "糖炒栗子", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101112, 53, "烘焙师", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101113, 53, "巧克力蛋糕", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101114, 53, "脆皮圣代", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101115, 53, "布丁诱惑", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101116, 53, "星星泡芙", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101117, 54, "草莓虎皮卷", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101118, 53, "萌睡甜甜圈", "萌二写真卡。", 5, false, false, 10, 480, 9999, false, {back=5}, "", nil, 1, 0},
	{16101119, 54, "云朵棉花糖", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16101121, 53, "猪猪睡衣", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101122, 53, "兔子睡衣", "萌二写真卡。", 2, false, false, 10, 60, 9999, false, {back=5}, "", nil, 1, 0},
	{16101123, 53, "小蜜蜂睡衣", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101124, 53, "独角兽睡衣", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101125, 53, "小羊睡衣", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101126, 54, "熊熊睡衣", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101127, 54, "老虎睡衣", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101128, 53, "熊猫睡衣", "萌二写真卡。", 5, false, false, 10, 480, 9999, false, {back=5}, "", nil, 1, 0},
	{16101129, 54, "彩虹咩咩梦", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16101131, 53, "萌二花环", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101132, 53, "贪吃驯鹿", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101133, 53, "礼物就是我", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101134, 53, "跳舞雪人", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101135, 54, "姜饼人大厨", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101136, 54, "妥妥的炫耀", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101137, 53, "幸福时刻", "萌二写真卡。", 5, false, false, 10, 480, 9999, false, {back=5}, "", nil, 1, 0},
	{16101138, 54, "送礼物去咯", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16101139, 54, "萌二飞飞", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16101141, 53, "灰姑娘", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101142, 53, "迷糊魔法师", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101143, 53, "疯帽子", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101144, 53, "小红帽", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101145, 54, "茶壶先生", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101146, 54, "狼外婆", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101147, 53, "扫帚巫师", "萌二写真卡。", 5, false, false, 10, 480, 9999, false, {back=5}, "", nil, 1, 0},
	{16101148, 54, "白兔先生", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16101149, 54, "红皇后", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16101151, 53, "章鱼小丸子", "萌二写真卡。", 3, false, false, 10, 120, 9999, false, {back=5}, "", nil, 1, 0},
	{16101152, 53, "关东煮", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101153, 53, "杂粮煎饼", "萌二写真卡。", 4, false, false, 10, 240, 9999, false, {back=5}, "", nil, 1, 0},
	{16101154, 54, "南方熊熊看见雪", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101155, 54, "北方熊熊看见雪", "萌二写真卡。", 4, false, false, 10, 360, 9999, false, {back=5}, "", nil, 1, 0},
	{16101156, 53, "炸串", "萌二写真卡。", 5, false, false, 10, 480, 9999, false, {back=5}, "", nil, 1, 0},
	{16101157, 53, "回家啦", "萌二写真卡。", 5, false, false, 10, 480, 9999, false, {back=5}, "", nil, 1, 0},
	{16101158, 54, "欢乐过大年", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16101159, 54, "宅家玩耍", "萌二写真卡。", 5, false, false, 10, 720, 9999, false, {back=5}, "", nil, 1, 0},
	{16102011, 53, "奶油饼干", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102012, 53, "甜甜小鱼饼", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102013, 53, "爆脆炸虾", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102014, 53, "草莓蛋糕", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102015, 53, "小泡芙咖啡", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102016, 53, "芒果冰淇淋", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102017, 53, "Q弹布丁", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102018, 53, "棉花糖串串", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102019, 53, "海苔饭团", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102021, 53, "烤肉形松饼", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102022, 53, "松饼小锅", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102023, 53, "黄油松饼", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102024, 53, "松饼伴侣", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102025, 53, "松饼变形记", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102026, 53, "松饼巨无霸", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102027, 53, "热乎松饼", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102028, 53, "松饼时间", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102029, 53, "松饼弹弹床", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102031, 53, "草堆堆", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102032, 53, "小小花丛", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102033, 53, "大大花丛", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102034, 53, "黄杜鹃小伞", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102035, 53, "小雏菊花篮", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102036, 53, "向日葵风车", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102037, 53, "茶花杯杯", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102038, 53, "芍药小背篓", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102039, 53, "樱花秋千", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102041, 53, "礼物就是我", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102042, 53, "惊喜将至", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102043, 53, "高兴到飞起", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102044, 53, "拆开的礼物", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102045, 53, "我的礼物", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102046, 53, "派对表演", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102047, 53, "白马王子", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102048, 53, "超大礼物", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102049, 53, "礼物车车", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102051, 53, "暗夜糖果", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102052, 53, "黑夜精灵", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102053, 53, "微笑南瓜怪", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102054, 53, "兔兔惊袭", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102055, 53, "南瓜兔兔", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102056, 53, "惊悚怪物", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102057, 53, "南瓜糖罐", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102058, 53, "南瓜小泡芙", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102059, 53, "暗夜飞帚", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102061, 53, "小泡芙饭团", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102062, 53, "玉子寿司", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102063, 53, "甜虾寿司", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102064, 53, "三文鱼寿司", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102065, 53, "三文鱼刺身", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102066, 53, "寿司碟叠高", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102067, 53, "酱油芥末没了", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102068, 53, "回转寿司", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102069, 53, "兔兔寿司", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102071, 53, "睡觉觉", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102072, 53, "上学去", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102073, 53, "穿新衣服", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102074, 53, "选衣服", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102075, 53, "梳头发", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102076, 53, "吹头发", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102077, 53, "看什么看", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102078, 53, "整理服装", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102079, 53, "一起刷牙", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102081, 53, "一瓶魔药", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102082, 53, "一朵毒蘑菇", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102083, 53, "一朵甜蘑菇", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102084, 53, "两瓶臭臭水", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102085, 53, "变质蜂蜜水", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102086, 53, "清理和偷吃", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102087, 53, "踩扁药材", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102088, 53, "搅拌魔药", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102089, 54, "魔药出锅", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102091, 53, "泳池之梦", "小泡芙画册。", 1, false, false, 10, 30, 9999, false, {back=6}, "", nil, 1, 0},
	{16102092, 53, "桌面清洁", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102093, 53, "洁净时刻", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102094, 53, "洁净厨房", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102095, 53, "浴中静谧", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102096, 53, "家居清扫工", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102097, 53, "水花飞溅", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102098, 53, "沐浴时光", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102099, 54, "浴乐融融", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102101, 53, "真挚祝福", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102102, 53, "糖果味气球", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102103, 53, "美少女变身", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102104, 53, "小泡芙蜡烛", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102105, 53, "生日捉迷藏", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102106, 53, "蛋糕开吃啦", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102107, 53, "小泡芙蛋糕", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102108, 54, "生日派对", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102109, 53, "快乐挤奶油", "小泡芙画册。", 5, false, false, 10, 480, 9999, false, {back=6}, "", nil, 1, 0},
	{16102111, 53, "秀秀抱枕", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102112, 53, "睡意酝酿", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102113, 53, "睡前告白", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102114, 53, "睡眠准备", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102115, 53, "糟糕失眠", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102116, 53, "抱枕大战", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102117, 54, "浪漫入眠", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102118, 53, "惬意睡姿", "小泡芙画册。", 5, false, false, 10, 480, 9999, false, {back=6}, "", nil, 1, 0},
	{16102119, 54, "梦前时光", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16102121, 53, "雪之礼物", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102122, 53, "礼物堆堆", "小泡芙画册。", 2, false, false, 10, 60, 9999, false, {back=6}, "", nil, 1, 0},
	{16102123, 53, "甜蜜蛋糕", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102124, 53, "冬雪祝愿", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102125, 53, "美味邀请", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102126, 54, "雪人水晶球", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102127, 54, "袜子和礼物", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102128, 53, "仪式感满满", "小泡芙画册。", 5, false, false, 10, 480, 9999, false, {back=6}, "", nil, 1, 0},
	{16102129, 54, "姜饼小屋", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16102131, 53, "打包箱", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102132, 53, "买面包", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102133, 53, "购物篮", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102134, 53, "购物袋", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102135, 54, "大袋山竹", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102136, 54, "奶茶时间", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102137, 53, "购物车", "小泡芙画册。", 5, false, false, 10, 480, 9999, false, {back=6}, "", nil, 1, 0},
	{16102138, 54, "购物后的快乐", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16102139, 54, "欢迎光临", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16102141, 53, "准备开店", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102142, 53, "第一个面包", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102143, 53, "第一杯咖啡", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102144, 53, "汉堡玻璃柜", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102145, 54, "美味煎蛋", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102146, 54, "小泡芙饼干", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102147, 53, "早餐时间", "小泡芙画册。", 5, false, false, 10, 480, 9999, false, {back=6}, "", nil, 1, 0},
	{16102148, 54, "小泡芙汉堡包", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16102149, 54, "奶油搅搅搅", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16102151, 53, "打打棒球", "小泡芙画册。", 3, false, false, 10, 120, 9999, false, {back=6}, "", nil, 1, 0},
	{16102152, 53, "送送牛奶", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102153, 53, "尝尝爆米花", "小泡芙画册。", 4, false, false, 10, 240, 9999, false, {back=6}, "", nil, 1, 0},
	{16102154, 54, "吃吃西瓜", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102155, 54, "喝喝奶昔", "小泡芙画册。", 4, false, false, 10, 360, 9999, false, {back=6}, "", nil, 1, 0},
	{16102156, 53, "扮扮蜜蜂", "小泡芙画册。", 5, false, false, 10, 480, 9999, false, {back=6}, "", nil, 1, 0},
	{16102157, 53, "晒晒太阳", "小泡芙画册。", 5, false, false, 10, 480, 9999, false, {back=6}, "", nil, 1, 0},
	{16102158, 54, "滑滑雪", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16102159, 54, "潜潜水", "小泡芙画册。", 5, false, false, 10, 720, 9999, false, {back=6}, "", nil, 1, 0},
	{16103011, 53, "蹦蹦床", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103012, 53, "旋转杯", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103013, 53, "咖啡杯", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103014, 53, "小火车", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103015, 53, "摇摇乐", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103016, 53, "礼物盒", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103017, 53, "甜蜜梦乡", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103018, 53, "咖啡甜心", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103019, 53, "游戏机", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103021, 53, "海豚", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103022, 53, "章鱼", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103023, 53, "海星", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103024, 53, "海洋贝壳", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103025, 53, "浮游", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103026, 53, "鲸", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103027, 53, "珊瑚", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103028, 53, "小龟", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103029, 53, "企鹅", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103031, 53, "甜甜圈", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103032, 53, "冰淇淋", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103033, 53, "草莓乳酪", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103034, 53, "草莓布丁", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103035, 53, "美味购物车", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103036, 53, "小奶瓶", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103037, 53, "吐司喵", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103038, 53, "糖霜不倒翁", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103039, 53, "汉堡包", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103041, 53, "小象", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103042, 53, "小浣熊", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103043, 53, "小猪", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103044, 53, "小狮子", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103045, 53, "小奶牛", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103046, 53, "熊猫", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103047, 53, "小考拉", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103048, 53, "小猫咪", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103049, 53, "小老虎", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103051, 53, "沉溺", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103052, 53, "连结", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103053, 53, "倾听海浪", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103054, 53, "愿望", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103055, 53, "花吃了这人儿", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103056, 53, "栽种思绪", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103057, 53, "风与你", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103058, 53, "融", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103059, 53, "烛光", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103061, 53, "绚烂的梦", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103062, 53, "天使梦儿", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103063, 53, "小确幸", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103064, 53, "眼泪宝石", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103065, 53, "小甜心", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103066, 53, "星愿", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103067, 53, "气球假日", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103068, 53, "花语浪漫", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103069, 53, "珍珠的泪", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103071, 53, "窗花", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103072, 53, "福娃", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103073, 53, "挥春", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103074, 53, "小星愿", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103075, 53, "蛋糕派对", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103076, 53, "奶昔之约", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103077, 53, "浮世烟花", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103078, 53, "幸福花环", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103079, 53, "花之宴", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103081, 53, "萌萌表演家", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103082, 53, "诗歌艺术家", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103083, 53, "飒爽飞行员", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103084, 53, "新潮司机", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103085, 53, "快乐矿工", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103086, 53, "复古车司机", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103087, 53, "超级宇航员", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103088, 53, "天才小厨师", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103089, 54, "灵魂画家", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103091, 53, "炫彩番茄", "Starbliss画册。", 1, false, false, 10, 30, 9999, false, {back=7}, "", nil, 1, 0},
	{16103092, 53, "玩具熊", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103093, 53, "蝴蝶小姐", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103094, 53, "拥抱爱心", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103095, 53, "心之爱", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103096, 53, "摩登花瓶", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103097, 53, "漫游世界", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103098, 53, "兔之梦", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103099, 54, "兔剑士", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103101, 53, "小丑", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103102, 53, "吹龙口哨", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103103, 53, "惊喜盒", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103104, 53, "驯兽师", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103105, 53, "魔术小帽", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103106, 53, "舞台", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103107, 53, "催眠梦境", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103108, 54, "假面", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103109, 53, "兔耳扑克", "Starbliss画册。", 5, false, false, 10, 480, 9999, false, {back=7}, "", nil, 1, 0},
	{16103111, 53, "调皮小恶魔", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103112, 53, "魔镜之灵", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103113, 53, "药水大师", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103114, 53, "捣蛋鬼", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103115, 53, "时间管理者", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103116, 53, "悲伤契约者", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103117, 54, "魔女工作室", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103118, 53, "魔女宝箱", "Starbliss画册。", 5, false, false, 10, 480, 9999, false, {back=7}, "", nil, 1, 0},
	{16103119, 54, "吸血鬼", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16103121, 53, "私藏爱心", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103122, 53, "机甲幻想", "Starbliss画册。", 2, false, false, 10, 60, 9999, false, {back=7}, "", nil, 1, 0},
	{16103123, 53, "秘密星球", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103124, 53, "暗中调香", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103125, 53, "珍藏玩偶", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103126, 54, "隐藏的爱好", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103127, 54, "真正的我", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103128, 53, "偷偷扮演", "Starbliss画册。", 5, false, false, 10, 480, 9999, false, {back=7}, "", nil, 1, 0},
	{16103129, 54, "偷吃巧克力", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16103131, 53, "梦幻珍珠", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103132, 53, "梦幻蜜蜂", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103133, 53, "梦幻招财猫", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103134, 53, "梦幻彩花", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103135, 54, "梦幻绿叶", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103136, 54, "梦幻魔石", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103137, 53, "梦幻爱神", "Starbliss画册。", 5, false, false, 10, 480, 9999, false, {back=7}, "", nil, 1, 0},
	{16103138, 54, "梦幻宝石", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16103139, 54, "梦幻舞蝶", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16103141, 53, "蘑菇精灵", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103142, 53, "雨滴精灵", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103143, 53, "蜜蜂精灵", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103144, 53, "小狐狸精灵", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103145, 54, "红蘑菇精灵", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103146, 54, "小虫精灵", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103147, 53, "精灵美味小摊", "Starbliss画册。", 5, false, false, 10, 480, 9999, false, {back=7}, "", nil, 1, 0},
	{16103148, 54, "向日葵精灵", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16103149, 54, "精灵眷顾者", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16103151, 53, "撕裂", "Starbliss画册。", 3, false, false, 10, 120, 9999, false, {back=7}, "", nil, 1, 0},
	{16103152, 53, "寻觅", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103153, 53, "苏醒", "Starbliss画册。", 4, false, false, 10, 240, 9999, false, {back=7}, "", nil, 1, 0},
	{16103154, 54, "太空诗篇", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103155, 54, "奔赴", "Starbliss画册。", 4, false, false, 10, 360, 9999, false, {back=7}, "", nil, 1, 0},
	{16103156, 53, "希望与救赎", "Starbliss画册。", 5, false, false, 10, 480, 9999, false, {back=7}, "", nil, 1, 0},
	{16103157, 53, "诞生", "Starbliss画册。", 5, false, false, 10, 480, 9999, false, {back=7}, "", nil, 1, 0},
	{16103158, 54, "星空飞车", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16103159, 54, "月亮船", "Starbliss画册。", 5, false, false, 10, 720, 9999, false, {back=7}, "", nil, 1, 0},
	{16104011, 53, "呆萌花花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104012, 53, "迷惑点点", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104013, 53, "观望小鸡", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104014, 53, "害羞添添", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104015, 53, "好奇猪米", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104016, 53, "眨眼花花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104017, 53, "吃瓜小鸡", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104018, 53, "快乐点点", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104019, 53, "俏皮猪米", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104021, 53, "花花和花花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104022, 53, "头上一枝小黄花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104023, 53, "叼一枝小花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104024, 53, "帽子下的小黄花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104025, 53, "一朵小红花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104026, 53, "墨镜与靓仔", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104027, 53, "自信得发光", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104028, 53, "玫瑰的魅力", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104029, 53, "炫彩自拍", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104031, 53, "送胡萝卜", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104032, 53, "送麦穗", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104033, 53, "送星星", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104034, 53, "送肉骨头", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104035, 53, "送苹果", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104036, 53, "送荧光棒", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104037, 53, "送花花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104038, 53, "送钻石", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104039, 53, "送爱心", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104041, 53, "快乐奔跑", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104042, 53, "悠闲午睡", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104043, 53, "享受阳光", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104044, 53, "拥抱自由", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104045, 53, "练拳", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104046, 53, "迎接美好", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104047, 53, "散散步", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104048, 53, "打招呼", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104049, 53, "吹泡泡", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104051, 53, "小鸡腿点点", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104052, 53, "蔬菜小鸡", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104053, 53, "胡萝卜花花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104054, 53, "大鸡腿点点", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104055, 53, "糖果猪米", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104056, 53, "甜筒花花", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104057, 53, "香蕉添添", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104058, 53, "蛋糕猪米", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104059, 53, "汉堡包猪米", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104061, 53, "飘飘小幽灵", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104062, 53, "南瓜灯怪花花", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104063, 53, "女巫添添", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104064, 53, "缝合怪小鸡", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104065, 53, "南瓜幽灵花花", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104066, 53, "木乃伊小鸡", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104067, 53, "睡衣幽灵添添", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104068, 53, "骷髅怪点点", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104069, 53, "南瓜怪猪米", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104071, 53, "做梦的鸡宝宝", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104072, 53, "甜梦中的添添", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104073, 53, "沉睡中的小鸡", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104074, 53, "熬夜的猪咪", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104075, 53, "听故事的花花", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104076, 53, "美梦中的点点", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104077, 53, "失眠的添添", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104078, 53, "睡前思考的小鸡", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104079, 53, "打呼的猪米", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104081, 53, "花花独唱", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104082, 53, "猪米独唱", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104083, 53, "吹萨克斯", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104084, 53, "弹吉他", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104085, 53, "打鼓添添", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104086, 53, "打鼓点点", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104087, 53, "打鼓花花", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104088, 53, "打鼓小鸡", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104089, 54, "打鼓猪米", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104091, 53, "看我表演", "米花照片。", 1, false, false, 10, 30, 9999, false, {back=8}, "", nil, 1, 0},
	{16104092, 53, "金鸡独立", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104093, 53, "双臂支撑", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104094, 53, "丢掷彩球", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104095, 53, "单臂倒立", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104096, 53, "踩圆环", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104097, 53, "双重呼啦圈", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104098, 53, "套圈圈", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104099, 54, "跳圈圈", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104101, 53, "一捧花", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104102, 53, "星星棒", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104103, 53, "叠爱心", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104104, 53, "欢乐起舞", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104105, 53, "一堆礼物", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104106, 53, "爱心气球", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104107, 53, "红气球", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104108, 54, "两颗爱心", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104109, 53, "变身礼物", "米花照片。", 5, false, false, 10, 480, 9999, false, {back=8}, "", nil, 1, 0},
	{16104111, 53, "鼻子接水", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104112, 53, "幸好，幸好", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104113, 53, "怎么下雨了？", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104114, 53, "出门忘带伞", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104115, 53, "冷冷的雨水", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104116, 53, "悲伤成河", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104117, 54, "雨天滑倒", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104118, 53, "猪猪忧伤", "米花照片。", 5, false, false, 10, 480, 9999, false, {back=8}, "", nil, 1, 0},
	{16104119, 54, "大耳朵的妙处", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16104121, 53, "写日记", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104122, 53, "私人空间", "米花照片。", 2, false, false, 10, 60, 9999, false, {back=8}, "", nil, 1, 0},
	{16104123, 53, "小小粉刷匠", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104124, 53, "巨型铅笔", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104125, 53, "泡泡脚", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104126, 54, "仔细阅读", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104127, 54, "沙发看书", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104128, 53, "奇妙烧烤", "米花照片。", 5, false, false, 10, 480, 9999, false, {back=8}, "", nil, 1, 0},
	{16104129, 54, "绘画时间", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16104131, 53, "堆雪人", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104132, 53, "姜饼人", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104133, 53, "糖果花环", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104134, 53, "闪亮松树", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104135, 54, "杯子浴缸", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104136, 54, "两个雪人", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104137, 53, "雪人球", "米花照片。", 5, false, false, 10, 480, 9999, false, {back=8}, "", nil, 1, 0},
	{16104138, 54, "雪人串串", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16104139, 54, "真冰糖葫芦", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16104141, 53, "认真洗澡", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104142, 53, "思考中度过", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104143, 53, "跳个舞", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104144, 53, "忘拿毛巾", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104145, 54, "和小鸭子玩耍", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104146, 54, "游个泳", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104147, 53, "泡澡也是度假", "米花照片。", 5, false, false, 10, 480, 9999, false, {back=8}, "", nil, 1, 0},
	{16104148, 54, "滑倒也快乐", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16104149, 54, "打水仗", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16104151, 53, "快乐聚餐", "米花照片。", 3, false, false, 10, 120, 9999, false, {back=8}, "", nil, 1, 0},
	{16104152, 53, "齐齐看电视", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104153, 53, "拥挤的被窝", "米花照片。", 4, false, false, 10, 240, 9999, false, {back=8}, "", nil, 1, 0},
	{16104154, 54, "音乐派对", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104155, 54, "心花怒放", "米花照片。", 4, false, false, 10, 360, 9999, false, {back=8}, "", nil, 1, 0},
	{16104156, 53, "比赛滑滑梯", "米花照片。", 5, false, false, 10, 480, 9999, false, {back=8}, "", nil, 1, 0},
	{16104157, 53, "礼物盒", "米花照片。", 5, false, false, 10, 480, 9999, false, {back=8}, "", nil, 1, 0},
	{16104158, 54, "热气球", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16104159, 54, "野餐", "米花照片。", 5, false, false, 10, 720, 9999, false, {back=8}, "", nil, 1, 0},
	{16105011, 53, "KO小兔", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105012, 53, "被淘汰的小可怜", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105013, 53, "奇怪贪吃蛇", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105014, 53, "愤怒小兔", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105015, 53, "悲伤小兔", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105016, 53, "街头游戏机", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105017, 53, "迷你游戏机", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105018, 53, "一起组队吗", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105019, 53, "拔草", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105021, 53, "一杯咖啡", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105022, 53, "咖啡爱好兔", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105023, 53, "咖啡服务员", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105024, 53, "激动的客人", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105025, 53, "咖啡豆精灵", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105026, 53, "怪奇咖啡", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105027, 53, "彩虹糖喵喵", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105028, 53, "咖啡豆相框", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105029, 53, "咖啡店老板", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105031, 53, "青蛙玩偶", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105032, 53, "兔兔玩偶", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105033, 53, "发条波波玩偶", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105034, 53, "狮子玩偶", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105035, 53, "苹果玩偶", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105036, 53, "玩偶叠叠高", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105037, 53, "发条猫玩偶", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105038, 53, "猫猫玩偶", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105039, 53, "玩偶小帽", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105041, 53, "奶酪", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105042, 53, "鸡蛋布丁", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105043, 53, "柠檬星光气泡水", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105044, 53, "芒果布丁", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105045, 53, "鼠鼠雪顶气泡水", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105046, 53, "薄荷樱桃气泡水", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105047, 53, "鼠鼠姐服务员", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105048, 53, "鼠鼠妹服务员", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105049, 53, "呆萌小老板", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105051, 53, "暴走冰块", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105052, 53, "奶香小猪", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105053, 53, "飞飞小粉猪", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105054, 53, "三只猪噗噗", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105055, 53, "冰块小粉猪", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105056, 53, "雪顶奶茶", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105057, 53, "哼唧奶油小猪", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105058, 53, "猫咪小窗", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105059, 53, "猪猪奶茶", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105061, 53, "快乐散步", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105062, 53, "收到花花", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105063, 53, "你真棒", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105064, 53, "吃西瓜咯", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105065, 53, "彩虹喵喵", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105066, 53, "漫步青草", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105067, 53, "星光夜跑", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105068, 53, "花儿笑眯眯", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105069, 53, "送你一朵小红花", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105071, 53, "花花盆栽", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105072, 53, "蝴蝶结挂件", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105073, 53, "小粉蝶", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105074, 53, "小绿蝶", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105075, 53, "粉蝶采蜜", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105076, 53, "休憩片刻", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105077, 53, "花苞小蝶", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105078, 53, "蝴蝶精灵", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105079, 53, "花蝶公主", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105081, 53, "花朵兔兔", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105082, 53, "生气兔兔", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105083, 53, "送花兔兔", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105084, 53, "微笑兔兔", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105085, 53, "草莓兔兔", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105086, 53, "青草兔兔", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105087, 53, "惊讶兔兔", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105088, 53, "左边爱心兔兔", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105089, 54, "右边爱心兔兔", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105091, 53, "甜甜盆栽", "优米鹿照片。", 1, false, false, 10, 30, 9999, false, {back=9}, "", nil, 1, 0},
	{16105092, 53, "甜甜熊", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105093, 53, "甜甜小蜜蜂", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105094, 53, "甜甜小飞熊", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105095, 53, "甜甜雪糕", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105096, 53, "甜甜番茄", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105097, 53, "甜甜小龙", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105098, 53, "甜甜小熊花", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105099, 54, "甜甜少女", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105101, 53, "硬糖小狗", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105102, 53, "甜甜圈小狗", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105103, 53, "向甜点奔赴", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105104, 53, "甜点挂件", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105105, 53, "甜甜圈盆栽", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105106, 53, "甜点吃完啦", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105107, 53, "肚子饿饿", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105108, 54, "小狗棉鞋", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105109, 53, "甜甜圈小凳", "优米鹿照片。", 5, false, false, 10, 480, 9999, false, {back=9}, "", nil, 1, 0},
	{16105111, 53, "美味蘑菇", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105112, 53, "觅食小猫", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105113, 53, "蘑菇精灵", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105114, 53, "餐前舞蹈", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105115, 53, "美味祈祷", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105116, 53, "森林奔跑", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105117, 54, "采蘑菇", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105118, 53, "快乐泡澡", "优米鹿照片。", 5, false, false, 10, 480, 9999, false, {back=9}, "", nil, 1, 0},
	{16105119, 54, "美食家少女", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
	{16105121, 53, "逃跑的樱桃", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105122, 53, "翻倒的果汁", "优米鹿照片。", 2, false, false, 10, 60, 9999, false, {back=9}, "", nil, 1, 0},
	{16105123, 53, "藏起来", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105124, 53, "小蜜蜂招牌", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105125, 53, "犯困小精灵", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105126, 54, "享用小麦汁", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105127, 54, "猫猫小蜜蜂", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105128, 53, "悄悄喝一口", "优米鹿照片。", 5, false, false, 10, 480, 9999, false, {back=9}, "", nil, 1, 0},
	{16105129, 54, "回家啦", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
	{16105131, 53, "许愿", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105132, 53, "锻炼", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105133, 53, "比耶", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105134, 53, "嗅花", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105135, 54, "跳舞", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105136, 54, "采花", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105137, 53, "呼唤", "优米鹿照片。", 5, false, false, 10, 480, 9999, false, {back=9}, "", nil, 1, 0},
	{16105138, 54, "思考", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
	{16105139, 54, "玩耍", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
	{16105141, 53, "糖果精灵", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105142, 53, "姜饼人精灵", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105143, 53, "彩蛋精灵", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105144, 53, "多芒星精灵", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105145, 54, "采花精灵", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105146, 54, "浇水精灵", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105147, 53, "蝴蝶结精灵", "优米鹿照片。", 5, false, false, 10, 480, 9999, false, {back=9}, "", nil, 1, 0},
	{16105148, 54, "小星星精灵", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
	{16105149, 54, "快乐精灵", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
	{16105151, 53, "见习魔女", "优米鹿照片。", 3, false, false, 10, 120, 9999, false, {back=9}, "", nil, 1, 0},
	{16105152, 53, "爱心魔女", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105153, 53, "暗夜魔女", "优米鹿照片。", 4, false, false, 10, 240, 9999, false, {back=9}, "", nil, 1, 0},
	{16105154, 54, "海洋魔女", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105155, 54, "森林魔女", "优米鹿照片。", 4, false, false, 10, 360, 9999, false, {back=9}, "", nil, 1, 0},
	{16105156, 53, "太阳魔女", "优米鹿照片。", 5, false, false, 10, 480, 9999, false, {back=9}, "", nil, 1, 0},
	{16105157, 53, "雷电魔女", "优米鹿照片。", 5, false, false, 10, 480, 9999, false, {back=9}, "", nil, 1, 0},
	{16105158, 54, "招财魔女联盟", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
	{16105159, 54, "火焰魔女联盟", "优米鹿照片。", 5, false, false, 10, 720, 9999, false, {back=9}, "", nil, 1, 0},
}

local t_prop = {
	[16000001] = dataList[1],
	[16000002] = dataList[2],
	[16000003] = dataList[3],
	[16000004] = dataList[4],
	[16000005] = dataList[5],
	[16000006] = dataList[6],
	[16000007] = dataList[7],
	[16000008] = dataList[8],
	[16000009] = dataList[9],
	[16000010] = dataList[10],
	[16000011] = dataList[11],
	[16000012] = dataList[12],
	[16000013] = dataList[13],
	[16000014] = dataList[14],
	[16000015] = dataList[15],
	[16000016] = dataList[16],
	[16000017] = dataList[17],
	[16000018] = dataList[18],
	[16000019] = dataList[19],
	[16000020] = dataList[20],
	[16000021] = dataList[21],
	[16000022] = dataList[22],
	[16000023] = dataList[23],
	[16000024] = dataList[24],
	[16000025] = dataList[25],
	[16000026] = dataList[26],
	[16000027] = dataList[27],
	[16000028] = dataList[28],
	[16000029] = dataList[29],
	[16000030] = dataList[30],
	[16000031] = dataList[31],
	[16000032] = dataList[32],
	[16000033] = dataList[33],
	[16000034] = dataList[34],
	[16000035] = dataList[35],
	[16000036] = dataList[36],
	[16000037] = dataList[37],
	[16000038] = dataList[38],
	[16000039] = dataList[39],
	[16000040] = dataList[40],
	[16000041] = dataList[41],
	[16000042] = dataList[42],
	[16000043] = dataList[43],
	[16000044] = dataList[44],
	[16000045] = dataList[45],
	[16000046] = dataList[46],
	[16000047] = dataList[47],
	[16000048] = dataList[48],
	[16000049] = dataList[49],
	[16000050] = dataList[50],
	[16000051] = dataList[51],
	[16000052] = dataList[52],
	[16000053] = dataList[53],
	[16000054] = dataList[54],
	[16000055] = dataList[55],
	[16000056] = dataList[56],
	[16000057] = dataList[57],
	[16000058] = dataList[58],
	[16000059] = dataList[59],
	[16000060] = dataList[60],
	[16000061] = dataList[61],
	[16000062] = dataList[62],
	[16000063] = dataList[63],
	[16000064] = dataList[64],
	[16000065] = dataList[65],
	[16000066] = dataList[66],
	[16000067] = dataList[67],
	[16000068] = dataList[68],
	[16000069] = dataList[69],
	[16000070] = dataList[70],
	[16000071] = dataList[71],
	[16000072] = dataList[72],
	[16000073] = dataList[73],
	[16000074] = dataList[74],
	[16000075] = dataList[75],
	[16000076] = dataList[76],
	[16000077] = dataList[77],
	[16000078] = dataList[78],
	[16000079] = dataList[79],
	[16000080] = dataList[80],
	[16000081] = dataList[81],
	[16000082] = dataList[82],
	[16000083] = dataList[83],
	[16000084] = dataList[84],
	[16000085] = dataList[85],
	[16000086] = dataList[86],
	[16000087] = dataList[87],
	[16000088] = dataList[88],
	[16000089] = dataList[89],
	[16000090] = dataList[90],
	[16000091] = dataList[91],
	[16000092] = dataList[92],
	[16000093] = dataList[93],
	[16000094] = dataList[94],
	[16000095] = dataList[95],
	[16000096] = dataList[96],
	[16000097] = dataList[97],
	[16000098] = dataList[98],
	[16000099] = dataList[99],
	[16000100] = dataList[100],
	[16000101] = dataList[101],
	[16000102] = dataList[102],
	[16000103] = dataList[103],
	[16000104] = dataList[104],
	[16000105] = dataList[105],
	[16000106] = dataList[106],
	[16000107] = dataList[107],
	[16000108] = dataList[108],
	[16000109] = dataList[109],
	[16000110] = dataList[110],
	[16000111] = dataList[111],
	[16000112] = dataList[112],
	[16000113] = dataList[113],
	[16000114] = dataList[114],
	[16000115] = dataList[115],
	[16000116] = dataList[116],
	[16000117] = dataList[117],
	[16000118] = dataList[118],
	[16000119] = dataList[119],
	[16000120] = dataList[120],
	[16000121] = dataList[121],
	[16000122] = dataList[122],
	[16000123] = dataList[123],
	[16000124] = dataList[124],
	[16000125] = dataList[125],
	[16000126] = dataList[126],
	[16000127] = dataList[127],
	[16000128] = dataList[128],
	[16000129] = dataList[129],
	[16000130] = dataList[130],
	[16000131] = dataList[131],
	[16000132] = dataList[132],
	[16000133] = dataList[133],
	[16000134] = dataList[134],
	[16000135] = dataList[135],
	[16000136] = dataList[136],
	[16000137] = dataList[137],
	[16000138] = dataList[138],
	[16000139] = dataList[139],
	[16000140] = dataList[140],
	[16000141] = dataList[141],
	[16000142] = dataList[142],
	[16000143] = dataList[143],
	[16000144] = dataList[144],
	[16000145] = dataList[145],
	[16000146] = dataList[146],
	[16000147] = dataList[147],
	[16000148] = dataList[148],
	[16000149] = dataList[149],
	[16000150] = dataList[150],
	[16000151] = dataList[151],
	[16000152] = dataList[152],
	[16000153] = dataList[153],
	[16000154] = dataList[154],
	[16000155] = dataList[155],
	[16000156] = dataList[156],
	[16000157] = dataList[157],
	[16000158] = dataList[158],
	[16000159] = dataList[159],
	[16000160] = dataList[160],
	[16000161] = dataList[161],
	[16000162] = dataList[162],
	[16000163] = dataList[163],
	[16000164] = dataList[164],
	[16000165] = dataList[165],
	[16000166] = dataList[166],
	[16000167] = dataList[167],
	[16000168] = dataList[168],
	[16000169] = dataList[169],
	[16000170] = dataList[170],
	[16000171] = dataList[171],
	[16000172] = dataList[172],
	[16000173] = dataList[173],
	[16000174] = dataList[174],
	[16000175] = dataList[175],
	[16000176] = dataList[176],
	[16000177] = dataList[177],
	[16000178] = dataList[178],
	[16000179] = dataList[179],
	[16000180] = dataList[180],
	[16000181] = dataList[181],
	[16000182] = dataList[182],
	[16000183] = dataList[183],
	[16000184] = dataList[184],
	[16000185] = dataList[185],
	[16000186] = dataList[186],
	[16000187] = dataList[187],
	[16000188] = dataList[188],
	[16000189] = dataList[189],
	[16000190] = dataList[190],
	[16000191] = dataList[191],
	[16000192] = dataList[192],
	[16000193] = dataList[193],
	[16000194] = dataList[194],
	[16000195] = dataList[195],
	[16000196] = dataList[196],
	[16000197] = dataList[197],
	[16000198] = dataList[198],
	[16000199] = dataList[199],
	[16000200] = dataList[200],
	[16000201] = dataList[201],
	[16000202] = dataList[202],
	[16000203] = dataList[203],
	[16000204] = dataList[204],
	[16000205] = dataList[205],
	[16000206] = dataList[206],
	[16000207] = dataList[207],
	[16000208] = dataList[208],
	[16000209] = dataList[209],
	[16000210] = dataList[210],
	[16000211] = dataList[211],
	[16000212] = dataList[212],
	[16000213] = dataList[213],
	[16000214] = dataList[214],
	[16000215] = dataList[215],
	[16000216] = dataList[216],
	[16000217] = dataList[217],
	[16000218] = dataList[218],
	[16000219] = dataList[219],
	[16000220] = dataList[220],
	[16000221] = dataList[221],
	[16000222] = dataList[222],
	[16000223] = dataList[223],
	[16000224] = dataList[224],
	[16000225] = dataList[225],
	[16000226] = dataList[226],
	[16000227] = dataList[227],
	[16000228] = dataList[228],
	[16000229] = dataList[229],
	[16000230] = dataList[230],
	[16000231] = dataList[231],
	[16000232] = dataList[232],
	[16000234] = dataList[233],
	[16000235] = dataList[234],
	[16000236] = dataList[235],
	[16000237] = dataList[236],
	[16000238] = dataList[237],
	[16000239] = dataList[238],
	[16000240] = dataList[239],
	[16000241] = dataList[240],
	[16000242] = dataList[241],
	[16000243] = dataList[242],
	[16000244] = dataList[243],
	[16000245] = dataList[244],
	[16000246] = dataList[245],
	[16000247] = dataList[246],
	[16000248] = dataList[247],
	[16000249] = dataList[248],
	[16000250] = dataList[249],
	[16000251] = dataList[250],
	[16000252] = dataList[251],
	[16000253] = dataList[252],
	[16000254] = dataList[253],
	[16000255] = dataList[254],
	[16000256] = dataList[255],
	[16000257] = dataList[256],
	[16000258] = dataList[257],
	[16000259] = dataList[258],
	[16000260] = dataList[259],
	[16000261] = dataList[260],
	[16000262] = dataList[261],
	[16000263] = dataList[262],
	[16000264] = dataList[263],
	[16000265] = dataList[264],
	[16000266] = dataList[265],
	[16000267] = dataList[266],
	[16000268] = dataList[267],
	[16000269] = dataList[268],
	[16000270] = dataList[269],
	[16000271] = dataList[270],
	[16000272] = dataList[271],
	[16000273] = dataList[272],
	[16000274] = dataList[273],
	[16000275] = dataList[274],
	[16000276] = dataList[275],
	[16000277] = dataList[276],
	[16000278] = dataList[277],
	[16000279] = dataList[278],
	[16000280] = dataList[279],
	[16000281] = dataList[280],
	[16000282] = dataList[281],
	[16000283] = dataList[282],
	[16000284] = dataList[283],
	[16000285] = dataList[284],
	[16000286] = dataList[285],
	[16000287] = dataList[286],
	[16000288] = dataList[287],
	[16000289] = dataList[288],
	[16000290] = dataList[289],
	[16000291] = dataList[290],
	[16000292] = dataList[291],
	[16000293] = dataList[292],
	[16000294] = dataList[293],
	[16000295] = dataList[294],
	[16000296] = dataList[295],
	[16000297] = dataList[296],
	[16000298] = dataList[297],
	[16000299] = dataList[298],
	[16000300] = dataList[299],
	[16000301] = dataList[300],
	[16000302] = dataList[301],
	[16000303] = dataList[302],
	[16000304] = dataList[303],
	[16000305] = dataList[304],
	[16000306] = dataList[305],
	[16000307] = dataList[306],
	[16000308] = dataList[307],
	[16000309] = dataList[308],
	[16000310] = dataList[309],
	[16000311] = dataList[310],
	[16000312] = dataList[311],
	[16000313] = dataList[312],
	[16000314] = dataList[313],
	[16000315] = dataList[314],
	[16000316] = dataList[315],
	[16000317] = dataList[316],
	[16000318] = dataList[317],
	[16000319] = dataList[318],
	[16000320] = dataList[319],
	[16000321] = dataList[320],
	[16000326] = dataList[321],
	[16000327] = dataList[322],
	[16000328] = dataList[323],
	[16000329] = dataList[324],
	[16000330] = dataList[325],
	[16000331] = dataList[326],
	[16000332] = dataList[327],
	[16000333] = dataList[328],
	[16000334] = dataList[329],
	[16000335] = dataList[330],
	[16000336] = dataList[331],
	[16000337] = dataList[332],
	[16000338] = dataList[333],
	[16000339] = dataList[334],
	[16000340] = dataList[335],
	[16000341] = dataList[336],
	[16000342] = dataList[337],
	[16000343] = dataList[338],
	[16000344] = dataList[339],
	[16000345] = dataList[340],
	[16000346] = dataList[341],
	[16000347] = dataList[342],
	[16000348] = dataList[343],
	[16000349] = dataList[344],
	[16000350] = dataList[345],
	[16000351] = dataList[346],
	[16000352] = dataList[347],
	[16000353] = dataList[348],
	[16000354] = dataList[349],
	[16000355] = dataList[350],
	[16000356] = dataList[351],
	[16000357] = dataList[352],
	[16000358] = dataList[353],
	[16000359] = dataList[354],
	[16000360] = dataList[355],
	[16000361] = dataList[356],
	[16000362] = dataList[357],
	[16000363] = dataList[358],
	[16000364] = dataList[359],
	[16000365] = dataList[360],
	[16000366] = dataList[361],
	[16000367] = dataList[362],
	[16000368] = dataList[363],
	[16000369] = dataList[364],
	[16000370] = dataList[365],
	[16000371] = dataList[366],
	[16000372] = dataList[367],
	[16000373] = dataList[368],
	[16000374] = dataList[369],
	[16000375] = dataList[370],
	[16000376] = dataList[371],
	[16000377] = dataList[372],
	[16000378] = dataList[373],
	[16000379] = dataList[374],
	[16000388] = dataList[375],
	[16000389] = dataList[376],
	[16000390] = dataList[377],
	[16000391] = dataList[378],
	[16000392] = dataList[379],
	[16000393] = dataList[380],
	[16000394] = dataList[381],
	[16000395] = dataList[382],
	[16000396] = dataList[383],
	[16000397] = dataList[384],
	[16000398] = dataList[385],
	[16000399] = dataList[386],
	[16000400] = dataList[387],
	[16000401] = dataList[388],
	[16000402] = dataList[389],
	[16000403] = dataList[390],
	[16000404] = dataList[391],
	[16000405] = dataList[392],
	[16000406] = dataList[393],
	[16000407] = dataList[394],
	[16000408] = dataList[395],
	[16000409] = dataList[396],
	[16000410] = dataList[397],
	[16000411] = dataList[398],
	[16000412] = dataList[399],
	[16000413] = dataList[400],
	[16000414] = dataList[401],
	[16000415] = dataList[402],
	[16000416] = dataList[403],
	[16000417] = dataList[404],
	[16000418] = dataList[405],
	[16000419] = dataList[406],
	[16000420] = dataList[407],
	[16000421] = dataList[408],
	[16000422] = dataList[409],
	[16000423] = dataList[410],
	[16000424] = dataList[411],
	[16000425] = dataList[412],
	[16000426] = dataList[413],
	[16000427] = dataList[414],
	[16000428] = dataList[415],
	[16000429] = dataList[416],
	[16000430] = dataList[417],
	[16000431] = dataList[418],
	[16000432] = dataList[419],
	[16000433] = dataList[420],
	[16000434] = dataList[421],
	[16000435] = dataList[422],
	[16000436] = dataList[423],
	[16000437] = dataList[424],
	[16000438] = dataList[425],
	[16000439] = dataList[426],
	[16000440] = dataList[427],
	[16000441] = dataList[428],
	[16000442] = dataList[429],
	[16000443] = dataList[430],
	[16000444] = dataList[431],
	[16000445] = dataList[432],
	[16000446] = dataList[433],
	[16000447] = dataList[434],
	[16000448] = dataList[435],
	[16000449] = dataList[436],
	[16000450] = dataList[437],
	[16000451] = dataList[438],
	[16000452] = dataList[439],
	[16000453] = dataList[440],
	[16000454] = dataList[441],
	[16000455] = dataList[442],
	[16000456] = dataList[443],
	[16000457] = dataList[444],
	[16000458] = dataList[445],
	[16000459] = dataList[446],
	[16000460] = dataList[447],
	[16000461] = dataList[448],
	[16000462] = dataList[449],
	[16000463] = dataList[450],
	[16000464] = dataList[451],
	[16000465] = dataList[452],
	[16000466] = dataList[453],
	[16000467] = dataList[454],
	[16000468] = dataList[455],
	[16000469] = dataList[456],
	[16000470] = dataList[457],
	[16000471] = dataList[458],
	[16000472] = dataList[459],
	[16000473] = dataList[460],
	[16000474] = dataList[461],
	[16000475] = dataList[462],
	[16000476] = dataList[463],
	[16000477] = dataList[464],
	[16000478] = dataList[465],
	[16000479] = dataList[466],
	[16000480] = dataList[467],
	[16000481] = dataList[468],
	[16000482] = dataList[469],
	[16000483] = dataList[470],
	[16000484] = dataList[471],
	[16000485] = dataList[472],
	[16000486] = dataList[473],
	[16000487] = dataList[474],
	[16000488] = dataList[475],
	[16000489] = dataList[476],
	[16000490] = dataList[477],
	[16000491] = dataList[478],
	[16000492] = dataList[479],
	[16000493] = dataList[480],
	[16000494] = dataList[481],
	[16000495] = dataList[482],
	[16000496] = dataList[483],
	[16000497] = dataList[484],
	[16000498] = dataList[485],
	[16000499] = dataList[486],
	[16000500] = dataList[487],
	[16000501] = dataList[488],
	[16000502] = dataList[489],
	[16000503] = dataList[490],
	[16000504] = dataList[491],
	[16000505] = dataList[492],
	[16000506] = dataList[493],
	[16000507] = dataList[494],
	[16000508] = dataList[495],
	[16000509] = dataList[496],
	[16000510] = dataList[497],
	[16000511] = dataList[498],
	[16000512] = dataList[499],
	[16000513] = dataList[500],
	[16000514] = dataList[501],
	[16000515] = dataList[502],
	[16000516] = dataList[503],
	[16000517] = dataList[504],
	[16000518] = dataList[505],
	[16000519] = dataList[506],
	[16000520] = dataList[507],
	[16000521] = dataList[508],
	[16000522] = dataList[509],
	[16000523] = dataList[510],
	[16000524] = dataList[511],
	[16000525] = dataList[512],
	[16000526] = dataList[513],
	[16000527] = dataList[514],
	[16000528] = dataList[515],
	[16000529] = dataList[516],
	[16000530] = dataList[517],
	[16000531] = dataList[518],
	[16000532] = dataList[519],
	[16000533] = dataList[520],
	[16000534] = dataList[521],
	[16000535] = dataList[522],
	[16000536] = dataList[523],
	[16000537] = dataList[524],
	[16000538] = dataList[525],
	[16000539] = dataList[526],
	[16000540] = dataList[527],
	[16000541] = dataList[528],
	[16000542] = dataList[529],
	[16000543] = dataList[530],
	[16000544] = dataList[531],
	[16000545] = dataList[532],
	[16000546] = dataList[533],
	[16000547] = dataList[534],
	[16000548] = dataList[535],
	[16000549] = dataList[536],
	[16000550] = dataList[537],
	[16000551] = dataList[538],
	[16000552] = dataList[539],
	[16000553] = dataList[540],
	[16000554] = dataList[541],
	[16000555] = dataList[542],
	[16000556] = dataList[543],
	[16000557] = dataList[544],
	[16000558] = dataList[545],
	[16000559] = dataList[546],
	[16000560] = dataList[547],
	[16000561] = dataList[548],
	[16000562] = dataList[549],
	[16000563] = dataList[550],
	[16000564] = dataList[551],
	[16000565] = dataList[552],
	[16000566] = dataList[553],
	[16000567] = dataList[554],
	[16000568] = dataList[555],
	[16000569] = dataList[556],
	[16000570] = dataList[557],
	[16000571] = dataList[558],
	[16000572] = dataList[559],
	[16000573] = dataList[560],
	[16000574] = dataList[561],
	[16000575] = dataList[562],
	[16000576] = dataList[563],
	[16000577] = dataList[564],
	[16000578] = dataList[565],
	[16000579] = dataList[566],
	[16000580] = dataList[567],
	[16000581] = dataList[568],
	[16000582] = dataList[569],
	[16000583] = dataList[570],
	[16000584] = dataList[571],
	[16000585] = dataList[572],
	[16000586] = dataList[573],
	[16000587] = dataList[574],
	[16000588] = dataList[575],
	[16000589] = dataList[576],
	[16000590] = dataList[577],
	[16000591] = dataList[578],
	[16000592] = dataList[579],
	[16000593] = dataList[580],
	[16000594] = dataList[581],
	[16000595] = dataList[582],
	[16000596] = dataList[583],
	[16000597] = dataList[584],
	[16000598] = dataList[585],
	[16000599] = dataList[586],
	[16000600] = dataList[587],
	[16000601] = dataList[588],
	[16000602] = dataList[589],
	[16000603] = dataList[590],
	[16000604] = dataList[591],
	[16000605] = dataList[592],
	[16000606] = dataList[593],
	[16000607] = dataList[594],
	[16000608] = dataList[595],
	[16000609] = dataList[596],
	[16000610] = dataList[597],
	[16000611] = dataList[598],
	[16000612] = dataList[599],
	[16000613] = dataList[600],
	[16000614] = dataList[601],
	[16000615] = dataList[602],
	[16000616] = dataList[603],
	[16000617] = dataList[604],
	[16000618] = dataList[605],
	[16000619] = dataList[606],
	[16000620] = dataList[607],
	[16000621] = dataList[608],
	[16000622] = dataList[609],
	[16000623] = dataList[610],
	[16000624] = dataList[611],
	[16000625] = dataList[612],
	[16000626] = dataList[613],
	[16000627] = dataList[614],
	[16000628] = dataList[615],
	[16000629] = dataList[616],
	[16000630] = dataList[617],
	[16000631] = dataList[618],
	[16000632] = dataList[619],
	[16000633] = dataList[620],
	[16000634] = dataList[621],
	[16000635] = dataList[622],
	[16000636] = dataList[623],
	[16000637] = dataList[624],
	[16000638] = dataList[625],
	[16000639] = dataList[626],
	[16000640] = dataList[627],
	[16000641] = dataList[628],
	[16000642] = dataList[629],
	[16000644] = dataList[630],
	[16000645] = dataList[631],
	[16000646] = dataList[632],
	[16000647] = dataList[633],
	[16000648] = dataList[634],
	[16000649] = dataList[635],
	[16000650] = dataList[636],
	[16000651] = dataList[637],
	[16000652] = dataList[638],
	[16000653] = dataList[639],
	[16000654] = dataList[640],
	[16000655] = dataList[641],
	[16000656] = dataList[642],
	[16000657] = dataList[643],
	[16000658] = dataList[644],
	[16000659] = dataList[645],
	[16000660] = dataList[646],
	[16000661] = dataList[647],
	[16000662] = dataList[648],
	[16000663] = dataList[649],
	[16000664] = dataList[650],
	[16000665] = dataList[651],
	[16000666] = dataList[652],
	[16000667] = dataList[653],
	[16000668] = dataList[654],
	[16000669] = dataList[655],
	[16000670] = dataList[656],
	[16000671] = dataList[657],
	[16000672] = dataList[658],
	[16000673] = dataList[659],
	[16000674] = dataList[660],
	[16000675] = dataList[661],
	[16000676] = dataList[662],
	[16000677] = dataList[663],
	[16000678] = dataList[664],
	[16000679] = dataList[665],
	[16000680] = dataList[666],
	[16000681] = dataList[667],
	[16000682] = dataList[668],
	[16000683] = dataList[669],
	[16000684] = dataList[670],
	[16000685] = dataList[671],
	[16000686] = dataList[672],
	[16000687] = dataList[673],
	[16000688] = dataList[674],
	[16000689] = dataList[675],
	[16000690] = dataList[676],
	[16000691] = dataList[677],
	[16000692] = dataList[678],
	[16000693] = dataList[679],
	[16000694] = dataList[680],
	[16000695] = dataList[681],
	[16000696] = dataList[682],
	[16000697] = dataList[683],
	[16000698] = dataList[684],
	[16000699] = dataList[685],
	[16000700] = dataList[686],
	[16000701] = dataList[687],
	[16000702] = dataList[688],
	[16000703] = dataList[689],
	[16000704] = dataList[690],
	[16000705] = dataList[691],
	[16000706] = dataList[692],
	[16000707] = dataList[693],
	[16000708] = dataList[694],
	[16000709] = dataList[695],
	[16000710] = dataList[696],
	[16000711] = dataList[697],
	[16000712] = dataList[698],
	[16000713] = dataList[699],
	[16000714] = dataList[700],
	[16000715] = dataList[701],
	[16000716] = dataList[702],
	[16000717] = dataList[703],
	[16000718] = dataList[704],
	[16000719] = dataList[705],
	[16000720] = dataList[706],
	[16000721] = dataList[707],
	[16000722] = dataList[708],
	[16000723] = dataList[709],
	[16000724] = dataList[710],
	[16000725] = dataList[711],
	[16000726] = dataList[712],
	[16000727] = dataList[713],
	[16000728] = dataList[714],
	[16000729] = dataList[715],
	[16000730] = dataList[716],
	[16000731] = dataList[717],
	[16000732] = dataList[718],
	[16000733] = dataList[719],
	[16000734] = dataList[720],
	[16000735] = dataList[721],
	[16000736] = dataList[722],
	[16000737] = dataList[723],
	[16000738] = dataList[724],
	[16000739] = dataList[725],
	[16000740] = dataList[726],
	[16000741] = dataList[727],
	[16000742] = dataList[728],
	[16000743] = dataList[729],
	[16000744] = dataList[730],
	[16000745] = dataList[731],
	[16000746] = dataList[732],
	[16000747] = dataList[733],
	[16000748] = dataList[734],
	[16000749] = dataList[735],
	[16000750] = dataList[736],
	[16000751] = dataList[737],
	[16000752] = dataList[738],
	[16000753] = dataList[739],
	[16000754] = dataList[740],
	[16000755] = dataList[741],
	[16000756] = dataList[742],
	[16000757] = dataList[743],
	[16000758] = dataList[744],
	[16000759] = dataList[745],
	[16000760] = dataList[746],
	[16000761] = dataList[747],
	[16000762] = dataList[748],
	[16000763] = dataList[749],
	[16000764] = dataList[750],
	[16000765] = dataList[751],
	[16000766] = dataList[752],
	[16000767] = dataList[753],
	[16000768] = dataList[754],
	[16000769] = dataList[755],
	[16000770] = dataList[756],
	[16000771] = dataList[757],
	[16000772] = dataList[758],
	[16000773] = dataList[759],
	[16000774] = dataList[760],
	[16000775] = dataList[761],
	[16000776] = dataList[762],
	[16000779] = dataList[763],
	[16000781] = dataList[764],
	[16000782] = dataList[765],
	[16000783] = dataList[766],
	[16000784] = dataList[767],
	[16000785] = dataList[768],
	[16000786] = dataList[769],
	[16000787] = dataList[770],
	[16000788] = dataList[771],
	[16000789] = dataList[772],
	[16000790] = dataList[773],
	[16000791] = dataList[774],
	[16000792] = dataList[775],
	[16000793] = dataList[776],
	[16000794] = dataList[777],
	[16000795] = dataList[778],
	[16000796] = dataList[779],
	[16000797] = dataList[780],
	[16000798] = dataList[781],
	[16000799] = dataList[782],
	[16000800] = dataList[783],
	[16000801] = dataList[784],
	[16000802] = dataList[785],
	[16000803] = dataList[786],
	[16000804] = dataList[787],
	[16000805] = dataList[788],
	[16000806] = dataList[789],
	[16000807] = dataList[790],
	[16000808] = dataList[791],
	[16000809] = dataList[792],
	[16000810] = dataList[793],
	[16000811] = dataList[794],
	[16000812] = dataList[795],
	[16000813] = dataList[796],
	[16000814] = dataList[797],
	[16000815] = dataList[798],
	[16001001] = dataList[799],
	[16001002] = dataList[800],
	[16001003] = dataList[801],
	[16001004] = dataList[802],
	[16001011] = dataList[803],
	[16001012] = dataList[804],
	[16001013] = dataList[805],
	[16001014] = dataList[806],
	[16001021] = dataList[807],
	[16001022] = dataList[808],
	[16001023] = dataList[809],
	[16001024] = dataList[810],
	[16001031] = dataList[811],
	[16001041] = dataList[812],
	[16001051] = dataList[813],
	[16002001] = dataList[814],
	[16002002] = dataList[815],
	[16002003] = dataList[816],
	[16002004] = dataList[817],
	[16002011] = dataList[818],
	[16002012] = dataList[819],
	[16002013] = dataList[820],
	[16002014] = dataList[821],
	[16002021] = dataList[822],
	[16002022] = dataList[823],
	[16002023] = dataList[824],
	[16002024] = dataList[825],
	[16002031] = dataList[826],
	[16002041] = dataList[827],
	[16002051] = dataList[828],
	[16003001] = dataList[829],
	[16003002] = dataList[830],
	[16003003] = dataList[831],
	[16003004] = dataList[832],
	[16003005] = dataList[833],
	[16003006] = dataList[834],
	[16003007] = dataList[835],
	[16003008] = dataList[836],
	[16003009] = dataList[837],
	[16003010] = dataList[838],
	[16004001] = dataList[839],
	[16004002] = dataList[840],
	[16004003] = dataList[841],
	[16004004] = dataList[842],
	[16004005] = dataList[843],
	[16004006] = dataList[844],
	[16004007] = dataList[845],
	[16004008] = dataList[846],
	[16004009] = dataList[847],
	[16004010] = dataList[848],
	[16004011] = dataList[849],
	[16004012] = dataList[850],
	[16004013] = dataList[851],
	[16004014] = dataList[852],
	[16004015] = dataList[853],
	[16004016] = dataList[854],
	[16004017] = dataList[855],
	[16004018] = dataList[856],
	[16004019] = dataList[857],
	[16004020] = dataList[858],
	[16004021] = dataList[859],
	[16004022] = dataList[860],
	[16004023] = dataList[861],
	[16004024] = dataList[862],
	[16004025] = dataList[863],
	[16004026] = dataList[864],
	[16004027] = dataList[865],
	[16004028] = dataList[866],
	[16004029] = dataList[867],
	[16004030] = dataList[868],
	[16004031] = dataList[869],
	[16004032] = dataList[870],
	[16004033] = dataList[871],
	[16004034] = dataList[872],
	[16004035] = dataList[873],
	[16004036] = dataList[874],
	[16004037] = dataList[875],
	[16004038] = dataList[876],
	[16004039] = dataList[877],
	[16004040] = dataList[878],
	[16004041] = dataList[879],
	[16004042] = dataList[880],
	[16004043] = dataList[881],
	[16004044] = dataList[882],
	[16004045] = dataList[883],
	[16004046] = dataList[884],
	[16004047] = dataList[885],
	[16004048] = dataList[886],
	[16004049] = dataList[887],
	[16004050] = dataList[888],
	[16004051] = dataList[889],
	[16004052] = dataList[890],
	[16004053] = dataList[891],
	[16004501] = dataList[892],
	[16004502] = dataList[893],
	[16004503] = dataList[894],
	[16004504] = dataList[895],
	[16004601] = dataList[896],
	[16004602] = dataList[897],
	[16004603] = dataList[898],
	[16004701] = dataList[899],
	[16004702] = dataList[900],
	[16005001] = dataList[901],
	[16005002] = dataList[902],
	[16006001] = dataList[903],
	[16006002] = dataList[904],
	[16006003] = dataList[905],
	[16006004] = dataList[906],
	[16006005] = dataList[907],
	[16006006] = dataList[908],
	[16006007] = dataList[909],
	[16006008] = dataList[910],
	[16006009] = dataList[911],
	[16006010] = dataList[912],
	[16006011] = dataList[913],
	[16006012] = dataList[914],
	[16007001] = dataList[915],
	[16007002] = dataList[916],
	[16007003] = dataList[917],
	[16007004] = dataList[918],
	[16007005] = dataList[919],
	[16007006] = dataList[920],
	[16007007] = dataList[921],
	[16007008] = dataList[922],
	[16007009] = dataList[923],
	[16007010] = dataList[924],
	[16007011] = dataList[925],
	[16007012] = dataList[926],
	[16007013] = dataList[927],
	[16009000] = dataList[928],
	[16009001] = dataList[929],
	[16009002] = dataList[930],
	[16009003] = dataList[931],
	[16009010] = dataList[932],
	[16009011] = dataList[933],
	[16009012] = dataList[934],
	[16009013] = dataList[935],
	[16009020] = dataList[936],
	[16009021] = dataList[937],
	[16009022] = dataList[938],
	[16009023] = dataList[939],
	[16010001] = dataList[940],
	[16010002] = dataList[941],
	[16010003] = dataList[942],
	[16010004] = dataList[943],
	[16010005] = dataList[944],
	[16010006] = dataList[945],
	[16010007] = dataList[946],
	[16010008] = dataList[947],
	[16010009] = dataList[948],
	[16010010] = dataList[949],
	[16010011] = dataList[950],
	[16010012] = dataList[951],
	[16010013] = dataList[952],
	[16010014] = dataList[953],
	[16010015] = dataList[954],
	[16010016] = dataList[955],
	[16010017] = dataList[956],
	[16010018] = dataList[957],
	[16010019] = dataList[958],
	[16010020] = dataList[959],
	[16010021] = dataList[960],
	[16010022] = dataList[961],
	[16010023] = dataList[962],
	[16010501] = dataList[963],
	[16010502] = dataList[964],
	[16010503] = dataList[965],
	[16020001] = dataList[966],
	[16020002] = dataList[967],
	[16020003] = dataList[968],
	[16020004] = dataList[969],
	[16020005] = dataList[970],
	[16020006] = dataList[971],
	[16020007] = dataList[972],
	[16020008] = dataList[973],
	[16020009] = dataList[974],
	[16020010] = dataList[975],
	[16020011] = dataList[976],
	[16020012] = dataList[977],
	[16020013] = dataList[978],
	[16020014] = dataList[979],
	[16020015] = dataList[980],
	[16020016] = dataList[981],
	[16020017] = dataList[982],
	[16020018] = dataList[983],
	[16020019] = dataList[984],
	[16020020] = dataList[985],
	[16020021] = dataList[986],
	[16020022] = dataList[987],
	[16020023] = dataList[988],
	[16020024] = dataList[989],
	[16020025] = dataList[990],
	[16020026] = dataList[991],
	[16020027] = dataList[992],
	[16020028] = dataList[993],
	[16020029] = dataList[994],
	[16020030] = dataList[995],
	[16020031] = dataList[996],
	[16020032] = dataList[997],
	[16020033] = dataList[998],
	[16020034] = dataList[999],
	[16020035] = dataList[1000],
	[16020036] = dataList[1001],
	[16020037] = dataList[1002],
	[16020038] = dataList[1003],
	[16020039] = dataList[1004],
	[16020040] = dataList[1005],
	[16020041] = dataList[1006],
	[16020042] = dataList[1007],
	[16020043] = dataList[1008],
	[16020044] = dataList[1009],
	[16020045] = dataList[1010],
	[16020046] = dataList[1011],
	[16020047] = dataList[1012],
	[16020048] = dataList[1013],
	[16020049] = dataList[1014],
	[16020050] = dataList[1015],
	[16020051] = dataList[1016],
	[16020052] = dataList[1017],
	[16020053] = dataList[1018],
	[16020054] = dataList[1019],
	[16020055] = dataList[1020],
	[16020056] = dataList[1021],
	[16020057] = dataList[1022],
	[16020058] = dataList[1023],
	[16020059] = dataList[1024],
	[16020060] = dataList[1025],
	[16020061] = dataList[1026],
	[16020062] = dataList[1027],
	[16020063] = dataList[1028],
	[16020064] = dataList[1029],
	[16020065] = dataList[1030],
	[16020066] = dataList[1031],
	[16020067] = dataList[1032],
	[16020068] = dataList[1033],
	[16020069] = dataList[1034],
	[16020070] = dataList[1035],
	[16020071] = dataList[1036],
	[16020072] = dataList[1037],
	[16020073] = dataList[1038],
	[16020074] = dataList[1039],
	[16030001] = dataList[1040],
	[16030002] = dataList[1041],
	[16030003] = dataList[1042],
	[16030004] = dataList[1043],
	[16030005] = dataList[1044],
	[16030006] = dataList[1045],
	[16030007] = dataList[1046],
	[16030008] = dataList[1047],
	[16030009] = dataList[1048],
	[16030010] = dataList[1049],
	[16030011] = dataList[1050],
	[16030012] = dataList[1051],
	[16030013] = dataList[1052],
	[16030014] = dataList[1053],
	[16030015] = dataList[1054],
	[16030016] = dataList[1055],
	[16030017] = dataList[1056],
	[16030018] = dataList[1057],
	[16030019] = dataList[1058],
	[16030020] = dataList[1059],
	[16030021] = dataList[1060],
	[16030022] = dataList[1061],
	[16030023] = dataList[1062],
	[16030024] = dataList[1063],
	[16030025] = dataList[1064],
	[16030026] = dataList[1065],
	[16030027] = dataList[1066],
	[16030028] = dataList[1067],
	[16030029] = dataList[1068],
	[16030030] = dataList[1069],
	[16030031] = dataList[1070],
	[16030032] = dataList[1071],
	[16030033] = dataList[1072],
	[16030034] = dataList[1073],
	[16030035] = dataList[1074],
	[16030036] = dataList[1075],
	[16030037] = dataList[1076],
	[16030038] = dataList[1077],
	[16030039] = dataList[1078],
	[16030040] = dataList[1079],
	[16030041] = dataList[1080],
	[16030042] = dataList[1081],
	[16040001] = dataList[1082],
	[16040002] = dataList[1083],
	[16040003] = dataList[1084],
	[16040004] = dataList[1085],
	[16040005] = dataList[1086],
	[16040006] = dataList[1087],
	[16040007] = dataList[1088],
	[16040008] = dataList[1089],
	[16040009] = dataList[1090],
	[16040010] = dataList[1091],
	[16040011] = dataList[1092],
	[16040012] = dataList[1093],
	[16040013] = dataList[1094],
	[16040014] = dataList[1095],
	[16040015] = dataList[1096],
	[16040016] = dataList[1097],
	[16040017] = dataList[1098],
	[16040018] = dataList[1099],
	[16040019] = dataList[1100],
	[16040020] = dataList[1101],
	[16040021] = dataList[1102],
	[16040022] = dataList[1103],
	[16040023] = dataList[1104],
	[16040024] = dataList[1105],
	[16040025] = dataList[1106],
	[16040026] = dataList[1107],
	[16040027] = dataList[1108],
	[16040028] = dataList[1109],
	[16040029] = dataList[1110],
	[16040030] = dataList[1111],
	[16040031] = dataList[1112],
	[16040032] = dataList[1113],
	[16040033] = dataList[1114],
	[16040034] = dataList[1115],
	[16040035] = dataList[1116],
	[16040036] = dataList[1117],
	[16040037] = dataList[1118],
	[16040038] = dataList[1119],
	[16050001] = dataList[1120],
	[16050002] = dataList[1121],
	[16060101] = dataList[1122],
	[16060102] = dataList[1123],
	[16060103] = dataList[1124],
	[16060104] = dataList[1125],
	[16060105] = dataList[1126],
	[16060106] = dataList[1127],
	[16060201] = dataList[1128],
	[16060202] = dataList[1129],
	[16060203] = dataList[1130],
	[16060204] = dataList[1131],
	[16060205] = dataList[1132],
	[16060206] = dataList[1133],
	[16070100] = dataList[1134],
	[16070101] = dataList[1135],
	[16070102] = dataList[1136],
	[16070200] = dataList[1137],
	[16070300] = dataList[1138],
	[16070301] = dataList[1139],
	[16070302] = dataList[1140],
	[16070400] = dataList[1141],
	[16070401] = dataList[1142],
	[16070402] = dataList[1143],
	[16070500] = dataList[1144],
	[16070501] = dataList[1145],
	[16070502] = dataList[1146],
	[16070600] = dataList[1147],
	[16070601] = dataList[1148],
	[16070602] = dataList[1149],
	[16070700] = dataList[1150],
	[16070701] = dataList[1151],
	[16070702] = dataList[1152],
	[16070703] = dataList[1153],
	[16070800] = dataList[1154],
	[16070801] = dataList[1155],
	[16070802] = dataList[1156],
	[16070900] = dataList[1157],
	[16070901] = dataList[1158],
	[16070902] = dataList[1159],
	[16070903] = dataList[1160],
	[16071000] = dataList[1161],
	[16071001] = dataList[1162],
	[16071002] = dataList[1163],
	[16071100] = dataList[1164],
	[16071101] = dataList[1165],
	[16071102] = dataList[1166],
	[16071200] = dataList[1167],
	[16071201] = dataList[1168],
	[16071202] = dataList[1169],
	[16071203] = dataList[1170],
	[16071300] = dataList[1171],
	[16071301] = dataList[1172],
	[16071302] = dataList[1173],
	[16071400] = dataList[1174],
	[16071401] = dataList[1175],
	[16071402] = dataList[1176],
	[16071403] = dataList[1177],
	[16071500] = dataList[1178],
	[16071501] = dataList[1179],
	[16071502] = dataList[1180],
	[16071503] = dataList[1181],
	[16080001] = dataList[1182],
	[16080002] = dataList[1183],
	[16080003] = dataList[1184],
	[16080004] = dataList[1185],
	[16080005] = dataList[1186],
	[16080006] = dataList[1187],
	[16080007] = dataList[1188],
	[16080008] = dataList[1189],
	[16080009] = dataList[1190],
	[16080010] = dataList[1191],
	[16080011] = dataList[1192],
	[16080012] = dataList[1193],
	[16080013] = dataList[1194],
	[16080014] = dataList[1195],
	[16080015] = dataList[1196],
	[16080016] = dataList[1197],
	[16080017] = dataList[1198],
	[16080018] = dataList[1199],
	[16080019] = dataList[1200],
	[16080020] = dataList[1201],
	[16081001] = dataList[1202],
	[16081002] = dataList[1203],
	[16081003] = dataList[1204],
	[16081004] = dataList[1205],
	[16081005] = dataList[1206],
	[16081006] = dataList[1207],
	[16081007] = dataList[1208],
	[16081008] = dataList[1209],
	[16081009] = dataList[1210],
	[16081010] = dataList[1211],
	[16081011] = dataList[1212],
	[16081012] = dataList[1213],
	[16081013] = dataList[1214],
	[16081014] = dataList[1215],
	[16081015] = dataList[1216],
	[16081016] = dataList[1217],
	[16081017] = dataList[1218],
	[16081018] = dataList[1219],
	[16081019] = dataList[1220],
	[16081020] = dataList[1221],
	[16090001] = dataList[1222],
	[16090002] = dataList[1223],
	[16090003] = dataList[1224],
	[16090004] = dataList[1225],
	[16090005] = dataList[1226],
	[16090006] = dataList[1227],
	[16090007] = dataList[1228],
	[16090008] = dataList[1229],
	[16090009] = dataList[1230],
	[16091001] = dataList[1231],
	[16091002] = dataList[1232],
	[16091003] = dataList[1233],
	[16091004] = dataList[1234],
	[16091005] = dataList[1235],
	[16100011] = dataList[1236],
	[16100012] = dataList[1237],
	[16100013] = dataList[1238],
	[16100014] = dataList[1239],
	[16100015] = dataList[1240],
	[16100016] = dataList[1241],
	[16100017] = dataList[1242],
	[16100018] = dataList[1243],
	[16100019] = dataList[1244],
	[16100021] = dataList[1245],
	[16100022] = dataList[1246],
	[16100023] = dataList[1247],
	[16100024] = dataList[1248],
	[16100025] = dataList[1249],
	[16100026] = dataList[1250],
	[16100027] = dataList[1251],
	[16100028] = dataList[1252],
	[16100029] = dataList[1253],
	[16100031] = dataList[1254],
	[16100032] = dataList[1255],
	[16100033] = dataList[1256],
	[16100034] = dataList[1257],
	[16100035] = dataList[1258],
	[16100036] = dataList[1259],
	[16100037] = dataList[1260],
	[16100038] = dataList[1261],
	[16100039] = dataList[1262],
	[16100041] = dataList[1263],
	[16100042] = dataList[1264],
	[16100043] = dataList[1265],
	[16100044] = dataList[1266],
	[16100045] = dataList[1267],
	[16100046] = dataList[1268],
	[16100047] = dataList[1269],
	[16100048] = dataList[1270],
	[16100049] = dataList[1271],
	[16100051] = dataList[1272],
	[16100052] = dataList[1273],
	[16100053] = dataList[1274],
	[16100054] = dataList[1275],
	[16100055] = dataList[1276],
	[16100056] = dataList[1277],
	[16100057] = dataList[1278],
	[16100058] = dataList[1279],
	[16100059] = dataList[1280],
	[16100061] = dataList[1281],
	[16100062] = dataList[1282],
	[16100063] = dataList[1283],
	[16100064] = dataList[1284],
	[16100065] = dataList[1285],
	[16100066] = dataList[1286],
	[16100067] = dataList[1287],
	[16100068] = dataList[1288],
	[16100069] = dataList[1289],
	[16100071] = dataList[1290],
	[16100072] = dataList[1291],
	[16100073] = dataList[1292],
	[16100074] = dataList[1293],
	[16100075] = dataList[1294],
	[16100076] = dataList[1295],
	[16100077] = dataList[1296],
	[16100078] = dataList[1297],
	[16100079] = dataList[1298],
	[16100081] = dataList[1299],
	[16100082] = dataList[1300],
	[16100083] = dataList[1301],
	[16100084] = dataList[1302],
	[16100085] = dataList[1303],
	[16100086] = dataList[1304],
	[16100087] = dataList[1305],
	[16100088] = dataList[1306],
	[16100089] = dataList[1307],
	[16100091] = dataList[1308],
	[16100092] = dataList[1309],
	[16100093] = dataList[1310],
	[16100094] = dataList[1311],
	[16100095] = dataList[1312],
	[16100096] = dataList[1313],
	[16100097] = dataList[1314],
	[16100098] = dataList[1315],
	[16100099] = dataList[1316],
	[16100101] = dataList[1317],
	[16100102] = dataList[1318],
	[16100103] = dataList[1319],
	[16100104] = dataList[1320],
	[16100105] = dataList[1321],
	[16100106] = dataList[1322],
	[16100107] = dataList[1323],
	[16100108] = dataList[1324],
	[16100109] = dataList[1325],
	[16100111] = dataList[1326],
	[16100112] = dataList[1327],
	[16100113] = dataList[1328],
	[16100114] = dataList[1329],
	[16100115] = dataList[1330],
	[16100116] = dataList[1331],
	[16100117] = dataList[1332],
	[16100118] = dataList[1333],
	[16100119] = dataList[1334],
	[16100121] = dataList[1335],
	[16100122] = dataList[1336],
	[16100123] = dataList[1337],
	[16100124] = dataList[1338],
	[16100125] = dataList[1339],
	[16100126] = dataList[1340],
	[16100127] = dataList[1341],
	[16100128] = dataList[1342],
	[16100129] = dataList[1343],
	[16100131] = dataList[1344],
	[16100132] = dataList[1345],
	[16100133] = dataList[1346],
	[16100134] = dataList[1347],
	[16100135] = dataList[1348],
	[16100136] = dataList[1349],
	[16100137] = dataList[1350],
	[16100138] = dataList[1351],
	[16100139] = dataList[1352],
	[16100141] = dataList[1353],
	[16100142] = dataList[1354],
	[16100143] = dataList[1355],
	[16100144] = dataList[1356],
	[16100145] = dataList[1357],
	[16100146] = dataList[1358],
	[16100147] = dataList[1359],
	[16100148] = dataList[1360],
	[16100149] = dataList[1361],
	[16100151] = dataList[1362],
	[16100152] = dataList[1363],
	[16100153] = dataList[1364],
	[16100154] = dataList[1365],
	[16100155] = dataList[1366],
	[16100156] = dataList[1367],
	[16100157] = dataList[1368],
	[16100158] = dataList[1369],
	[16100159] = dataList[1370],
	[16101011] = dataList[1371],
	[16101012] = dataList[1372],
	[16101013] = dataList[1373],
	[16101014] = dataList[1374],
	[16101015] = dataList[1375],
	[16101016] = dataList[1376],
	[16101017] = dataList[1377],
	[16101018] = dataList[1378],
	[16101019] = dataList[1379],
	[16101021] = dataList[1380],
	[16101022] = dataList[1381],
	[16101023] = dataList[1382],
	[16101024] = dataList[1383],
	[16101025] = dataList[1384],
	[16101026] = dataList[1385],
	[16101027] = dataList[1386],
	[16101028] = dataList[1387],
	[16101029] = dataList[1388],
	[16101031] = dataList[1389],
	[16101032] = dataList[1390],
	[16101033] = dataList[1391],
	[16101034] = dataList[1392],
	[16101035] = dataList[1393],
	[16101036] = dataList[1394],
	[16101037] = dataList[1395],
	[16101038] = dataList[1396],
	[16101039] = dataList[1397],
	[16101041] = dataList[1398],
	[16101042] = dataList[1399],
	[16101043] = dataList[1400],
	[16101044] = dataList[1401],
	[16101045] = dataList[1402],
	[16101046] = dataList[1403],
	[16101047] = dataList[1404],
	[16101048] = dataList[1405],
	[16101049] = dataList[1406],
	[16101051] = dataList[1407],
	[16101052] = dataList[1408],
	[16101053] = dataList[1409],
	[16101054] = dataList[1410],
	[16101055] = dataList[1411],
	[16101056] = dataList[1412],
	[16101057] = dataList[1413],
	[16101058] = dataList[1414],
	[16101059] = dataList[1415],
	[16101061] = dataList[1416],
	[16101062] = dataList[1417],
	[16101063] = dataList[1418],
	[16101064] = dataList[1419],
	[16101065] = dataList[1420],
	[16101066] = dataList[1421],
	[16101067] = dataList[1422],
	[16101068] = dataList[1423],
	[16101069] = dataList[1424],
	[16101071] = dataList[1425],
	[16101072] = dataList[1426],
	[16101073] = dataList[1427],
	[16101074] = dataList[1428],
	[16101075] = dataList[1429],
	[16101076] = dataList[1430],
	[16101077] = dataList[1431],
	[16101078] = dataList[1432],
	[16101079] = dataList[1433],
	[16101081] = dataList[1434],
	[16101082] = dataList[1435],
	[16101083] = dataList[1436],
	[16101084] = dataList[1437],
	[16101085] = dataList[1438],
	[16101086] = dataList[1439],
	[16101087] = dataList[1440],
	[16101088] = dataList[1441],
	[16101089] = dataList[1442],
	[16101091] = dataList[1443],
	[16101092] = dataList[1444],
	[16101093] = dataList[1445],
	[16101094] = dataList[1446],
	[16101095] = dataList[1447],
	[16101096] = dataList[1448],
	[16101097] = dataList[1449],
	[16101098] = dataList[1450],
	[16101099] = dataList[1451],
	[16101101] = dataList[1452],
	[16101102] = dataList[1453],
	[16101103] = dataList[1454],
	[16101104] = dataList[1455],
	[16101105] = dataList[1456],
	[16101106] = dataList[1457],
	[16101107] = dataList[1458],
	[16101108] = dataList[1459],
	[16101109] = dataList[1460],
	[16101111] = dataList[1461],
	[16101112] = dataList[1462],
	[16101113] = dataList[1463],
	[16101114] = dataList[1464],
	[16101115] = dataList[1465],
	[16101116] = dataList[1466],
	[16101117] = dataList[1467],
	[16101118] = dataList[1468],
	[16101119] = dataList[1469],
	[16101121] = dataList[1470],
	[16101122] = dataList[1471],
	[16101123] = dataList[1472],
	[16101124] = dataList[1473],
	[16101125] = dataList[1474],
	[16101126] = dataList[1475],
	[16101127] = dataList[1476],
	[16101128] = dataList[1477],
	[16101129] = dataList[1478],
	[16101131] = dataList[1479],
	[16101132] = dataList[1480],
	[16101133] = dataList[1481],
	[16101134] = dataList[1482],
	[16101135] = dataList[1483],
	[16101136] = dataList[1484],
	[16101137] = dataList[1485],
	[16101138] = dataList[1486],
	[16101139] = dataList[1487],
	[16101141] = dataList[1488],
	[16101142] = dataList[1489],
	[16101143] = dataList[1490],
	[16101144] = dataList[1491],
	[16101145] = dataList[1492],
	[16101146] = dataList[1493],
	[16101147] = dataList[1494],
	[16101148] = dataList[1495],
	[16101149] = dataList[1496],
	[16101151] = dataList[1497],
	[16101152] = dataList[1498],
	[16101153] = dataList[1499],
	[16101154] = dataList[1500],
	[16101155] = dataList[1501],
	[16101156] = dataList[1502],
	[16101157] = dataList[1503],
	[16101158] = dataList[1504],
	[16101159] = dataList[1505],
	[16102011] = dataList[1506],
	[16102012] = dataList[1507],
	[16102013] = dataList[1508],
	[16102014] = dataList[1509],
	[16102015] = dataList[1510],
	[16102016] = dataList[1511],
	[16102017] = dataList[1512],
	[16102018] = dataList[1513],
	[16102019] = dataList[1514],
	[16102021] = dataList[1515],
	[16102022] = dataList[1516],
	[16102023] = dataList[1517],
	[16102024] = dataList[1518],
	[16102025] = dataList[1519],
	[16102026] = dataList[1520],
	[16102027] = dataList[1521],
	[16102028] = dataList[1522],
	[16102029] = dataList[1523],
	[16102031] = dataList[1524],
	[16102032] = dataList[1525],
	[16102033] = dataList[1526],
	[16102034] = dataList[1527],
	[16102035] = dataList[1528],
	[16102036] = dataList[1529],
	[16102037] = dataList[1530],
	[16102038] = dataList[1531],
	[16102039] = dataList[1532],
	[16102041] = dataList[1533],
	[16102042] = dataList[1534],
	[16102043] = dataList[1535],
	[16102044] = dataList[1536],
	[16102045] = dataList[1537],
	[16102046] = dataList[1538],
	[16102047] = dataList[1539],
	[16102048] = dataList[1540],
	[16102049] = dataList[1541],
	[16102051] = dataList[1542],
	[16102052] = dataList[1543],
	[16102053] = dataList[1544],
	[16102054] = dataList[1545],
	[16102055] = dataList[1546],
	[16102056] = dataList[1547],
	[16102057] = dataList[1548],
	[16102058] = dataList[1549],
	[16102059] = dataList[1550],
	[16102061] = dataList[1551],
	[16102062] = dataList[1552],
	[16102063] = dataList[1553],
	[16102064] = dataList[1554],
	[16102065] = dataList[1555],
	[16102066] = dataList[1556],
	[16102067] = dataList[1557],
	[16102068] = dataList[1558],
	[16102069] = dataList[1559],
	[16102071] = dataList[1560],
	[16102072] = dataList[1561],
	[16102073] = dataList[1562],
	[16102074] = dataList[1563],
	[16102075] = dataList[1564],
	[16102076] = dataList[1565],
	[16102077] = dataList[1566],
	[16102078] = dataList[1567],
	[16102079] = dataList[1568],
	[16102081] = dataList[1569],
	[16102082] = dataList[1570],
	[16102083] = dataList[1571],
	[16102084] = dataList[1572],
	[16102085] = dataList[1573],
	[16102086] = dataList[1574],
	[16102087] = dataList[1575],
	[16102088] = dataList[1576],
	[16102089] = dataList[1577],
	[16102091] = dataList[1578],
	[16102092] = dataList[1579],
	[16102093] = dataList[1580],
	[16102094] = dataList[1581],
	[16102095] = dataList[1582],
	[16102096] = dataList[1583],
	[16102097] = dataList[1584],
	[16102098] = dataList[1585],
	[16102099] = dataList[1586],
	[16102101] = dataList[1587],
	[16102102] = dataList[1588],
	[16102103] = dataList[1589],
	[16102104] = dataList[1590],
	[16102105] = dataList[1591],
	[16102106] = dataList[1592],
	[16102107] = dataList[1593],
	[16102108] = dataList[1594],
	[16102109] = dataList[1595],
	[16102111] = dataList[1596],
	[16102112] = dataList[1597],
	[16102113] = dataList[1598],
	[16102114] = dataList[1599],
	[16102115] = dataList[1600],
	[16102116] = dataList[1601],
	[16102117] = dataList[1602],
	[16102118] = dataList[1603],
	[16102119] = dataList[1604],
	[16102121] = dataList[1605],
	[16102122] = dataList[1606],
	[16102123] = dataList[1607],
	[16102124] = dataList[1608],
	[16102125] = dataList[1609],
	[16102126] = dataList[1610],
	[16102127] = dataList[1611],
	[16102128] = dataList[1612],
	[16102129] = dataList[1613],
	[16102131] = dataList[1614],
	[16102132] = dataList[1615],
	[16102133] = dataList[1616],
	[16102134] = dataList[1617],
	[16102135] = dataList[1618],
	[16102136] = dataList[1619],
	[16102137] = dataList[1620],
	[16102138] = dataList[1621],
	[16102139] = dataList[1622],
	[16102141] = dataList[1623],
	[16102142] = dataList[1624],
	[16102143] = dataList[1625],
	[16102144] = dataList[1626],
	[16102145] = dataList[1627],
	[16102146] = dataList[1628],
	[16102147] = dataList[1629],
	[16102148] = dataList[1630],
	[16102149] = dataList[1631],
	[16102151] = dataList[1632],
	[16102152] = dataList[1633],
	[16102153] = dataList[1634],
	[16102154] = dataList[1635],
	[16102155] = dataList[1636],
	[16102156] = dataList[1637],
	[16102157] = dataList[1638],
	[16102158] = dataList[1639],
	[16102159] = dataList[1640],
	[16103011] = dataList[1641],
	[16103012] = dataList[1642],
	[16103013] = dataList[1643],
	[16103014] = dataList[1644],
	[16103015] = dataList[1645],
	[16103016] = dataList[1646],
	[16103017] = dataList[1647],
	[16103018] = dataList[1648],
	[16103019] = dataList[1649],
	[16103021] = dataList[1650],
	[16103022] = dataList[1651],
	[16103023] = dataList[1652],
	[16103024] = dataList[1653],
	[16103025] = dataList[1654],
	[16103026] = dataList[1655],
	[16103027] = dataList[1656],
	[16103028] = dataList[1657],
	[16103029] = dataList[1658],
	[16103031] = dataList[1659],
	[16103032] = dataList[1660],
	[16103033] = dataList[1661],
	[16103034] = dataList[1662],
	[16103035] = dataList[1663],
	[16103036] = dataList[1664],
	[16103037] = dataList[1665],
	[16103038] = dataList[1666],
	[16103039] = dataList[1667],
	[16103041] = dataList[1668],
	[16103042] = dataList[1669],
	[16103043] = dataList[1670],
	[16103044] = dataList[1671],
	[16103045] = dataList[1672],
	[16103046] = dataList[1673],
	[16103047] = dataList[1674],
	[16103048] = dataList[1675],
	[16103049] = dataList[1676],
	[16103051] = dataList[1677],
	[16103052] = dataList[1678],
	[16103053] = dataList[1679],
	[16103054] = dataList[1680],
	[16103055] = dataList[1681],
	[16103056] = dataList[1682],
	[16103057] = dataList[1683],
	[16103058] = dataList[1684],
	[16103059] = dataList[1685],
	[16103061] = dataList[1686],
	[16103062] = dataList[1687],
	[16103063] = dataList[1688],
	[16103064] = dataList[1689],
	[16103065] = dataList[1690],
	[16103066] = dataList[1691],
	[16103067] = dataList[1692],
	[16103068] = dataList[1693],
	[16103069] = dataList[1694],
	[16103071] = dataList[1695],
	[16103072] = dataList[1696],
	[16103073] = dataList[1697],
	[16103074] = dataList[1698],
	[16103075] = dataList[1699],
	[16103076] = dataList[1700],
	[16103077] = dataList[1701],
	[16103078] = dataList[1702],
	[16103079] = dataList[1703],
	[16103081] = dataList[1704],
	[16103082] = dataList[1705],
	[16103083] = dataList[1706],
	[16103084] = dataList[1707],
	[16103085] = dataList[1708],
	[16103086] = dataList[1709],
	[16103087] = dataList[1710],
	[16103088] = dataList[1711],
	[16103089] = dataList[1712],
	[16103091] = dataList[1713],
	[16103092] = dataList[1714],
	[16103093] = dataList[1715],
	[16103094] = dataList[1716],
	[16103095] = dataList[1717],
	[16103096] = dataList[1718],
	[16103097] = dataList[1719],
	[16103098] = dataList[1720],
	[16103099] = dataList[1721],
	[16103101] = dataList[1722],
	[16103102] = dataList[1723],
	[16103103] = dataList[1724],
	[16103104] = dataList[1725],
	[16103105] = dataList[1726],
	[16103106] = dataList[1727],
	[16103107] = dataList[1728],
	[16103108] = dataList[1729],
	[16103109] = dataList[1730],
	[16103111] = dataList[1731],
	[16103112] = dataList[1732],
	[16103113] = dataList[1733],
	[16103114] = dataList[1734],
	[16103115] = dataList[1735],
	[16103116] = dataList[1736],
	[16103117] = dataList[1737],
	[16103118] = dataList[1738],
	[16103119] = dataList[1739],
	[16103121] = dataList[1740],
	[16103122] = dataList[1741],
	[16103123] = dataList[1742],
	[16103124] = dataList[1743],
	[16103125] = dataList[1744],
	[16103126] = dataList[1745],
	[16103127] = dataList[1746],
	[16103128] = dataList[1747],
	[16103129] = dataList[1748],
	[16103131] = dataList[1749],
	[16103132] = dataList[1750],
	[16103133] = dataList[1751],
	[16103134] = dataList[1752],
	[16103135] = dataList[1753],
	[16103136] = dataList[1754],
	[16103137] = dataList[1755],
	[16103138] = dataList[1756],
	[16103139] = dataList[1757],
	[16103141] = dataList[1758],
	[16103142] = dataList[1759],
	[16103143] = dataList[1760],
	[16103144] = dataList[1761],
	[16103145] = dataList[1762],
	[16103146] = dataList[1763],
	[16103147] = dataList[1764],
	[16103148] = dataList[1765],
	[16103149] = dataList[1766],
	[16103151] = dataList[1767],
	[16103152] = dataList[1768],
	[16103153] = dataList[1769],
	[16103154] = dataList[1770],
	[16103155] = dataList[1771],
	[16103156] = dataList[1772],
	[16103157] = dataList[1773],
	[16103158] = dataList[1774],
	[16103159] = dataList[1775],
	[16104011] = dataList[1776],
	[16104012] = dataList[1777],
	[16104013] = dataList[1778],
	[16104014] = dataList[1779],
	[16104015] = dataList[1780],
	[16104016] = dataList[1781],
	[16104017] = dataList[1782],
	[16104018] = dataList[1783],
	[16104019] = dataList[1784],
	[16104021] = dataList[1785],
	[16104022] = dataList[1786],
	[16104023] = dataList[1787],
	[16104024] = dataList[1788],
	[16104025] = dataList[1789],
	[16104026] = dataList[1790],
	[16104027] = dataList[1791],
	[16104028] = dataList[1792],
	[16104029] = dataList[1793],
	[16104031] = dataList[1794],
	[16104032] = dataList[1795],
	[16104033] = dataList[1796],
	[16104034] = dataList[1797],
	[16104035] = dataList[1798],
	[16104036] = dataList[1799],
	[16104037] = dataList[1800],
	[16104038] = dataList[1801],
	[16104039] = dataList[1802],
	[16104041] = dataList[1803],
	[16104042] = dataList[1804],
	[16104043] = dataList[1805],
	[16104044] = dataList[1806],
	[16104045] = dataList[1807],
	[16104046] = dataList[1808],
	[16104047] = dataList[1809],
	[16104048] = dataList[1810],
	[16104049] = dataList[1811],
	[16104051] = dataList[1812],
	[16104052] = dataList[1813],
	[16104053] = dataList[1814],
	[16104054] = dataList[1815],
	[16104055] = dataList[1816],
	[16104056] = dataList[1817],
	[16104057] = dataList[1818],
	[16104058] = dataList[1819],
	[16104059] = dataList[1820],
	[16104061] = dataList[1821],
	[16104062] = dataList[1822],
	[16104063] = dataList[1823],
	[16104064] = dataList[1824],
	[16104065] = dataList[1825],
	[16104066] = dataList[1826],
	[16104067] = dataList[1827],
	[16104068] = dataList[1828],
	[16104069] = dataList[1829],
	[16104071] = dataList[1830],
	[16104072] = dataList[1831],
	[16104073] = dataList[1832],
	[16104074] = dataList[1833],
	[16104075] = dataList[1834],
	[16104076] = dataList[1835],
	[16104077] = dataList[1836],
	[16104078] = dataList[1837],
	[16104079] = dataList[1838],
	[16104081] = dataList[1839],
	[16104082] = dataList[1840],
	[16104083] = dataList[1841],
	[16104084] = dataList[1842],
	[16104085] = dataList[1843],
	[16104086] = dataList[1844],
	[16104087] = dataList[1845],
	[16104088] = dataList[1846],
	[16104089] = dataList[1847],
	[16104091] = dataList[1848],
	[16104092] = dataList[1849],
	[16104093] = dataList[1850],
	[16104094] = dataList[1851],
	[16104095] = dataList[1852],
	[16104096] = dataList[1853],
	[16104097] = dataList[1854],
	[16104098] = dataList[1855],
	[16104099] = dataList[1856],
	[16104101] = dataList[1857],
	[16104102] = dataList[1858],
	[16104103] = dataList[1859],
	[16104104] = dataList[1860],
	[16104105] = dataList[1861],
	[16104106] = dataList[1862],
	[16104107] = dataList[1863],
	[16104108] = dataList[1864],
	[16104109] = dataList[1865],
	[16104111] = dataList[1866],
	[16104112] = dataList[1867],
	[16104113] = dataList[1868],
	[16104114] = dataList[1869],
	[16104115] = dataList[1870],
	[16104116] = dataList[1871],
	[16104117] = dataList[1872],
	[16104118] = dataList[1873],
	[16104119] = dataList[1874],
	[16104121] = dataList[1875],
	[16104122] = dataList[1876],
	[16104123] = dataList[1877],
	[16104124] = dataList[1878],
	[16104125] = dataList[1879],
	[16104126] = dataList[1880],
	[16104127] = dataList[1881],
	[16104128] = dataList[1882],
	[16104129] = dataList[1883],
	[16104131] = dataList[1884],
	[16104132] = dataList[1885],
	[16104133] = dataList[1886],
	[16104134] = dataList[1887],
	[16104135] = dataList[1888],
	[16104136] = dataList[1889],
	[16104137] = dataList[1890],
	[16104138] = dataList[1891],
	[16104139] = dataList[1892],
	[16104141] = dataList[1893],
	[16104142] = dataList[1894],
	[16104143] = dataList[1895],
	[16104144] = dataList[1896],
	[16104145] = dataList[1897],
	[16104146] = dataList[1898],
	[16104147] = dataList[1899],
	[16104148] = dataList[1900],
	[16104149] = dataList[1901],
	[16104151] = dataList[1902],
	[16104152] = dataList[1903],
	[16104153] = dataList[1904],
	[16104154] = dataList[1905],
	[16104155] = dataList[1906],
	[16104156] = dataList[1907],
	[16104157] = dataList[1908],
	[16104158] = dataList[1909],
	[16104159] = dataList[1910],
	[16105011] = dataList[1911],
	[16105012] = dataList[1912],
	[16105013] = dataList[1913],
	[16105014] = dataList[1914],
	[16105015] = dataList[1915],
	[16105016] = dataList[1916],
	[16105017] = dataList[1917],
	[16105018] = dataList[1918],
	[16105019] = dataList[1919],
	[16105021] = dataList[1920],
	[16105022] = dataList[1921],
	[16105023] = dataList[1922],
	[16105024] = dataList[1923],
	[16105025] = dataList[1924],
	[16105026] = dataList[1925],
	[16105027] = dataList[1926],
	[16105028] = dataList[1927],
	[16105029] = dataList[1928],
	[16105031] = dataList[1929],
	[16105032] = dataList[1930],
	[16105033] = dataList[1931],
	[16105034] = dataList[1932],
	[16105035] = dataList[1933],
	[16105036] = dataList[1934],
	[16105037] = dataList[1935],
	[16105038] = dataList[1936],
	[16105039] = dataList[1937],
	[16105041] = dataList[1938],
	[16105042] = dataList[1939],
	[16105043] = dataList[1940],
	[16105044] = dataList[1941],
	[16105045] = dataList[1942],
	[16105046] = dataList[1943],
	[16105047] = dataList[1944],
	[16105048] = dataList[1945],
	[16105049] = dataList[1946],
	[16105051] = dataList[1947],
	[16105052] = dataList[1948],
	[16105053] = dataList[1949],
	[16105054] = dataList[1950],
	[16105055] = dataList[1951],
	[16105056] = dataList[1952],
	[16105057] = dataList[1953],
	[16105058] = dataList[1954],
	[16105059] = dataList[1955],
	[16105061] = dataList[1956],
	[16105062] = dataList[1957],
	[16105063] = dataList[1958],
	[16105064] = dataList[1959],
	[16105065] = dataList[1960],
	[16105066] = dataList[1961],
	[16105067] = dataList[1962],
	[16105068] = dataList[1963],
	[16105069] = dataList[1964],
	[16105071] = dataList[1965],
	[16105072] = dataList[1966],
	[16105073] = dataList[1967],
	[16105074] = dataList[1968],
	[16105075] = dataList[1969],
	[16105076] = dataList[1970],
	[16105077] = dataList[1971],
	[16105078] = dataList[1972],
	[16105079] = dataList[1973],
	[16105081] = dataList[1974],
	[16105082] = dataList[1975],
	[16105083] = dataList[1976],
	[16105084] = dataList[1977],
	[16105085] = dataList[1978],
	[16105086] = dataList[1979],
	[16105087] = dataList[1980],
	[16105088] = dataList[1981],
	[16105089] = dataList[1982],
	[16105091] = dataList[1983],
	[16105092] = dataList[1984],
	[16105093] = dataList[1985],
	[16105094] = dataList[1986],
	[16105095] = dataList[1987],
	[16105096] = dataList[1988],
	[16105097] = dataList[1989],
	[16105098] = dataList[1990],
	[16105099] = dataList[1991],
	[16105101] = dataList[1992],
	[16105102] = dataList[1993],
	[16105103] = dataList[1994],
	[16105104] = dataList[1995],
	[16105105] = dataList[1996],
	[16105106] = dataList[1997],
	[16105107] = dataList[1998],
	[16105108] = dataList[1999],
	[16105109] = dataList[2000],
	[16105111] = dataList[2001],
	[16105112] = dataList[2002],
	[16105113] = dataList[2003],
	[16105114] = dataList[2004],
	[16105115] = dataList[2005],
	[16105116] = dataList[2006],
	[16105117] = dataList[2007],
	[16105118] = dataList[2008],
	[16105119] = dataList[2009],
	[16105121] = dataList[2010],
	[16105122] = dataList[2011],
	[16105123] = dataList[2012],
	[16105124] = dataList[2013],
	[16105125] = dataList[2014],
	[16105126] = dataList[2015],
	[16105127] = dataList[2016],
	[16105128] = dataList[2017],
	[16105129] = dataList[2018],
	[16105131] = dataList[2019],
	[16105132] = dataList[2020],
	[16105133] = dataList[2021],
	[16105134] = dataList[2022],
	[16105135] = dataList[2023],
	[16105136] = dataList[2024],
	[16105137] = dataList[2025],
	[16105138] = dataList[2026],
	[16105139] = dataList[2027],
	[16105141] = dataList[2028],
	[16105142] = dataList[2029],
	[16105143] = dataList[2030],
	[16105144] = dataList[2031],
	[16105145] = dataList[2032],
	[16105146] = dataList[2033],
	[16105147] = dataList[2034],
	[16105148] = dataList[2035],
	[16105149] = dataList[2036],
	[16105151] = dataList[2037],
	[16105152] = dataList[2038],
	[16105153] = dataList[2039],
	[16105154] = dataList[2040],
	[16105155] = dataList[2041],
	[16105156] = dataList[2042],
	[16105157] = dataList[2043],
	[16105158] = dataList[2044],
	[16105159] = dataList[2045],
}

t_prop.dataList = dataList
local mt
if ItemDefine then
	mt = {
		__cname =  "ItemDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or ItemDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_prop