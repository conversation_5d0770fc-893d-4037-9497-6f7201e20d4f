module("logic.scene.unit.specialmove.PaperPlaneHandler",package.seeall)
local PaperPlaneHandler = class("PaperPlaneHandler", PlayerShortActionHandler)

function PaperPlaneHandler:onStart()

	self.unit:lookTo(self.info.x, self.info.y)	

	self.unit:setNavEnable(false)
	if not self.info.z then 
		self.info.z = GameUtils.GetPosYOnGround(self.info.x, self.info.y)
	end
	self.unit:playAnimation("sit", true)
	printInfo("jump pos", self.info.x, self.info.y, self.info.z)
	local startX, startY, startZ = self.unit:getPos()
	if not startZ then
		startZ = self.info.z
	end
	local dis = math.sqrt((self.info.x-startX)^2+(self.info.y-startY)^2 + (self.info.z-startZ)^2)
	self.time = dis / 6

	self.planeGo = goutil.clone(SceneManager.instance:getCurScene():getSceneRes("scene/pjab_scene_prefab/s158_room/paperplane.prefab"))

	goutil.addChildToParent(self.planeGo, SceneManager.instance:getCurScene().stage.overlap)
	tfutil.SetLPos(self.planeGo, startX, startZ, startY)
	local dir = Vector3.New(self.info.x-startX, 0, self.info.y-startY)
	self.planeGo.transform.rotation = Quaternion.LookRotation(dir)
	self.tween1 = self.unit.go.transform:DOMove(Vector3.New(self.info.x, self.info.z, self.info.y), self.time)
	self.tween1:OnComplete(handler(self.jumpOver, self))
	self.tween2 = self.planeGo.transform:DOMove(Vector3.New(self.info.x, self.info.z, self.info.y), self.time)
	if self.unit.isUser then
		SoundManager.instance:playEffect(142186, self.unit.go)
	end
end




function PaperPlaneHandler:jumpOver()
	self.tween1 = nil
	self.tween2 = nil
	if self.unit.isUser then
		SoundManager.instance:stopEffect(142186, self.unit.go)
	end
	if self.planeGo then
		goutil.destroy(self.planeGo)
		self.planeGo = nil
	end
	-- self.unit:teleport(self.info.x, self.info.y)
	-- self.unit:setPos(self.info.x, self.info.y, self.info.z)
	self:finish()
end

function PaperPlaneHandler:onStop()
	if self.tween1 then
		self.tween1:Kill()
		self.tween1 = nil
		self.tween2:Kill()
		self.tween2 = nil
	end
	if self.planeGo then
		goutil.destroy(self.planeGo)
		self.planeGo = nil
	end
	self.unit:teleport(self.info.x, self.info.y, self.info.z)
end
return PaperPlaneHandler