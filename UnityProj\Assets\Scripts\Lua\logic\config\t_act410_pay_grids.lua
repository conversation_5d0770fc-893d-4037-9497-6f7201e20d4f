-- {excel:410赛季链式礼包.xlsx, sheetName:export_付费格子配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act410_pay_grids", package.seeall)

local title = {activityId=1,id=2,condition=3,reward=4,canRefresh=5}

local dataList = {
	{1835, 1, {preId=0,goodsId=0,price=0}, {{count=2,id=16000559}}, true},
	{1835, 2, {preId=1,goodsId=0,price=0}, {{count=10,id=16000352}}, true},
	{1835, 3, {preId=2,goodsId=900088,price=3}, {{count=20,id=16000353},{count=10000,id=1}}, false},
	{1835, 4, {preId=3,goodsId=0,price=0}, {{count=3,id=16000559}}, false},
	{1835, 5, {preId=4,goodsId=0,price=0}, {{count=2,id=16000560},{count=1,id=16000558}}, false},
	{1835, 6, {preId=5,goodsId=900089,price=6}, {{count=30,id=16000353},{count=10,id=16000557},{count=20000,id=1}}, false},
	{1835, 7, {preId=6,goodsId=0,price=0}, {{count=5,id=16000559}}, false},
	{1835, 8, {preId=7,goodsId=0,price=0}, {{count=3,id=16000560},{count=20,id=2}}, false},
	{1835, 9, {preId=8,goodsId=0,price=0}, {{count=1,id=16000561},{count=1,id=16000558}}, false},
	{1835, 10, {preId=9,goodsId=900090,price=12}, {{count=50,id=16000353},{count=15,id=16000557},{count=30000,id=1}}, false},
	{1835, 11, {preId=10,goodsId=0,price=0}, {{count=10,id=16000559}}, false},
	{1835, 12, {preId=11,goodsId=0,price=0}, {{count=5,id=16000560},{count=30,id=2}}, false},
	{1835, 13, {preId=12,goodsId=0,price=0}, {{count=3,id=16000561},{count=2,id=16000558}}, false},
	{1835, 14, {preId=13,goodsId=900091,price=28}, {{count=100,id=16000353},{count=20,id=16000557},{count=60000,id=1}}, false},
	{1835, 15, {preId=14,goodsId=0,price=0}, {{count=5,id=16000561}}, false},
	{1835, 16, {preId=15,goodsId=0,price=0}, {{count=25,id=16000352},{count=50,id=2}}, false},
	{1835, 17, {preId=16,goodsId=0,price=0}, {{count=1,id=16000564},{count=2,id=16000558}}, false},
	{1835, 18, {preId=17,goodsId=900092,price=68}, {{count=220,id=16000353},{count=30,id=16000557},{count=100000,id=1}}, false},
	{1835, 19, {preId=18,goodsId=0,price=0}, {{count=10,id=16000560}}, false},
	{1835, 20, {preId=19,goodsId=0,price=0}, {{count=50,id=16000352}}, false},
	{1835, 21, {preId=20,goodsId=0,price=0}, {{count=10,id=16000561},{count=100,id=2}}, false},
	{1835, 22, {preId=21,goodsId=0,price=0}, {{count=1,id=16000565},{count=3,id=16000558}}, false},
	{1835, 23, {preId=22,goodsId=900093,price=128}, {{count=400,id=16000353},{count=50,id=16000557},{count=200000,id=1}}, false},
	{1835, 24, {preId=23,goodsId=0,price=0}, {{count=20,id=16000560}}, false},
	{1835, 25, {preId=24,goodsId=0,price=0}, {{count=1,id=16000565}}, false},
	{1835, 26, {preId=25,goodsId=0,price=0}, {{count=20,id=16000561},{count=200,id=2}}, false},
	{1835, 27, {preId=26,goodsId=0,price=0}, {{count=1,id=16000566},{count=3,id=16000558}}, false},
	{1933, 1, {preId=0,goodsId=0,price=0}, {{count=2,id=16000598}}, true},
	{1933, 2, {preId=1,goodsId=0,price=0}, {{count=10,id=16000352}}, true},
	{1933, 3, {preId=2,goodsId=900107,price=3}, {{count=20,id=16000353},{count=10000,id=1}}, false},
	{1933, 4, {preId=3,goodsId=0,price=0}, {{count=3,id=16000598}}, false},
	{1933, 5, {preId=4,goodsId=0,price=0}, {{count=2,id=16000599},{count=1,id=16000558}}, false},
	{1933, 6, {preId=5,goodsId=900108,price=6}, {{count=30,id=16000353},{count=10,id=16000557},{count=20000,id=1}}, false},
	{1933, 7, {preId=6,goodsId=0,price=0}, {{count=5,id=16000598}}, false},
	{1933, 8, {preId=7,goodsId=0,price=0}, {{count=3,id=16000599},{count=20,id=2}}, false},
	{1933, 9, {preId=8,goodsId=0,price=0}, {{count=1,id=16000600},{count=1,id=16000558}}, false},
	{1933, 10, {preId=9,goodsId=900109,price=12}, {{count=50,id=16000353},{count=15,id=16000557},{count=30000,id=1}}, false},
	{1933, 11, {preId=10,goodsId=0,price=0}, {{count=10,id=16000598}}, false},
	{1933, 12, {preId=11,goodsId=0,price=0}, {{count=5,id=16000599},{count=30,id=2}}, false},
	{1933, 13, {preId=12,goodsId=0,price=0}, {{count=3,id=16000600},{count=2,id=16000558}}, false},
	{1933, 14, {preId=13,goodsId=900110,price=28}, {{count=100,id=16000353},{count=20,id=16000557},{count=60000,id=1}}, false},
	{1933, 15, {preId=14,goodsId=0,price=0}, {{count=5,id=16000600}}, false},
	{1933, 16, {preId=15,goodsId=0,price=0}, {{count=25,id=16000352},{count=50,id=2}}, false},
	{1933, 17, {preId=16,goodsId=0,price=0}, {{count=1,id=16000604},{count=2,id=16000558}}, false},
	{1933, 18, {preId=17,goodsId=900111,price=68}, {{count=220,id=16000353},{count=30,id=16000557},{count=100000,id=1}}, false},
	{1933, 19, {preId=18,goodsId=0,price=0}, {{count=10,id=16000599}}, false},
	{1933, 20, {preId=19,goodsId=0,price=0}, {{count=50,id=16000352}}, false},
	{1933, 21, {preId=20,goodsId=0,price=0}, {{count=10,id=16000600},{count=100,id=2}}, false},
	{1933, 22, {preId=21,goodsId=0,price=0}, {{count=1,id=16000605},{count=3,id=16000558}}, false},
	{1933, 23, {preId=22,goodsId=900112,price=128}, {{count=400,id=16000353},{count=50,id=16000557},{count=200000,id=1}}, false},
	{1933, 24, {preId=23,goodsId=0,price=0}, {{count=20,id=16000599}}, false},
	{1933, 25, {preId=24,goodsId=0,price=0}, {{count=1,id=16000605}}, false},
	{1933, 26, {preId=25,goodsId=0,price=0}, {{count=20,id=16000600},{count=200,id=2}}, false},
	{1933, 27, {preId=26,goodsId=0,price=0}, {{count=1,id=16000606},{count=3,id=16000558}}, false},
	{2129, 1, {preId=0,goodsId=0,price=0}, {{count=2,id=16000685}}, true},
	{2129, 2, {preId=1,goodsId=0,price=0}, {{count=10,id=16000352}}, true},
	{2129, 3, {preId=2,goodsId=900130,price=3}, {{count=20,id=16000353},{count=10000,id=1}}, false},
	{2129, 4, {preId=3,goodsId=0,price=0}, {{count=3,id=16000685}}, false},
	{2129, 5, {preId=4,goodsId=0,price=0}, {{count=2,id=16000686},{count=1,id=16000558}}, false},
	{2129, 6, {preId=5,goodsId=900131,price=6}, {{count=30,id=16000353},{count=10,id=16000557},{count=20000,id=1}}, false},
	{2129, 7, {preId=6,goodsId=0,price=0}, {{count=5,id=16000685}}, false},
	{2129, 8, {preId=7,goodsId=0,price=0}, {{count=3,id=16000686},{count=20,id=2}}, false},
	{2129, 9, {preId=8,goodsId=0,price=0}, {{count=1,id=16000687},{count=1,id=16000558}}, false},
	{2129, 10, {preId=9,goodsId=900132,price=12}, {{count=50,id=16000353},{count=15,id=16000557},{count=30000,id=1}}, false},
	{2129, 11, {preId=10,goodsId=0,price=0}, {{count=10,id=16000685}}, false},
	{2129, 12, {preId=11,goodsId=0,price=0}, {{count=5,id=16000686},{count=30,id=2}}, false},
	{2129, 13, {preId=12,goodsId=0,price=0}, {{count=3,id=16000687},{count=2,id=16000558}}, false},
	{2129, 14, {preId=13,goodsId=900133,price=28}, {{count=100,id=16000353},{count=20,id=16000557},{count=60000,id=1}}, false},
	{2129, 15, {preId=14,goodsId=0,price=0}, {{count=5,id=16000687}}, false},
	{2129, 16, {preId=15,goodsId=0,price=0}, {{count=25,id=16000352},{count=50,id=2}}, false},
	{2129, 17, {preId=16,goodsId=0,price=0}, {{count=1,id=16000688},{count=2,id=16000558}}, false},
	{2129, 18, {preId=17,goodsId=900134,price=68}, {{count=220,id=16000353},{count=30,id=16000557},{count=100000,id=1}}, false},
	{2129, 19, {preId=18,goodsId=0,price=0}, {{count=10,id=16000686}}, false},
	{2129, 20, {preId=19,goodsId=0,price=0}, {{count=50,id=16000352}}, false},
	{2129, 21, {preId=20,goodsId=0,price=0}, {{count=10,id=16000687},{count=100,id=2}}, false},
	{2129, 22, {preId=21,goodsId=0,price=0}, {{count=1,id=16000689},{count=3,id=16000558}}, false},
	{2129, 23, {preId=22,goodsId=900135,price=128}, {{count=400,id=16000353},{count=50,id=16000557},{count=200000,id=1}}, false},
	{2129, 24, {preId=23,goodsId=0,price=0}, {{count=20,id=16000686}}, false},
	{2129, 25, {preId=24,goodsId=0,price=0}, {{count=1,id=16000689}}, false},
	{2129, 26, {preId=25,goodsId=0,price=0}, {{count=20,id=16000687},{count=200,id=2}}, false},
	{2129, 27, {preId=26,goodsId=0,price=0}, {{count=1,id=16000690},{count=3,id=16000558}}, false},
	{2245, 1, {preId=0,goodsId=0,price=0}, {{count=2,id=16000718}}, true},
	{2245, 2, {preId=1,goodsId=0,price=0}, {{count=10,id=16000352}}, true},
	{2245, 3, {preId=2,goodsId=900150,price=3}, {{count=20,id=16000353},{count=10000,id=1}}, false},
	{2245, 4, {preId=3,goodsId=0,price=0}, {{count=3,id=16000718}}, false},
	{2245, 5, {preId=4,goodsId=0,price=0}, {{count=2,id=16000719},{count=1,id=16000558}}, false},
	{2245, 6, {preId=5,goodsId=900151,price=6}, {{count=30,id=16000353},{count=10,id=16000557},{count=20000,id=1}}, false},
	{2245, 7, {preId=6,goodsId=0,price=0}, {{count=5,id=16000718}}, false},
	{2245, 8, {preId=7,goodsId=0,price=0}, {{count=3,id=16000719},{count=20,id=2}}, false},
	{2245, 9, {preId=8,goodsId=0,price=0}, {{count=1,id=16000720},{count=1,id=16000558}}, false},
	{2245, 10, {preId=9,goodsId=900152,price=12}, {{count=50,id=16000353},{count=15,id=16000557},{count=30000,id=1}}, false},
	{2245, 11, {preId=10,goodsId=0,price=0}, {{count=10,id=16000718}}, false},
	{2245, 12, {preId=11,goodsId=0,price=0}, {{count=5,id=16000719},{count=30,id=2}}, false},
	{2245, 13, {preId=12,goodsId=0,price=0}, {{count=3,id=16000720},{count=2,id=16000558}}, false},
	{2245, 14, {preId=13,goodsId=900153,price=28}, {{count=100,id=16000353},{count=20,id=16000557},{count=60000,id=1}}, false},
	{2245, 15, {preId=14,goodsId=0,price=0}, {{count=5,id=16000720}}, false},
	{2245, 16, {preId=15,goodsId=0,price=0}, {{count=25,id=16000352},{count=50,id=2}}, false},
	{2245, 17, {preId=16,goodsId=0,price=0}, {{count=1,id=16000721},{count=2,id=16000558}}, false},
	{2245, 18, {preId=17,goodsId=900154,price=68}, {{count=220,id=16000353},{count=30,id=16000557},{count=100000,id=1}}, false},
	{2245, 19, {preId=18,goodsId=0,price=0}, {{count=10,id=16000719}}, false},
	{2245, 20, {preId=19,goodsId=0,price=0}, {{count=50,id=16000352}}, false},
	{2245, 21, {preId=20,goodsId=0,price=0}, {{count=10,id=16000720},{count=100,id=2}}, false},
	{2245, 22, {preId=21,goodsId=0,price=0}, {{count=1,id=16000722},{count=3,id=16000558}}, false},
	{2245, 23, {preId=22,goodsId=900155,price=128}, {{count=400,id=16000353},{count=50,id=16000557},{count=200000,id=1}}, false},
	{2245, 24, {preId=23,goodsId=0,price=0}, {{count=20,id=16000719}}, false},
	{2245, 25, {preId=24,goodsId=0,price=0}, {{count=1,id=16000722}}, false},
	{2245, 26, {preId=25,goodsId=0,price=0}, {{count=20,id=16000720},{count=200,id=2}}, false},
	{2245, 27, {preId=26,goodsId=0,price=0}, {{count=1,id=16000723},{count=3,id=16000558}}, false},
	{2373, 1, {preId=0,goodsId=0,price=0}, {{count=2,id=16000765}}, true},
	{2373, 2, {preId=1,goodsId=0,price=0}, {{count=10,id=16000352}}, true},
	{2373, 3, {preId=2,goodsId=900178,price=3}, {{count=20,id=16000353},{count=10000,id=1}}, false},
	{2373, 4, {preId=3,goodsId=0,price=0}, {{count=3,id=16000765}}, false},
	{2373, 5, {preId=4,goodsId=0,price=0}, {{count=2,id=16000766},{count=1,id=16000558}}, false},
	{2373, 6, {preId=5,goodsId=900179,price=6}, {{count=30,id=16000353},{count=10,id=16000557},{count=20000,id=1}}, false},
	{2373, 7, {preId=6,goodsId=0,price=0}, {{count=5,id=16000765}}, false},
	{2373, 8, {preId=7,goodsId=0,price=0}, {{count=3,id=16000766},{count=20,id=2}}, false},
	{2373, 9, {preId=8,goodsId=0,price=0}, {{count=1,id=16000767},{count=1,id=16000558}}, false},
	{2373, 10, {preId=9,goodsId=900180,price=12}, {{count=50,id=16000353},{count=15,id=16000557},{count=30000,id=1}}, false},
	{2373, 11, {preId=10,goodsId=0,price=0}, {{count=10,id=16000765}}, false},
	{2373, 12, {preId=11,goodsId=0,price=0}, {{count=5,id=16000766},{count=30,id=2}}, false},
	{2373, 13, {preId=12,goodsId=0,price=0}, {{count=3,id=16000767},{count=2,id=16000558}}, false},
	{2373, 14, {preId=13,goodsId=900181,price=28}, {{count=100,id=16000353},{count=20,id=16000557},{count=60000,id=1}}, false},
	{2373, 15, {preId=14,goodsId=0,price=0}, {{count=5,id=16000767}}, false},
	{2373, 16, {preId=15,goodsId=0,price=0}, {{count=25,id=16000352},{count=50,id=2}}, false},
	{2373, 17, {preId=16,goodsId=0,price=0}, {{count=1,id=16000768},{count=2,id=16000558}}, false},
	{2373, 18, {preId=17,goodsId=900182,price=68}, {{count=220,id=16000353},{count=30,id=16000557},{count=100000,id=1}}, false},
	{2373, 19, {preId=18,goodsId=0,price=0}, {{count=10,id=16000766}}, false},
	{2373, 20, {preId=19,goodsId=0,price=0}, {{count=50,id=16000352}}, false},
	{2373, 21, {preId=20,goodsId=0,price=0}, {{count=10,id=16000767},{count=100,id=2}}, false},
	{2373, 22, {preId=21,goodsId=0,price=0}, {{count=1,id=16000769},{count=3,id=16000558}}, false},
	{2373, 23, {preId=22,goodsId=900183,price=128}, {{count=400,id=16000353},{count=50,id=16000557},{count=200000,id=1}}, false},
	{2373, 24, {preId=23,goodsId=0,price=0}, {{count=20,id=16000766}}, false},
	{2373, 25, {preId=24,goodsId=0,price=0}, {{count=1,id=16000769}}, false},
	{2373, 26, {preId=25,goodsId=0,price=0}, {{count=20,id=16000767},{count=200,id=2}}, false},
	{2373, 27, {preId=26,goodsId=0,price=0}, {{count=1,id=16000770},{count=3,id=16000558}}, false},
	{2464, 1, {preId=0,goodsId=0,price=0}, {{count=2,id=16000787}}, true},
	{2464, 2, {preId=1,goodsId=0,price=0}, {{count=10,id=16000352}}, true},
	{2464, 3, {preId=2,goodsId=900203,price=3}, {{count=20,id=16000353},{count=10000,id=1}}, false},
	{2464, 4, {preId=3,goodsId=0,price=0}, {{count=3,id=16000787}}, false},
	{2464, 5, {preId=4,goodsId=0,price=0}, {{count=2,id=16000788},{count=1,id=16000558}}, false},
	{2464, 6, {preId=5,goodsId=900204,price=6}, {{count=30,id=16000353},{count=10,id=16000557},{count=20000,id=1}}, false},
	{2464, 7, {preId=6,goodsId=0,price=0}, {{count=5,id=16000787}}, false},
	{2464, 8, {preId=7,goodsId=0,price=0}, {{count=3,id=16000788},{count=20,id=2}}, false},
	{2464, 9, {preId=8,goodsId=0,price=0}, {{count=1,id=16000789},{count=1,id=16000558}}, false},
	{2464, 10, {preId=9,goodsId=900205,price=12}, {{count=50,id=16000353},{count=15,id=16000557},{count=30000,id=1}}, false},
	{2464, 11, {preId=10,goodsId=0,price=0}, {{count=10,id=16000787}}, false},
	{2464, 12, {preId=11,goodsId=0,price=0}, {{count=5,id=16000788},{count=30,id=2}}, false},
	{2464, 13, {preId=12,goodsId=0,price=0}, {{count=3,id=16000789},{count=2,id=16000558}}, false},
	{2464, 14, {preId=13,goodsId=900206,price=28}, {{count=100,id=16000353},{count=20,id=16000557},{count=60000,id=1}}, false},
	{2464, 15, {preId=14,goodsId=0,price=0}, {{count=5,id=16000789}}, false},
	{2464, 16, {preId=15,goodsId=0,price=0}, {{count=25,id=16000352},{count=50,id=2}}, false},
	{2464, 17, {preId=16,goodsId=0,price=0}, {{count=1,id=16000790},{count=2,id=16000558}}, false},
	{2464, 18, {preId=17,goodsId=900207,price=68}, {{count=220,id=16000353},{count=30,id=16000557},{count=100000,id=1}}, false},
	{2464, 19, {preId=18,goodsId=0,price=0}, {{count=10,id=16000788}}, false},
	{2464, 20, {preId=19,goodsId=0,price=0}, {{count=50,id=16000352}}, false},
	{2464, 21, {preId=20,goodsId=0,price=0}, {{count=10,id=16000789},{count=100,id=2}}, false},
	{2464, 22, {preId=21,goodsId=0,price=0}, {{count=1,id=16000791},{count=3,id=16000558}}, false},
	{2464, 23, {preId=22,goodsId=900208,price=128}, {{count=400,id=16000353},{count=50,id=16000557},{count=200000,id=1}}, false},
	{2464, 24, {preId=23,goodsId=0,price=0}, {{count=20,id=16000788}}, false},
	{2464, 25, {preId=24,goodsId=0,price=0}, {{count=1,id=16000791}}, false},
	{2464, 26, {preId=25,goodsId=0,price=0}, {{count=20,id=16000789},{count=200,id=2}}, false},
	{2464, 27, {preId=26,goodsId=0,price=0}, {{count=1,id=16000792},{count=3,id=16000558}}, false},
}

local t_act410_pay_grids = {
	[1835] = {
		[1] = dataList[1],
		[2] = dataList[2],
		[3] = dataList[3],
		[4] = dataList[4],
		[5] = dataList[5],
		[6] = dataList[6],
		[7] = dataList[7],
		[8] = dataList[8],
		[9] = dataList[9],
		[10] = dataList[10],
		[11] = dataList[11],
		[12] = dataList[12],
		[13] = dataList[13],
		[14] = dataList[14],
		[15] = dataList[15],
		[16] = dataList[16],
		[17] = dataList[17],
		[18] = dataList[18],
		[19] = dataList[19],
		[20] = dataList[20],
		[21] = dataList[21],
		[22] = dataList[22],
		[23] = dataList[23],
		[24] = dataList[24],
		[25] = dataList[25],
		[26] = dataList[26],
		[27] = dataList[27],
	},
	[1933] = {
		[1] = dataList[28],
		[2] = dataList[29],
		[3] = dataList[30],
		[4] = dataList[31],
		[5] = dataList[32],
		[6] = dataList[33],
		[7] = dataList[34],
		[8] = dataList[35],
		[9] = dataList[36],
		[10] = dataList[37],
		[11] = dataList[38],
		[12] = dataList[39],
		[13] = dataList[40],
		[14] = dataList[41],
		[15] = dataList[42],
		[16] = dataList[43],
		[17] = dataList[44],
		[18] = dataList[45],
		[19] = dataList[46],
		[20] = dataList[47],
		[21] = dataList[48],
		[22] = dataList[49],
		[23] = dataList[50],
		[24] = dataList[51],
		[25] = dataList[52],
		[26] = dataList[53],
		[27] = dataList[54],
	},
	[2129] = {
		[1] = dataList[55],
		[2] = dataList[56],
		[3] = dataList[57],
		[4] = dataList[58],
		[5] = dataList[59],
		[6] = dataList[60],
		[7] = dataList[61],
		[8] = dataList[62],
		[9] = dataList[63],
		[10] = dataList[64],
		[11] = dataList[65],
		[12] = dataList[66],
		[13] = dataList[67],
		[14] = dataList[68],
		[15] = dataList[69],
		[16] = dataList[70],
		[17] = dataList[71],
		[18] = dataList[72],
		[19] = dataList[73],
		[20] = dataList[74],
		[21] = dataList[75],
		[22] = dataList[76],
		[23] = dataList[77],
		[24] = dataList[78],
		[25] = dataList[79],
		[26] = dataList[80],
		[27] = dataList[81],
	},
	[2245] = {
		[1] = dataList[82],
		[2] = dataList[83],
		[3] = dataList[84],
		[4] = dataList[85],
		[5] = dataList[86],
		[6] = dataList[87],
		[7] = dataList[88],
		[8] = dataList[89],
		[9] = dataList[90],
		[10] = dataList[91],
		[11] = dataList[92],
		[12] = dataList[93],
		[13] = dataList[94],
		[14] = dataList[95],
		[15] = dataList[96],
		[16] = dataList[97],
		[17] = dataList[98],
		[18] = dataList[99],
		[19] = dataList[100],
		[20] = dataList[101],
		[21] = dataList[102],
		[22] = dataList[103],
		[23] = dataList[104],
		[24] = dataList[105],
		[25] = dataList[106],
		[26] = dataList[107],
		[27] = dataList[108],
	},
	[2373] = {
		[1] = dataList[109],
		[2] = dataList[110],
		[3] = dataList[111],
		[4] = dataList[112],
		[5] = dataList[113],
		[6] = dataList[114],
		[7] = dataList[115],
		[8] = dataList[116],
		[9] = dataList[117],
		[10] = dataList[118],
		[11] = dataList[119],
		[12] = dataList[120],
		[13] = dataList[121],
		[14] = dataList[122],
		[15] = dataList[123],
		[16] = dataList[124],
		[17] = dataList[125],
		[18] = dataList[126],
		[19] = dataList[127],
		[20] = dataList[128],
		[21] = dataList[129],
		[22] = dataList[130],
		[23] = dataList[131],
		[24] = dataList[132],
		[25] = dataList[133],
		[26] = dataList[134],
		[27] = dataList[135],
	},
	[2464] = {
		[1] = dataList[136],
		[2] = dataList[137],
		[3] = dataList[138],
		[4] = dataList[139],
		[5] = dataList[140],
		[6] = dataList[141],
		[7] = dataList[142],
		[8] = dataList[143],
		[9] = dataList[144],
		[10] = dataList[145],
		[11] = dataList[146],
		[12] = dataList[147],
		[13] = dataList[148],
		[14] = dataList[149],
		[15] = dataList[150],
		[16] = dataList[151],
		[17] = dataList[152],
		[18] = dataList[153],
		[19] = dataList[154],
		[20] = dataList[155],
		[21] = dataList[156],
		[22] = dataList[157],
		[23] = dataList[158],
		[24] = dataList[159],
		[25] = dataList[160],
		[26] = dataList[161],
		[27] = dataList[162],
	},
}

t_act410_pay_grids.dataList = dataList
local mt
if Act410PayGridsDefine then
	mt = {
		__cname =  "Act410PayGridsDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act410PayGridsDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act410_pay_grids