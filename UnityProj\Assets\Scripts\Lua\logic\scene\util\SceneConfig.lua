module("logic.scene.util.SceneConfig",package.seeall)
local SceneConfig = class("SceneConfig")

local sceneConfig = ConfigLoader.New("scene")
local sceneTypeConfig = ConfigLoader.New("scene_type")
local SceneObjectConfig = ConfigLoader.New("scene_special_area")
local sceneActionConfig = ConfigLoader.New("scene_action")
local areaTypeConfig = ConfigLoader.New("area_type")
local treasureConfig = ConfigLoader.New("scene_treasure_area")
local weatherConfig = ConfigLoader.New("scene_weather_type")
local snailWalkConfig = ConfigLoader.New("snail_walk_area")
local homonymConfig = ConfigLoader.New("homonym")
local trapConfig = ConfigLoader.New("scene_trouble")
local dupEntryConfig = ConfigLoader.New("scene_entry_pos")
local scene_common = ConfigLoader.New("scene_common")
local clothe_teleport = ConfigLoader.New("clothe_teleport")

SceneConfig.GroundOffset = 0.1

local function initSceneObjects()
	local list = SceneObjectConfig:getConfig().dataList
	local id, sceneCfg
	for i=1, #list do
		local config = list[i]
		id = config.sceneId
		sceneCfg = sceneConfig:getConfig()[id]
		if sceneCfg then
			if config.type == SceneObjectType.BornPoint or
				config.type == SceneObjectType.Exit and config.triggerPos then
				sceneCfg.bornPointMap[config.triggerValue.id] = config.triggerPos
			end
			if config.type ~= SceneObjectType.BornPoint then
				table.insert(sceneCfg.areaList, config)
			end
		end
	end
end

function SceneConfig.getSceneConfig(sceneId)
	if not sceneConfig:isInit() then
		-- printInfo("initSceneObjects")
		initSceneObjects()
	end
	return sceneConfig:getConfig()[sceneId]
end

function SceneConfig.getAllSceneConfig()
	return sceneConfig:getConfig().dataList
end

function SceneConfig.getSceneTypeCfg(sceneTypeId)
	return sceneTypeConfig:getConfig()[sceneTypeId]
end


function SceneConfig.getAreaTypeConfig(type)
	return areaTypeConfig:getConfig()[type]
end


function SceneConfig.GetPlayerSpeed(isNpc)
	if VirtualCameraMgr.instance:is3DScene() then
		return tonumber(GameUtils.getCommonConfig(isNpc and "NPCMoveSpeed3D" or "PlayerMoveSpeed3D"))
	elseif HouseModel.instance:isInSeabed() then
		return tonumber(GameUtils.getCommonConfig(isNpc and "NPCMoveSpeed2D" or "PlayerMoveSpeedSeabed"))
	else
		return tonumber(GameUtils.getCommonConfig(isNpc and "NPCMoveSpeed2D" or "PlayerMoveSpeed2D"))
	end
end

function SceneConfig.getTreasureArea(sceneId)
	if SceneConfig.treasureShowTips == nil then
		SceneConfig.treasureShowTips =""
		local allAreaCfg = treasureConfig:getConfig()
		local sceneIds = {}
			for k,v in pairs(allAreaCfg) do
				if k ~= "dataList" then
					table.insert(sceneIds,k)
				end
			end

		table.sort(sceneIds,
			function(a,b)
				return a<b
			end
			)

		for i,v in ipairs(sceneIds) do
			local define = SceneConfig.getSceneConfig(v)
			SceneConfig.treasureShowTips = SceneConfig.treasureShowTips .. define.name
			if i ~= #sceneIds then
				SceneConfig.treasureShowTips = SceneConfig.treasureShowTips .. "、"
			end
		end
	end

	local sceneTreasureList = treasureConfig:getConfig()[sceneId]
	return sceneTreasureList,SceneConfig.treasureShowTips
end

function SceneConfig.checkEnter(sceneId, dontShowRemind)
	local sceneInfo = SceneConfig.getSceneConfig(sceneId)
	local canEnter = true
	local remindText = ""

	if UserInfo.getUserLevel() >= sceneInfo.userLevel then
		if sceneId == 20 and not CouncilManager.instance:getCouncilId() then
			remindText = lang("需要加入家族才能进入")
			canEnter = false
		end
	else
		canEnter = false
		remindText = lang("等级到达{1}才能进入", sceneInfo.userLevel)
	end

	if not canEnter and not dontShowRemind then
		FlyTextManager.instance:showFlyText(remindText)
	end

	return canEnter
end

function SceneConfig.getSceneActoinConfig(typeId)
	return sceneActionConfig:getConfig()[typeId]
end

function SceneConfig.getWeatherConfig(id)
	return weatherConfig:getConfig()[id]
end

function SceneConfig.getRandomSnailWalkPosArea(sceneId)
	local areaList = snailWalkConfig:getConfig()[sceneId]
	local posRange = arrayutil.randomOne(areaList)
	dump(posRange)
	return posRange
end

function SceneConfig.getHomonym(char)
	local h = homonymConfig:getConfig()[char]
	return h and h.targetWords
end

function SceneConfig.getTrapDefineById(id)
	return trapConfig:getConfig()[id]
end

--- 获取场景中的副本入口配置
--- @param sceneId 场景id
--- @return table <areaId, cfg>
function SceneConfig.getSceneDupEntryCfg(sceneId)
	return dupEntryConfig:getConfig()[sceneId]
end

--- 获取场景中某个副本入口的配置
--- @param sceneId 场景id
--- @param areaId 服务器下发的位置id
function SceneConfig.getSingleSceneDupEntryCfg(sceneId, areaId)
	local sceneEntryCfg = SceneConfig.getSceneDupEntryCfg(sceneId)
	if not sceneEntryCfg then
		return
	end
	return sceneEntryCfg[areaId]
end

function SceneConfig.getSceneCommonValue(key)
	return scene_common:getConfig()[key].value
end

-- 场景stage中加载的额外资源
function SceneConfig.getExtraRes(sceneId)
	local res
	if sceneId == 95 then
		res = {
			"prefabs/bonfireparty/ghwh_datiehua_q.prefab",
			"prefabs/bonfireparty/ghwh_datiehua_wushi_h.prefab",
			"prefabs/bonfireparty/ghwh_datiehua_wushi_q.prefab",
			"prefabs/bonfireparty/ghwh_yaoguwu.prefab",
			"prefabs/bonfireparty/ghwh_huobawu.prefab",
			"prefabs/bonfireparty/s154_center_fire_01.prefab",
			"prefabs/bonfireparty/scene154_iron_fire_01_small.prefab",
			"prefabs/bonfireparty/scene154_iron_fire_00001.prefab",
		}
		local need = BonfirePartyConfig.getNeedEffNames()
		for eff, _ in pairs(need) do
			table.insert(res, "prefabs/bonfireparty/".. eff .. ".prefab")
		end
	elseif sceneId == 106 then
		res = {
			"scene/pjab_scene_prefab/s158_room/paperplane.prefab"
		}
	end
	return res
end

SceneConfig.CamHight = 6.5
SceneConfig.CamFar = 22
SceneConfig.CamFocusPointFixY = 1.8
SceneConfig.CamRotaSpeed = -5

return SceneConfig