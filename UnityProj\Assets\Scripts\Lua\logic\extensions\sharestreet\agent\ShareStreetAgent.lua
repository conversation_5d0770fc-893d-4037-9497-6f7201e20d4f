--auto gen by editor
module("logic.extensions.sharestreet.agent.ShareStreetAgent", package.seeall)

local ShareStreetAgent = class("ShareStreetAgent", BaseAgent)

function ShareStreetAgent:_block(blockKey, isBlock)
	TaskUtil.BlockClick(isBlock, blockKey)
end

function ShareStreetAgent:sendGetShareStreetInfoRequest(userId, isSelf, handler)
	self:_block("GetShareStreetInfoRequest", true)
	local req = ShareStreetExtension_pb.GetShareStreetInfoRequest()
	req.userId = userId
	req.isSelf = isSelf
	self.getShareStreetInfoHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetShareStreetInfoReply(status, msg)
	self:_block("GetShareStreetInfoRequest", false)
	if status == 0 then
		local sharestreetinfo = msg.shareStreetInfo
		local like = msg.like
		local hasinvited = msg.hasInvited
		local hasapply = msg.hasApply
		local mineStreetId = msg.mineStreetId
		local extInfo = {}
		extInfo.like = like
		extInfo.hasInvited = hasinvited
		extInfo.hasApply = hasapply
		extInfo.mineStreetId = mineStreetId
		extInfo.modifyNameCount = msg.modifyNameCount
		if self.getShareStreetInfoHandler then
			self.getShareStreetInfoHandler(sharestreetinfo, extInfo)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getShareStreetInfoHandler = nil
end

function ShareStreetAgent:sendGetStreetReceiveAskListRequest(handler)
	self:_block("GetStreetReceiveAskListRequest", true)
	local req = ShareStreetExtension_pb.GetStreetReceiveAskListRequest()
	self.getStreetReceiveAskListHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetStreetReceiveAskListReply(status, msg)
	self:_block("GetStreetReceiveAskListRequest", false)
	if status == 0 then
		local asklist = msg.askList
		local acceptask = msg.acceptAsk
		if self.getStreetReceiveAskListHandler then
			self.getStreetReceiveAskListHandler(asklist, acceptask)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getStreetReceiveAskListHandler = nil
end

function ShareStreetAgent:sendApplyJoinShareStreetRequest(ownerId, handler)
	self:_block("ApplyJoinShareStreetRequest", true)
	local req = ShareStreetExtension_pb.ApplyJoinShareStreetRequest()
	req.ownerId = ownerId
	self.applyJoinShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleApplyJoinShareStreetReply(status, msg)
	self:_block("ApplyJoinShareStreetRequest", false)
	if status == 0 then
		if self.applyJoinShareStreetHandler then
			self.applyJoinShareStreetHandler()
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.applyJoinShareStreetHandler = nil
end

function ShareStreetAgent:sendGetStreetApplyListRequest(handler)
	self:_block("GetStreetApplyListRequest", true)
	local req = ShareStreetExtension_pb.GetStreetApplyListRequest()
	self.getStreetApplyListHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetStreetApplyListReply(status, msg)
	self:_block("GetStreetApplyListRequest", false)
	if status == 0 then
		local applylist = msg.applyList
		if self.getStreetApplyListHandler then
			self.getStreetApplyListHandler(applylist)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getStreetApplyListHandler = nil
end

function ShareStreetAgent:sendLikeShareStreetRequest(streetId, handler)
	self:_block("LikeShareStreetRequest", true)
	local req = ShareStreetExtension_pb.LikeShareStreetRequest()
	req.streetId = streetId
	self.likeShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleLikeShareStreetReply(status, msg)
	self:_block("LikeShareStreetRequest", false)
	if status == 0 then
		if self.likeShareStreetHandler then
			self.likeShareStreetHandler()
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.likeShareStreetHandler = nil
end

function ShareStreetAgent:sendModifyStreetAcceptAskRequest(isAccept, handler)
	self:_block("ModifyStreetAcceptAskRequest", true)
	local req = ShareStreetExtension_pb.ModifyStreetAcceptAskRequest()
	req.isAccept = isAccept
	self.modifyStreetAcceptAskHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleModifyStreetAcceptAskReply(status, msg)
	self:_block("ModifyStreetAcceptAskRequest", false)
	if status == 0 then
		local isaccept = msg.isAccept
		if self.modifyStreetAcceptAskHandler then
			self.modifyStreetAcceptAskHandler(isaccept)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.modifyStreetAcceptAskHandler = nil
end

function ShareStreetAgent:sendGetStreetPopularityRankRequest(friend, handler)
	self:_block("GetStreetPopularityRankRequest", true)
	local req = ShareStreetExtension_pb.GetStreetPopularityRankRequest()
	req.friend = friend
	self.getStreetPopularityRankHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetStreetPopularityRankReply(status, msg)
	self:_block("GetStreetPopularityRankRequest", false)
	if status == 0 then
		local ranklist = msg.rankList
		if self.getStreetPopularityRankHandler then
			self.getStreetPopularityRankHandler(ranklist)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getStreetPopularityRankHandler = nil
end

function ShareStreetAgent:sendAgreeJoinShareStreetRequest(applyUserId, handler)
	self:_block("AgreeJoinShareStreetRequest", true)
	local req = ShareStreetExtension_pb.AgreeJoinShareStreetRequest()
	req.applyUserId = applyUserId
	self.agreeJoinShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleAgreeJoinShareStreetReply(status, msg)
	self:_block("AgreeJoinShareStreetRequest", false)
	if status == 0 then
		local asklist = msg.askList
		local shareStreetInfo = msg.shareStreetInfo
		if self.agreeJoinShareStreetHandler then
			self.agreeJoinShareStreetHandler(asklist, shareStreetInfo)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.agreeJoinShareStreetHandler = nil
end

function ShareStreetAgent:sendRefuseJoinShareStreetRequest(applyUserId, handler)
	self:_block("RefuseJoinShareStreetRequest", true)
	local req = ShareStreetExtension_pb.RefuseJoinShareStreetRequest()
	req.applyUserId = applyUserId
	self.refuseJoinShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleRefuseJoinShareStreetReply(status, msg)
	self:_block("RefuseJoinShareStreetRequest", false)
	if status == 0 then
		local asklist = msg.askList
		if self.refuseJoinShareStreetHandler then
			self.refuseJoinShareStreetHandler(asklist)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.refuseJoinShareStreetHandler = nil
end

function ShareStreetAgent:sendQuitShareStreetRequest(handler)
	self:_block("QuitShareStreetRequest", true)
	local req = ShareStreetExtension_pb.QuitShareStreetRequest()
	self.quitShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleQuitShareStreetReply(status, msg)
	self:_block("QuitShareStreetRequest", false)
	if status == 0 then
		local shareStreetInfo = msg.shareStreetInfo
		if self.quitShareStreetHandler then
			self.quitShareStreetHandler(shareStreetInfo)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.quitShareStreetHandler = nil
end

function ShareStreetAgent:sendKickOutShareStreetRequest(kickUserId, handler)
	self:_block("KickOutShareStreetRequest", true)
	local req = ShareStreetExtension_pb.KickOutShareStreetRequest()
	req.kickUserId = kickUserId
	self.kickOutShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleKickOutShareStreetReply(status, msg)
	self:_block("KickOutShareStreetRequest", false)
	if status == 0 then
		local shareStreetInfo = msg.shareStreetInfo
		if self.kickOutShareStreetHandler then
			self.kickOutShareStreetHandler(shareStreetInfo)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.kickOutShareStreetHandler = nil
end

function ShareStreetAgent:sendInviteJoinShareStreetRequest(inviteUserId, handler)
	self:_block("InviteJoinShareStreetRequest", true)
	local req = ShareStreetExtension_pb.InviteJoinShareStreetRequest()
	req.inviteUserId = inviteUserId
	self.inviteJoinShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleInviteJoinShareStreetReply(status, msg)
	self:_block("InviteJoinShareStreetRequest", false)
	if status == 0 then
		if self.inviteJoinShareStreetHandler then
			self.inviteJoinShareStreetHandler()
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.inviteJoinShareStreetHandler = nil
end

function ShareStreetAgent:sendAgreeInviteJoinShareStreetRequest(streetUserId, handler)
	self:_block("AgreeInviteJoinShareStreetRequest", true)
	local req = ShareStreetExtension_pb.AgreeInviteJoinShareStreetRequest()
	req.streetUserId = streetUserId
	self.agreeInviteJoinShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleAgreeInviteJoinShareStreetReply(status, msg)
	self:_block("AgreeInviteJoinShareStreetRequest", false)
	if status == 0 then
		local sharestreetinfo = msg.shareStreetInfo
		if self.agreeInviteJoinShareStreetHandler then
			self.agreeInviteJoinShareStreetHandler(sharestreetinfo)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.agreeInviteJoinShareStreetHandler = nil
end

function ShareStreetAgent:sendCreateShareStreetRequest(name, desc, handler)
	self:_block("CreateShareStreetRequest", true)
	local req = ShareStreetExtension_pb.CreateShareStreetRequest()
	req.name = name
	req.desc = desc
	self.createShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleCreateShareStreetReply(status, msg)
	self:_block("CreateShareStreetRequest", false)
	if status == 0 then
		local sharestreetinfo = msg.shareStreetInfo
		if self.createShareStreetHandler then
			self.createShareStreetHandler(sharestreetinfo)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.createShareStreetHandler = nil
end

function ShareStreetAgent:sendStreetShareFurnitureRequest(shareItems, handler)
	self:_block("StreetShareFurnitureRequest", true)
	local req = ShareStreetExtension_pb.StreetShareFurnitureRequest()
	for i = 1, #shareItems do
		local add = req.furnitureCount:add()
		add.furnitureId = shareItems[i].id
		add.count = shareItems[i].num
	end
	self.streetShareFurnitureHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleStreetShareFurnitureReply(status, msg)
	self:_block("StreetShareFurnitureRequest", false)
	if status == 0 then
		local changeid = msg.changeId
		if self.streetShareFurnitureHandler then
			self.streetShareFurnitureHandler(changeid)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.streetShareFurnitureHandler = nil
end

function ShareStreetAgent:sendStreetCancelShareFurnitureRequest(shareItems, handler)
	self:_block("StreetCancelShareFurnitureRequest", true)
	local req = ShareStreetExtension_pb.StreetCancelShareFurnitureRequest()
	for i = 1, #shareItems do
		local add = req.furnitureCount:add()
		add.furnitureId = shareItems[i].id
		add.count = shareItems[i].num
	end
	self.streetCancelShareFurnitureHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleStreetCancelShareFurnitureReply(status, msg)
	self:_block("StreetCancelShareFurnitureRequest", false)
	if status == 0 then
		local changeid = msg.changeId
		if self.streetCancelShareFurnitureHandler then
			self.streetCancelShareFurnitureHandler(changeid)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.streetCancelShareFurnitureHandler = nil
end

function ShareStreetAgent:sendStreetUnlockAreaRequest(areaId, handler)
	self:_block("StreetUnlockAreaRequest", true)
	local req = ShareStreetExtension_pb.StreetUnlockAreaRequest()
	req.areaId = areaId
	self.streetUnlockAreaHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleStreetUnlockAreaReply(status, msg)
	self:_block("StreetUnlockAreaRequest", false)
	if status == 0 then
		local changeid = msg.changeId
		if self.streetUnlockAreaHandler then
			self.streetUnlockAreaHandler(changeid)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.streetUnlockAreaHandler = nil
end

-- StreetUpdateInfoRequest: |-
-- optional string text = 1;
-- optional int32  type = 2;   //1:名称 2:描述
function ShareStreetAgent:sendStreetUpdateInfoRequest(text, type, handler)
	self:_block("StreetUpdateInfoRequest", true)
	local req = ShareStreetExtension_pb.StreetUpdateInfoRequest()
	req.text = text
	req.type = type
	self.streetUpdateInfoHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleStreetUpdateInfoReply(status, msg)
	self:_block("StreetUpdateInfoRequest", false)
	if status == 0 then
		if self.streetUpdateInfoHandler then
			self.streetUpdateInfoHandler()
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.streetUpdateInfoHandler = nil
end

function ShareStreetAgent:sendGetShareStreetFurnitureInfoRequest(userId, handler)
	local req = ShareStreetExtension_pb.GetShareStreetFurnitureInfoRequest()
	req.userId = userId
	self.userId = userId
	self.getShareStreetFurnitureInfoHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetShareStreetFurnitureInfoReply(status, msg)
	if status == 0 then
		if string.nilorempty(self.userId) then self.userId = UserInfo.userId end
		
		HouseModel.instance:clear()
		IslandWeatherModel.instance:clear()
		
		local lv = 200
		local areaList = {}
		local upcfgs = RoomConfig.getAllUpgradeConfig(0, 2)
		for i, aId in ipairs(msg.areaId) do
			table.insert(areaList, {id = aId, unlockTime = 0})
			for j, cfg in ipairs(upcfgs) do
				if table.indexof(cfg.areaIds, aId) ~= false then
					lv = math.max(lv, cfg.id)
				end
			end
		end
		HouseModel.instance:setHouse(self.userId, {level = lv})
		HouseModel.instance:getAreaModel():setIslandArea(areaList, {})
		
		HouseModel.instance:getHouseMo():setComfort({plus = 0, minus = 0})
		FarmModel.instance:clear()
		HouseModel.instance.needHappyBirthday = msg.needHappyBirthday
		HouseModel.instance.needBackFlow2Story = msg.needBackFlow2Story
		ShareStreetSceneModel.instance.streetName = msg.streetName
		ShareStreetSceneModel.instance:clear()
		ShareStreetSceneModel.instance:setSceneItem(msg.sceneItems)
		ShareStreetSceneModel.instance.likeCount = msg.likeCount
		ShareStreetSceneModel.instance.visitCount = msg.visitCount
		ShareStreetSceneModel.instance.hasLike = msg.like
		ShareStreetSceneModel.instance.memberCount = msg.decorationInfo and #msg.decorationInfo or 0 -- 街区成员数量
		ShareStreetSceneModel.instance.unlockAreaCount = msg.areaId and #msg.areaId or 0 -- 解锁区域数量
		ShareStreetSceneModel.instance.streetDecorationInfoList = msg.decorationInfo
		FSTModel.instance:clear()
		for i, decorationInfo in ipairs(msg.decorationInfo) do
			local areaOwnerId = decorationInfo.areaOwner.id
			ShareStreetSceneModel.instance:setUserInfo(decorationInfo.areaOwner)
			IslandModel.instance:setKaweiFromServer(decorationInfo.houseInfo, areaOwnerId)
			local offset = ShareStreetCfg:getCommonConfigValue("furnitureOffset" .. decorationInfo.areaId):splitToNumber(",")
			HouseModel.instance:getHouseMo():getFurnitureModel(areaOwnerId):setBlockOffet(offset[1], offset[2])
			HouseModel.instance:getHouseMo():_setFurniture(decorationInfo.decoratedItems, areaOwnerId)
			HouseModel.instance:getHouseMo():setFoodInfos(decorationInfo.foodItemInfos, areaOwnerId)
			HouseModel.instance:getHouseMo():getFurnitureModel(areaOwnerId):decodeSurfaceData(decorationInfo.terrains)
			HouseModel.instance:getHouseMo():setAllowToEat(decorationInfo.isAllowToEat, areaOwnerId)
			HouseModel.instance:getHouseMo():getAreaModel():setAreaOwner(decorationInfo.areaId, areaOwnerId)
			FSTModel.instance:setDataFromServer(decorationInfo.friendTrees, areaOwnerId)
			ShareStreetSceneModel.instance:setUserSceneItem(areaOwnerId, decorationInfo.sceneItems)
			for j, info in ipairs(decorationInfo.crops) do
				FarmModel.instance:addPlantFromServer(info, areaOwnerId)
			end
			if areaOwnerId == self.userId then
				HouseModel.instance:setRoleInfo({roleSimpleInfo = decorationInfo.areaOwner})
				HouseModel.instance:setFurnitureInScene(decorationInfo.decoratedItems)
			end
			if decorationInfo.areaId == 200 then
				ShareStreetSceneModel.instance.ownerId = decorationInfo.areaOwner.id
			end
		end
		
		HouseModel.instance.takePhoto = msg.takePhoto
		HouseModel.instance:setUserProperty(HouseUserProperty.IslandType, 2)
		HouseModel.instance.houseMo:getRoomFurnitureList()
		self.getShareStreetFurnitureInfoHandler()
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getShareStreetFurnitureInfoHandler = nil
	self.userId = nil
end

function ShareStreetAgent:sendDecorateShareStreetRequest(furnitures, sellingItems, buyingItems, takeBackList, surfaceList, itemLockPassword, handler)
	LoadingMask.instance:show(true)
	local req = ShareStreetExtension_pb.DecorateShareStreetRequest()
	local decType = 0
	if #furnitures > 0 or #sellingItems > 0 or #buyingItems > 0 then
		decType = decType + 1
	end
	for i = 1, #furnitures do
		table.insert(req.decoratedItems, HouseAgent.instance:fillFurniturePb(furnitures[i], false))
	end
	for _, itemId in pairs(sellingItems) do
		table.insert(req.sellingItems, itemId)
	end
	for _, itemId in pairs(buyingItems) do
		table.insert(req.buyingItems, itemId)
	end
	for _, serverId in ipairs(takeBackList) do
		table.insert(req.takeBackFurniture, serverId)
	end
	if surfaceList and #surfaceList > 0 then
		for _, surfacePb in ipairs(surfaceList) do
			table.insert(req.terrains, surfacePb)
		end
		decType = decType + 2
	end
	local hasChange1, sceneItems = ShareStreetSceneModel.instance:hasChangeShareItems()
	if hasChange1 then
		for pos, itemId in pairs(sceneItems) do
			local shareItem = ShareStreetExtension_pb.ShareStreetSceneItemNO()
			shareItem.positionId = pos
			shareItem.itemId = itemId
			table.insert(req.publicItems, shareItem)
		end
		decType = decType + 32
	end
	local hasChange2, userSceneItems = ShareStreetSceneModel.instance:hasChangeUserItems()
	if hasChange2 then
		for pos, itemId in pairs(userSceneItems) do
			local shareItem = ShareStreetExtension_pb.ShareStreetSceneItemNO()
			shareItem.positionId = pos
			shareItem.itemId = itemId
			table.insert(req.personalItems, shareItem)
		end
		decType = decType + 16
	end
	req.decorateType = decType
	req.itemLockPassword = itemLockPassword
	self.decorateShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleDecorateShareStreetReply(status, msg)
	LoadingMask.instance:close()
	if status == 0 then
		HouseModel.instance.houseMo:getFurnitureModel(UserInfo.userId).surfaceServerDatas = msg.terrains
		self.decorateShareStreetHandler(msg)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.decorateShareStreetHandler = nil
end

function ShareStreetAgent:sendChangeShareStreetHouseRequest(houseId, handler)
	LoadingMask.instance:show()
	local req = ShareStreetExtension_pb.ChangeShareStreetHouseRequest()
	req.houseId = houseId
	self.changeShareStreetHouseHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleChangeShareStreetHouseReply(status, msg)
	LoadingMask.instance:close()
	self.changeShareStreetHouseHandler(status == 0)
	if status == 0 then
	else
		DialogHelper.showErrorMsg(status)
	end
	self.changeShareStreetHouseHandler = nil
end

function ShareStreetAgent:sendModifyStreetSceneItemRequest(positionId, itemId, handler)
	local req = ShareStreetExtension_pb.ModifyStreetSceneItemRequest()
	req.positionId = positionId
	req.itemId = itemId
	self.modifyStreetSceneItemHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleModifyStreetSceneItemReply(status, msg)
	if status == 0 then
		ShareStreetSceneModel.instance:setSceneItem(msg.sceneItems)
		ShareStreetController.instance:localNotify(ShareStreetLocalNotify.OnChangeSceneItem, msg.sceneItems)
		self.modifyStreetSceneItemHandler()
	else
		DialogHelper.showErrorMsg(status)
	end
	self.modifyStreetSceneItemHandler = nil
end

function ShareStreetAgent:sendGetPersonShareFurnitureCountRequest(handler)
	LoadingMask.instance:show()
	local req = ShareStreetExtension_pb.GetPersonShareFurnitureCountRequest()
	self.getPersonShareFurnitureCountHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetPersonShareFurnitureCountReply(status, msg)
	LoadingMask.instance:close()
	if status == 0 then
		local furniturecount = msg.furnitureCount
		local list = {}
		for i = 1, #furniturecount do
			local item = furniturecount[i]
			local newItem = Item.New(item.furnitureId, item.count)
			newItem.useCount = item.useCount
			table.insert(list, newItem)
		end
		self.getPersonShareFurnitureCountHandler(list)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getPersonShareFurnitureCountHandler = nil
end

function ShareStreetAgent:sendGetStreetShareFurnitureCountRequest(isTotal, handler)
	self:_block("SearchShareStreetRequest", true)
	local req = ShareStreetExtension_pb.GetStreetShareFurnitureCountRequest()
	req.isTotal = isTotal
	self.getStreetShareFurnitureCountHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetStreetShareFurnitureCountReply(status, msg)
	self:_block("SearchShareStreetRequest", false)
	if status == 0 then
		local totalcount = msg.totalCount
		local userfurniturecount = msg.userFurnitureCount
		self.getStreetShareFurnitureCountHandler(msg.totalCount, msg.userFurnitureCount)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getStreetShareFurnitureCountHandler = nil
end

function ShareStreetAgent:sendVisitShareStreetRequest(userId, handler)
	local req = ShareStreetExtension_pb.VisitShareStreetRequest()
	req.userId = userId
	self.visitShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleVisitShareStreetReply(status, msg)
	if status == 0 then
	else
		DialogHelper.showErrorMsg(status)
	end
	self.visitShareStreetHandler = nil
end

function ShareStreetAgent:sendGetStreetRecommendListRequest(handler)
	local req = ShareStreetExtension_pb.GetStreetRecommendListRequest()
	self.getStreetRecommendListHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetStreetRecommendListReply(status, msg)
	if status == 0 then
		local recommendlist = msg.recommendList
		if self.getStreetRecommendListHandler then
			self.getStreetRecommendListHandler(recommendlist)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getStreetRecommendListHandler = nil
end

function ShareStreetAgent:sendSearchShareStreetRequest(searchKey, searchType, handler)
	self:_block("SearchShareStreetRequest", true)
	local req = ShareStreetExtension_pb.SearchShareStreetRequest()
	req.searchKey = searchKey
	req.searchType = searchType
	self.searchShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleSearchShareStreetReply(status, msg)
	self:_block("SearchShareStreetRequest", false)
	if status == 0 then
		local streetinfo = msg.streetInfo
		if self.searchShareStreetHandler then
			self.searchShareStreetHandler(streetinfo)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.searchShareStreetHandler = nil
end

function ShareStreetAgent:sendGetFriendStreetListRequest(handler)
	self:_block("GetFriendStreetListRequest", true)
	local req = ShareStreetExtension_pb.GetFriendStreetListRequest()
	self.getFriendStreetListHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetFriendStreetListReply(status, msg)
	self:_block("GetFriendStreetListRequest", false)
	if status == 0 then
		local streetlist = msg.streetList
		if self.getFriendStreetListHandler then
			self.getFriendStreetListHandler(streetlist)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getFriendStreetListHandler = nil
end

function ShareStreetAgent:sendGetStreetReceiveInviteListRequest(handler)
	self:_block("GetStreetReceiveInviteListRequest", true)
	local req = ShareStreetExtension_pb.GetStreetReceiveInviteListRequest()
	self.getStreetReceiveInviteListHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleGetStreetReceiveInviteListReply(status, msg)
	self:_block("GetStreetReceiveInviteListRequest", false)
	if status == 0 then
		local invitelist = msg.inviteList
		if self.getStreetReceiveInviteListHandler then
			self.getStreetReceiveInviteListHandler(invitelist)
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.getStreetReceiveInviteListHandler = nil
end

function ShareStreetAgent:sendCancelApplyJoinShareStreetRequest(streetId, handler)
	self:_block("CancelApplyJoinShareStreetRequest", true)
	local req = ShareStreetExtension_pb.CancelApplyJoinShareStreetRequest()
	req.streetId = streetId
	self.cancelApplyJoinShareStreetHandler = handler
	self:sendMsg(req)
end

function ShareStreetAgent:handleCancelApplyJoinShareStreetReply(status, msg)
	self:_block("CancelApplyJoinShareStreetRequest", false)
	if status == 0 then
		if self.cancelApplyJoinShareStreetHandler then
			self.cancelApplyJoinShareStreetHandler()
		end
	else
		DialogHelper.showErrorMsg(status)
	end
	self.cancelApplyJoinShareStreetHandler = nil
end

function ShareStreetAgent:handleStreetFurnitureInfoChangePush(status, msg)
	if status == 0 then
		ShareStreetController.instance:localNotify(ShareStreetLocalNotify.OnFurnitureInfoChangePush, msg)
	else
		DialogHelper.showErrorMsg(status)
	end
	self.streetFurnitureInfoChangeHandler = nil
end

function ShareStreetAgent:sendGetUserJoinedStreetIdRequest(userId,handler)
	self:_block("GetUserJoinedStreetIdRequest", true)
    local req = ShareStreetExtension_pb.GetUserJoinedStreetIdRequest()
	req.userId = userId
	self.getUserJoinedStreetIdHandler = handler
    self:sendMsg(req)
end

function ShareStreetAgent:handleGetUserJoinedStreetIdReply(status, msg)
	self:_block("GetUserJoinedStreetIdRequest", false)
    if status == 0 then
		local streetid = msg.streetId
		if self.getUserJoinedStreetIdHandler then
			self.getUserJoinedStreetIdHandler(streetid)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.getUserJoinedStreetIdHandler = nil
end

ShareStreetAgent.instance = ShareStreetAgent.New()

return ShareStreetAgent
