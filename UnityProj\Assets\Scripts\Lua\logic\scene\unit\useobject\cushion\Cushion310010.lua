module("logic.scene.unit.useobject.cushion.Cushion310010", package.seeall)

local Cushion310010 = class("Cushion310010")

function Cushion310010:ctor(unit, isScene)
	self._unit = unit
	self._isScene = isScene
	self._playingEffect = false
end

function Cushion310010:getCushionIdleState()
	if self._unit._isFull then
		return "idle2"
	else
		return "idle1"
	end
end

function Cushion310010:hasFullAnimation()
	return true
end

function Cushion310010:showPose(params)
	local playerUnit, sitPos = params[1], params[3]
	local isFaceLeft = UnitDirection.isFaceLeft(self._unit.dir)
	local dir
	if sitPos == 1 then
		dir = UnitDirection.getDirection(false, isFaceLeft)
	elseif sitPos == 3 then
		dir = UnitDirection.getDirection(true, not isFaceLeft)
	else
		dir = UnitDirection.getDirection(true, isFaceLeft)
	end
	playerUnit:setDirection(dir)
	tfutil.SetScale(playerUnit.go, 1.25)
end

function Cushion310010:setFullAnimation(params)
	local playerUnit, sitPos = params[1], params[2]
	playerUnit.skinView:setAnimation("4.0_dgnd_xiongxiongtiaoqi_" .. sitPos, true, true)
	return true
end

function Cushion310010:setDir(params)
	local name = UnitDirection.isFaceLeft(params[1]) and "L" or "R"
	for i = 1, #self._unit._childSpine do
		self._unit._childSpine[i].helper.animComp.initialSkinName = name
		self._unit._childSpine[i].helper.animComp.Skeleton:SetSkin(name)
		self._unit._childSpine[i].helper.animComp.Skeleton:SetSlotsToSetupPose()
	end
end

return Cushion310010 