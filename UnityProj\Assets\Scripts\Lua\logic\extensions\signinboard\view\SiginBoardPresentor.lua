module("logic.extensions.signinboard.view.SiginBoardPresentor",package.seeall)

local SiginBoardPresentor = class("SiginBoardPresentor", ViewPresentor)

--- 配置view需要的资源列表
function SiginBoardPresentor:dependWhatResources()
	return {"ui/anniversary/signinboard/siginboardview.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function SiginBoardPresentor:buildViews()
	return {
        SiginBoardView.New(),
        ActivityEndTimeUltimateComp.New(GameEnum.ActivityEnum.ACTIVITY_466, "txtTime", true, true)
    }
end

--- 配置view所在的ui层
function SiginBoardPresentor:attachToWhichRoot()
	return ViewRootType.FullScreen
end

return SiginBoardPresentor