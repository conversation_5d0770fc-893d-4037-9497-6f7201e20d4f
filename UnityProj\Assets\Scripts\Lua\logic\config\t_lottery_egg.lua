-- {excel:N扭蛋.xlsx, sheetName:export_玩法}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_lottery_egg", package.seeall)

local title = {id=1,name=2,type=3,externalShare=4,credits=5,animationType=6,BuyTicketsCost=7,ticketId=8,ticketId2=9,ticketNums=10,tenTicketNum=11,currencyId=12,currencyNums=13,tenCurrencyNum=14,dailyMax=15,dailyFreeMax=16,dailyFreeCd=17,rarenessWeights=18,guarantee1Activate=19,guarantee1Rareness=20,guarantee2Activate=21,guarantee3Activate=22,guarantee3Rareness=23,showInHUD=24,showGoods=25,popGoods=26,showMoreCatalog=27,showLimitArchive=28,advs=29,actRedPoint=30,storyBgType=31,shareTexture=32,isBack=33,lotteryNumsPrize=34,activateTime=35,endTime=36,lotteryEndTime=37,extParams=38}

local dataList = {
	{1, "免费", 1, 0, nil, "p1", nil, 0, 0, nil, nil, 0, nil, nil, 0, 0, 0, {685,300,0,15}, 10, 2, "", 50, {4}, false, nil, 0, 0, false, nil, nil, 0, "", false, nil, "2020-06-29T11:00:00", "2020-12-31T23:59:59", nil, nil},
	{2, "金币服装彩蛋", 2, 0, nil, "p2", {num=15000,id=1}, 16000005, 0, {1,10}, nil, 1, {15000,150000}, nil, 1000, 3, 900, {500,400,80,20}, 10, 3, "", 200, {4}, false, nil, 0, 0, false, {"image/lottery/lottery2.jpg"}, nil, 0, "", false, nil, nil, nil, nil, nil},
	{3, "晶钻服装彩蛋", 3, 0, {num=10,id=100}, "p3", {num=60,id=2}, 16000006, 0, {1,10}, nil, 2, {60,600}, nil, 1000, -1, 172800, {0,0,500,400,80,20}, 10, 5, "4000:300,5000:200,7000:30", 200, {6}, false, nil, 0, 0, false, {"image/lottery/lottery3_2.jpg","image/lottery/lottery3.jpg","image/lottery/lottery3_1.jpg","image/lottery/lottery3_3.jpg"}, nil, 0, "", false, nil, nil, nil, nil, nil},
	{4, "爱心服装彩蛋", 4, 0, nil, "p4", {num=200,id=3}, 16000007, 0, {1,10}, nil, 3, {200,2000}, nil, 1000, 0, 0, {0,500,400,80,20}, 10, 4, "", 200, {5}, false, nil, 0, 0, false, {"image/lottery/lottery4.jpg"}, nil, 0, "", false, nil, nil, nil, nil, nil},
	{5, "图书馆", 5, 0, nil, "p3", nil, 16000088, 0, {1,10}, nil, 2, {100,1000}, nil, 1000, 0, 0, {350,350,80,60,40,90,10,20}, 0, 0, "", 20, {8}, false, nil, 0, 0, false, nil, nil, 0, "", false, nil, nil, nil, nil, nil},
	{6, "岛民卡宝物池", 6, 0, nil, "p3", nil, 16000089, 0, {1,10}, nil, 2, {100,1000}, nil, 1000, 0, 0, {0,0,800,180,20}, 0, 0, "", 50, {5}, false, nil, 0, 0, false, nil, nil, 0, "", false, nil, nil, nil, nil, nil},
	{7, "许愿池", 7, 0, nil, "p3", nil, 0, 0, {1,10}, nil, 8, {100,1000}, nil, 1000, 0, 0, {0,0,850,100,50}, 10, 4, "", 200, {5}, false, nil, 0, 0, false, nil, nil, 0, "", false, nil, nil, nil, nil, nil},
	{8, "金币家具彩蛋", 2, 0, nil, "p2", {num=10000,id=1}, 16000053, 0, {1,10}, nil, 1, {10000,100000}, nil, 1000, 3, 900, {500,400,80,20}, 10, 3, "", 200, {4}, false, nil, 0, 0, false, {"image/lottery/lottery8.jpg"}, nil, 0, "", false, nil, nil, nil, nil, nil},
	{9, "晶钻家具彩蛋", 3, 0, {num=10,id=101}, "p3", {num=40,id=2}, 16000054, 0, {1,10}, nil, 2, {40,400}, nil, 1000, -1, 172800, {0,0,500,400,80,20}, 10, 5, "4000:300,5000:200,7000:30", 200, {6}, false, nil, 0, 0, false, {"image/lottery/lottery9_3.jpg","image/lottery/lottery9.jpg","image/lottery/lottery9_1.jpg","image/lottery/lottery9_2.jpg"}, nil, 0, "", false, nil, nil, nil, nil, nil},
	{10, "爱心家具彩蛋", 4, 0, nil, "p4", {num=150,id=3}, 16000055, 0, {1,10}, nil, 3, {150,1500}, nil, 1000, 0, 0, {0,500,400,80,20}, 10, 4, "", 200, {5}, false, nil, 0, 0, false, {"image/lottery/lottery10.jpg"}, nil, 0, "", false, nil, nil, nil, nil, nil},
	{137, "郁香祈愿", 137, 50, {num=10,id=102}, "1.4", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery11.prefab"}, nil, 0, "", true, nil, nil, nil, nil, nil},
	{162, "星际祈愿", 162, 10, nil, "1.1", {num=60,id=2}, 16000144, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503002}, 0, 1, false, {"ui/lottery/bg/lottery12.prefab"}, nil, 0, "lottery12", false, nil, nil, nil, nil, nil},
	{108, "快乐祈愿", 108, 46, {num=10,id=103}, "1.3", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery108.prefab"}, nil, 0, "", true, nil, nil, nil, nil, nil},
	{171, "雾影密室", 171, 80, {num=10,id=118}, "1.2", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery171.prefab"}, nil, 0, "", true, nil, nil, nil, nil, nil},
	{208, "蜂蜂祈愿", 208, 75, {num=10,id=105}, "1.5", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery208.prefab"}, nil, 0, "", true, nil, nil, nil, nil, nil},
	{212, "冰雪祈愿", 212, 89, {num=10,id=106}, "1.6", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery212.prefab"}, nil, 0, "", true, nil, nil, nil, nil, nil},
	{224, "团年祈愿", 224, 94, {num=10,id=107}, "1.7", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery224.prefab"}, {"Lottery_HUD"}, 4, "", true, nil, nil, nil, nil, nil},
	{226, "爱神祈愿", 226, 96, {num=10,id=108}, "1.8", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery226.prefab"}, {"activity226"}, 0, "", true, nil, nil, nil, nil, nil},
	{230, "海神祈愿", 230, 85, {num=10,id=109}, "1.9", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery230.prefab"}, {"activity230"}, 0, "", true, nil, nil, nil, nil, nil},
	{239, "音符祈愿", 239, 10, {num=10,id=110}, "1.1", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503141}, 0, 1, true, {"ui/lottery/bg/lotterycommon.prefab"}, nil, 0, "", false, nil, nil, nil, nil, nil},
	{240, "茶壶呼呼", 240, 102, {num=10,id=111}, "1.11", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery240.prefab"}, {"activity240"}, 0, "", true, nil, nil, nil, nil, nil},
	{241, "活力扭蛋机", 241, 0, nil, "", nil, 16000308, 0, {1,10}, nil, 0, nil, nil, 1000, 0, 0, {0,0,0,0,1000,0}, 0, 0, "", 0, nil, false, nil, 0, 0, false, nil, nil, 0, "", false, nil, nil, nil, nil, nil},
	{253, "奇游祈愿", 253, 100, {num=10,id=112}, "1.12", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery253.prefab"}, {"Lottery_HUD"}, 0, "", true, nil, nil, nil, nil, nil},
	{300, "炫彩祈愿", 300, 121, {num=10,id=113}, "2.0", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery300.prefab"}, {"activity300"}, 4, "", true, nil, nil, nil, nil, nil},
	{301, "星际祈愿", 301, 124, {num=10,id=114}, "2.1", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery301.prefab"}, {"activity301"}, 0, "", true, nil, nil, nil, nil, nil},
	{302, "星际祈愿", 302, 69, {num=10,id=115}, "1.1", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery12.prefab"}, nil, 0, "", true, nil, nil, nil, nil, nil},
	{303, "快乐祈愿", 303, 103, {num=10,id=116}, "2.2", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery303.prefab"}, {"Lottery_HUD","activity303"}, 0, "", true, nil, nil, nil, nil, nil},
	{304, "月灵祈愿", 304, 106, {num=10,id=117}, "2.3", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery304.prefab"}, {"activity1555"}, 0, "", true, nil, nil, nil, nil, nil},
	{305, "雾月祈愿", 305, 107, {num=10,id=119}, "2.4", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery305.prefab"}, {"activity305"}, 0, "", true, nil, nil, nil, nil, nil},
	{306, "星之祈愿", 306, 132, {num=10,id=120}, "2.5", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery306.prefab"}, {"Lottery_HUD","activity306"}, 0, "", true, nil, nil, nil, nil, nil},
	{307, "光影祈愿", 307, 133, {num=10,id=121}, "2.6", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery307.prefab"}, {"Lottery_HUD","activity307"}, 0, "", true, nil, nil, nil, nil, nil},
	{308, "紫禁祈福", 308, 10, {num=10,id=122}, "2.7", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503314,900079}, 0, 1, true, {"ui/lottery/bg/lottery308.prefab"}, {"Lottery_HUD","activity308"}, 0, "", false, nil, nil, nil, nil, nil},
	{309, "春日祈愿", 309, 138, {num=10,id=123}, "2.8", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery309.prefab"}, {"Lottery_HUD","activity309"}, 4, "", true, nil, nil, nil, nil, nil},
	{310, "城市祈愿", 310, 140, {num=10,id=124}, "2.9", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery310.prefab"}, {"Lottery_HUD"}, 0, "", true, nil, nil, nil, nil, nil},
	{311, "冒险之旅", 311, 142, {num=10,id=125}, "2.10", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 0, 0, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, nil, 0, 1, false, {"ui/lottery/bg/lottery311.prefab"}, {"Lottery_HUD"}, 0, "", true, nil, nil, nil, nil, nil},
	{312, "怪盗寻踪", 312, 10, {num=10,id=126}, "2.11", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503391,900102}, 0, 1, true, {"ui/lottery/bg/lottery312.prefab"}, {"Lottery_HUD","activity312"}, 0, "", false, nil, nil, nil, nil, nil},
	{313, "幻海祈愿", 313, 149, {num=10,id=127}, "3.0", {num=60,id=2}, 16000212, 0, {1,10}, nil, 2, {60,600}, nil, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503410,900106}, 0, 1, true, {"ui/lottery/bg/lottery313.prefab"}, {"Lottery_HUD","activity313"}, 3, "", false, nil, nil, nil, nil, nil},
	{314, "星际祈愿", 314, 10, {num=10,id=128}, "3.1", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503429,900118}, 0, 1, true, {"ui/lottery/bg/lottery314.prefab"}, {"Lottery_HUD","activity314"}, 3, "", false, nil, nil, nil, nil, nil},
	{315, "奇院呼神", 315, 10, {num=10,id=129}, "3.2", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503448,900120}, 0, 1, true, {"ui/lottery/bg/lottery315.prefab"}, {"Lottery_HUD","activity315"}, 3, "", false, nil, nil, nil, nil, nil},
	{316, "快乐祈愿", 316, 10, {num=10,id=130}, "3.3", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503467,900124}, 0, 101, true, {"ui/lottery/bg/lottery316.prefab"}, {"Lottery_HUD","activity316"}, 3, "", false, nil, nil, nil, nil, nil},
	{317, "人偶祈愿", 317, 10, {num=10,id=131}, "3.4", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503486,900129}, 0, 101, true, {"ui/lottery/bg/lottery317.prefab"}, {"Lottery_HUD","activity317"}, 3, "", false, nil, nil, nil, nil, nil},
	{318, "雾月契妖", 318, 10, {num=10,id=132}, "3.5", {num=60,id=2}, 16000212, 16080001, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503505,900140}, 0, 101, true, {"ui/lottery/bg/lottery318.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, nil, nil},
	{319, "绮梦祈愿", 319, 10, {num=10,id=133}, "3.6", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503524,900144}, 0, 101, true, {"ui/lottery/bg/lottery319.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, nil, nil},
	{320, "聚宝楼", 320, 10, {num=10,id=134}, "3.7", {num=60,id=2}, 16000212, 16080002, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503543,900148}, 0, 101, true, {"ui/lottery/bg/lottery320.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, "2025-02-12T23:59:59", nil},
	{321, "时花祈愿", 321, 10, {num=10,id=135}, "3.8", {num=60,id=2}, 16000212, 0, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503563,900160}, 0, 101, true, {"ui/lottery/bg/lottery321.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, "2025-03-12T23:59:59", nil},
	{322, "寻心祈愿", 322, 10, {num=10,id=136}, "3.9", {num=60,id=2}, 16000212, 16080003, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503582,900164}, 0, 101, true, {"ui/lottery/bg/lottery322.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, "2025-04-09T23:59:59", nil},
	{323, "摩登港城", 323, 10, {num=10,id=137}, "3.10", {num=60,id=2}, 16000212, 16080004, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "", 0, nil, true, {503601,900188}, 0, 101, true, {"ui/lottery/bg/lottery323.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, "2025-05-07T23:59:59", nil},
	{324, "市集祈愿", 324, 10, {num=10,id=138}, "3.11", {num=60,id=2}, 16000212, 16080005, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "200:1", 0, nil, true, {503620,900190}, 900189, 101, true, {"ui/lottery/bg/lottery324.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, "2025-06-05T23:59:59", nil},
	{325, "绘心祈愿", 325, 10, {num=10,id=139}, "3.12", {num=60,id=2}, 16000212, 16080006, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "200:1", 0, nil, true, {503639,900195}, 900194, 101, true, {"ui/lottery/bg/lottery325.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, "2025-06-27T23:59:59", nil},
	{326, "灵感祈愿", 326, 10, {num=10,id=140}, "4.0", {num=60,id=2}, 16000212, 16080007, {1,10}, {1,8}, 2, {60,600}, {1,480}, 1000, 1, -1, {0,0,0,700,300,0}, 0, 0, "200:1", 0, nil, true, {503658,900201}, 900200, 101, true, {"ui/lottery/bg/lottery326.prefab"}, {"Lottery_HUD","Lottery_New_Activity"}, 3, "", false, nil, nil, nil, "2025-07-23T23:59:59", nil},
	{401, "遇天狼", 401, 0, nil, "p3", nil, 16000436, 0, {1,10}, nil, 2, {100,1000}, nil, 1000, 0, 0, {350,350,80,60,40,90,10,20}, 0, 0, "", 20, {8}, false, nil, 0, 0, false, nil, nil, 0, "", false, nil, nil, nil, nil, {treasure="90:4017001",traveller="40:4018001"}},
}

local t_lottery_egg = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
	[9] = dataList[9],
	[10] = dataList[10],
	[137] = dataList[11],
	[162] = dataList[12],
	[108] = dataList[13],
	[171] = dataList[14],
	[208] = dataList[15],
	[212] = dataList[16],
	[224] = dataList[17],
	[226] = dataList[18],
	[230] = dataList[19],
	[239] = dataList[20],
	[240] = dataList[21],
	[241] = dataList[22],
	[253] = dataList[23],
	[300] = dataList[24],
	[301] = dataList[25],
	[302] = dataList[26],
	[303] = dataList[27],
	[304] = dataList[28],
	[305] = dataList[29],
	[306] = dataList[30],
	[307] = dataList[31],
	[308] = dataList[32],
	[309] = dataList[33],
	[310] = dataList[34],
	[311] = dataList[35],
	[312] = dataList[36],
	[313] = dataList[37],
	[314] = dataList[38],
	[315] = dataList[39],
	[316] = dataList[40],
	[317] = dataList[41],
	[318] = dataList[42],
	[319] = dataList[43],
	[320] = dataList[44],
	[321] = dataList[45],
	[322] = dataList[46],
	[323] = dataList[47],
	[324] = dataList[48],
	[325] = dataList[49],
	[326] = dataList[50],
	[401] = dataList[51],
}

t_lottery_egg.dataList = dataList
local mt
if LotteryEggDefine then
	mt = {
		__cname =  "LotteryEggDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or LotteryEggDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_lottery_egg