module("logic.scene.room.factory.RoomSceneFactory", package.seeall)
local RoomSceneFactory = class("RoomSceneFactory", SceneUnitManager)

local UnitCls = {}
UnitCls[FurnitureType.ResourceType.Furniture] = SceneUnitType.Furniture
UnitCls[FurnitureType.ResourceType.Plant] = SceneUnitType.Plant
UnitCls[FurnitureType.ResourceType.SpineFurniture] = SceneUnitType.SpineFurniture
UnitCls[FurnitureType.ResourceType.ElfEgg] = SceneUnitType.ElfEgg
UnitCls[FurnitureType.ResourceType.Surface] = SceneUnitType.SurfaceFurniture
UnitCls[FurnitureType.ResourceType.Special] = SceneUnitType.SpecialFurniture


function RoomSceneFactory:onEnterSceneFinished(curSceneId, curBornX, curBornZ)
	RoomSceneFactory.super.onEnterSceneFinished(self, curSceneId, curBornX, curBornZ)
	self._unloadUnit = {}
	self._roomEdit = RoomEdit.New()
	self._roomEdit:onInit()
	self:_buildRoomSuite()
	RoomController.instance:registerLocalNotify(RoomNotifyName.DoLoadNotInScreenFurniture, self.loadAllNotInScreenUnit, self)
end

-- function RoomSceneFactory:setOnloadUnit(callBack, callBackTarget)
-- 	self.callBack = callBack
-- 	self.callBackTarget = callBackTarget
-- end

--build家具
--uniqueId：家具配置表Id_家具标志位Id
function RoomSceneFactory:buildFurniture(uniqueId, mirror, serverId, waitForOpenScene, callBack, target, userId, isShare)
	local idStrList = uniqueId:splitToNumber("_")
	local furnitureId = idStrList[1]
	local unitParams = {userId = userId, isShare = isShare, uniqueId = uniqueId, itemId = furnitureId, mirror = mirror, serverId = serverId, waitForOpenScene = waitForOpenScene, callBack = callBack, target = target}
	local cfg = FurnitureConfig.getFurnitureDefine(furnitureId)
	return self:_createUnit(cfg.resourceType, unitParams)
end
--build彩景
function RoomSceneFactory:buildSpecialFurniture(uniqueId, serverId, callBack, target, userId)
	local furnitureId = uniqueId
	local unitParams = {userId = userId, uniqueId = uniqueId, itemId = furnitureId, serverId = serverId, callBack = callBack, target = target}
	local cfg = FurnitureConfig.getFurnitureDefine(furnitureId)
	print("cfg.resourceType ======", cfg.resourceType)
	return self:_createUnit(cfg.resourceType, unitParams)
end
--build 种植物
function RoomSceneFactory:buildCrops(uniqueId, cropsId, userId)
	local unitParams = {userId = userId, uniqueId = uniqueId, itemId = cropsId, handler = "PlantHandler"}
	return self:_createUnit(FurnitureType.ResourceType.Plant, unitParams)
end
--build垃圾
function RoomSceneFactory:buildRubbish(serverId, id, userId)
	local uniqueId = string.format("r_%d_%d", serverId, id)
	local unitParams = {userId = userId, uniqueId = uniqueId, itemId = id, serverId = serverId}
	local cfg = FurnitureConfig.getFurnitureDefine(id)
	return self:_createUnit(cfg.resourceType, unitParams)
end
--==============================--
--desc:屏幕中央开始找一个，格子坐标
--==============================--
function RoomSceneFactory:getFreeCoordForSceneObjFromScreenCenter(width, height)
	return self:getRandomPosForSceneObj(width, height)
end
--==============================--
--desc:全地图随机1个，世界坐标
--==============================--
function RoomSceneFactory:getRandomPosForSceneObj(width, height, pos)
	if self._roomSuite == nil then return false end
	return self._roomSuite:getComponent(RoomCompMaper):getFreeCoordForSceneObjFromScreenCenter(width, height, pos)
end

function RoomSceneFactory:getWorldPosFromMapPos(cx, cy)
	local maper = self._roomSuite:getComponent(RoomCompMaper)
	return maper:mapPosToWorldPos2(FurnitureType.CoordType.Floor, false, cx, cy)
end

function RoomSceneFactory:getSortZByPos(wPosX, wPosY, callBack)
	local maper = self._roomSuite:getComponent(RoomCompMaper)
	return maper:getSortZByPos(wPosX, wPosY, callBack)
end

--==============================--
--desc:全地图随机N个，格子坐标
--==============================--
function RoomSceneFactory:getFreeCoordsForSceneObj(width, height, count)
	if not self._roomSuite then
		printError("小屋还没初始化好，就调用随机格子getRandomPosForSceneObj")
		return false
	end
	local sceneObjMo = FurnitureSceneObjMo.New(width, height)
	local maper = self._roomSuite:getComponent(RoomCompMaper)
	return maper:getFreeCoordsForSceneObj(sceneObjMo, count)
end
function RoomSceneFactory:getIsFreeForSceneObj(coodX, coordY, width, height)
	if not self._roomSuite then
		printError("小屋还没初始化好，就调用随机格子getRandomPosForSceneObj")
		return false
	end
	local sceneObjMo = FurnitureSceneObjMo.New(width, height)
	sceneObjMo:setCoord(coodX, coordY)
	local maper = self._roomSuite:getComponent(RoomCompMaper)
	return maper:getIsCanPlace(sceneObjMo)
end

function RoomSceneFactory:_createUnit(type, params)
	local unitId = params.unitId or self:_getUnitNewId()
	params.id = unitId
	params.tag = UnitTag.Furniture
	local unitType = UnitCls[type]
	local unit = self:addUnit(unitType, params)
	unit:initData(self, self:getRoomSuite())
	unit:load(self._onUnitLoaded, self)
	table.insert(self._furnitureList, unit)
	self._unitDict[unit.id] = unit
	return unit
end

function RoomSceneFactory:_getUnitNewId()
	self._unitStartId = self._unitStartId + 1
	return self._unitStartId
end

function RoomSceneFactory:rebuildHouse()
	self.isRebuild = true
	self.mapDataUrl = RoomConfig.getHouseWalkDataUrl(HouseModel.instance:getUserProperty(HouseUserProperty.HouseType))
	getres(self.mapDataUrl, self._onReloadMap, self)
end

function RoomSceneFactory:_onReloadMap(res)
	if res.IsSuccess then
		self:destroySuite()
		self.mapRes = res
		res:Retain()
		local mapData = res:GetMainAsset()
		SceneManager.instance:getCurScene().mapMgr:rebuildHouse()
		SceneManager.instance:getCurScene().mapMgr:setMapData(mapData)
		self:_buildRoomSuite()
	end
end

--构建当前的套间
function RoomSceneFactory:_buildRoomSuite()
	if SceneManager.instance:getCurScene() == nil then return end
	RoomController.instance:localNotify(RoomNotifyName.StartBuildRoom)
	self:initUnitState()
	self._roomSuite = RoomSuite.New(HouseModel.instance:getHouseMo(), self)
	self._roomSuite:initComponents()
end

function RoomSceneFactory:initUnitState()
	self._unitStartId = 0
	self._unitDict = {}
	self._furnitureList = {}
end

function RoomSceneFactory:_checkIsClearLoadRes()
	local curSceneType = SceneManager.instance:getCurSceneType()
	return SceneManager.instance:getCurScene() == nil or not table.indexof({SceneType.House, SceneType.Island}, curSceneType)
end

function RoomSceneFactory:_onUnitLoaded(unitComp)
	--加载的资源回来后需要检测是否还在当前场景，不在的话直接删除掉
	if self:_checkIsClearLoadRes() then
		return
	end
	local unit = unitComp._unit
	if unit.params and unit.params.callBack then
		unit.params.callBack(unit.params.target, unit, self)
	end
	RoomController.instance:localNotify(RoomNotifyName.FurnitureLoaded, unit, self)
	-- if self.callBack then
	-- 	self.callBack(self.callBackTarget, unit, self)
	-- end
end

function RoomSceneFactory:getRoomSuite()
	return self._roomSuite
end

function RoomSceneFactory:getHouseMo()
	if self._roomSuite == nil then return nil end
	return self._roomSuite.houseMo
end

function RoomSceneFactory:getFurnitureModel(userId)
	if self:getHouseMo() == nil then return nil end
	return self:getHouseMo():getFurnitureModel(userId)
end

function RoomSceneFactory:getRoomEdit()
	return self._roomEdit
end

function RoomSceneFactory:removeUnit(unitType, id)
	if self._roomSuite == nil then
		RoomSceneFactory.super.removeUnit(self, unitType, id)
		return
	end
	local isFurniture = false
	for _, type in pairs(UnitCls) do
		if type == unitType then
			local unit = self._unitDict[id]
			if unit then
				table.removebyvalue(self._furnitureList, unit)
				self._unitDict[id] = nil
				isFurniture = true
			end
			break
		end
	end
	local maper = self._roomSuite:getComponent(RoomCompMaper)
	if not isFurniture and maper then
		local unit = self:getUnit(unitType,id)
		if unit then
			maper:removeSceneObjectSortMap(unit.go)
		end
	end
	RoomSceneFactory.super.removeUnit(self, unitType, id)
end

function RoomSceneFactory:getDoorGo()
	return self._roomSuite:getComponent(RoomSuiteCompMesh).door
end

function RoomSceneFactory:getRoomUnit(id)
	if self._unitDict[id] then
		return self._unitDict[id]
	end
end

function RoomSceneFactory:getAllRoomUnit()
	return self._furnitureList
end

function RoomSceneFactory:onUnitBindFurniture(unit, bindParams)
	unit.bindingFurniture = bindParams
	local bindFurniture = math.floor(bindParams / 10)
	local bindPos = bindParams % 10
	local furnitureMo = HouseModel.instance.houseMo:getFurnitureModel(unit.userId):getFurnitureByServerId(bindFurniture)
	if furnitureMo then
		furnitureMo:setBindUnit(bindPos, unit)
	end
end

function RoomSceneFactory:onUnitUnbindFurniture(unit, bindParams)
	unit.bindingFurniture = nil
	local bindFurniture = math.floor(bindParams / 10)
	local bindPos = bindParams % 10
	local furnitureMo = HouseModel.instance.houseMo:getFurnitureModel(unit.userId ):getFurnitureByServerId(bindFurniture)
	if furnitureMo then
		furnitureMo:setBindUnit(bindPos, nil)
	end
end

function RoomSceneFactory:onExitScene()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.DoLoadNotInScreenFurniture, self.loadAllNotInScreenUnit, self)
	RoomEditModel.instance:clear()
	HouseModel.instance:clear()
	self:destroySuite()
	if self._roomEdit then
		self._roomEdit:onDestroy()
		self._roomEdit = nil
	end
	self._unitDict = nil
	self._furnitureList = nil
	self._unloadUnit = nil
	RoomSceneFactory.super.onExitScene(self)
end

function RoomSceneFactory:destroySuite()
	if self.mapRes then
		self.mapRes:Release()
		self.mapRes = nil
	end
	if self._roomSuite then
		self._roomSuite:destroy()
		self._roomSuite = nil
	end
end

function RoomSceneFactory:loadAllNotInScreenUnit()
	if self._unloadUnit == nil or #self._unloadUnit == 0 then
		self:_notifyLoadAll()
		return
	end
	for i = #self._unloadUnit, 1, -1 do
		self._unloadUnit[i]:_doLoad()
	end
	SceneTimer:setTimer(tonumber(GameUtils.getCommonConfig("LoadFurnitureNotInScreenTimeOut")), self._notifyLoadAll, self, false)
end

function RoomSceneFactory:addUnloadUnit(unit)
	if self._unloadUnit == nil then return end
	table.insert(self._unloadUnit, unit)
end

function RoomSceneFactory:removeUnloadUnit(unit)
	if self._unloadUnit == nil then return end
	local index = table.indexof(self._unloadUnit, unit)
	if index ~= false then
		table.remove(self._unloadUnit, index)
		if #self._unloadUnit == 0 then
			self:_notifyLoadAll()
		end
	end
end

function RoomSceneFactory:getCameraViewRect()
	if self._cameraViewRect == nil or self._lastGetTime == nil or ServerTime.now() - self._lastGetTime > 1 then
		self._cameraViewRect = SceneManager.instance:getCurScene().camera:getViewRect(true)
		self._lastGetTime = ServerTime.now()
	end
	return self._cameraViewRect
end

function RoomSceneFactory:_notifyLoadAll()
	SceneTimer:removeTimer(self._notifyLoadAll, self)
	RoomController.instance:localNotify(RoomNotifyName.OnLoadNotInScreenFurniture)
end

return RoomSceneFactory 