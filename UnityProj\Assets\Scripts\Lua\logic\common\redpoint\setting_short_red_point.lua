local setting_short_red_point = {}

-- life :红点有效期，默认60天，过期清除。

--捣蛋大会红点
setting_short_red_point.Daodangui_Hud = {}
for i = 1, 11 do
	setting_short_red_point["Daodangui_Tab" .. i] = {parent = {"Daodangui_Hud"}}
end
setting_short_red_point.ActivityDiscountDay = {activityId = 147}
setting_short_red_point.Daodangui_Task_Finish = {parent = {"Daodangui_Tab8"}}
setting_short_red_point.Daodangui_Login = {parent = {"Daodangui_Hud"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_109_RED_POINT}
setting_short_red_point.Daodangui_BingDiao = {parent = {"Daodangui_Hud"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_101_RED_POINT}
-- setting_short_red_point.Daodangui_DaoCaoWu = {parent = {"Daodangui_Hud"},isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_192_RED_POINT,activityId = GameEnum.ActivityEnum.ACTIVITY_192_ICE_SCULPTURE}
setting_short_red_point.HarvestSeasonRewardRedpoint = {activityId=183,isFirstClickRP = true,parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_183_RED_POINT}
setting_short_red_point.AobiBank = {key = GameEnum.RedPointTypeEnum.ACTIVITY_102_REWARD_RED_POINT}
setting_short_red_point.UCG_VOTE = {parent = {"ActivityWinterFirst"},key = GameEnum.RedPointTypeEnum.ACTIVITY_190_RED_POINT,
	activityId = GameEnum.ActivityEnum.ACTIVITY_190_UGC_VOTE}

--大富翁点数奖励
setting_short_red_point.Monopoly_Point_Reward = {key = GameEnum.RedPointTypeEnum.MONOPOLY_RED_POINT,activityId =125}
--大富翁任务
setting_short_red_point.Monopoly_Task_Finish = {activityId =125}
--大富翁每日红点
setting_short_red_point.Monopoly_Daily_Free = {activityId =125,isEveryDayRP = true}

setting_short_red_point.GiftBagSaleSelectRed = {activityId=GameEnum.ActivityEnum.ACTIVITY_460,isEveryDayRP = true}


--- 累计充值(小熊钱罐)
setting_short_red_point.Act_Multi_Recharge = {key = GameEnum.RedPointTypeEnum.ACTIVITY_193_RED_POINT,activityId = GameEnum.ActivityEnum.ACTIVITY_193_RECHARGE}
setting_short_red_point.Act_NewHand_Recharge = {key = GameEnum.RedPointTypeEnum.ACTIVITY_283_RED_POINT,activityId = GameEnum.ActivityEnum.ACTIVITY_283}

--花神签到HUD红点
setting_short_red_point.GOF_HUD = {}
--setting_short_red_point.GOF_Sign = {key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT, parent = {"GOF_HUD"}}
setting_short_red_point.GOF_Grocery = {parent = {"GOF_HUD"}}
setting_short_red_point.GOF_Grocery_New = {parent = {"GOF_Grocery"}}
setting_short_red_point.GOF_Task = {parent = {"GOF_HUD"}}
setting_short_red_point.GOF_Task_New = {parent = {"GOF_Task"}}
setting_short_red_point.GOF_Carving = {parent = {"GOF_HUD"}}
setting_short_red_point.GOF_Carving_New = {parent = {"GOF_Carving"}}
setting_short_red_point.GOF_Carving_Reward = {key = GameEnum.RedPointTypeEnum.ACTIVITY_130_RED_POINT, parent = {"GOF_Carving"}}
setting_short_red_point.GOF_CloudGame = {parent = {"GOF_HUD"}}
setting_short_red_point.GOF_CloudGame_New = {parent = {"GOF_CloudGame"}}
setting_short_red_point.GOF_CloudGame_Reward = {key = GameEnum.RedPointTypeEnum.ACTIVITY_131_RED_POINT, parent = {"Activity131Red"}}
setting_short_red_point.GOF_BOSS = {parent = {"GOF_HUD"}}
setting_short_red_point.GOF_BOSS_New = {parent = {"GOF_BOSS"}}
setting_short_red_point.GOF_BOSS_Collect = {parent = {"GOF_BOSS_New"}}
setting_short_red_point.GOF_Lottery = {parent = {"GOF_HUD"}}
setting_short_red_point.GOF_Lottery_New = {parent = {"GOF_Lottery"}}
setting_short_red_point.GOF_Firework = {parent = {"GOF_HUD"}}
setting_short_red_point.GOF_Firework_New = {parent = {"GOF_Firework"}}

 setting_short_red_point.LinkGame_Reward = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_202_RED_POINT}
 setting_short_red_point.LinkGame_Level1 = {parent = {"ToyHouseMapHUD"}}
 setting_short_red_point.LinkGame_Level2 = {parent = {"ToyHouseMapHUD"}}
 setting_short_red_point.LinkGame_Level3 = {parent = {"ToyHouseMapHUD"}}
 setting_short_red_point.LinkGame_Level4 = {parent = {"ToyHouseMapHUD"}}
 setting_short_red_point.LinkGame_Level5 = {parent = {"ToyHouseMapHUD"}}
--星际活动
--星际hud
setting_short_red_point.ActivityStarMatchHUD = {activityId = GameEnum.ActivityEnum.ACTIVITY_162_STAR_ACTIVITY_CENTER}
setting_short_red_point.Activity106Red = {	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId =106}
setting_short_red_point.Activity131Red = {	parent = {"CCTV"},isFirstClickRP = true,activityId =131, isDefineId=true}
--星际签到
--setting_short_red_point.Activity138Red = {key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT,
--	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_138_STAR_LOGIN}
setting_short_red_point.Activity139Red = {	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId =139}
--星际任务
setting_short_red_point.ActivityStarTask = {parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,
	activityId = GameEnum.ActivityEnum.ACTIVITY_141_STAR_TASK}
setting_short_red_point.Activity142Red = {	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId =142}
setting_short_red_point.Activity143Red = {	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId =143}
setting_short_red_point.Activity144Red = {	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId =144}
setting_short_red_point.Activity145Red =  {key = GameEnum.RedPointTypeEnum.ACTIVITY_145_RED_POINT,
	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId =145}
setting_short_red_point.Activity150Red = {	parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId =150}
--星际写真
setting_short_red_point.Activity152Red = {key = GameEnum.RedPointTypeEnum.ACTIVITY_152_RED_POINT,
    parent = {"ActivityStarMatchHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_152_STAR_PHOTO_ALBUM}
setting_short_red_point.StarBtn1 = {parent = {"ActivityStarMatchHUD"},isFirstClickRP = true}
setting_short_red_point.StarBtn2 = {parent = {"ActivityStarMatchHUD"},isFirstClickRP = true}
setting_short_red_point.StarBtn3 = {parent = {"ActivityStarMatchHUD"},isFirstClickRP = true}
setting_short_red_point.StarBtn6 = {parent = {"ActivityStarMatchHUD"},isFirstClickRP = true}
setting_short_red_point.StarBtn7 = {parent = {"ActivityStarMatchHUD"},isFirstClickRP = true}

--评选赛
setting_short_red_point.FashionRaceVote = {key = 56}
setting_short_red_point.FashionRaceMatch = {parent = {"DressMatch3"}}

--开服活动
setting_short_red_point.OCMapHUD = {}
setting_short_red_point.Activity153CreditAwardRed = {key = GameEnum.RedPointTypeEnum.ACTIVITY_153_RED_POINT,
	activityId = GameEnum.ActivityEnum.ACTIVITY_153_OPENING_CEREMONY,
	parent = {"OCMapHUD"}} --开服庆典筹备积分奖励红点
setting_short_red_point.Activity153TaskAwardRed = {activityId = GameEnum.ActivityEnum.ACTIVITY_153_OPENING_CEREMONY,
	parent = {"OCMapHUD"}} --开服庆典筹备任务奖励红点
setting_short_red_point.Activity155Red = {key = GameEnum.RedPointTypeEnum.ACTIVITY_155_RED_POINT,
    activityId = GameEnum.ActivityEnum.ACTIVITY_155_OPENING_STORE,
	parent = {"OCMapHUD"},isFirstClickRP = true,} --开服庆典商店红点
setting_short_red_point.Activity156Red = {key = GameEnum.RedPointTypeEnum.ACTIVITY_156_RED_POINT,
	activityId = GameEnum.ActivityEnum.ACTIVITY_156_OPENING_PLOT_TASK,
	parent = {"OCMapHUD"}} --开服庆典剧情任务
	-- setting_short_red_point.Activity157Red = {key = GameEnum.RedPointTypeEnum.ACTIVITY_157_RED_POINT,
	-- activityId = GameEnum.ActivityEnum.ACTIVITY_157_LOOK_FOR_STAR,
	-- parent = {"OCMapHUD"}} --开服庆典寻找明星


--开服转盘红点
setting_short_red_point.OPENING_TURNTABLE = {key = GameEnum.RedPointTypeEnum.ACTIVITY_161_RED_POINT}

--梦工厂任务首次点击红点
-- setting_short_red_point.TURNTABLE_DreamTask = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"},activityId = GameEnum.ActivityEnum.ACTIVITY_161_OPENING_TURNTABLE}
-- --梦工厂疲劳度奖励首次点击红点
-- setting_short_red_point.TURNTABLE_DreamTiredGift = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"},activityId = GameEnum.ActivityEnum.ACTIVITY_161_OPENING_TURNTABLE}

--满月活动(奇食)
setting_short_red_point.FullMoonHud = {activityId = GameEnum.ActivityEnum.ACTIVITY_162_STAR_ACTIVITY_CENTER}
-- setting_short_red_point.FullMoonOpenView = {isFirstClickRP = true,parent = {"FullMoonHud"},activityId =  GameEnum.ActivityEnum.ACTIVITY_167_FULL_MOON_CENTER}
-- setting_short_red_point.FullMoonFengEr = {isFirstClickRP = true,parent = {"FullMoonHud"},activityId = GameEnum.ActivityEnum.ACTIVITY_168_FULL_MOON_SEED_SHOP}
-- setting_short_red_point.FullMoonWorkShop = {isFirstClickRP = true,parent = {"FullMoonHud"},activityId = GameEnum.ActivityEnum.ACTIVITY_166_FULL_MOON_WORKSHOP}
-- setting_short_red_point.FullMoonOrder = {isEveryDayRP = true,parent = {"FullMoonHud"},activityId = GameEnum.ActivityEnum.ACTIVITY_164_FULL_MOON_TASK}
-- setting_short_red_point.FullMoonGrocery = {isFirstClickRP = true,parent = {"FullMoonHud"},activityId = 201}
setting_short_red_point.Activity194FirstClick = {parent = {"FullMoonHud"}, activityId = GameEnum.ActivityEnum.ACTIVITY_194_BLIND_BOX}

--setting_short_red_point.StrangeFoodSignin = {parent = {"FullMoonHud"}, activityId = GameEnum.ActivityEnum.ACTIVITY_196_SIGN, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

--狼人杀地图hud
setting_short_red_point.WerewolfMapHUD = {isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_169_WEREWOLF_THEME}

--狼人杀签到红点ACTIVITY_170_RED_POINT
setting_short_red_point.ACTIVITY_170_WEREWOLF_SIGN = {parent = {"WerewolfMapHUD"},key = GameEnum.RedPointTypeEnum.ACTIVITY_170_RED_POINT,isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_170_WEREWOLF_SIGN}
--源晶溯源实验任务红点
setting_short_red_point.Wolf_Experiment_Task_RedPoint = {parent = {"WerewolfMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_176_WEREWOLF_CRYSTAL_EXPERIMENT}
--狼人杀彩蛋首次点击红点
setting_short_red_point.Wolf_Egg_First_Click_RedPoint = {parent = {"WerewolfMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_171_WEREWOLF_FOGGY_LAB}
--雾尽商店 首次点击红点
setting_short_red_point.Wolf_Grocery_First_Click_RedPoint = {parent = {"WerewolfMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_177_WEREWOLF_FOGGY_SHOP}
--暗夜袭击 首次点击红点
-- setting_short_red_point.Wolf_NightAt_First_Click_RedPoint = {parent = {"WerewolfMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_175_WEREWOLF_NIGHT_ATTACK}


--找茬 首次点击红点
-- setting_short_red_point.WolfFindDiffClick = {parent = {"WerewolfMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_178_WEREWOLF_FIND_DIFF}
-- setting_short_red_point.WolfFindDiffClick1 = {parent = {"ActivityMapHud"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_178_WEREWOLF_FIND_DIFF}
setting_short_red_point.WolfFindDiffClick = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_178_WEREWOLF_FIND_DIFF}
--找茬 奖励红点
-- setting_short_red_point.WolfFindDiffReward = {key = GameEnum.RedPointTypeEnum.ACTIVITY_178_RED_POINT, parent = {"ActivityMapHud"}}
setting_short_red_point.WolfFindDiffReward = {key = GameEnum.RedPointTypeEnum.ACTIVITY_178_RED_POINT, parent = {"ToyHouseMapHUD"}}

--找茬 关卡开启红点
setting_short_red_point.WolfFindDiffLevel1 = {parent = {"WolfFindDiffReward"}}
setting_short_red_point.WolfFindDiffLevel2 = {parent = {"WolfFindDiffReward"}}
setting_short_red_point.WolfFindDiffLevel3 = {parent = {"WolfFindDiffReward"}}
setting_short_red_point.WolfFindDiffLevel4 = {parent = {"WolfFindDiffReward"}}
setting_short_red_point.WolfFindDiffLevel5 = {parent = {"WolfFindDiffReward"}}
setting_short_red_point.WolfFindDiffLevel6 = {parent = {"WolfFindDiffReward"}}

--暗月之章 首次点击红点
setting_short_red_point.WolfStoryClick = {parent = {"WerewolfMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_174_WEREWOLF_CHAPTER_MOON}
--暗月之章 剧情开启红点
setting_short_red_point.WolfStoryStory1 = {parent = {"WerewolfMapHUD"}}
setting_short_red_point.WolfStoryStory2 = {parent = {"WerewolfMapHUD"}}
setting_short_red_point.WolfStoryStory3 = {parent = {"WerewolfMapHUD"}}
setting_short_red_point.WolfStoryStory4 = {parent = {"WerewolfMapHUD"}}

-- 通用暗月之章任务剧情模板红点
-- setting_short_red_point.Act1172TaskStory = {isFirstClickRP = true,parent = {CommonActTaskStoryUnit.getRedKeyParent()}}
-- setting_short_red_point.Act1224TaskStory = {isFirstClickRP = true,parent = {CommonActTaskStoryUnit.getRedKeyParent()}}
setting_short_red_point.Act1236TaskStory = {isFirstClickRP = true,parent = {CommonActTaskStoryUnit.getRedKeyParent()}}

--回归活动红点
setting_short_red_point.AobiReturn = {}
setting_short_red_point.AobiReturnUnlock = {key = GameEnum.RedPointTypeEnum.BACKFLOW_OPEN_RED_POINT}
setting_short_red_point.IslandChange = {parent={"AobiReturn"}}
setting_short_red_point.IslandChangePVClick = {parent={"IslandChange"}}
setting_short_red_point.IslandChangeGuideRedpoint = {parent={"IslandChange"}}
setting_short_red_point.AobiReturnTask = {parent={"AobiReturn"}}
setting_short_red_point.AobiReturnTaskProcess = {parent={"AobiReturnTask"},key=GameEnum.RedPointTypeEnum.BACKFLOW_TASK_RED_POINT}
setting_short_red_point.AobiReturnTaskTab = {parent={"AobiReturnTask"}}
setting_short_red_point.AobiReturnShopDailyGoods = {parent={"AobiReturn"}, isEveryDayRP = true}
setting_short_red_point.AobiReturn3Task15 = {parent={"AobiReturn"}}
setting_short_red_point.AobiReturn3Task15FirstClick = {parent={"AobiReturn3Task15"},isFirstClickRP = true}
setting_short_red_point.AobiReturn3Task15DailyReward= {parent={"AobiReturn3Task15"}, isEveryDayRP = true}
setting_short_red_point.AobiReturn3Task16 = {}
setting_short_red_point.AobiDailyReward = {parent={"AobiReturn3Task16"}, isEveryDayRP = true}
setting_short_red_point.AobiDailyReward1 = {key = GameEnum.RedPointTypeEnum.BACKFLOW3_REWARD}
--回归瓶子
setting_short_red_point.AobiReturnMagicCrystalBottle = {parent={"AobiReturn"},isFirstClickRP = true}
setting_short_red_point.AobiBottle = {parent={"AobiReturnMagicCrystalBottle"},key = GameEnum.RedPointTypeEnum.BACKFLOW3_GIFT_RED_POINT}
--回归转盘
setting_short_red_point.AobiTurnTable = {parent={"AobiReturn"},isEveryDayRP = true}

--翻牌 首次点击&奖励红点
setting_short_red_point.WEREWOLF_FLOP_GAME_RedPoint = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_173_WEREWOLF_FLOP_GAME}
setting_short_red_point.FlopGameReward = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_173_RED_POINT}
--翻牌 新开启子红点
setting_short_red_point.FLOP_GAME_Tab = {parent = {"ToyHouseMapHUD"}}

--星愿卡礼包每日提醒
setting_short_red_point.StarWishCardTip = {isFirstClickRP = true,parent = {"DreamBox"},activityId = GameEnum.ActivityEnum.ACTIVITY_160_STAR_WISH_CARD}
setting_short_red_point.StarWishGiftTip = {isEveryDayRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_160_STAR_WISH_CARD}
setting_short_red_point.ACTIVITY_160_ACCUMULATE_RED_POINT = {parent = {"DreamBox"},key = GameEnum.RedPointTypeEnum.ACTIVITY_160_ACCUMULATE_RED_POINT,activityId = GameEnum.ActivityEnum.ACTIVITY_160_STAR_WISH_CARD,isDefineId = true}


--14周年红点
setting_short_red_point.Anniversary15HUD = {isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_188_AOBI_ANNIVERSARY}
--2.2.4ip周年庆签到
setting_short_red_point.AnniversarySign = {parent = {"Anniversary15HUD"},isFirstClickRP = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_188_RED_POINT,activityId = GameEnum.ActivityEnum.ACTIVITY_188_AOBI_ANNIVERSARY}
setting_short_red_point.AnniversaryStory = {parent = {"Anniversary15HUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_206_GENERIC_ACT_MAP}
setting_short_red_point.AnniversaryCake = {parent = {"Anniversary15HUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_174_THEME_CHAPTER_TASK}

--快乐面包地图红点
setting_short_red_point.HAPPY_BREAD_GAME_REDPOINT = {parent = {"DreamBox"},isFirstClickRP = true,activityId = 1304, key = GameEnum.RedPointTypeEnum.ACTIVITY_189_RED_POINT, isDefineId=false}

--快乐面包 新关卡开启红点
setting_short_red_point.HAPPY_BREAD_GAME_1 = {parent = {"HAPPY_BREAD_GAME_REDPOINT"}, activityId = GameEnum.ActivityEnum.ACTIVITY_189_AOBI_HAPPY_BREAD}
setting_short_red_point.HAPPY_BREAD_GAME_2 = {parent = {"HAPPY_BREAD_GAME_REDPOINT"}, activityId = GameEnum.ActivityEnum.ACTIVITY_189_AOBI_HAPPY_BREAD}
setting_short_red_point.HAPPY_BREAD_GAME_3 = {parent = {"HAPPY_BREAD_GAME_REDPOINT"}, activityId = GameEnum.ActivityEnum.ACTIVITY_189_AOBI_HAPPY_BREAD}
setting_short_red_point.HAPPY_BREAD_GAME_4 = {parent = {"HAPPY_BREAD_GAME_REDPOINT"}, activityId = GameEnum.ActivityEnum.ACTIVITY_189_AOBI_HAPPY_BREAD}
setting_short_red_point.HAPPY_BREAD_GAME_5 = {parent = {"HAPPY_BREAD_GAME_REDPOINT"}, activityId = GameEnum.ActivityEnum.ACTIVITY_189_AOBI_HAPPY_BREAD}
setting_short_red_point.HAPPY_BREAD_GAME_6 = {parent = {"HAPPY_BREAD_GAME_REDPOINT"}, activityId = GameEnum.ActivityEnum.ACTIVITY_189_AOBI_HAPPY_BREAD}

setting_short_red_point.DRESSRACE_REDPOINT = {isEveryDayRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_163_DRESS_RACE, parent={"DressMatch2"}}

setting_short_red_point.InviteNewbie = {isFirstClickRP = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_191_RED_POINT}
setting_short_red_point.InviteNewbie_NewcomerReward = {parent = {"InviteNewbie"}}
setting_short_red_point.InviteNewbie_VeteranReward = {parent = {"InviteNewbie"}}


setting_short_red_point.NationalDay = {isFirstClickRP=true}
setting_short_red_point.NationalDay1 = {activityId = GameEnum.ActivityEnum.ACTIVITY_199_SIGN, parent = {"NationalDay"}}
setting_short_red_point.NationalDay2 = {parent = {"NationalDay"}}
setting_short_red_point.NationalDayTaskProcess = {activityId = GameEnum.ActivityEnum.ACTIVITY_200_TASK, key = GameEnum.RedPointTypeEnum.ACTIVITY_200_RED_POINT,parent = {"NationalDay2"}}
setting_short_red_point.NationalDayTaskAward = {activityId = GameEnum.ActivityEnum.ACTIVITY_200_TASK, parent = {"NationalDay2"}}
setting_short_red_point.DreamPalace = {key = GameEnum.RedPointTypeEnum.DREAMPALACE_RED_POINT}
--蜂运会地图hud红点
setting_short_red_point.BeeSportMapHUD = {isFirstClickRP = true,activityId = 1141,isDefineId = false}
--蜂运会彩蛋红点
setting_short_red_point.BeeSportEggFirstClick = {parent = {"BeeSportMapHUD"},isFirstClickRP = true, activityId = 1149,isDefineId = false}
--蜂蜂商店首次点击红点
setting_short_red_point.BeeSportShopFirstClick = {parent = {"BeeSportMapHUD"}, isFirstClickRP = true, activityId = 1143,isDefineId = false}
--蜂王涂鸦首次点击红点
setting_short_red_point.BeeDrawFirstClick = { parent = {"BeeSportMapHUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_205_QUEEN_BEE_GRAFFITI, }
--蜂王涂鸦首次点击红点
setting_short_red_point.BeeDrawFirstClick1 = { parent = {"BeeDrawFirstClick"},  activityId = GameEnum.ActivityEnum.ACTIVITY_205_QUEEN_BEE_GRAFFITI, }
setting_short_red_point.BeeDrawFirstClick2 = { parent = {"BeeDrawFirstClick"},  activityId = GameEnum.ActivityEnum.ACTIVITY_205_QUEEN_BEE_GRAFFITI, }
setting_short_red_point.BeeDrawFirstClick3 = { parent = {"BeeDrawFirstClick"},  activityId = GameEnum.ActivityEnum.ACTIVITY_205_QUEEN_BEE_GRAFFITI, }
setting_short_red_point.BeeDrawFirstClick4 = { parent = {"BeeDrawFirstClick"},  activityId = GameEnum.ActivityEnum.ACTIVITY_205_QUEEN_BEE_GRAFFITI, }
setting_short_red_point.BeeDrawFirstClick5 = { parent = {"BeeDrawFirstClick"},  activityId = GameEnum.ActivityEnum.ACTIVITY_205_QUEEN_BEE_GRAFFITI, }

--嗡嗡签到首次点击红点
--setting_short_red_point.BeeSigninViewFirstClick = {parent = {"BeeSportMapHUD"}, isFirstClickRP = true, activityId = 1146,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}
--分手搬家
setting_short_red_point.MoveHouseFirstClick = {key = GameEnum.RedPointTypeEnum.ACTIVITY_210_RED_POINT, parent = {"BeeSportMapHUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_210_MOVE_HOUSE}

--跳一跳首次点击红点
setting_short_red_point.JumpBoxFirstClick = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1330, key = GameEnum.RedPointTypeEnum.ACTIVITY_204_RED_POINT, isDefineId = false}

--跳一跳页签红点
setting_short_red_point.JumpBox_Tab_1 = {parent = {"JumpBoxFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_204_JUMP_GAME}
setting_short_red_point.JumpBox_Tab_2 = {parent = {"JumpBoxFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_204_JUMP_GAME}
setting_short_red_point.JumpBox_Tab_3 = {parent = {"JumpBoxFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_204_JUMP_GAME}
setting_short_red_point.JumpBox_Tab_4 = {parent = {"JumpBoxFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_204_JUMP_GAME}


--火柴妙旅任务红点
setting_short_red_point.MiaoMiaoWu_Task_RedPoint = {parent = {"IceSnowMapHUD"}, isFirstClickRP = true, activityId = 1169, isDefineId = false}
--冰雪节地图hud红点
setting_short_red_point.IceSnowMapHUD = {isFirstClickRP = true,activityId = 1158,isDefineId = false}
--冰雪节商店首次点击红点
setting_short_red_point.IceSnowtShopFirstClick = {parent = {"IceSnowMapHUD"}, isFirstClickRP = true, activityId = 1159,isDefineId = false}
--冰雪摘星首次点击红点
setting_short_red_point.IceSnowtCatchStarFirstClick = {parent = {"IceSnowMapHUD"}, isFirstClickRP = true, activityId = 1152,isDefineId = false}
--冰雪摘星奖励红点
setting_short_red_point.IceSnowCatchstarAwardRedPoint = {parent = {"IceSnowMapHUD"},
	activityId = GameEnum.ActivityEnum.ACTIVITY_209_CATCH_STAR,
	key = GameEnum.RedPointTypeEnum.ACTIVITY_209_RED_POINT}
--冰雪节签到首次点击红点
--setting_short_red_point.IceSnowSigninViewFirstClick = {parent = {"IceSnowMapHUD"}, isFirstClickRP = true, activityId = 1179,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

--堆堆雪人首次点击红点
setting_short_red_point.IceSnowSnowmanViewFirstClick = { parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN,activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN }
setting_short_red_point.IceSnowSnowmanTab_1 = { parent = {"IceSnowSnowmanViewFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanTab_2 = { parent = {"IceSnowSnowmanViewFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanTab_3 = { parent = {"IceSnowSnowmanViewFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_1 = { parent = {"IceSnowSnowmanTab_1"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_2 = { parent = {"IceSnowSnowmanTab_1"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_3 = { parent = {"IceSnowSnowmanTab_1"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_4 = { parent = {"IceSnowSnowmanTab_1"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_5 = { parent = {"IceSnowSnowmanTab_2"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_6 = { parent = {"IceSnowSnowmanTab_2"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_7 = { parent = {"IceSnowSnowmanTab_2"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_8 = { parent = {"IceSnowSnowmanTab_2"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_9 = { parent = {"IceSnowSnowmanTab_3"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_10 = { parent = {"IceSnowSnowmanTab_3"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_11 = { parent = {"IceSnowSnowmanTab_3"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }
setting_short_red_point.IceSnowSnowmanMission_12 = { parent = {"IceSnowSnowmanTab_3"}, activityId = GameEnum.ActivityEnum.ACTIVITY_207_MAKE_SNOWMAN, }

--冰雪节彩蛋首次点击红点
setting_short_red_point.IceSnowEggFirstClick = {parent = {"IceSnowMapHUD"},isFirstClickRP = true, activityId = 1167,isDefineId = false}

--祈福节hud红点
setting_short_red_point.BlessingFestivalHud = {activityId = 1173,isDefineId = false}

--祈福节工坊
setting_short_red_point.BlessingFestival1180 = {isFirstClickRP = true, parent = {"BlessingFestivalHud"}}

--祈福节任务
setting_short_red_point.BlessingFestival1181 = {isFirstClickRP = true, parent = {"BlessingFestivalHud"}}

--祈福签到
-- setting_short_red_point.Activity138Red = {key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT,parent = {"BlessingFestivalHud"},isFirstClickRP = true,activityId = 1183, isDefineId = false}

--祈福节商店
setting_short_red_point.BlessingFestivalShop1174 = {isFirstClickRP = true, parent = {"BlessingFestivalHud"}}


setting_short_red_point.ACTIVITY_215_PIPELINE_Reward = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_215_RED_POINT,
	activityId = GameEnum.ActivityEnum.ACTIVITY_215_PIPELINE} --春节流水线全服进度奖励红点

-- --风华任务首次点击红点
-- setting_short_red_point.TURNTABLE_FengHuaTask = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"}, activityId = TurnTableConfig.DragonTurntableId, isDefineId = false}
-- --风华疲劳度奖励首次点击红点
-- setting_short_red_point.TURNTABLE_FengHuaGift = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"}, activityId = TurnTableConfig.DragonTurntableId, isDefineId = false}

setting_short_red_point.HongBaoRainFirstClick = {activityId = GameEnum.ActivityEnum.ACTIVITY_213_RED_PAPER}

setting_short_red_point.CCTV = {isFirstClickRP=true,activityId = 1983,isDefineId = false}
setting_short_red_point.CCTV_SignIn = {key = GameEnum.RedPointTypeEnum.ACTIVITY_222_RED_POINT,parent = {"CCTV"},activityId = 1983,isDefineId = false}
setting_short_red_point.CCTV_Task = {isFirstClickRP = true,parent = {"CCTV"},activityId = 1193,isDefineId = false}
-- setting_short_red_point.CCTV_Shop = {isFirstClickRP=true,parent = {"CCTV"},activityId = 1195,isDefineId = false}
setting_short_red_point.Shop1412 = {isFirstClickRP = true,parent = {"CCTV"}, activityId = 1412, isDefineId = false}
-- setting_short_red_point.CCTV_Fatigue = {isFirstClickRP=true,parent = {"CCTV"},key = GameEnum.RedPointTypeEnum.ACTIVITY_221_RED_POINT,activityId = 1196,isDefineId = false}
-- setting_short_red_point.CCTV_CardCollecting = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_PROCESS_RED_POINT,parent = {"CCTV"},activityId = 1411,isDefineId = false, isFirstClickRP = true}
-- setting_short_red_point.CCTV_220 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_RED_POINT,parent = {"CCTV"},activityId = 1411,isDefineId = false}

--限时宠物
setting_short_red_point.Time_Limit_Pet_Reward = {key = GameEnum.RedPointTypeEnum.ACTIVITY_100_LIMIT_PET_RED_POINT,activityId = 2234,isDefineId = false}

--开心餐厅页签红点
-- setting_short_red_point.HappyRestaurant_Tab_1 = {activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}
-- setting_short_red_point.HappyRestaurant_Tab_2 = {activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}
-- setting_short_red_point.HappyRestaurant_Tab_3 = {activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}
-- setting_short_red_point.HappyRestaurant_Tab_4 = {activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}

--风华节地图hud红点
setting_short_red_point.SpringFestivalMapHUD = {isFirstClickRP = true,activityId = SpringFestivalGotoUtil.mapActivityId,isDefineId = false}
--风华节商店首次点击红点
setting_short_red_point.SpringFestivalShopFirstClick = {parent = {"SpringFestivalMapHUD"}, isFirstClickRP = true, activityId = SpringFestivalGotoUtil.shopActivityId,isDefineId = false}
--团圆圈圈任务红点
setting_short_red_point.ReunionCircle_Task_RedPoint = {parent = {"SpringFestivalMapHUD"}, isFirstClickRP = true, activityId = SpringFestivalGotoUtil.reunionCircle, isDefineId = false}
---贪吃蛇首次点击红点
-- setting_short_red_point.Snake2Game = {isFirstClickRP = true, activityId = SpringFestivalGotoUtil.snake, isDefineId = false}

--风华节任务跳转首次点击红点
setting_short_red_point.SpringFestivalTaskJumpFirstClick = {parent = {"SpringFestivalMapHUD"}, isFirstClickRP = true}
--风华节AR首次点击红点
setting_short_red_point.SpringFestivalARFirstClick = {parent = {"SpringFestivalMapHUD"}, isFirstClickRP = true, activityId = SpringFestivalGotoUtil.mapActivityId,isDefineId = false}

--风华节彩蛋首次点击红点
setting_short_red_point.SpringFestivalLotteryFirstClick = {parent = {"SpringFestivalMapHUD"}, isFirstClickRP = true, activityId = SpringFestivalGotoUtil.lottery,isDefineId = false}

--风华节打工每日点击红点
setting_short_red_point.SpringFestivalParttimeClick = {parent = {"SpringFestivalMapHUD"}, isEveryDayRP = true, activityId = SpringFestivalGotoUtil.parttime,isDefineId = false}

--燃灯节
setting_short_red_point.LanternFestival = {isFirstClickRP = true,activityId = 1199, isDefineId = false}
--setting_short_red_point.LanternFestivalSignin = {key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT,parent = {"LanternFestival"},isFirstClickRP = true,activityId = 1201, isDefineId = false}
setting_short_red_point.LanternFestivalTask = {isEveryDayRP = true,parent = {"LanternFestival"},activityId=1200, isDefineId = false}
-- setting_short_red_point.LanternFestivalShop = {parent = {"DreamBox"}}
setting_short_red_point.LanternFestivalShopTab1 = {isFirstClickRP=true}
setting_short_red_point.LanternFestivalShopTab2 = {isFirstClickRP=true}
--元宵灯谜页签红点
setting_short_red_point.LanternRiddleTab = {isEveryDayRP = true,key = GameEnum.ActivityEnum.ACTIVITY_217_LANTERN_RIDDLE,parent = {"LanternFestival"}}
--1.7主题签到，团圆签到
--setting_short_red_point.SpringFestivalSigninViewFirstClick = {parent = {"SpringFestivalMapHUD"}, isFirstClickRP = true, activityId = SpringFestivalGotoUtil.signin,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

--1.7开心餐厅
--setting_short_red_point.HappyRestaurant = {parent = {"SpringFestivalMapHUD"}, isFirstClickRP = true, activityId = SpringFestivalGotoUtil.happyRestaurant,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_214_RED_POINT}

--1.8 情人节
setting_short_red_point.ActivityMapHud = {activityId = 1233, isDefineId = false}
setting_short_red_point.ActivityStopHud = {parent = {"ActivityMapHud"},activityId = 1231, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.LoverLotteryFirstClick = {parent = {"ActivityMapHud"}, isFirstClickRP = true, activityId = 1235,isDefineId = false}

--1.8 情人节签到
-- setting_short_red_point.LoverSignin = {parent = {"ActivityMapHud"}, isFirstClickRP = true, activityId = 1234,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

--丘比之箭红点
setting_short_red_point.ArcheryGame = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_144_STAR_SHOOT, key = GameEnum.RedPointTypeEnum.ACTIVITY_144_RED_POINT}
--丘比之箭关卡新开启子红点
setting_short_red_point.ArcheryGameTab = {parent = {"ArcheryGame"}}

--1.9 鱼之歌
setting_short_red_point.FishSongHud = {activityId = FishSongGotoUtil.mapActivityId, isDefineId = false}
setting_short_red_point.FishSongShopFirstClick = {parent = {"FishSongHud"},activityId = FishSongGotoUtil.shopActivityId, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.FlopGameFirstClick = {parent = {"FishSongHud"},activityId = FishSongGotoUtil.flopGameId, isDefineId = false, isFirstClickRP = true}

setting_short_red_point.FlowerCloudFirstClick = {parent = {"ToyHouseMapHUD"},activityId = ToyHouseGotoUtil.flowerCloudActId, isDefineId = false, isFirstClickRP = true}
-- setting_short_red_point.FishSignin = {parent = {"FishSongHud"}, isFirstClickRP = true, activityId = 1256,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}
-- setting_short_red_point.FishTaskStory = {parent = {"FishSongHud"},activityId = FishSongGotoUtil.taskStoryId, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.FishSongEggFirstClick = {parent = {"FishSongHud"}, isFirstClickRP = true, activityId = FishSongGotoUtil.eggId,isDefineId = false}
--通用任务
setting_short_red_point.ACTIVITY_227_RED_POINT = {parent = {"FishSongHud"},activityId = FishSongGotoUtil.incubateEgg, isDefineId = false,key = GameEnum.RedPointTypeEnum.ACTIVITY_227_RED_POINT}

--1.9宠物公益
setting_short_red_point.PetWelfare = {key = GameEnum.RedPointTypeEnum.ACTIVITY_229_RED_POINT,activityId = 1263,isDefineId = false}
setting_short_red_point.PetWelfare_Task = {isFirstClickRP = true,parent = {"PetWelfare"},activityId = 1263,isDefineId = false}
setting_short_red_point.PetWelfare_Recharge = {isFirstClickRP = true,parent = {"PetWelfare"},activityId = 1263,isDefineId = false}
setting_short_red_point.PetWelfare_Process = {isFirstClickRP = true,parent = {"PetWelfare"},activityId = 1267,isDefineId = false}

--1.11.2小主题活动
setting_short_red_point.DreamBox = {activityId = DreamBoxFacade.mapActivityId, isDefineId = false}
-- setting_short_red_point.DreamBoxSignin = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1475,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT}
-- setting_short_red_point.DreamBoxFatigue = {isFirstClickRP = true,parent = {"DreamBox"},key = DreamBoxFacade.fatigueActivityId, isDefineId = false}
-- setting_short_red_point.DreamBoxFatigueReward = {parent = {"DreamBox"},key = GameEnum.RedPointTypeEnum.ACTIVITY_237_RED_POINT}
-- setting_short_red_point.DreamBoxGame1 = {isFirstClickRP = true,parent = {"DreamBox"}}
setting_short_red_point.Story1376 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1376, isDefineId = false}
setting_short_red_point.Shop1377 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1377, isDefineId = false}
setting_short_red_point.Shop1377Tab1 = {isFirstClickRP=true}
setting_short_red_point.Shop1377Tab2 = {isFirstClickRP=true}
-- setting_short_red_point.CollectStamp1379 = {isFirstClickRP=true, parent = {"DreamBox"}, activityId = 1379, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_192_RED_POINT}
-- setting_short_red_point.MakeDreamShopFirstClick = {isFirstClickRP = true,parent = {"DreamBox"}}
--setting_short_red_point.MakeDreamTableFirstClick = {isFirstClickRP = true,parent = {"DreamBox"}}
setting_short_red_point.MakeDreamTable = {parent = {"DreamBox"},key = GameEnum.RedPointTypeEnum.ACTIVITY_234_RED_POINT}
setting_short_red_point.Activity157Red = {
	isFirstClickRP = true,
	key = GameEnum.RedPointTypeEnum.ACTIVITY_157_RED_POINT,
	activityId = GameEnum.ActivityEnum.ACTIVITY_157_LOOK_FOR_STAR,
	parent = {"DreamBox"}} --游园赠礼寻找明星

--1.10.2造物
-- setting_short_red_point.MakeItemShopFirstClick = {isFirstClickRP = true,parent = {"DreamBox"}}
-- setting_short_red_point.WorkShopFirstClick = {isFirstClickRP = true,parent = {"DreamBox"}}

--1.10音咖地图hud红点
setting_short_red_point.MusicCoffeeMapHUD = {isFirstClickRP = true, activityId = MusicCoffeeGotoUtil.mapActivityId, isDefineId = false}

--2.60 音乐豆豆
setting_short_red_point.MusicBeansActivity = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_236_RED_POINT,isFirstClickRP = true,activityId = 2062, isDefineId = false}
setting_short_red_point.MusicBeans_1 = {parent = {"MusicBeansActivity"}, activityId = 2062, isDefineId = false}
setting_short_red_point.MusicBeans_2 = {parent = {"MusicBeansActivity"}, activityId = 2062, isDefineId = false}
setting_short_red_point.MusicBeans_3 = {parent = {"MusicBeansActivity"}, activityId = 2062, isDefineId = false}
setting_short_red_point.MusicBeans_4 = {parent = {"MusicBeansActivity"}, activityId = 2062, isDefineId = false}

--1.10 音乐派对
setting_short_red_point.MusicParty = {parent = {"ToyHouseMapHUD"},activityId = 238, isFirstClickRP = true}
setting_short_red_point.MusicParty_Open = {parent = {"MusicParty"}, activityId = 238}
setting_short_red_point.MusicParty_Award = {parent = {"MusicParty"}, activityId = 238,key = GameEnum.RedPointTypeEnum.ACTIVITY_238_RED_POINT}
setting_short_red_point.MusicParty_Barrage = { activityId = 238, isFirstClickRP = true}

--1.10 音之咖
--setting_short_red_point.MusicSignin = {parent = {"MusicCoffeeMapHUD"}, isFirstClickRP = true, activityId = 1291,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}
--setting_short_red_point.MusicTaskStory = {parent = {"MusicCoffeeMapHUD"},activityId = 1293, isDefineId = false, isFirstClickRP = true}

--1.10商店
setting_short_red_point.MusicCoffeeShopFirstClick = {parent = {"MusicCoffeeMapHUD"}, isFirstClickRP = true}
--通用大地图活动商店tab开启
setting_short_red_point.CommonShopTab = {parent = {"ToyHouseMapHUD"}}
--通用boss
setting_short_red_point.SFBoss = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 219,isDefineId = true}
setting_short_red_point.SFBossReward = {parent = {"SFBoss"}, activityId = 219,isDefineId = true,key = GameEnum.RedPointTypeEnum.WORLD_BOSS_RED_POINT}
setting_short_red_point.SFBossCollect = {parent = {"SFBoss"}, isEveryDayRP = true, activityId = 219,isDefineId = true}
--1.10彩蛋
setting_short_red_point.MusicEggFirstClick = {parent = {"MusicCoffeeMapHUD"}, isFirstClickRP = true}
--拼图
setting_short_red_point.PuzzleFirstClick = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 232,isDefineId = true}
setting_short_red_point.PuzzleUnlockLevel = {parent = {"ToyHouseMapHUD"}}
setting_short_red_point.PuzzleReward = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_232_RED_POINT, activityId = 232,isDefineId = true}

--1.10推广任务
setting_short_red_point.MusicCoffeeADFirstClick = {parent = {"MusicCoffeeMapHUD"}, isFirstClickRP = true}

setting_short_red_point.returnView2 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_235_RED_POINT, activityId = 1298,isDefineId = false}

setting_short_red_point.MiniProgram = {isFirstClickRP = true}
setting_short_red_point.MiniProgram2 = {activityId = 248, isFirstClickRP = true}

--星歌会任务首次点击红点
-- setting_short_red_point.TURNTABLE_MusicTask = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"}}
---星歌会疲劳度奖励首次点击红点
-- setting_short_red_point.TURNTABLE_MusicGift = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"}}

--转盘任务点击红点
-- setting_short_red_point.TURNTABLE_TraditionTask = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"}, activityId = 161, isDefineId = true}
setting_short_red_point.TURNTABLE_TraditionTask = {}
setting_short_red_point.TURNTABLE_FirstClick = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"}, activityId = 161, isDefineId = true}
--转盘疲劳度奖励首次点击红点
-- setting_short_red_point.TURNTABLE_TraditionGift = {isFirstClickRP = true,parent = {"OPENING_TURNTABLE"}, activityId = 161, isDefineId = true}
setting_short_red_point.TURNTABLE_TraditionGift = {}
--转盘红包
setting_short_red_point.TURNTABLE_RedBagGift = {key = GameEnum.RedPointTypeEnum.ACTIVITY_161_RED_ENVELOPE_POINT}


setting_short_red_point.Act241RedPoint = {key = GameEnum.RedPointTypeEnum.ACTIVITY_241_RED_POINT}
setting_short_red_point.Act256RedPoint = {key = GameEnum.RedPointTypeEnum.ACTIVITY_256_RED_POINT}
setting_short_red_point.Act257RedPoint = {key = GameEnum.RedPointTypeEnum.ACTIVITY_257_RED_POINT}
setting_short_red_point.Act284RedPoint = {key = GameEnum.RedPointTypeEnum.ACTIVITY_284_RED_POINT}

--七日充值活动红点
setting_short_red_point.Act243RedPoint = {key = GameEnum.RedPointTypeEnum.ACTIVITY_243_RED_POINT}
setting_short_red_point.First243RedPoint = {}
--活动订单首次点击红点
--setting_short_red_point.Act195RedPoint = {parent = {"DreamBox"}, isEveryDayRP = true}

--1.12地图hud红点
setting_short_red_point.ToyHouseMapHUD = {isFirstClickRP = true}
setting_short_red_point.Wolf_Werewolf_RedPoint = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_172_WEREWOLF_BREAK_FOGGY}
setting_short_red_point.Daledou_RedPoint = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_106_FIGHT}
setting_short_red_point.Daodangui_Daledou_Reward = {parent = {"Daledou_RedPoint"},key = GameEnum.RedPointTypeEnum.CHALLENGE_SCORE_RED_POINT,activityId =GameEnum.ActivityEnum.ACTIVITY_106_FIGHT}
-- setting_short_red_point.LumberJack_lvl1 = {parent = {"LumberJack_RedPoint"}, activityId = 1330, isDefineId = false}
-- setting_short_red_point.LumberJack_lvl2 = {parent = {"LumberJack_RedPoint"}, activityId = 1330, isDefineId = false}
-- setting_short_red_point.LumberJack_lvl3 = {parent = {"LumberJack_RedPoint"}, activityId = 1330, isDefineId = false}
-- setting_short_red_point.LumberJack_lvl4 = {parent = {"LumberJack_RedPoint"}, activityId = 1330, isDefineId = false}
--setting_short_red_point.ToyHouseSignin = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1363,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}
setting_short_red_point.ToyHouseTaskStory = {parent = {"ToyHouseMapHUD"},activityId = ToyHouseGotoUtil.storyActId, isDefineId = false, isFirstClickRP = true}

setting_short_red_point.ToyHouseEggFirstClick = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = 1364,isDefineId = false}

setting_short_red_point.ToyHouseParttime = {parent = {"ToyHouseMapHUD"}, isEveryDayRP = true, activityId = ToyHouseGotoUtil.parttime, isDefineId = false}

--游园
setting_short_red_point.ACTIVITY_244_FirstClick = {parent = {"DreamBox"},isFirstClickRP = true,activityId = 1320,isDefineId = false}
-- setting_short_red_point.ACTIVITY_244_RED_POINT = {parent = {"DreamBox"},key = GameEnum.RedPointTypeEnum.ACTIVITY_244_RED_POINT,activityId = 1320,isDefineId = false}
--预下载整包奖励
setting_short_red_point.ACTIVITY_247_RED_POINT = {key =GameEnum.RedPointTypeEnum.ACTIVITY_247_RED_POINT, activityId = 247,isDefineId = true}

--捉虫游戏活动页面红点
setting_short_red_point.ACTIVITY_246_RED_POINT = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = 1344,isDefineId = false}
--捉虫游戏后端推送红点
setting_short_red_point.ACTIVITY_246_RED_POINT_Info = {parent = {"ACTIVITY_246_RED_POINT"},key =GameEnum.RedPointTypeEnum.ACTIVITY_246_RED_POINT,activityId = 1344,isDefineId = false}
--捉虫游戏活动种类奖励红点
setting_short_red_point.ACTIVITY_246_RED_POINT_1 = {parent = {"ACTIVITY_246_RED_POINT"},activityId = 1344,isDefineId = false}
--捉虫游戏进度奖励红点
setting_short_red_point.ACTIVITY_246_RED_POINT_2 = {parent = {"ACTIVITY_246_RED_POINT"},activityId = 1344,isDefineId = false}
--捉虫游戏昆虫种类红点
setting_short_red_point.ACTIVITY_246_RED_POINT_Type_1 = {parent = {"ACTIVITY_246_RED_POINT"},activityId = 1344,isDefineId = false}
setting_short_red_point.ACTIVITY_246_RED_POINT_Type_2 = {parent = {"ACTIVITY_246_RED_POINT"},activityId = 1344,isDefineId = false}
setting_short_red_point.ACTIVITY_246_RED_POINT_Type_3 = {parent = {"ACTIVITY_246_RED_POINT"},activityId = 1344,isDefineId = false}

--涂鸦游戏活动页面红点
setting_short_red_point.ACTIVITY_205_RED_POINT = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,key =GameEnum.RedPointTypeEnum.ACTIVITY_205_RED_POINT,activityId = 1504,isDefineId = false}
setting_short_red_point.ACTIVITY_205_RED_POINT_1 = {parent = {"ACTIVITY_205_RED_POINT"},activityId = 1504,isDefineId = false}
setting_short_red_point.ACTIVITY_205_RED_POINT_2 = {parent = {"ACTIVITY_205_RED_POINT"},activityId = 1504,isDefineId = false}
setting_short_red_point.ACTIVITY_205_RED_POINT_3 = {parent = {"ACTIVITY_205_RED_POINT"},activityId = 1504,isDefineId = false}

setting_short_red_point.RUNBEAN = {parent = {"DreamBox"},key = GameEnum.RedPointTypeEnum.ACTIVITY_250_RED_POINT}
-- setting_short_red_point.ACTIVITY_250R = {parent = {"DreamBox"}, isFirstClickRP = true}

setting_short_red_point.RUNBEAN_OPEN_RED = {parent = {"RUNBEAN"}, isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_250}
setting_short_red_point.RUNBEAN_LEVEL_1 = {parent = {"RUNBEAN"}, activityId = GameEnum.ActivityEnum.ACTIVITY_250}
setting_short_red_point.RUNBEAN_LEVEL_2 = {parent = {"RUNBEAN"}, activityId = GameEnum.ActivityEnum.ACTIVITY_250}
setting_short_red_point.RUNBEAN_LEVEL_3 = {parent = {"RUNBEAN"}, activityId = GameEnum.ActivityEnum.ACTIVITY_250}
setting_short_red_point.RUNBEAN_LEVEL_4 = {parent = {"RUNBEAN"}, activityId = GameEnum.ActivityEnum.ACTIVITY_250}
setting_short_red_point.RUNBEAN_LEVEL_5 = {parent = {"RUNBEAN"}, activityId = GameEnum.ActivityEnum.ACTIVITY_250}
setting_short_red_point.RUNBEAN_LEVEL_6 = {parent = {"RUNBEAN"}, activityId = GameEnum.ActivityEnum.ACTIVITY_250}

--蛋糕师红点
setting_short_red_point.ThrowCakeGame = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_252, key = GameEnum.RedPointTypeEnum.ACTIVITY_252_RED_POINT}
--蛋糕师关卡新开启子红点
setting_short_red_point.ThrowCakeGameTab = {parent = {"ThrowCakeGame"}, activityId = GameEnum.ActivityEnum.ACTIVITY_252,}

-- --彩熊制造机首次点击红点
-- setting_short_red_point.BeautyMakerFirstClick = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1402, isDefineId = false,key=GameEnum.RedPointTypeEnum.ACTIVITY_258_RED_POINT}

-- --彩熊制造机页签红点
-- setting_short_red_point.BeautyMaker_Tab_1 = {parent = {"BeautyMakerFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
-- setting_short_red_point.BeautyMaker_Tab_2 = {parent = {"BeautyMakerFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
-- setting_short_red_point.BeautyMaker_Tab_3 = {parent = {"BeautyMakerFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
-- setting_short_red_point.BeautyMaker_Tab_4 = {parent = {"BeautyMakerFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
-- setting_short_red_point.BeautyMaker_Tab_5 = {parent = {"BeautyMakerFirstClick"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}

--递进式大转盘
setting_short_red_point.ProgressiveTurnTable = {key = GameEnum.RedPointTypeEnum.ACTIVITY_254_RED_POINT}
setting_short_red_point.ProgressiveTurnTableLottery = {parent = {"ProgressiveTurnTable"}, activityId = ProgressiveTurnTableConst.ActivityId, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.ProgressiveTurnTableAward = {parent = {"ProgressiveTurnTable"}, activityId = ProgressiveTurnTableConst.ActivityId, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.ProgressiveTurnTablePlot = {parent = {"ProgressiveTurnTable"}, activityId = ProgressiveTurnTableConst.ActivityId, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.ProgressiveTurnTableLotteryReward = {parent = {"ProgressiveTurnTableLottery"}}
setting_short_red_point.ProgressiveTurnTableAwardReward = {parent = {"ProgressiveTurnTableAward"}}
setting_short_red_point.ProgressiveTurnTablePlotReward = {parent = {"ProgressiveTurnTablePlot"}}

--碰乌龟抽奖
setting_short_red_point.TurtleLottery = {key = GameEnum.RedPointTypeEnum.ACTIVITY_461_RED_POINT}
setting_short_red_point.TurtleLottery_FirstClick = {parent = {"TurtleLottery"}, activityId = Activity461Config.ActId, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.TurtleLottery_Task = {parent = {"TurtleLottery"}, activityId = Activity461Config.ActId, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.TurtleLottery_FatigueReward = {parent = {"TurtleLottery"}, activityId = Activity461Config.ActId, isDefineId = false, isFirstClickRP = true}

--星级大赛赛制
setting_short_red_point.StarMatchGame = {key = GameEnum.RedPointTypeEnum.ACTIVITY_263_RED_POINT, isFirstClickRP = true}
setting_short_red_point.StarMatchGameStageTotalReward = {parent = {"StarMatchGame"}}
setting_short_red_point.StarMatchGameStage1stReward = {parent = {"StarMatchGame"}}
setting_short_red_point.StarMatchGameStage2ndReward = {parent = {"StarMatchGame"}}
setting_short_red_point.StarMatchGameStage3rdReward = {parent = {"StarMatchGame"}}

--庆典晚会
setting_short_red_point.CelebParty = {parent = {"activity471"}}

--彩虹步道奖励
setting_short_red_point.RainbowTrail_Reward = {parent = {"DreamBox","activity1407"},activityId = 259,isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_259_RED_POINT}
setting_short_red_point.RainbowTrail_Tab1 = {parent = {"DreamBox","activity1407"},activityId = GameEnum.ActivityEnum.ACTIVITY_259,isDefineId = true}
setting_short_red_point.RainbowTrail_Tab2 = {parent = {"DreamBox","activity1407"},activityId = GameEnum.ActivityEnum.ACTIVITY_259,isDefineId = true}
setting_short_red_point.RainbowTrail_Tab3 = {parent = {"DreamBox","activity1407"},activityId = GameEnum.ActivityEnum.ACTIVITY_259,isDefineId = true}
setting_short_red_point.RainbowTrail_Goto = {parent = {"DreamBox","activity1407"},activityId = GameEnum.ActivityEnum.ACTIVITY_259,isDefineId = true}

setting_short_red_point.AnniversaryCostumeClothesReward = {parent = {"CarnivalPartyMap","activity1405"}, activityId = 2484, isDefineId = false}
setting_short_red_point.AnniversaryCostumeProgressReward = {parent = {"CarnivalPartyMap","activity1405"}, activityId = 2484, isDefineId = false}
setting_short_red_point.AnniversaryCostumeFirstClick = {isFirstClickRP = true,parent = {"CarnivalPartyMap","activity1405"}, activityId = 2484, isDefineId = false}

setting_short_red_point.Shop1404 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1404, isDefineId = false}
setting_short_red_point.activity1405 = {isFirstClickRP = true,parent = {"ToyHouseMapHUD"}, activityId = 2484, isDefineId = false,key = GameEnum.RedPointTypeEnum.ACTIVITY_251_RED_1_POINT}
setting_short_red_point.activity1407 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1407, isDefineId = false}

--周年蛋糕页签红点
setting_short_red_point.activity1406 = {isFirstClickRP = true,parent = {"ToyHouseMapHUD"}, activityId = 2485, isDefineId = false,key = GameEnum.RedPointTypeEnum.ACTIVITY_251_RED_2_POINT}
--周年蛋糕跳转按钮红点
setting_short_red_point.activity1406_go = {isFirstClickRP = true,parent = {"activity1406"}, activityId = 2485, isDefineId = false,}
setting_short_red_point.Story1476 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1476, isDefineId = false}

setting_short_red_point.actStory1447 = {parent = {"ToyHouseMapHUD"}, activityId = 1447, isDefineId = false, isFirstClickRP = true}
setting_short_red_point.actStory1464 = {parent = {"ToyHouseMapHUD"}, activityId = 1464, isDefineId = false, isEveryDayRP = true}
setting_short_red_point.actStory1465 = {parent = {"ToyHouseMapHUD"}, activityId = 1465, isDefineId = false, isFirstClickRP = true}
-- setting_short_red_point.actSign1398 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1398,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

setting_short_red_point.MoveOrDieActRed = {key = GameEnum.RedPointTypeEnum.ACTIVITY_262_RED_POINT, parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_262}
-- -- 2.0.3小主题活动剧情红点
-- setting_short_red_point.actStory1410 = {isFirstClickRP = true,parent = {"CCTV"}}
-- setting_short_red_point.actStoryTab1410_1 = {isFirstClickRP = true,parent = {"actStory1410"}}
-- setting_short_red_point.actStoryTab1410_2 = {isFirstClickRP = true,parent = {"actStory1410"}}

--2.2.3小红书活动剧情红点
setting_short_red_point.actStory1541 = {isFirstClickRP = true,parent = {"CCTV"}}
setting_short_red_point.actStoryTab1541_1 = {isFirstClickRP = true,parent = {"actStory1541"}}
setting_short_red_point.actStoryTab1541_2 = {isFirstClickRP = true,parent = {"actStory1541"}}
setting_short_red_point.actStoryTab1541_3 = {isFirstClickRP = true,parent = {"actStory1541"}}
-- setting_short_red_point.CollectStamp1610= {parent = {"CCTV"}, activityId = 1610, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_244_RED_POINT}
--setting_short_red_point.activity1540 = {parent = {"CCTV"}, isFirstClickRP = true}

--这里因为有双层逻辑，所以用了商店的逻辑
setting_short_red_point.activity1387_2 = {isFirstClickRP = true,parent = {"activity1387_1"}, activityId = 1404, isDefineId = false}
setting_short_red_point.activity1387_1 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1387, isDefineId = false}

setting_short_red_point.DreamBox_1 = {activityId = DreamBoxFacade.mapActivityId_1, isDefineId = false}

setting_short_red_point.Tangram_Level1 = {parent = {"DreamBox"}}
setting_short_red_point.Tangram_Level2 = {parent = {"DreamBox"}}
setting_short_red_point.Tangram_Level3 = {parent = {"DreamBox"}}
setting_short_red_point.Tangram_Level4 = {parent = {"DreamBox"}}
setting_short_red_point.Tangram_Level5 = {parent = {"DreamBox"}}
setting_short_red_point.Tangram_Level6 = {parent = {"DreamBox"}}
-- setting_short_red_point.CollectStamp1451 = {parent = {"DreamBox_1"}, activityId = 1451, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_244_RED_POINT}
-- setting_short_red_point.activity1451 = {parent = {"DreamBox_1"}, isFirstClickRP = true}

-- setting_short_red_point.activity1479 = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1479, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_268_RED_POINT}
--游梦相思增加一个子节点，用于前端跨天刷新红点
-- setting_short_red_point.activity1479_son = {parent = {"activity1536"} ,isEveryDayRP = true , activityId = 1548, isDefineId = false,}

-- setting_short_red_point.signin1414 = {parent = {"DreamBox_1"}, isFirstClickRP = true, activityId = 1414,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT}
setting_short_red_point.Story1415 = {isFirstClickRP = true,parent = {"DreamBox_1"}, activityId = 1415, isDefineId = false}

setting_short_red_point.activity1452 = {isEveryDayRP = true, activityId = 261, isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_261_RED_POINT}
setting_short_red_point.activity1452Task = {parent = {"activity1452"}}
setting_short_red_point.activity425 = {}
setting_short_red_point.activity425Task = {parent = {"activity425"}}
setting_short_red_point.activity425Reward = {key = 425, parent={"activity425"}}
setting_short_red_point.activity425Goods = {isFirstClickRP = true, parent={"activity425"}}
-- setting_short_red_point.activity1452Signup = {isFirstClickRP = true, parent = {"activity1452"}}


setting_short_red_point.ACTIVITY_265_RED_POINT = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = 265, isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_265_RED_POINT}
setting_short_red_point.ACTIVITY_266_RED_POINT = {isFirstClickRP = true, parent = {"ToyHouseMapHUD"}, activityId = 266, isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_266_RED_POINT}
setting_short_red_point.ACTIVITY_267_RED_POINT = {isEveryDayRP = true, parent = {"ToyHouseMapHUD"}, activityId = 267, isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_267_RED_POINT}
-- setting_short_red_point.ACTIVITY_271_RED_POINT = {isFirstClickRP = true, parent = {"ToyHouseMapHUD"}, activityId = 268, isDefineId = true}
-- setting_short_red_point.ACTIVITY_269_RED_POINT = {isFirstClickRP = true, parent = {"ToyHouseMapHUD"}, activityId = 269, isDefineId = true}
-- setting_short_red_point.ACTIVITY_270_RED_POINT = {isFirstClickRP = true, parent = {"ToyHouseMapHUD"}, activityId = 270, isDefineId = true}
-- 星际大赛狼人杀首次点击
setting_short_red_point.StarGameWerewolfFirstClick = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = 1454, isDefineId = false}

-- setting_short_red_point.actSign1445 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1445,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}
--游梦觅诗红点
-- setting_short_red_point.act1466 = {parent = {"DreamBox"}, activityId = 1466,isDefineId = false}
-- setting_short_red_point.act1466_Tab_1 = {parent = {"act1466"}}
-- setting_short_red_point.act1466_Tab_2 = {parent = {"act1466"}}
-- setting_short_red_point.act1466_Tab_3 = {parent = {"act1466"}}
-- setting_short_red_point.act1466_Tab_4 = {parent = {"act1466"}}
-- setting_short_red_point.act1466_Tab_5 = {parent = {"act1466"}}
-- setting_short_red_point.act1466_Tab_6 = {parent = {"act1466"}}

-- 小主题活动
setting_short_red_point.Termbegins = {isFirstClickRP = true, activityId = 2069, isDefineId = false}
setting_short_red_point.SmallActivitySignIn = {parent = {"Termbegins"}, isEveryDayRP = true, activityId = 2068, key = GameEnum.RedPointTypeEnum.ACTIVITY_199_RED_POINT, isDefineId = false}
setting_short_red_point.SmallActivityAnswer = {parent = {"Termbegins"}, isEveryDayRP = true, activityId = 2066, key = GameEnum.RedPointTypeEnum.ACTIVITY_143_RED_POINT, isDefineId = false}
setting_short_red_point.SmallActivityScore = {parent = {"Termbegins"}, isFirstClickRP = true, activityId = 2067, key = GameEnum.RedPointTypeEnum.ACTIVITY_299_RED_POINT, isDefineId = false}
-- setting_short_red_point.SmallActivityStore = {parent = {"Termbegins"}, isFirstClickRP = true, activityId = 1921, isDefineId = false}
-- setting_short_red_point.SmallActivityCollect = {parent = {"Termbegins"} ,isFirstClickRP = true, activityId = 1855, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_244_RED_POINT}

--乞巧织物
setting_short_red_point.CollectStamp1481 = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1845,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_192_RED_POINT}
setting_short_red_point.CollectStamp1481_btnFriend = {parent = {"CollectStamp1481"}, isFirstClickRP = true, activityId = 1845,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_192_SEND_RED_POINT}
--乞巧织物绑定好友但未发送礼物时的一次性红点
setting_short_red_point.CollectStamp1481_btnFriend_2 = {parent = {"CollectStamp1481_btnFriend"}}

--2.1.2音箱
 setting_short_red_point.DreamBoxSpeakerGift = {
 	parent = {"DreamBox"},
     isFirstClickRP = true,
     activityId = 1793,
     isDefineId = false
 }

--俄罗斯方块
setting_short_red_point.Tetris_Tab_1 = {activityId = GameEnum.ActivityEnum.ACTIVITY_278,isDefineId = true}
setting_short_red_point.Tetris_Tab_2 = {activityId = GameEnum.ActivityEnum.ACTIVITY_278,isDefineId = true}
setting_short_red_point.Tetris_Tab_3 = {activityId = GameEnum.ActivityEnum.ACTIVITY_278,isDefineId = true}
setting_short_red_point.Tetris_Tab_4 = {activityId = GameEnum.ActivityEnum.ACTIVITY_278,isDefineId = true}
setting_short_red_point.Tetris_Reward = {isFirstClickRP = true,parent = {"ToyHouseMapHUD"}, activityId = GameEnum.ActivityEnum.ACTIVITY_278,isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_278_RED_POINT}
setting_short_red_point.TetrisNewLevel  = {activityId = GameEnum.ActivityEnum.ACTIVITY_278, isDefineId = true}
--setting_short_red_point.TetrisNewLevel = {parent = {"ToyHouseMapHUD"}, activityId = GameEnum.ActivityEnum.ACTIVITY_278, isDefineId = true}

--俄罗斯方块
setting_short_red_point.PuzzleBobble_Tab_1 = {parent = {"PuzzleBobble_Reward"}, activityId = 411,isDefineId = true}
setting_short_red_point.PuzzleBobble_Tab_2 = {parent = {"PuzzleBobble_Reward"}, activityId = 411,isDefineId = true}
setting_short_red_point.PuzzleBobble_Tab_3 = {parent = {"PuzzleBobble_Reward"}, activityId = 411,isDefineId = true}
setting_short_red_point.PuzzleBobble_Tab_4 = {parent = {"PuzzleBobble_Reward"}, activityId = 411,isDefineId = true}
setting_short_red_point.PuzzleBobble_Reward = {isFirstClickRP = true,parent = {"ToyHouseMapHUD"}, activityId =411,isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_411_RED_POINT}

-- setting_short_red_point.actSign1499 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1499,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

--周轮询活动
setting_short_red_point.WeekPollRedPoint = {key = GameEnum.RedPointTypeEnum.ACTIVITY_276_RED_POINT}

setting_short_red_point.activity1505 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1505,isDefineId = false}
setting_short_red_point.activity1555 = {activityId = 1555,isDefineId = false}

setting_short_red_point.ACTIVITY_277_RED_POINT_1 = {isFirstClickRP = true,activityId = Activity277Controller.ActivityId1,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_277_RED_2_POINT}
setting_short_red_point.ACTIVITY_277_RED_POINT_2 = {isFirstClickRP = true,activityId = Activity277Controller.ActivityId2,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_277_RED_1_POINT}

-- 满月纪念
setting_short_red_point.FullMoonCommemoration = {parent = {"CarnivalPartyMap"}, isFirstClickRP = true, activityId = 1953, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_281_RED_POINT}
for i = 1, 4 do
	setting_short_red_point["FullMoonCommemoration_Tab_" .. i] = {parent = {"FullMoonCommemoration"}}
end

-- 满月商店
setting_short_red_point.Shop1530Tab = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1530, isDefineId = false}
setting_short_red_point.Shop1530Tab1 = {parent = {"DreamBox"}, isDefineId = false}
setting_short_red_point.Shop1530Tab2 = {parent = {"DreamBox"}, isDefineId = false}
--满月签到
-- setting_short_red_point.actSign1528 = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1528,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT}

setting_short_red_point.lanternriddle = {parent = {"MiddleActivity"}, isFirstClickRP = true, activityId = 280, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_280_RED_POINT}
--满月灯谜增加一个子节点，用于前端跨天刷新红点
setting_short_red_point.lanternriddle_son = {parent = {"lanternriddle"} ,isEveryDayRP = true , activityId = 280, isDefineId = true,}

--满月工坊
 setting_short_red_point.activity1536 = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1775,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_268_RED_POINT}
 setting_short_red_point.activity1775_son = {parent = {"activity1536"} ,isEveryDayRP = true , activityId = 1775, isDefineId = false}
 setting_short_red_point.activity1536F = {parent = {"activity1536"}, activityId = 1775,isFirstClickRP = true}

 --setting_short_red_point.activity1536R = {parent = {"activity1536"}, activityId = 1703, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_268_RED_POINT}

--ip周年庆任务
setting_short_red_point.Story1525 = {isFirstClickRP = true,parent = {"DreamBox_1"}, activityId = 1525, isDefineId = false}
setting_short_red_point.Firework1549 = {isEveryDayRP = true,parent = {"DreamBox_1"}, activityId = 135, isDefineId = true}
setting_short_red_point.Share1524 = {isFirstClickRP = true,parent = {"DreamBox_1"}, activityId = 1524, isDefineId = false}

--满月剧情
setting_short_red_point.Story1529 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1529, isDefineId = false}

--解密红点
setting_short_red_point.Wolf_NightAt_First_Click_RedPoint = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_175_WEREWOLF_NIGHT_ATTACK}
--暗夜袭击 红点
setting_short_red_point.DecodeGame = {key = GameEnum.RedPointTypeEnum.ACTIVITY_175_RED_POINT, parent = {"ToyHouseMapHUD"}}

--捉虫游戏活动页面红点
setting_short_red_point.ACTIVITY_282_RED_POINT = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = 1704,isDefineId = false}
--捉虫游戏后端推送红点
setting_short_red_point.ACTIVITY_282_RED_POINT_Info = {parent = {"ACTIVITY_282_RED_POINT"},key =GameEnum.RedPointTypeEnum.ACTIVITY_282_RED_POINT,activityId = 1704,isDefineId = false}
--捉虫游戏活动种类奖励红点
setting_short_red_point.ACTIVITY_282_RED_POINT_1 = {parent = {"ACTIVITY_282_RED_POINT"},activityId = 1704,isDefineId = false}
--捉虫游戏进度奖励红点
setting_short_red_point.ACTIVITY_282_RED_POINT_2 = {parent = {"ACTIVITY_282_RED_POINT"},activityId = 1704,isDefineId = false}
--捉虫游戏分数奖励红点
setting_short_red_point.ACTIVITY_282_RED_POINT_3 = {parent = {"ACTIVITY_282_RED_POINT_Task"},activityId = 1704,isDefineId = false}
--捉虫游戏昆虫种类红点
setting_short_red_point.ACTIVITY_282_RED_POINT_Type_1 = {parent = {"ACTIVITY_282_RED_POINT"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Type_2 = {parent = {"ACTIVITY_282_RED_POINT"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Type_3 = {parent = {"ACTIVITY_282_RED_POINT"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Type_4 = {parent = {"ACTIVITY_282_RED_POINT"},activityId = 1704,isDefineId = false}
--组队任务按钮红点
setting_short_red_point.ACTIVITY_282_RED_POINT_Task = {parent = {"ACTIVITY_282_RED_POINT"},isFirstClickRP = true,activityId = 1704,isDefineId = false}
--任务页签红点
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_1 = {parent = {"ACTIVITY_282_RED_POINT_Task"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_2 = {parent = {"ACTIVITY_282_RED_POINT_Task"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_3 = {parent = {"ACTIVITY_282_RED_POINT_Task"},activityId = 1704,isDefineId = false}
--任务页签任务假红点
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_Tab_1 = {parent = {"ACTIVITY_282_RED_POINT_Task_1"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_Tab_2 = {parent = {"ACTIVITY_282_RED_POINT_Task_2"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_Tab_3 = {parent = {"ACTIVITY_282_RED_POINT_Task_3"},activityId = 1704,isDefineId = false}
--任务页签首次点击假红点
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_First_1 = {parent = {"ACTIVITY_282_RED_POINT_Task_1"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_First_2 = {parent = {"ACTIVITY_282_RED_POINT_Task_2"},activityId = 1704,isDefineId = false}
setting_short_red_point.ACTIVITY_282_RED_POINT_Task_First_3 = {parent = {"ACTIVITY_282_RED_POINT_Task_3"},activityId = 1704,isDefineId = false}


-- setting_short_red_point.actSign1554 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1554,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

--setting_short_red_point.DWOLTotal = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1580,isDefineId = false}
setting_short_red_point.DWOL_Level1 = {parent = {"DreamBox"}}
setting_short_red_point.DWOL_Level2 = {parent = {"DreamBox"}}
setting_short_red_point.DWOL_Level3 = {parent = {"DreamBox"}}
setting_short_red_point.DWOL_Level4 = {parent = {"DreamBox"}}
setting_short_red_point.DWOL_Level5 = {parent = {"DreamBox"}}
setting_short_red_point.DWOL_Level6 = {parent = {"DreamBox"}}
-- 盛世月典商店
-- setting_short_red_point.Shop1577Tab = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1577, isDefineId = false}
-- setting_short_red_point.Shop1577Tab1 = {parent = {"DreamBox"}, isDefineId = false}
-- setting_short_red_point.Shop1577Tab2 = {parent = {"DreamBox"}, isDefineId = false}

-- 盛世月典签到
-- setting_short_red_point.actSign1575 = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1575,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT}

-- 盛世月典剧情
setting_short_red_point.Story1576 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1576, isDefineId = false}
---- 2.3.2月典选编，其中第二第三个红点是没用的，为了不报错而占的坑
--setting_short_red_point.CardCollecting1573 = {parent = {"DreamBox"}, activityId = 1573,isDefineId = false, isFirstClickRP = true}
--setting_short_red_point.CardCollecting1573_1 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_PROCESS_RED_POINT,activityId = 1573,isDefineId = false, isFirstClickRP = true}
--setting_short_red_point.CardCollecting1573_2 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_RED_POINT,activityId = 1573,isDefineId = false, isFirstClickRP = true}

--熊熊摇摇船
setting_short_red_point.RockingboatRedpoint = {activityId = 287, isDefineId = true}
setting_short_red_point.RockingboatReward = {activityId = 287, parent = {"RockingboatRedpoint"}, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_287_RED_POINT}
setting_short_red_point.RockingboatTask = {activityId = 287, parent = {"RockingboatRedpoint"},  isDefineId = true}
setting_short_red_point.RockingboatFreeTimes = {activityId = 287, parent = {"RockingboatRedpoint"}, isDefineId = true}

--晶钻瓶
setting_short_red_point.ACTIVITY_286_RED_POINT = {activityId = 286, key = GameEnum.RedPointTypeEnum.ACTIVITY_286_RED_POINT, isDefineId = true}
setting_short_red_point.ACTIVITY_286_LOGIN_RED_POINT = {parent = {"ACTIVITY_286_RED_POINT"},activityId = 286}

--关注企微
setting_short_red_point.Activity289 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_289_RED_POINT}

-- setting_short_red_point.actSign1596 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 1596,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}
setting_short_red_point.ToyHouseSign = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 196, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_196_RED_POINT}

--雾月变装玩法主题推广
setting_short_red_point.act288_promotion = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true,  activityId = 1592,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_288_DESIGN_COUNT_RED_POINT}

-- setting_short_red_point.CollectStamp1610= {parent = {"DreamBox"} ,isFirstClickRP = true, activityId = 1610, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_244_RED_POINT}

-- 2.4.2剧情
-- setting_short_red_point.Story1605 = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = 1605, isDefineId = false}

-- 2.4.2 npc提交
--setting_short_red_point.activity1607 = {parent = {"DreamBox"}, isEveryDayRP = true, activityId = 1607, isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_268_RED_POINT}

-- 2.4.2 彩熊制造机首次点击红点
setting_short_red_point.BeautyMaker_1606 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 258, isDefineId = true,key=GameEnum.RedPointTypeEnum.ACTIVITY_258_RED_POINT}

-- 2.4.2 彩熊制造机页签红点
setting_short_red_point.BeautyMaker_Tab_1 = {parent = {"BeautyMaker_1606"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
setting_short_red_point.BeautyMaker_Tab_2 = {parent = {"BeautyMaker_1606"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
setting_short_red_point.BeautyMaker_Tab_3 = {parent = {"BeautyMaker_1606"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
setting_short_red_point.BeautyMaker_Tab_4 = {parent = {"BeautyMaker_1606"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}
setting_short_red_point.BeautyMaker_Tab_5 = {parent = {"BeautyMaker_1606"}, activityId = GameEnum.ActivityEnum.ACTIVITY_258}

-- setting_short_red_point.actSign1604 = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = 1604,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT}

-- setting_short_red_point.activity208 = {isFirstClickRP = true, activityId = 208,isDefineId = true}
-- setting_short_red_point.activity226 = {isFirstClickRP = true, activityId = 226,isDefineId = true}
-- setting_short_red_point.activity230 = {isFirstClickRP = true, activityId = 230,isDefineId = true}
-- setting_short_red_point.activity240 = {}
-- setting_short_red_point.activity300 = {isFirstClickRP = true, activityId = 300,isDefineId = true}
-- setting_short_red_point.activity301 = {isFirstClickRP = true, activityId = 301,isDefineId = true}
-- setting_short_red_point.activity303 = {}
-- setting_short_red_point.activity305 = {isFirstClickRP = true, activityId = 305,isDefineId = true}
setting_short_red_point.activity306 = {isFirstClickRP = true, activityId = 306,isDefineId = true}
setting_short_red_point.activity307 = {isFirstClickRP = true, activityId = 307,isDefineId = true}
-- setting_short_red_point.activity308 = {isFirstClickRP = true, activityId = 308,isDefineId = true}
setting_short_red_point.activity309 = {activityId = 309,isDefineId = true}
-- setting_short_red_point.activity310 = {isFirstClickRP = true, activityId = 310,isDefineId = true}
-- setting_short_red_point.activity311 = {isFirstClickRP = true, activityId = 311,isDefineId = true}
-- setting_short_red_point.activity312 = {isFirstClickRP = true, activityId = 312,isDefineId = true}
-- setting_short_red_point.activity313 = {isFirstClickRP = true, activityId = 313,isDefineId = true}
-- setting_short_red_point.activity314 = {isFirstClickRP = true, activityId = 314,isDefineId = true}
-- setting_short_red_point.activity315 = {isFirstClickRP = true, activityId = 315,isDefineId = true}
-- setting_short_red_point.activity316 = {isFirstClickRP = true, activityId = 316,isDefineId = true}
-- setting_short_red_point.activity317 = {isFirstClickRP = true, activityId = 317,isDefineId = true}
setting_short_red_point.Lottery_Old_Activity = {activityId = 305,isDefineId = true}
setting_short_red_point.Lottery_New_Activity = {activityId = 326,isDefineId = true, parent = {"ToyHouseMapHUD"}}
setting_short_red_point.Lottery_Exchange = {isEveryDayRP = true, parent = {"Lottery_New_Activity"}}
setting_short_red_point.Lottery_Progress_Reward = {parent = {"Lottery_New_Activity"}, activityId = 320, isDefineId = true, key=87}
setting_short_red_point.selectLottery413 = {parent = {"Lottery_HUD"}, key = 413}
--- 限时图鉴
setting_short_red_point.Fashion_Act_Reward = {parent = {"selectLottery413","CommonShopTab","Lottery_New_Activity"},key = GameEnum.RedPointTypeEnum.ACTIVITY_132_AHS_REWARDS_RED_POINT,
	activityId =GameEnum.ActivityEnum.ACTIVITY_132_TIME_LIMIT_ARCHIVE}
setting_short_red_point.Fashion_Act_RewardEveryDay = { parent = {"Fashion_Act_Reward"},customHandlerName="isCollectAll",isEveryDayRP = true,activityId =GameEnum.ActivityEnum.ACTIVITY_132_TIME_LIMIT_ARCHIVE}
setting_short_red_point.Fashion_Act_GetNew = {key = GameEnum.RedPointTypeEnum.ACTIVITY_132_NEW_CLOTHES_RED_POINT}
setting_short_red_point.Fashion_Act_GetNewItem = {parent = {"Fashion_Act_Reward"}}
--- 2.5 砍树红点咯
-- setting_short_red_point.ACTIVITY_242_RED_POINT = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_242, key = GameEnum.RedPointTypeEnum.ACTIVITY_242_RED_POINT}
setting_short_red_point.LumberJack_RedPoint = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_242, key = GameEnum.RedPointTypeEnum.ACTIVITY_242_RED_POINT}
setting_short_red_point.LumberJack_lvl1 = {parent = {"LumberJack_RedPoint"}, activityId = LumberJackConfig.getCurActId(), isDefineId = false}
setting_short_red_point.LumberJack_lvl2 = {parent = {"LumberJack_RedPoint"}, activityId = LumberJackConfig.getCurActId(), isDefineId = false}
setting_short_red_point.LumberJack_lvl3 = {parent = {"LumberJack_RedPoint"}, activityId = LumberJackConfig.getCurActId(), isDefineId = false}
setting_short_red_point.LumberJack_lvl4 = {parent = {"LumberJack_RedPoint"}, activityId = LumberJackConfig.getCurActId(), isDefineId = false}
--星际大赛砍树关卡红点
-- setting_short_red_point.LumberJack_lvl5 = {parent = {"LumberJack_RedPoint"}, activityId = 1449, isDefineId = false}
-- setting_short_red_point.LumberJack_lvl6 = {parent = {"LumberJack_RedPoint"}, activityId = 1449, isDefineId = false}
-- setting_short_red_point.LumberJack_lvl6_RewardRP = {parent = {"LumberJack_lvl6"}, activityId = 1449, isDefineId = false}
--2.5你画我猜
setting_short_red_point.activity290 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 290,isDefineId = true,key=GameEnum.RedPointTypeEnum.ACTIVITY_290_RED_POINT}

-- DreamBox
setting_short_red_point.DreamBoxSign = {isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_138_STAR_LOGIN,isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_138_RED_POINT}
setting_short_red_point.DreamBoxStory = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = DreamBoxFacade.storyActivityId, isDefineId = false}
setting_short_red_point.DreamBoxStory_1 = {isFirstClickRP = true, activityId = DreamBoxFacade.storyActivityId_1, isDefineId = false}
--小主题跳往充值商店
-- setting_short_red_point.DreamBoxToRechargeView = {isFirstClickRP = true,parent = {"DreamBox"}, activityId = DreamBoxFacade.mapActivityId, isDefineId = false}

--2.5.2 煲汤
setting_short_red_point.activity260 = {parent = {"DreamBox"}, isEveryDayRP = true, activityId = 1939,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_260_RED_POINT}

-- setting_short_red_point.TaskSignRedPoint = {parent = {"DreamBox_1"}, isEveryDayRP = true, activityId = DreamBoxFacade.taskSignInActId,isDefineId = false}
setting_short_red_point.NewYearTimeLine = {parent = {"DreamBox"}, activityId = 291,isDefineId = true,isFirstClickRP = true}

setting_short_red_point.activity294 = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_294, key = GameEnum.RedPointTypeEnum.ACTIVITY_294_RED_POINT, isDefineId = true}

--生态园推广
-- setting_short_red_point.EcologyParkAds = {parent = {"ToyHouseMapHUD"},isFirstClickRP=true, activityId = 176}
setting_short_red_point.EcologyParkAds_tab_1 = {parent = {"StoryTellerFirstClick"}} -- 我也不懂
-- setting_short_red_point.EcologyParkAds_tab_2 = {parent = {"EcologyParkAds"}}
-- setting_short_red_point.EcologyParkAds_tab_3 = {parent = {"EcologyParkAds"}}
-- setting_short_red_point.EcologyParkAds_tab_4 = {parent = {"EcologyParkAds"}}
-- setting_short_red_point.EcologyParkAds_tab_5 = {parent = {"EcologyParkAds"}}
-- setting_short_red_point.EcologyParkAds_tab_6 = {parent = {"EcologyParkAds"}}
-- setting_short_red_point.EcologyParkAds_tab_7 = {parent = {"EcologyParkAds"}}
-- 生态园累充
setting_short_red_point.EcoParkRecharge = {activityId = 296}

setting_short_red_point.Shop2433Tab = {activityId = 2433, isDefineId = false ,key = GameEnum.RedPointTypeEnum.ACTIVITY_298_RED_POINT}
setting_short_red_point.Shop2433Tab1 = {}
setting_short_red_point.Shop2433Tab2 = {}
setting_short_red_point.Shop2433Tab3 = {}
--赛季链式礼包
setting_short_red_point.SeasonChainBag = {activityId = 410}

setting_short_red_point.Act297_Level1 = {parent = {"Act297_Award"}, activityId = 297, isDefineId = true}
setting_short_red_point.Act297_Level2 = {parent = {"Act297_Award"}, activityId = 297, isDefineId = true}
setting_short_red_point.Act297_Level3 = {parent = {"Act297_Award"}, activityId = 297, isDefineId = true}
setting_short_red_point.Act297_Level4 = {parent = {"Act297_Award"}, activityId = 297, isDefineId = true}
setting_short_red_point.Act297_Award = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 297, isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_297_RED_POINT}

setting_short_red_point.WealthGodFirstClick = {activityId = 295,isEveryDayRP = true,isDefineId = true,isFirstClickRP = true}

--2.7 开心餐厅
setting_short_red_point.HappyRestaurant_1736 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = ToyHouseGotoUtil.happyrestaurant,isDefineId = false, key = GameEnum.RedPointTypeEnum.ACTIVITY_214_RED_POINT}
--开心餐厅页签红点
setting_short_red_point.HappyRestaurant_1736_Tab_1 = {parent = {"HappyRestaurant_1736"}, activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}
setting_short_red_point.HappyRestaurant_1736_Tab_2 = {parent = {"HappyRestaurant_1736"}, activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}
setting_short_red_point.HappyRestaurant_1736_Tab_3 = {parent = {"HappyRestaurant_1736"}, activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}
setting_short_red_point.HappyRestaurant_1736_Tab_4 = {parent = {"HappyRestaurant_1736"}, activityId = GameEnum.ActivityEnum.ACTIVITY_214_HAPPY_CANTEEN}

--2.7过年活动签到红点
--setting_short_red_point.TaskSignRedPoint_1765 = {parent = {"DreamBox_1"}, isEveryDayRP = true, activityId = 1765,isDefineId = false}
setting_short_red_point.TaskSignRedPoint = {parent = {"DreamBox"}, isEveryDayRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_293,isDefineId = true}

-- 2.7华服集卡活动

setting_short_red_point.CardCollecting2005 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_PROCESS_RED_POINT,parent = {"CCTV"}, activityId = 2005,isDefineId = false, isFirstClickRP = true}
setting_short_red_point.CardCollecting2005_1 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_RED_POINT,parent = {"CardCollecting2005"},activityId = 2005,isDefineId = false}
setting_short_red_point.CardCollecting2005_2 = {parent = {"CardCollecting2005"},activityId = 2005,isDefineId = false}

-- setting_short_red_point.CCTV_Fatigue = {isFirstClickRP=true,parent = {"CCTV"},key = GameEnum.RedPointTypeEnum.ACTIVITY_221_RED_POINT,activityId = 1196,isDefineId = false}
-- setting_short_red_point.CCTV_CardCollecting = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_PROCESS_RED_POINT,parent = {"CCTV"},activityId = 1411,isDefineId = false, isFirstClickRP = true}
-- setting_short_red_point.CCTV_220 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_220_RED_POINT,parent = {"CCTV"},activityId = 1411,isDefineId = false}

--2.7.2
setting_short_red_point.ConnectLineGame = {parent={"DreamBox"},key = GameEnum.RedPointTypeEnum.ACTIVITY_403_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_403,isDefineId = true, isFirstClickRP = true}
for i = 1, 6 do
	setting_short_red_point["ConnectLineGame_Level"..i] = {parent = {"ConnectLineGame"},activityId = GameEnum.ActivityEnum.ACTIVITY_403,isDefineId = true}
end
--神庙善行
setting_short_red_point.TempleRedPoint = {parent={"DreamBox"}, activityId = GameEnum.ActivityEnum.ACTIVITY_409,isDefineId = true, isEveryDayRP = true}

--消灭花花首次点击红点
setting_short_red_point.EraseFlowerFirstClick = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_404, key = GameEnum.RedPointTypeEnum.ACTIVITY_404_RED_POINT, isDefineId = true}

--消灭花花页签红点
setting_short_red_point.EraseFlower_Tab_1 = {--[[parent = {"EraseFlowerFirstClick"},]] activityId = GameEnum.ActivityEnum.ACTIVITY_404}
setting_short_red_point.EraseFlower_Tab_2 = {--[[parent = {"EraseFlowerFirstClick"},]] activityId = GameEnum.ActivityEnum.ACTIVITY_404}
setting_short_red_point.EraseFlower_Tab_3 = {--[[parent = {"EraseFlowerFirstClick"},]] activityId = GameEnum.ActivityEnum.ACTIVITY_404}
setting_short_red_point.EraseFlower_Tab_4 = {--[[parent = {"EraseFlowerFirstClick"},]] activityId = GameEnum.ActivityEnum.ACTIVITY_404}

-- setting_short_red_point.ACTIVITY_198_RED_POINT = {parent = {"DreamBox"}, isEveryDayRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_198_RED_POINT}

--游戏机时间
setting_short_red_point.TinyGameTime = { key = GameEnum.RedPointTypeEnum.ACTIVITY_407_RED_POINT}

setting_short_red_point.SeasonCollectCard = {}
setting_short_red_point.SeasonCollectCardEntrance = {parent = {"SeasonCollectCard"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_405, isDefineId = true}
setting_short_red_point.SeasonCollectCardTips = {isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_405, isDefineId = true}
setting_short_red_point.SeasonCollectCardReward = {parent = {"SeasonCollectCard"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_405_REWARD_RED_POINT}
setting_short_red_point.SeasonCollectCardStory = {parent = {"SeasonCollectCard"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_405, isDefineId = true}
setting_short_red_point.SeasonCollectGroceryFree = {parent = {"SeasonCollectCard"}, isEveryWeekRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_405, isDefineId = true}
setting_short_red_point.SeasonCollectXiaochengxu = {isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_405, isDefineId = true}

setting_short_red_point.RhythemBattles_RedPoint = {parent = {"ToyHouseMapHUD"},isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_406, key = GameEnum.RedPointTypeEnum.ACTIVITY_406_RED_POINT}

--扫雷
setting_short_red_point.MinesWeeperGame = { key = GameEnum.RedPointTypeEnum.ACTIVITY_408_RED_POINT}


setting_short_red_point.aobiCircleNew = {
	parent = {"hudAobiCircle"}, isFirstClickRP = true
}

setting_short_red_point.Act412Level_1 = { parent = {"Act412Tab"},activityId = 412, isDefineId = true }
setting_short_red_point.Act412Level_2 = { parent = {"Act412Tab"},activityId = 412, isDefineId = true }
setting_short_red_point.Act412Level_3 = { parent = {"Act412Tab"},activityId = 412, isDefineId = true }
setting_short_red_point.Act412Level_4 = { parent = {"Act412Tab"},activityId = 412, isDefineId = true }
setting_short_red_point.Act412Level_5 = { parent = {"Act412Tab"},activityId = 412, isDefineId = true }
setting_short_red_point.Act412Tab = {parent = {"DreamBox"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_412_RED_POINT, activityId = 412, isDefineId = true}

setting_short_red_point.ActivityOrder = {parent = {"DreamBox"}, isEveryDayRP = true, isDefineId = true, activityId = GameEnum.ActivityEnum.ACTIVITY_195_ORDER}

setting_short_red_point.Act420Level_1 = { parent = {"Act420Tab"},activityId = 420,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 1}
setting_short_red_point.Act420Level_2 = { parent = {"Act420Tab"},activityId = 420,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 2}
setting_short_red_point.Act420Level_3 = { parent = {"Act420Tab"},activityId = 420,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 3}
setting_short_red_point.Act420Level_4 = { parent = {"Act420Tab"},activityId = 420,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 4}
setting_short_red_point.Act420Tab = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_420_RED_POINT, activityId = 420, isDefineId = true}

--424
setting_short_red_point.Act424Game = {parent = {"ToyHouseMapHUD"}}
setting_short_red_point.Act424GameTab1 = {parent = {"Act424Game"},key = GameEnum.RedPointTypeEnum.ACTIVITY_424_RED_POINT, activityId = 424, isDefineId = true, isFirstClickRP = true}
setting_short_red_point.Act424GameTab2 = {parent = {"Act424Game"},activityId = 424, isDefineId = true, isFirstClickRP = true}
-- 2.11.2 找你妹
setting_short_red_point.FindTheItem = {parent = {"DreamBox"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_417, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_417_RED_POINT}
setting_short_red_point.FindTheItem_Tab_1 = {parent = {"FindTheItem"}}
setting_short_red_point.FindTheItem_Tab_2 = {parent = {"FindTheItem"}}
setting_short_red_point.FindTheItem_Tab_3 = {parent = {"FindTheItem"}}
setting_short_red_point.FindTheItem_Tab_4 = {parent = {"FindTheItem"}}

setting_short_red_point.StoryTellerFirstClick = {
	parent = {"ToyHouseMapHUD"},
	isFirstClickRP = true, 
	activityId = 1929, 
	isDefineId = false,
	-- key = GameEnum.RedPointTypeEnum.ACTIVITY_406_RED_POINT

}

setting_short_red_point.CarnivalPartyMap = {isFirstClickRP = true, activityId = DreamBoxFacade.carnivalMapActivityId, isDefineId = false}
--- 共建奥比广场推广活动红点
setting_short_red_point.BuildAobiSquare = {
	isDefineId = false, isFirstClickRP = true, 
	activityId = GameEnum.ActivityEnum.ACTIVITY_414, 
	key = GameEnum.RedPointTypeEnum.ACTIVITY_414_RED_POINT
}
--- 共建奥比广场推广活动红点
setting_short_red_point.BuildAobiSquarePromotion = {
	isDefineId = true, isEveryDayRP = true, 
	parent = {"ToyHouseMapHUD"},
	activityId = GameEnum.ActivityEnum.ACTIVITY_415, 
	key = GameEnum.RedPointTypeEnum.ACTIVITY_415_RED_POINT
}
setting_short_red_point.MileStoneMain = {
	isFirstClickRP = true,isDefineId = false, 
	activityId = GameEnum.ActivityEnum.ACTIVITY_416, 
}
setting_short_red_point.MileStoneWorldTask = {parent = {"MileStoneMain"}}
setting_short_red_point.MileStonePersonalTask = {parent = {"MileStoneMain"}}
setting_short_red_point.MileStoneGlobalTarget = {parent = {"MileStoneMain"}}
setting_short_red_point.MileStoneGlobalTarget_Server = {
	parent = {"MileStoneMain"},
	key = GameEnum.RedPointTypeEnum.ACTIVITY_416_RED_POINT
}
setting_short_red_point.SeaDanceActivity = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_419_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_419, isDefineId = true}
setting_short_red_point.SeaDanceFree = {parent = {"SeaDanceActivity"}, isEveryDayRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_419, isDefineId = true}
-- setting_short_red_point.SeaDanceActivityTogether = {parent = {"SeaDanceActivity"}, isEveryDayRP = true, activityId = DreamBoxFacade.carnivalSeadanceActivityId, isDefineId = false}

setting_short_red_point.CashRedEnvelopeActivity = {}
setting_short_red_point.CashRedEnvelopeFirstClick = {parent = {"CashRedEnvelopeActivity"}, isFirstClickRP = true,activityId = GameEnum.ActivityEnum.ACTIVITY_418}
setting_short_red_point.CashRedEnvelopeCashChestActivity = {parent = {"CashRedEnvelopeActivity"},activityId = GameEnum.ActivityEnum.ACTIVITY_418}

setting_short_red_point.DonatteActFirstClick = {parent = {"DreamBox"} ,activityId = GameEnum.ActivityEnum.ACTIVITY_421, isDefineId = true}
setting_short_red_point.DonatteActTaskComplete = {parent = {"DreamBox"} ,activityId = GameEnum.ActivityEnum.ACTIVITY_421, isDefineId = true}
setting_short_red_point.DonatteActLevelReward = {parent = {"DreamBox"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_421_1_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_421, isDefineId = true}
setting_short_red_point.DonatteActCanLevelUp = {parent = {"DreamBox"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_421_2_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_421, isDefineId = true}

-- setting_short_red_point.Act422Level_1 = { parent = {"Act422Tab"},activityId = 422,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 1}
-- setting_short_red_point.Act422Level_2 = { parent = {"Act422Tab"},activityId = 422,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 2}
-- setting_short_red_point.Act422Level_3 = { parent = {"Act422Tab"},activityId = 422,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 3}
--setting_short_red_point.Act422Level_4 = { parent = {"Act422Tab"},activityId = 422,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 4}
--setting_short_red_point.Act422Level_5 = { parent = {"Act422Tab"},activityId = 422,isFirstClickRP_ActTinyGame = true, isDefineId = true ,startStampHandlerName = "getAct420StartTime",startStampHandlerParams = 5}
setting_short_red_point.Act422Game = { parent = {"Act422Tab"},activityId = 422,isFirstClickRP_ActTinyGame = true, isDefineId = true }
setting_short_red_point.Act422Task = { parent = {"Act422Tab"},activityId = 422, isDefineId = true }
setting_short_red_point.Act422Reward = { parent = {"Act422Tab"},activityId = 422, isDefineId = true }
setting_short_red_point.Act422Tab = {parent = {"MiddleActivity"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_422_RED_POINT, activityId = 422, isDefineId = true}

setting_short_red_point.PetBattles_RedPoint = {isDefineId = false, isFirstClickRP = true, parent = {"ToyHouseMapHUD"}, activityId = 2020}

-- 马戏团
setting_short_red_point.act423 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 423, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_423_RED_POINT}

setting_short_red_point.act426 = {parent = {"activity425"}, isFirstClickRP = true, activityId = 426, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_426_RED_POINT}
setting_short_red_point.act430 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 430, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_430_RED_POINT}
-- 快乐节签到（活动日历）
setting_short_red_point.act434 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_434_RED_POINT}

setting_short_red_point.act431 = {isFirstClickRP = true, activityId = 431, isDefineId = true}
setting_short_red_point.act431_reward = {parent = {"act431"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_431_RED_POINT, activityId = 431, isDefineId = true}

setting_short_red_point.activity429 = {activityId = 429, isDefineId = true, parent={"MiddleActivity"}}
setting_short_red_point.activity429Item = {parent={"activity429"}}
setting_short_red_point.activity429Reward = {parent={"activity429"}, key = 429}
setting_short_red_point.activity429Task = {parent={"activity429"}}
setting_short_red_point.activity432 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_432_RED_POINT, activityId = 432, isDefineId = true}
setting_short_red_point.activity438 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_438_RED_POINT, activityId = 438, isDefineId = true}
setting_short_red_point.activity441 = {activityId = 441, isDefineId = true, isEveryWeekRP = true}

--后端有道具的和有奖励的红点
setting_short_red_point.activity435 = {parent={"MiddleActivity"}, activityId = GameEnum.ActivityEnum.ACTIVITY_435, isDefineId = true , key = GameEnum.RedPointTypeEnum.ACTIVITY_435_RED_POINT}
setting_short_red_point.activity435Task = {parent={"activity435"}, activityId = GameEnum.ActivityEnum.ACTIVITY_435, isDefineId = true}
setting_short_red_point.activity435Reward = {parent={"activity435"}, activityId = GameEnum.ActivityEnum.ACTIVITY_435, isDefineId = true}
setting_short_red_point.activity435HasCostItem = {parent={"activity435"}, activityId = GameEnum.ActivityEnum.ACTIVITY_435, isDefineId = true}

-- 狼人杀逃生模式推广
setting_short_red_point.activity437 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_437_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_437, isDefineId = true}

setting_short_red_point.activity436 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_436_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_436, isDefineId = true}

setting_short_red_point.activity436_Tab_1 = {parent = {"activity436"}, activityId = GameEnum.ActivityEnum.ACTIVITY_436, isDefineId = true}
setting_short_red_point.activity436_Tab_2 = {parent = {"activity436"}, activityId = GameEnum.ActivityEnum.ACTIVITY_436, isDefineId = true}
setting_short_red_point.activity436_Tab_3 = {parent = {"activity436"}, activityId = GameEnum.ActivityEnum.ACTIVITY_436, isDefineId = true}

setting_short_red_point.activity440 = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACT440_SKATING_RED_POINT, activityId = 440, isDefineId = true, isEveryDayRP = true}
setting_short_red_point.activity440NewLevel = {parent = {"ToyHouseMapHUD"}, activityId = 440, isDefineId = true}

setting_short_red_point.activity445 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_445_RED_POINT, activityId = 445, isDefineId = true}

setting_short_red_point.activity446 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_446_RED_POINT, activityId = 446, isDefineId = true}
setting_short_red_point.activity249 = {key = GameEnum.RedPointTypeEnum.ACTIVITY_249_RED_POINT, parent = {"Time_Limit_Pet_Reward"}, activityId = GameEnum.RedPointTypeEnum.ACTIVITY_249_RED_POINT, isDefineId = true}

setting_short_red_point.activity448 = {parent = {"ToyHouseMapHUD"}, activityId = 448, isDefineId = true, isFirstClickRP = true}
setting_short_red_point.XiaoXiaoLe_Tab_1 = {activityId = GameEnum.ActivityEnum.ACTIVITY_448,isDefineId = true}
setting_short_red_point.XiaoXiaoLe_Tab_2 = {activityId = GameEnum.ActivityEnum.ACTIVITY_448,isDefineId = true}
setting_short_red_point.XiaoXiaoLe_Tab_3 = {activityId = GameEnum.ActivityEnum.ACTIVITY_448,isDefineId = true}
setting_short_red_point.XiaoXiaoLe_Tab_4 = {activityId = GameEnum.ActivityEnum.ACTIVITY_448,isDefineId = true}
setting_short_red_point.XiaoXiaoLe_Reward = {isFirstClickRP = true,parent = {"ToyHouseMapHUD"}, activityId = GameEnum.ActivityEnum.ACTIVITY_448,isDefineId = true,key = GameEnum.RedPointTypeEnum.ACTIVITY_448_RED_POINT}
setting_short_red_point.XiaoXiaoLeNewLevel = {parent = {"ToyHouseMapHUD"}, activityId = GameEnum.ActivityEnum.ACTIVITY_448, isDefineId = true}

setting_short_red_point.activity449 = {activityId = 449, isDefineId = true, isEveryDayRP = true}
setting_short_red_point.activity449_task = {parent={"activity449"}}
setting_short_red_point.activity449_reward = {parent={"activity449"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_449_RED_POINT}

setting_short_red_point.Exhibition = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 2227}

setting_short_red_point.act458 = {activityId = 458, isDefineId = true, isEveryDayRP = true}
setting_short_red_point.act458Award = {parent = {"act458"}, activityId = 458, isDefineId = true, key = 458}
setting_short_red_point.act458Tips = {parent = {"act458"},isFirstClickRP = true,  activityId = 458, isDefineId = true}
--中主题活动
setting_short_red_point.MiddleActivity = { isFirstClickRP = true}
-- setting_short_red_point.MiddleActivity_Shop = {parent = {"MiddleActivity"}, isFirstClickRP = true}

--城镇模拟
setting_short_red_point.TownSimulation = {key = GameEnum.RedPointTypeEnum.ACTIVITY_442_RED_POINT,activityId = 442, isDefineId = true}
setting_short_red_point.TownSimulationTask = {activityId = 442, isDefineId = true}

setting_short_red_point.LongStreetBanquet = {activityId = 444}

setting_short_red_point.NewYearGuide = {activityId = 451, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_451_RED_POINT,parent = {"ToyHouseMapHUD"}}
setting_short_red_point.NewYearGuideDailyReward = {activityId = 451, isDefineId = true,parent = {"NewYearGuide"} }
setting_short_red_point.NewYearGuideTask = {activityId = 451, isDefineId = true,parent = {"NewYearGuide"}}
setting_short_red_point.NewYearGuideTaskTab1 = {activityId = 451, isDefineId = true,parent = {"NewYearGuideTask"}}
setting_short_red_point.NewYearGuideTaskTab2 = {activityId = 451, isDefineId = true,parent = {"NewYearGuideTask"}}
setting_short_red_point.NewYearGuideTaskTab3 = {activityId = 451, isDefineId = true,parent = {"NewYearGuideTask"}}
setting_short_red_point.NewYearGuideTaskTab4 = {activityId = 451, isDefineId = true,parent = {"NewYearGuideTask"}}
setting_short_red_point.NewYearGuideFirstClick = {activityId = 451, isDefineId = true, isFirstClickRP = true,parent = {"NewYearGuide"}}

setting_short_red_point.activity439 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_439_RED_POINT, activityId = 439, isDefineId = true}
setting_short_red_point.activity439Arena = {isEveryWeekRP = true, activityId = 439, isDefineId = true}

-- setting_short_red_point.gotoTaskPanel = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_206_GENERIC_ACT_MAP, isDefineId = true}

setting_short_red_point.Activity2285 = {activityId = 2285, isFirstClickRP = true, parent={"MiddleActivity"}, isDefineId = false}

-- 3.7.2兑换商店
 setting_short_red_point.Shop2279Tab = {parent = {"MiddleActivity"}, isFirstClickRP = true, activityId = 2279, isDefineId = false}


setting_short_red_point.ActivitySeaHome = {activityId = 2286, isDefineId = false}
setting_short_red_point.ActivitySeaHomeBuild = {parent = {"ActivitySeaHome"},activityId = 2286, isDefineId = false}
setting_short_red_point.ActivitySeaHomeTechTreeTab = {parent = {"ActivitySeaHomeBuild"}, activityId = 2286, isDefineId = false}
setting_short_red_point.ActivitySeaHomeOrderTab = {parent = {"ActivitySeaHomeBuild"}, activityId = 2286, isDefineId = false}
setting_short_red_point.ActivitySeaHomeIslandTab = {parent = {"ActivitySeaHomeBuild"}, activityId = 2286, isDefineId = false}

setting_short_red_point.ActivitySeaHomeTask = {parent = {"ActivitySeaHome"},activityId = 2286, isDefineId = false}
setting_short_red_point.ActivitySeaHomeDayTask = {activityId = 2286, isDefineId = false,parent = {"ActivitySeaHomeTask"}}
setting_short_red_point.ActivitySeaHomeWeekTask = {activityId = 2286, isDefineId = false,parent = {"ActivitySeaHomeTask"}}

setting_short_red_point.ActivityWelfare = {activityId = 454, isDefineId = true}
setting_short_red_point.ActivityWelfareTab_1 = {parent = {"ActivityWelfare"}}
setting_short_red_point.ActivityWelfareTab_2 = {parent = {"ActivityWelfare"}}
setting_short_red_point.ActivityWelfareTab_3 = {parent = {"ActivityWelfare"}}
setting_short_red_point.ActivityWelfareTab_4 = {parent = {"ActivityWelfare"}}

setting_short_red_point.RandomDress = {isFirstClickRP = true}

setting_short_red_point.activity453 = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_453_RED_POINT, activityId = 453, isDefineId = true, isFirstClickRP = true}
setting_short_red_point.ActShareStreetTask_Tab_1 = {activityId = 2491, isDefineId = false, parent = {"activity453"}}
setting_short_red_point.ActShareStreetTask_Tab_2 = {activityId = 2491, isDefineId = false, parent = {"activity453"}}

setting_short_red_point.activity455 = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_455_RED_POINT, activityId = 455, isDefineId = true, isFirstClickRP = true}
setting_short_red_point.activity471 = {parent = {"ToyHouseMapHUD"}, key = GameEnum.RedPointTypeEnum.ACTIVITY_471_RED_POINT, activityId = 471, isDefineId = true, isFirstClickRP = true}

setting_short_red_point.ArtShowFirstClick = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = 2364, isDefineId = false}

setting_short_red_point.activity456 = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_456,key = GameEnum.RedPointTypeEnum.ACTIVITY_456_RED_POINT, isDefineId = true}
setting_short_red_point.activity456_Tab_1 = {parent = {"activity456"}, activityId = GameEnum.ActivityEnum.ACTIVITY_456, isDefineId = true}
setting_short_red_point.activity456_Tab_2 = {parent = {"activity456"}, activityId = GameEnum.ActivityEnum.ACTIVITY_456, isDefineId = true}

setting_short_red_point.Activity459RedPoint = {isEveryWeekRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_459, isDefineId = true}
setting_short_red_point.Activity457RedPoint = {isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_457, isDefineId = true}
setting_short_red_point.Activity465RedPoint = {isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_465, isDefineId = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_465_RED_POINT}

setting_short_red_point.Activity470TaskRedPoint = {isFirstClickRP = true, activityId = 470, isDefineId = true}

setting_short_red_point.FirstRecharge = {isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_464_FIRST_RECHARGE_V4, isDefineId = true}
-- 街区推广
setting_short_red_point.streetPromotion = {isFirstClickRP = true, activityId = GameEnum.ActivityEnum.ACTIVITY_181_APPLE_OFFLINE_INVITATION, isDefineId = true}

setting_short_red_point.Activity462RedPoint = {isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_462_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_462, isDefineId = true}

setting_short_red_point.Activity469RedPoint = {isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_469_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_469, isDefineId = true}

setting_short_red_point.Activity463RedPoint = {isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_463_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_463, isDefineId = true}
-- 周年签到板（留言）
setting_short_red_point.Activity466RedPoint = {isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_466_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_466, isDefineId = true}

setting_short_red_point.Act467RedPoint = {parent = {"ToyHouseMapHUD"}, isFirstClickRP = true, key = GameEnum.RedPointTypeEnum.ACTIVITY_467_RED_POINT, activityId = GameEnum.ActivityEnum.ACTIVITY_467, isDefineId = true}

return setting_short_red_point
