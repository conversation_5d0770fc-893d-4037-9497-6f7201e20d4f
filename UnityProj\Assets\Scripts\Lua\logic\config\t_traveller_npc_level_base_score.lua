-- {excel:D岛民卡配置表.xlsx, sheetName:export_npc好感度等级基础分}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_traveller_npc_level_base_score", package.seeall)

local title = {level=1,baseScore=2}

local dataList = {
	{1, 0},
	{2, 40},
	{3, 80},
	{4, 120},
	{5, 160},
	{6, 200},
	{7, 250},
	{8, 300},
	{9, 350},
	{10, 400},
}

local t_traveller_npc_level_base_score = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
	[9] = dataList[9],
	[10] = dataList[10],
}

t_traveller_npc_level_base_score.dataList = dataList
local mt
if TravellerNpcLevelBaseScoreDefine then
	mt = {
		__cname =  "TravellerNpcLevelBaseScoreDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or TravellerNpcLevelBaseScoreDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_traveller_npc_level_base_score