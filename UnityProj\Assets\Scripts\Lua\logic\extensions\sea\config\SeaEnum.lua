module("logic.extensions.sea.config.SeaEnum", package.seeall)

local SeaEnum = {}

-- 地形枚举
SeaEnum.Terrain = {
    Main = 1,
    Normal = 2
}

-- 换房方式枚举
SeaEnum.Way = {
    Lift = 1,
    Door = 2
}

-- 方向枚举
SeaEnum.Dir = {
    Up = 1,
    Down = 2,
    Left = 3,
    Right = 4
}

-- 鱼类枚举
SeaEnum.Fish = {
    Com = 1,
    King = 2,
    SpCom = 3,
    SpKing = 4
}

-- 武器枚举
SeaEnum.Weapon = {
    Gun1 = 1,
    Gun2 = 2
}

-- 操作枚举
SeaEnum.Input = {
    Key = 1,
    FreeBox = 2,
    Pick = 3,
    Upward = 4,
    Embrace = 5,
    Plot = 6
}

-- 动作枚举
SeaEnum.Ani = {
    Aim = 1,
    Fire = 2,
    Qte = 3,
    Suc = 4,
    Fail = 5,
    Mingxiang = 6
}

-- 发射方式枚举
SeaEnum.Strategy = {
    Line = "直线",
    Angle = "扇形分散",
    Circle = "圆周分散",
    LineUp = "直线并列",
    LinePlace = "直线放置",
    CirclePlace = "圆周放置",
    CircleRandom = "圆内随机放置"
}

-- 角色状态枚举
SeaEnum.State = {
    Speed = 14000772
}

-- 角色Buff枚举
SeaEnum.Buff = {
    Bullet = 1, -- 制作子弹
    TreasureCoins = 2, -- 宝箱掉落金币
    SpeedBuffUp = 3, -- 加速道具移动速度增加
    FirstFishingWeight = 4, -- 第一层捕鱼重量提升
    Qte = 5, -- 缩短捕鱼请求间隔
    FirstFishingExp = 6, -- 第一层捕鱼重量提升
    SeaOrderCoins = 7, -- 订单金币加成
    weaponLevel = 8, -- 武器等级提升
    SpeedBuffTimeUp = 9, -- 加速道具持续时间
    TreasureBullet = 10, -- 宝箱掉落子弹
    SecondFishingWeight = 11, -- 第二层捕鱼重量提升
    FishingCoins = 12, -- 捕鱼金币增加
    DailyTreasure = 13, -- 每日开启宝箱上限
    CatchCommonFishDrops = 14, -- 捕普通鱼掉落
    CatchDragonFishDrops = 15, -- 捕龙鱼掉落
    CatchShinyFishDrops = 16, -- 捕闪光鱼掉落
    ThirdFishingWeight = 17, -- 第三层捕鱼重量提升
    SeaOrderExp = 18, -- 订单经验加成
    FourthFishingWeight = 19 -- 第四层捕鱼重量提升
}

SeaEnum.TechTreeValueType = {
    Add = 1,
    Percentage = 2,
    Replacement = 3,
    WorkshopUnlockLevel = 4,
    QTE = 5
}

SeaEnum.MountType = {
    Sea = 1,
    Land = 2
}

return SeaEnum
