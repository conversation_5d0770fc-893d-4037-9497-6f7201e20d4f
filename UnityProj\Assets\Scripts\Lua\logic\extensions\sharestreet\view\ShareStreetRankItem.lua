module("logic.extensions.sharestreet.view.ShareStreetRankItem", package.seeall)

local ShareStreetRankItem = class("ShareStreetRankItem", ListBinderCell, BaseLuaComponent)

function ShareStreetRankItem:Awake()
    self._txtAreaInfo = self:getText("formulateGo/countGo/txt_1")
    self._txtVisitCount = self:getText("formulateGo/countGo/txt_2")
    self._txtLikeCount = self:getText("formulateGo/countGo/txt_3")

    self._goHeadIcon = self:getGo("headicon")
    self._goStreetPic = self:getGo("mask")

    self._txtStreetName = self:getText("streetName/nameTxt")
    self._txtRoleName = self:getText("nameGo/nameTxt")

    self._goStreetIcon = self:getGo("streetName/icon")

    self._goVip = self:getGo("nameGo/icon/img_1")
    self._goNotVip = self:getGo("nameGo/icon/img_2")

    self._btnGo = Framework.ButtonAdapter.Get(self._go)
    self._btnGo:AddClickListener(self._onClickGo, self)
end

function ShareStreetRankItem:onSetMo(mo)
    self._mo = mo
    self._roleInfo = mo.roleInfo
    self._streetInfo = mo.no

    HeadPortraitModel.instance:refreshHeadInfo(self._roleInfo)
    HeadPortraitHelper.instance:clearHeadIcon(self._goHeadIcon)
    HeadPortraitHelper.instance:setHeadPortraitWithUserId(self._goHeadIcon, self._roleInfo.id)
    self._txtRoleName.text = self._roleInfo.nickname
    self._txtStreetName.text = self._streetInfo.name
    self._txtAreaInfo.text = string.format("%s/%s", self._streetInfo.memberCount, self._streetInfo.areaCount)
    self._txtVisitCount.text = self._streetInfo.visitCount
    self._txtLikeCount.text = self._streetInfo.likeCount

    self._goVip:SetActive(self._roleInfo.vipCardType > 0)
    self._goNotVip:SetActive(self._roleInfo.vipCardType <= 0)

    ShareStreetUtil.setPic(self._goStreetPic, self._streetInfo.picture, 311, 176)
end

function ShareStreetRankItem:_onClickGo()
    ShareStreetFacade.instance:showDetailView(self._roleInfo.id)
end

return ShareStreetRankItem
