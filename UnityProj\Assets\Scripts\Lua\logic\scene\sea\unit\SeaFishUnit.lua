module("logic.scene.sea.unit.SeaFishUnit", package.seeall)

local SeaFishUnit = class("SeaFishUnit", SeaUnitBase)

function SeaFishUnit:defineStateMachine()
    return {
        {
            stateType = "swim",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("idle")
                end
            }
        },
        {
            stateType = "escape",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("move")
                end
            }
        },
        {
            stateType = "hide",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self:onStopFight()
                    self.go:SetActive(false)
                    self.trigger:setTriggerEnable(false)
                end
            }
        },
        {
            stateType = "fight",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    local fishTypeDefine = SeaConfig.getFishType(self.cfg.type)
                    SoundManager.instance:playEffect(SeaSound.FishFight, self.go)
                    SceneTimer:setTimer(fishTypeDefine.fightTime, self.onFight, self, false)
                    self.go:SetActive(true)
                    self.trigger:setTriggerEnable(true)
                    self.animator:Play("fight")
                    self:setDir(1, 0)
                end
            }
        },
        {
            stateType = "leave",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.go:SetActive(true)
                    self.trigger:setTriggerEnable(false)
                    self.behaviour:doEscape(SceneManager.instance:getCurScene():getUserPlayer())
                end
            }
        },
        {
            stateType = "reuse",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.go:SetActive(true)
                    self.trigger:setTriggerEnable(true)
                    self.behaviour:doEscape(SceneManager.instance:getCurScene():getUserPlayer())
                end
            }
        },
        {
            stateType = "remove",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    SceneManager.instance:getCurScene().nestMgr:reduceFish(self.cfg.id)
                    self:onDisappear()
                end
            }
        }
    }
end

function SeaFishUnit:defineFirstState()
    return "swim"
end

function SeaFishUnit:onReady()
    local go = goutil.findChild(self.view:getInst(), "go")
    self.mesh = go.transform:GetChild(0).gameObject
    self.animator = go:GetComponent("Animator")
    SeaFishUnit.super.onReady(self)

    self.trigger:setTriggerEnable(true)
    self:setAutoRotationEnable(false)

    local scale = GameUtils.getLocalScale(go)
    local collider = self.trigger:getCollider()
    local center = collider.center
    local size = collider.size
    local offset = (center.y + size.y * 0.5) * scale.y
    tfutil.SetAnchoredY(self.nameBar.go, offset * 60)
end

function SeaFishUnit:onReset()
    SeaFishUnit.super.onReset(self)
    if self._tween then
        self._tween:Kill()
        self._tween = nil
    end
    self:onStopFight()
    self:onSetAlpha(1)
    self:setActId(nil)
end

function SeaFishUnit:onUpdate()
    if self:isMoving() then
        SeaFishUnit.super.onUpdate(self)
    end
end

function SeaFishUnit:_initComponents()
    SeaFishUnit.super._initComponents(self)
    self:_addComponent("followUIComp", UnitCompFollowUI)
    self:_addComponent("nameBarContainer", SeaNameBarContainerComp)
    self:_addComponent("nameBar", SeaFishUnitCompNameBar)
    self:_addComponent("mover", SeaMoverComp)
    self:_addComponent("speeder", SeaSpeederComp)
    self:_addComponent("behaviour", SeaFishBehaviourComp)
    self:_addComponent("blendShape", SeaFishBlendShapeComp)
end

function SeaFishUnit:isHasPathFinding()
    return false
end

function SeaFishUnit:isMoving()
    return self:isState("swim") or self:isState("escape") or self:isState("leave") or self:isState("reuse")
end

function SeaFishUnit:isLive()
    return self:isState("swim") or self:isState("escape") or self:isState("fight") or self:isState("reuse")
end

function SeaFishUnit:onSetInfo()
    self.cfg = SeaConfig.getFish(self.info.fishId)
    self:setView(string.format(SeaResPath.Prefab, "fish/" .. self.cfg.prefab))
end

function SeaFishUnit:onSetDir()
    self.go.transform.rotation = Quaternion.LookRotation(gvec3(self._dirX, self._dirY, 0))
end

function SeaFishUnit:onFish(bulletUnit, success)
    bulletUnit:setResult(function()
        if success then
            SeaController.instance:seaSceneFishAwardRequest(self.cfg.id, true, handlerWithParams(self.onAward, self, {true}))
        else
            local player = SceneManager.instance:getCurScene():getUserPlayer()
            player.seaAniComp:fail()
            self:onAward(false)
        end
    end)
end

function SeaFishUnit:onAward(success)
    if success then
        SeaController.instance:localNotify(SeaNotify.onFishSucc, self)
        SoundManager.instance:playEffect(SeaSound.FishSucc)
        self:transition("remove")
    else
        SeaController.instance:localNotify(SeaNotify.onFishFail, self)
        SoundManager.instance:playEffect(SeaSound.FishFail)
        self:transition("fight")
    end
end

function SeaFishUnit:onFight()
    local fishTypeDefine = SeaConfig.getFishType(self.cfg.type)
    SoundManager.instance:stopEffect(SeaSound.FishFight, self.go)
    if self.actId == 441 then
        self:transition("reuse")
    else
        self:transition("leave")
        self._tween = DOTweenHelper.UpdateValue(1, 0, fishTypeDefine.disappearTime, self.onSetAlpha, self)
        self._tween:OnComplete(function()
            self:transition("remove")
        end)
    end
end

function SeaFishUnit:onStopFight()
    SoundManager.instance:stopEffect(SeaSound.FishFight, self.go)
    SceneTimer:removeTimer(self.onFight, self)
end

function SeaFishUnit:onSetAlpha(value)
    if self.mesh then
        PjAobi.CSGameUtils.SetMaterialBlock(self.mesh, "_Color", Color.New(1, 1, 1, value))
        PjAobi.CSGameUtils.SetMaterialBlock(self.mesh, "_Cutoff", 1 - value)
    end
end

function SeaFishUnit:onTriggerUpdate(source, name, unit, collider)
    if name == "SeaBulletUnit" then
        if source == "enter" then
            local fishTypeDefine = SeaConfig.getFishType(self.cfg.type)
            local qte = fishTypeDefine.qte
            local fishId = self.cfg.id
            local bulletId = unit.cfg.id
            local bulletUnitId = unit.id
            SeaController.instance:seaSceneFishRequest(fishId, bulletId, self.actId, function(success)
                if success and qte then
                    if qte.game == 1 then
                        ViewMgr.instance:open("SeaQte1View", {
                            qte = qte,
                            fishId = fishId,
                            bulletId = bulletId,
                            bulletUnitId = bulletUnitId,
                            callback = handlerWithParams(self.onFish, self, {unit})
                        })
                    elseif qte.game == 2 then
                        ViewMgr.instance:open("SeaQte2View", {
                            qte = qte,
                            fishId = fishId,
                            bulletId = bulletId,
                            bulletUnitId = bulletUnitId,
                            callback = handlerWithParams(self.onFish, self, {unit})
                        })
                    end
                else
                    self:onFish(unit, success)
                end
            end)
            self:transition("hide")
        end
    else
        if collider.tag == "Ground" then
            if source == "enter" then
                self.behaviour:doReverse()
            end
        end
    end
end

function SeaFishUnit:setActId(actId)
    self.actId = actId
end

function SeaFishUnit:updatePercentage()
    local playerInfo = SeaModel.instance:getPlayerInfo()
    local weaponId = playerInfo.weaponLevel
    local bulletId = SeaController.instance:getBulletId()
    local weaponDefine = SeaConfig.getWeapon(weaponId)
    local bulletDefine = SeaConfig.getBullet(bulletId)
    local percentage = self.cfg.baseRatio * weaponDefine.ratio * bulletDefine.ratio
    local mul = BuffService.instance:calculateResultValue(BuffType.AllType.SeaFishingRatioIncrease, 100) / 100
    percentage = percentage * mul
    if Activity438Controller.instance:isHasTask() then
        local onceFishId = Activity438Model.instance:getOnceFishId()
        if onceFishId == self.cfg.id then
            local addRatio = CommonConfig.getInt("act_438_common", "addRatio")
            percentage = percentage * addRatio
        end
    end
    if self.actId == 441 then
        local mul = Activity441Model.instance:getFishNum()
        percentage = percentage * 2 ^ mul
        if mul <= 0 then
            mul = nil
        end
        self.nameBar:setMul(mul)
    else
        self.nameBar:setMul(nil)
    end
    if percentage < 100 then
        local showScale = SeaConfig.FishPercentageShowScale
        percentage = math.ceil(percentage * showScale * 100)
    end
    percentage = math.min(percentage, 100)
    self.nameBar:setPercentage(percentage .. "%")
end

return SeaFishUnit
