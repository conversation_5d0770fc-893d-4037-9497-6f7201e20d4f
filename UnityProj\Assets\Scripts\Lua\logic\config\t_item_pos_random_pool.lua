-- {excel:D岛民事件系统-跑图找物.xlsx, sheetName:export_跑图找物随机配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_item_pos_random_pool", package.seeall)

local title = {uniqueId=1,sceneId=2,index=3,pos=4}

local dataList = {
	{1, 3, 1, {4.22,0.71}},
	{2, 3, 2, {-10.12,-1.66}},
	{3, 3, 3, {3.67,7.67}},
	{4, 3, 4, {-12.02,-9.75}},
	{5, 3, 5, {6.82,-10.55}},
	{6, 3, 6, {-7.56,-6.46}},
}

local t_item_pos_random_pool = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
}

t_item_pos_random_pool.dataList = dataList
local mt
if ItemPosRandomPoolDefine then
	mt = {
		__cname =  "ItemPosRandomPoolDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or ItemPosRandomPoolDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_item_pos_random_pool