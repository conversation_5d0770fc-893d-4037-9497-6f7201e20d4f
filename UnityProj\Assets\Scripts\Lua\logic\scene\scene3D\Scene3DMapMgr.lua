module("logic.scene.scene3D.Scene3DMapMgr",package.seeall)
local Scene3DMapMgr = class("Scene3DMapMgr", SceneComponentBase)

function Scene3DMapMgr:onInit()
	
end


function Scene3DMapMgr:onEnterSceneFinished(sceneId,bornX,bornZ)

end

function Scene3DMapMgr:isWalkable(x, y, z)
	if not z then
		z = CurveSurface.Instance:GetPosYOnGround(x, y)
	end
	return CurveSurface.Instance:IsWalkable(x, z, y)
end

function Scene3DMapMgr:isDirectWalkable(unit, endX, endY)
	local go
	if unit.posCtrl and unit.posCtrl:getMount() then
		go = unit.posCtrl:getMount().go
	else
		go = unit.go
	end
	local found = CurveSurface.Instance:IsDirectMove(go, endX,endY)
	return found
end

function Scene3DMapMgr:getNearestWalkable(unit, endX, endY)
	local go
	if unit.posCtrl and unit.posCtrl:getMount() then
		go = unit.posCtrl:getMount().go
	else
		go = unit.go
	end
	local found,canX,canZ = CurveSurface.Instance:GetNearestWalkable(go, endX,endY,0,0)
	return found,canX,canZ
end

function Scene3DMapMgr:findPath(unit, descX, descY, descZ, targetRange)
	targetRange = targetRange or 0
	if not descZ then
		descZ = CurveSurface.Instance:GetPosYOnGround(descX, descY)
		-- printInfo("calc z", descZ)
	end
	local wayPoints = {}
	local find = false
	local go
	if unit.posCtrl and unit.posCtrl:getMount() then
		go = unit.posCtrl:getMount().go
	else
		go = unit.go
	end
	local points = CurveSurface.Instance:GetFindWayPointListNonAlloc(go, descX, descZ, descY)
	if points then
		local length = points.Length-1
		local p = points[length]
		if math.abs(p.x - descX) < 0.1 and math.abs(p.z - descY) < 0.1 then
			find = true
			for i=0,length do
				p = points[i]		
				wayPoints[i+1] = {posX=p.x, posY=p.z}
			end
			if targetRange > 0 then
				while #wayPoints > 1 do
					local lastIndex = #wayPoints
					local dx = wayPoints[lastIndex].posX - wayPoints[lastIndex-1].posX
					local dy = wayPoints[lastIndex].posY - wayPoints[lastIndex-1].posY
					local dis = math.sqrt(dx*dx + dy*dy)
					if targetRange >= dis then
						targetRange = targetRange - dis
						table.remove(wayPoints, lastIndex)
					else
						local pos = {}
						pos.posX = (dis - targetRange) / dis * dx + wayPoints[lastIndex-1].posX
						pos.posY = (dis - targetRange) / dis * dy + wayPoints[lastIndex-1].posY
						wayPoints[lastIndex] = pos
						break
					end
				end
			end
		end

	end
	return wayPoints, find 
end

function Scene3DMapMgr:findPath_Vector3(unit, descX, descY, descZ, targetRange)
	targetRange = targetRange or 0
	local wayPoints = {}
	if not descZ then
		descZ = GameUtils.GetPosYOnGround(descX, descY)
		-- printInfo("calc z", descZ)
	end
	local find = false
	local go
	if unit.posCtrl and unit.posCtrl:getMount() then
		go = unit.posCtrl:getMount().go
	else
		go = unit.go
	end
	local points = CurveSurface.Instance:GetFindWayPointListNonAlloc(go, descX, descZ, descY)
	if points then
		local length = points.Length-1
		local p = points[length]
		if math.abs(p.x - descX) < 0.1 and math.abs(p.z - descY) < 0.1 and math.abs(p.y - descZ) < 0.3 then
			find = true
			for i=0,length do
				p = points[i]
				wayPoints[i+1] = {posX=p.x, posY=p.z,posZ=p.y + 0.1}
			end
			if targetRange > 0 then
				while #wayPoints > 1 do
					local lastIndex = #wayPoints
					local dx = wayPoints[lastIndex].posX - wayPoints[lastIndex-1].posX
					local dy = wayPoints[lastIndex].posY - wayPoints[lastIndex-1].posY
					local dis = math.sqrt(dx*dx + dy*dy)
					if targetRange >= dis then
						targetRange = targetRange - dis
						table.remove(wayPoints, lastIndex)
					else
						local pos = {}
						local t = (dis - targetRange) / dis
						pos.posX = t * dx + wayPoints[lastIndex-1].posX
						pos.posY = t * dy + wayPoints[lastIndex-1].posY
						pos.posZ = t * wayPoints[lastIndex].posZ + (1-t) * wayPoints[lastIndex-1].posZ
						wayPoints[lastIndex] = pos
						break
					end
				end
			end
		end
	end
	return wayPoints, find
end


--function Scene3DMapMgr:getWalkDirectionPath_Vector3(unit, dirX, dirY, distance)
--	local go
--	if unit.posCtrl and unit.posCtrl:getMount() then
--		go = unit.posCtrl:getMount().go
--	else
--		go = unit.go
--	end
--	local points, count = CurveSurface.Instance:GetWalkDirectionPath(go, dirX, dirY, distance, 0)
--	local find = count > 0
--	local wayPoints
--	if find then
--		local px,py,pz = unit:getPos()
--		if pz then
--			pz =  CurveSurface.Instance:GetPosYOnGroundMultilayer(px,py,pz)
--			printError(pz .. "== pz")
--		end
--		wayPoints = {{posX=px, posY=py,posZ = pz}}
--		for i=0,count-1 do
--			local p = points[i]
--			p.y =  CurveSurface.Instance:GetPosYOnGroundMultilayer(p.x,p.z,p.y)
--			printError(p.y .. "== p.y")
--			--这里Y变化幅度很大的时候，还是会插地一点点
--			table.insert(wayPoints, {posX=p.x, posY=p.z,posZ = p.y + 0.1})
--			--table.insert(wayPoints, {posX=p.x,posY=p.z,posZ = pz})
--		end
--	end
--	return find, wayPoints
--end

function Scene3DMapMgr:getWalkDirectionPath_Vector3(unit, dirX, dirY, distance)
	local go
	if unit.posCtrl and unit.posCtrl:getMount() then
		go = unit.posCtrl:getMount().go
	else
		go = unit.go
	end
	local px,py,pz = unit:getPos()
	--local pos = GameUtils.getPos(go)
	local points = {}
	local count = 0
	if pz ~= nil then
		points = CurveSurface.Instance:GetFindWayPointListNonAllocDistance(go,px + dirX * distance,pz,py + dirY * distance,0.1)
		if points then
			count = points.Length
		else
			count = 0
		end
	else
		points, count = CurveSurface.Instance:GetWalkDirectionPath(go, dirX, dirY, distance, 0)
	end
	local find = count > 0
	local wayPoints
	if find then
		--if pz == nil then
		--	pz = points[0].y
		--end
		if pz then
			wayPoints = {{posX=px, posY=py,posZ=pz}}
		else
			wayPoints = {{posX=px, posY=py}}
		end
		--wayPoints = {}
		for i=0,count-1 do
			local p = points[i]
			table.insert(wayPoints, {posX=p.x, posY=p.z,posZ=p.y + 0.1})
		end
	end
	return find, wayPoints
end

function Scene3DMapMgr:getWalkDirectionPath(unit, dirX, dirY, distance)
	local go
	if unit.posCtrl and unit.posCtrl:getMount() then
		go = unit.posCtrl:getMount().go
	else
		go = unit.go
	end
	local points, count = CurveSurface.Instance:GetWalkDirectionPath(go, dirX, dirY, distance, 0)
	local find = count > 0
	local wayPoints
	if find then
		local px, py = unit:getPos()
		wayPoints = {{posX=px, posY=py}}
		for i=0,count-1 do
			local p = points[i]		
			table.insert(wayPoints, {posX=p.x, posY=p.z})
		end
	end
	return find, wayPoints
end



function Scene3DMapMgr:getWalkDirectionPathFly(unit, dirX, dirY, distance)
	local wayPoints
	local px, py = unit:getPos()
	local height = unit:getHeight()
	wayPoints = {{posX=px, posY=py,posZ=height}}
	local myPosition = GameUtils.getPos(unit.go)
	if unit.customSkin3D:getAngle() then
		local angle = unit.customSkin3D:getAngle() * 0.5
		if self.nextDir == nil then
			self.nextDir =	Vector3.New(0,0,0)
		end
		self.nextDir.x = dirX
		self.nextDir.z = dirY
		self.minOffsetY = GameUtils.GetPosYOnGround(px,py)
		local dir = Vector3.Dot(angle,self.nextDir)
		if angle then
			self.forward,self.hitInfo2 = UnityEngine.Physics.Linecast(myPosition,myPosition + angle,hitInfo2)
			if self.forward == false or dir < 0 or
					self.hitInfo2.collider.gameObject.tag ~= "Ground" or self.hitInfo2.collider.gameObject.name == "ab-s139_sceneheight"
			then
				if unit.isUser == true then
					if height < self.minOffsetY then
						height = self.minOffsetY
					end
					table.insert(wayPoints, {posX=px + dirX * distance, posY=py + dirY * distance,posZ = height})
				else
					height = height + height * 0.3
					if height < self.minOffsetY then
						height = self.minOffsetY
					end
					table.insert(wayPoints, {posX=px + dirX * distance, posY=py + dirY * distance,posZ = height})
				end
			else
				--printError( self.hitInfo2.collider.gameObject.name .. "==name")
			end
		end
		return true, wayPoints
	end
end

function Scene3DMapMgr:getSceneWidth()
	return 0
end

function Scene3DMapMgr:getSceneHeight()
	return 0
end

function Scene3DMapMgr:onExitScene()
end

return Scene3DMapMgr