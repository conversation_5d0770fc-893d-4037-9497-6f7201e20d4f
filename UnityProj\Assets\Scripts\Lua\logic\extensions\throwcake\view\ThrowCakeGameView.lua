module("logic.extensions.throwcake.view.ThrowCakeGameView", package.seeall)
local ThrowCakeGameView = class("ThrowCakeGameView", ViewComponent)


local showCakeEffect = 141711
local stackSucEffect = 141709
local stackLoseEffect = 141710
local winEffectId = 141423
local loseEffectId = 141424
local throwEffect = 141708

--- view初始化时会执行
function ThrowCakeGameView:buildUI()
    self._btnClose = self:getBtn("btnReturn")
    self._levelInfo = self:getGo("levelInfo")
    self._scoreInfo = self:getGo("scoreInfo")

    self._timeGo = self:getGo("timeGo")
    self._goCountDown1Left = self:getGo("timeGo/goCountDown1Left")
    self.normalTimeText = self:getText("timeGo/goCountDown1Left/txtCountDownNomalLeft")
    self._goCountDown2Left = self:getGo("timeGo/goCountDown2Left")
    self.redTimeText = self:getText("timeGo/goCountDown2Left/txtCountDownExigenceLeft")

    self._goStart = self:getGo("goStart")
    self._goComplete = self:getGo("goComplete")
    self._goVictory = self:getGo("goVictory")
    self._goGameOver = self:getGo("goGameOver")
    self._Button = self:getBtn("Button")

    self._goTips = self:getGo("goTips")
    self._goWaitLeft = self:getGo("leftCloud/goWaitLeft")
    self._goPositionControlBarLeft = self:getGo("goPositionControlBarLeft")
    self._goWaitRight = self:getGo("rightCloud/goWaitRight")

    self._posLeft = self:getGo("goPositionControlBarLeft/posLeft")

    self._txtLevel = self:getText("levelInfo/txtLevel")
    self._txtLayer = self:getText("levelInfo/txtLayer")
    self.hpGos = {}
    self.hpLimitGo = {}
    for i = 1, 5, 1 do
        self.hpLimitGo[i] = self:getGo("scoreInfo/hp/hp" .. i)
        self.hpGos[i] = self:getGo("scoreInfo/hp/hp" .. i .. "/gohp")
    end
    self._score = self:getText("scoreInfo/txtScore")

    self._goFaceLeft = TalkComp_UI.New(self:getGo("leftCloud/player/goFaceLeft"))
    self._goFaceRight = TalkComp_UI.New(self:getGo("rightCloud/partner/goFaceRight"))

    self.EvaluateGo = self:getGo("goEvaluateInfo")
    self._txtAddCount = self:getText("goEvaluateInfo/txtAddCount")
    self.evaluateTexts = {}
    for i = 1, 4 do
        self.evaluateTexts[i] = self:getGo("goEvaluateInfo/" .. i)
    end

    self.playerGo = self:getGo("leftCloud")
    self.partnerGo = self:getGo("rightCloud")

    self._goWindDir = self:getGo("goWindDir")
    self._txtSpeed = self:getText("goWindDir/txtSpeed")
    self._leftDir = self:getGo("goWindDir/imgDir1")
    self._rightDir = self:getGo("goWindDir/imgDir2")
    self._windEffectGo = self:getGo("goWindDir/windEffect")
    self.windEffects = {}
    for i = 1, 3, 1 do
        self.windEffects[i] = self:getGo("goWindDir/windEffect/" .. i)
    end

    self._txtName1 = self:getText("leftCloud/txtName")
    self._txtName2  = self:getText("rightCloud/txtName ")
end
--- view初始化时会执行，在buildUI之后
function ThrowCakeGameView:bindEvents()
    self._btnClose:AddClickListener(self._onClickClose, self)
    self._Button:AddClickListener(self._onClickThrow, self)
    self:registerLocalNotify(ThrowCakeNotify.SetLevelInfo, self.setLevelInfo, self)
    self:registerLocalNotify(ThrowCakeNotify.SetScoreInfo, self.setScoreAndHp, self)
    self:registerLocalNotify(ThrowCakeNotify.GameOver, self.onGameOver, self)
    self:registerLocalNotify(ThrowCakeNotify.PlayEmoji, self.playEmojiAndAddScore, self)
    self:registerLocalNotify(ThrowCakeNotify.UpdateInputTips, self.updateInputTips, self)
    self:registerLocalNotify(ThrowCakeNotify.UpdateTimeUI, self.updateTimeUI, self)
    self:registerLocalNotify(ThrowCakeNotify.PlayPlayerAnim, self.playPlayerAnim, self)
end

--- view销毁时会执行，在destroyUI之前
function ThrowCakeGameView:unbindEvents()
    self:unregisterLocalNotify(ThrowCakeNotify.SetLevelInfo, self.setLevelInfo, self)
    self:unregisterLocalNotify(ThrowCakeNotify.SetScoreInfo, self.setScoreAndHp, self)
    self:unregisterLocalNotify(ThrowCakeNotify.GameOver, self.onGameOver, self)
    self:unregisterLocalNotify(ThrowCakeNotify.PlayEmoji, self.playEmojiAndAddScore, self)
    self:unregisterLocalNotify(ThrowCakeNotify.UpdateInputTips, self.updateInputTips, self)
    self:unregisterLocalNotify(ThrowCakeNotify.UpdateTimeUI, self.updateTimeUI, self)
    self:unregisterLocalNotify(ThrowCakeNotify.PlayPlayerAnim, self.playPlayerAnim, self)
end

function ThrowCakeGameView:_onClickClose()
    DialogHelper.showConfirmDlg(lang("天才蛋糕师中途退出提示"), function(isOk)
        if not isOk then
            return
        end
        ThrowCakeController.instance:quitGame()
    end)
end

function ThrowCakeGameView:_onClickThrow()
    self.valid = true
end

function ThrowCakeGameView:updatePower()
    self.curPower = self.curPower + Time.deltaTime * self.powerSpeed * self.powerDir
    if self.curPower > 1 or self.curPower < 0 then
        self.powerDir = self.powerDir * -1
        if self.curPower > 1 then
            self.curPower = 0.99
        else
            self.curPower = 0.01
        end
    end
    local X = (self.curPower * 140) - 70
    tfutil.SetAnchoredX(self._posLeft, X)
    local input = {
        speed = self.curPower,
        valid = self.valid
    }
    ThrowCakeModel.instance:setInput(input)
    self.valid = false
end

function ThrowCakeGameView:initView()
    goutil.setActive(self._btnClose.gameObject, self.isSingle)
    goutil.setActive(self._goPositionControlBarLeft, false)
    goutil.setActive(self._timeGo, false)
    goutil.setActive(self._goTips, true)
    goutil.setActive(self._scoreInfo, false)
    goutil.setActive(self._levelInfo, false)
    goutil.setActive(self._goVictory, false)
    goutil.setActive(self._goComplete, false)
    goutil.setActive(self._goGameOver, false)
    goutil.setActive(self._goWindDir, false)
    tfutil.SetScale(self.EvaluateGo, 0)
    self:loadPlayer()
    goutil.setActive(self.partnerGo, not self.isSingle)
    self._txtName1.text = UserInfo.nickname
    if self.partnerInfo then
        self:loadPartner() 
        self._txtName2.text = self.partnerInfo.name
    end
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ThrowCakeGameView:onEnter()
    self.bgmId = 120058
    SoundManager.instance:playBGM(self.bgmId)
    self:hideStartGame()
    settimer(3, self.showStartGame, self, false)
    settimer(4, self.hideStartGame, self, false)
    self.isSingle = self:getOpenParam()[1]
    self.partnerInfo = self:getOpenParam()[2]
    self:initView()
    ThrowCakeController.instance:tryStartGame(self.mainGO)
    self.curPower = 0
    self.powerDir = 1
    self.powerSpeed = 1
    UpdateBeat:Add(self.updatePower, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ThrowCakeGameView:onExit()
    SoundManager.instance:replayBGM()
    if self._tweener then
        self._tweener:Kill()
        self._tweener = nil
    end
    UpdateBeat:Remove(self.updatePower, self)
    removetimer(self._onGameOver, self)
    removetimer(self.showStartGame, self)
    removetimer(self.hideStartGame, self)
    LoadingMask.instance:close()
    if self.partnerModel then
        self.partnerElfunit:dispose()
        self.partnerModel:dispose()
        self.partnerElfunit = nil
        self.partnerModel = nil
    end
    self.playerModel:dispose()
    self.playerElfunit:dispose()
    self.playerModel = nil
    self.playerElfunit = nil
end

function ThrowCakeGameView:showStartGame()
    goutil.setActive(self._goStart, true)
end
function ThrowCakeGameView:hideStartGame()
    goutil.setActive(self._goStart, false)
end

function ThrowCakeGameView:loadPlayer()
    local dir = UnitDirection.RightDown
    local elfSkinName = "p1_3"
    self.playerModel = ModelPhotoBase.New(self:getGo("leftCloud/player"), 2, ModelPhotoBase.Type_MainPlayer)
    local avatarView = self.playerModel:getAvatarView()
    avatarView:setIdlePoseEnable(true)
    avatarView:setDirection(dir)
    self.playerElfunit = GameUtils.createUIElfView(self:getGo("leftCloud/elf"), true, dir, elfSkinName)
    ThrowCakeAgent.instance:sendGetAct252RoleAndElfInfoRequest(UserInfo.userId, handler(self.onGetPlayerElfInfo, self))
end

function ThrowCakeGameView:onGetPlayerElfInfo(msg)
    local elfoinfo = msg.elfoInfo
    local hasElf = elfoinfo and elfoinfo.state > 0
    if hasElf then
        self.playerElfunit:setInfo({
            skinName = ElfConfig.getElfDefine(elfoinfo.elfId).skinName
        })
    end
end

function ThrowCakeGameView:loadPartner()
    local playerId = self.partnerInfo.id
    local avatarInfo = self.partnerInfo.avatarInfo
    local dir = UnitDirection.LeftDown
    -- local playerId = UserInfo.userId
    -- local unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
    -- local avatarInfo = unitMgr:getUnit(SceneUnitType.AnniversaryGalaUnit, playerId).avatar
    local elfSkinName = "p1_3"
    self.partnerModel = ModelPhotoBase.New(self:getGo("rightCloud/partner"), 2, ModelPhotoBase.Type_Player)
    local avatarView = self.partnerModel:getAvatarView()
    avatarView:isShowCardView(false)
    avatarView:setIdlePoseEnable(true)
    avatarView:setDirection(dir)
    self.partnerModel:setClothes(2, avatarInfo)
    self.partnerElfunit = GameUtils.createUIElfView(self:getGo("rightCloud/elf"), true, dir, elfSkinName)
    ThrowCakeAgent.instance:sendGetAct252RoleAndElfInfoRequest(playerId, handler(self.onGetPartnerElfInfo, self))
end

function ThrowCakeGameView:onGetPartnerElfInfo(msg)
    local elfoinfo = msg.elfoInfo
    local hasElf = elfoinfo and elfoinfo.state > 0
    if hasElf then
        self.partnerElfunit:setInfo({
            skinName = ElfConfig.getElfDefine(elfoinfo.elfId).skinName
        })
    end
end
function ThrowCakeGameView:setLevelInfo(info)
    goutil.setActive(self._timeGo, true)
    self:setWind(info)
    LoadingMask.instance:close()
    self.isNewRoundMask = true
    self.isNewShowCake = true
    if info.maxLevel == -1 then
        self._txtLevel.text = lang("第{1}关", info.level)
    else
        self._txtLevel.text = lang("第<color=#c96841>{1}</color>/{2}关", info.level, info.maxLevel)
    end
    self._txtLayer.text = lang("层数:<color=#c96841>{1}</color>/{2}", info.layer, info.maxLayer)
    self.powerSpeed = info.powerSpeed
end

function ThrowCakeGameView:setWind(info)
    goutil.setActive(self._goWindDir, true)
    self._txtSpeed.text = lang("{1}M/S", info.windStrength)
    goutil.setActive(self._leftDir, info.isLeftWind)
    goutil.setActive(self._rightDir, not info.isLeftWind)
    if info.windStrength == 0 then
        goutil.setActive(self._windEffectGo, false)
    else
        goutil.setActive(self._windEffectGo, true)
        local Ry = info.isLeftWind and 0 or 180
        tfutil.SetRotationY(self._windEffectGo, Ry)
        local strengthLevel = math.ceil(info.windStrength / 5)
        if strengthLevel > 3 then
            strengthLevel = 3
        end
        for i = 1, 3, 1 do
            goutil.setActive(self.windEffects[i], i == strengthLevel)
        end
    end

end

function ThrowCakeGameView:setScoreAndHp(info)
    self._score.text = info.score
    self._txtLayer.text = lang("层数:<color=#c96841>{1}</color>/{2}", info.layer, info.maxLayer)
    for i, v in ipairs(self.hpGos) do
        goutil.setActive(self.hpLimitGo[i], i <= info.maxHp)
        goutil.setActive(v, i <= info.hp)
    end
end

function ThrowCakeGameView:playEmojiAndAddScore(info)
    local soundEffectids = {
       [1] = 141177,
       [2] = 141176,
       [3] = 141175,
    }
    SoundManager.instance:playEffect(info.isSuc and stackSucEffect or stackLoseEffect)
    local emojiCfg = ThrowCakeConfig.getEmojiCfg()
    local id = info.isSuc and info.evaluateLevel or 5
    local emoji = tonumber(emojiCfg[id])
    if info.isSelf then 
        self._goFaceLeft:showSceneChat(emoji, true)
    else
        self._goFaceRight:showSceneChat(emoji, true)
    end
    if info.score then
        tfutil.SetScale(self.EvaluateGo, 0)
        self._txtAddCount.text = lang("+{1}", info.score)
        print(info.evaluateLevel)
        for i = 1, 4 do
            goutil.setActive(self.evaluateTexts[i], info.evaluateLevel == i)
        end
        if self._tweener then
            self._tweener:Kill()
            self._tweener = nil
        end
        if soundEffectids[info.evaluateLevel] then 
            SoundManager.instance:playEffect(soundEffectids[info.evaluateLevel])
        end
        self._tweener = DG.Tweening.DOTween.Sequence()
        self._tweener:Append(self.EvaluateGo.transform:DOScale(1.1, 0.2))
        self._tweener:Append(self.EvaluateGo.transform:DOScale(1.3, 0.2))
        self._tweener:Append(self.EvaluateGo.transform:DOScale(0, 0.2))
    end

end

function ThrowCakeGameView:onGameOver(info)
    SoundManager.instance:playEffect(info.isWin and winEffectId or loseEffectId)
    goutil.setActive(self._goComplete, false)
    goutil.setActive(self._goVictory, info.isWin)
    goutil.setActive(self._goGameOver, not info.isWin)
    settimer(1, self._onGameOver, self, false)
end

function ThrowCakeGameView:updateInputTips(info)
    goutil.setActive(self._goTips, info.showInputTips)
    goutil.setActive(self._goWaitLeft, info.showLeftWait)
    goutil.setActive(self._goWaitRight, info.showRightWait)
    goutil.setActive(self._goPositionControlBarLeft, info.showLeftControlBar)
end

function ThrowCakeGameView:updateTimeUI(info)
    goutil.setActive(self._goComplete, info.isShowCake)
    if info.isShowCake and self.isNewShowCake then
        self.isNewShowCake = false
        SoundManager.instance:playEffect(showCakeEffect)
    end
    goutil.setActive(self._scoreInfo, true)
    goutil.setActive(self._levelInfo, true)
    local leftPos = {
        x = -450,
        y = 100
    }
    local rightPos = {
        x = 450,
        y = 100
    }
    local maxRoundTime = ThrowCakeConfig.getMaxRoundTime()
    if info.isSingle then
        goutil.setActive(self._timeGo, false)
        return
    else
        local pos = info.isSelfRound and leftPos or rightPos
        tfutil.SetAnchoredPos(self._timeGo, pos.x, pos.y)

        goutil.setActive(self._goCountDown1Left, not info.showRed)
        goutil.setActive(self._goCountDown2Left, info.showRed)
        if info.timeOver then
            self:tryShowMask()
        end
        local remainTime = math.ceil(maxRoundTime - info.curTime)
        if remainTime < 1 then
            remainTime = 0
        end
        if info.showRed then
            self.redTimeText.text = remainTime .. "s"
        else
            self.normalTimeText.text = remainTime .. "s"
        end
    end
end

function ThrowCakeGameView:tryShowMask()
    if self.isNewRoundMask then
        LoadingMask.instance:show()
        self.isNewRoundMask = false
    end
end

function ThrowCakeGameView:playPlayerAnim(info)
    local _, throwName = ThrowCakeConfig.getThrowAnimName()
    if info.elfAnimName == throwName then
        SoundManager.instance:playEffect(throwEffect)
        LoadingMask.instance:close()
        self.isNewRoundMask = false
        goutil.setActive(self._timeGo, false)
    end
    local player = info.isSelf and self.playerModel or self.partnerModel
    local avatar = player:getAvatarView()
    local elf = info.isSelf and self.playerElfunit or self.partnerElfunit
    print(info.isLoop)
    if info.elfAnimName then
        elf:playAnimation(info.elfAnimName, info.isLoop, true)
    end
    if info.playerAnimName then
        avatar:playAnimation(info.playerAnimName, info.isLoop, true)
    end
end

function ThrowCakeGameView:_onGameOver()
    ThrowCakeController.instance:quitGame()
end

return ThrowCakeGameView
