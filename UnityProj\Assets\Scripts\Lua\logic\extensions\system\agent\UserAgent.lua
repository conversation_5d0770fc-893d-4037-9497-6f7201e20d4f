--auto gen by editor
module("logic.extensions.system.agent.UserAgent",package.seeall)

local UserAgent = class("UserAgent",BaseAgent)

--协议请求来源
UserAgent.SendSource = 
{
	None = 0,
	InviteNewbie = 1, -- 专属拉新活动
}

function UserAgent:ctor()
	UserAgent.super.ctor(self)
end

function UserAgent:sendGetRoleInfoRequest(handler)
    local req = UserExtension_pb.GetRoleInfoRequest()
    self:setClientInfo(req.clientInfo)
	req.clientVsion = BootstrapUtil.getGameHotUpdateVersionCode()
	printInfo("client info", tostring(req))

	self.getRoleInfoHandler = handler
    self:sendMsg(req)
    if LoginService.instance.isSDKLogin and (string.nilorempty(req.clientInfo.channel) or 
		(Framework.OSDef.RunOS == Framework.OSDef.Android and string.nilorempty(req.clientInfo.media))) then
		printError("channel or media is null! " .. req.clientInfo.channel .. req.clientInfo.media)
	end

end

function UserAgent:setClientInfo(clientInfo)
    local info = SDKManager.getSDKInstacne():getDevInfo(2)
    if info then
		clientInfo.terminal = tostring(info.terminal)
		clientInfo.imsi = tostring(info.imsi)
		clientInfo.phoneModel = tostring(info.phoneModel)
		clientInfo.phoneSysVersion = tostring(info.phoneSysVersion)
		clientInfo.densityDpi = tostring(info.densityDpi)
		clientInfo.displayScreenWidth = tostring(info.displayScreenWidth)
		clientInfo.displayScreenHeight = tostring(info.displayScreenHeight)
		clientInfo.networkInfo = tostring(info.networkInfo)
		clientInfo.gameVersionCode = string.format("%s-%s", BootstrapUtil.getGameVersionName(), BootstrapUtil.getGameHotUpdateVersionCode())
		clientInfo.platformIdSecond = tostring(SDKManager.getSDKInstacne():getChannelId())
		clientInfo.sdkType = tostring(info.sdkType)

		clientInfo.androidId = tostring(info.androidId)	
		-- req.clientInfo.serialno = tostring(info.serialno)	
		clientInfo.oaid = tostring(info.oaid)	
		clientInfo.imei = tostring(info.imei)			
		clientInfo.osVer = tostring(info.osVer)
		clientInfo.terminInfo = tostring(info.terminInfo)
		clientInfo.mac = tostring(info.mac)
		clientInfo.ip = tostring(info.ip)
		clientInfo.ydDeviceId = tostring(info.ydDeviceId)
	


		-- info = SDKManager.getSDKInstacne():getDevInfo()

		-- if info then
		-- 	req.clientInfo.sdkVersion = tostring(info.sdkVersion)
		-- end
	end
	clientInfo.gameCode = tostring(UserInfo.accountInfo.game)
	local ext = SDKManager.getSDKInstacne():getDevJson(5)
	clientInfo.extend = ext	
	clientInfo.media = tostring(SDKManager.getSDKInstacne():getMedia())
	clientInfo.channel = tostring(UserInfo.accountInfo.channelNo or "")
end

function UserAgent:handleGetRoleInfoReply(status, msg)
	self.getRoleInfoHandler(status, msg)
	self.getRoleInfoHandler = nil
end

function UserAgent:sendCreateRoleRequest(sex,nickName,indexList, modelId, handler)
    local req = UserExtension_pb.CreateRoleRequest()
	req.sex = sex
	req.nickName = nickName
	req.modelId = modelId
	for i=1,#indexList do
		req.clothesIndex:append(indexList[i])
	end
	self.createRoleHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleCreateRoleReply(status, msg)
	if self.createRoleHandler then
		self.createRoleHandler(status, msg)
	end
	self.createRoleHandler = nil
end

function UserAgent:startHeartBeat()
	self.curTime = 0
	UpdateBeat:Add(self.update, self)
	-- Scheduler.addListener(10, self.sendHeartBeatRequest, self, true)
end

function UserAgent:stopHeartBeat()
	UpdateBeat:Remove(self.update, self)
end

function UserAgent:update()
	self.curTime = self.curTime + Time.deltaTime
	if self.curTime > 30 then
		self.curTime = 0
		self:sendHeartBeatRequest()
	end
end

function UserAgent:sendHeartBeatRequest()
    local req = UserExtension_pb.HeartBeatRequest()
	self.requestTime = Time.realtimeSinceStartup
    self:sendMsg(req)
end

function UserAgent:handleHeartBeatReply(status, msg)
	local delay = math.floor((Time.realtimeSinceStartup - self.requestTime) * 1000 / 2)
	printInfo("delay:", delay)
	SceneManager.instance:setSceneTimeDelay(delay)	
end

function UserAgent:sendSyncServerTimeRequest()
    local req = UserExtension_pb.SyncServerTimeRequest()
	-- self.syncServerTimeHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleSyncServerTimeReply(status, msg)
    if status == 0 then
		ServerTime.init(math.ceil(msg.timeOffset / 1000))
		ServerTime.sync(msg.serverTime)

		-- GameUtils.initServerTimeStamp(msg.serverRunningMillis + delay)
		-- GlobalDispatcher:dispatch(GlobalNotify.OnGetRoleFinish)
    else
		DialogHelper.showErrorMsg(status)
	end
	-- self.syncServerTimeHandler = nil
end

function UserAgent:sendModifyInformationCardRequest(k, v, handler)
    local req = UserExtension_pb.ModifyInformationCardRequest()
	if GameUtils.isTable(v) then
		for i=1, #v do
			req.InformationCard[k]:append(v[i])
		end
	else
		req.InformationCard[k] = v
	end
	self.modifyInformationCardHandler = handler
    self:sendMsg(req)
end

function UserAgent:sendModifyMultiInformationCardRequest(k, v, handler)
	local req = UserExtension_pb.ModifyInformationCardRequest()
	for i=1, #k do
		req.InformationCard[k[i]] = v[i]
	end
	self.modifyInformationCardHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleModifyInformationCardReply(status, msg)
	if self.modifyInformationCardHandler then
		self.modifyInformationCardHandler(status, msg)
	end
	UserInfo.InfomationCard = msg.infomationCard
	self.modifyInformationCardHandler = nil
end

function UserAgent:sendModifyNickNameRequest(useItem,nickName,handler)
	LoadingMask.instance:show()
    local req = UserExtension_pb.ModifyNickNameRequest()
	req.useItem = useItem
	req.nickName = nickName
	self.modifyNickNameHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleModifyNickNameReply(status, msg)
	LoadingMask.instance:close()
	self.modifyNickNameHandler(status)
	self.modifyNickNameHandler = nil
end

--sendType:传输类型
function UserAgent:sendGetRoleInformationCardRequest(userId,handler,sendSource)
    local req = UserExtension_pb.GetRoleInformationCardRequest()
	req.userId = userId
	self.getInfoSendSource = sendSource
	self.getRoleInformationCardHandler = handler
	if self.getRoleInformationCardQueue == nil then
		self.getRoleInformationCardQueue = CallbackQueue.New()
	end
	self.getRoleInformationCardQueue:add(handler)

    self:sendMsg(req)
end

function UserAgent:handleGetRoleInformationCardReply(status, msg)
    if status == 0 then
		self.getRoleInformationCardQueue:invoke(msg)
    else
    	LoadingMask.instance:close()
		if self.getInfoSendSource then
			self.getRoleInformationCardHandler(nil,self.getInfoSendSource)
		else
			DialogHelper.showErrorMsg(status)
		end
	end
	self.getInfoSendSource = nil
	self.getRoleInformationCardHandler = nil
end

function UserAgent:handleRoleLevelOrExpChangePush(status, msg)
    if status == 0 then
		UserInfo.updateLevelInfo(msg)
	end
end

--同一时间多个调用--
function UserAgent:sendSelectRoleInfoByUserIdRequest(userId,handler)
    local req = UserExtension_pb.SelectRoleInfoByUserIdRequest()
    for i=1, #userId do
		table.insert(req.userId, userId[i])
	end
	if self.selectRoleInfoByUserIdHandler == nil then
		self.selectRoleInfoByUserIdHandler = {}
	end
	table.insert(self.selectRoleInfoByUserIdHandler, handler)
	printInfo("sdfsdf", self.selectRoleInfoByUserIdHandler)
    self:sendMsg(req)
end

function UserAgent:handleSelectRoleInfoByUserIdReply(status, msg)
	local callBack = table.remove(self.selectRoleInfoByUserIdHandler, 1)
    if status == 0 then
		local roleInfo = msg.roleInfo
		callBack(roleInfo)
    else
		callBack({})
		-- DialogHelper.showErrorMsg(status)
	end
end

function UserAgent:sendChangeTitleRequest(titleId,handler)
    local req = UserExtension_pb.ChangeTitleRequest()
	req.titleId = titleId
	self.changeTitleHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleChangeTitleReply(status, msg)
	if status == 0 then
		if self.changeTitleHandler then
			self.changeTitleHandler()
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.changeTitleHandler = nil
end

function UserAgent:sendLogoutRequest()
    local req = UserExtension_pb.LogoutRequest()
	-- self.logoutHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleLogoutReply(status, msg)
    if status == 0 then
    else
		DialogHelper.showErrorMsg(status)
	end
	-- self.logoutHandler = nil
end

function UserAgent:sendRealNameFinishRequest(handler)
    local req = UserExtension_pb.RealNameFinishRequest()
	self.realNameFinishHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleRealNameFinishReply(status, msg)
    if status == 0 then
		self.realNameFinishHandler(msg.realNameInfo)
    else
		DialogHelper.showErrorMsg(status)
	end
	self.realNameFinishHandler = nil
end

function UserAgent:sendReportPlatformInfoRequest()
    local req = UserExtension_pb.ReportPlatformInfoRequest()
    self:setClientInfo(req.clientInfo)
	printInfo("client info", tostring(req))
	self:sendMsg(req)
end



function UserAgent:handleReportPlatformInfoReply(status, msg)

end


function UserAgent:sendClientReportRequest(evtId,clientReportInfo)
    local req = UserExtension_pb.ClientReportRequest()
	req.evtId = evtId
	local item
	if clientReportInfo then
		for k,v in pairs(clientReportInfo) do
			item = req.clientReportInfo:add()
			item.key = k
			item.value = tostring(v)
		end
	end
    self:sendMsg(req)
end



function UserAgent:handleClientReportReply(status, msg)
    if status == 0 then
    else
		DialogHelper.showErrorMsg(status)
	end
end



function UserAgent:sendFeedbackRequest(type,content,imageIds,handler)
    local req = UserExtension_pb.FeedbackRequest()
	req.type = type
	req.content = content
	for i,v in ipairs(imageIds) do
		table.insert(req.imageId,v)
	end
	self.feedbackHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleFeedbackReply(status, msg)
    if status == 0 then
    	if self.feedbackHandler then
			self.feedbackHandler()
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.feedbackHandler = nil
end

function UserAgent:sendExchangeActiveCodeRequest(code,handler)
    local req = UserExtension_pb.ExchangeActiveCodeRequest()
	req.code = code
	self.exchangeActiveCodeHandler = handler
	self:sendMsg(req)
	ViewBlockMgr.instance:blockClick(true, "sendExchangeActiveCode")
end

function UserAgent:handleExchangeActiveCodeReply(status, msg)
	ViewBlockMgr.instance:blockClick(false, "sendExchangeActiveCode")
    if status == 0 then
		self.exchangeActiveCodeHandler()
	elseif status == 103 then
		FlyTextManager.instance:showFlyText("兑换失败，该序号已经过期")
	elseif status == 104 then
		FlyTextManager.instance:showFlyText("兑换失败，该序号不存在")
	elseif status == 106 then
		FlyTextManager.instance:showFlyText("兑换失败，该序号兑换已达上限")
    else
		DialogHelper.showErrorMsg(status)
	end
	self.exchangeActiveCodeHandler = nil
end

function UserAgent:handleServerRefreshPush(status, msg)
	ServerTime.init(math.ceil(msg.timeOffset / 1000))
	ServerTime.sync(msg.serverTime)
	if LoginService.instance.isLogined then
		GlobalDispatcher:dispatch(GlobalNotify.OnServerRefresh)
	end
end

function UserAgent:handleServerShutdownPush(status, msg)
	local tipCodeId = msg.tipCodeId
	self.leftSecond = msg.leftSecond
	local codeText = DialogHelper.getErrorMsg(tipCodeId)
	self.tipText = codeText or "还有%d秒就要停服了，您尽快保存现在的操作，以防丢失"
	DialogHelper.showMsg(string.format(codeText, self.leftSecond))
	settimer(1, self._shutdownPrepareHandler, self)
end

function UserAgent:_shutdownPrepareHandler()
	self.leftSecond = self.leftSecond - 1
	if self.leftSecond == 10 then
		FlyTextManager.instance:showFlyText(string.format(self.tipText, self.leftSecond))
	elseif self.leftSecond == 5 then
		FlyTextManager.instance:showFlyText(string.format(self.tipText, self.leftSecond))
	elseif self.leftSecond <= 0 then
		removetimer(self._shutdownPrepareHandler, self)
	end
end

function UserAgent:sendChangeHeadWindowsRequest(headWindows,handler)
    local req = UserExtension_pb.ChangeHeadWindowsRequest()
	req.headWindows = headWindows
	self.changeHeadWindowsHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleChangeHeadWindowsReply(status, msg)
    if status == 0 then
		if self.changeHeadWindowsHandler  then
			self.changeHeadWindowsHandler()
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.changeHeadWindowsHandler = nil
end

function UserAgent:sendGetAllHeadWindowsRequest(handler)
	local req = UserExtension_pb.GetAllHeadWindowsRequst()
	self.getAllHeadWindowsHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleGetAllHeadWindowsReply(status, msg)
    if status == 0 then
		local headwindows = msg.headWindows
		if self.getAllHeadWindowsHandler then
			self.getAllHeadWindowsHandler(headwindows)
		end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.getAllHeadWindowsHandler = nil
end

function UserAgent:sendGetUserIsOnlineRequest(uId,handler)
    local req = UserExtension_pb.GetUserIsOnlineRequest()
	req.uId = uId
	if self.getUserIsOnlineHandler == nil then
		self.getUserIsOnlineHandler = {}
	end
	table.insert(self.getUserIsOnlineHandler, handler)
    self:sendMsg(req)
end

function UserAgent:handleGetUserIsOnlineReply(status, msg)
	local callBack = table.remove(self.getUserIsOnlineHandler, 1)
    if status == 0 then
		local isonline = msg.isOnline
		callBack(isonline)
    else
		DialogHelper.showErrorMsg(status)
	end
end

function UserAgent:sendReportUserRequest(beReportUId, reportType, content, reportId, speakTime, extend, handler)
    local req = UserExtension_pb.ReportUserRequest()
	req.beReportUId = beReportUId
	req.type = reportType
	req.reportId = reportId
	req.content = content
	if speakTime then
		req.speakTime = speakTime
	end
	if extend then
		req.extend = extend
	end
	self.reportUserHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleReportUserReply(status, msg)
	self.reportUserHandler(status)
	self.reportUserHandler = nil
end



function UserAgent:handleForbidSpeakPush(status, msg)
    if status == 0 then
		UserInfo.forbidSpeakCause = msg.forbidSpeakCause
    else
		DialogHelper.showErrorMsg(status)
	end
	self.forbidSpeakHandler = nil
end

function UserAgent:handleGMCloseSystemPush(status, msg)
    if status == 0 then
		FuncModel.instance:setCloseSystem(msg.closeSystemEnum)
	end
end

--1_41群岛种族选择
function UserAgent:sendChooseIslandRacialRequest(islandRacialId,handler)
	LoadingMask.instance:show()
    local req = UserExtension_pb.ChooseIslandRacialRequest()
	req.islandRacialId = islandRacialId
	self.chooseIslandRacialHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleChooseIslandRacialReply(status, msg)
	LoadingMask.instance:close()
    if status == 0 then
		self.chooseIslandRacialHandler()
    else
		DialogHelper.showErrorMsg(status)
	end
	self.chooseIslandRacialHandler = nil
end

--1_42群岛人数信息查询
function UserAgent:sendGetIslandRacialInfoRequest(handler)
    local req = UserExtension_pb.GetIslandRacialInfoRequest()
	self.getIslandRacialInfoHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleGetIslandRacialInfoReply(status, msg)
    if status == 0 then
		self.getIslandRacialInfoHandler(msg)
    else
		DialogHelper.showErrorMsg(status)
	end
	self.getIslandRacialInfoHandler = nil
end

function UserAgent:sendCheckHasSurveyRequest(handler)
    local req = UserExtension_pb.CheckHasSurveyRequest()
	self.checkHasSurveyHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleCheckHasSurveyReply(status, msg)
    if status == 0 then
		local sign = msg.sign
		self.checkHasSurveyHandler(sign)
	end
	self.checkHasSurveyHandler = nil
end

function UserAgent:sendModifySexRequest(sex,handler)
	LoadingMask.instance:show()
    local req = UserExtension_pb.ModifySexRequest()
	req.sex = sex
	self.modifySexHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleModifySexReply(status, msg)
	LoadingMask.instance:close()
    if status == 0 then
		UserInfo.sex = msg.sex
		self.modifySexHandler(msg.sex)
    else
		DialogHelper.showErrorMsg(status)
	end
	self.modifySexHandler = nil
end

function UserAgent:sendGetUserSimpleInfoCardRequest(userId,handler)
    local req = UserExtension_pb.GetUserSimpleInfoCardRequest()
	req.userId = userId
	self.getUserSimpleInfoCardHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleGetUserSimpleInfoCardReply(status, msg)
    if status == 0 then
		local isonline = msg.isOnline
		local councilname = msg.councilName
		local headicon = msg.headIcon
		self.getUserSimpleInfoCardHandler(isonline, councilname, headicon)
    else
		DialogHelper.showErrorMsg(status)
	end
	self.getUserSimpleInfoCardHandler = nil
end

function UserAgent:sendCheckContentSensitiveTextRequest(type,content,handler)
	LoadingMask.instance:show()
    local req = UserExtension_pb.CheckContentSensitiveTextRequest()
	req.type = type
	req.content = content
	self.sendCheckContentSensitiveTextRequestType=type
	self.checkContentSensitiveTextHandler = handler
    self:sendMsg(req)
end

function UserAgent:handleCheckContentSensitiveTextReply(status, msg)
	LoadingMask.instance:close()
	if self.sendCheckContentSensitiveTextRequestType == GameEnum.ThunderXFReportType.ACT_140_FAREWELL_LETTER then
		self.checkContentSensitiveTextHandler(status,msg)
	else
		if status == 0 then
			local sensitive = msg.sensitive
			if sensitive then
				FlyTextManager.instance:showFlyText(lang("输入内容含有敏感内容"))
			else
				self.checkContentSensitiveTextHandler()
			end
		else
			DialogHelper.showErrorMsg(status)
		end
	end
	self.checkContentSensitiveTextHandler = nil
end

function UserAgent:sendFinishHappyBirthdayPartyRequest()
    local req = UserExtension_pb.FinishHappyBirthdayPartyRequest()
    self:sendMsg(req)
end

function UserAgent:handleFinishHappyBirthdayPartyReply(status, msg)
end

function UserAgent:handleClientHotVersionPush(status, msg)
    if status == 0 then
		if not GameUtils.isLocalVersionNew(msg.version) then
			LoginService.instance:forceHotUpdate()
		end
	end
end

UserAgent.instance = UserAgent.New()

return UserAgent