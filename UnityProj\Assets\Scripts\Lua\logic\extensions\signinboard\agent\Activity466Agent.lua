module("logic.extensions.signinboard.agent.Activity466Agent",package.seeall)

local Activity466Agent = class("Activity466Agent", BaseAgent)

function Activity466Agent:sendGetAct466InfoRequest(handler)
    LoadingMask.instance:show()
    local req = Activity466Extension_pb.GetAct466InfoRequest()
	self.getAct466InfoHandler = handler
    self:sendMsg(req)
end

function Activity466Agent:handleGetAct466InfoReply(status, msg)
    LoadingMask.instance:close()
    if status == 0 then
		local curglobalprogress = msg.curGlobalProgress
		local selfmessage = msg.selfMessage
		local backgroundindex = msg.backgroundIndex
		local gainedglobalprogressrewardids = msg.gainedGlobalProgressRewardIds
        if self.getAct466InfoHandler then
            self.getAct466InfoHandler(curglobalprogress, selfmessage, backgroundindex, gainedglobalprogressrewardids)
        end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.getAct466InfoHandler = nil
end

function Activity466Agent:sendGainAct466ProgressRewardRequest(handler)
    LoadingMask.instance:show()
    local req = Activity466Extension_pb.GainAct466ProgressRewardRequest()
	self.gainAct466ProgressRewardHandler = handler
    self:sendMsg(req)
end

function Activity466Agent:handleGainAct466ProgressRewardReply(status, msg)
    LoadingMask.instance:close()
    if status == 0 then
		local changesetid = msg.changeSetId
        if self.gainAct466ProgressRewardHandler then
            self.gainAct466ProgressRewardHandler(changesetid)
        end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.gainAct466ProgressRewardHandler = nil
end

function Activity466Agent:sendAct466LeaveMessageRequest(message,backgroundIndex,handler)
    LoadingMask.instance:show()
    local req = Activity466Extension_pb.Act466LeaveMessageRequest()
	req.message = message
    req.backgroundIndex = backgroundIndex
	self.act466LeaveMessageHandler = handler
    self:sendMsg(req)
end

function Activity466Agent:handleAct466LeaveMessageReply(status, msg)
    LoadingMask.instance:close()
    if status == 0 then
		local changesetid = msg.changeSetId
        if self.act466LeaveMessageHandler then
            self.act466LeaveMessageHandler(changesetid)
        end
    else
		DialogHelper.showErrorMsg(status)
	end
	self.act466LeaveMessageHandler = nil
end

Activity466Agent.instance = Activity466Agent.New()
return Activity466Agent