-- {excel:F服装配置.xlsx, sheetName:export_套装}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_suit", package.seeall)

local title = {id=1,name=2,quality=3,desc=4,tags=5}

local dataList = {
	{11000004, "阳光学弟套装", 3, "新入学的第一天，期待与你的相遇。", {15,11}},
	{11000005, "可爱学妹套装", 3, "新入学的第一天，会遇见谁呢？", {15,11}},
	{11000010, "猫咪绅士套装", 5, "遥远的过去，有一位猫咪绅士。想听听吗，他的故事？", {17,5}},
	{11000013, "活泼休闲套装", 2, "我要成为夏天最耀眼的色彩。", {1}},
	{11000014, "时尚休闲套装", 2, "我要成为夏天最明亮的色彩。", {1}},
	{11000019, "贝斯手套装", 4, "双人组合中的贝斯手，演奏梦境的人。", {15,17}},
	{11000020, "偶像歌手套装", 4, "双人组合中的主唱，梦是我的陷阱。", {15,17}},
	{11000021, "猫咪女士套装", 5, "美丽的女士，你永葆青春，无所不能，还有什么烦恼？", {17,5}},
	{11000022, "运动学长套装", 4, "奥比学园，奥比学园，运动社开始招新啦！", {15,11}},
	{11000023, "酷酷学姐套装", 4, "奥比学园，奥比学园，运动社开始招新啦！", {15,11}},
	{11000024, "金色奥比套装", 3, "奥比节日传统服装，为纪念本岛的历史和祖先的模样。", {17}},
	{11000025, "工作服套装", 2, "一套绿色花纹的简单兜帽装。", {1}},
	{11000026, "奇妙先生套装", 5, "他在黑夜降临，带走价值连城的宝石与少女的心。", {15}},
	{11000027, "安全员小姐套装", 5, "有案件的地方，就会有她的身影，这一次也不会例外。", {15}},
	{11000028, "甜品师套装", 4, "甜品师的精髓，难道不是在于可以边做边吃？", {15}},
	{11000029, "服务生套装", 4, "没有客人是不能用一杯茶解决的，如果有，那就两杯。", {15}},
	{11000030, "失明的日神", 5, "但炽烈的爱与光芒永不熄灭。", {23}},
	{11000031, "温柔的月神", 5, "温柔的月亮，藏匿在夜幕之中。", {23}},
	{11000032, "兔子小姐套装", 2, "春天，兔子小姐出发寻找花朵和爱情。", {17}},
	{11000033, "糖果兔小姐套装", 3, "春天，兔子小姐出发寻找糖果和爱情。", {17}},
	{11000034, "黑巧兔小姐套装", 3, "春天，兔子小姐出发寻找巧克力和爱情。", {17}},
	{11000035, "童话兔小姐套装", 3, "春天，兔子小姐出发寻找童话和爱情。", {17}},
	{11000036, "狐狸先生套装", 2, "秋天，狐狸先生悄悄叼走枫叶和心。", {17}},
	{11000037, "棕狐先生套装", 3, "秋天，狐狸先生悄悄叼走栗子和心。", {17}},
	{11000038, "深蓝狐先生套装", 3, "秋天，狐狸先生悄悄叼走鼠尾草和心。", {17}},
	{11000039, "雪狐先生套装", 3, "冬天，狐狸先生悄悄把自己藏起。", {17}},
	{11000040, "奶糖咖啡礼服", 1, "要来一杯奶糖咖啡吗？", {3}},
	{11000041, "细碎星光礼服", 1, "星光在裙摆上闪耀。", {3}},
	{11000042, "女探险家套装", 2, "伊莎贝拉在路上，永远在路上。", {15}},
	{11000043, "男探险家套装", 2, "斐迪南的梦想，到未踏足的世界去。", {15}},
	{11000044, "客厅女仆套装", 2, "胜任客厅女仆并不容易，但她恰好端庄美丽又忧愁。", {15}},
	{11000045, "私人管家套装", 2, "训练有素的私人管家，整座庄园在他手中运转。", {15}},
	{11000046, "乐队主唱套装", 4, "怀揣着梦想，歌声可以到达任何地方。", {15,17}},
	{11000047, "乐队吉他手套装", 4, "音符都被他驯服。", {15,17}},
	{11000048, "名侦探少女套装", 3, "熟读侦探故事的少女，今天决定成为名侦探。", {15}},
	{11000049, "名侦探少年套装", 3, "熟读侦探故事的少年，今天决定成为名侦探。", {15}},
	{11000050, "星星魔女套装", 3, "偷走星星的魔女，将银河投入魔药锅。", {20}},
	{11000051, "星星创造师套装", 3, "行走在星夜的创造师，拥有神秘的靴子。", {20}},
	{11000052, "女探险家套装红", 3, "伊莎贝拉在路上，永远在路上。", {15}},
	{11000053, "女探险家套装黄", 3, "伊莎贝拉在路上，永远在路上。", {15}},
	{11000054, "女探险家套装蓝", 3, "伊莎贝拉在路上，永远在路上。", {15}},
	{11000055, "男探险家套装红", 3, "斐迪南的梦想，到未踏足的世界去。", {15}},
	{11000056, "男探险家套装黄", 3, "斐迪南的梦想，到未踏足的世界去。", {15}},
	{11000057, "男探险家套装蓝", 3, "斐迪南的梦想，到未踏足的世界去。", {15}},
	{11000058, "客厅女仆黑制服", 3, "胜任客厅女仆并不容易，但她恰好端庄美丽又忧愁。", {15}},
	{11000059, "客厅女仆蓝制服", 3, "胜任客厅女仆并不容易，但她恰好端庄美丽又忧愁。", {15}},
	{11000060, "客厅女仆紫制服", 3, "胜任客厅女仆并不容易，但她恰好端庄美丽又忧愁。", {15}},
	{11000061, "优雅管家套装", 3, "训练有素的私人管家，整座庄园在他手中运转。", {15}},
	{11000062, "权威管家套装", 3, "训练有素的私人管家，整座庄园在他手中运转。", {15}},
	{11000063, "悠然管家套装", 3, "训练有素的私人管家，整座庄园在他手中运转。", {15}},
	{11000064, "春日爱丽丝套装", 4, "和煦的春日，爱丽丝掉进了兔子洞。", {19,17}},
	{11000065, "春夜爱丽丝套装", 4, "宁谧的春夜，爱丽丝走过茂密的树丛。", {19,17}},
	{11000066, "紫色奥比套装", 3, "奥比节日传统服装，为纪念本岛的历史和祖先的模样。", {17}},
	{11000067, "幽冥来客套装", 5, "大炎国的怪谈故事中，贪玩的少女偶尔溜出冥狱。", {2,5}},
	{11000068, "天使轻奢套装", 5, "是独角兽的化身吗？", {17,12,3}},
	{11000069, "绯红魔女套装", 3, "偷走星星的绯红魔女，将银河投入魔药锅。", {20}},
	{11000070, "绯红魔术师套装", 3, "行走在星夜的绯红魔术师，拥有神秘的靴子。", {20}},
	{11000071, "深蓝魔女套装", 3, "偷走星星的深蓝魔女，将银河投入魔药锅。", {20}},
	{11000072, "深蓝魔术师套装", 3, "行走在星夜的深蓝创造师，拥有神秘的靴子。", {20}},
	{11000073, "爱神轻奢套装", 5, "是降临在人间的爱神。", {17,12,3}},
	{11000074, "浅玫魔女套装", 3, "偷走星星的浅玫魔女，将银河投入魔药锅。", {20}},
	{11000075, "浅玫魔术师套装", 3, "行走在星夜的浅玫创造师，拥有神秘的靴子。", {20}},
	{11000076, "侦探少女紫套装", 3, "熟读侦探故事的沉静少女，今天决定成为名侦探。", {15}},
	{11000077, "侦探少年紫套装", 3, "熟读侦探故事的沉静少年，今天决定成为名侦探。", {15}},
	{11000078, "侦探少女红套装", 3, "熟读侦探故事的热情少女，今天决定成为名侦探。", {15}},
	{11000079, "侦探少年红套装", 3, "熟读侦探故事的热情少年，今天决定成为名侦探。", {15}},
	{11000080, "侦探少女蓝套装", 3, "熟读侦探故事的聪明少女，今天决定成为名侦探。", {15}},
	{11000081, "侦探少年蓝套装", 3, "熟读侦探故事的聪明少年，今天决定成为名侦探。", {15}},
	{11000082, "妙手画家套装", 4, "笔下流转的是梦想与爱。", {15}},
	{11000083, "匠心画家套装", 4, "纸上跳跃的是爱与梦想。", {15}},
	{11000084, "迷你牛仔背带装", 3, "曾许的愿实现了吗？今天的你，比那时快乐吧。", {1}},
	{11000085, "活泼牛仔背带装", 3, "也许回不到过去，但今天的你是更好的你。", {1}},
	{11000086, "茉莉公主套装", 3, "美丽的茉莉公主，在窗台上等待着谁？", {3}},
	{11000087, "白马王子套装", 3, "骑着白马的王子，为心上人献上水晶鞋。", {3}},
	{11000088, "弗朗明戈男舞者", 4, "舞蹈吧！让最炽热的阳光也相形见绌。", {15}},
	{11000089, "弗朗明戈女舞者", 4, "舞蹈吧！让最热烈的玫瑰也黯淡无光。", {15}},
	{11000090, "马戏小丑套装", 5, "擦掉眼泪吧，下一个节目属于马戏小丑。", {15}},
	{11000091, "月光白玫瑰套装", 5, "月光下的白玫瑰，走入了尘世之中。", {26,3}},
	{11000092, "甜梦睡衣套装", 4, "昨夜睡得好吗？做了甜甜的梦。", {1,17}},
	{11000093, "安眠睡衣套装", 4, "慢慢入睡吧，祝你今晚安眠。", {1,17}},
	{11000094, "莓莓甜品套装", 4, "草莓甜点，今日新鲜售卖！", {28}},
	{11000095, "草莓点心套装", 4, "草莓点心，出售甜蜜味道！", {28}},
	{11000096, "气质公主套装", 4, "她的气质，犹如娇嫩欲滴的粉玫瑰。", {22,3}},
	{11000097, "优雅王子套装", 4, "他的优雅，犹如琴弦上跳跃的音符。", {22,3}},
	{11000098, "儒雅绅士套装", 5, "在舞会上，他遇见了心上人。", {3}},
	{11000099, "高贵淑女套装", 5, "在舞会上，她悄悄红了脸庞。", {3}},
	{11000100, "微风海岛套装", 4, "来一场海岛派对吧，我的水枪准备就绪！", {10,1,17}},
	{11000101, "晴空海岛套装", 4, "来一场海岛派对吧，享受阳光和海浪！", {10,1,17}},
	{11000102, "（废除）星空幻想套装", 5, "远道而来的小王子，邀你做一场关于星空的梦。", {3}},
	{11000103, "夏日休闲套装", 3, "奥比街拍，潮味十足。", {1}},
	{11000104, "秋冬温暖套装", 3, "冬日里的恋歌，是你红红的脸颊。", {1}},
	{11000105, "夏日活泼套装", 3, "奥比街拍，可爱十足。", {1}},
	{11000106, "秋冬温柔套装", 3, "冬日里的恋歌，是你温暖的手心。", {1}},
	{11000107, "春季制服套装", 3, "我的青春就是在春日的校园里漫步。", {15,11}},
	{11000108, "绿芽清新套装", 3, "奔跑在广阔田野上，向往自由。", {8}},
	{11000109, "潮流牛仔套装", 3, "追寻潮流，永不停息。", {1}},
	{11000110, "春樱制服套装", 3, "我的青春就是校园里飘落的春樱。", {15,11}},
	{11000111, "绿芽田园套装", 3, "栖息在林间荫下，寻觅宁静。", {8}},
	{11000112, "（废除）时尚牛仔套装", 3, "追寻时尚，永不停息。", {1}},
	{11000113, "传统马甲套装", 3, "奥比民族的传统服饰，穿上小马甲向前奔跑吧！", {10,1}},
	{11000114, "传统围裙套装", 3, "奥比民族的传统服饰，穿上小围裙向前奔跑吧！", {10,1}},
	{11000115, "简约休闲蓝套装", 2, "简单的蓝色背带裙套装，永远不会过时。", {1}},
	{11000116, "轻松休闲蓝套装", 2, "简单的蓝色背带裤套装，永远不会过时。", {1}},
	{11000117, "晨曦白玫瑰套装", 5, "晨露中的白玫瑰，走入了尘世之中。", {26,3}},
	{11000118, "春日小茶杯套装", 5, "来喝杯下午茶吧，做一场春日里的美梦。", {2}},
	{11000119, "春日小茶壶套装", 5, "春日里，赴一场愉快的茶话会。", {2}},
	{11000120, "忐忑心事套装", 2, "心脏怦怦跳，想快点来到你身边。", {12}},
	{11000121, "轻快心情套装", 2, "想变得可爱，等待你来到我身边。", {12}},
	{11000122, "传统草纹套装", 2, "绣着草纹图案的传统民族服饰。", {10,1}},
	{11000123, "传统花纹套装", 2, "绣着花纹图案的传统民族服饰。", {10,1}},
	{11000124, "秋日琥珀套装", 2, "你像琥珀一样柔情与天真。", {1}},
	{11000125, "秋日蜜糖套装", 2, "你像蜜糖一样可爱与纯洁。", {1}},
	{11000126, "骑士守护套装", 4, "骑士存在的意义是为了守护。", {3}},
	{11000127, "骑士信仰套装", 4, "骑士存在的意义是为了信仰。", {3}},
	{11000128, "甜蜜蛋糕公主装", 4, "我是甜蜜蛋糕公主，你有想念我吗？", {28}},
	{11000129, "甜蜜蛋糕王子装", 4, "我是甜蜜蛋糕王子，你还记得我吗？", {28,3}},
	{11000130, "快乐成长大树装", 3, "我是一棵大树，但我想去远方。", {26}},
	{11000131, "密林仙子套装", 5, "传说中，森林里有一位由花朵化身的仙子。", {23}},
	{11000132, "密林王子套装", 5, "传说中，森林里有一位与花朵为伴的王子。", {23,3}},
	{11000133, "领航员套装", 2, "助人为乐是领航员的义务。", {15}},
	{11000134, "（废除）欢乐庆典套装", 5, "跑快点，要赶不上庆典了！", nil},
	{11000135, "自由信使套装", 4, "将自由与希望带到你身边。", {15}},
	{11000136, "沉着信使套装", 4, "将快乐与幸福带到你身边。", {15}},
	{11000137, "田园恋歌套装", 3, "今天的天很蓝，与你共吟田园恋歌。", {8}},
	{11000138, "田园恋曲套装", 3, "今天的风很轻，与你共谱田园恋曲。", {8}},
	{11000139, "深海魅影套装", 5, "来自深海的女王，神秘且寂寞。", {26}},
	{11000140, "幻海魅影套装", 5, "来自幻海的公主，清澈且忧郁。", {26}},
	{11000141, "幽夜静谧套装", 4, "静谧的夜晚，总是如此神秘。", {5}},
	{11000142, "暗夜静谧套装", 4, "静谧的夜晚，却是如此忧伤。", {5}},
	{11000143, "奥比特使套装", 3, "是金发小卷毛的奥比特使哦。", {23}},
	{11000144, "热情夏威夷套装", 2, "阳光！热浪！来跳舞吧！", {10}},
	{11000145, "欢趣庆典套装", 5, "等等我，庆典可不能少了我！", {15,22}},
	{11000146, "明媚黑蔷薇套装", 4, "瑰丽也好，神秘也罢，每一朵花都有存在的意义。", {26}},
	{11000147, "清冽黑蔷薇套装", 4, "渺小也好，夺目也罢，每一朵花都有独特的意义。", {26}},
	{11000148, "田园恋歌黄套装", 3, "路上的花很美，与你共吟田园恋歌。", {8}},
	{11000149, "田园恋曲黄套装", 3, "今天的风很轻，与你共谱田园恋曲。", {8}},
	{11000150, "深蓝爱丽丝女装", 4, "融融的春日，爱丽丝掉进了兔子洞。", {19}},
	{11000151, "深蓝爱丽丝男装", 4, "静谧的春夜，爱丽丝走过茂密的树丛。", {19}},
	{11000152, "深绿爱丽丝女装", 4, "明媚的春日，爱丽丝掉进了兔子洞。", {19}},
	{11000153, "深绿爱丽丝男装", 4, "恬静的春夜，爱丽丝走过茂密的树丛。", {19}},
	{11000154, "浅紫爱丽丝女装", 4, "恬适的春日，爱丽丝掉进了兔子洞。", {19}},
	{11000155, "浅紫爱丽丝男装", 4, "清寂的春夜，爱丽丝走过茂密的树丛。", {19}},
	{11000156, "田园恋歌红套装", 3, "天边的夕阳很红，与你共吟田园恋歌。", {8}},
	{11000157, "田园恋曲红套装", 3, "今天的星星很亮，与你共谱田园恋曲。", {8}},
	{11000158, "田园恋歌蓝套装", 3, "远方的海水很蓝，与你共吟田园恋歌。", {8}},
	{11000159, "田园恋曲蓝套装", 3, "今天的月儿很弯，与你共谱田园恋曲。", {8}},
	{11000160, "幽夜静谧红套装", 3, "静谧的夜晚，总是如此苍凉。", {5}},
	{11000161, "暗夜静谧红套装", 3, "静谧的夜晚，却是如此忧愁。", {5}},
	{11000162, "幽夜静谧蓝套装", 3, "静谧的夜晚，总是如此孤寂。", {5}},
	{11000163, "暗夜静谧蓝套装", 3, "静谧的夜晚，却是如此凄然。", {5}},
	{11000164, "幽夜静谧黑套装", 3, "静谧的夜晚，总是如此奇妙。", {5}},
	{11000165, "暗夜静谧黑套装", 3, "静谧的夜晚，却是如此惆怅。", {5}},
	{11000166, "轻松休闲红套装", 2, "简单的红色背带裤套装，永远不会过时。", {1}},
	{11000167, "轻松休闲绿套装", 2, "简单的绿色背带裤套装，永远不会过时。", {1}},
	{11000168, "简约休闲红套装", 2, "简单的红色背带裙套装，永远不会过时。", {1}},
	{11000169, "简约休闲绿套装", 2, "简单的绿色背带裙套装，永远不会过时。", {1}},
	{11000170, "星空幻想套装", 5, "穿越时空而来的小王子，邀你做一场关于星空的梦。", {3}},
	{11000171, "星河漫游套装", 5, "跨越星河而来的小公主，应邀与王子一起起舞。", {3}},
	{11000172, "夜西套装", 3, "夜西的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000173, "金块套装", 3, "金块的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000174, "图书管理员套装", 3, "图书管理员的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000175, "守林员套装", 3, "守林员的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000176, "小耶套装", 3, "小耶的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000177, "潘潘套装", 3, "潘潘的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000178, "欢乐庆典套装", 5, "跑快点，要赶不上庆典了！", {15,22}},
	{11000179, "派对童趣套装", 2, "来参加我的派对吧！", {15,18}},
	{11000180, "派对缤纷套装", 3, "我是岛上最爱参加派对的小熊哦。", {15,18}},
	{11000181, "故事记录员套装", 2, "作为优秀的故事记录员，我最喜欢和大家聊天。", {15}},
	{11000182, "故事学者套装", 3, "作为优秀的故事学者，我要写下属于奥比岛的故事。", {15}},
	{11000183, "时尚心意套装", 2, "时尚是一种生活态度。", {15}},
	{11000184, "时尚心愿套装", 3, "什么是时尚？做自己就是时尚。", {15}},
	{11000185, "游戏时光套装", 2, "想念儿时和好朋友一起玩游戏的时光。", {29,15}},
	{11000186, "游戏动感套装", 3, "游戏开始！今天我也是最厉害的崽！", {29,15,17}},
	{11000187, "香甜果酱套装", 2, "来尝一口甜甜的果酱吧。", {15}},
	{11000188, "香甜奶油套装", 3, "最强熊熊厨师，今天要做什么料理呢？", {15,17}},
	{11000189, "探索未知套装", 2, "走！我们一起去探索未知的世界。", {15}},
	{11000190, "探索奇迹套装", 3, "探索世界，方可拥抱奇迹。", {15}},
	{11000191, "基础款休闲装", 1, "休息日最舒适的装扮。", {1}},
	{11000192, "郁金香花园礼装", 5, "布洛德温最喜欢的礼服装。", {26,3}},
	{11000193, "郁金香茶会礼装", 5, "森林里来的小松鼠也很喜欢郁金香和茶会呢。", {26,17,3}},
	{11000194, "传统田园套装", 3, "看，我的围裙上有花花。", {10}},
	{11000195, "童话红蔷薇套装", 4, "园丁先生，今日也与花儿相伴。", {26}},
	{11000196, "绮梦红蔷薇套装", 4, "瑰丽的红蔷薇在风中摇曳着。", {26}},
	{11000197, "奥比打工套装", 2, "我打工，我快乐！", {1}},
	{11000198, "时尚牛仔套装", 3, "追寻时尚，永不停息。", {1}},
	{11000199, "晶莹石榴少年装", 6, "剥开一颗石榴，每颗石榴籽都晶莹剔透。", {33}},
	{11000200, "甜蜜石榴少女装", 6, "勺起一勺石榴籽，咬一口，满嘴都是甜蜜。", {33}},
	{11000201, "时光之梦套装", 3, "甜甜的梦里，都是软软的云朵。", {14}},
	{11000202, "桃红锦裳套装", 4, "年华似水，匆匆流淌。", {2}},
	{11000203, "休闲春游套装", 3, "天气正好，一起去春游吧！", {1}},
	{11000204, "可爱女仆套装", 3, "勤劳可爱的小女仆，最喜欢的事情就是打扫啦。", {15}},
	{11000205, "（预留，可以用）", 0, "", nil},
	{11000206, "海洋遗珠套装", 3, "海洋遗落在尘世的珍宝。", {26,17}},
	{11000207, "月影之辉套装", 3, "听，是远方传来的，牛仔爽朗的笑声。", {15}},
	{11000208, "星光礼服套装", 4, "摇曳着的星光，闪烁了谁的眼。", {12}},
	{11000209, "奥比工友套装", 2, "没有困难的工作，只有勇敢的打工人!", {15}},
	{11000210, "靛蓝锦裳套装", 4, "岁月如梦，悄然消逝。", {2}},
	{11000211, "蝶梦萤火套装", 4, "蝴蝶啊蝴蝶，来我的梦里。", {17,12}},
	{11000212, "嘻哈街头套装", 2, "来吗？和我一起在街上跳动吧！", {6,1}},
	{11000213, "天蓝兔兔套装", 4, "小兔小兔慢慢跑。", {17,11}},
	{11000214, "浅粉兔兔套装", 4, "小兔小兔轻轻跳。", {1,17}},
	{11000215, "萌萌执事套装", 3, "等等等下，还差一块玻璃就擦完了！", {15}},
	{11000216, "南瓜公主套装", 4, "糖果和蝴蝶结，是世界上最可爱的。", {21,28,5}},
	{11000217, "学岚制服套装", 2, "鸟欲高飞先振翅，人求上进先读书。", {15}},
	{11000218, "南瓜王子套装", 4, "南瓜王子要出现啦，糖果准备好了吗？", {28,5,3}},
	{11000219, "北欧风格套装", 3, "似乎是从异国来游玩的少女。", {10}},
	{11000220, "古堡大灰狼套装", 5, "来自古老城堡的灰狼王子，正要去魔岭森林历练。", {19,17,5}},
	{11000221, "魔岭小红帽套装", 5, "来自魔岭森林的女孩，总喜欢戴着一顶红帽子。", {19,17,5}},
	{11000222, "灵动泡泡蓝套装", 3, "做一只快乐小熊，每一天都是那么可爱。", {9}},
	{11000223, "灵动泡泡粉套装", 3, "做一只快乐小熊，多留意身边的小惊喜。", {9}},
	{11000224, "奇妙马戏兔兔装", 4, "奇妙的兔兔，给观众带来奇妙的马戏表演。", {17}},
	{11000225, "优等生学姐套装", 3, "学习使我快乐。", {15}},
	{11000226, "优等生学长套装", 3, "学习令我成长。", {15}},
	{11000227, "宫廷小侍从套装", 3, "宫里好无聊，要和我一起出去玩吗？", {15,3}},
	{11000228, "宫廷小女仆套装", 3, "小女仆很忙，要漂亮地完成打扫哦。", {15,3}},
	{11000229, "森绿少年套装", 3, "像是把春日都穿在了身上。", {1}},
	{11000230, "暖阳少女套装", 3, "像是把阳光都穿在了身上。", {1}},
	{11000231, "月夜小夜曲套装", 5, "可以在你的屋顶上晒月亮吗？我会在月光下为你祈福。", {19,5}},
	{11000232, "风暖未来套装", 4, "暖风的化身，从未来而来。", {32,17}},
	{11000233, "繁花照影套装", 4, "繁花落在身上，照进心里。", {26}},
	{11000234, "繁花对影套装", 4, "于繁花之下与你相见。", {26}},
	{11000235, "元气应援套装", 4, "打起精神来！为大家加油打气！", {7}},
	{11000236, "节奏应援套装", 4, "加油加油！向前冲！", {7}},
	{11000237, "时空奇旅套装", 4, "穿梭于各个时空之间，在寻找什么呢？", {27,17}},
	{11000238, "伯爵石榴奶茶装", 6, "第一杯石榴奶茶，你想和谁分享？", {33}},
	{11000239, "追光迷音套装", 3, "追着光走吧，遇见更好的自己。", {22}},
	{11000240, "逐影迷音套装", 3, "追逐影子，其实是在寻找自我。", {22}},
	{11000241, "宅宅物语套装", 3, "忙碌的学习工作之后，还是喜欢呆在家里休息呢。", {1}},
	{11000242, "火凤套装", 3, "火凤同款套装。", {16}},
	{11000243, "龙三太子套装", 3, "龙三太子同款套装。", {16}},
	{11000244, "龙妮套装", 3, "龙妮同款套装。", {16}},
	{11000245, "彩衣套装", 3, "彩衣同款套装。", {16}},
	{11000246, "华大夫套装", 3, "华大夫同款套装。", {16}},
	{11000247, "雷欧套装", 3, "雷欧同款套装。", {16}},
	{11000248, "纳多王子套装", 3, "纳多王子同款套装。", {16}},
	{11000249, "白龙长老套装", 3, "白龙长老同款套装。", {16}},
	{11000250, "土狼套装", 3, "土狼同款套装。", {16}},
	{11000251, "金笛套装", 3, "金笛同款套装。", {16}},
	{11000252, "木阳套装", 3, "木阳同款套装。", {16}},
	{11000253, "阿宅套装", 3, "阿宅同款套装。", {16}},
	{11000254, "雷文套装", 3, "雷文同款套装。", {16}},
	{11000255, "缇亚套装", 3, "缇亚同款套装。", {16}},
	{11000256, "卡布套装", 3, "卡布同款套装。", {16}},
	{11000257, "红猪套装", 3, "红猪同款套装。", {16}},
	{11000258, "可比套装", 3, "可比同款套装。", {16}},
	{11000259, "大脚雪怪套装", 3, "大脚雪怪同款套装。", {16}},
	{11000260, "浩天套装", 3, "浩天同款套装。", {16}},
	{11000261, "云阳公主套装", 3, "云阳公主同款套装。", {16}},
	{11000262, "行星引导者套装", 5, "相伴相生的双子星，在星河之上引导着秩序。", {27,12,33}},
	{11000263, "黑天鹅王子套装", 5, "他护着心上的公主，邀她共舞。", {17,22,3}},
	{11000264, "凤之子乞巧新衣", 4, "来自遥远东方大炎国的凤凰族王子。", {2}},
	{11000265, "星间奇旅套装", 4, "穿梭于各个时空之间，追寻着宇宙的秘密。", {27}},
	{11000266, "白天鹅公主套装", 5, "她踮起足尖，宛如湖边的精灵。", {17,22,3}},
	{11000267, "行星掌控者套装", 5, "相伴相生的双子星，在黑暗的宇宙中永不分离。", {27,12,33}},
	{11000268, "龙之女乞巧华裳", 4, "来自遥远东方大炎国的龙族之女。", {2}},
	{11000269, "铃兰颂歌套装", 5, "进入神秘森林的音乐家，用乐曲唤醒了沉睡的铃兰。", {26,22}},
	{11000270, "铃兰礼赞套装", 5, "铃兰给予了音乐家祝福，森林音乐会在夜幕中奏响。", {26,22}},
	{11000271, "暗夜创造师套装", 3, "协会中最可爱的创造师。", {15}},
	{11000272, "幽影创造师套装", 3, "协会中最帅气的创造师。", {15}},
	{11000273, "苜宿守护套装男", 3, "（这两系列散件形式投放）", nil},
	{11000274, "苜宿守护套装女", 3, "（这两系列散件形式投放）", nil},
	{11000275, "神秘马戏兔兔装", 4, "神秘的兔兔，给观众带来奇异的马戏表演。", {17}},
	{11000276, "暮色来信套装", 4, "暮色降临，收到了一封神秘的信件。", {15}},
	{11000277, "糖霜奶油棒棒糖", 5, "呐，甜甜的棒棒糖，治愈你了吗？", {28}},
	{11000278, "雾尽明澈套装", 4, "雾的化身，驱散了笼罩在空中的暗。", {32}},
	{11000279, "甜点奶油棒棒糖", 5, "哇！要被淹没在甜甜的奶油里啦！", {28}},
	{11000280, "暮色入梦套装", 4, "暮色降临，进入漆黑的梦境。", {15}},
	{11000281, "月桂寄情套装", 4, "桂花啊桂花，请将我的思念寄向远方。", {2}},
	{11000282, "夜影创造师套装", 5, "她行走于夜幕之中，寻找着光。", {20}},
	{11000283, "雨后初晴套装", 4, "雨后的晴空，治愈了夏天。", {26}},
	{11000284, "光源创造师套装", 5, "他行走在日光之下，眼睛却望向阴影处。", {20}},
	{11000285, "雨意空濛套装", 4, "我偏爱着，雨落下的声音。", {26}},
	{11000286, "月桂相思套装", 4, "月亮啊月亮，我总是在日暮时分思念着你。", {2}},
	{11000287, "沧海魅影套装", 5, "来自海洋的王子，安静且浪漫。", {10,26}},
	{11000288, "希尔达套装", 3, "希尔达的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000289, "维克多同款", 3, "维克多的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000290, "大胡子同款", 3, "大胡子的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000291, "柯莱同款", 3, "柯莱的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000292, "豪气少年总裁装", 4, "深情的总裁少年，礼物是送给谁呢？", {15}},
	{11000293, "垂耳冰兔套装", 4, "毛茸茸、软乎乎的小白兔。", {17}},
	{11000294, "梅尔同款", 3, "梅尔的同款。（单品以散件系列投放，暂时没用）", {16}},
	{11000295, "明星领航员套装", 3, "帮助小奥比，我们义不容辞。", {15}},
	{11000296, "童趣小丑套装", 5, "抱着玩偶的小丑，有很多故事可以说。", {15}},
	{11000297, "马尔斯王子套装", 4, "他的剑和铠甲，为暗夜而生，为暗夜而死。", {3}},
	{11000298, "暗夜公主套装", 4, "她的笑容，是暗夜中盛开的一朵野蔷薇。", {3}},
	{11000299, "白雪姬套装", 4, "被赶出城堡的公主，自己一人踏上旅途。", {19,3}},
	{11000300, "白雪王子套装", 4, "喜欢冒险的王子，带着佩剑踏上旅途。", {19,3}},
	{11000301, "巧克力雪糕套装", 4, "巧克力口味的雪糕，甜进你心里了吗？", {28}},
	{11000302, "巧克力莓果套装", 4, "树莓风味巧克力，酸甜中带着一丝醇厚。", {28}},
	{11000303, "奥比专使套装", 3, "是来自奥比岛的使者哦。", {23}},
	{11000304, "快乐摇摆套装", 3, "来一起快乐起舞吧！", {22}},
	{11000305, "复古摇摆套装", 3, "让我们快乐摇摆吧！", {22}},
	{11000306, "葡萄气泡饮套装", 4, "葡萄执事擦拭着盘子，等待客人坠入葡萄梦境中。", {28}},
	{11000307, "蜜糖黄宝石套装", 6, "来品尝一口最甜的蜜糖。", {33}},
	{11000308, "桃桃酸奶冰套装", 4, "桃桃女仆将客人的秘密，藏在酸奶冰块中。", {28}},
	{11000309, "火凤个性套装", 4, "火凤的同款套装。", {16}},
	{11000310, "阿宅个性套装", 4, "阿宅的同款套装。", {16}},
	{11000311, "金笛个性套装", 4, "金笛的同款套装。", {16}},
	{11000312, "木阳个性套装", 4, "木阳的同款套装。", {16}},
	{11000313, "雷欧个性套装", 4, "雷欧的同款套装。", {16}},
	{11000314, "卡布个性套装", 4, "卡布的同款套装。", {16}},
	{11000315, "土狼个性套装", 4, "土狼的同款套装。", {16}},
	{11000316, "华大夫个性套装", 4, "华大夫的同款套装。", {16}},
	{11000317, "雷文个性套装", 4, "雷文的同款套装。", {16}},
	{11000318, "缇亚个性套装", 4, "缇亚的同款套装。", {16}},
	{11000319, "可比个性套装", 4, "可比的同款套装。", {16}},
	{11000320, "大脚雪怪个性装", 4, "大脚雪怪的同款套装。", {16}},
	{11000321, "红猪个性套装", 4, "红猪的同款套装。", {16}},
	{11000322, "纳多王子个性装", 4, "纳多王子的同款套装。", {16}},
	{11000323, "龙妮个性套装", 4, "龙妮的同款套装。", {16}},
	{11000324, "白龙长老个性装", 4, "白龙长老的同款套装。", {16}},
	{11000325, "彩衣个性套装", 4, "彩衣的同款套装。", {16}},
	{11000326, "浩天个性套装", 4, "浩天的同款套装。", {16}},
	{11000327, "龙三太子个性装", 4, "龙三太子的同款套装。", {16}},
	{11000328, "云阳个性套装", 4, "云阳的同款套装。", {16}},
	{11000329, "科技机修套装", 4, "机械修理与女孩，再完美不过的组合！", {32}},
	{11000330, "沉睡之语套装", 4, "美丽的公主，你为何沉睡？", {19,3}},
	{11000331, "枫叶解语套装", 4, "秋天到了，一起去赏枫叶吧。", {26}},
	{11000332, "荆棘誓言套装", 4, "勇敢的王子，你为何而来？", {19,3}},
	{11000333, "枫叶知秋套装", 4, "秋天到了，要记录下每一个瞬间。", {15,26}},
	{11000334, "科技时空套装", 4, "稍等，异时空跳跃者寻找中……", {32}},
	{11000335, "棉棉新潮套装", 4, "运动会怎么能少得了我呢！", {6,1,17}},
	{11000336, "向阳爱语套装", 5, "从油画中苏醒，将沉默的爱意诉与你听。", {26}},
	{11000337, "水晶奇缘套装", 4, "我梦想着有一天，能走出这里，去往更辽阔的世界。", {19,3}},
	{11000338, "棉棉风潮套装", 4, "想来运动会一较高下吗？", {6,1}},
	{11000339, "蜜蜂休闲女装", 3, "蜜蜂印象的休闲女装。", {1,17}},
	{11000340, "蜜蜂休闲男装", 3, "蜜蜂印象的休闲男装。", {1,17}},
	{11000341, "向阳心声套装", 5, "将你刻在画上，心声封印在画中。", {15,26}},
	{11000342, "水晶挚爱套装", 4, "是你吗？我一直在寻找的公主。", {19,3}},
	{11000343, "宇航员套装", 0, "", {15}},
	{11000344, "探险装", 4, "", {15}},
	{11000345, "冬日游园套装", 4, "苏醒后的雪精灵，尽情享受游乐园的快乐。", {26}},
	{11000346, "微光许愿套装", 5, "微光亮起，许下心愿。", {19,26}},
	{11000347, "冬日乐园套装", 4, "凛冬已至，雪精灵从沉睡中苏醒。", {26}},
	{11000348, "冰雪誓言套装", 5, "冰雪降临，许下誓言。", {19}},
	{11000349, "冬眠睡裙套装", 3, "冬天要做的第一件事，就是睡觉！", {17,1,14}},
	{11000350, "岁月如歌套装", 4, "岁月的交响曲奏响，永远期待新的明天。", {21}},
	{11000351, "冬眠睡衣套装", 3, "睡醒的第一件事，就是继续打盹。", {17,1,14}},
	{11000352, "夜宴蓝玫瑰套装", 6, "夜幕已然降临，宴会开始。", {26,33,3}},
	{11000353, "冰之心灵套装", 4, "像雪一样冰冷的心灵，要如何才能温暖？", {19,26,3}},
	{11000354, "冰之守护套装", 4, "即使在黑暗中，也要守护那冰晶一般的心。", {19,26,17,3}},
	{11000355, "魔术蓝玫瑰套装", 6, "宴会既已开始，不妨来欣赏下这精彩的表演。", {26,33,3}},
	{11000356, "岁月如诗套装", 4, "岁月如诗，明日会播种新的希望。", {21}},
	{11000357, "翰墨风华套装", 6, "青龙帝君应劫而下凡间，一部分神识化为大炎国的女王孟章。", {2,23}},
	{11000358, "金兔报春套装", 5, "青龙帝君座下的金兔星官，汲取满月之息，司掌除妖驱邪。", {2}},
	{11000359, "景星麒麟套装", 5, "将军手中一柄麒麟长剑，所向披靡，能斩一切妖魔。", {2,23}},
	{11000360, "镌刻丹青套装", 6, "青龙帝君应劫而下凡间，另一部分神识化为大炎国的王子苍。", {2,23}},
	{11000361, "千岁鹤归套装", 4, "温婉善良的仙鹤族妹妹，对色彩的使用炉火纯青。", {2,23}},
	{11000362, "唐僧同款系列", 3, "唐僧同款系列服饰。", {25}},
	{11000363, "孙悟空同款系列", 3, "孙悟空同款系列服饰。", {25}},
	{11000364, "沙和尚同款系列", 3, "沙和尚同款系列服饰。", {25}},
	{11000365, "猪八戒同款系列", 3, "猪八戒同款系列服饰。", {25}},
	{11000366, "哪吒同款系列", 3, "哪吒同款系列服饰。", {25}},
	{11000367, "小龙女同款系列", 3, "小龙女同款系列服饰。", {25}},
	{11000368, "景星庆云套装", 5, "谦谦君子，温润如玉。", {2,23}},
	{11000369, "明镜花月套装", 4, "如花，如月。", {2,19}},
	{11000370, "孙悟空同款套装", 3, "孙悟空同款套装。", {16,25}},
	{11000371, "赤兔送福套装", 5, "赤兔星官挥墨，好运连绵满年。", {2}},
	{11000372, "流水梦鲤套装", 4, "得到了大家喜爱的锦鲤，也能实现自己的心愿吗？", {2}},
	{11000373, "鹤骨松姿套装", 4, "性情高冷的仙鹤族哥哥，上知天文下知地理。", {2,23}},
	{11000374, "锦玉归年套装", 4, "小锦鲤呀小锦鲤，我来实现你的心愿。", {2}},
	{11000375, "风霜剑影套装", 4, "似剑，似霜。", {2,19}},
	{11000376, "瑞狮贺年套装", 3, "锣鼓喧天响，新年迎吉兆！", {2}},
	{11000377, "醒狮辞岁套装", 3, "醒狮辞旧岁，吉祥过大年！", {2}},
	{11000378, "斗战胜佛套装", 4, "斗战胜佛孙悟空的同款套装。", {16}},
	{11000379, "玫瑰圆舞曲套装", 4, "伴着浪漫的圆舞曲，她与王子上演着华丽的双人舞。", {19,3}},
	{11000380, "倾心誓约套装", 5, "许下誓约，仍然要相信永恒不变的爱。", {12}},
	{11000381, "钟情诺言套装", 5, "应允诺言，依然要守护天长地久的情谊。", {12}},
	{11000382, "绮丽化妆师套装", 4, "谁和他欢喜冤家，只是普通朋友罢了。", {15}},
	{11000383, "璀璨摄影师套装", 4, "口不对心的欢喜冤家，也是彼此最重要的人。", {15}},
	{11000384, "黑猫歌唱家套装", 4, "唱着唯美的乐曲，他与女孩演绎着动人的感情。", {19,3}},
	{11000385, "绮梦花童套装", 3, "穿着小礼服的小花童，可爱到令人尖叫。", {12}},
	{11000386, "梦忆花童套装", 3, "跟随在新郎新娘身边的小花童，可爱满分。", {12}},
	{11000387, "歌者美乐蒂套装", 5, "聚光灯下，她甜美动人的身影犹如星光般璀璨。", {25}},
	{11000388, "乐手酷洛米套装", 5, "霓虹灯下，她激昂澎湃的身影炒热了每个人的心。", {25}},
	{11000389, "学生会长套装", 4, "学生会长，完美学生的代表。", {11}},
	{11000390, "话剧部长套装", 4, "话剧部部长，也是一名编剧。", {11}},
	{11000391, "艾伦王子套装", 4, "窘迫？不存在的，任何时候都要风度翩翩。", {3}},
	{11000392, "旋律咖啡套装", 4, "奇妙音乐搭配咖啡香气，治愈你的心灵。", {15,3}},
	{11000393, "暖洋宴会套装", 4, "他会将亲手制作的永生花，献给敬爱的神明。", {26,12}},
	{11000394, "魔书精灵套装", 5, "魔书中的每一页都是她记载的漫长岁月。", {20}},
	{11000395, "魔书使者套装", 5, "在漫长岁月里，守护魔书是他永恒的使命。", {20}},
	{11000396, "人鱼之鳞套装", 5, "他的灵魂被割裂，镜中凝聚出了另一个“她”。", {10,26,17}},
	{11000397, "人鱼之泪套装", 5, "茫茫深海中，她陪伴着他，一起守护陨落的海神花园。", {10,26,17}},
	{11000398, "黑羽迷雾套装", 4, "循着路边散落的黑羽，就可以找到的神秘之人。", {19}},
	{11000399, "梳羽流光套装", 4, "森林高塔中的公主，在窗边静静眺望。", {19,3}},
	{11000400, "春和花园套装", 4, "她摘来了只在春天盛开的鲜花，并用这些鲜花装扮自己。", {26,12}},
	{11000401, "可爱水手套装", 3, "寻找陨落的海神花园，水手们非常有信心！", {15,26}},
	{11000402, "勇气水手套装", 3, "怀着勇气，水手们踏上了寻宝的旅程。", {15,26}},
	{11000403, "优雅公主套装", 4, "她的一颦一笑，都如水般温柔宁静。", {3}},
	{11000404, "云梦大耳狗套装", 5, "不期而遇的交集，是云端之间最美的惊喜。", {25}},
	{11000405, "星梦大耳狗套装", 5, "不经意的邂逅，是云端之间最美的相遇。", {25}},
	{11000406, "闪亮打歌套装", 4, "站在自己热爱的舞台上，闪闪发亮！", {15}},
	{11000407, "韵律咖啡套装", 4, "欢迎光临音乐咖啡厅，要喝杯什么呢？", {15}},
	{11000408, "梦境爱丽丝套装", 4, "被注入梦境世界的梦，会有自己的意识吗？", {19,17,5}},
	{11000409, "雨意空濛蓝套装", 4, "我偏爱着，雨落下的气息。", {26}},
	{11000410, "梦境兔先生套装", 4, "是梦境世界的迎接者，也是“造梦玩家”。", {19,17,5}},
	{11000411, "悠游少女套装", 3, "有很多新鲜事想要说给你听哦！", {1}},
	{11000412, "悠游少年套装", 3, "下课了，不如去咖啡厅喝杯咖啡吧！", {1}},
	{11000413, "雏菊花旅套装", 4, "花盛开的季节，去旅行吧！", {8,19,3}},
	{11000414, "新叶之行套装", 4, "叶苏醒的季节，去游历吧！", {8,19,3}},
	{11000415, "潮酷舞台套装", 4, "音乐响起，这里就是我的舞台！", {15}},
	{11000416, "暮色入梦红套装", 4, "暮色降临，进入彩色的梦境。", {15}},
	{11000417, "暮色入梦紫套装", 4, "暮色降临，进入轻松的梦境。", {15}},
	{11000418, "暮色入梦蓝套装", 4, "暮色降临，进入安详的梦境。", {15}},
	{11000419, "暮色来信红套装", 4, "暮色降临，收到了一封突然的信件。", {15}},
	{11000420, "暮色来信紫套装", 4, "暮色降临，收到了一封邀约的信件。", {15}},
	{11000421, "暮色来信蓝套装", 4, "暮色降临，收到了一封意外的信件。", {15}},
	{11000422, "幻海魅影蓝套装", 5, "来自幻海的公主，温柔且优雅。", {26}},
	{11000423, "幻海魅影橙套装", 5, "来自幻海的公主，可爱且活泼。", {26}},
	{11000424, "幻海魅影绿套装", 5, "来自幻海的公主，安静且端庄。", {26}},
	{11000425, "沧海魅影蓝套装", 5, "来自海洋的王子，温和且忧伤。", {26}},
	{11000426, "沧海魅影橙套装", 5, "来自海洋的王子，阳光且积极。", {26}},
	{11000427, "沧海魅影绿套装", 5, "来自海洋的王子，坚韧且不屈。", {26}},
	{11000428, "雨后初晴粉套装", 4, "雨后的晴空，明媚了夏天", {26}},
	{11000429, "雨后初晴橙套装", 4, "雨后的晴空，点亮了夏天", {26}},
	{11000430, "雨后初晴绿套装", 4, "雨后的晴空，清新了夏天", {26}},
	{11000431, "雨意空濛橙套装", 4, "我偏爱着，雨落下的味道。", {26}},
	{11000432, "雨意空濛粉套装", 4, "我偏爱着，雨落下的氛围。", {26}},
	{11000433, "流砂之影套装", 4, "他不喜喧闹，却愿陪她走遍繁华。", {10,19,3}},
	{11000434, "卫兵格兰德套装", 5, "卫兵向小主人起誓，要永远守卫最重要的朋友。", {19,18,3}},
	{11000435, "童心泡泡套装", 4, "你是否也有，在夏日公园里，肆意挥舞泡泡的回忆。", {17,1,18}},
	{11000436, "童趣泡泡套装", 4, "我曾在泡泡中看见彩虹的模样。", {1,18}},
	{11000437, "小熊莉亚套装", 5, "小熊是小主人的第一个伙伴，会永远陪伴在你身边。", {17,18}},
	{11000438, "玩具修复师套装", 3, "听说，任何玩具到她手上，都能变得焕然一新。", {15}},
	{11000439, "流砂之愿套装", 4, "她向往自由，确幸一路有他同行。", {10,19,3}},
	{11000440, "玩具修理师套装", 3, "听说，任何玩具到他手上，都能变得完美无缺。", {15}},
	{11000441, "岁月流金男套装", 4, "", {15}},
	{11000442, "征途骑士套装", 4, "即使前路艰险，也要英勇向前。", {22}},
	{11000443, "双子座织梦套装", 4, "我们搞怪机灵，喜欢寻找身边的新鲜事。", {29,30}},
	{11000444, "狼神夜幕套装", 5, "当夜幕降临，他黑金色的眼眸如夜色般孤独。", {17,23}},
	{11000445, "珍珠少女套装", 3, "从容恬淡的少女，耳畔的珍珠耳环若隐若现。", {22}},
	{11000446, "猫神月华套装", 5, "当月色笼罩，她娇媚的身姿如月光般明艳。", {17,23}},
	{11000447, "白貂少女套装", 4, "温柔知性的少女，依偎着白色的绒貂。", {22}},
	{11000448, "吹笛少年套装", 3, "年轻无畏的士兵，正在吹奏着笛子。", {22}},
	{11000449, "双子座幻想套装", 4, "我们无拘无束，拥有永无休止的好奇心。", {29,30}},
	{11000450, "红小豆踏青套装", 4, "随着夏日的脚步声，感受大自然的热情与活力。", {1,17,25}},
	{11000451, "红小豆郊游套装", 4, "让我们在自然中漫步，与绿草清风为伴。", {1,17,25}},
	{11000452, "绘彩光年套装", 5, "来自彩虹星河的独角兽使者，携礼而来。", {17,12}},
	{11000453, "花车巡游套装", 4, "随着悦耳节拍，向大家投以最灿烂的笑容。", {15,22}},
	{11000454, "花车游园套装", 4, "随着鲜花礼炮，为大家送上最诚挚的祝福。", {15}},
	{11000455, "狼女王魅影套装", 5, "趣味的生活怎么少得了小惊喜呢？", {17,25}},
	{11000456, "懒羊羊吃货天使", 6, "吧唧吧唧，是哪只小肥羊在吃好吃的？", {17,25}},
	{11000457, "巨蟹座夏日套装", 4, "让我们一起享受夏日舒服的海风吧。", {13,30,17}},
	{11000458, "幻彩星河套装", 5, "受邀而来的彩虹星河使者，带来了浪漫的色彩。", {17,12}},
	{11000459, "银河调查官套装", 5, "来自银河安全局的调查官，为你保驾护航。", {15,32}},
	{11000460, "星际医官套装", 5, "来自星际医疗站的星际医官，守护你的健康。", {15,32}},
	{11000461, "古城唯美公主装", 4, "是美丽优雅的古城公主呀。", {3}},
	{11000462, "懒羊羊睡梦天使", 6, "呼呼呼，是哪只小肥羊在睡懒觉呢？", {17,25}},
	{11000463, "帅气道尔侦探装", 4, "是银发侦探大帅哥！", {15}},
	{11000464, "灰太狼暗影套装", 5, "安稳的生活怎么能容忍被破坏呢？", {17,25}},
	{11000465, "帅气熊猫机甲装", 4, "今日任务已完成，圆满收官。", {32,17}},
	{11000466, "呆萌熊猫机甲装", 4, "真是元气满满的一天！开工啦！", {32,17}},
	{11000467, "巨蟹座假日套装", 4, "一起看海，一起听风的温柔低语。", {13,30,17}},
	{11000468, "喜羊羊同款系列", 3, "喜羊羊的同款系列服饰。", {17,25}},
	{11000469, "灰太狼同款系列", 3, "灰太狼的同款系列服饰。", {17,25}},
	{11000470, "懒羊羊同款系列", 3, "懒羊羊的同款系列服饰。", {17,25}},
	{11000471, "美羊羊同款系列", 3, "美羊羊的同款系列服饰。", {17,25}},
	{11000472, "喜羊羊潮流套装", 4, "大肥羊学校里的风云人物，运动系清爽少年。", {1,25}},
	{11000473, "美羊羊软萌套装", 4, "大肥羊学校里的时尚名人，可爱善良的软萌少女。", {1,25}},
	{11000474, "乐园漫步套装", 3, "为了融入炫彩季的氛围，我们也要穿得明亮一些！", {1}},
	{11000475, "乐园漫游套装", 3, "炫彩季开始了，一起看看有什么好玩的游戏吧！", {1}},
	{11000476, "神之蝶套装", 5, "他自神的花园中诞生，从蝴蝶幻化而来。", {22,17,23}},
	{11000477, "神之花园套装", 5, "她是掌管花园的神，她的花园里有世间最美的蝴蝶。", {26,23}},
	{11000478, "狮子座总裁套装", 4, "互相追逐的感觉令我们着迷。", {15,30}},
	{11000479, "狮子座明星套装", 4, "让我们顶峰相见。", {15,30}},
	{11000480, "蘑菇精灵套装", 5, "可爱的蘑菇精灵，是丰收女神派出的使者。", {26}},
	{11000481, "星际引导员套装", 3, "为大家指引方向，是他的职责。", {15,32}},
	{11000482, "星际播报员套装", 3, "为大家播报实况，是她的职责。", {15,32}},
	{11000483, "夜西宠爱套装", 4, "天气越来越冷了，小老板要记得多加件衣服呢。", {1}},
	{11000484, "萧萧九重套装", 5, "头戴帅盔，腰扎靠绸。他一开口，便是金戈铁马之声。", {2,22}},
	{11000485, "部落首领套装", 4, "他是信仰丰收女神的部落首领，会带领部落获得希望。", {10}},
	{11000486, "部落祭司套装", 4, "她是信仰丰收女神的部落祭司，能够占卜天气与吉凶。", {10}},
	{11000487, "处女座馥郁套装", 4, "馥郁的香气萦绕，是否能遇见默契的友人？", {15,30}},
	{11000488, "处女座绅士套装", 4, "第六次擦拭手心，时刻保持干净是他对生活的态度。", {15,30}},
	{11000489, "蘑菇信使套装", 5, "稳重的蘑菇信使，为各个部族送去祝福。", {26}},
	{11000490, "悠悠凤鸣套装", 5, "头戴凤冠，腰悬玉带。她一开口，便如凤凰高鸣。", {2,22}},
	{11000491, "牡丹寄情套装", 4, "“咿呀呀。”她轻捻指尖，唱出了这曲《牡丹情》。", {2,22}},
	{11000492, "星纱公主套装", 4, "星纱公主的脑海中都是童话般美好的想法~", {3}},
	{11000493, "游园一梦套装", 4, "“小生有礼了。”他俯身作揖，拉开了昆曲的序幕。", {2,22}},
	{11000494, "稻草人劳作装", 3, "保护稻田的稻草人，鸟儿可不许来偷吃哦。", {10,15}},
	{11000495, "小农夫耕种套装", 3, "擅长耕种的小农夫，锄头挥得一级棒！", {10,15}},
	{11000496, "月灵物语套装", 4, "姐姐准备了最好吃的糖果，和她一起去晒月亮吧！", {5}},
	{11000497, "月灵夜欢歌套装", 5, "我想到了一个新点子，大家准备迎接新的“惊喜”吧！", {5}},
	{11000498, "月灵提灯套装", 4, "弟弟点亮了提灯的光芒，和他一起去追逐月光吧！", {5}},
	{11000499, "天秤座守护套装", 4, "他仔细地准备着资料，只愿正义能得以伸张。", {15,30}},
	{11000500, "天秤座审判套装", 4, "她谨慎地敲下槌，只愿公平和正义永存。", {15,30}},
	{11000501, "月夜舞步套装", 3, "少年敲敲门说：“给我薄荷糖，我就跳街舞给你看！”", {1}},
	{11000502, "月夜舞曲套装", 3, "少女敲敲窗说：“给我跳跳糖，我跳踢踏舞给你看！”", {1}},
	{11000503, "天狼个性套装", 4, "天狼的同款套装。", {16}},
	{11000504, "天狼套装", 3, "天狼同款套装。", {16}},
	{11000505, "摇篮曲套装", 4, "温柔的姐姐，每晚都会给弟弟哼唱一首摇篮曲。", {1,3}},
	{11000506, "安眠曲套装", 4, "怕黑的男孩，每晚都要听着摇篮曲才能入睡。", {1,3}},
	{11000507, "花卉魔药师套装", 5, "前往森林的魔药师，喜欢用花儿来调配魔药试剂。", {26,20}},
	{11000508, "森林魔药师套装", 5, "隐居森林的魔药师，想调制出让鲜花永远绽放的魔药。", {26,20}},
	{11000509, "欢世之颂套装", 5, "擅于作曲的音乐家能将世界的美好化为演奏的乐曲。", {22,3}},
	{11000510, "索马尼王子套装", 4, "绅士的外表下藏着一颗侠义之心。", {3}},
	{11000511, "白夜之花套装", 5, "旋转的芭蕾舞步陷入黑暗，奏响的音乐之声将其救赎。", {22,3}},
	{11000512, "天蝎座神秘套装", 4, "神秘的星夜牌，能够预知未来的走向。", {15,30}},
	{11000513, "天蝎座命运套装", 4, "命运的水晶球，能够指引前方的道路。", {15,30}},
	{11000514, "吞星猎手套装", 4, "机智、爽朗、勇敢，这些夸奖他就通通收下了。", {17}},
	{11000515, "星辰精灵套装", 5, "管理星辰之国的精灵，能化为流星在天空飞翔。", {3}},
	{11000516, "极地女巫套装", 5, "极地中的女巫，肩负着守护安宁的责任。", {12,20,3}},
	{11000517, "青春序曲套装", 3, "为剧团配乐的合唱团成员，高声唱出青春的序曲。", {15}},
	{11000518, "青春赞歌套装", 3, "为剧团配乐的合唱团成员，欢快地吟诵青春的赞歌。", {15}},
	{11000519, "沐光未来套装", 5, "晨曦的启明之光，司掌着未来。", {15}},
	{11000520, "冬日猎手套装", 4, "耐心、可爱、聪慧，这些词用在她身上刚刚好。", {17}},
	{11000521, "射手座光辉套装", 4, "丛林中的指引者，引领着岛屿上的子民。", {30,23}},
	{11000522, "射手座航海套装", 4, "海上的冒险家，想要走遍世界的每一片土地。", {15,30}},
	{11000523, "野蛮公主套装", 4, "谁说野蛮和美丽不能共存？", {3}},
	{11000524, "毛线小熊套装", 3, "她伸出手，迎接冬日的第一片雪花。", {1}},
	{11000525, "温暖冬日套装", 3, "裹紧外套，寒冷的冬日也变得温暖起来。", {1}},
	{11000526, "千里江山套装", 4, "游览天下风光，遍赏千里江山。", {2,25}},
	{11000527, "绒绢花果套装", 4, "看针线交织，绣世间草木。", {2,25}},
	{11000528, "金瓯永固套装", 5, "愿海晏河清，山河永固。", {2,25}},
	{11000529, "洒蓝描金套装", 5, "望顺遂无虞，皆得所愿。", {2,25}},
	{11000530, "暴雨精灵套装", 4, "生活在天空中的暴雨精灵，降雨量永远刚刚好。", {26}},
	{11000531, "雷电精灵套装", 4, "生活在天空中的雷电精灵，据说很喜欢跳摩擦舞。", {26}},
	{11000532, "福至灵犀套装", 5, "司掌福运的福运龙，祝大家好运常伴！", {2,17,23}},
	{11000533, "浩然玄风套装", 5, "司掌正义的浩气龙，愿世间公平永存。", {2,17,23}},
	{11000534, "苍翠润泽套装", 6, "司掌天象万物，唤醒天地生机。", {2,17,23}},
	{11000535, "财源玲珑套装", 4, "司掌财运的财源龙，获得她祝福的人将获得滚滚财源。", {2,17,23}},
	{11000536, "摩羯座使者套装", 4, "驻守于神殿的使者，收集着世间的欢乐。", {15,30,17}},
	{11000537, "摩羯座颂祝套装", 4, "四处奔波的颂祝者，将祝福播撒在世间。", {15,30,17}},
	{11000538, "智掌乾坤套装", 4, "司掌智慧的智慧龙，拥有取之不尽的智慧与学识。", {2,17,23}},
	{11000539, "沉影往昔套装", 5, "暮夜的沉渊之影，能改变过去。", {15}},
	{11000540, "灰羽侍者套装", 3, "穿梭在光影间的侍者，从前总因不靠谱的上司而头痛。", {15,1,11}},
	{11000541, "灰羽信使套装", 3, "穿梭在光影间的信使，发现上司突然变得可靠起来。", {15,1,11}},
	{11000542, "红莲灼厄套装", 6, "司掌红莲火焰，燃尽灾难厄运。", {2,17,23}},
	{11000543, "绣球花之韵套装", 6, "采一株绣球，嗅满园芬芳。", {26,33,3}},
	{11000544, "紫罗兰之语套装", 6, "花束与宝石，碰撞出独特的灵感。", {26,33,3}},
	{11000545, "喜乐长安套装", 3, "新年伊始，喜乐长安！", {10,2}},
	{11000546, "龙年贺吉套装", 3, "龙腾盛世，瑞气盈门！", {10,2}},
	{11000547, "水瓶座记忆套装", 4, "将水收藏，倾听水中的故事。", {30}},
	{11000548, "水瓶座知音套装", 4, "奔涌的海浪，将故事诉说。", {30}},
	{11000549, "复苏之花套装", 5, "当永生不再，他于春天复苏。", {26,17}},
	{11000550, "翩舞之蝶套装", 5, "快醒醒、快醒醒，一起在春天起舞吧！", {26,17}},
	{11000551, "元气龟龟套装", 4, "元气满满的少女，比兔子跑得还要快！", {17}},
	{11000552, "悠然龟龟套装", 4, "患有拖延症的少年，口头禅是“明天再说吧”。", {17}},
	{11000553, "活泼芹芹套装", 3, "凡她所在之处，必是阵阵欢声笑语。", {10,3}},
	{11000554, "气质才子套装", 3, "心向四海，口吐文章，富贵且逍遥。", {10,3}},
	{11000555, "温柔素素套装", 3, "那一抹温柔，最是动人。", {10,3}},
	{11000556, "金枝玉叶套装", 3, "可叹身娇体弱，却道人间正好。", {10,3}},
	{11000557, "才俊非凡套装", 3, "饱读诗书、温文尔雅、才俊非凡。", {10,3}},
	{11000558, "春日暖阳套装", 3, "趁着春天来到，快去原野间玩耍！", {1}},
	{11000559, "春日晴风套装", 3, "趁着春天来到，快去田野间踏青！", {1}},
	{11000560, "浪漫之都套装", 4, "城市的浪漫，从建筑的浪漫开始。", {9}},
	{11000561, "都会新潮套装", 5, "欢呼和掌声不要停！大家一起嗨起来！", {6}},
	{11000562, "龙城奇珍套装", 5, "身为生意人，表面做的是生意，实际讲的是人情。", {2}},
	{11000563, "双鱼座俊朗套装", 4, "纯真并非错误，他要将遗憾弥补。", {30,17}},
	{11000564, "雾都印象套装", 4, "埋藏在迷雾下的真相，更让人有探究的欲望。", {9}},
	{11000565, "双鱼座纯真套装", 4, "水珠流过指尖，鱼儿亲吻面颊，大海在欢迎她回家。", {30,17}},
	{11000566, "白羊座机敏套装", 4, "哪里受伤了吗？快让他来看一看。", {30,17}},
	{11000567, "白羊座温柔套装", 4, "在她温柔的外表下，隐藏着一个怪力少女。", {30,17}},
	{11000568, "史瓦西个性套装", 4, "史瓦西的同款套装。", {16}},
	{11000569, "万象之旅套装", 3, "来到万象城，当然要把每个景点都打个卡呀！", {1}},
	{11000570, "史瓦西套装", 3, "史瓦西同款套装。", {16}},
	{11000571, "万象之行套装", 3, "来到万象城，当然要舒舒服服地玩一圈啊！", {1}},
	{11000572, "心动派套装", 5, "生活在月球上的它，脑内有许多天马行空的幻想。", {17,25}},
	{11000573, "行动派套装", 5, "生活在月球上的它，将心事都融入到了画笔之中。", {17,25}},
	{11000574, "元气梅子套装", 4, "酸甜爽口的梅子饭团，为你的精力续个航！", {10,28}},
	{11000575, "蒜香奶油虾套装", 5, "自然的清新与随性搭配，每一口都是出乎意料的美味。", {15,28,17}},
	{11000576, "酥皮蘑菇汤套装", 5, "将优雅融于烹饪，让精致存在于生活各处。", {15,28,17}},
	{11000577, "吟奏塔可套装", 4, "香脆的面皮夹杂着丰富的馅料，美味值能打一百分。", {10,28}},
	{11000578, "勇者大冒险套装", 5, "冒险的途中，最快乐的就是与伙伴们同行。", {29,15}},
	{11000579, "火线压制套装", 4, "小心！她的火线压制可是敌我不分的！", {29,26,15}},
	{11000580, "森之精灵套装", 5, "隐藏身份的精灵女王，居然成为了与勇者同行的队友。", {29,26,17}},
	{11000581, "少女诉说套装", 3, "副本世界的引导员，负责引领新玩家适应副本的世界。", {1}},
	{11000582, "副本维修工套装", 3, "副本世界的首席维修工，梦想是能睡个好觉。", {29,1}},
	{11000583, "金牛座守望套装", 4, "沉寂已久的心再次跳动，这次重逢能否成功缔结缘分？", {30,17}},
	{11000584, "金牛座收藏套装", 4, "企图收藏一切美丽的她，遇到了一位美丽的少年。", {30,17}},
	{11000585, "暗影潜行套装", 4, "为什么又有敌人在拦路？他真的生气了！", {29,15,17}},
	{11000586, "月夜魅影套装", 5, "只要有你在身边，整个世界都是我们的舞台。", {15,26}},
	{11000587, "恋心魔术套装", 5, "想要成为专属的唯一，想要永远不分离。", {15,26}},
	{11000588, "梦向云霄套装", 4, "风拦不住云，困难拦不住他那颗想飞的心。", {15,17}},
	{11000589, "巧思筑梦套装", 4, "双脚无法到达的地方，想象力却可以畅行无阻。", {15,17}},
	{11000590, "悠闲海獭套装", 4, "悠然自得的海獭少年，每日漂浮在海面上，充当奥比的海洋安全员。", {1,7}},
	{11000591, "活力海豹套装", 4, "充满活力的海豹少女，每日奔走在沙滩上，为大家筹备更有趣的活动。", {1,7}},
	{11000592, "大地之神套装", 5, "经历无数次生命的轮回，为海陆祈福，是她不渝的使命。", {26,17,20}},
	{11000593, "深海之鲸套装", 5, "游遍深海，寻觅曾经的朋友，是他心中的执念。", {26,17,20}},
	{11000594, "魔仙美琪套装", 4, "活泼开朗的小魔仙凌美琪，总是积极热心地冲在最前面。", {20,25}},
	{11000595, "沉思侦探套装", 3, "这位侦探并不沉默，他只是习惯于陷入沉思。", {15,25}},
	{11000596, "魔仙美雪套装", 4, "沉稳聪明的小魔仙凌美雪，不管是读书还是魔法都很擅长。", {20,25}},
	{11000597, "聪敏侦探套装", 3, "侦探小姐初出茅庐，却有着无可比拟的聪明头脑。", {15}},
	{11000598, "纯真爱意套装", 4, "静谧的苹果园里，她的心第一次萌生纯真的爱意。", {31}},
	{11000599, "优雅丽影套装", 3, "穿上永不过时的经典套装，她总能成为岛上一道优雅的风景线。", {25}},
	{11000600, "魔仙女王套装", 6, "守护着魔仙堡的女王，高贵美丽的她受到所有魔仙的崇敬。", {20,25}},
	{11000601, "小黑魔仙套装", 6, "只要坚持内心的善良，小黑魔仙也可以是最棒的小魔仙！", {20,25}},
	{11000602, "萌二甜梦套装", 5, "睡觉时间到，要乖乖上床啦……才怪！", {1,25}},
	{11000603, "魔仙小蓝套装", 5, "为了完成女王的任务，就算是别扭王子也能克服！", {20,25}},
	{11000604, "游乐王子套装", 5, "我不会帮任何人，当然也不会帮那个啰嗦老太婆。", {20,25}},
	{11000605, "轻拨心弦套装", 4, "静谧的苹果园里，他的心感受到了前所未有的悸动。", {31}},
	{11000606, "萌六睡意套装", 5, "虽然不知道为什么睡到半夜突然要开睡衣派对，但也挺有趣的。", {1,25}},
	{11000607, "真理回响套装", 4, "纯真的女孩，是世界真理的化身。", {31}},
	{11000608, "朽木生花套装", 5, "埋葬可以带来新生。", {8,19,5}},
	{11000609, "神秘学者套装", 4, "年轻的学者，渴望追寻世界的真理。", {15,31}},
	{11000610, "MOCO潮音套装", 5, "为了给她创作出最合适的音乐，他愿意进行新的尝试。", {32,17,25}},
	{11000611, "新芽破土套装", 5, "束缚要靠自己冲开。", {8,19,5}},
	{11000612, "群星盛宴套装", 5, "名为“发现”的魔法生效，美丽的灵魂在舞会中闪耀。", {12,3}},
	{11000613, "狼族少年套装", 4, "少年内心的热情与希望，如同烈火般在困境中熊熊燃烧，照亮前行的道路。", {10,17}},
	{11000614, "奶咖小狗套装", 4, "满满的热情和心意，是她的秘密配方！", {1,17}},
	{11000615, "奥斯卡赛车套装", 4, "“还没有哪场比赛是我赢不了的！”", {17,7,25}},
	{11000616, "美琪日常套装", 3, "美琪用无畏的心态和乐观的笑容迎接每一天。", {1,25}},
	{11000617, "美雪日常套装", 3, "心思细腻的美雪，总能从生活小事里发现有趣的思考。", {1,25}},
	{11000618, "MIA赛车套装", 4, "甜美乖巧的小公主，一旦坐上赛车，立刻展现出另一番风采。", {17,7,25}},
	{11000619, "盛夏海岸套装", 3, "海风卷起浪花在沙滩上跳跃，送出一起玩耍的邀约。", {1}},
	{11000620, "缤纷夏日套装", 3, "阳光像彩色糖果一样洒落四处，每一刻都是甜美的夏日乐章。", {1}},
	{11000621, "小蓝日常套装", 3, "今天的小蓝又会闹出什么可爱的小糊涂事件呢？", {1,25}},
	{11000622, "时沙掠影套装", 5, "倒转时间的沙漏，施展群星的魔法，发现美丽的灵魂。", {12,3}},
	{11000623, "严莉莉日常套装", 3, "找到了内心的答案，严莉莉眼中的风景似乎也变得不同了！", {1,25}},
	{11000624, "YUMMY梦之羽套装", 5, "手中的彩带棒随着音乐舞动，开幕式表演正式开始。", {17,7,25}},
	{11000625, "金砂凯旋套装", 4, "他将永远高居凯旋的战车之上，带领子民走向胜利与辉煌。", {10,31}},
	{11000626, "坚持不懈套装", 3, "每次训练，倾注全部心血，为的就是站上赛场的这一刻。", {1,7}},
	{11000627, "青春无悔套装", 3, "在比赛中尽情挥洒汗水，为青春留下难忘的印记！", {1,7}},
	{11000628, "动漫社学妹套装", 4, "动漫社的咖啡厅活动上，学妹化身可爱的小女仆。", {15,3}},
	{11000629, "命运之轮套装", 4, "接受来自命运的礼物，她侧耳倾听命运之轮的回响。", {10,31}},
	{11000630, "理学院套装", 5, "理思无尽，每个问题都可能是一扇通往新世界的大门，每个答案都孕育着更多的疑问与可能。", {20,11}},
	{11000631, "动漫社学长套装", 4, "动漫社的咖啡厅活动上，学长打扮成优雅的执事。", {15}},
	{11000632, "梅花护卫套装", 4, "她怀着勇敢的心，捍卫镜之国的每一份美好与纯真。", {3}},
	{11000633, "文学院套装", 5, "文韵沁心，犹如清泉入心，为每一个渴望美的灵魂带来滋养与启迪。", {11}},
	{11000634, "扭蛋妹妹套装", 4, "在快乐因子的感染下，游乐园里的扭蛋机神奇地变身为一位活泼可爱的扭蛋女孩。", {18}},
	{11000635, "网球新星套装", 3, "一颗冉冉升起的新星，正在闪耀赛场！", {1,7}},
	{11000636, "棒球英才套装", 3, "棒球场上潜力无限的英才，正蓄势待发！", {1,7}},
	{11000637, "红桃心镜套装", 5, "皇后的存在，是为了让王国的子民重获更多的快乐。", {12,3}},
	{11000638, "开学晨光套装", 3, "洒满校园的晨光，让新学期的一切充满希望。", {15,25}},
	{11000639, "校园序章套装", 3, "新学期的校园，每一个相遇都是久别重逢的温暖和期待。", {15,25}},
	{11000640, "隐士独思套装", 4, "不急不躁，探寻智慧与真理。", {31}},
	{11000641, "糖果兔兔套装", 5, "最快乐的乐园旋转木马，有最会拍照的快乐宣传员！", {18}},
	{11000642, "扭蛋哥哥套装", 4, "原来，游乐园里不止一台扭蛋机变身了，那个扭蛋女孩好像还叫他“哥哥”呢！", {18}},
	{11000643, "正义之光套装", 4, "不偏不倚，做出公平而正当的决定。", {31}},
	{11000644, "通讯使者套装", 4, "听说是戴罪立功的新手通讯使者，但似乎关于他过去的一切都是保密的。", {18}},
	{11000645, "方块护卫套装", 4, "无论风雨多大，他都愿为镜之国撑起一片安宁的天空。", {3}},
	{11000646, "跳跳萌兔套装", 5, "最刺激的游乐园过山车，有最调皮的秩序维护员！", {18}},
	{11000647, "白棋梦镜套装", 5, "王的诞生，是为了让王国的子民不再失去更多的快乐。", {12,3}},
	{11000648, "电话精灵套装", 4, "掌管通讯世界秩序的电话精灵，正在接通信号。", {18}},
	{11000649, "炫酷熊猫套装", 4, "喜欢装酷的少年，最爱的动物却是萌萌的熊猫。", {17}},
	{11000650, "夜美俏皮套装", 4, "俏皮可爱的少女，仿佛是冬日雪地里的精灵。", {1}},
	{11000651, "开朗愚者套装", 4, "游乐园里的表演者，永远带着无忧无虑的乐观笑容。", {31}},
	{11000652, "复兴潮流套装", 3, "一不小心回到过去，唤醒失去的记忆。", {9,1}},
	{11000653, "重返旧忆套装", 3, "回忆往事，真想好好安慰当时的自己。", {9,1}},
	{11000654, "庄园针线套装", 4, "即使被困在庄园无法离开，她也绝对不会放下手中的针线。", {15,5}},
	{11000655, "倒吊之心套装", 4, "游乐园里的表演者，能倒吊着完成各种高难度演出。", {31}},
	{11000656, "幽灵骑士套装", 4, "骑士为求胜利，不惜与恶魔签下契约。然而，曾为胜利欢呼的子民，如今又有谁还记得他？", {23,31}},
	{11000657, "魅影心魔套装", 4, "黑暗中的魅影，伴随着迷雾和低语潜入生灵们的内心深处。", {23,31}},
	{11000658, "庄园逃生者套装", 3, "她是被困在庄园中的玩偶，不断尝试着逃离这座庄园。", {1,5}},
	{11000659, "异瞳谜影套装", 5, "你好，我的新玩……新客人，欢迎进入“我”的庄园。", {19,5}},
	{11000660, "庄园哀歌套装", 5, "绷带缝补残躯，善良的灵魂在悄悄修复破碎的庄园。", {19,5}},
	{11000661, "庄园隐匿者套装", 3, "他是被困在庄园中的玩偶，每天胆战心惊地躲在最隐秘的角落里。", {1,5}},
	{11000662, "庄园夜巡套装", 4, "面具遮挡真实的面容，黑色是他夜巡时最好的伪装。", {15,5}},
	{11000663, "呆萌宝贝套装", 5, "牛奶是世界上最美味的食物。", {25}},
	{11000664, "魔焱蛮牛套装", 4, "魔焱和利斧，帮助好战不羁的魔王，在妖界打出属于自己的赫赫威名。", {2,23}},
	{11000665, "狐心妙算套装", 4, "心计和智慧，助她将财富化作笔墨，在妖界写下属于自己的传奇故事。", {2,23}},
	{11000666, "莉卡套装", 3, "莉卡同款套装。", {16,17}},
	{11000667, "温柔公主套装", 4, "温柔和善良就是她的力量。", {3}},
	{11000668, "吹散烦恼套装", 4, "分享一个变开心的魔法：感到沮丧时，就去洗个热水澡吧！让水流洗去不愉快，让风吹散烦恼。", {1,14,25}},
	{11000669, "泡泡时光套装", 5, "世上最舒服的事情就是泡澡澡哦！", {25}},
	{11000670, "慵懒午后套装", 4, "无需追赶时间，也不必在意外界，请好好享受当下属于自己的慵懒时光。", {1,14,25}},
	{11000671, "妖火之心套装", 3, "在高手林立的妖界，她这个无名小妖，誓要闯出一片天地！", {2,1,23}},
	{11000672, "莉卡个性套装", 4, "莉卡的同款套装。", {2,16}},
	{11000673, "竹香清新套装", 3, "住在竹林里的少女，身上时时带着竹子的清香。", {2,25}},
	{11000674, "卡诺王子套装", 4, "优雅与温和就是他的风采。", {3}},
	{11000675, "月涟幻梦套装", 4, "她是月亮女神，以月相的变化指引每一个灵魂。", {31}},
	{11000676, "毛茸茸精灵套装", 5, "诞生自白日梦小姐收到礼物时的喜悦情绪，他很喜欢赠送礼物，每次赠予都是幸福在传递。", {1,14,25}},
	{11000677, "莹骨妙颜套装", 5, "剔透骨，倾城颜。万千传闻皆可怖，殊知有情亦有心。", {2,23}},
	{11000678, "闲逸之妖套装", 3, "在强手如云的妖界，他这个无名小妖，只想悠闲度日。", {2,1,23}},
	{11000679, "银焰焚寂套装", 5, "看炉童子，孤寂千年。窃宝甘堕自在妖，不做炉前傀儡仙。", {2,23}},
	{11000680, "灵魂审判套装", 4, "他是月神座下的使者，以金色的号角审判每一个灵魂。", {31}},
	{11000681, "夜影星辰套装", 4, "如同夜空中的北极星，她总是用温柔的光芒为迷途者指引方向。", {31}},
	{11000682, "智慧圣者套装", 4, "在生命的旅途中，他用智慧照亮前行的路。", {31}},
	{11000683, "焦绿侠女套装", 4, "忙得停不下来的焦绿侠女，总是对许多事感到很焦虑。", {25,21,2}},
	{11000684, "卡宾王子套装", 4, "高贵和勇敢都是他的勋章。", nil},
	{11000685, "晚安精灵套装", 5, "诞生于白日梦小姐睡梦时的片刻情绪中，她拥抱每一个需要温暖的伙伴。", {1,14,25}},
	{11000686, "素心济世套装", 4, "塔下赎罪千年，白蛇悟道成仙。从前她痴爱一人，如今她博爱世人。", {2,23}},
	{11000687, "摩登风范套装", 3, "穿上永不过时的经典时装，他无时无刻不散发着独特的摩登气质。", nil},
	{11000688, "佛系隐士套装", 4, "无论面对什么困难，隐士永远都能保持情绪稳定。", {25,21,2}},
	{11000689, "星光梦境套装", 3, "情绪总得找个出口，不如让它们在夜晚化作一场梦，在梦里释放吧！", {20,25}},
	{11000690, "玉蛇盘福套装", 3, "蛇年到，福气绕。", {2,21}},
	{11000691, "金蛇献瑞套装", 3, "金蛇献瑞，喜满乾坤。", {2,21}},
	{11000692, "九首倾澜套装", 5, "抬手间倾覆沧海的九首水蛇仙，为凡间的温暖而感到茫然失措。", {2,23}},
	{11000693, "冬日暖心套装", 3, "天气变冷了，情绪也好像跟着掉进了冰窟。但别担心，总有方法能暖暖地度过这个冬天。", {1,25}},
	{11000694, "万物生长套装", 4, "她总说：“大地的馈赠要与万物分享，才能生生不息。”", {3,31}},
	{11000695, "腾翼凌云套装", 4, "腾蛇展翅，少年凌云志。他的神仙准则就一条，打服再说。", {2,23}},
	{11000696, "晨曦之翼套装", 4, "他带来新的希望，如同破晓时分的第一缕光芒。", {3,31}},
	{11000697, "热情掌柜套装", 5, "直爽的热情掌柜，认为沟通的最高艺术就是有话直说。", {25,21,2}},
	{11000698, "火树银花套装", 3, "火树银花不夜天，烟花飞舞贺新春。", {2}},
	{11000699, "江湖捕快套装", 5, "累点很低，全靠硬撑的江湖捕快，依旧保持微笑努力生活。", {25,21,2}},
	{11000700, "花语心事套装", 5, "关于那些永恒的花，以及说不出口的话。", {12,22,26}},
	{11000701, "旺火祈福锦绣套", 3, "旺火祈福庆平安，载歌载舞贺风华。", {2}},
	{11000702, "欲染青鳞套装", 5, "作恶多端的蛇精终食恶果，赎清罪孽后迷途知返，重求仙途。", {2,23,17}},
	{11000703, "森林精灵套装", 4, "森林里的快乐寻宝家，探索每一个让眼睛发光的小秘密！", {1,26}},
	{11000704, "铃兰精灵套装", 4, "诞生于铃兰花中的精灵，用自己独特的方式表达着对自然的喜爱。", {12,26}},
	{11000705, "击鼓迎春祥云套", 3, "击鼓迎春庆团圆，锣鼓喧天好运来。", {2}},
	{11000706, "永恒之春套装", 4, "她是时光长河中的小小魔术师，在永恒的莫比乌斯环上，变出一个又一个春天。", {31,26}},
	{11000707, "暖阳使者套装", 4, "光与温暖的使者，将太阳的笑意播撒到世间每个需要温暖的角落。", {31,26}},
	{11000708, "花艺新芽套装", 3, "作为花艺工作室的新手学徒，他每天都手忙脚乱。", {1,26}},
	{11000709, "朔漠神女套装", 6, "抟土造人，炼石补天。女娲所愿，不过天地永固，众生安宁。", {2,23}},
	{11000710, "煌宇莲华套装", 6, "一画开天，上下求索。伏羲之举，只为开先民之智，传万世之慧。", {2,23}},
	{11000711, "心花盛放套装", 5, "她用充满温度的双手，让每一朵花都找到最自然的位置。", {12,22,26}},
	{11000712, "Rabeea兔兔套装", 4, "向往友情却孤独躲在角落的垂耳兔Rebeea，她拥有最纯真善良的内心。", {25,26}},
	{11000713, "花艺悠然套装", 3, "作为花艺工作室的资深学徒，她处理任何事情都游刃有余。", {1,26}},
	{11000714, "田园童话套装", 5, "用纯真和爱意将田园生活变成一幅美好的童话画卷。", {8}},
	{11000715, "欢趣庆典橙套装", 5, "等等我，庆典可不能少了活泼的我！", {15,22}},
	{11000716, "欢趣庆典蓝套装", 5, "等等我，庆典可不能少了可爱的我！", {15,22}},
	{11000717, "欢趣庆典紫套装", 5, "等等我，庆典可不能少了优雅的我！", {15,22}},
	{11000718, "欢乐庆典橙套装", 5, "跑快点，要赶不上热烈的庆典了！", {15,22}},
	{11000719, "欢乐庆典蓝套装", 5, "跑快点，要赶不上新奇的庆典了！", {15,22}},
	{11000720, "欢乐庆典紫套装", 5, "跑快点，要赶不上华丽的庆典了！", {15,22}},
	{11000721, "FLORA精灵套装", 4, "心之森里自由可爱的花精灵FLORA，是MiMiA公主最可靠的伙伴。", {25,26}},
	{11000722, "闪耀之兔套装", 5, "看见闪耀，才能成为闪耀。", {25,4,19,12}},
	{11000723, "牧野追梦套装", 5, "在金色牧场上奔驰，渴望有一天成为真正的牛仔。", {8}},
	{11000724, "Rabeea闹钟套装", 3, "背着闹钟的Rabeea，总是一副还没睡够的样子。", {25}},
	{11000725, "元气橘光套装", 4, "用创意和活力，将生活的每一个角落都变成独特气息的展示台。", {1,28}},
	{11000726, "心森之灵套装", 3, "作为心之森的树精灵，他最喜欢的事就是收集森林里各种美丽的树叶。", {1,26,8}},
	{11000727, "钻石之泪套装", 5, "这天，她碎裂的世界里，透进了温暖的光。", {25,4,19,12}},
	{11000728, "淡雅香韵套装", 5, "沉稳如松柏，清雅如木香。", {26}},
	{11000729, "Rabeea圆周舞曲", 3, "三角尺、圆尺、还有各式各样的尺子，能否准确测量出Rabeea的可爱程度？", {25}},
	{11000730, "甜馨牧语套装", 4, "用记忆调制香气，用嗅觉捕捉情感。", {1}},
	{11000731, "心森蔓语套装", 3, "作为心之森的藤蔓精灵，她最喜欢的事就是爬上最高的地方，欣赏森林最梦幻美妙的模样。", {1,26,8}},
	{11000732, "Rabeea卷笔刀套", 3, "打扮成铅笔一样的Rabeea，简直就是文具盒里逃出来的小精灵。", {25}},
	{11000733, "月光祭司套装", 4, "连接天空与大地的一道月光桥梁，将智慧的星火传递给每一个寻求指引的灵魂。", {31}},
	{11000734, "破妄使者套装", 4, "他是划破虚妄的闪电，是骄傲者的清醒剂。", {31}},
	{11000735, "茶记靓女套装", 4, "超爱港剧港影的茶餐厅老板，比她研发料理速度更快的是她换男神的速度！", {9}},
	{11000736, "港影之星套装", 4, "真诚和善的巨星男演员，比他获奖速度更快的是他长胖的速度！", {9,1}},
	{11000737, "梦幻MiMiA套装", 5, "因为遗忘心愿而失去力量的MiMiA公主，决心要找回守护森林的魔法和重要的朋友。", {25,19}},
	{11000738, "均衡之灵套装", 4, "节制不是压制，而是在于找到真正的均衡。", {31}},
	{11000739, "摩登港城套装", 3, "离开家乡来到港城摆摊的少女，她心中对于未来怀抱着无限期许。", {1,9}},
	{11000740, "港城风潮套装", 3, "来到港城追梦的年轻小演员，目前只能出演不露脸的远景龙套。", {1,9}},
	{11000741, "炫音DISCO套装", 5, "放下一切、执着追求音乐梦想的少年，最终成为了港城里最红的天王歌星。", {6,22,9}},
	{11000742, "旷野之力套装", 4, "百兽少年王，保持着野性本真的同时，也掌握了控制力量的智慧。", {31}},
	{11000743, "璀璨芳华套装", 5, "她调制了无数香氛，但最令她自豪的，还是她对自己独特魅力的塑造。", {4,12}},
	{11000744, "纯真米卷套装", 5, "因为回应愿望而醒来的许愿精灵米卷，想要再次唤醒那个闪闪发光的愿望。", {25,26}},
	{11000745, "旧港恋旅套装", 5, "从未来穿越而来的她，在港城大荧幕上塑造了永恒的经典。", {9,12}},
	{11000746, "电波Browny套装", 4, "代码、像素等电子元素相互交织，构建出只属于Browny的纯粹世界。", {25,32}},
	{11000747, "鬼马yoyo套装", 4, "yoyo总有各种古灵精怪的想法，她最喜欢随心所欲地调制出奇怪的魔药。", {25,20}},
	{11000748, "暖阳时光套装", 3, "暖阳时光里，旧的不会被忘记，而新的正在发生。", {25}},
	{11000749, "小象添添套装", 5, "我叫添添，爱吃刨冰，爱宅家追剧，还爱……网购。", {25,1}},
	{11000750, "小猪猪米套装", 5, "我是猪米，爱夏天，爱汽水，更爱冰爽的猪米。", {25,1}},
	{11000751, "碎花浪漫套装", 3, "碎花和蝴蝶结，碰撞出浪漫的季节。", {25}},
	{11000752, "奇想nono套装", 5, "总是活在自己世界里的nono，即使会被他人评价“奇怪”，依然勇敢地做自己。", {25,5}},
	{11000753, "摇滚momo套装", 5, "平时乖巧礼貌的momo，原来有一颗摇滚不羁的心。", {25,6}},
	{11000754, "心灵碎片套装", 5, "在表达中释放破碎的过去，在创造中治愈现在的自己。", {20}},
	{11000755, "印韵拾光套装", 3, "刚刚接触手帐的小奥比，喜欢用各种图案的印章把本子盖得满满当当。", {11,1}},
	{11000756, "青葱绘梦套装", 5, "即将毕业离开社团的学姐，手帐本里记录的美好，都是支撑她独自前往未来的力量。", {11}},
	{11000757, "灵感飞鸟套装", 4, "灵感飞鸟会将人们许下的愿望都绘制进大家的梦乡。", {18,19}},
	{11000758, "绒绒布贴套装", 4, "最爱毛茸茸的萌萌学妹，连做手帐的贴纸也要用毛绒款的。", {11}},
	{11000759, "彩墨纪行套装", 5, "这个毕业季，要和社团的学姐告别了。虽然心中充满不舍，但他写下的全部是祝福。", {11}},
	{11000760, "彩贴青春套装", 3, "刚刚接触手帐的小奥比，喜欢用各种花里胡哨的贴纸装点手帐。", {11,1}},
	{11000761, "梦幻彩球套装", 3, "把梦幻的小彩球装饰在身上，变成传递快乐的使者吧！", nil},
	{11000762, "灵感泡泡套装", 4, "“让我用泡泡来创造最绚烂的画作吧。”", {18,19}},
	{11000763, "牙仙天使套装", 5, "在梦境与现实之间穿梭，用爱心和智慧，教会孩子们的珍视自己每一颗牙齿。", {19,20,15}},
	{11000764, "执念成囚套装", 4, "一个渴望被理解的扭曲灵魂。", {4,9}},
	{11000765, "炫彩假日套装", 3, "这个假日，因为自由和快乐而变得炫彩。", nil},
	{11000766, "自由之歌套装", 4, "无论是谁，都无法阻挡她向往自由的心。", {4,9}},
	{11000767, "糖果恶魔套装", 5, "“我这里有全世界最好吃的糖果哦！”", {19,20,4}},
	{11000768, "胶带心晴套装", 4, "热爱用胶带来拼贴手帐的学弟，熊生目标是囤更多新的胶带。", {11}},
	{11000769, "奇遇少女套装", 4, "无论身处何方，她都能将简单的日常转化为充满魔力的冒险。", {8}},
	{11000770, "灵光一闪套装", 5, "她是创意之火的化身，让每一颗遇见她的心，都能绽放自己的创意之光。", {20}},
	{11000771, "远宙拾星套装", 4, "木星的化身，他珍藏的每一颗陨石，都记录着宇宙的亘古万年。", {23,27}},
	{11000772, "爱之蜕变套装", 6, "她曾热衷于制造浪漫，近乎狂热。现在她已学会放下执念，静静品味爱意。", {23,27}},
	{11000773, "红粉都会套装", 5, "美丽的反差角色，有着高达百分之百的任务成功率。", {12,9}},
	{11000774, "魔书精灵绿套装", 5, "魔书中的每一页都是她亲笔记录的漫长岁月。", {20}},
	{11000775, "魔书精灵红套装", 5, "魔书中的每一页都是她亲身经历的漫长岁月。", {20}},
	{11000776, "魔书精灵蓝套装", 5, "魔书中的每一页都是她亲眼见证的漫长岁月。", {20}},
	{11000777, "魔书使者绿套装", 5, "在漫长岁月里，守护魔书是他无尽的使命。", {20}},
	{11000778, "魔书使者红套装", 5, "在漫长岁月里，守护魔书是他不变的使命。", {20}},
	{11000779, "魔书使者蓝套装", 5, "在漫长岁月里，守护魔书是他亘古的使命。", {20}},
	{11000780, "战意永燃套装", 4, "火星的化身，熊熊燃烧的战意源自她不灭的守护之心。", {23,27}},
	{11000781, "无心骑士套装", 4, "被遗忘的无心骑士，找到了“心的力量”。", {19}},
	{11000782, "玫瑰冰晶套装", 5, "隐身在暗处的密令指挥者，为执行任务者传递最准确的信息。", {12,9}},
	{11000783, "采蜜绒蜂套装", 5, "“我不会错过任何一个传播花粉的季节。”", {8,19}},
	{11000784, "", 0, "", nil},
	{11000785, "彩虹魔女套装", 5, "怎么样，我的三只小猫可爱吧？如果你肯帮我试一试魔药的效果，我就让你摸摸它们哦！", {25,20}},
	{11000786, "蜜罐绒绒套装", 5, "暖洋洋的阳光洒在身上，手中的蜂蜜罐儿满溢着甜美的蜂蜜，这是小蜜蜂最幸福的时刻。", {8,19}},
	{11000787, "甜酷誓约套装", 5, "“甜美是我的天赋，扮酷是我的时尚态度！”", {6,1}},
	{11000788, "金辉魔法套装", 5, "我不会放过任何破坏规则的家伙，但如果是小猫的话……它能有什么错？", {25,20}},
	{11000789, "4.1彼得潘套装", 4, "", nil},
	{11000790, "雨滴作曲家套装", 4, "她善于捕捉雨落的节奏，谱写出自然与音乐碰撞的交响乐章。", {12,9}},
	{11000791, "4.1兑换海盗女装", 3, "", nil},
	{11000792, "4.1兑换海盗男装", 3, "", nil},
	{11000793, "粉熊邀约套装", 5, "“希望今天的我，会是她喜欢的风格。”", {6,1}},
	{11000794, "星璨之光套装", 4, "脑海中的奇思妙想，化作璀璨的灵感之星。", {19,25}},
	{11000795, "雨幕舞者套装", 4, "雨幕中的舞者，在自然馈赠的伴奏中，舒展着身姿。", {12,9}},
	{11000796, "星焰之光套装", 4, "当灵感之星变成现实时，会绽放成绚烂的烟花。", {19,25}},
	{11000797, "自由之风套装", 6, "无拘无束，自由飞翔。", {23,27}},
	{11000798, "时间之弦套装", 5, "穿越次元，掌管时间的神行者。", {23,27,22}},
	{11000799, "生命之息套装", 5, "联结自然，诠释生命，传递生机的使者。", {23,27}},
	{11000800, "4.1小叮当套装", 0, "", nil},
	{11000801, "4.1偷心海盗女装", 5, "", nil},
	{11000802, "4.1海盗船长男装", 5, "", nil},
}

local t_suit = {
	[11000004] = dataList[1],
	[11000005] = dataList[2],
	[11000010] = dataList[3],
	[11000013] = dataList[4],
	[11000014] = dataList[5],
	[11000019] = dataList[6],
	[11000020] = dataList[7],
	[11000021] = dataList[8],
	[11000022] = dataList[9],
	[11000023] = dataList[10],
	[11000024] = dataList[11],
	[11000025] = dataList[12],
	[11000026] = dataList[13],
	[11000027] = dataList[14],
	[11000028] = dataList[15],
	[11000029] = dataList[16],
	[11000030] = dataList[17],
	[11000031] = dataList[18],
	[11000032] = dataList[19],
	[11000033] = dataList[20],
	[11000034] = dataList[21],
	[11000035] = dataList[22],
	[11000036] = dataList[23],
	[11000037] = dataList[24],
	[11000038] = dataList[25],
	[11000039] = dataList[26],
	[11000040] = dataList[27],
	[11000041] = dataList[28],
	[11000042] = dataList[29],
	[11000043] = dataList[30],
	[11000044] = dataList[31],
	[11000045] = dataList[32],
	[11000046] = dataList[33],
	[11000047] = dataList[34],
	[11000048] = dataList[35],
	[11000049] = dataList[36],
	[11000050] = dataList[37],
	[11000051] = dataList[38],
	[11000052] = dataList[39],
	[11000053] = dataList[40],
	[11000054] = dataList[41],
	[11000055] = dataList[42],
	[11000056] = dataList[43],
	[11000057] = dataList[44],
	[11000058] = dataList[45],
	[11000059] = dataList[46],
	[11000060] = dataList[47],
	[11000061] = dataList[48],
	[11000062] = dataList[49],
	[11000063] = dataList[50],
	[11000064] = dataList[51],
	[11000065] = dataList[52],
	[11000066] = dataList[53],
	[11000067] = dataList[54],
	[11000068] = dataList[55],
	[11000069] = dataList[56],
	[11000070] = dataList[57],
	[11000071] = dataList[58],
	[11000072] = dataList[59],
	[11000073] = dataList[60],
	[11000074] = dataList[61],
	[11000075] = dataList[62],
	[11000076] = dataList[63],
	[11000077] = dataList[64],
	[11000078] = dataList[65],
	[11000079] = dataList[66],
	[11000080] = dataList[67],
	[11000081] = dataList[68],
	[11000082] = dataList[69],
	[11000083] = dataList[70],
	[11000084] = dataList[71],
	[11000085] = dataList[72],
	[11000086] = dataList[73],
	[11000087] = dataList[74],
	[11000088] = dataList[75],
	[11000089] = dataList[76],
	[11000090] = dataList[77],
	[11000091] = dataList[78],
	[11000092] = dataList[79],
	[11000093] = dataList[80],
	[11000094] = dataList[81],
	[11000095] = dataList[82],
	[11000096] = dataList[83],
	[11000097] = dataList[84],
	[11000098] = dataList[85],
	[11000099] = dataList[86],
	[11000100] = dataList[87],
	[11000101] = dataList[88],
	[11000102] = dataList[89],
	[11000103] = dataList[90],
	[11000104] = dataList[91],
	[11000105] = dataList[92],
	[11000106] = dataList[93],
	[11000107] = dataList[94],
	[11000108] = dataList[95],
	[11000109] = dataList[96],
	[11000110] = dataList[97],
	[11000111] = dataList[98],
	[11000112] = dataList[99],
	[11000113] = dataList[100],
	[11000114] = dataList[101],
	[11000115] = dataList[102],
	[11000116] = dataList[103],
	[11000117] = dataList[104],
	[11000118] = dataList[105],
	[11000119] = dataList[106],
	[11000120] = dataList[107],
	[11000121] = dataList[108],
	[11000122] = dataList[109],
	[11000123] = dataList[110],
	[11000124] = dataList[111],
	[11000125] = dataList[112],
	[11000126] = dataList[113],
	[11000127] = dataList[114],
	[11000128] = dataList[115],
	[11000129] = dataList[116],
	[11000130] = dataList[117],
	[11000131] = dataList[118],
	[11000132] = dataList[119],
	[11000133] = dataList[120],
	[11000134] = dataList[121],
	[11000135] = dataList[122],
	[11000136] = dataList[123],
	[11000137] = dataList[124],
	[11000138] = dataList[125],
	[11000139] = dataList[126],
	[11000140] = dataList[127],
	[11000141] = dataList[128],
	[11000142] = dataList[129],
	[11000143] = dataList[130],
	[11000144] = dataList[131],
	[11000145] = dataList[132],
	[11000146] = dataList[133],
	[11000147] = dataList[134],
	[11000148] = dataList[135],
	[11000149] = dataList[136],
	[11000150] = dataList[137],
	[11000151] = dataList[138],
	[11000152] = dataList[139],
	[11000153] = dataList[140],
	[11000154] = dataList[141],
	[11000155] = dataList[142],
	[11000156] = dataList[143],
	[11000157] = dataList[144],
	[11000158] = dataList[145],
	[11000159] = dataList[146],
	[11000160] = dataList[147],
	[11000161] = dataList[148],
	[11000162] = dataList[149],
	[11000163] = dataList[150],
	[11000164] = dataList[151],
	[11000165] = dataList[152],
	[11000166] = dataList[153],
	[11000167] = dataList[154],
	[11000168] = dataList[155],
	[11000169] = dataList[156],
	[11000170] = dataList[157],
	[11000171] = dataList[158],
	[11000172] = dataList[159],
	[11000173] = dataList[160],
	[11000174] = dataList[161],
	[11000175] = dataList[162],
	[11000176] = dataList[163],
	[11000177] = dataList[164],
	[11000178] = dataList[165],
	[11000179] = dataList[166],
	[11000180] = dataList[167],
	[11000181] = dataList[168],
	[11000182] = dataList[169],
	[11000183] = dataList[170],
	[11000184] = dataList[171],
	[11000185] = dataList[172],
	[11000186] = dataList[173],
	[11000187] = dataList[174],
	[11000188] = dataList[175],
	[11000189] = dataList[176],
	[11000190] = dataList[177],
	[11000191] = dataList[178],
	[11000192] = dataList[179],
	[11000193] = dataList[180],
	[11000194] = dataList[181],
	[11000195] = dataList[182],
	[11000196] = dataList[183],
	[11000197] = dataList[184],
	[11000198] = dataList[185],
	[11000199] = dataList[186],
	[11000200] = dataList[187],
	[11000201] = dataList[188],
	[11000202] = dataList[189],
	[11000203] = dataList[190],
	[11000204] = dataList[191],
	[11000205] = dataList[192],
	[11000206] = dataList[193],
	[11000207] = dataList[194],
	[11000208] = dataList[195],
	[11000209] = dataList[196],
	[11000210] = dataList[197],
	[11000211] = dataList[198],
	[11000212] = dataList[199],
	[11000213] = dataList[200],
	[11000214] = dataList[201],
	[11000215] = dataList[202],
	[11000216] = dataList[203],
	[11000217] = dataList[204],
	[11000218] = dataList[205],
	[11000219] = dataList[206],
	[11000220] = dataList[207],
	[11000221] = dataList[208],
	[11000222] = dataList[209],
	[11000223] = dataList[210],
	[11000224] = dataList[211],
	[11000225] = dataList[212],
	[11000226] = dataList[213],
	[11000227] = dataList[214],
	[11000228] = dataList[215],
	[11000229] = dataList[216],
	[11000230] = dataList[217],
	[11000231] = dataList[218],
	[11000232] = dataList[219],
	[11000233] = dataList[220],
	[11000234] = dataList[221],
	[11000235] = dataList[222],
	[11000236] = dataList[223],
	[11000237] = dataList[224],
	[11000238] = dataList[225],
	[11000239] = dataList[226],
	[11000240] = dataList[227],
	[11000241] = dataList[228],
	[11000242] = dataList[229],
	[11000243] = dataList[230],
	[11000244] = dataList[231],
	[11000245] = dataList[232],
	[11000246] = dataList[233],
	[11000247] = dataList[234],
	[11000248] = dataList[235],
	[11000249] = dataList[236],
	[11000250] = dataList[237],
	[11000251] = dataList[238],
	[11000252] = dataList[239],
	[11000253] = dataList[240],
	[11000254] = dataList[241],
	[11000255] = dataList[242],
	[11000256] = dataList[243],
	[11000257] = dataList[244],
	[11000258] = dataList[245],
	[11000259] = dataList[246],
	[11000260] = dataList[247],
	[11000261] = dataList[248],
	[11000262] = dataList[249],
	[11000263] = dataList[250],
	[11000264] = dataList[251],
	[11000265] = dataList[252],
	[11000266] = dataList[253],
	[11000267] = dataList[254],
	[11000268] = dataList[255],
	[11000269] = dataList[256],
	[11000270] = dataList[257],
	[11000271] = dataList[258],
	[11000272] = dataList[259],
	[11000273] = dataList[260],
	[11000274] = dataList[261],
	[11000275] = dataList[262],
	[11000276] = dataList[263],
	[11000277] = dataList[264],
	[11000278] = dataList[265],
	[11000279] = dataList[266],
	[11000280] = dataList[267],
	[11000281] = dataList[268],
	[11000282] = dataList[269],
	[11000283] = dataList[270],
	[11000284] = dataList[271],
	[11000285] = dataList[272],
	[11000286] = dataList[273],
	[11000287] = dataList[274],
	[11000288] = dataList[275],
	[11000289] = dataList[276],
	[11000290] = dataList[277],
	[11000291] = dataList[278],
	[11000292] = dataList[279],
	[11000293] = dataList[280],
	[11000294] = dataList[281],
	[11000295] = dataList[282],
	[11000296] = dataList[283],
	[11000297] = dataList[284],
	[11000298] = dataList[285],
	[11000299] = dataList[286],
	[11000300] = dataList[287],
	[11000301] = dataList[288],
	[11000302] = dataList[289],
	[11000303] = dataList[290],
	[11000304] = dataList[291],
	[11000305] = dataList[292],
	[11000306] = dataList[293],
	[11000307] = dataList[294],
	[11000308] = dataList[295],
	[11000309] = dataList[296],
	[11000310] = dataList[297],
	[11000311] = dataList[298],
	[11000312] = dataList[299],
	[11000313] = dataList[300],
	[11000314] = dataList[301],
	[11000315] = dataList[302],
	[11000316] = dataList[303],
	[11000317] = dataList[304],
	[11000318] = dataList[305],
	[11000319] = dataList[306],
	[11000320] = dataList[307],
	[11000321] = dataList[308],
	[11000322] = dataList[309],
	[11000323] = dataList[310],
	[11000324] = dataList[311],
	[11000325] = dataList[312],
	[11000326] = dataList[313],
	[11000327] = dataList[314],
	[11000328] = dataList[315],
	[11000329] = dataList[316],
	[11000330] = dataList[317],
	[11000331] = dataList[318],
	[11000332] = dataList[319],
	[11000333] = dataList[320],
	[11000334] = dataList[321],
	[11000335] = dataList[322],
	[11000336] = dataList[323],
	[11000337] = dataList[324],
	[11000338] = dataList[325],
	[11000339] = dataList[326],
	[11000340] = dataList[327],
	[11000341] = dataList[328],
	[11000342] = dataList[329],
	[11000343] = dataList[330],
	[11000344] = dataList[331],
	[11000345] = dataList[332],
	[11000346] = dataList[333],
	[11000347] = dataList[334],
	[11000348] = dataList[335],
	[11000349] = dataList[336],
	[11000350] = dataList[337],
	[11000351] = dataList[338],
	[11000352] = dataList[339],
	[11000353] = dataList[340],
	[11000354] = dataList[341],
	[11000355] = dataList[342],
	[11000356] = dataList[343],
	[11000357] = dataList[344],
	[11000358] = dataList[345],
	[11000359] = dataList[346],
	[11000360] = dataList[347],
	[11000361] = dataList[348],
	[11000362] = dataList[349],
	[11000363] = dataList[350],
	[11000364] = dataList[351],
	[11000365] = dataList[352],
	[11000366] = dataList[353],
	[11000367] = dataList[354],
	[11000368] = dataList[355],
	[11000369] = dataList[356],
	[11000370] = dataList[357],
	[11000371] = dataList[358],
	[11000372] = dataList[359],
	[11000373] = dataList[360],
	[11000374] = dataList[361],
	[11000375] = dataList[362],
	[11000376] = dataList[363],
	[11000377] = dataList[364],
	[11000378] = dataList[365],
	[11000379] = dataList[366],
	[11000380] = dataList[367],
	[11000381] = dataList[368],
	[11000382] = dataList[369],
	[11000383] = dataList[370],
	[11000384] = dataList[371],
	[11000385] = dataList[372],
	[11000386] = dataList[373],
	[11000387] = dataList[374],
	[11000388] = dataList[375],
	[11000389] = dataList[376],
	[11000390] = dataList[377],
	[11000391] = dataList[378],
	[11000392] = dataList[379],
	[11000393] = dataList[380],
	[11000394] = dataList[381],
	[11000395] = dataList[382],
	[11000396] = dataList[383],
	[11000397] = dataList[384],
	[11000398] = dataList[385],
	[11000399] = dataList[386],
	[11000400] = dataList[387],
	[11000401] = dataList[388],
	[11000402] = dataList[389],
	[11000403] = dataList[390],
	[11000404] = dataList[391],
	[11000405] = dataList[392],
	[11000406] = dataList[393],
	[11000407] = dataList[394],
	[11000408] = dataList[395],
	[11000409] = dataList[396],
	[11000410] = dataList[397],
	[11000411] = dataList[398],
	[11000412] = dataList[399],
	[11000413] = dataList[400],
	[11000414] = dataList[401],
	[11000415] = dataList[402],
	[11000416] = dataList[403],
	[11000417] = dataList[404],
	[11000418] = dataList[405],
	[11000419] = dataList[406],
	[11000420] = dataList[407],
	[11000421] = dataList[408],
	[11000422] = dataList[409],
	[11000423] = dataList[410],
	[11000424] = dataList[411],
	[11000425] = dataList[412],
	[11000426] = dataList[413],
	[11000427] = dataList[414],
	[11000428] = dataList[415],
	[11000429] = dataList[416],
	[11000430] = dataList[417],
	[11000431] = dataList[418],
	[11000432] = dataList[419],
	[11000433] = dataList[420],
	[11000434] = dataList[421],
	[11000435] = dataList[422],
	[11000436] = dataList[423],
	[11000437] = dataList[424],
	[11000438] = dataList[425],
	[11000439] = dataList[426],
	[11000440] = dataList[427],
	[11000441] = dataList[428],
	[11000442] = dataList[429],
	[11000443] = dataList[430],
	[11000444] = dataList[431],
	[11000445] = dataList[432],
	[11000446] = dataList[433],
	[11000447] = dataList[434],
	[11000448] = dataList[435],
	[11000449] = dataList[436],
	[11000450] = dataList[437],
	[11000451] = dataList[438],
	[11000452] = dataList[439],
	[11000453] = dataList[440],
	[11000454] = dataList[441],
	[11000455] = dataList[442],
	[11000456] = dataList[443],
	[11000457] = dataList[444],
	[11000458] = dataList[445],
	[11000459] = dataList[446],
	[11000460] = dataList[447],
	[11000461] = dataList[448],
	[11000462] = dataList[449],
	[11000463] = dataList[450],
	[11000464] = dataList[451],
	[11000465] = dataList[452],
	[11000466] = dataList[453],
	[11000467] = dataList[454],
	[11000468] = dataList[455],
	[11000469] = dataList[456],
	[11000470] = dataList[457],
	[11000471] = dataList[458],
	[11000472] = dataList[459],
	[11000473] = dataList[460],
	[11000474] = dataList[461],
	[11000475] = dataList[462],
	[11000476] = dataList[463],
	[11000477] = dataList[464],
	[11000478] = dataList[465],
	[11000479] = dataList[466],
	[11000480] = dataList[467],
	[11000481] = dataList[468],
	[11000482] = dataList[469],
	[11000483] = dataList[470],
	[11000484] = dataList[471],
	[11000485] = dataList[472],
	[11000486] = dataList[473],
	[11000487] = dataList[474],
	[11000488] = dataList[475],
	[11000489] = dataList[476],
	[11000490] = dataList[477],
	[11000491] = dataList[478],
	[11000492] = dataList[479],
	[11000493] = dataList[480],
	[11000494] = dataList[481],
	[11000495] = dataList[482],
	[11000496] = dataList[483],
	[11000497] = dataList[484],
	[11000498] = dataList[485],
	[11000499] = dataList[486],
	[11000500] = dataList[487],
	[11000501] = dataList[488],
	[11000502] = dataList[489],
	[11000503] = dataList[490],
	[11000504] = dataList[491],
	[11000505] = dataList[492],
	[11000506] = dataList[493],
	[11000507] = dataList[494],
	[11000508] = dataList[495],
	[11000509] = dataList[496],
	[11000510] = dataList[497],
	[11000511] = dataList[498],
	[11000512] = dataList[499],
	[11000513] = dataList[500],
	[11000514] = dataList[501],
	[11000515] = dataList[502],
	[11000516] = dataList[503],
	[11000517] = dataList[504],
	[11000518] = dataList[505],
	[11000519] = dataList[506],
	[11000520] = dataList[507],
	[11000521] = dataList[508],
	[11000522] = dataList[509],
	[11000523] = dataList[510],
	[11000524] = dataList[511],
	[11000525] = dataList[512],
	[11000526] = dataList[513],
	[11000527] = dataList[514],
	[11000528] = dataList[515],
	[11000529] = dataList[516],
	[11000530] = dataList[517],
	[11000531] = dataList[518],
	[11000532] = dataList[519],
	[11000533] = dataList[520],
	[11000534] = dataList[521],
	[11000535] = dataList[522],
	[11000536] = dataList[523],
	[11000537] = dataList[524],
	[11000538] = dataList[525],
	[11000539] = dataList[526],
	[11000540] = dataList[527],
	[11000541] = dataList[528],
	[11000542] = dataList[529],
	[11000543] = dataList[530],
	[11000544] = dataList[531],
	[11000545] = dataList[532],
	[11000546] = dataList[533],
	[11000547] = dataList[534],
	[11000548] = dataList[535],
	[11000549] = dataList[536],
	[11000550] = dataList[537],
	[11000551] = dataList[538],
	[11000552] = dataList[539],
	[11000553] = dataList[540],
	[11000554] = dataList[541],
	[11000555] = dataList[542],
	[11000556] = dataList[543],
	[11000557] = dataList[544],
	[11000558] = dataList[545],
	[11000559] = dataList[546],
	[11000560] = dataList[547],
	[11000561] = dataList[548],
	[11000562] = dataList[549],
	[11000563] = dataList[550],
	[11000564] = dataList[551],
	[11000565] = dataList[552],
	[11000566] = dataList[553],
	[11000567] = dataList[554],
	[11000568] = dataList[555],
	[11000569] = dataList[556],
	[11000570] = dataList[557],
	[11000571] = dataList[558],
	[11000572] = dataList[559],
	[11000573] = dataList[560],
	[11000574] = dataList[561],
	[11000575] = dataList[562],
	[11000576] = dataList[563],
	[11000577] = dataList[564],
	[11000578] = dataList[565],
	[11000579] = dataList[566],
	[11000580] = dataList[567],
	[11000581] = dataList[568],
	[11000582] = dataList[569],
	[11000583] = dataList[570],
	[11000584] = dataList[571],
	[11000585] = dataList[572],
	[11000586] = dataList[573],
	[11000587] = dataList[574],
	[11000588] = dataList[575],
	[11000589] = dataList[576],
	[11000590] = dataList[577],
	[11000591] = dataList[578],
	[11000592] = dataList[579],
	[11000593] = dataList[580],
	[11000594] = dataList[581],
	[11000595] = dataList[582],
	[11000596] = dataList[583],
	[11000597] = dataList[584],
	[11000598] = dataList[585],
	[11000599] = dataList[586],
	[11000600] = dataList[587],
	[11000601] = dataList[588],
	[11000602] = dataList[589],
	[11000603] = dataList[590],
	[11000604] = dataList[591],
	[11000605] = dataList[592],
	[11000606] = dataList[593],
	[11000607] = dataList[594],
	[11000608] = dataList[595],
	[11000609] = dataList[596],
	[11000610] = dataList[597],
	[11000611] = dataList[598],
	[11000612] = dataList[599],
	[11000613] = dataList[600],
	[11000614] = dataList[601],
	[11000615] = dataList[602],
	[11000616] = dataList[603],
	[11000617] = dataList[604],
	[11000618] = dataList[605],
	[11000619] = dataList[606],
	[11000620] = dataList[607],
	[11000621] = dataList[608],
	[11000622] = dataList[609],
	[11000623] = dataList[610],
	[11000624] = dataList[611],
	[11000625] = dataList[612],
	[11000626] = dataList[613],
	[11000627] = dataList[614],
	[11000628] = dataList[615],
	[11000629] = dataList[616],
	[11000630] = dataList[617],
	[11000631] = dataList[618],
	[11000632] = dataList[619],
	[11000633] = dataList[620],
	[11000634] = dataList[621],
	[11000635] = dataList[622],
	[11000636] = dataList[623],
	[11000637] = dataList[624],
	[11000638] = dataList[625],
	[11000639] = dataList[626],
	[11000640] = dataList[627],
	[11000641] = dataList[628],
	[11000642] = dataList[629],
	[11000643] = dataList[630],
	[11000644] = dataList[631],
	[11000645] = dataList[632],
	[11000646] = dataList[633],
	[11000647] = dataList[634],
	[11000648] = dataList[635],
	[11000649] = dataList[636],
	[11000650] = dataList[637],
	[11000651] = dataList[638],
	[11000652] = dataList[639],
	[11000653] = dataList[640],
	[11000654] = dataList[641],
	[11000655] = dataList[642],
	[11000656] = dataList[643],
	[11000657] = dataList[644],
	[11000658] = dataList[645],
	[11000659] = dataList[646],
	[11000660] = dataList[647],
	[11000661] = dataList[648],
	[11000662] = dataList[649],
	[11000663] = dataList[650],
	[11000664] = dataList[651],
	[11000665] = dataList[652],
	[11000666] = dataList[653],
	[11000667] = dataList[654],
	[11000668] = dataList[655],
	[11000669] = dataList[656],
	[11000670] = dataList[657],
	[11000671] = dataList[658],
	[11000672] = dataList[659],
	[11000673] = dataList[660],
	[11000674] = dataList[661],
	[11000675] = dataList[662],
	[11000676] = dataList[663],
	[11000677] = dataList[664],
	[11000678] = dataList[665],
	[11000679] = dataList[666],
	[11000680] = dataList[667],
	[11000681] = dataList[668],
	[11000682] = dataList[669],
	[11000683] = dataList[670],
	[11000684] = dataList[671],
	[11000685] = dataList[672],
	[11000686] = dataList[673],
	[11000687] = dataList[674],
	[11000688] = dataList[675],
	[11000689] = dataList[676],
	[11000690] = dataList[677],
	[11000691] = dataList[678],
	[11000692] = dataList[679],
	[11000693] = dataList[680],
	[11000694] = dataList[681],
	[11000695] = dataList[682],
	[11000696] = dataList[683],
	[11000697] = dataList[684],
	[11000698] = dataList[685],
	[11000699] = dataList[686],
	[11000700] = dataList[687],
	[11000701] = dataList[688],
	[11000702] = dataList[689],
	[11000703] = dataList[690],
	[11000704] = dataList[691],
	[11000705] = dataList[692],
	[11000706] = dataList[693],
	[11000707] = dataList[694],
	[11000708] = dataList[695],
	[11000709] = dataList[696],
	[11000710] = dataList[697],
	[11000711] = dataList[698],
	[11000712] = dataList[699],
	[11000713] = dataList[700],
	[11000714] = dataList[701],
	[11000715] = dataList[702],
	[11000716] = dataList[703],
	[11000717] = dataList[704],
	[11000718] = dataList[705],
	[11000719] = dataList[706],
	[11000720] = dataList[707],
	[11000721] = dataList[708],
	[11000722] = dataList[709],
	[11000723] = dataList[710],
	[11000724] = dataList[711],
	[11000725] = dataList[712],
	[11000726] = dataList[713],
	[11000727] = dataList[714],
	[11000728] = dataList[715],
	[11000729] = dataList[716],
	[11000730] = dataList[717],
	[11000731] = dataList[718],
	[11000732] = dataList[719],
	[11000733] = dataList[720],
	[11000734] = dataList[721],
	[11000735] = dataList[722],
	[11000736] = dataList[723],
	[11000737] = dataList[724],
	[11000738] = dataList[725],
	[11000739] = dataList[726],
	[11000740] = dataList[727],
	[11000741] = dataList[728],
	[11000742] = dataList[729],
	[11000743] = dataList[730],
	[11000744] = dataList[731],
	[11000745] = dataList[732],
	[11000746] = dataList[733],
	[11000747] = dataList[734],
	[11000748] = dataList[735],
	[11000749] = dataList[736],
	[11000750] = dataList[737],
	[11000751] = dataList[738],
	[11000752] = dataList[739],
	[11000753] = dataList[740],
	[11000754] = dataList[741],
	[11000755] = dataList[742],
	[11000756] = dataList[743],
	[11000757] = dataList[744],
	[11000758] = dataList[745],
	[11000759] = dataList[746],
	[11000760] = dataList[747],
	[11000761] = dataList[748],
	[11000762] = dataList[749],
	[11000763] = dataList[750],
	[11000764] = dataList[751],
	[11000765] = dataList[752],
	[11000766] = dataList[753],
	[11000767] = dataList[754],
	[11000768] = dataList[755],
	[11000769] = dataList[756],
	[11000770] = dataList[757],
	[11000771] = dataList[758],
	[11000772] = dataList[759],
	[11000773] = dataList[760],
	[11000774] = dataList[761],
	[11000775] = dataList[762],
	[11000776] = dataList[763],
	[11000777] = dataList[764],
	[11000778] = dataList[765],
	[11000779] = dataList[766],
	[11000780] = dataList[767],
	[11000781] = dataList[768],
	[11000782] = dataList[769],
	[11000783] = dataList[770],
	[11000784] = dataList[771],
	[11000785] = dataList[772],
	[11000786] = dataList[773],
	[11000787] = dataList[774],
	[11000788] = dataList[775],
	[11000789] = dataList[776],
	[11000790] = dataList[777],
	[11000791] = dataList[778],
	[11000792] = dataList[779],
	[11000793] = dataList[780],
	[11000794] = dataList[781],
	[11000795] = dataList[782],
	[11000796] = dataList[783],
	[11000797] = dataList[784],
	[11000798] = dataList[785],
	[11000799] = dataList[786],
	[11000800] = dataList[787],
	[11000801] = dataList[788],
	[11000802] = dataList[789],
}

t_suit.dataList = dataList
local mt
if ClothesSuitDefine then
	mt = {
		__cname =  "ClothesSuitDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or ClothesSuitDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_suit