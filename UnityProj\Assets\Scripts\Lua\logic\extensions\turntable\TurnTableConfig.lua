module("logic.extensions.turntable.TurnTableConfig", package.seeall)
local TurnTableConfig = class("TurnTableConfig")

local poolConfig = ConfigLoader.New("act_161_pools")

local progressAward = ConfigLoader.New("act_161_process_reward")

local act_161_pools_type = ConfigLoader.New("act_161_pools_type")

local turntable_task_view = ConfigLoader.New("turntable_task_view")

local turntable_fatigue_reward = ConfigLoader.New("act161_fatigue_reward")

local act161_commonconfig = ConfigLoader.New("act161_commonconfig")

local act_161_red_envelope = ConfigLoader.New("act_161_red_envelope")

local act_161_red_envelope_type = ConfigLoader.New("act_161_red_envelope_type")

local act_161_lottery_score_rewards = ConfigLoader.New("act_161_lottery_score_rewards")

TurnTableConfig.ActivityDefineId = 161

--梦工厂活动id
TurnTableConfig.DreamFactoryId = 1118

--风华秀活动id
TurnTableConfig.FengHuaTurntableId = 1184

--音乐双子转盘id
TurnTableConfig.MusicTurntableId = 1287

--传统芳华曲转盘
TurnTableConfig.TraditionTurntableId = 1507

--神龙转盘
TurnTableConfig.DragonTurntableId = 1733

--星厨宴转盘
TurnTableConfig.StarCookTurntableId = 1825

--3.0巴啦啦转盘
TurnTableConfig.BalalaTurntableId = 1948
--以后不用配了，通过活动defineId直接拿

function TurnTableConfig.getBonus(activityId)
    return poolConfig:getConfig()[activityId]
end

-- 转换一下原始配置
local progressAwardConfig
-- 整理奖池
local poolMap

local hasInit

function TurnTableConfig.initCfg()
    if hasInit then
        return
    end
    hasInit = true
    progressAwardConfig = {}
    poolMap = {}
    for i, v in ipairs(progressAward:getConfig().dataList) do
        if progressAwardConfig[v.activityId] == nil then
            print("make progressAwardConfig table ", v.activityId)
            progressAwardConfig[v.activityId] = {}
        end
        -- 清空奖励相关重新造一份
        local finalRewards = deepcopy(v)
        finalRewards.rewards = {}
        finalRewards.fixRewards = {}
        -- 转换奖池id
        if v.rewards then
            for _, poolId in ipairs(v.rewards) do
                local reward = deepcopy(poolConfig:getConfig()[v.activityId][poolId].rewards)
                reward.poolId = poolId
                reward.index = #finalRewards.rewards
                table.insert(finalRewards.rewards, reward)
            end
        end
        -- 插入固定奖励
        for index, value in ipairs(v.fixRewards) do
            value.index = #finalRewards.rewards
            value.isFixed = true
            table.insert(finalRewards.rewards, value)
        end
        table.insert(progressAwardConfig[v.activityId], finalRewards)
    end

    for i, v in ipairs(poolConfig:getConfig().dataList) do
        if poolMap[v.activityId] == nil then
            -- print("make activity", v.activityId)
            poolMap[v.activityId] = {}
        end
        if poolMap[v.activityId][v.rewardType] == nil then
            -- print("make", v.rewardType)
            poolMap[v.activityId][v.rewardType] = {}
        end
        local reward = {
            id = v.rewards.id,
            count = v.rewards.count,
            bonusId = v.id,
            weight = v.weight
        }
        table.insert(poolMap[v.activityId][v.rewardType], reward)
    end
end

--获取特殊解锁奖励的key的数量
function TurnTableConfig.getKeyCount(activityId)
    return #act161_commonconfig:getConfig()[activityId].act161CollectRewardIds
end

--获取特殊解锁奖励的key的数量
function TurnTableConfig.getAllClearKeyCount(activityId)
    return act161_commonconfig:getConfig()[activityId].allRewardsCollectRewardTypes or {}
end

--判断物品是否是特殊收集奖励要素
function TurnTableConfig.checkIsKeyReward(id, activityId)
    for i, v in ipairs(act161_commonconfig:getConfig()[activityId].act161CollectRewardIds) do
        if id == v then
            print(id, v)
            return true
        end
    end
    return false
end

function TurnTableConfig.getProgressAward(activityId)
    -- return progressAward:getConfig().dataList
    return progressAwardConfig[activityId]
end

--大背景路径
function TurnTableConfig.getTableBgUrl(id, activityId)
    return act_161_pools_type:getConfig()[activityId][id].bgURL
end

--图标路径
function TurnTableConfig.getTableBagUrl(id, activityId)
    return act_161_pools_type:getConfig()[activityId][id].bagURL
end

function TurnTableConfig.getTableNeedHide(id,activityId)
    return act_161_pools_type:getConfig()[activityId][id].hideBubble   
end

--稀有度背景
function TurnTableConfig.getTableRankId(id, activityId)
    return act_161_pools_type:getConfig()[activityId][id].rankId
end

--专属气泡背景
function TurnTableConfig.getBubbleRankId(id, activityId)
    return act_161_pools_type:getConfig()[activityId][id].bubbleRankId
end

function TurnTableConfig.getTypeById(id, activityId)
    return poolConfig:getConfig()[activityId][id].rewardType
end

--获取某个池子的奖励配置
function TurnTableConfig.getPoolRewardsById(i, activityId)
    return poolMap[activityId][i]
end

--总数量
function TurnTableConfig.getTotalCount(activityId)
    return #poolConfig:getConfig()[activityId]
end
--各个池子的总数量
function TurnTableConfig.getTotalCountByType(id, activityId)
    print(id, activityId)
    return #poolMap[activityId][id]
end
--获取池子
function TurnTableConfig.getPools(activityId)
    return act_161_pools_type:getConfig()[activityId]
end

function TurnTableConfig.getSpecialRewardItems(activityId)
    -- local cfgKey = lang("Act{1}CollectRewardIds", activityId)
    return act161_commonconfig:getConfig()[activityId].act161CollectRewards
end

function TurnTableConfig.getAllClearRewardItems(activityId)
    -- local cfgKey = lang("Act{1}CollectRewardIds", activityId)
    return act161_commonconfig:getConfig()[activityId].allCollectRewards
end

--特殊奖池
function TurnTableConfig.getSpcialPools(activityId)
    local result = {}
    for i, v in ipairs(TurnTableConfig.getPools(activityId)) do
        if v.showRate then
            table.insert(result, v)
        end
    end
    return result
end
--一般奖池
function TurnTableConfig.getNormalPools(activityId)
    local result = {}
    for i, v in ipairs(TurnTableConfig.getPools(activityId)) do
        if not v.showRate then
            table.insert(result, v)
        end
    end
    return result
end

--有无配套的任务
function TurnTableConfig.getHasTask(activityId)
    return turntable_task_view:getConfig()[activityId] ~= nil
end
--有无配套的赠送礼物
function TurnTableConfig.getHasGift(activityId)
    return true
end

-- local shareMap = {
--     [161] = GameEnum.ExternalShareType.ACT_161_SHARE
-- }
--获取分享类型
function TurnTableConfig.getShareType(activityId)
    return act161_commonconfig:getConfig()[activityId].shareId
end

-- local xuanyaoParamsMap = {
--     [161] = {
--         shareType = AobiCircleShareType.OpenTurnTable,
--         -- Assets/GameAssets/image/shareicon/lottery/openturntable.png
--         lotteryType = "openturntable"
--     }
-- }

function TurnTableConfig.getXuanYaoParams(activityId)
    local cfg = act161_commonconfig:getConfig()[activityId]
    local params = {
        shareType = cfg.aobiCircleId,
        lotteryType = cfg.aobiCircleImg
    }
    return params
end

function TurnTableConfig.getTaskViewParams(activityId)
    print(activityId)
    return turntable_task_view:getConfig()[activityId]
end

function TurnTableConfig.getFatigueRewards(activityId)
    print(activityId)
    return turntable_fatigue_reward:getConfig()[activityId]
end

--获取货币配置  晶钻特供版
function TurnTableConfig.getDiamandCostConfig(activityId)
    local costId, costCount, costDiamondValue, freeCostId
    local cfg = act161_commonconfig:getConfig()[activityId]
    costId = cfg.act161LotteryCost.id
    costCount = cfg.act161LotteryCost.count
    costDiamondValue = cfg.act161LotteryItemCost.count
    freeCostId = cfg.timeLimitedLotteryCost.id
    return costId, costCount, costDiamondValue, freeCostId
end

function TurnTableConfig.getDiscountIds(activityId)
    return act161_commonconfig:getConfig()[activityId].discountBagIds
end

--判断是否是抽奖券
function TurnTableConfig.isCostTicket(id)
    print("check id,", id)
    local result = false
    for i, v in ipairs(act161_commonconfig:getConfig().dataList) do
        if id == v.act161LotteryCost.id then
            result = true
            break
        end
    end
    print("check id,", id, result)
    return result
end

function TurnTableConfig.getCommonConfig(activityId)
    return act161_commonconfig:getConfig()[activityId]  
end

--红包祝福语
function TurnTableConfig.getRedBagDes(activityId,id)
    print(activityId,id)
    return act_161_red_envelope:getConfig()[activityId][id].describe 
end

function TurnTableConfig.getRedBagTypeName(activityId, id)
    return act_161_red_envelope_type:getConfig()[activityId][id].name
end

--额外进度奖励

function TurnTableConfig.hasExProgressReward(activityId)
    return act_161_lottery_score_rewards:getConfig()[activityId] ~= nil
end

function TurnTableConfig.getExProgressRewardCfg(activityId)
    return act_161_lottery_score_rewards:getConfig()[activityId]
end

function TurnTableConfig.getRedBagShareId(activityId)
    return act161_commonconfig:getConfig()[activityId].redBagShareId
end

function TurnTableConfig.getRedBagRecordMaxCount(activityId)
    return act161_commonconfig:getConfig()[activityId].drawRedEnvelopeRecordCount
end

function TurnTableConfig.getProgressAwardConfig(activityId, index)
    return progressAward:getConfig()[activityId][index]

end

function TurnTableConfig.getPoolConfig(activityId, id)
    return poolConfig:getConfig()[activityId][id]
end

return TurnTableConfig
