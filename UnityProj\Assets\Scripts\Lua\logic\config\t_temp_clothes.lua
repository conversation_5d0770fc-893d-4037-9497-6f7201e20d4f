-- {excel:B表情配置.xlsx, sheetName:export_假衣服}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_temp_clothes", package.seeall)

local title = {id=1,hideParts=2,hasBack=3,type=4,followPart=5}

local dataList = {
	{1, {26,27,29}, false, 1, nil},
	{2, {7,14}, true, 2, {"toubu","b_toubu"}},
	{3, {7,14}, true, 2, {"toubu","b_toubu"}},
	{4, {26,27}, false, 1, nil},
	{5, {26,27,52}, false, 1, nil},
	{6, {26,27}, true, 1, nil},
	{7, {26,27}, false, 1, nil},
	{8, {26,27}, false, 1, nil},
	{9, {26,27}, false, 1, nil},
	{10, {26,27}, true, 1, nil},
	{11, {26,27}, false, 1, nil},
	{12, {26,27}, false, 1, nil},
	{13, {26,27}, false, 1, nil},
	{14, {26,27}, false, 1, nil},
	{15, {26,27}, true, 1, nil},
	{16, {}, false, 1, nil},
	{17, {26,27}, false, 1, nil},
	{18, {26,27}, false, 1, nil},
	{19, {}, true, 1, nil},
	{20, {26,27}, true, 1, nil},
	{21, {26,27}, true, 1, nil},
	{22, {26,27}, false, 1, nil},
	{23, {26,27}, false, 1, nil},
	{24, {26,27}, true, 1, nil},
	{25, {26,27}, false, 1, nil},
	{26, {26,27}, true, 1, nil},
	{27, {26,27}, false, 1, nil},
	{28, {26,27}, false, 1, nil},
	{29, {26,27}, false, 1, nil},
	{30, {26,27}, false, 1, nil},
	{31, {26,27}, false, 1, nil},
	{32, {26,27}, true, 1, nil},
	{33, {26,27}, true, 1, nil},
	{34, {26,27}, true, 1, nil},
	{35, {26,27}, true, 1, nil},
	{36, {26,27}, true, 1, nil},
	{37, {26,27}, false, 1, nil},
	{38, {26,27}, false, 1, nil},
	{39, {26,27,14,44}, false, 1, nil},
	{40, {26,27,14,44}, false, 1, nil},
	{41, {26,27,14,44}, false, 1, nil},
	{42, {26,27,14,44}, false, 1, nil},
	{43, {26,27,14,44}, false, 1, nil},
	{44, {26,27,14,44}, false, 1, nil},
	{45, {26,27}, true, 1, nil},
	{46, {26,27}, true, 1, nil},
	{47, {26,27}, true, 1, nil},
	{48, {26,27}, true, 1, nil},
	{49, {26,27}, false, 1, nil},
	{50, {23,24,26,27,14,44}, true, 1, nil},
	{51, {26,27,14,44}, false, 1, nil},
	{52, {26,27,14,44}, false, 1, nil},
	{53, {26,27,14,44}, false, 1, nil},
	{54, {26,27,14,44}, false, 1, nil},
	{55, {8,9,10,21,22,23,24,25,26,27,28,29,32,43,44,54}, false, 1, nil},
	{56, {8,9,10,21,22,23,24,25,26,27,28,29,32,43,44,54}, false, 1, nil},
	{57, {8,9,10,21,22,23,24,25,26,27,28,29,32,43,44,54}, false, 1, nil},
	{64, {26,27,14,44,7}, false, 1, nil},
	{65, {26,27,14,44,7}, false, 1, nil},
	{66, {26,27,14,44,7}, false, 1, nil},
	{58, {26,27}, false, 1, nil},
	{59, {26,27}, false, 1, nil},
	{60, {14,30}, false, 1, nil},
	{61, {14,30}, false, 1, nil},
	{62, {14,30}, false, 1, nil},
	{63, {14,30}, false, 1, nil},
	{67, {17,26,27}, true, 1, nil},
	{68, {12,25,26,27,54}, true, 1, nil},
	{69, {26,27}, true, 1, nil},
	{70, {26,27}, true, 1, nil},
	{71, {26,27}, true, 1, nil},
	{72, {19,26,27}, true, 1, nil},
	{73, {26,27,29}, false, 1, nil},
	{74, {20,26,27}, false, 1, nil},
	{75, {26,27}, false, 1, nil},
	{76, {8,9,10,21,22,23,24,25,26,27,28,29,32,43,44,54}, false, 1, nil},
	{77, {7,8,9,10,12,14,17,18,21,22,23,24,25,26,27,28,29,32,43,44,54}, false, 1, nil},
	{78, {26,27}, false, 1, nil},
	{79, {26,27}, false, 1, nil},
	{80, {26,27}, false, 1, nil},
	{81, {26,27}, false, 1, nil},
	{82, {26,27}, false, 1, nil},
	{83, {26,27}, false, 1, nil},
	{84, {26,27}, false, 1, nil},
	{85, {19,26,27}, false, 1, nil},
	{86, {26,27}, false, 1, nil},
	{87, {26,27}, false, 1, nil},
	{88, {26,27}, false, 1, nil},
	{89, {26,27}, false, 1, nil},
	{90, {26,27}, false, 1, nil},
	{91, {26,28}, false, 1, nil},
	{92, {26,29}, false, 1, nil},
	{93, {26,27}, false, 1, nil},
	{94, {26,27}, false, 1, nil},
	{95, {26,27}, false, 1, nil},
	{96, {26,27}, false, 1, nil},
	{97, {26,27}, false, 1, nil},
	{98, {26,27}, true, 1, nil},
	{99, {26,27}, true, 1, nil},
	{100, {26,27}, false, 1, nil},
	{101, {26,27,14}, true, 1, nil},
	{102, {26,27,14}, true, 1, nil},
	{103, {26,27,14}, true, 1, nil},
	{104, {26,27}, false, 1, nil},
	{105, {26,27}, false, 1, nil},
	{106, {26,27}, false, 1, nil},
	{107, {26,27}, true, 1, nil},
	{108, {26,27}, true, 2, {"zs_shouwan_3a","zs_shouwan_3b"}},
	{109, {26,27}, true, 2, {"zs_shouwan_3a","zs_shouwan_3b"}},
	{110, {26,27}, true, 2, {"zs_shouwan_3a","zs_shouwan_3b"}},
	{111, {26,27}, false, 1, nil},
	{112, {26,27}, false, 1, nil},
	{113, {8,9,10,14,21,22,23,24,25,26,27,28,29,32,43,44,54}, false, 1, nil},
	{114, {26,27}, true, 1, nil},
	{115, {26,27}, true, 1, nil},
	{116, {17,44}, false, 1, nil},
	{117, {26,27}, false, 1, nil},
	{118, {26,27}, false, 1, nil},
	{119, {26,27}, false, 1, nil},
	{120, {}, true, 1, nil},
	{121, {}, true, 1, nil},
	{122, {}, true, 1, nil},
	{123, {26,27}, false, 1, nil},
	{124, {26,27}, false, 1, nil},
	{125, {26,27}, false, 1, nil},
	{126, {26,27}, false, 2, {"ys_shouwan_3a"}},
	{127, {26,27}, true, 1, nil},
	{128, {26,27}, false, 1, nil},
	{129, {26,27}, false, 1, nil},
	{130, {26,27}, false, 1, nil},
	{131, {26,27}, false, 1, nil},
	{133, {26,27}, true, 1, nil},
	{134, {32}, false, 1, nil},
	{135, {26,27}, false, 1, nil},
	{136, {26,27,52}, false, 1, nil},
	{137, {26,27}, false, 1, nil},
	{138, {26,27,52}, false, 1, nil},
	{139, {8,9,10,21,25,26,27,28,29,32,43,44,54}, false, 1, nil},
	{140, {26,27}, false, 1, nil},
	{141, {26,27}, false, 1, nil},
	{142, {26,27}, false, 1, nil},
	{143, {26,27}, false, 1, nil},
	{144, {26,27}, false, 1, nil},
	{145, {26,27}, false, 1, nil},
	{146, {26,27,52}, false, 1, nil},
	{147, {26,27}, false, 1, nil},
	{148, {26,27}, false, 1, nil},
	{149, {26,27}, false, 1, nil},
	{150, {26,27,52}, false, 1, nil},
	{151, {26,27}, false, 1, nil},
	{152, {26,27}, false, 1, nil},
	{153, {26,27,29}, false, 1, nil},
	{154, {7,8,9,10,26,27,44,54}, false, 1, nil},
	{155, {26,27}, false, 1, nil},
	{156, {8,9,10,12,17,21,22,23,24,25,26,27,28,29,32,43,44,54}, true, 1, nil},
	{157, {8,9,10,12,17,21,22,23,24,25,26,27,28,29,32,43,45,54}, true, 1, nil},
	{158, {8,9,10,12,17,21,22,23,24,25,26,27,28,29,32,43,46,54}, true, 1, nil},
	{159, {8,9,10,12,17,21,22,23,24,25,26,27,28,29,32,43,47,54}, true, 1, nil},
	{160, {8,9,10,12,14,21,22,23,24,25,26,27,28,29,32,43,44,52,54}, false, 1, nil},
	{161, {26,27}, false, 1, nil},
	{162, {26,27}, false, 1, nil},
	{163, {26,27}, false, 1, nil},
	{164, {26,27,52}, false, 1, nil},
	{165, {26,27}, false, 1, nil},
	{166, {26,27}, false, 1, nil},
	{167, {26,27}, false, 1, nil},
	{168, {26,27}, false, 1, nil},
	{169, {26,27}, false, 1, nil},
	{170, {26,27}, false, 1, nil},
	{171, {26,27}, false, 1, nil},
	{172, {26,27,52}, false, 1, nil},
	{174, {26,27}, false, 1, nil},
	{175, {26,27}, false, 1, nil},
	{176, {26,27}, false, 1, nil},
	{177, {26,27}, false, 1, nil},
	{178, {26,27}, false, 1, nil},
	{179, {26,27}, false, 1, nil},
	{180, {26,27}, false, 1, nil},
	{181, {26,27}, false, 1, nil},
	{182, {26,27}, false, 1, nil},
	{183, {26,27}, false, 1, nil},
	{184, {26,27}, false, 1, nil},
	{185, {26,27,14}, false, 1, nil},
	{186, {19,26,27}, false, 1, nil},
	{187, {26,27,52}, true, 1, nil},
	{188, {26,27,52}, true, 1, nil},
	{189, {26,27}, false, 1, nil},
	{190, {26,27}, false, 1, nil},
	{191, {26,27}, false, 1, nil},
	{192, {26,27}, false, 1, nil},
	{193, {26,27}, false, 1, nil},
	{194, {26,27}, false, 1, nil},
	{195, {26,27}, false, 1, nil},
	{196, {26,27}, false, 1, nil},
	{197, {26,27}, false, 1, nil},
	{198, {26,27}, false, 1, nil},
	{199, {26,27}, false, 1, nil},
	{200, {26,27}, false, 1, nil},
	{201, {26,27}, false, 1, nil},
	{202, {17,44}, false, 1, nil},
	{203, {26,27,52}, false, 1, nil},
	{204, {26,27}, false, 1, nil},
	{205, {26,27}, false, 1, nil},
	{206, {26,27}, false, 1, nil},
	{207, {17,44}, false, 1, nil},
	{208, {26,27}, true, 1, nil},
	{209, {26,27}, false, 1, nil},
	{210, {26,27,52}, false, 1, nil},
	{211, {26,27}, false, 1, nil},
	{212, {26,27}, false, 1, nil},
	{213, {26,27,52}, false, 1, nil},
	{214, {26,27}, false, 1, nil},
	{215, {26,27}, false, 1, nil},
	{216, {26,27}, true, 1, nil},
	{217, {17,26,27,44}, false, 1, nil},
	{218, {26,27,52}, false, 1, nil},
	{219, {26,27}, false, 1, nil},
	{220, {7,8,9,10,12,14,17,18,21,22,23,24,25,26,27,28,29,32,43,44,54}, true, 1, nil},
	{221, {26,27}, false, 1, nil},
	{223, {26,27}, false, 1, nil},
	{224, {26,27}, false, 1, nil},
	{225, {26,27}, false, 1, nil},
	{226, {26,27}, false, 1, nil},
	{227, {26,27}, false, 1, nil},
	{228, {26,27}, false, 1, nil},
	{229, {26,27}, false, 1, nil},
	{230, {26,27}, false, 1, nil},
	{231, {26,27}, false, 1, nil},
	{232, {26,27}, true, 1, nil},
	{233, {26,27}, false, 1, nil},
	{234, {26,27}, false, 1, nil},
	{235, {26,27}, false, 1, nil},
	{236, {26,27,52}, false, 1, nil},
	{237, {26,27,52}, false, 1, nil},
	{238, {26,27}, false, 1, nil},
	{239, {17,26,27,14,44,7}, false, 1, nil},
	{240, {26,27,52}, false, 1, nil},
	{241, {26,27,52}, false, 1, nil},
	{243, {26,27}, false, 1, nil},
	{244, {15,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,46,47,48,49,50,51,52,53}, false, 1, nil},
	{245, {15,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,46,47,48,49,50,51,52,53}, false, 1, nil},
	{246, {26,27,52}, true, 1, nil},
	{247, {26,27,52}, false, 1, nil},
	{248, {26,27,52}, true, 1, nil},
	{249, {26,27,29,32,52}, false, 1, nil},
	{250, {26,27,52}, false, 1, nil},
	{253, {26,27,52}, false, 1, nil},
	{252, {26,27,29,52}, false, 1, nil},
	{254, {26,27,52}, true, 1, {"wenquan_q","wenquan_h"}},
	{255, {26,27,52}, false, 1, nil},
	{256, {26,27,52}, false, 1, nil},
	{257, {26,27,52}, false, 1, nil},
	{258, {26,27,52}, false, 1, nil},
	{259, {26,27,52}, false, 1, nil},
	{260, {26,27,52}, false, 1, nil},
	{261, {17,44}, false, 1, nil},
	{262, {26,27,52}, false, 1, nil},
	{263, {26,27,52}, false, 1, nil},
	{264, {26,27,52}, false, 1, nil},
	{265, {26,27,52}, false, 1, nil},
	{266, {26,27,52}, false, 1, nil},
	{267, {26,27,52}, false, 1, nil},
	{268, {26,27,52}, false, 1, nil},
	{269, {26,27,52}, false, 1, nil},
	{270, {26,27,52}, true, 1, nil},
	{272, {26,27,52}, false, 1, nil},
	{273, {26,27,52}, false, 1, nil},
	{274, {26,27,52}, false, 1, nil},
	{275, {26,27,52}, true, 1, nil},
	{276, {26,27,52}, false, 1, nil},
	{277, {26,27,52}, false, 1, nil},
	{278, {26,27,52}, false, 1, nil},
	{279, {26,27,52}, false, 1, nil},
	{280, {26,27,52}, false, 1, nil},
	{281, {26,27,52}, false, 1, nil},
	{282, {26,27,52}, false, 1, nil},
	{283, {26,27,14}, false, 1, nil},
	{284, {26,27,14}, false, 1, nil},
	{285, {26,27,14}, false, 1, nil},
	{286, {26,27,52}, false, 1, nil},
	{287, {26,27,52}, false, 1, nil},
	{288, {26,27,32,52}, false, 1, nil},
	{289, {26,27,52}, false, 1, nil},
	{291, {26,27,52}, false, 1, nil},
	{290, {26,27,52}, false, 1, nil},
	{292, {26,27,52}, false, 1, nil},
	{5001, {26,27,52}, true, 1, nil},
	{5002, {26,27,52}, true, 1, nil},
	{5003, {26,27,52}, true, 1, nil},
	{5004, {26,27,52}, true, 1, nil},
	{5005, {26,27,52}, true, 1, nil},
	{5006, {26,27,52}, true, 1, nil},
	{5007, {26,27,52}, true, 1, nil},
	{5008, {26,27,52}, true, 1, nil},
	{5009, {26,27,52}, true, 1, nil},
	{5010, {26,27,52}, true, 1, nil},
	{5011, {26,27,52}, true, 1, nil},
	{9999, {13,14,15,16,17,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,46,47,48,49,50,51,52}, false, 1, nil},
	{10000, {15,16,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,41,42,46,47,48,49,50,51,52,53}, false, 1, nil},
}

local t_temp_clothes = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
	[9] = dataList[9],
	[10] = dataList[10],
	[11] = dataList[11],
	[12] = dataList[12],
	[13] = dataList[13],
	[14] = dataList[14],
	[15] = dataList[15],
	[16] = dataList[16],
	[17] = dataList[17],
	[18] = dataList[18],
	[19] = dataList[19],
	[20] = dataList[20],
	[21] = dataList[21],
	[22] = dataList[22],
	[23] = dataList[23],
	[24] = dataList[24],
	[25] = dataList[25],
	[26] = dataList[26],
	[27] = dataList[27],
	[28] = dataList[28],
	[29] = dataList[29],
	[30] = dataList[30],
	[31] = dataList[31],
	[32] = dataList[32],
	[33] = dataList[33],
	[34] = dataList[34],
	[35] = dataList[35],
	[36] = dataList[36],
	[37] = dataList[37],
	[38] = dataList[38],
	[39] = dataList[39],
	[40] = dataList[40],
	[41] = dataList[41],
	[42] = dataList[42],
	[43] = dataList[43],
	[44] = dataList[44],
	[45] = dataList[45],
	[46] = dataList[46],
	[47] = dataList[47],
	[48] = dataList[48],
	[49] = dataList[49],
	[50] = dataList[50],
	[51] = dataList[51],
	[52] = dataList[52],
	[53] = dataList[53],
	[54] = dataList[54],
	[55] = dataList[55],
	[56] = dataList[56],
	[57] = dataList[57],
	[64] = dataList[58],
	[65] = dataList[59],
	[66] = dataList[60],
	[58] = dataList[61],
	[59] = dataList[62],
	[60] = dataList[63],
	[61] = dataList[64],
	[62] = dataList[65],
	[63] = dataList[66],
	[67] = dataList[67],
	[68] = dataList[68],
	[69] = dataList[69],
	[70] = dataList[70],
	[71] = dataList[71],
	[72] = dataList[72],
	[73] = dataList[73],
	[74] = dataList[74],
	[75] = dataList[75],
	[76] = dataList[76],
	[77] = dataList[77],
	[78] = dataList[78],
	[79] = dataList[79],
	[80] = dataList[80],
	[81] = dataList[81],
	[82] = dataList[82],
	[83] = dataList[83],
	[84] = dataList[84],
	[85] = dataList[85],
	[86] = dataList[86],
	[87] = dataList[87],
	[88] = dataList[88],
	[89] = dataList[89],
	[90] = dataList[90],
	[91] = dataList[91],
	[92] = dataList[92],
	[93] = dataList[93],
	[94] = dataList[94],
	[95] = dataList[95],
	[96] = dataList[96],
	[97] = dataList[97],
	[98] = dataList[98],
	[99] = dataList[99],
	[100] = dataList[100],
	[101] = dataList[101],
	[102] = dataList[102],
	[103] = dataList[103],
	[104] = dataList[104],
	[105] = dataList[105],
	[106] = dataList[106],
	[107] = dataList[107],
	[108] = dataList[108],
	[109] = dataList[109],
	[110] = dataList[110],
	[111] = dataList[111],
	[112] = dataList[112],
	[113] = dataList[113],
	[114] = dataList[114],
	[115] = dataList[115],
	[116] = dataList[116],
	[117] = dataList[117],
	[118] = dataList[118],
	[119] = dataList[119],
	[120] = dataList[120],
	[121] = dataList[121],
	[122] = dataList[122],
	[123] = dataList[123],
	[124] = dataList[124],
	[125] = dataList[125],
	[126] = dataList[126],
	[127] = dataList[127],
	[128] = dataList[128],
	[129] = dataList[129],
	[130] = dataList[130],
	[131] = dataList[131],
	[133] = dataList[132],
	[134] = dataList[133],
	[135] = dataList[134],
	[136] = dataList[135],
	[137] = dataList[136],
	[138] = dataList[137],
	[139] = dataList[138],
	[140] = dataList[139],
	[141] = dataList[140],
	[142] = dataList[141],
	[143] = dataList[142],
	[144] = dataList[143],
	[145] = dataList[144],
	[146] = dataList[145],
	[147] = dataList[146],
	[148] = dataList[147],
	[149] = dataList[148],
	[150] = dataList[149],
	[151] = dataList[150],
	[152] = dataList[151],
	[153] = dataList[152],
	[154] = dataList[153],
	[155] = dataList[154],
	[156] = dataList[155],
	[157] = dataList[156],
	[158] = dataList[157],
	[159] = dataList[158],
	[160] = dataList[159],
	[161] = dataList[160],
	[162] = dataList[161],
	[163] = dataList[162],
	[164] = dataList[163],
	[165] = dataList[164],
	[166] = dataList[165],
	[167] = dataList[166],
	[168] = dataList[167],
	[169] = dataList[168],
	[170] = dataList[169],
	[171] = dataList[170],
	[172] = dataList[171],
	[174] = dataList[172],
	[175] = dataList[173],
	[176] = dataList[174],
	[177] = dataList[175],
	[178] = dataList[176],
	[179] = dataList[177],
	[180] = dataList[178],
	[181] = dataList[179],
	[182] = dataList[180],
	[183] = dataList[181],
	[184] = dataList[182],
	[185] = dataList[183],
	[186] = dataList[184],
	[187] = dataList[185],
	[188] = dataList[186],
	[189] = dataList[187],
	[190] = dataList[188],
	[191] = dataList[189],
	[192] = dataList[190],
	[193] = dataList[191],
	[194] = dataList[192],
	[195] = dataList[193],
	[196] = dataList[194],
	[197] = dataList[195],
	[198] = dataList[196],
	[199] = dataList[197],
	[200] = dataList[198],
	[201] = dataList[199],
	[202] = dataList[200],
	[203] = dataList[201],
	[204] = dataList[202],
	[205] = dataList[203],
	[206] = dataList[204],
	[207] = dataList[205],
	[208] = dataList[206],
	[209] = dataList[207],
	[210] = dataList[208],
	[211] = dataList[209],
	[212] = dataList[210],
	[213] = dataList[211],
	[214] = dataList[212],
	[215] = dataList[213],
	[216] = dataList[214],
	[217] = dataList[215],
	[218] = dataList[216],
	[219] = dataList[217],
	[220] = dataList[218],
	[221] = dataList[219],
	[223] = dataList[220],
	[224] = dataList[221],
	[225] = dataList[222],
	[226] = dataList[223],
	[227] = dataList[224],
	[228] = dataList[225],
	[229] = dataList[226],
	[230] = dataList[227],
	[231] = dataList[228],
	[232] = dataList[229],
	[233] = dataList[230],
	[234] = dataList[231],
	[235] = dataList[232],
	[236] = dataList[233],
	[237] = dataList[234],
	[238] = dataList[235],
	[239] = dataList[236],
	[240] = dataList[237],
	[241] = dataList[238],
	[243] = dataList[239],
	[244] = dataList[240],
	[245] = dataList[241],
	[246] = dataList[242],
	[247] = dataList[243],
	[248] = dataList[244],
	[249] = dataList[245],
	[250] = dataList[246],
	[253] = dataList[247],
	[252] = dataList[248],
	[254] = dataList[249],
	[255] = dataList[250],
	[256] = dataList[251],
	[257] = dataList[252],
	[258] = dataList[253],
	[259] = dataList[254],
	[260] = dataList[255],
	[261] = dataList[256],
	[262] = dataList[257],
	[263] = dataList[258],
	[264] = dataList[259],
	[265] = dataList[260],
	[266] = dataList[261],
	[267] = dataList[262],
	[268] = dataList[263],
	[269] = dataList[264],
	[270] = dataList[265],
	[272] = dataList[266],
	[273] = dataList[267],
	[274] = dataList[268],
	[275] = dataList[269],
	[276] = dataList[270],
	[277] = dataList[271],
	[278] = dataList[272],
	[279] = dataList[273],
	[280] = dataList[274],
	[281] = dataList[275],
	[282] = dataList[276],
	[283] = dataList[277],
	[284] = dataList[278],
	[285] = dataList[279],
	[286] = dataList[280],
	[287] = dataList[281],
	[288] = dataList[282],
	[289] = dataList[283],
	[291] = dataList[284],
	[290] = dataList[285],
	[292] = dataList[286],
	[5001] = dataList[287],
	[5002] = dataList[288],
	[5003] = dataList[289],
	[5004] = dataList[290],
	[5005] = dataList[291],
	[5006] = dataList[292],
	[5007] = dataList[293],
	[5008] = dataList[294],
	[5009] = dataList[295],
	[5010] = dataList[296],
	[5011] = dataList[297],
	[9999] = dataList[298],
	[10000] = dataList[299],
}

t_temp_clothes.dataList = dataList
local mt
if TempClothesDefine then
	mt = {
		__cname =  "TempClothesDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or TempClothesDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_temp_clothes