module("logic.scene.common.action.TransformSkinPlayerAction",package.seeall)
local TransformSkinPlayerAction = class("TransformSkinPlayerAction", SceneActionBase)

--{[1]=self.params.uniqueId,[2] = self.params.isLongState, [3]=self.params.itemId, [4]=self.params.posX, [5]=self.params.posY, [6]=skinName, [7] = skinIndex}
function TransformSkinPlayerAction:onStart()
	self.isWorking = true

	SceneAgent.instance:sendSceneShareRequest(PlayerActionType.TransformSkin.typeId, self.params, handler(self.onShareResponse, self))
	TransformSkinController.instance:registerLocalNotify(TransformSkinNotify.playLongStateAnim, self.onPlayLongStateAnim, self)
end

function TransformSkinPlayerAction:onShareResponse(status)
	if status == 0 then
		TransformSkinController.instance:transformSkinSuccess(self.params[1],self.params[3])
		SceneController.instance:registerLocalNotify(SceneNotify.PlayerActionChange, self.onStateChange, self)
	else
		DialogHelper.showErrorMsg(status)
		self:finish(false)
	end
	self.isWorking = false
end

function TransformSkinPlayerAction:onStateChange(nowState, oldState)
	if nowState == PlayerActionType.TransformSkin then
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onStateChange, self)
	end
end

function TransformSkinPlayerAction:onStop()
	SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onStateChange, self)
	SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.TransformSkin.typeId, handler(self.onCancelRequest, self))

	TransformSkinController.instance:registerLocalNotify(TransformSkinNotify.playLongStateAnim, self.onPlayLongStateAnim, self)
end

function TransformSkinPlayerAction:onCancelRequest(status)
	if status == 0 then
		self:finish(true)
	else
		self:finish(false)
	end
end

function TransformSkinPlayerAction:getType()
	return SceneActionType.TransformSkinPlayerAction
end

function TransformSkinPlayerAction:onPlayLongStateAnim(longState)
	self.params[2] = longState and 2 or 1
	SceneAgent.instance:sendSceneShareRequest(PlayerActionType.TransformSkin.typeId, self.params)
end

return TransformSkinPlayerAction