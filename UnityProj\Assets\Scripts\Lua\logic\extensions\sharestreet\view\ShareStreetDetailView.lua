module("logic.extensions.sharestreet.view.ShareStreetDetailView",package.seeall)
---@class ShareStreetDetailView
local ShareStreetDetailView = class("ShareStreetDetailView",ViewComponent)

function ShareStreetDetailView:ctor()
	ShareStreetDetailView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetDetailView:buildUI()
	ShareStreetDetailView.super.buildUI(self)

	self._btnApply = self:getBtn("btnGo2/btnApply")
	self._btnVisit = self:getBtn("btnGo2/btnVisit")
	self._btnLike = self:getBtn("btnGo/btnLike")
	self._btnReport = self:getBtn("btnGo/btnReport")
	self._btnClose = self:getBtn("btnClose")

	self._goStreetPic = self:getGo("mask/icon")

	self._txtVisitCount = self:getText("txtGounp/countGo/txt_2")
	self._txtStreetName = self:getText("txtGounp/txtName")
	self._txtAreaInfo = self:getText("txtGounp/countGo/txt_1")
	self._txtLikeCount = self:getText("txtGounp/countGo/txt_3")
	self._txtMemberCount = self:getText("txtGounp/txtCount")

	self._txtId = self:getText("txtGounp/txtId")
	self._btnCopy = self:getBtn("txtGounp/txtId/btnCopy")

	self._goMemberContainer = self:getGo("listView/content")
	self._goRawItem = self:getGo("listView/content/item")
	self._goRawItem:SetActive(false)
	self._memberItemList = {}

	self._goLikeUnselect = self:getGo("btnGo/btnLike/unselect")
	self._goLikeSelect = self:getGo("btnGo/btnLike/select")
end

--- view初始化时会执行，在buildUI之后
function ShareStreetDetailView:bindEvents()
	ShareStreetDetailView.super.bindEvents(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetDetailView:onEnter()
	ShareStreetDetailView.super.onEnter(self)

	self._btnClose:AddClickListener(self.close, self)
	self._btnLike:AddClickListener(self._onBtnLikeClick, self)
	self._btnReport:AddClickListener(self._onBtnReportClick, self)
	self._btnVisit:AddClickListener(self._onBtnVisitClick, self)
	self._btnApply:AddClickListener(self._onBtnApplyClick, self)
	self._btnCopy:AddClickListener(self._onBtnCopyClick, self)

	self._streetInfo = self:getFirstParam()
	self._ownerId = self._streetInfo:getOwnerId()

	self._txtStreetName.text = self._streetInfo:getName()
    self._txtAreaInfo.text = string.format("%s/%s", self._streetInfo:getMemberCount(), self._streetInfo:getUnlockedAreaIdCount())
    self._txtVisitCount.text = self._streetInfo:getVisitCount()
    self._txtLikeCount.text = self._streetInfo:getLikeCount()
	self._txtId.text = lang("ID：{1}", self._streetInfo:getDisplayId())
	self._txtMemberCount.text = lang("街区人数（{1}/{2}）", #self._streetInfo:getMemberList(), self._streetInfo:getUnlockedAreaIdCount())

	ShareStreetUtil.setPic(self._goStreetPic, self._streetInfo:getPicture(), 832, 472)

	TaskUtil.unactiveAllChildren(self._goMemberContainer)
	local memberList = self._streetInfo:getMemberList()
	for index, memberInfo in ipairs(memberList) do
		local memberItem = self._memberItemList[index]
		local goMember = memberItem and memberItem.go
		if not goMember then
			memberItem = {}
			goMember = goutil.clone(self._goRawItem, "item_" .. index)
			goutil.addChildToParent(goMember, self._goMemberContainer)
			memberItem.go = goMember
			memberItem.txtName = goutil.findChildTextComponent(goMember, "txtName")
			memberItem.goFemale = goutil.findChild(goMember, "imgGirl")
			memberItem.goMale = goutil.findChild(goMember, "imgBoy")
			memberItem.goHeadIcon = goutil.findChild(goMember, "headicon")
			memberItem.goOwner = goutil.findChild(goMember, "imgMaster")
			memberItem.clickTrigger = Framework.UIClickTrigger.Get(memberItem.go)
			self._memberItemList[index] = memberItem
		end
		goMember:SetActive(true)
		local roleInfo = memberInfo.roleInfo
		memberItem.txtName.text = roleInfo.nickname
		memberItem.goFemale:SetActive(roleInfo.sex == 1)
		memberItem.goMale:SetActive(roleInfo.sex == 0)
		HeadPortraitHelper.instance:setHeadPortraitWithUserId(memberItem.goHeadIcon, roleInfo.id)
		memberItem.goOwner:SetActive(index == 1)
		memberItem.clickTrigger:AddClickListener(self._onClickHeadIcon, self, {id = roleInfo.id})
	end

	-- 如果是自己的街区则屏蔽申请按钮
	local isMyStreet = self._ownerId == UserInfo.userId
	self._btnApply.gameObject:SetActive(self._streetInfo:isCanJoin() and not isMyStreet)

	self:_updateLikeBtn()
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetDetailView:onEnterFinished()
	ShareStreetDetailView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetDetailView:onExit()
	ShareStreetDetailView.super.onExit(self)

	self._btnClose:RemoveClickListener()
	self._btnLike:RemoveClickListener()
	self._btnReport:RemoveClickListener()
	self._btnVisit:RemoveClickListener()
	self._btnApply:RemoveClickListener()
	self._btnCopy:RemoveClickListener()

	for _, memberItem in ipairs(self._memberItemList) do
		memberItem.clickTrigger:RemoveClickListener()
	end
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetDetailView:onExitFinished()
	ShareStreetDetailView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetDetailView:unbindEvents()
	ShareStreetDetailView.super.unbindEvents(self)
end

--- view销毁时会执行
function ShareStreetDetailView:destroyUI()
	ShareStreetDetailView.super.destroyUI(self)
end

function ShareStreetDetailView:_onClickHeadIcon(_, param)
	local userId = param.id
	ViewFacade.showUserInfo(userId)
end

function ShareStreetDetailView:_onBtnCopyClick()
	Clipboard.copy(self._streetInfo.displayId)
    FlyTextManager.instance:showFlyText(lang("已复制到剪切板"))
end

function ShareStreetDetailView:_onBtnLikeClick()
	if ShareStreetModel.instance:getIsLike(self._ownerId) then
		return
	end
	
	-- 检查当前用户是否是街区成员
	local memberList = self._streetInfo:getMemberList()
	for _, memberInfo in ipairs(memberList) do
		if memberInfo.roleInfo.id == UserInfo.userId then
			FlyTextManager.instance:showFlyText(lang("无法为自己所在的街区点赞"))
			return
		end
	end
	
	ShareStreetAgent.instance:sendLikeShareStreetRequest(self._ownerId, handler(self._onLikeSucc, self))
end

function ShareStreetDetailView:_onLikeSucc()
	FlyTextManager.instance:showFlyText(lang("点赞成功"))
	ShareStreetModel.instance:setIsLike(self._ownerId, true)
	self:_updateLikeBtn()
end

function ShareStreetDetailView:_updateLikeBtn()
	local isLike = ShareStreetModel.instance:getIsLike(self._ownerId)
	self._goLikeUnselect:SetActive(not isLike)
	self._goLikeSelect:SetActive(isLike)
end

function ShareStreetDetailView:_onBtnReportClick()
    ReportFacade.instance:showReport(self._ownerId, self._streetInfo:getOwnerRoleInfo().nickname, {55, 56, 57, 58},
        {self._streetInfo:getName(), self._streetInfo:getDesc(), "", self._streetInfo:getPicture() or ""})
end

function ShareStreetDetailView:_onBtnVisitClick()
	ViewMgr.instance:clearBackStack()
	RoomFacade.instance:enterShareStreet(self._ownerId)
end

function ShareStreetDetailView:_onBtnApplyClick()
	ShareStreetAgent.instance:sendApplyJoinShareStreetRequest(self._ownerId, handler(self._onApplySucc, self))
end

function ShareStreetDetailView:_onApplySucc()
	FlyTextManager.instance:showFlyText(lang("申请成功"))
end

return ShareStreetDetailView