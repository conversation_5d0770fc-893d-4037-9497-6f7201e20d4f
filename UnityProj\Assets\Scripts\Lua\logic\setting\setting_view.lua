

--[[
setting_view 中每一项属性的含义：
name:
string
界面的名字
presentor:
string
界面展示器的类名字
showMode:
int

ViewSetting.kWindow = 1;              --窗体
ViewSetting.kModalWindow = 2;         --带遮罩窗体
ViewSetting.kFullScreenWindow = 3;    --全屏窗口界面

ViewSetting.kNormalView = 11;         --普通界面
ViewSetting.kModalView  = 12;         --带遮罩界面
ViewSetting.kFullScreenView = 13;     --全屏界面

extId:
int
所属扩展的扩展id

autoDestroyTime:
float
界面关闭后，多久自动销毁，单位秒，每次重新打开会重置倒计时，界面打开中间不会发生销毁
不配置此项的话，则不会销毁

syncOpenList:
string#string...
打开此界面时，也同时打开的界面列表
没有需要的话，可以不配置此项

syncCloseList:
string#string...
关闭此界面时，也同时关闭的界面列表
没有需要的话，可以不配置此项

supportBack:
ViewSetting.BackBig = 101;				--回退过程中的大界面
ViewSetting.BackSmall = 102;			--回退过程中的小界面
ViewSetting.BackNoRecord = 103;			--回退过程中不会再出现的界面
没有需要的话，可以不配置此项

hideHUD:
true or false
打开面板时候是否隐藏hud

openSound:
int	--面板打开时播放音效id

closeSound:
int --面板关闭时播放音效id音效id
--]]


--[[
local setting_view = {}

setting_view.login = {
presentor="LoginViewPresentor",showMode=13,extId=255
}

setting_view.chat = {
presentor="ChatViewPresentor",showMode=13,extId=4
}

return setting_view
--]]

local setting_view = {}

setting_view.LoginPanel =  {
	presentor="LoginPanelPresentor",showMode=ViewSetting.kModalView,extId=-1,
}


setting_view.StartPanel =  {
	presentor="StartPanelPresentor",showMode=ViewSetting.kWindow,extId=-1,
}

setting_view.LoginQueuePanel =  {
	presentor="LoginQueuePanelPresentor",showMode=ViewSetting.kModalView,extId=-1,
}


setting_view.TestUrlPanel =  {
	presentor="TestUrlPanelPresentor",showMode=ViewSetting.kModalView,extId=-1,
}

setting_view.NoticePanel =  {
	presentor="NewNoticeViewPresentor",showMode=ViewSetting.kModalWindow,extId=-1,
}

setting_view.WebView =  {
	presentor="WebViewPresentor",showMode=ViewSetting.kModalWindow,extId=-1, showRead=true
}

setting_view.AgeTipsPanel =  {
	presentor="AgeTipsPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.DailyNoticePanel =  {
	presentor="DailyNoticePanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1,
}

setting_view.SelectZonePanel =  {
	presentor="SelectZonePanelPresentor",showMode=2,extId=-1,
}

setting_view.LevelUpPanel =  {
	presentor="LevelUpPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,
}

setting_view.TitlePanel = {
	presentor ="TitlePanelPresentor",showMode=ViewSetting.kModalWindow,extId=1
}

setting_view.UnlockAndUpgradePanel =  {
	presentor="UnlockAndUpgradePanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,
}
setting_view.CanUnlockAreaPanel =  {
	presentor="CanUnlockAreaPresentor",showMode=ViewSetting.kModalWindow,extId=1,
}
setting_view.InfoCardPanel =  {
	presentor="InfoCardPanelPresentor",showMode=ViewSetting.kFullScreenView,extId=1,supportBack = 101, showRead=true,openSound=141085
}

setting_view.VoiceIntroductionPanel =  {
	presentor="VoiceIntroductionPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,
}

setting_view.HeadFrame = {
	presentor="HeadFramePresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.UserInfoView =  {
	presentor="UserInfoViewPresentor",showMode=ViewSetting.kNormalView,extId=1,logId=172
}

setting_view.ClothesShowView =  {
	presentor="ClothesShowViewPresentor",showMode=ViewSetting.kNormalView,extId=1
}

setting_view.ClothesShowDetailView =  {
	presentor="ClothesShowDetailViewPresentor",showMode=ViewSetting.kModalWindow,extId=1
}

setting_view.CardAchievementView =  {
	presentor="CardAchievementViewPresentor",showMode=ViewSetting.kNormalView,extId=1,autoDestroyTime=0.1
}

setting_view.CardSelectAchPanel =  {
	presentor="CardSelectAchPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1
}

setting_view.CardArchiveView =  {
	presentor="CardArchiveViewPresentor",showMode=ViewSetting.kNormalView,extId=1
}

setting_view.EditTagPanel =  {
	presentor="EditTagPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,autoDestroyTime=10
}

setting_view.EditNamePanel =  {
	presentor="EditNamePanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,autoDestroyTime=10
}

setting_view.ElfInfoCardView =  {
	presentor="ElfInfoCardViewPresentor",showMode=ViewSetting.kNormalView,extId=1,autoDestroyTime=10
}

setting_view.ElfRankingResultView = {
	presentor = "ElfRankRewardPresentor", showMode=ViewSetting.kModalWindow,extId= 35
}

setting_view.ElfFriendView = {
	presentor = "ElfFriendPresentor", showMode = ViewSetting.kModalWindow, extId = 35
}

setting_view.ElfSchoolAbilityBook = {
	presentor = "ElfSchoolAbilityBookPresentor", showMode=ViewSetting.kModalWindow,extId=35,noPadMask=true
}

setting_view.EditWordPanel =  {
	presentor="EditWordPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,autoDestroyTime=10
}

setting_view.CommonHUD =  {
	presentor="CommonHUDViewPresentor",showMode=1,extId=2005,autoDestroyTime=0
}

setting_view.DoublePosePanel =  {
	presentor="DoublePosePanelPresentor",showMode=ViewSetting.kModalWindow,extId=2,autoDestroyTime=10
}

setting_view.JoystickView =  {
	presentor="VirtualJoystickViewPresentor",showMode=1,extId=2,
}

setting_view.MapPanel =  {
	presentor="MapPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=2,autoDestroyTime=10, logId=124, showRead=true,openSound=141086,noPadMask=true
}

setting_view.ChannelPanel =  {
	presentor="ChannelPanelPresentor",showMode=ViewSetting.kModalView,extId=2,autoDestroyTime=10, showRead=true
}

setting_view.EditCamPanel =  {
	presentor="EditCamPanelPresentor",showMode=ViewSetting.kWindow,extId=2
}

setting_view.StoryMaskView =  {
	presentor="StoryMaskViewPresentor",showMode=1,extId=2,
}

setting_view.RoomEditView = {
	presentor="RoomEditorPresentor",showMode=1,extId=3,supportBack = 101,logId=128, showRead=true, hideHUD = true
}

setting_view.PillarEditView = {
	presentor="RoomPillarEditorPresentor",showMode=1,extId=3,supportBack = 101, showRead=true, hideHUD = true
}

setting_view.FurTagEdit = {
	presentor="FurTagEditPresentor",showMode=ViewSetting.kModalView,extId=3
}


setting_view.MySampleView = {
	presentor="MySampleListPresentor",showMode=ViewSetting.kModalView,extId=8
}

setting_view.FurnitureSamplePreview = {
	presentor="FurnitureSamplePresentor",showMode=ViewSetting.kModalView,extId=8, supportBack = 102
}

setting_view.CropsListView =  {
	presentor="IslandCropListPresentor",showMode=ViewSetting.kNormalView,extId=8,
}

setting_view.IslandPropListView =  {
	presentor="IslandPropListPresentor",showMode=ViewSetting.kNormalView,extId=8,
}

setting_view.IslandPlantGuide = {
	presentor = "IslandPlantGuidePresentor",showMode = ViewSetting.kModalView,extId = 8
}

setting_view.FSTPropListView =  {
	presentor="FSTPropListPresentor",showMode=ViewSetting.kNormalView,extId=8,
}

setting_view.DressPanel =  {
	presentor="DressPanelPresentor",showMode=3,extId=4, supportBack = 101,logId=123, showRead=true, openSound=141084
}

setting_view.DressTypePanel =  {
	presentor="DressTypePanelPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

setting_view.DressShareView =  {
	presentor="DressShareViewPresentor",showMode=ViewSetting.kModalWindow,extId=91
}

setting_view.DressShareRecordView =  {
	presentor="DressShareRecordViewPresentor",showMode=ViewSetting.kModalWindow,extId=91
}

setting_view.BatchUnlockColorView =  {
	presentor="BatchUnlockColorViewPresentor",showMode=ViewSetting.kModalWindow,extId=91
}

setting_view.DressChangeColor =  {
	presentor="DressChangeColorPresentor",showMode=ViewSetting.kFullScreenView,extId=4, supportBack = 101,
}

setting_view.ChangeColorSuccess =  {
	presentor="ChangeColorSuccessPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

setting_view.DressDyeSure =  {
	presentor="DressDyeSurePresentor",showMode=ViewSetting.kModalWindow,extId=4
}

setting_view.DressDyeUnlockColor =  {
	presentor="DressDyeUnlockColorPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

setting_view.ClothesShowEditPanel =  {
	presentor="ClothesShowEditPanelPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

setting_view.ModelChangePanel =  {
	presentor="ModelChangePanelPresentor",showMode=ViewSetting.kFullScreenView,extId=4
}

setting_view.ModelChangeSucPanel =  {
	presentor="ModelChangeSucPanelPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

setting_view.ChangeColorPanel =  {
	presentor="ChangeColorPanelPresentor",showMode=ViewSetting.kNormalView,extId=4
}

setting_view.DialogView =  {
	presentor="DialogView",showMode=2,extId=-1,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ForceDialogView =  {
	presentor="ForceDialogPanelPresentor",showMode=2,extId=-1,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ForceConfirmDialogView =  {
	presentor="ForceConfirmDialogPanelPresentor",showMode=2,extId=-1,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.RechargeNotEnoughItem =  {
	presentor="RechargeNotEnoughItemPresentor",showMode=2,extId=-1,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.NegativeDialogView =  {
	presentor="DialogView",showMode=2,extId=-1,openSound=SoundManager.NegativeMessage,closeSound=SoundManager.DefaultClosePanel
}

setting_view.LogoutPanel =  {
	presentor="LogoutPanelPresentor",showMode=2,extId=-1,
}

setting_view.LogoutBigPanel =  {
	presentor="LogoutBigPanelPresentor",showMode=2,extId=-1,
}


setting_view.RegisterView =  {
	presentor="RegisterViewPresentor",showMode=3,extId=-1,
}

setting_view.AgreementPanel =  {
	presentor="AgreementPanelPresentor",showMode=3,extId=-1,
}

setting_view.SelectEggView =  {
	presentor="SelectEggViewPresentor",showMode=3,extId=1,
}
setting_view.LoadingPanel =  {
	presentor="LoadingPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=-1,
}

setting_view.SpLoadingPanel =  {
	presentor="SpLoadingPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=-1,
}

setting_view.SeaLoadingPanel =  {
	presentor="SeaLoadingPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=-1,
}

setting_view.DreamRoomLoadingPanel =  {
	presentor="DreamRoomLoadingPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=-1,
}

setting_view.GMItemPanel =  {
	presentor="GMItemPanelPresentor",showMode=2,extId=255,
}

setting_view.GMTaskPanel =  {
	presentor="GMTaskPanelPresentor",showMode=2,extId=255,
}

setting_view.GMViewMonitor =  {
	presentor="GMViewMonitorPresentor",showMode=ViewSetting.kWindow,extId=255,
}

setting_view.GMCodePanel = {
	presentor="GMCodeViewPresentor",showMode=ViewSetting.kNormalView,extId=255,
}

setting_view.GMSelectPathView = {
	presentor="GMSelectPathViewPresentor",showMode=ViewSetting.kNormalView,extId=255,
}

setting_view.GMClearLuaTableView = {
	presentor="GMClearLuaTableViewPresentor",showMode=ViewSetting.kNormalView,extId=255,
}

setting_view.GMTimePanel = {
	presentor="GMTimeToolPresentor",showMode=ViewSetting.kWindow,extId=255,
}
setting_view.GMResTestView = {
	presentor="GMResTestViewPresentor",showMode=ViewSetting.kWindow,extId=255,
}
setting_view.GMVoiceMessagePanel = {
	presentor="GMVoiceMessagePanelPresentor",showMode=ViewSetting.kModalView,extId=255,
}

setting_view.GMFurniturePanel = {
	presentor="GMFurnitureCheckViewPresentor",showMode=ViewSetting.kModalWindow,extId=255,
}

setting_view.GMMessageView = {
	presentor="GMMessageViewPresentor",showMode=ViewSetting.kWindow,extId=255,
}

setting_view.GMRedPointDebugPanel = {
	presentor="GMRedPointDebugPanelPresentor",showMode=ViewSetting.kWindow,extId=255,
}

setting_view.RuntimeHierarchy = {
	presentor="RuntimeHierachyPresentor",showMode=ViewSetting.kNormalView,extId=255,
}

setting_view.BackPackPanel =  {
	presentor="BackPackPanelPresentor",showMode=ViewSetting.kModalView,extId=5, supportBack = 101, hideHUD = true, logId=122, showRead=true,openSound=141094
}

setting_view.AddCapacityPanel =  {
	presentor="AddCapacityPanelPresentor",showMode=ViewSetting.kModalView,extId=5, supportBack = 102
}

setting_view.AddCapacityAniPanel =  {
	presentor="AddCapacityAniPanelPresentor",showMode=2,extId=5
}

setting_view.BackpackFullPanel =  {
	presentor="BackpackFullPanelPresentor",showMode=2,extId=5,
}

setting_view.SelectGiftPanel =  {
	presentor="SelectGiftPanelPresentor",showMode=2,extId=5,
}

setting_view.HeartLimitPanel =  {
	presentor="HeartLimitPanelPresentor",showMode=ViewSetting.kModalView,extId=2,autoDestroyTime=10
}

setting_view.WorkshopGuidePanel =  {
	presentor="WorkshopGuidePanelPresentor",showMode=2,extId=6,
}

setting_view.WorkshopPanel =  {
	presentor="WorkshopPrensentor",showMode=ViewSetting.kFullScreenView,extId=6,supportBack = 101,logId=110, showRead=true
}

setting_view.WorkshopTip =  {
	presentor="WorkshopTipPresentor",showMode=1,extId=6,
}

setting_view.FavorGiftTip =  {
	presentor="FavorGiftTipPresentor",showMode=1,extId=-1,
}

setting_view.WorkshopGain = {
	presentor="WorkshopGainPresentor",showMode=2,extId=6,supportBack = 103,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.WorkshopUpgrade = {
	presentor="WorkshopUpgradePresentor",showMode=2,extId=6,supportBack = 102
}

setting_view.WorkshopPreview = {
	presentor="WorkshopPreviewPresentor",showMode=2,extId=6,supportBack = 102
}

setting_view.WSUpgradeSucc = {
	presentor="WSUpgradSuccPresentor",showMode=2,extId=6
}

setting_view.WorkshopUnlock = {
	presentor="WorkshopUnlockPresentor",showMode=2,extId=6,supportBack = 101
}

setting_view.WorkshopMaking = {
	presentor="WorkshopMakingPresentor",showMode=ViewSetting.kFullScreenView,extId=6,
}

setting_view.WorkshopNpcMaking = {
	presentor="WorkshopNpcMakingPresentor",showMode=ViewSetting.kFullScreenView,extId=6,supportBack = 101
}

setting_view.WorkshopSecretItem = {
	presentor="WorkshopSecretItemPresentor",showMode=2,extId=6,supportBack = 102
}

setting_view.WorkshopComplete = {
	presentor="WorkshopCompletePresentor",showMode=2,extId=6,
}
setting_view.ShowItemList = {
	presentor="ShowItemListPresentor",showMode=2,extId=6,openSound=141097
}

setting_view.MakeItemComplete = {
	presentor="MakeItemCompletePresentor",showMode=2,extId=6,
}
setting_view.RoleDialogue = {
	presentor="RoleDialogueViewPresentor",showMode=ViewSetting.kModalView,extId=2003,autoDestroyTime=0
}

setting_view.GuideView = {
	presentor="GuideViewPresentor",showMode=11,extId=-1,
}

setting_view.SimpleGuideView = {
	presentor="SimpleGuideViewPresentor",showMode=ViewSetting.kModalView,extId=-1,
}

setting_view.ItemMake = {
	presentor="ItemMakePrensentor",showMode=2,extId=5,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}
setting_view.ItemSold = {
	presentor="ItemSoldPrensentor",showMode=2,extId=5,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}
setting_view.ItemUse = {
	presentor="ItemUsePrensentor",showMode=2,extId=5,supportBack = 103,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.BatchUsePanel = {
	presentor="BatchUsePanelPresentor",showMode=2,extId=5,
}

setting_view.BirthdayView = {
	presentor="BirthdayViewPresentor",showMode=2,extId=-1
}

setting_view.LocationView = {
	presentor="LocationViewPresentor",showMode=2,extId=-1
}

setting_view.AgeView = {
	presentor="AgeViewPresentor",showMode=2,extId=-1
}
-- setting_view.Upgrade = {
-- 	presentor="UpgradeViewPresentor",showMode=2,extId=-1
-- }
setting_view.HelpView = {
	presentor="HelpViewPresentor",showMode=2,extId=-1
}
setting_view.RewardsDlg = {
	presentor="RewardDlgViewPresentor",showMode=2,extId=-1,openSound=141098
}
setting_view.TaskFinish = {
	presentor="TaskFinishViewPresentor",showMode=2,extId=-1
}
setting_view.TaskSkipView =  {
	presentor="TaskSkipViewPresentor",showMode=ViewSetting.kWindow,extId=-1,
}
setting_view.TaskPanel = {
	presentor="TaskPanelPresentor",showMode=2,extId=7,supportBack = 101,logId = 108, showRead=true,
}
setting_view.TaskPanelLite = {
	presentor="TaskPanelLitePresentor",showMode=2,extId=7,supportBack = 101,
}
setting_view.TaskGo = {
	presentor="TaskGoPresentor",showMode=2,extId=7,supportBack = 103,
}
setting_view.IslandOrganizePhotoAlbum = {
	presentor="IslanderOrganizePhotoAlbumViewPresentor",showMode=2,extId=7,hideHUD=true
}

setting_view.SevenDayActivePanel = {
	presentor="SevenDayActivePanelPresentor",showMode=2,extId=7,supportBack = 101,logId = 159, showRead=true
}
setting_view.TaskStoryPanel = {
	presentor="TaskStoryPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=7,logId=141,
}
setting_view.TaskStoryChapterPanel = {
	presentor="TaskStoryChapterPanelPresentor",showMode=ViewSetting.kModalWindow,extId=7,
}
setting_view.TaskStoryPreview = {
	presentor="TaskChapterPreviewPresentor",showMode=ViewSetting.kFullScreenWindow,extId=7,logId=192,
}
setting_view.TaskNPCPanel = {
	presentor="TaskNPCPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=7,supportBack = 101, logId=142
}
setting_view.TaskNPCDetailPanel = {
	presentor="TaskNPCDetailPanelPresentor",showMode=ViewSetting.kModalWindow,extId=7,
}
setting_view.TaskObjectivePanel = {
	presentor="TaskObjectivePanelPresentor",showMode=ViewSetting.kModalWindow,extId=7,supportBack = 101,openSound=141073,hideHUD = true, autoDestroyTime=60,showRead=true
}
setting_view.ChapterUnlockView = {
	presentor="TaskChapterUnlockPresentor",showMode=ViewSetting.kModalWindow,extId=7,
}
setting_view.ChapterStartView = {
	presentor="TaskChapterStartPresentor",showMode=ViewSetting.kModalWindow,extId=7,
}

setting_view.BuildScarecrow = {
	presentor="BuildScarecrowViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.DecorateHat = {
	presentor="DecorateHatViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.FindSomePeople = {
	presentor="FindSomePeopleViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.GmSpineTestView = {
	presentor="GmSpineTestViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,
}

setting_view.DragItemPanel = {
	presentor="DragItemPanelPresentor",showMode=ViewSetting.kModalWindow,extId=7,
}

setting_view.RepairChip = {
	presentor="RepairChipViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.FindLeaf = {
	presentor="FindLeafViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.LookUpBook = {
	presentor="LookUpBookViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.LookUpBookDetail = {
	presentor="LookUpBookDetailViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.RestoreLetterPaper = {
	presentor="RestoreLetterPaperViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.SignName = {
	presentor="SignNameViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.CandlePsychic = {
	presentor="CandlePsychicViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.Augur = {
	presentor="AugurViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.PizzaGame = {
	presentor="PizzaGamePresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD = true
}

setting_view.FindBullion = {
	presentor="FindBullionPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD = true
}

setting_view.AbilityTest = {
	presentor="AbilityTestPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD = true
}

setting_view.ReadPaper = {
	presentor="ReadPaperPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD = true
}

setting_view.PlaceDebris = {
	presentor="PlaceDebrisPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD = true,autoDestroyTime=1
}

setting_view.EscapeSnowCave = {
	presentor="EscapeSnowCavePresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD = true
}

setting_view.BalanceWeigh = {
	presentor="BalanceWeighViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.LetterView = {
	presentor="LetterViewPresentor",showMode=ViewSetting.kModalWindow,extId=7,hideHUD=true
}

setting_view.Passport = {
	presentor="PassportViewPresentor",showMode=2,extId=7,
}

setting_view.ThrowTipsView = {
	presentor="ThrowTipsViewPresentor",showMode=ViewSetting.kWindow,extId=7,
}


setting_view.DeliverPassport = {
	presentor="DeliverPassportViewPresentor",showMode=1,extId=7,
}

setting_view.FinishStoryTask = {
	presentor="FinishTaskViewPresentor",showMode=2,extId=7,hideHUD=true
}
-- setting_view.NewbiePack = {
-- 	presentor="NewbiePackViewPresentor",showMode=2,extId=-1
-- }
setting_view.StartCG = {
	presentor="StartCGViewPresentor",showMode=2,extId=-1
}
setting_view.GMHUD = {
	presentor="GMHUDPresentor",showMode=1,extId=2,
}
setting_view.RoomAreaUnlock = {
	presentor="RoomUnlockAreaPresentor",showMode=2,extId=8,supportBack = 101,
}
setting_view.RoomBuildingUnlock = {
	presentor="RoomUnlockBuildingPresentor",showMode=2,extId=8,supportBack = 101,
}
setting_view.RoomBuildingUnlockSuc = {
	presentor="RoomUnlockBuildingSucPresentor",showMode=2,extId=8,hideHUD=true
}

setting_view.SuitDetail = {
	presentor="SuitDetailPresentor",showMode=ViewSetting.kFullScreenWindow,extId=10,supportBack = 101,
}

-- setting_view.ActivityView = {
-- 	presentor="ActivityPresentor",showMode=1,extId=100,supportBack = 101,logId=106, showRead=true,openSound=141072
-- }

setting_view.ActivityCalendar = {
	presentor="ActivityCalendarViewPresentor",showMode=ViewSetting.kFullScreenWindow,extId=100,logId=144,hideHUD=true,supportBack=ViewSetting.BackBig,openSound = 141072
}

-- setting_view.ActivityCalendar_Main = {
-- 	presentor="ActivityCalendarMainViewPresentor",showMode=ViewSetting.kNormalView,extId=100,logId=144
-- }

setting_view.LegendOfGarden = {
	presentor = "LegendOfGardenViewPresentor", showMode = ViewSetting.kNormalView,extId=100
}

setting_view.ActivityDetail = {
	presentor="ActivityDetailViewPresentor",showMode=ViewSetting.kModalView,extId=100
}

setting_view.GiftBagSaleView = {
	presentor = "GiftBagSaleViewPresentor",showMode=ViewSetting.kNormalView,extId=123
}

setting_view.GiftBagSaleSelect = {
	presentor = "GiftBagSaleSelectPresentor",showMode=ViewSetting.kNormalView
}

setting_view.GiftBagSaleManulSelect = {
	presentor = "GiftBagSaleManulSelectPresentor",showMode=ViewSetting.kModalView
}

setting_view.GodOfFlowerCarvingView = {
	presentor = "GodOfFlowerCarvingViewPresentor", showMode=ViewSetting.kFullScreenWindow,extId=100,
}

setting_view.GodOfFlowerRuleView = {
	presentor = "GodOfFlowerRuleViewPresentor", showMode=ViewSetting.kFullScreenWindow,extId=100,
}

setting_view.SFRuleView = {
	presentor = "SFRuleViewPresentor", showMode=ViewSetting.kFullScreenWindow,extId=219,
}

setting_view.SFBossRewardPreview = {
	presentor = "SFBossRewardPreviewPanelPresentor", showMode=ViewSetting.kModalView,extId=219,
}

setting_view.LinkGame = {
	presentor = "LinkGamePresentor", showMode=ViewSetting.kFullScreenWindow,supportBack=ViewSetting.BackBig,extId=202,
}
setting_view.LinkGameEntry = {
	presentor = "LinkGameEntryPresentor", showMode=ViewSetting.kFullScreenWindow,supportBack=ViewSetting.BackBig,extId=202,
}


setting_view.FireworkView135 = {
	presentor = "FireworkView135Presentor", showMode=ViewSetting.kFullScreenWindow,extId=100,
}
setting_view.FireworkShopPanel = {
	presentor = "FireworkShopPanelPresentor", showMode=ViewSetting.kModalView,extId=100,
}
setting_view.FireworkView = {
	presentor = "FireworkViewPresentor", showMode=ViewSetting.kFullScreenWindow,extId=100,
}
setting_view.GOFCollectView = {
	presentor = "GOFCollectViewPresentor", showMode=ViewSetting.kModalView,extId=133,
}
setting_view.ActivityCollectView = {
	presentor = "ActivityCollectViewPresentor", showMode=ViewSetting.kModalView,extId=218,
}
setting_view.ActivityCollectView2 = {
	presentor = "ActivityCollectViewPresentor2", showMode=ViewSetting.kModalView,extId=218,
}

setting_view.ActivityDirectSaleView = {
	presentor = "ActivityDirectSaleViewPresentor", showMode=ViewSetting.kNormalView,
}

setting_view.ActivityCollectSpecialPanel = {
	presentor = "ActivityCollectSpecialPanelPresentor", showMode=ViewSetting.kNormalView,extId=133,
}

setting_view.GOFGuideView = {
	presentor = "GOFGuideViewPresentor", showMode=ViewSetting.kModalWindow,extId=133,
}

setting_view.GOFCollectSpecialPanel = {
	presentor = "GOFCollectSpecialPresentor", showMode=ViewSetting.kNormalView,extId=133,
}

setting_view.GOFBloodTipView = {
	presentor = "GOFBloodTipViewPresentor", showMode=ViewSetting.kNormalView,extId=133,
}

setting_view.SFBloodTipView = {
	presentor = "SFBloodTipViewPresentor", showMode=ViewSetting.kNormalView,extId=219,
}

setting_view.GOFFoundTipPanel = {
	presentor = "GOFFoundTipPanelPresentor", showMode=ViewSetting.kNormalView,extId=133,hideHUD=true,openSound=141289
}

setting_view.SFFoundTipPanel = {
	presentor = "SFFoundTipPanelPresentor", showMode=ViewSetting.kNormalView,extId=219,hideHUD=true,openSound=141979
}

setting_view.GOFWinPanel = {
	presentor = "GOFWinPanelPresentor", showMode=ViewSetting.kNormalView,extId=133,hideHUD=true,
}
setting_view.SFWinPanel = {
	presentor = "SFWinPanelPresentor", showMode=ViewSetting.kNormalView,extId=133,hideHUD=true,
}

setting_view.RubyMemberPage = {
	presentor="RubyMemberViewPresentor1",showMode=ViewSetting.kFullScreenView,extId=48,hideHUD=true,supportBack=ViewSetting.BackBig,openSound=141285,logId=168,
	syncCloseList= {"RubyGetDailyReward","RubyInheritView","RubyBuffView"}
}

setting_view.RubyBuy = {
	presentor="RubyBuyViewPresentor1",showMode=ViewSetting.kFullScreenView,extId=48,hideHUD = true,supportBack=ViewSetting.BackBig
}

setting_view.RubySendFriendView = {
	presentor="RubySendFriendViewPresentor",showMode=ViewSetting.kModalView,extId=48,hideHUD = true
}

setting_view.RubyGetDailyReward =  {
	presentor="RubyGetDailyRewardViewPresentor",showMode=ViewSetting.kModalWindow,extId=48,
}

setting_view.RubyInheritView =  {
	presentor="RubyInheritViewPresentor",showMode=ViewSetting.kModalWindow,extId=48,
}

setting_view.RubyBuffView =  {
	presentor="RubyBuffViewPresentor",showMode=ViewSetting.kModalWindow,extId=48,
}

setting_view.CouncilGrocery = {
	presentor="CouncilGroceryViewPresentor",showMode=ViewSetting.kFullScreenView,extId=48,hideHUD=true,supportBack=ViewSetting.BackBig
}

setting_view.CouncilTimeListOrder = {
	presentor="CouncilTimeLimitOrderViewPresentor",showMode=ViewSetting.kModalWindow,extId=48,hideHUD=true,supportBack=ViewSetting.BackBig
}

setting_view.CouncilAwards = {
	presentor = "CouncilAwardsViewPresentor", showMode=ViewSetting.kModalWindow,extId=48
}

setting_view.CouncilDonate = {
	presentor = "CouncilDonateViewPresentor", showMode=ViewSetting.kWindow,extId= 48, hideHUD = true,
	syncCloseList= {"WorkshopGain","CouncilDonatePop","CouncilDonateNote","RewardsDlg"}
}

setting_view.CouncilDonatePop = {
	presentor = "CouncilDonatePopViewPresentor", showMode=ViewSetting.kModalView,extId= 48
}

setting_view.CouncilDonateNote = {
	presentor = "CouncilDonateNoteViewPresentor", showMode=ViewSetting.kModalWindow,extId= 48, hideHUD = true
}

setting_view.CouncilBossGameOverView = {
	presentor = "CouncilBossGameOverViewPresentor", showMode=ViewSetting.kModalWindow,extId= 59, hideHUD = true
}

setting_view.CouncilBossMainView = {
	presentor = "CouncilBossMainViewPresentor", showMode=ViewSetting.kWindow,extId= 59
}
setting_view.CouncilBossThrowView = {
	presentor = "CouncilBossThrowViewPresentor", showMode=ViewSetting.kWindow,extId= 59
}
setting_view.CouncilBossOpView = {
	presentor = "CouncilBossOpViewPresentor", showMode=ViewSetting.kWindow,extId= 59
}
setting_view.CBGameOverPlayerListView = {
	presentor = "CBGameOverPlayerListViewPresentor", showMode=ViewSetting.kModalWindow,extId= 59
}

setting_view.ActDaoDanGuiView = {
	presentor="ActDaoDanGuiPresentor",showMode=ViewSetting.kModalWindow,extId=100,supportBack = 101, showRead=true,hideHUD=true
}

setting_view.DaodanguiGuide = {
	presentor="DaodanguiGuideViewPresentor",showMode=ViewSetting.kModalWindow,extId=100,hideHUD=true,supportBack=ViewSetting.BackBig,openSound=141286
}

for i = 1, 11 do
	setting_view["ActDaoDanGuiSubView"..i] = {
		presentor="ActDaoDanGuiSubPresentor",showMode=ViewSetting.kFullScreenView,extId=100,supportBack = ViewSetting.BackBig,
		syncCloseList= {"WorkshopGain","DialogView","NegativeDialogView","RewardsDlg"}
	}
end

setting_view.HappyCollect = {
	presentor = "HappyCollectViewPresentor",showMode=ViewSetting.kModalWindow,extId=100,hideHUD=true,supportBack = ViewSetting.BackSmall,logId=153
}


setting_view.SevenDaySelect = {
	presentor="SevenDaySelectPresentor",showMode=ViewSetting.kModalWindow,extId=100
}

setting_view.FirstChargePoster = {
	presentor="FirstChargePosterPresentor",showMode=ViewSetting.kWindow,extId=100
}

-- setting_view.PosterView = {
-- 	presentor="PosterPresentor",showMode=2,extId=100
-- }

setting_view.ActivityRuleCommon = {
	presentor="ActivityRuleCommonViewPresentor",showMode=2,extId=100
}

setting_view.SevenDayView = {
	presentor="SevenDayPresentor",showMode=ViewSetting.kNormalView,extId=100,supportBack = ViewSetting.BackSmall,
}

setting_view.ActivityLevelAwardPanel = {
	presentor="ActivityLevelAwardPanelPresentor",showMode=ViewSetting.kNormalView,extId=100, logId=169
}

setting_view.ActivityTinyGamePanel = {
	presentor="ActivityTinyGamePanelPresentor",showMode=ViewSetting.kNormalView,extId=100
}

setting_view.ActivityTinyGameReportPanel = {
	presentor="ActivityTinyGameReportPanelPresentor",showMode=ViewSetting.kModalView,extId=100
}

-- setting_view.SignView = {
-- 	presentor="SignPresentor",showMode=2,extId=100,supportBack = 101,
-- }

setting_view.FindClue = {
	presentor="FindClueViewPresentor",showMode=2,extId=100,supportBack = 101,
}

setting_view.CheckWatch = {
	presentor="CheckWatchViewPresentor",showMode=2,extId=100,supportBack = 101,hideHUD = true
}

setting_view.EnterGirlBirthday = {
	presentor="EnterGirlBirthdayViewPresentor",showMode=2,extId=100,supportBack = 101,hideHUD = true
}

setting_view.EnterMomBirthday = {
	presentor="EnterMomBirthdayViewPresentor",showMode=2,extId=100,supportBack = 101,hideHUD = true
}

setting_view.SnowCelebration = {
	presentor="SnowCelebrationViewPresentor",showMode=2,extId=101, logId=136
}

setting_view.SnowCelebrationRule = {
	presentor="SnowCelebrationRulePresentor",showMode=2,extId=101,
}

setting_view.Cotton =  {
	presentor="CottonPresentor",showMode=kFullScreenWindow,extId=38,
}

setting_view.InteractionDialog =  {
	presentor="InteractionDialogViewPresentor",showMode=kFullScreenWindow,extId=38,
}

setting_view.NameCardView = {
	presentor="NameCardViewPresentor",showMode=1,extId=13,autoDestroyTime=0
}

setting_view.ChatView = {
	presentor="ChatViewPresentor",showMode=1,extId=13
}
setting_view.ChatShareView = {
	presentor="ChatShareViewPresentor",showMode=2,extId=13
}
setting_view.ChatWorldHornView = {
	presentor="ChatWorldHornViewPresentor",showMode=2,extId=13
}
setting_view.ChatBubbleSelectView = {
	presentor="ChatBubbleSelectViewPresentor",showMode=2, extId=13
}
setting_view.ChatTeaPartyEnterView = {
	presentor="ChatTeaPartyEnterViewPresentor",showMode=2,extId=13
}
setting_view.ChatTeaPartyCreateView = {
	presentor="ChatTeaPartyCreateViewPresentor",showMode=2,extId=13
}
setting_view.ChatTeaPartyChatView = {
	presentor="ChatTeaPartyChatViewPresentor",showMode=ViewSetting.kNormalView,extId=13,hideHUD=true
}
setting_view.ChatTeaPartyChatTabView = {
	presentor="ChatTeaPartyChatTabViewPresentor",showMode=ViewSetting.kNormalView,extId=13,hideHUD=true
}
setting_view.FurnitureCushionSettingpanel = {
	presentor="FurnitureCushionSettingPresentor",showMode=2,extId=8
}
setting_view.FurnitureCushionRewardPanel = {
	presentor="FurnitureCushionRewardPresentor",showMode=2,extId=8
}
setting_view.FurnitureCushionRewardRecordPanel = {
	presentor="FurnitureCushionRewardRecordPresentor",showMode=2,extId=8
}
setting_view.ChatInviteView = {
	presentor="ChatInviteViewPresentor",showMode=ViewSetting.kModalView,extId=13
}

setting_view.RedEnvelop = {
	presentor="RedEnvelopPresentor",showMode=ViewSetting.kModalView,extId=66
}

setting_view.GiveEnvelop = {
	presentor="GiveEnvelopPresentor",showMode=ViewSetting.kModalView,extId=66
}

setting_view.RedEnvelopRecord = {
	presentor="RedEnvelopRecordPresentor",showMode=ViewSetting.kModalView,extId=66
}

setting_view.RedEnvelopLeak = {
	presentor="RedEnvelopLeakPresentor",showMode=ViewSetting.kModalView,extId=66
}


setting_view.Marquee = {
	presentor = "MarqueeviewPresentor",showMode = ViewSetting.kNormalView, extId = 13
}
setting_view.LotteryView = {
	presentor="LotteryViewPresentor",showMode=ViewSetting.kFullScreenView,extId=12,autoDestroyTime=10,supportBack=101,logId=105,hideHUD=true, showRead=true,openSound=141071,noPadMask=true
}
setting_view.LotteryGM = {
	presentor="LotteryGMPresentor",showMode=ViewSetting.kFullScreenView,extId=12
}
setting_view.LotteryAward = {
	presentor="LotteryAwardPresentor",showMode=ViewSetting.kModalWindow,extId=12,autoDestroyTime=10,supportBack=ViewSetting.BackSmall
}
setting_view.LotterProgressReward = {
	presentor="LotterProgressRewardPresentor",showMode=ViewSetting.kModalWindow,extId=12,autoDestroyTime=10,supportBack=ViewSetting.BackSmall
}
setting_view.LibraryLotteryAward = {
	presentor="LibraryLotteryPreviewPresentor",showMode=ViewSetting.kModalWindow,extId=12,autoDestroyTime=10,supportBack=ViewSetting.BackSmall
}
setting_view.LotteryMovie = {
	presentor="LotteryMoviePresentor",showMode=ViewSetting.kFullScreenWindow,extId=12,autoDestroyTime=10
}
setting_view.TravellerUpLotteryView = {
	presentor="TravellerUpLotteryPresentor",showMode=ViewSetting.kModalWindow,extId=12,autoDestroyTime=10,supportBack=101,hideHUD=true, showRead=true
}
setting_view.LotteryBonusView2 = {
	presentor="LotteryBonusViewPresentor2",showMode=ViewSetting.kModalWindow,extId=12,autoDestroyTime=10
}
setting_view.LotteryScore = {
	presentor="LotteryScorePresentor",showMode=ViewSetting.kModalWindow,extId=12
}
setting_view.LotteryStoryView = {
	presentor="LotteryStoryPresentor",showMode=ViewSetting.kWindow,extId=12
}
setting_view.LotteryActExchange = {
	presentor="LotteryActExchangePresentor",showMode=ViewSetting.kModalWindow,extId=12,autoDestroyTime=0.1
}
setting_view.LotteryWish = {
	presentor="LotteryWishPresentor",showMode=ViewSetting.kWindow,extId=12,supportBack=101
}
setting_view.LotteryWishMovie = {
	presentor="LotteryWishMoviePresentor",showMode=ViewSetting.kModalView,extId=12,supportBack=101
}

setting_view.FriendView = {
	presentor="FriendPresentor",showMode=2,extId=11,supportBack = 101,logId=116, showRead=true,openSound=141087, hideHUD=true
}

setting_view.SceneFriendPanel = {
	presentor="SceneFriendPanelPresentor",showMode=ViewSetting.kModalView,extId=11,supportBack=ViewSetting.BackSmall
}


setting_view.AffinityUpgrade = {
	presentor="AffinityUpgradeViewPresentor",showMode=2,hideHUD = true
}

setting_view.AffinityLevelUp = {
	presentor="AffinityLevelUpPresentor",showMode=2,hideHUD = true
}

setting_view.FriendTaskFinish = {
	presentor="FriendTaskFinishViewPresentor",showMode=2,hideHUD = true,extId=42
}

setting_view.AffinityTask = {
	presentor="AffinityTaskViewPresentor",showMode=2,hideHUD = true
}

setting_view.AffinitySelectFriend = {
	presentor="AffinitySelectFriendViewPresentor",showMode=2,autoDestroyTime=0.1
}

setting_view.AffinityUnlockView = {
	presentor="AffinityUnlockViewPresentor",showMode=2,hideHUD = true
}

setting_view.BlacklistView = {
	presentor="BlacklistPresentor",showMode=ViewSetting.kModalWindow,extId=11
}

setting_view.FishingPanel = {
	presentor="FishingPanelPresentor",showMode=2,extId=14,
}

setting_view.FishGuidePanel = {
	presentor="FishGuideViewPresentor",showMode=2,extId=14,
}

setting_view.GetNewFishPanel = {
	presentor="GetNewFishPanelPresentor",showMode=ViewSetting.kModalWindow,extId=14,
}


setting_view.FishMaching = {
	presentor = "FishMachingPresentor",showMode = ViewSetting.kModalView,extId = 14
}

setting_view.FishExchange = {
	presentor="FishExchangeViewPresentor",showMode=ViewSetting.kFullScreenView,extId=25,supportBack=ViewSetting.BackBig
}

setting_view.MailPanel = {
	presentor="MailPresentor",showMode=ViewSetting.kModalWindow,extId=16,supportBack = ViewSetting.BackBig,hideHUD = true,logId=121, showRead=true,openSound=141092
}

setting_view.MailDetailView = {
	presentor="MailDetailPresentor",showMode=ViewSetting.kModalWindow ,extId=16,supportBack = ViewSetting.BackSmall
}

setting_view.PartyMainView = {
	presentor="PartyMainPresentor",showMode=ViewSetting.kModalView,extId=15,supportBack=101,hideHUD = true,logId=125, showRead=true,openSound=141088
}
setting_view.PartyGameInstruction = {
	presentor="PartyGameInstructions", showMode=ViewSetting.kModalWindow,extId=22
}
setting_view.PartyGameItem = {
	presentor="PartyGameItemPresentor", showMode=ViewSetting.kNormalView,extId=22
}
setting_view.PartyGiftItem = {
	presentor="PartyGiftPresentor", showMode=ViewSetting.kNormalView,extId=22
}
setting_view.CrossBubbleSettig = {
	presentor="CrossBubbleSettigPrenstor",showMode=ViewSetting.kModalWindow,extId=22
}
setting_view.OrderMain = {
	presentor="OrderMainPresentor",showMode=ViewSetting.kFullScreenView,extId=18,supportBack=101,hideHUD = true,logId=109, showRead=true
}
setting_view.OrderSearch = {
	presentor="OrderSearchPresentor",showMode=ViewSetting.kModalWindow,extId=18
}
setting_view.PublishOrder = {
	presentor="PublishOrderPresentor",showMode=ViewSetting.kModalWindow,extId=18
}
setting_view.OrderGuide = {
	presentor="OrderGuidePresentor",showMode=ViewSetting.kModalWindow,extId=18
}

setting_view.ArchiveSelect = {
	presentor="ArchiveSelectPresentor",showMode=ViewSetting.kFullScreenView,extId=19,supportBack=101,hideHUD = true,logId=120, showRead=true,openSound=141091
}

setting_view.ArchiveEntry = {
	presentor="ArchiveEntryPresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102
}

setting_view.Archive = {
	presentor="ArchivePresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102
}

setting_view.ArchiveBigImgPreview = {
	presentor="ArchiveBigImgPresentor",showMode=ViewSetting.kModalView,extId=20000
}

setting_view.ArchiveEffectBigView = {
	presentor="ArchiveEffectBigViewPresentor",showMode=ViewSetting.kModalView,extId=20000
}

setting_view.ArchivesingledetailView = {
	presentor="ArchiveSingleDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102
}

setting_view.ArchiveLandscapeDetail = {
	presentor="ArchiveLandscapeDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102
}

setting_view.ArchiveStreetBig = {
	presentor="ArchiveStreetBigPresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102
}


setting_view.FavorabilityDetailView = {
	presentor="FavorabilityDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102
}

setting_view.ArchiveFurnitureSuitDetailView = {
	presentor="ArchiveFurnitureSuitDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ArchiveDetailView = {
	presentor="ArchiveSuitDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,supportBack=102,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ArchivePetDetail = {
	presentor="ArchivePetDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ArchiveFishDetail = {
	presentor="ArchiveFishDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ArchiveAward = {
	presentor="ArchiveAwardPresentor",showMode=ViewSetting.kModalWindow,extId=19,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ArchiveAnimalDetail = {
	presentor="ArchiveAnimalDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ArchiveVideo = {
	presentor="ArchiveVideoPresentor",showMode=ViewSetting.kModalWindow,extId=19,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.GobangFurnitureWaiting = {
	presentor="GobangFurnitureWaitingPresentor",showMode=ViewSetting.kWindow,hideHUD=true
}

setting_view.TinyGameWaitingPanel = {
	presentor="TinyGameWaitingPanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.SceneGameWaitingPanel = {
	presentor="SceneGameWaitingPanelPresentor",showMode=ViewSetting.kWindow,extId=20,hideHUD=true
}

setting_view.AttackMolePanel = {
	presentor="AttackMolePanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.ColaGameTips = {
	presentor="ColaGameTipsPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.ColaGamePanel = {
	presentor="ColaGamePanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.ColaHelpPanel = {
	presentor="ColaHelpPanelPresentor",showMode=ViewSetting.kWindow,extId=20
}


setting_view.SnowBallPanel = {
	presentor="SnowBallPanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.DaLeDouGamePanel = {
	presentor="DaLeDouGamePanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.DaLeDouRankPanel = {
	presentor="DaLeDouRankPanelPresentor",showMode=ViewSetting.kModalWindow,extId=20,supportBack = ViewSetting.BackSmall
}

setting_view.DaLeDouRankRewardPanel = {
	presentor="DaLeDouRankRewardViewPresentor",showMode=ViewSetting.kModalWindow,extId=20
}


setting_view.GameChatPanel = {
	presentor="GameChatPanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.HitRiceCakePanel = {
	presentor="HitRiceCakePanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.TinyGameResultPanel = {
	presentor="TinyGameResultPanelPresentor",showMode=ViewSetting.kModalWindow,extId=20
}


setting_view.SingleGameResultPanel = {
	presentor="SingleGameResultPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.FollowGamePanel = {
	presentor="FollowGamePanelPresentor",showMode=ViewSetting.kModalWindow,extId=20
}
setting_view.ReverseFlagPanel = {
	presentor="ReverseFlagPanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.AllotWheatPanel = {
	presentor="AllotWheatPanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.GatherHoneyPanel = {
	presentor="GatherHoneyPanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.FoodInfoView = {
	presentor="FoodInfoPresentor",showMode=ViewSetting.kModalView,extId=17
}

setting_view.Reload = {
	presentor="ReloadPresentor",showMode=ViewSetting.kWindow,extId=20000
}

setting_view.TakePhoto = {
	presentor="TakePhotoPresentor",showMode=ViewSetting.kWindow,extId=23,supportBack=101,logId=129
}

setting_view.TakePhotoShow = {
	presentor="TakePhotoShowPresentor",showMode=ViewSetting.kModalView,extId=23
}

setting_view.ItemPreview = {
	presentor="ItemPreviewPresentor",showMode=ViewSetting.kFullScreenView,extId=20000
}

setting_view.IslandBgPreview = {
	presentor="IslandBgPreviewPresentor",showMode=ViewSetting.kModalView,extId=20000
}

setting_view.MagicPlantPreview = {
	presentor="MagicPlantPreviewPresentor",showMode=ViewSetting.kFullScreenView,extId=20000
}

setting_view.PosePreview = {
	presentor="PosePreviewPanelPresentor",showMode=ViewSetting.kFullScreenView,extId=20000
}

setting_view.ItemPictureView = {
	presentor="ItemPictureViewPresentor",showMode=ViewSetting.kModalView,extId=20000
}

setting_view.AobiCircle = {
	presentor="AobiCirclePresentor",showMode=ViewSetting.kFullScreenView,extId=26,supportBack=101,logId=126, showRead=true,openSound=141089
}

setting_view.PublishDynamicPanel = {
	presentor="PublishDynamicPresentor",showMode=ViewSetting.kModalView,extId=26,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.DynamicDetailPanel = {
	presentor="DynamicDetailPresentor",showMode=ViewSetting.kModalView,extId=26,supportBack=103,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.BigDynamicDetailPanel = {
	presentor="BigDynamicDetailPresentor",showMode=ViewSetting.kModalView,extId=26,supportBack=103,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ImagePreviewView = {
	presentor="ImagePreviewPresentor",showMode=ViewSetting.kWindow,extId=26
}

setting_view.AobiCircleShareDetail = {
	presentor="AobiCircleShareDetailPresentor",showMode=ViewSetting.kModalView,extId=26
}

setting_view.TradingRegister = {
	presentor="RegisterPackagePresentor",showMode=ViewSetting.kModalWindow,extId=24
}

setting_view.TradingMarket = {
	presentor="TradingMarketPresentor",showMode=ViewSetting.kModalWindow,extId=24,supportBack=101, hideHUD = true,logId=115, showRead=true
}

setting_view.TradingList = {
	presentor="TradingListPresentor",showMode=ViewSetting.kModalWindow,extId=24
}

setting_view.DigInteraction = {
	presentor="DigInteractionPresentor",showMode=ViewSetting.kFullScreenView,extId=9
}

setting_view.DigInteractionPanel = {
	presentor="DigInteractionPanelPresentor",showMode=ViewSetting.kWindow,extId=9
}

setting_view.ProductNumPanel = {
	presentor="ProductNumPanelPresentor",showMode=ViewSetting.kModalView,extId=9,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.SeaProductNumPanel = {
	presentor="SeaProductNumPanelPresentor",showMode=ViewSetting.kModalView,extId=9,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.EditGyroPanel = {
	presentor="EditGyroPanelPresentor",showMode=ViewSetting.kWindow,extId=-1
}
setting_view.HouseUpgrade = {
	presentor="HouseUpgradePresentor",showMode=ViewSetting.kModalWindow,extId=3,supportBack=102
}
setting_view.HouseList = {
	presentor="HouseListPresentor",showMode=ViewSetting.kModalView,extId=3,supportBack=101
}
setting_view.MyIsland = {
	presentor="MyIslandPresentor",showMode=ViewSetting.kModalView,extId=3,supportBack=101,logId=127, showRead=true
}
setting_view.MySeabedIsland = {
	presentor="MySeabedPresentor",showMode=ViewSetting.kModalView,extId=3,supportBack=101,logId=127, showRead=true
}
setting_view.MyShareStreetMap = {
	presentor="MyShareStreetMapPresentor",showMode=ViewSetting.kModalView,extId=3,supportBack=101,logId=127, showRead=true
}
setting_view.HouseAreaUnlock = {
	presentor="HouseAreaUnlockPresentor",showMode=ViewSetting.kModalWindow,extId=3,supportBack=102
}
setting_view.HouseAreaUnlockTips = {
	presentor="HouseAreaUnlockTipsPresentor",showMode=ViewSetting.kModalWindow,extId=3
}

-- setting_view.SmallStorePanel = {
-- 	presentor="SmallStorePanelPresentor",showMode=ViewSetting.kFullScreenView,extId=25,supportBack = ViewSetting.BackBig
-- }

setting_view.BuyGoodsPanel = {
	presentor="BuyGoodsPanelPresentor",showMode=ViewSetting.kModalView,extId=25
}
setting_view.ExchangePanel = {
	presentor="ExchangePresentor",showMode=ViewSetting.kModalView,extId=25
}

setting_view.BuyGoodsDetailPanel = {
	presentor="BuyGoodsDetailPanelPresentor",showMode=ViewSetting.kModalView,extId=-1,supportBack = ViewSetting.BackNoRecord,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.GroceryView = {
	presentor="GroceryViewPresentor",showMode=ViewSetting.kFullScreenView,extId=25,supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.TravellerExchageEntry = {
	presentor="TravellerExchageEntry",showMode=ViewSetting.kNormalView,extId=25,supportBack = ViewSetting.BackBig, showRead=true
}


setting_view.GodOfFlowerGroceryView = {
	presentor="GodOfFlowerGroceryPresentor",showMode=ViewSetting.kFullScreenView,extId=25,supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.GodOfFlowerMap = {
	presentor="GodOfFlowerMapPresentor",showMode=ViewSetting.kModalView,extId=100,supportBack = ViewSetting.BackBig, showRead=true,openSound=141357
}

setting_view.GodOfFlowerSign = {
	presentor="GodOfFlowerSignPresentor",showMode=ViewSetting.kFullScreenView,extId=128,supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.Setting = {
	presentor="SettingPresentor",showMode=ViewSetting.kModalView,extId=-2
}

setting_view.CdKeyPanel = {
	presentor="CdKeyPanelPresentor",showMode=ViewSetting.kModalView,extId=-2
}

setting_view.GetItemView = {
	presentor="GmItemPreviewPresentor",showMode=ViewSetting.kFullScreenView,extId=-2
}

setting_view.AchievementEntryView = {
	presentor="AchievementEntryViewPresentor",showMode=ViewSetting.kFullScreenView,extId=28,supportBack = ViewSetting.BackBig, logId = 119, showRead=true
}

setting_view.AchievementView = {
	presentor="AchievementViewPresentor",showMode=ViewSetting.kFullScreenView,extId=28,supportBack = ViewSetting.BackBig, showRead=true, openSound=141090
}

setting_view.PetCatch = {
	presentor="PetCatchPresentor",showMode=ViewSetting.kFullScreenView,extId = 30,supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.GainPetView = {
	presentor="GainPetPresentor",showMode=ViewSetting.kModalView,extId= 30,supportBack = ViewSetting.BackSmall
}

setting_view.BatchGainPetView = {
	presentor="BatchGainPetViewPresentor",showMode=ViewSetting.kModalView,extId= 30
}

setting_view.SelectLureNumPanel = {
	presentor="SelectLureNumPanelPresentor",showMode=ViewSetting.kModalView,extId= 30
}

setting_view.PetHome = {
	presentor="PetHomeViewPresentor",showMode=ViewSetting.kFullScreenView,extId=30,logId = 130, showRead=true,openSound=141078,noPadMask=true
}

setting_view.PetMedal =  {
	presentor="PetMedalPresentor",showMode=ViewSetting.kModalView,extId=30
}

setting_view.PetShowEdit =  {
	presentor="PetShowEditPresentor",showMode=ViewSetting.kNormalView,extId=30,hideHUD = true
}

setting_view.PetHomeGuidePanel = {
	presentor="PetHomeGuideViewPresentor",showMode=2,extId=30,
}

setting_view.PetExpand = {
	presentor="PetExpandPresentor",showMode=ViewSetting.kModalView,extId=30
}

setting_view.PetNoCapacity = {
	presentor="PetNoCapacityPresentor",showMode=ViewSetting.kModalView,extId=30,supportBack = ViewSetting.BackNoRecord
}

setting_view.PetInfo = {
	presentor="PetInfoViewPresentor",showMode=ViewSetting.kModalView,extId=30,supportBack = ViewSetting.BackBig
}

setting_view.PetEvolution = {
	presentor="PetEvolutionViewPresentor",showMode=ViewSetting.kModalWindow,extId=30
}

setting_view.PetEvolutionPreview = {
	presentor="PetEvolutionPreviewViewPresentor",showMode=ViewSetting.kModalWindow,extId=30
}

setting_view.PetRelease = {
	presentor="PetReleaseViewPresentor",showMode=ViewSetting.kModalWindow,extId=30
}

setting_view.PetFeed = {
	presentor="PetFeedViewPresentor",showMode=ViewSetting.kNormalView,extId=30
}

setting_view.PetBatchFeed = {
	presentor="PetBatchFeedViewPresentor",showMode=ViewSetting.kModalWindow,extId=30
}

setting_view.PetRename = {
	presentor="PetRenamePresentor",showMode=ViewSetting.kModalView,extId=30
}

setting_view.PetEvolutionShow = {
	presentor="PetEvolutionShowViewPresentor",showMode=ViewSetting.kNormalView,extId=30
}

setting_view.PetEvolutionSuccess = {
	presentor="PetEvolutionSuccessViewPresentor",showMode=ViewSetting.kNormalView,extId=30
}

setting_view.PetExpeditionMain = {
	presentor="PetExpeditionScenePresentor",showMode=ViewSetting.kModalWindow,extId=30,logId=131,hideHUD=true
}

setting_view.PetExpeditionSelect = {
	presentor="PetExpeditionSelectPresentor",showMode=ViewSetting.kModalWindow,extId=30
}

setting_view.PetVariation = {
	presentor="PetVariationPanelViewPresentor",showMode=ViewSetting.kNormalView,extId=30
}

setting_view.Pet5DView = {
	presentor="Pet5DViewPresentor",showMode=ViewSetting.kModalWindow,extId=30
}

setting_view.RecycleEntry = {
	presentor="RecycleEntryPresentor",showMode=ViewSetting.kModalView,extId=31,supportBack=103
}

setting_view.Recycle = {
	presentor="RecyclePresentor",showMode=ViewSetting.kModalView,extId=31,supportBack=101
}

setting_view.ProtoDebug = {
	presentor="ProtoDebugPresentor",showMode=ViewSetting.kNormalView,extId=-1, autoDestroyTime= 0
}

setting_view.FuncUnlock = {
	presentor="FuncUnlockViewPresentor",showMode=ViewSetting.kModalView,extId=32
}

setting_view.FuncUnlockEft = {
	presentor="FuncUnlockEftViewPresentor",showMode=ViewSetting.kNormalView,extId=32
}

setting_view.Recharge = {
	presentor="RechargeStorePresentor",showMode=ViewSetting.kFullScreenWindow,extId=33, showRead=true, openSound=141068, hideHUD=true, supportBack = ViewSetting.BackBig
}

setting_view.RechargeSuite = {
	presentor="RechargeSotreSuitePresentor",showMode=ViewSetting.kFullScreenWindow,extId=33, showRead=true, openSound=141068, hideHUD=true, supportBack = ViewSetting.BackBig
}

setting_view.RechargeGiftBag = {
	presentor="RechargeBuyGiftPresentor",showMode=ViewSetting.kModalWindow,extId=33
}
setting_view.RechargeStoreBuyItem = {
	presentor="RechargeStoreBuyItemPresentor",showMode=ViewSetting.kModalWindow,extId=33,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}
setting_view.RechargeStoreBuyGiftWithRequired = {
	presentor="RechargeStoreBuyGiftWithReqPresentor",showMode=ViewSetting.kModalWindow,extId=33,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}
setting_view.RechargePresent = {
	presentor="RechargePresentPresentor",showMode=ViewSetting.kModalWindow,extId=5
}
setting_view.RechargeVipPanel = {
	presentor="RechargeVipPanelPresentor",showMode=ViewSetting.kFullScreenWindow,extId=33, showRead=true, supportBack = ViewSetting.BackBig
}

setting_view.PopupGift = {
	presentor="PopupGiftPresentor", showMode=ViewSetting.kModalWindow, extId=33, supportBack = ViewSetting.BackSmall
}

setting_view.FirstRecharge2 = {
	presentor="FirstRechargePresentor2",showMode=ViewSetting.kModalView,extId=33, showRead=true, openSound=141068, hideHUD=true,supportBack =ViewSetting.BackSmall
}

setting_view.FirstRechargeSelectView = {
	presentor="FirstRechargeSelectViewPresentor",showMode=ViewSetting.kModalView,extId=33 
}

setting_view.ExchangeDiamondPanel = {
	presentor="ExchangeDiamondPanelPresentor",showMode=ViewSetting.kModalView,extId=33,supportBack = ViewSetting.BackSmall
}

setting_view.ExchangeItemPanel = {
	presentor="ExchangeItemPanelPresentor",showMode=ViewSetting.kModalView,extId=33,supportBack = ViewSetting.BackSmall
}

setting_view.StoryBlack = {
	presentor="StoryBlackViewPresentor",showMode=ViewSetting.kNormalView,extId=7
}

setting_view.FashionRace = {
	presentor="FashionRaceMainPresentor",showMode=ViewSetting.kFullScreenView,extId=34, supportBack = ViewSetting.BackBig,logId=132, showRead=true,openSound=141079,noPadMask=true
}

setting_view.FashionRaceHud = {
	presentor="FashionRaceHUDPresentor",showMode=ViewSetting.kWindow,extId=34
}

setting_view.FashionRaceExchange = {
	presentor="FashionRaceExchangePresentor",showMode=ViewSetting.kModalWindow,extId=34,logId=133,openSound=141081
}

setting_view.FashionRaceVote = {
	presentor = "FashionRaceVotePresentor", showMode = ViewSetting.kFullScreenView, extId = 34, supportBack = ViewSetting.BackBig, showRead=true,noPadMask=true
}

setting_view.FashionRaceRank = {
	presentor = "FashionRaceRankPresentor", showMode = ViewSetting.kFullScreenView, extId = 34, supportBack = ViewSetting.BackBig, showRead=true,openSound=141080,noPadMask=true
}

setting_view.PlayingMusic = {
	presentor="PlayingMusicPresentor",showMode=ViewSetting.kNormalView,extId=123,hideHUD = true
}
setting_view.PortablePlayingMusic = {
	presentor="PortablePlayingMusicPresentor",showMode=ViewSetting.kNormalView,extId=123,hideHUD = true
}

setting_view.SelectMusicInstrument = {
	presentor="SelectMusicInstrumentPresentor",showMode=ViewSetting.kModalView,extId=123,hideHUD = true
}

setting_view.DragonChildInvestSucc = {
	presentor="DragonChildInvestSuccPresentor",showMode=ViewSetting.kModalView,extId=36,hideHUD = true
}

setting_view.NavigationHistory = {
	presentor="NavigationHistoryPresentor",showMode=ViewSetting.kModalView,extId=36,hideHUD = true
}

setting_view.DragonChildSubmitInvest = {
	presentor="SubmitInvestPresentor",showMode=ViewSetting.kModalView,extId=36,hideHUD = true
}

setting_view.DragonChildLetter = {
	presentor="DragonChildLetterPresentor",showMode=ViewSetting.kModalView,extId=36,hideHUD = true
}

setting_view.ElfRename = {
	presentor="ElfRenameViewPresentor" , showMode=ViewSetting.kModalView, extId=35
}

setting_view.ElfSuccView = {
	presentor="ElfSuccViewPresentor",showMode=ViewSetting.kModalView,extId=35,hideHUD = true,openSound = 141131
}

setting_view.ElfInfo = {
	presentor="ElfInfoViewPresentor",showMode=ViewSetting.kModalView, extId=35
}

setting_view.ElfBattlePracticePanel = {
	presentor = "ElfBattlePracticePanelPresentor", showMode = ViewSetting.kNormalView,extId = -1, supportBack = ViewSetting.BackBig
}
setting_view.ElfBattlePracticeResultPanel = {
	presentor = "ElfBattlePracticeResultPanelPresentor", showMode = ViewSetting.kModalWindow,extId = -1
}
setting_view.ElfBattleWaitingPanel = {
	presentor = "ElfBattleWaitingPanelPresentor", showMode = ViewSetting.kNormalView,extId = -1, supportBack = ViewSetting.BackBig
}

setting_view.ElfBattleStartAniPanel = {
	presentor = "ElfBattleStartAniPanelPresentor", showMode = ViewSetting.kNormalView,extId = -1
}

setting_view.ElfBattleEntryPanel = {
	presentor = "ElfBattleEntryPanelPresentor", showMode = ViewSetting.kNormalView,extId = -1
}

setting_view.ElfBattlePanel = {
	presentor = "ElfBattlePanelPresentor", showMode = ViewSetting.kFullScreenView,extId = -1,noPadMask=true
}

setting_view.ElfBattleReportDetailPanel = {
	presentor = "ElfBattleReportDetailPanelPresentor", showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ElfBattleMagicDescPanel = {
	presentor = "ElfBattleMagicDescPanelPresentor", showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ElfBattleResultPanel = {
	presentor = "ElfBattleResultPanelPresentor", showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ElfBattleWinningStreakPanel = {
	presentor = "ElfBattleWinningStreakPanelPresentor", showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ElfBattleEvolutionPanel = {
	presentor = "ElfBattleEvolutionPanelPresentor", showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ElfEvolutionView = {
	presentor = "ElfEvolutionViewPresentor", showMode=ViewSetting.kModalWindow,extId=35
}

setting_view.ElfFollowView = {
	presentor = "ElfFollowViewPresentor", showMode=ViewSetting.kModalWindow,extId=35
}
setting_view.ElfBattleEntrance = {
	presentor="ElfBattleEntranceViewPresentor",showMode=ViewSetting.kModalView, extId=35, supportBack = ViewSetting.BackBig
}

setting_view.ElfLearnView = {
	presentor="ElfLearnViewPresentor",showMode=ViewSetting.kFullScreenWindow, extId=35,supportBack = ViewSetting.BackBig,noPadMask=true
}

setting_view.ElfSchoolView = {
	presentor = "ElfSchoolViewPresenter", showMode=ViewSetting.kFullScreenWindow,extId=35,supportBack = ViewSetting.BackBig,noPadMask=true
}

setting_view.ElfMagicUpLevelView = {
	presentor = "ElfMagicUpLevelViewPresentor", showMode=ViewSetting.kFullScreenView,extId=35
}

setting_view.ElfMagicCourseView = {
	presentor = "ElfMagicCourseViewPresentor", showMode=ViewSetting.kFullScreenWindow,extId=35,supportBack = ViewSetting.BackBig,noPadMask=true
}
setting_view.ElfBattleRankRewardView = {
	presentor = "ElfBattleRankRewardViewPresentor", showMode=ViewSetting.kModalWindow,extId=35
}

setting_view.ElfPlayView = {
	presentor="ElfPlayPresentor",showMode=ViewSetting.kNormalView, extId=35,hideHUD = true
}

setting_view.WishClover ={
	presentor = "WishCloverViewPresentor", showMode = ViewSetting.kModalView,extId = 37
}

setting_view.BigHeadMaker =  {
	presentor="BigHeadMakerViewPresentor",showMode=2,extId=38, showRead=true
}

setting_view.BigHeadMakerShow =  {
	presentor="BigHeadMakerShowViewPresentor",showMode=2,extId=38, showRead=true
}

setting_view.FlowerStoreRewardInfo = {
	presentor = "FlowerStoreRewardInfoViewPresentor",showMode = ViewSetting.kModalWindow,extId=38,hideHUD = true
}

setting_view.FlowerStore = {
	presentor = "FlowerStoreViewPresentor",showMode = ViewSetting.kWindow,extId=38,hideHUD = true
}

setting_view.Feedback = {
	presentor = "FeedbackPresentor", showMode = ViewSetting.kModalView,extId = 999,showRead=true
}

setting_view.HudBuffView = {
	presentor = "HudBuffPresentor",showMode = ViewSetting.kModalView,extId = 21
}

setting_view.Aquarium = {
	presentor = "AquariumPresentor",showMode = ViewSetting.kFullScreenView,extId = 14
}

setting_view.IslanderEventBook = {
	presentor = "IslanderEventBookPresentor",showMode = ViewSetting.kModalView,extId = 7, logId=143,hideHUD=true
}

setting_view.IslanderEventBookDetail = {
	presentor = "IslanderBookDetailPresentor",showMode = ViewSetting.kModalView,extId = 7,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.IslanderRewardPreview = {
	presentor = "IslanderRewardPreviewPresentor",showMode = ViewSetting.kModalView,extId = 7, hideHUD=true
}

setting_view.AobiBank = {
	presentor = "AobiBankPresentor",showMode = ViewSetting.kNormalView,extId = 102, logId=137,supportBack = ViewSetting.BackSmall
}

setting_view.ReturnLottery = {
	presentor = "BackFlowLotteryPresentor",showMode = ViewSetting.kNormalView,extId = 241, supportBack = ViewSetting.BackSmall
}

setting_view.UCGVote = {
	presentor = "UCGVoteViewPresentor",showMode = ViewSetting.kNormalView,extId = 102, logId=137,supportBack = ViewSetting.BackSmall
}

setting_view.RechargeTurntableView = {
	presentor = "RechargeTurntableViewPresentor",showMode = ViewSetting.kNormalView,extId = -1, supportBack = ViewSetting.BackSmall
}

setting_view.AobiBankRule = {
	presentor = "AobiBankRulePresentor",showMode = ViewSetting.kModalView,extId = 102
}

setting_view.MonopolyView = {
	presentor = "MonopolyViewPresentor",showMode = ViewSetting.kNormalView,extId = 125,supportBack = ViewSetting.BackSmall,
	syncCloseList = {"MonopolyChange","MonopolyTask"}
}

setting_view.MonopolyChange = {
	presentor = "MonopolyChangePresentor",showMode = ViewSetting.kModalWindow,extId = 125
}

setting_view.MonopolyTask= {
	presentor = "MonopolyTaskPresentor",showMode = ViewSetting.kModalWindow,extId = 125
}

setting_view.MonopolyReward = {
	presentor="MonopolyRewardPresentor",showMode=ViewSetting.kModalView,extId=125,autoDestroyTime=10
}
setting_view.AobiNewspaper = {
	presentor = "AobiNewspaperPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.NewspaperDetail = {
	presentor = "NewspaperDetailPresentor",showMode = ViewSetting.kModalView,extId = -1, logId=135,openSound=141132
}

setting_view.HotpotPanel = {
	presentor = "HotpotPanelPresentor",showMode = ViewSetting.kModalView,extId = 103,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.HotSpringPanel = {
	presentor = "HotSpringPanelPresentor",showMode = ViewSetting.kNormalView,extId = 103
}

setting_view.DaLeDouActivity = {
	presentor = "DaLeDouActivityPanelPresentor",showMode = ViewSetting.kFullScreenView,extId = 103,supportBack = ViewSetting.BackBig,syncCloseList = {"DaLeDouRankPanel","DaLeDouRankRewardPanel"}
}

setting_view.HappyBloodTip = {
	presentor="HappyBloodTipViewPresentor",showMode=1,extId=107,syncCloseList = {"ChatInviteView"}
}

setting_view.HappyFoundTipPanel = {
	presentor="HappyFoundTipPanelPresentor",showMode=ViewSetting.kNormalView,extId=107, hideHUD=true
}

setting_view.HappyWinPanel = {
	presentor="HappyWinPanelPresentor",showMode=ViewSetting.kNormalView,extId=107, hideHUD=true
}

setting_view.HappyWinCountView = {
	presentor="HappyWinCountViewPresentor",showMode=ViewSetting.kModalView,extId=107, hideHUD=true
}

setting_view.HappyGuide = {
	presentor= "HappyGuideViewPresentor",showMode=ViewSetting.kModalWindow,extId=107
}

setting_view.HappyCollectSpecial = {
	presentor="HappyCollectSpecialPanelPresentor",showMode=1,extId=107
}

setting_view.HappyBlackListView = {
	presentor = "HappyBlackListViewPresentor", showMode=ViewSetting.kModalWindow,extId=107
}

setting_view.NewspaperContribute = {
	presentor = "ContributeNewspaperPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.IslandDiaryTipsPanel = {
	presentor = "IslandDiaryTipsPanelPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.IslandDiaryPhotoPanel = {
	presentor = "IslandDiaryPhotoPanelPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.IslandDiaryPanel = {
	presentor = "IslandDiaryPanelPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ThrowItemPanel = {
	presentor = "ThrowItemPanelPresentor",showMode=1,extId=2,autoDestroyTime=10
}

setting_view.ThrowItemMainPanel = {
	presentor = "ThrowItemMainPanelPresentor",showMode = ViewSetting.kWindow,extId = -1
}


setting_view.ArchiveElfHouseDetail = {
	presentor="ArchiveElfHouseDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.ArchiveHouseDetail = {
	presentor="ArchiveHouseDetailPresentor",showMode=ViewSetting.kModalWindow,extId=19,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.LifeGuideView = {
	presentor="LifeGuidePresentor",showMode=ViewSetting.kFullScreenView,extId=111,supportBack=ViewSetting.BackBig,logId=148,openSound=141359
}

setting_view.LifeGuideBuyLevel = {
	presentor="LifeGuideBuyLevelPresentor",showMode=ViewSetting.kModalWindow,extId=111,supportBack=ViewSetting.BackSmall
}

setting_view.LifeGuideGiving = {
	presentor="LifeGuideGivingPresentor",showMode=ViewSetting.kModalWindow,extId=111,supportBack=ViewSetting.BackSmall
}

setting_view.TravellerOnIslandPanel =  {
	presentor="TravellerOnIslandPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1
}

setting_view.TravellerRolePanel =  {
	presentor="TravellerRolePanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,hideHUD=true, logId=156,--supportBack=ViewSetting.BackBig
}

setting_view.TravellerGoIslandPanel =  {
	presentor="TravellerGoIslandPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1
}

setting_view.TravellerMainPanel =  {
	presentor="TravellerMainPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1,hideHUD=true, logId=156,--supportBack=ViewSetting.BackBig
}

setting_view.TravellerStarMapPanel =  {
	presentor="TravellerStarMapPanelPresentor",showMode=ViewSetting.kFullScreenView,extId=1,hideHUD=true, logId=156,
}

setting_view.TravellerStarMapAwardPanel =  {
	presentor="TravellerStarMapAwardPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1, logId=156,
}

setting_view.TravellerStarMapUnlockPanel =  {
	presentor="TravellerStarMapUnlockPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1, logId=156,
}

setting_view.TravellerStarMapDescPanel =  {
	presentor="TravellerStarMapDescPanelPresentor",showMode=ViewSetting.kModalWindow,extId=1, logId=156,
}

setting_view.TravellerFragmentExchangePanel =  {
	presentor="TravellerFragmentExchangePanelPresentor",showMode=ViewSetting.kModalWindow,extId=1, logId=156,
}

setting_view.TravellerInfoPanel = {
	presentor="TravellerInfoPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.TravellerUpgradeMainPanel =  {
	presentor="TravellerUpgradeTreasurePanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.TravellerHearsayPanel =  {
	presentor="TravellerHearsayPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.TravellerWriteHearsayPanel =  {
	presentor="TravellerWriteHearsayPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.TravellerTreasureDetailPanel =  {
	presentor="TravellerTreasureDetailPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.TravellerTreasureSelectPanel =  {
	presentor="TravellerTreasureSelectPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}
setting_view.TravellerTipsPanel =  {
	presentor="TravellerTipsPanelPresentor",showMode=ViewSetting.kWindow,extId=-1
}
setting_view.TravellerClothPreviewPanel =  {
	presentor="TravellerClothPreviewPanelPresentor",showMode=ViewSetting.kModalView,extId=-1
}
setting_view.TravellerLibrary =  {
	presentor = "TravellerTeamPresentor", showMode = ViewSetting.kModalWindow, extId = 39, hideHUD = true--, supportBack = ViewSetting.BackBig
}
setting_view.TravellerEditTeam =  {
	presentor = "TravellerTeamEditPresentor", showMode = ViewSetting.kModalWindow, extId = 39, hideHUD = true
}
setting_view.TravellerTeamTreasurePanel = {
	presentor = "TravellerTeamTreasurePanelPresentor", showMode=ViewSetting.kModalWindow,extId=39
}
setting_view.TravellerChange =  {
	presentor = "TravellerChangePresentor", showMode = ViewSetting.kModalWindow, extId = -1
}
setting_view.TravellerSaveTeamPanel =  {
	presentor = "TravellerSaveTeamPresentor", showMode = ViewSetting.kModalWindow, extId = 39, 
}

setting_view.LibraryNamePanel =  {
	presentor = "LibraryNamePanelPresentor", showMode = ViewSetting.kModalWindow, extId = 39, 
}

setting_view.LibraryLotteryHUD =  {
	presentor = "LibraryLotteryHUDPresentor", showMode = ViewSetting.kWindow, extId = 12, hideHUD = true
}

setting_view.LibraryLotteryMovie =  {
	presentor = "LibraryLotteryMoviePrenstor", showMode = ViewSetting.kWindow, extId = 12, preventPop = true, autoDestroyTime = 0.1
}

setting_view.LibraryLotteryMovie10 =  {
	presentor = "LibraryLotteryMovie10Prenstor", showMode = ViewSetting.kWindow, extId = 12, preventPop = true, autoDestroyTime = 0.1
}

setting_view.StaminaDetailPanel =  {
	presentor="StaminaDetailPanelPresentor",showMode=ViewSetting.kModalWindow,extId=5
}

setting_view.AddStaminaPanel = {
	presentor="AddStaminaPanelPresentor",showMode=ViewSetting.kModalWindow,extId=5
}

setting_view.MassagePanel = {
	presentor="MassagePanelPresentor",showMode=ViewSetting.kFullScreenView,extId=5
}

setting_view.AffinityAwardpreViewPresentor = {
	presentor="AffinityAwardpreViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1,supportBack = 101
}

setting_view.AffinityAwarDgetViewPresentor = {
	presentor="AffinityAwarDgetViewPresentor",showMode=ViewSetting.kModalWindow,extId = -1,supportBack = 101
}


setting_view.RelativesBookPresentor = {
	presentor="RelativesBookPresentor",showMode = ViewSetting.kFullScreenView,extId = -1,supportBack = 101
}

setting_view.RelativesBookSendPresentor = {
	presentor="RelativesBookSendPresentor",showMode = ViewSetting.kModalWindow,extId = -1,supportBack = 102
}

setting_view.RelativesBookGetViewPresentor = {
	presentor="RelativesBookGetPresentor",showMode = ViewSetting.kModalWindow,extId = -1,supportBack = 102
}

setting_view.RelativeBlessingPresentor = {
	presentor="RelativeBlessingPresentor",showMode = ViewSetting.kModalView,extId = -1,supportBack = 101,hideHUD = true
}

setting_view.ReportView = {
	presentor="ReportViewPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.SevenDayRecharge = {
	presentor = "SevenDayRechargePresentor",showMode = ViewSetting.kNormalView,extId = 115, logId=170,supportBack =ViewSetting.BackSmall
}

setting_view.MagicAllAndSelectPresentor = {
	presentor = "MagicAllAndSelectPresentor",showMode = ViewSetting.kFullScreenView,supportBack = 101,hideHUD = true
}

setting_view.MagiCallAndPresentor = {
	presentor = "MagiCallAndPresentor",showMode = ViewSetting.kFullScreenView,supportBack = 101,hideHUD = true
}

setting_view.DreamLikeGamePresentor = {
	presentor = "DreamLikeGamePresentor",showMode = ViewSetting.kWindow,supportBack = 101,hideHUD = true
}

setting_view.DreamLikeRewards = {
	presentor = "DreamLikeRewardsPresentor",showMode = ViewSetting.kModalView,hideHUD = true
}

setting_view.DreamMagicTable = {
	presentor = "DreamMagicTablePresentor",showMode = ViewSetting.kModalView, hideHUD = true
}

setting_view.DreamMagicConversation = {
	presentor = "DreamLikeConversationPresentor",showMode = ViewSetting.kModalView, hideHUD = true
}

setting_view.PasswordBox = {
	presentor = "PasswordBoxViewPresentor",showMode = ViewSetting.kModalView,hideHUD = true
}

setting_view.QuestionBox = {
	presentor = "QuestionBoxViewPresentor",showMode = ViewSetting.kModalView,hideHUD = true
}

setting_view.DreamlikePoster = {
	presentor = "DreamlikePosterViewPresentor",showMode = ViewSetting.kNormalView
}
setting_view.RechargeDiscountDay = {
	presentor = "RechargeDiscountDayPresentor",showMode = ViewSetting.kNormalView
}


setting_view.FirstPalaceBook = {
	presentor = "FirstPalaceBookPresentor", showMode = ViewSetting.kModalView,hideHUD = true
}

setting_view.FirstPalacCollect = {
	presentor = "FirstPalacCollectPresentor", showMode = ViewSetting.kFullScreenView,hideHUD = true
}

setting_view.GardenMakeFlower = {
	presentor = "GardenMakeFlowerPresentor", showMode = ViewSetting.kFullScreenView,supportBack = 101, hideHUD = true, extId = 41
}

setting_view.GardenSendFlower = {
	presentor = "GardenSendFlowerPresentor", showMode= ViewSetting.kNormalView, openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel, extId = 41
}

setting_view.GardenSendFlower2 = {
	--- 策划想要在通过别人的信息卡打开送花界面的时候单独一个送花界面，不带tab的，淦
	presentor = "GardenSendFlowerPresentor", showMode= ViewSetting.kModalWindow, openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel, extId = 41
}

setting_view.GardenReceiveFlower = {
	presentor = "GardenReceiveFlowerPresentor", showMode= ViewSetting.kNormalView, openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel, extId = 41
}

---满月工坊送月饼，复用送花逻辑
setting_view.MooncakeSend = {
	presentor = "MooncakeSendPresentor", showMode= ViewSetting.kNormalView, openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel, extId = 41
}
setting_view.MooncakeReceive = {
	presentor = "MooncakeReceivePresentor", showMode= ViewSetting.kNormalView, openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel, extId = 41
}
setting_view.MooncakeMainPanel = {
	presentor="MooncakeMainPanelPresentor",showMode=ViewSetting.kModalWindow,extId=20
}
-------

setting_view.GardenFlowerRank = {
	presentor = "GardenFlowerRankingPresentor", showMode = ViewSetting.kModalWindow, supportBack = ViewSetting.BackBig, showRead=true, hideHUD = true, extId = 41,openSound=141085,
}

setting_view.PartyPopRank = {
	presentor = "PartyRankPresentor", showMode = ViewSetting.kModalWindow, supportBack = ViewSetting.BackSmall, showRead=true, hideHUD = true, extId = 41,openSound=141085,
}

setting_view.GardenMakeFlowerReward = {
	presentor="GardenMakeFlowerRewardPresentor",showMode=2,extId=41,openSound=141098
}

setting_view.GardenRankingReward = {
	presentor="GardenRankRewardPresentor",showMode=ViewSetting.kModalWindow,extId=20
}

setting_view.GardenFlowerMainPanel = {
	presentor="GardenFlowerMainPanelPresentor",showMode=ViewSetting.kModalWindow,extId=20
}

setting_view.DreamSettlemen = {
	presentor = "DreamSettlemenPresentor", showMode = ViewSetting.kFullScreenView,hideHUD = true
}

setting_view.DreamLikeNewEvent = {
	presentor = "DreamLikeNewEventPresentor", showMode = ViewSetting.kModalView,hideHUD = true
}

setting_view.AffinityRuleView = {
	presentor = "AffinityRuleViewPresentor", showMode = ViewSetting.kModalView,supportBack = 102
}

setting_view.CouncilMainView = {
	presentor = "CouncilMainViewPresentor", showMode = ViewSetting.kFullScreenView,supportBack = 101, hideHUD = true, extId = 45,showRead = true,logId=104
}

setting_view.CouncilMainViewDetail = {
	presentor = "CouncilMainViewPresentor", showMode = ViewSetting.kModalWindow, extId = 45
}

setting_view.CouncilMemberView = {
	presentor = "CouncilMemberListPresentor",showMode=ViewSetting.kFullScreenView,supportBack = 101,showRead=true,openSound=141089,extId=45,syncCloseList = {"ChatInviteView"}
}

setting_view.CouncilBuyGoodsPanel = {
	presentor="CouncilResourceBuyPresentor",showMode=ViewSetting.kModalView,extId=25
}

setting_view.LevelListView = {
	presentor="LevelListViewPresentor",showMode=ViewSetting.kModalWindow,extId=45
}

setting_view.CouncilNameEditView = {
	presentor="CouncilNameEditPresentor",showMode=ViewSetting.kModalWindow,extId=45
}

setting_view.CouncilRegisterView ={
	presentor = "CouncilRegisterPresentor", showMode = ViewSetting.kModalWindow--,supportBack=101, showRead=true
}

setting_view.CouncilCreateView ={
	presentor = "CouncilCreatePresentor", showMode = ViewSetting.kModalWindow,supportBack=101
}

setting_view.CouncilCreateSuccView = {
	presentor = "CouncilCreateSuccPresentor", showMode = ViewSetting.kModalWindow, extId=45
}

setting_view.CouncilListView = {
	presentor = "CouncilListPresentor", showMode=ViewSetting.kModalView,supportBack=101, showRead=true,openSound=141089,extId=45
}

setting_view.CouncilApplyListView = {
	presentor = "CouncilApplyListPresentor", showMode=ViewSetting.kFullScreenView,showRead=true,openSound=141089,extId=45,supportBack = 101
}

setting_view.CouncilManagementView = {
	presentor = "CouncilManagementPresentor", showMode=ViewSetting.kModalWindow, showRead=true,openSound=141089,extId=45
}

setting_view.CouncilTitleListView = {
	presentor = "CouncilTitleListPresentor",showMode=ViewSetting.kModalWindow,extId=45
}

setting_view.CouncilMemberSelectView = {
	presentor = "CouncilMemberSelectPresentor", showMode=ViewSetting.kModalWindow, showRead=true,openSound=141089,extId=45
}

setting_view.CouncilLogView = {
	presentor = "CouncilLogListPresentor", showMode=ViewSetting.kModalWindow, showRead=true,openSound=141089,extId=45
}

setting_view.CouncilImpeachView ={
	presentor = "CouncilImpeachPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.CouncilBadge = {
	presentor="CouncilBadgeListPresentor",showMode=ViewSetting.kModalWindow,extId=45
}

setting_view.CouncilOutwardHud = {
	presentor="CouncilOutwardHudPresentor",showMode=ViewSetting.kWindow,extId=45
}
setting_view.CouncilAreaUnlockView= {
	presentor="CouncilAreaUnlockPresentor",showMode=ViewSetting.kModalWindow,extId=45
}
setting_view.CouncilOutwardEdit = {
	presentor="CouncilOutwardEditPresentor",showMode=ViewSetting.kWindow,extId=45, hideHUD = true, showRead=true
}
setting_view.CouncilOutwardPreview = {
	presentor="CouncilOutwardPreviewPresentor",showMode=ViewSetting.kWindow,extId=45,supportBack=101, hideHUD = true, showRead=true
}
setting_view.CouncilOutwardShop = {
	presentor="CouncilOutwardShopPresentor",showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true,openSound=141089,extId=45
}

setting_view.CouncilAreaUnlockSuccView = {
	presentor="CouncilAreaUnlockSuccPresentor",showMode=2,extId=45
}

setting_view.CouncilTakeOathView = {
	presentor = "CouncilTakeOathPresentor",showMode=ViewSetting.kModalWindow,extId=45, showRead=true
}

setting_view.CouncilInfoView = {
	presentor = "CouncilInfoViewPresentor",showMode=ViewSetting.kModalWindow,extId=45, showRead=true
}

setting_view.SceneChatView = {
	presentor = "SceneTrapChatPresentor", showMode = ViewSetting.kNormalView
}

setting_view.ATMMainView = {
	presentor = "ATMViewPresentor", showMode = ViewSetting.kModalView, logId=167
}

setting_view.ATMSignView = {
	presentor = "ATMSignViewPresentor", showMode = ViewSetting.kModalView
}

setting_view.AffinityCheckWaiting = {
	presentor = "AffinityCheckWaitingPresentor", showMode=ViewSetting.kWindow
}

setting_view.AffinityCheckFriendList = {
	presentor = "AffinityCheckFriendListPresentor", showMode=ViewSetting.kNormalView
}

setting_view.AffinityCheckSceneView = {
	presentor = "AffinityCheckSceneViewPresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.CouncilMistyForestActivityView = {
	presentor = "MistyForestActivityPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.MistyForestGameView = {
	presentor = "MistyForestGameViewPresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.MistyForestOpenTreasureView = {
	presentor = "MistyForestOpenTreasurePresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.MistyForestSettlementView = {
	presentor = "MistyForestSettlementPresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.MistyForestReadyView = {
	presentor = "MistyForestReadyViewPresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.MistyForestSignalLampView = {
	presentor = "MistyForestSignalLampViewPresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.MistyForestLeaveMsgView = {
	presentor = "MistyForestLeaveMsgViewPresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.MistyShowNoteView = {
	presentor = "MistyShowNoteViewPresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.MistyFailView = {
	presentor = "MistyFailViewPresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.MistySuccessView = {
	presentor = "MistySuccessViewPresentor", showMode = ViewSetting.kNormalView,extId = -1
}

setting_view.MistyForestPV = {
	presentor = "MistyForestPVPresentor", showMode = ViewSetting.kFullScreenView,extId = -1
}

setting_view.MistyForestQuickTeam = {
	presentor = "MistyForestQuickTeamPresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.AffinityCheckGameView = {
	presentor = "AffinityCheckGameViewPresentor", showMode = ViewSetting.kWindow,extId = -1
}


setting_view.AffinityInvitationView = {
	presentor = "AffinityInvitationViewPresentor", showMode = ViewSetting.kModalWindow,extId = -1,supportBack=102
}


setting_view.ChatVoiceMessagePanel = {
	presentor = "ChatVoiceMessagePanelPresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.ParttimeView = {
	presentor = "ParttimeViewPresentor", showMode = ViewSetting.kWindow, extId = 49, hideHUD = true
}

setting_view.ParttimeTouchView = {
	presentor = "ParttimeTouchViewPresentor", showMode = ViewSetting.kWindow, extId = 49, hideHUD = true
}

setting_view.ParttimeTouchTestView = {
	presentor = "ParttimeTouchTestViewPresentor", showMode = ViewSetting.kWindow, extId = 49, hideHUD = true
}

setting_view.ParttimeCompleteView = {
	presentor = "ParttimeCompleteViewPresentor", showMode = ViewSetting.kModalWindow, extId = 49, hideHUD = true, openSound = 141098
}

setting_view.ParttimeLevelupView = {
	presentor = "ParttimeLevelupViewPresentor", showMode = ViewSetting.kModalWindow, extId = 49, hideHUD = true
}

setting_view.ParttimeSelectView = {
	presentor = "ParttimeSelectViewPresentor", showMode = ViewSetting.kModalWindow, extId = 49, hideHUD = true,openSound = 141287, logId = 101
}

setting_view.ParttimeGradeDetailView = {
	presentor = "ParttimeGradeDetailViewPresentor", showMode = ViewSetting.kModalWindow, extId = 49
}

setting_view.HelperMainView = {
	presentor = "HelperMainViewPresentor", showMode = ViewSetting.kModalWindow, extId = 51, hideHUD = true,logId=106
}

setting_view.HelperHistoryView = {
	presentor = "HelperHistoryViewPresentor", showMode = ViewSetting.kModalWindow, extId = 51, hideHUD = true,autoDestroyTime=0
}

setting_view.AnswerTheQuestionView = {
	presentor = "AnswerTheQuestionViewPresentor", showMode = ViewSetting.kModalWindow, extId = 51, hideHUD = true,autoDestroyTime=0
}

setting_view.IslandBuildView = {
	presentor = "IslandBuildViewPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig, extId = 52
}

setting_view.IslandBuildGetBadgeView = {
	presentor = "IslandBuildGetBadgeViewPresentor", showMode = ViewSetting.kModalWindow, extId = 52, hideHUD = true
}

setting_view.IslandBuildLevelRewardsView = {
	presentor = "IslandBuildLevelRewardsViewPresentor", showMode = ViewSetting.kModalWindow, extId = 52
}

setting_view.IslandBuildUpgradeQualificationView = {
	presentor = "IslandBuildUpgradeQualificationViewPresentor", showMode = ViewSetting.kModalWindow, supportBack = ViewSetting.BackSmall, extId = 52
}

setting_view.IslandBuildUpgradeLicenseView = {
	presentor = "IslandBuildUpgradeLicenseViewPresentor", showMode = ViewSetting.kModalWindow, supportBack = ViewSetting.BackSmall, extId = 52, hideHUD = true
}

setting_view.IslandBuildUpgradeLicensePreView = {
	presentor = "IslandBuildUpgradeLicensePreViewPresentor", showMode = ViewSetting.kModalWindow, extId = 52, hideHUD = true
}

setting_view.IslandBuildMemoryView = {
	presentor = "IslandBuildMemoryViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 52
}

setting_view.ExternalShareView = {
	presentor = "ExternalShareViewPresentor", showMode = ViewSetting.kWindow, extId = 56
}

setting_view.FriendCheckShimmerChooseView = {
	presentor = "FriendCheckShimmerChooseViewPresentor", showMode = ViewSetting.kWindow, extId = 47
}

setting_view.FriendCheckShimmerControlView = {
	presentor = "FriendCheckShimmerControlViewPresentor", showMode = ViewSetting.kWindow, extId = 47
}

setting_view.Magicalland2GameView = {
	presentor = "Magicalland2GameViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 62, supportBack = 101, noPadMask = true
}

setting_view.Magicalland2MemberView = {
	presentor = "Magicalland2MemberViewPresentor", showMode = ViewSetting.kModalWindow, extId = 62
}

setting_view.Magicalland2NewUnlockView = {
	presentor = "Magicalland2NewUnlockViewPresentor", showMode = ViewSetting.kModalWindow, extId = 62
}

setting_view.Magicalland3GameView = {
	presentor = "Magicalland3GameViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 69, supportBack = 101, noPadMask = true
}

setting_view.Magicalland3LoadingView = {
	presentor = "Magicalland3LoadingViewPresentor", showMode = ViewSetting.kFullScreenWindow, extId = 69
}

setting_view.Magicalland3NewUnlockView = {
	presentor = "Magicalland3NewUnlockViewPresentor", showMode = ViewSetting.kModalWindow, extId = 69
}

setting_view.Magicalland3Book = {
	presentor = "Magicalland3BookViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.ARView = {
	presentor = "ARViewPresentor", showMode = ViewSetting.kFullScreenView, noPadMask = true
}

setting_view.BargainView = {
	presentor = "BargainViewPresentor", showMode = ViewSetting.kNormalView, extId = 257
}

setting_view.BargainInviteeView = {
	presentor = "BargainInviteeViewPresentor", showMode = ViewSetting.kModalWindow, extId = 257
}

setting_view.BargainShareView = {
	presentor = "BargainShareViewPresentor", showMode = ViewSetting.kWindow, extId = 257
}

setting_view.PayAttentionToAppView = {
	presentor = "PayAttentionToAppViewPresentor", showMode = ViewSetting.kNormalView, extId = 256
}

setting_view.PotionsRefineView = {
	presentor = "PotionsRefineViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 71, supportBack = ViewSetting.BackBig, showRead = true
}

setting_view.ProgressiveTurnTableView = {
	presentor = "ProgressiveTurnTableViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 254, supportBack = ViewSetting.BackBig
}

setting_view.ProgressiveTurnTablePreView = {
	presentor = "ProgressiveTurnTablePreViewPresentor", showMode = ViewSetting.kModalWindow, extId = 254
}

setting_view.ProgressiveTurnTableDetailView = {
	presentor = "ProgressiveTurnTableDetailViewPresentor", showMode = ViewSetting.kModalWindow, extId = 254
}

setting_view.ProgressiveTurnTableWishView = {
	presentor = "ProgressiveTurnTableWishViewPresentor", showMode = ViewSetting.kModalWindow, extId = 254
}

setting_view.ProgressiveTurnTableTreasureBoxView = {
	presentor = "ProgressiveTurnTableTreasureBoxViewPresentor", showMode = ViewSetting.kModalWindow, extId = 254
}

setting_view.ProgressiveTurnTableTreasureBoxPreView = {
	presentor = "ProgressiveTurnTableTreasureBoxPreViewPresentor", showMode = ViewSetting.kModalWindow, extId = 254
}

setting_view.TurtleLotteryView = {
	presentor = "TurtleLotteryViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 461, supportBack = ViewSetting.BackBig, syncCloseList = {"TurnTableTaskView", "TiredGiftView"}
}

setting_view.TurtleLotteryDetailView = {
	presentor = "TurtleLotteryDetailViewPresentor", showMode = ViewSetting.kModalWindow, extId = 461
}

setting_view.TurtleLotteryRecordView = {
	presentor = "TurtleLotteryRecordViewPresentor", showMode = ViewSetting.kModalWindow, extId = 461
}

setting_view.TurtleLotteryBonusView = {
	presentor = "TurtleLotteryBonusViewPresentor", showMode = ViewSetting.kModalWindow, extId = 461, openSound = 141098
}

setting_view.StarMatchGameView = {
	presentor = "StarMatchGameViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 263, supportBack = 101
}

setting_view.StarMatchGameDetailsView = {
	presentor = "StarMatchGameDetailsViewPresentor", showMode = ViewSetting.kModalWindow, extId = 263
}

setting_view.StarMatchGameRankView = {
	presentor = "StarMatchGameRankViewPresentor", showMode = ViewSetting.kModalWindow, extId = 263
}

setting_view.StarMatchGameSettlementView = {
	presentor = "StarMatchGameSettlementViewPresentor", showMode = ViewSetting.kWindow, extId = 263
}

setting_view.WeekendSignUpView = {
	presentor = "WeekendSignUpViewPresentor", showMode = ViewSetting.kNormalView, extId = 284
}

setting_view.CelebPartyHudView = {
	presentor = "CelebPartyHudViewPresentor", showMode = ViewSetting.kWindow, extId = 471
}

setting_view.CelebPartyActView = {
	presentor = "CelebPartyActViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 471, supportBack = 101, showRead = true
}

setting_view.TalentMainView = {
	presentor = "TalentMainViewPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true,openSound=141089,extId = -1,logId=108
}

setting_view.TalentInfoView = {
	presentor = "TalentInfoViewPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true,openSound=141089,extId = -1
}

setting_view.TalentCheckAnswerView = {
	presentor = "TalentCheckAnswerPresentor", showMode=ViewSetting.kModalWindow,supportBack=101, showRead=true,openSound=141089,extId = -1
}

setting_view.TalentCheckResultView = {
	presentor = "TalentCheckResultPresentor", showMode=ViewSetting.kModalWindow, openSound=141089,extId = -1
}
setting_view.TalentRewardView = {
	presentor = "TalentRewardPresentor", showMode=ViewSetting.kModalWindow, openSound=141089,extId = -1
}
setting_view.TalentExchangeRewardView = {
	presentor="TalentExchangeRewardPresentor",showMode=ViewSetting.kModalView,extId=-1,supportBack = ViewSetting.BackNoRecord,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.FlowerCloudGameView = {
	presentor = "FlowerCloudGameViewPresentor", showMode=ViewSetting.kModalView,extId = -1,supportBack = ViewSetting.BackBig,hideHUD = true
}

setting_view.FlowerCloudGameRankPanel = {
	presentor="FlowerCloudGameRankPresentor",showMode=ViewSetting.kModalWindow,extId=-1,supportBack = ViewSetting.BackSmall
}

setting_view.HuaYunGamePanel = {
	presentor="HuaYunGamePanelPresentor",showMode=ViewSetting.kWindow,extId=20
}

setting_view.GodOfFlowerTaskView = {
	presentor = "GodOfFlowerTaskPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true,openSound=141089, extId = -1
}

setting_view.WeekEndView = {
	presentor = "WeekEndViewPresentor",showMode = ViewSetting.kNormalView,extId = 126
}

setting_view.FashionActLimitDocView = {
	presentor = "FashionActLimitDocPresentor",showMode = ViewSetting.kModalWindow,extId = 132,supportBack=102
}

setting_view.FashionActLimitDocPreview = {
	presentor = "FashionActLimitDocPreviewPresentor",showMode=ViewSetting.kModalWindow,extId = 132,supportBack=102
}

setting_view.FashionActLimitDocSinglePanel = {
	presentor = "FashionActLimitDocSinglePanelPresentor",showMode=ViewSetting.kModalWindow,extId = 132,supportBack=102
}

setting_view.ElfBattleRankingView = {
	presentor = "ElfBattleRankingPresentor", showMode = ViewSetting.kModalWindow, supportBack = ViewSetting.BackBig, showRead=true, hideHUD = true, extId = 50,openSound=141085,
}

setting_view.RankingListView = {
	presentor = "RankingListViewPresentor", showMode=ViewSetting.kFullScreenWindow, supportBack = ViewSetting.BackBig, showRead=true,extId=53,logId=171,noPadMask=true
}

setting_view.RankingListShowView = {
	presentor = "RankingListShowViewPresentor", showMode=ViewSetting.kWindow, extId=53,
}

setting_view.FashionRankingListView = {
	presentor = "FashionRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.FurnitureRankingListView = {
	presentor = "FurnitureRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.FortuneRankingListView = {
	presentor = "FortuneRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.IslandRankingListView = {
	presentor = "IslandRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.TravellerRankingListView = {
	presentor = "TravellerRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.IslanderRankingListView = {
	presentor = "IslanderRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.FlowerKingRankingListView = {
	presentor = "FlowerKingRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.FriendshipPowerRankingListView = {
	presentor = "FriendshipPowerRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.ChangeClothesRankingListView = {
	presentor = "ChangeClothesRankingListPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.FriendshipRankingListView = {
	presentor = "FriendshipRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.AchievementRankingListView = {
	presentor = "AchievementRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.ArchiveRankingListView = {
	presentor = "ArchiveRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.CouncilRankingListView = {
	presentor = "CouncilRankingListViewPresentor", showMode=ViewSetting.kWindow,extId=53, supportBack = ViewSetting.BackNoRecord
}

setting_view.GameMachineView = {
	presentor = "GameMachineViewPresentor", showMode=ViewSetting.kFullScreenWindow, supportBack = ViewSetting.BackBig, extId = -1, logId = 173
}

setting_view.NewGameMachinePanel = {
	presentor = "NewGameMachineViewPresentor", showMode=ViewSetting.kFullScreenWindow, supportBack = ViewSetting.BackBig, extId = -1, logId = 173
}

setting_view.LotteryCollect = {
	presentor = "LotteryCollectPresentor", showMode=ViewSetting.kModalWindow,extId=12
}

setting_view.ClothesStage = {
	presentor = "ClothesStagePresentor", showMode = ViewSetting.kModalWindow, showRead=true, hideHUD = true, extId = 3, supportBack = ViewSetting.BackSmall
}
setting_view.WerewolfSelectModePanel = {
	presentor = "WerewolfSelectModePanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.WerewolfJoinRoomSelectPanel = {
	presentor = "WerewolfJoinRoomSelectPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.WerewolfCreateRoomPanel = {
	presentor = "WerewolfCreateRoomPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.WerewolfInvitePanel = {
	presentor = "WerewolfInvitePanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.WerewolfPreparePanel = {
	presentor = "WerewolfPreparePanelPresentor", showMode = ViewSetting.kWindow
}
setting_view.WerewolfMeetingInformPanel = {
	presentor = "WerewolfMeetingInformPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.WerewolfMeetingPanel = {
	presentor = "WerewolfMeetingPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.WerewolfMeetingConfirmPanel= {
	presentor = "WerewolfMeetingConfirmPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfVoteResultPanel= {
	presentor = "WerewolfVoteResultPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfTinyMapPanel= {
	presentor = "WerewolfTinyMapPresentor", showMode=ViewSetting.kWindow
}
setting_view.WerewolfRoomTestPanel= {
	presentor = "WerewolfRoomTestPanelPresentor", showMode=ViewSetting.kWindow
}
setting_view.WerewolfMissionTestPanel= {
	presentor = "WerewolfMissionTestPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfGamePanel= {
	presentor = "WerewolfGamePanelPresentor", showMode=ViewSetting.kWindow
}
setting_view.WerewolfVictoryPanel= {
	presentor = "WerewolfVictoryPanelPresentor", showMode=ViewSetting.kFullScreenWindow
}
setting_view.WerewolfResultPanel= {
	presentor = "WerewolfResultPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfConfirmRolePanel= {
	presentor = "WerewolfConfirmRolePanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfGameStartPanel= {
	presentor = "WerewolfGameStartPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfConfirmTeamPanel= {
	presentor = "WerewolfConfirmTeamPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfMvpPanel= {
	presentor = "WerewolfMvpPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfBigMapPanel= {
	presentor = "WerewolfBigMapPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfBeingAttackPanel= {
	presentor = "WerewolfBeingAttackPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfUseTunnelPanel ={
	presentor = "WerewolfUseTunnelPanelPresentor", showMode=ViewSetting.kWindow
}
setting_view.WerewolfMonitoringPanel ={
	presentor = "WerewolfMonitoringPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfTaskingPanel = {
	presentor = "WerewolfTaskingPanelPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.WerewolfWaitTipsPanel = {
	presentor = "WerewolfWaitTipsPanelPresentor", showMode=ViewSetting.kWindow
}
setting_view.WerewolfNegativeDialogView =  {
	presentor="WerewolfNegativePunishTipsPresentor",showMode=2,extId=-1,openSound=SoundManager.NegativeMessage,closeSound=SoundManager.DefaultClosePanel
}
setting_view.WerewolfLoveModeRulePanel = {
	presentor = "WerewolfLoveModeRulePanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfMatchModeSelectPanel = {
	presentor = "WerewolfMatchModeSelectPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.WerewolfDailyNoticePanel =  {
	presentor="WerewolfDailyNoticePanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1,
}

setting_view.FriendBreezeView = {
	presentor = "FriendBreezePresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.FriendBreezeCarrierView = {
	presentor = "FriendBreezeCarrierPresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.FriendCheckSentenceView = {
	presentor = "FriendCheckSentencePresentor", showMode = ViewSetting.kModalWindow,extId = -1
}

setting_view.ActivityPreviewPanel = {
	presentor = "ActivityPreviewPresentor", showMode=ViewSetting.kModalWindow,extId=-1,
}
setting_view.OpenServiceCountdownView =  {
	presentor="OpenServiceCountDownPresentor",showMode=ViewSetting.kNormalView,extId=-1,
}
setting_view.ActivityExclusiveGiftView =  {
	presentor="ActivityExclusiveGiftPresentor",showMode=ViewSetting.kNormalView,extId=-1,
}
setting_view.ActivityExclusiveGiftViewPopup =  {
	presentor="ActivityExclusiveGiftPresentorPopup",showMode=ViewSetting.kModalWindow,extId=-1,
}
setting_view.TapTapView =  {
	presentor="TapTapViewPresentor",showMode=ViewSetting.kModalWindow,extId=-1,
}

setting_view.ArcheryGameReadyView = {
	presentor="ArcheryGameReadyViewPresentor",showMode=ViewSetting.kFullScreenView,extId = 144, supportBack=101
}

setting_view.ArcheryGamePlayView = {
	presentor = "ArcheryGamePlayViewPresentor", showMode = ViewSetting.kFullScreenView,extId = 144
}

setting_view.ArcheryGameResultView = {
	presentor = "ArcheryGameResultViewPresentor", showMode = ViewSetting.kModalWindow,extId = 144
}

setting_view.SelectYourIslandView =  {
	presentor="SelectIslandViewPresentor",showMode=ViewSetting.kNormalView,extId=-1,
}

setting_view.SnakerGameView = {
	presentor = "SnakerGameViewPresentor", showMode=ViewSetting.kFullScreenView,extId= 150
}

setting_view.Snake2GameView = {
	presentor = "Snake2ViewPresentor", showMode=ViewSetting.kNormalView ,extId= 98
}

setting_view.ActivityRank = {
	presentor = "ActivityRankPresentor", showMode = ViewSetting.kModalView, extId = 140, supportBack = ViewSetting.BackSmall
}
setting_view.StarMatchScreen = {
	presentor="StarMatchScreenPresentor",showMode=ViewSetting.kFullScreenWindow,extId=140, showRead=true, hideHUD=true, supportBack = ViewSetting.BackBig
}
setting_view.StarMatchSignup = {
	presentor="StarMatchSignupPresentor",showMode=ViewSetting.kModalWindow,extId=261, showRead=true, hideHUD=true, supportBack = ViewSetting.BackBig
}
setting_view.StarMatchSignup2 = {
	presentor="StarMatchSignupPresentor2",showMode=ViewSetting.kFullScreenWindow,extId=140, showRead=true, hideHUD=true
}
setting_view.StarMatchTeamInfo = {
	presentor="StarMatchTeamInfoPresentor",showMode=ViewSetting.kModalWindow,extId=261
}
setting_view.StarMatchShare = {
	presentor="StarMatchSharePresentor",showMode=ViewSetting.kModalWindow,extId=261, supportBack = ViewSetting.BackBig
}
setting_view.StarMatchJoinSuc = {
	presentor="StarMatchJoinSucPresentor",showMode=ViewSetting.kModalWindow,extId=261, showRead=true, hideHUD=true
}
setting_view.StarMatchSchedule = {
	presentor="StarMatchSchedulePresentor",showMode=ViewSetting.kFullScreenWindow,extId=140, showRead=true, hideHUD=true, supportBack = ViewSetting.BackBig
}

setting_view.RankingListPanel = {
	presentor = "RankingListPanelPresentor", showMode=ViewSetting.kModalWindow,extId=53, hideHUD=true, showRead=true
}

setting_view.StarGroceryView = {
	presentor = "StarGroceryViewPresentor", showMode=ViewSetting.kFullScreenWindow,extId= 146, hideHUD=true, showRead=true,supportBack = ViewSetting.BackBig
}

setting_view.StarGroceryTinyGameView = {
	presentor = "StarGroceryTinyGamePresentor", showMode=ViewSetting.kFullScreenWindow,extId= 146, hideHUD=true, showRead=true,supportBack = ViewSetting.BackBig

}

setting_view.StarQuestion = {
	presentor = "StarQuestionPresentor", showMode=ViewSetting.kFullScreenView,extId= -1, hideHUD=true, showRead=true
}

setting_view.StarQuestionAward = {
	presentor = "StarQuestionAwardPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, showRead=true
}


setting_view.StarGamePhotoAlbum = {
	presentor = "StarGamePhotoAlbumPenelPresentor", showMode=ViewSetting.kModalWindow,extId= 146, hideHUD=true, showRead=true
}

setting_view.StarGamePhotoAlbumView = {
	presentor = "StarGamePhotoAlbumViewPresentor", showMode=ViewSetting.kModalWindow,extId= 146, hideHUD=true, showRead=true
}

setting_view.StarGamePhotoAlbumAwards = {
	presentor = "StarGamePhotoAlbumAwardsPresentor", showMode=ViewSetting.kModalWindow,extId= 146, hideHUD=true, showRead=true
}

setting_view.StarGamePhotoAlbumCompleteview = {
	presentor = "StarGamePhotoAlbumCompleteviewPresentor", showMode=ViewSetting.kModalWindow,extId= 146, hideHUD=true, showRead=true
}

setting_view.StarGamePhotoAlbumGetBadge = {
	presentor = "StarGamePhotoAlbumGetBadgeViewPresentor", showMode=ViewSetting.kModalWindow,extId= 146, hideHUD=true, showRead=true
}

setting_view.StarGameReporterView = {
	presentor = "StarGameReporterPresentor", showMode=ViewSetting.kFullScreenView,extId= -1, hideHUD=true
}

setting_view.StarMatchMapView = {
	presentor = "StarMatchMapPresentor", showMode=ViewSetting.kModalWindow,extId= 140, hideHUD=true, openSound=141356
}

setting_view.StarTaskView = {
	presentor = "StarTaskViewPresentor", showMode=ViewSetting.kFullScreenView,extId= 140, hideHUD=true
}

setting_view.StarGameSigninView = {
	presentor = "StarGameSigninPresentor", showMode=ViewSetting.kFullScreenView,extId= -1, hideHUD=true
}
setting_view.StarStoreView = {
	presentor = "StarStoreViewPresentor", showMode=ViewSetting.kFullScreenView,extId= -1, hideHUD=true
}

setting_view.StarGameAwardView = {
	presentor="StarGameAwardViewPresentor",showMode=ViewSetting.kModalWindow,extId=140,
}

setting_view.StarGameAwardCutScnene = {
	presentor = "StarGameAwardCutScnenePresentor", showMode=ViewSetting.kNormalView,supportBack = ViewSetting.BackBig,extId=45
}


setting_view.StarFarewellLetterView = {
	presentor="StarFarewellLetterViewPresentor",showMode=ViewSetting.kModalWindow,extId=140,
}

setting_view.OpeningceremonyStore = {
	presentor="OpeningceremonyStoreViewPresentor",showMode=ViewSetting.kFullScreenView,extId=128,supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.ActOCMap = {
	presentor = "ActOCMapPresentor", showMode=ViewSetting.kModalWindow,extId=153,supportBack = ViewSetting.BackBig,hideHUD = true,openSound=141358
}

setting_view.OpeningCeremonyCreditView = {
	presentor="OpeningCeremonyCreditPresentor",showMode=ViewSetting.kFullScreenView,extId=153,supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.OpeningCeremonyPlotTaskView = {
	presentor="OpeningCeremonyPlotTaskPresentor",showMode=ViewSetting.kFullScreenView,extId=153,supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.Postcard = {
	presentor = "PostcardPresentor", showMode=ViewSetting.kFullScreenWindow,extId=157
}

setting_view.SeekPostcard = {
	presentor = "SeekPostcardPresentor", showMode=ViewSetting.kModalWindow,extId=157,hideHUD = true
}

setting_view.CouncilSpaView = {
	presentor = "CouncilSpaViewPresentor", showMode=ViewSetting.kNormalView,extId=45
}

setting_view.VideoSkipView = {
	presentor = "VideoSkipPresentor", showMode=ViewSetting.kWindow,extId=45,supportBack=101
}

setting_view.LoginVideoView = {
	presentor = "LoginVideoViewPresentor", showMode=ViewSetting.kWindow
}

setting_view.TurnTableView = {
	presentor = "TurnTableViewPresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig
}

setting_view.TurnTableAwardPreviewView = {
	presentor="TurnTableAwardPreviewPresentor",showMode=ViewSetting.kModalWindow,extId=161,autoDestroyTime=10,supportBack=ViewSetting.BackSmall
}

setting_view.TurnTableSelectView = {
	presentor="TurnTableSelectPresentor",showMode=ViewSetting.kModalWindow,extId=161,autoDestroyTime=10,supportBack=ViewSetting.BackSmall
}

setting_view.TurnTableBoxPreviewView = {
	presentor="TurnTableBoxPreviewPresentor",showMode=ViewSetting.kModalWindow,extId=161,autoDestroyTime=10,supportBack=ViewSetting.BackSmall
}

setting_view.TurnTableJumpReward = {
	presentor="TurnTableJumpRewardPresentor",showMode=ViewSetting.kFullScreenWindow,extId=161,autoDestroyTime=10
}

setting_view.WorkshopUpgradeWait = {
	presentor = "WorkshopUpgradeWaitPresentor", showMode=ViewSetting.kFullScreenView,extId=45
}
setting_view.OCBigHeadMakerMainView = {
	presentor = "OCBigHeadMakerViewPresentor",showMode = ViewSetting.kModalView,extId = 158
}

setting_view.AirShipPartyMemberView = {
	presentor = "AirShipPartyMemberViewPresentor",showMode = ViewSetting.kModalWindow,extId = 158
}

setting_view.AirShipPartySignPanel = {
	presentor = "AirShipPartySignPanelPresentor",showMode = ViewSetting.kFullScreenView,extId = 158
}

setting_view.AirShipPosterPanel = {
	presentor = "AirShipPosterPanelPresentor",showMode = ViewSetting.kFullScreenView,extId = 158,supportBack = ViewSetting.BackBig
}

setting_view.WorkShopUpgradeLimitView = {
	presentor = "WorkShopUpgradeLimitPresentor",showMode = ViewSetting.kModalView,extId = -1
}


setting_view.AffinityLevelUpItemView = {
	presentor = "AffinityLevelUpItemPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ArtPublicitySharingView = {
	presentor = "ArtPublicitySharingPresentor",showMode = ViewSetting.kFullScreenView,extId = -1
}

setting_view.QiXiSignPanel = {
	presentor = "QiXiSignPanelPresentor",showMode = ViewSetting.kFullScreenView,extId = -1
}

setting_view.ChestRewardPreview = {
	presentor = "ChestRewardPreviewPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ChestOwnSelect = {
	presentor = "ChestOwnSelectPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.ChestMultiSelect = {
	presentor = "ChestMultSelectPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.TurntableChestSelect = {
	presentor = "TurnTableChestSelectPresentor",showMode = ViewSetting.kModalView,extId = -1
}

setting_view.AdvertisingView = {
	presentor = "AdvertisingViewPresentor", showMode=ViewSetting.kModalWindow,extId=100
}

setting_view.PosePanel =  {
	presentor="PosePanelPresentor",showMode=1,extId=2,autoDestroyTime=10
}

setting_view.ClothesActionPanel =  {
	presentor="ClothesActionPanelPresentor",showMode=1,extId=2,autoDestroyTime=10
}

setting_view.LimitTimePetView =  {
	presentor="LimitTimePetPresentor",showMode=ViewSetting.kNormalView,extId=100,
}
setting_view.LimitTimePetView2 =  {
	presentor="LimitTimePetPresentor2",showMode=ViewSetting.kNormalView,extId=100,
}
setting_view.ActivityTaskADView = {
	presentor="ActivityTaskADV2Presentor",showMode=ViewSetting.kNormalView,extId=100,
}

setting_view.ActivityPetExpediton =  {
	presentor="ActivityPetExpeditonPresentor",showMode=ViewSetting.kNormalView,extId=100,
}

--预留10个宠物面板配置
for i = 100 , 110 do
	setting_view["LimitTimePetView_" .. i] = {
		presentor="CommonLimitTimePetPresentor",
		showMode=ViewSetting.kNormalView,extId=100
	}
end
--预留5个ugc面板配置
for i = 1 , 5 do
	setting_view["CommonCalendarUGC_" .. i] = {
		presentor="CommonCalendarUGCPresentor",
		showMode=ViewSetting.kNormalView,extId=-1
	}
end


setting_view.DecodeGameView = {
	presentor = "DecodeGameViewPresentor", showMode=ViewSetting.kFullScreenView,extId= -1, hideHUD=true, supportBack=101
}

setting_view.DecodeGameDetailView = {
	presentor = "DecodeGameDetailViewPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true
}

setting_view.DecodeGameAnswerStView = {
	presentor = "DecodeGameAnswerStViewPresentor",showMode=ViewSetting.kModalWindow,extId= -1,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.FullMoonGroceryView = {
	presentor = "FullMoonGroceryViewPresentor", showMode=ViewSetting.kNormalView,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}

setting_view.FullMoonMainView = {
	presentor = "FullMoonMainPresentor", showMode=ViewSetting.kFullScreenView,syncCloseList = {"ActivityRuleCommon"},supportBack=101
}
setting_view.FullMoonOpeningView = {
	presentor = "FullMoonOpeningViewPresentor", showMode=ViewSetting.kNormalView,
}
setting_view.FullMoonWorkShopView = {
	presentor = "FullMoonWorkShopViewPresentor", showMode=ViewSetting.kNormalView,
}
setting_view.FullMoonShopView = {
	presentor = "FullMoonShopViewPresentor", showMode=ViewSetting.kNormalView,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}
setting_view.FullMoonTaskView = {
	presentor = "FullMoonTaskViewPresentor", showMode=ViewSetting.kNormalView,
}
setting_view.StrangeFoodTaskView = {
	presentor = "StrangeFoodTaskViewPresentor", showMode=ViewSetting.kNormalView,
}

setting_view.StrangeFoodShopView = {
	presentor = "StrangeFoodShopPresentor", showMode=ViewSetting.kNormalView,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}

setting_view.StrangeFoodOrder = {
	presentor = "StrangeFoodOrderPresentor", showMode=ViewSetting.kFullScreenView,supportBack=ViewSetting.BackBig,
}
setting_view.WerewolfStory = {
	presentor = "WerewolfStoryPresentor", showMode=ViewSetting.kNormalView, supportBack=101
}

setting_view.WerewolfFindDiffEntry = {
	presentor = "WerewolfFindDiffEntryPresentor",supportBack=ViewSetting.BackBig, showMode=ViewSetting.kFullScreenView
}

setting_view.WerewolfFindDiff = {
	presentor = "WerewolfFindDiffPresentor",supportBack=ViewSetting.BackBig, showMode=ViewSetting.kFullScreenView
}

setting_view.WerewolfFindDiffResult = {
	presentor = "WerewolfFindDiffResultPresentor", showMode=ViewSetting.kModalView
}


setting_view.PassThroughLaser = {
	presentor = "PassThroughLaserPresentor", showMode=ViewSetting.kFullScreenView,extId= 7, hideHUD=true
}
setting_view.CommonMultiRankPanel = {
	presentor = "CommonMultiRankPanelPresentor", showMode=ViewSetting.kModalWindow, supportBack=ViewSetting.BackSmall
}
setting_view.GameRoomEntryPanel = {
	presentor = "GameRoomEntryPresentor", showMode = ViewSetting.kFullScreenView, supportBack=ViewSetting.BackBig, noPadMask = true
}

setting_view.GameRoomListPanel = {
	presentor = "GameRoomListPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.GameRoomPreparePanel = {
	presentor = "GameRoomPreparePanelPresentor", showMode = ViewSetting.kWindow
}
setting_view.GameRoomPrepareExitBtnPanel = {
	presentor = "GameRoomPrepareExitBtnPanelPresentor", showMode = ViewSetting.kWindow
}
setting_view.GameRoomSettingPanel = {
	presentor = "GameRoomSettingPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.GameRoomIptPasswordPanel = {
	presentor = "GameRoomIptPasswordPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.GameRoomChatPanel = {
	presentor = "GameRoomChatPanelPresentor", showMode = ViewSetting.kNormalView
}
setting_view.GameRoomChatHudPanel = {
	presentor = "GameRoomChatHudPanelPresentor", showMode = ViewSetting.kNormalView
}
setting_view.GameRoomNegativeTipsPanel = {
	presentor = "GameRoomNegativeTipsPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.GameRoomNegativeRulePanel = {
	presentor = "GameRoomNegativeRulePanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.GameRoomMatchSuccPanel = {
	presentor = "GameRoomMatchSuccPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.OpenDoor = {
	presentor = "OpenDoorPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}
setting_view.CloseJar = {
	presentor = "CloseJarPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}


setting_view.StarWishCardView = {
	presentor = "StarWishCardViewPresentor",showMode = ViewSetting.kNormalView,extId = 160,supportBack = ViewSetting.BackSmall,
}

setting_view.FlopGameReadyView = {
	presentor="FlopGameReadyViewPresentor",showMode=ViewSetting.kFullScreenView,extId = 173, supportBack=101
}

setting_view.CouncilBossRuleView = {
	presentor="CouncilBossRuleViewPresentor",showMode=ViewSetting.kModalView,extId = 45, supportBack=ViewSetting.BackSmall
}

setting_view.FlopGameMainGameView = {
	presentor="FlopGameMainGameViewPresentor",showMode=ViewSetting.kFullScreenView,extId = 173, supportBack=101
}

setting_view.WerewolfActivitySigninView = {
	presentor="WerewolfActivitySigninPresentor",showMode=ViewSetting.kFullScreenView,extId = 170, supportBack=101
}

setting_view.WereWolfGrocery = {
	presentor="WereWolfGroceryPresentor",showMode=ViewSetting.kFullScreenView,extId = 177, supportBack=101
}

setting_view.WereWolfMapView = {
	presentor = "WereWolfMapPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, supportBack=101
}

setting_view.WereWolfExperimentView = {
	presentor = "WereWolfExperimentViewPresentor", showMode=ViewSetting.kFullScreenView,extId= 176, hideHUD=true, supportBack=101
}

setting_view.FriendDawnView = {
	presentor = "FriendDawnPresentor",showMode = ViewSetting.kWindow,extId = -1,hideHUD=true
}
setting_view.AobiReturnPanel = {
	presentor = "AobiReturnPanelPresentor", showMode=ViewSetting.kFullScreenView, supportBack=101
}

setting_view.AobiReturnChangeView = {
	presentor = "AobiReturnChangeViewPresentor", showMode=ViewSetting.kNormalView
}
setting_view.AobiReturnPartyView = {
	presentor = "AobiReturnPartyPresentor", showMode=ViewSetting.kNormalView
}
setting_view.AobiReturnPartyGameItem = {
	presentor="AobiReturnPartyItemPresentor", showMode=ViewSetting.kModalWindow,extId=68
}
setting_view.AobiReturnPartyMemberView = {
	presentor = "AobiReturnPartyMemberPresentor",showMode = ViewSetting.kModalWindow,extId = 68
}
setting_view.AobiReturnPartyInviteView = {
	presentor = "AobiReturnPartyInvitePresentor",showMode = ViewSetting.kModalWindow,extId = 68
}

setting_view.AobiReturnShopView = {
	presentor = "AobiReturnShopViewPresentor", showMode=ViewSetting.kNormalView,syncCloseList = {"RechargeStoreBuyItem","RechargeGiftBag"}
}

setting_view.AobiReturnSelectRewardPanel = {
	presentor = "AobiReturnSelectRewardPanelPresentor", showMode=ViewSetting.kModalView
}
setting_view.AobiReturnAwardPanel = {
	presentor = "AobiReturnAwardPanelPresentor", showMode=ViewSetting.kModalView
}

setting_view.ActivityDressRaceView = {
	presentor = "ActivityDressRacePresentor", showMode=ViewSetting.kFullScreenView,extId = -1,supportBack = ViewSetting.BackBig
}

setting_view.ActivityDressRaceRankView = {
	presentor = "ActivityDressRaceRankPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.DressRaceView = {
	presentor = "DressRacePresentor", showMode=ViewSetting.kFullScreenView,extId = -1
}

setting_view.DressRaceMatchingView = {
	presentor = "DressRaceMatchingPresentor", showMode=ViewSetting.kFullScreenView,extId = -1
}

setting_view.DressRaceResultView = {
	presentor = "DressRaceResultPresentor", showMode=ViewSetting.kModalView,extId = -1
}

setting_view.ChatEmojiView = {
	presentor = "ChatEmojiPresentor", showMode=ViewSetting.kWindow,extId = -1
}


setting_view.RewardsPreview = {
	presentor = "RewardsPreviewPresentor", showMode=ViewSetting.kModalView
}setting_view.StarSpecialProductionView =  {
	presentor="StarSpecialProductionViewPresentor",showMode=ViewSetting.kNormalView,extId=180,
}

setting_view.StarCollectSpecialPanel =  {
	presentor="StarCollectSpecialPanelPresentor",showMode=ViewSetting.kNormalView,extId=180,
}

setting_view.FreeRace =  {
	presentor="FreeRacePresentor",showMode=ViewSetting.kModalView,extId=57,
}


setting_view.RewardsPreview = {
	presentor = "RewardsPreviewPresentor", showMode=ViewSetting.kModalView
}

setting_view.GMActivityView = {
	presentor = "GMActivityViewPresentor", showMode=ViewSetting.kModalView
}


setting_view.Gobang = {
	presentor = "GobangPresentor", showMode = ViewSetting.kFullScreenView, extId = 60
}
setting_view.GobangResultTip = {
	presentor = "GobangResultTipPresentor", showMode = ViewSetting.kModalView, extId = 60
}
setting_view.GobangResult = {
	presentor = "GobangResultPresentor", showMode = ViewSetting.kModalView, extId = 60
}
setting_view.GobangAnswer = {
	presentor = "GobangAnswerPresentor", showMode = ViewSetting.kModalView, extId = 60
}

setting_view.FriendDawnSelectCarrierView = {
	presentor = "FriendDawnSelectCarrierPresentor", showMode = ViewSetting.kWindow, extId = 47
}

setting_view.OpenLetter = {
	presentor = "OpenLetterPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}

setting_view.CheckFragment = {
	presentor = "CheckFragmentPresentor", showMode=ViewSetting.kFullScreenView,extId= 7, hideHUD=true
}

setting_view.AnalyseClue = {
	presentor = "AnalyseCluePresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}

setting_view.Dive = {
	presentor = "DivePresentor", showMode=ViewSetting.kFullScreenView,extId= 7, hideHUD=true
}

setting_view.DriveShip = {
	presentor = "DriveShipPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}

setting_view.OutDark = {
	presentor = "OutDarkPresentor", showMode=ViewSetting.kFullScreenView,extId= 7, hideHUD=true
}

setting_view.StartMagic = {
	presentor = "StartMagicViewPresentor", showMode=ViewSetting.kModalView,extId= 7, hideHUD=true
}

setting_view.Compound = {
	presentor = "CompoundViewPresentor", showMode=ViewSetting.kModalView,extId= 7, hideHUD=true
}

setting_view.AnalyseClueDetailView = {
	presentor = "AnalyseClueDetailViewPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}

setting_view.Anniversary14View = {
	presentor="Anniversary14ViewPresentor",showMode=ViewSetting.kFullScreenView,extId=188,hideHUD=true
}

setting_view.ActMultiRechargePanel = {
	presentor = "ActMultiRechargePanelPresentor", showMode=ViewSetting.kWindow
}

setting_view.ActivityTotalRecharge = {
	presentor = "ActivityTotalRechargePresentor", showMode=ViewSetting.kWindow
}

setting_view.NewHandRechargeView = {
	presentor = "NewHandRechargePresentor", showMode=ViewSetting.kWindow
}

setting_view.NavigatorReplyEdit = {
	presentor="NavigatorReplyEditViewPresentor",showMode=ViewSetting.kModalView
}

setting_view.NavigatorLikeView = {
	presentor="NavigatorLikeViewPresentor",showMode=ViewSetting.kModalView
}

setting_view.HappyBreadView = {
	presentor="HappyBreadViewPresentor",showMode=ViewSetting.kFullScreenView,extId=189,hideHUD=true,supportBack=ViewSetting.BackBig
}

setting_view.HappyBreadGameView = {
	presentor="HappyBreadGameViewPresentor",showMode=ViewSetting.kFullScreenView,extId=189,hideHUD=true,supportBack = ViewSetting.BackBig
}

setting_view.MagicalLandInteractionOne = {
	presentor="MagicalLandInteractionOnePresentor",showMode=ViewSetting.kFullScreenView,hideHUD=true
}

setting_view.MagicAllShop = {
	presentor="MagicallShopPresentor",showMode = ViewSetting.kModalView,hideHUD = true
}

setting_view.AppleInvite = {
	presentor = "AppleInvitePresentor",showMode = ViewSetting.kNormalView,extId = 181,supportBack = ViewSetting.BackSmall,
}
setting_view.AppleInvite1 = {
	presentor = "AppleInvitePresentor1",showMode = ViewSetting.kNormalView,extId = 125,supportBack = ViewSetting.BackSmall,
}

setting_view.InviteNewbieView =  {
	presentor="InviteNewbiePresentor",showMode=ViewSetting.kModalView,extId=-1,hideHUD = false,supportBack = ViewSetting.BackBig
}
setting_view.InviteNewbieRecordFrame =  {
	presentor="InviteNewbieRecordPresentor",showMode=ViewSetting.kModalView,extId=-1,hideHUD = false
}
setting_view.InviteNewbieBindingCodeView =  {
	presentor="InviteNewBindingCodePresentor",showMode=ViewSetting.kModalWindow,extId=-1,hideHUD = false
}
setting_view.InviteNewbieGraduateView =  {
	presentor="InviteNewbieGraduatePresentor",showMode=ViewSetting.kModalWindow,extId=-1,hideHUD = false
}

setting_view.AffinityMapView = {
	presentor = "AffinityMapViewPresentor", showMode=ViewSetting.kModalWindow, hideHUD=true
}

setting_view.AsideView = {
	presentor = "AsideViewPresentor", showMode=ViewSetting.kNormalView
}

setting_view.Magicalland2Book = {
	presentor = "Magicalland2BookViewPresentor", showMode = ViewSetting.kModalView
}

setting_view.Magicalland2RewardView = {
	presentor = "Magicalland2RewardViewPresentor", showMode = ViewSetting.kModalView
}


setting_view.DreamFactoryView = {
	presentor = "DreamFactoryViewPresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
	syncCloseList={"TurnTableTaskView","TiredGiftView"}
}

setting_view.TurnTableTaskView = {
	presentor = "TurnTableTaskViewPresentor", showMode=ViewSetting.kModalView,extId=161
}
setting_view.TiredGiftView = {
	presentor = "TiredGiftViewPresentor", showMode=ViewSetting.kModalView,extId=161
}

setting_view.StrangeFoodWorkView = {
	presentor = "StrangeFoodWorkViewPresentor", showMode=ViewSetting.kNormalView
}

setting_view.FavorabilityView = {
	presentor = "FavorabilityViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.NationalDayPanel = {
	presentor = "NationalDayPanelPresentor",showMode = ViewSetting.kFullScreenView,
}

--奇食会盲盒
setting_view.BlindBoxView =  {
	presentor="BlindBoxPresentor",showMode=ViewSetting.kFullScreenView,extId=-1
}
setting_view.BlindBoxPreviewView =  {
	presentor="BlindBoxPreviewPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.EditRemarksPanel =  {
	presentor="EditRemarksPanelPresentor",showMode=ViewSetting.kModalWindow
}

--奇食会签到
setting_view.StrangeFoodSigninView = {
	presentor = "StrangeFoodSigninViewPresentor", showMode=ViewSetting.kNormalView,extId=196
}

setting_view.FavorabilityTaskView = {
	presentor = "FavorabilityTaskViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FavorabilityNPCView = {
	presentor = "FavorabilityNPCViewPresentor", showMode = ViewSetting.kFullScreenView,logId=103
}

setting_view.AlbumPanel = {
	presentor = "AlbumPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.SelectAlbumPanel = {
	presentor = "SelectAlbumPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.CommonSigninView = {
	presentor = "CommonSigninViewPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.MagicalLandInteractionTwo = {
	presentor = "MagicalLandInteractionTwoPresentor", showMode = ViewSetting.kFullScreenView
}

setting_view.RecordScreenPanel = {
	presentor = "RecordScreenPanelPresentor", showMode = ViewSetting.kModalWindow
}


setting_view.ExcessiveBlackView = {
	presentor="ExcessiveBlackPresentor",showMode=ViewSetting.kWindow
}


setting_view.MoveHouseGamePanel = {
	presentor = "MoveHouseGamePanelPresentor"--, showMode = ViewSetting.kNormalView
}

setting_view.MoveHouseCreateRoomPanel = {
	presentor = "MoveHouseCreateRoomPanelPresentor", showMode = ViewSetting.kModalView
}

setting_view.MoveHouseResultPanel = {
	presentor = "MoveHouseResultPanelPresentor", showMode = ViewSetting.kModalView
}

setting_view.MoveHouseEntryPanel = {
	presentor = "MoveHouseEntryPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack=ViewSetting.BackBig
}

setting_view.MoveHouseGameChatPanel = {
	presentor="MoveHouseGameChatPanelPresentor",showMode=ViewSetting.kWindow
}

setting_view.MoveHouseRewardPreviewPanel = {
	presentor = "MoveHouseRewardPreviewPanelPresentor", showMode = ViewSetting.kModalView
}

setting_view.Snake2GameCreateRoomPanel = {
	presentor = "Snake2GameCreateRoomPanelPresentor", showMode = ViewSetting.kModalView
}
setting_view.Snake2GamePreparePanel = {
	presentor = "Snake2GamePreparePanelPresentor", showMode = ViewSetting.kWindow
}
setting_view.Snake2GameMatchSuccPanel = {
	presentor = "Snake2GameMatchSuccPanelPresentor", showMode = ViewSetting.kModalView
}
setting_view.Snake2GameMatchModeSelectPanel = {
	presentor = "Snake2GameMatchModeSelectPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.BeeSportMap = {
	presentor = "BeeSportMapPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, supportBack=101
}

setting_view.BeeTaskStoryView = {
	presentor = "BeeTaskStoryPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true, extId = -1
}

setting_view.BeeSportShop = {
	presentor = "BeeSportShopPresentor", showMode=ViewSetting.kFullScreenView,
	extId= -1, hideHUD=true, supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.BeeDrawGameView = {
	presentor = "BeeDrawGameViewViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 205
}

setting_view.BeeDrawPhotoView = {
	presentor = "BeeDrawPhotoViewViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 205
}

setting_view.BeeDrawView = {
	presentor = "BeeDrawViewViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 205,supportBack=ViewSetting.BackBig
}

setting_view.BeeDrawCollectionView = {
	presentor = "BeeDrawCollectionViewViewPresentor", showMode = ViewSetting.kModalView, extId = 205
}

setting_view.BeeDrawCollectSpecialPanel = {
	presentor = "BeeDrawCollectSpecialPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.CommonActivityTask = {
	presentor = "CommonActivityTaskPresentor", showMode = ViewSetting.kFullScreenView, supportBack=ViewSetting.BackBig
}

setting_view.JumpBoxView = {
	presentor = "JumpBoxViewPresentor", showMode=ViewSetting.kFullScreenView, extId = 204, supportBack=ViewSetting.BackBig
}

setting_view.JumpBoxGameView = {
	presentor = "JumpBoxGameViewPresentor", showMode=ViewSetting.kFullScreenView, extId = 204, supportBack=ViewSetting.BackBig
}

setting_view.JumpBoxResultView = {
	presentor = "JumpBoxResultViewPresentor", showMode=ViewSetting.kModalView
}

setting_view.WerewolfActResultView = {
	presentor = "WerewolfActResultViewPresentor", showMode=ViewSetting.kModalView
}

setting_view.CatchStar_UI_View = {
	presentor = "CatchStar_UI_Presentor", showMode=ViewSetting.kWindow, supportBack=ViewSetting.BackBig
}
setting_view.CatchStar_UI_SettlementView = {
	presentor = "CatchStar_UI_SettlementPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.CatchStarRankingView = {
	presentor = "CatchStarRankingPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.CombineGameSelectMatchPanel = {
	presentor = "CombineGameSelectMatchPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.CombineGameCreateRoomPanel = {
	presentor = "CombineGameCreateRoomPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.CombineGameMainPanel = {
	presentor = "CombineGameMainPanelPresentor", showMode=ViewSetting.kWindow,extId = 65
}
setting_view.CombineGameChatPanel = {
	presentor = "CombineGameChatPanelPresentor", showMode = ViewSetting.kNormalView,extId = 65
}
setting_view.CombineGameChatHudPanel = {
	presentor = "CombineGameChatHudPanelPresentor", showMode = ViewSetting.kNormalView,extId = 65
}
setting_view.CombineGameMapPanel = {
	presentor = "CombineGameMapPanelPresentor", showMode=ViewSetting.kWindow,extId = 65
}

setting_view.MiaoMiaoWuTaskMianView = {
	presentor = "MiaoMiaoWuTaskViewPresentor", showMode=ViewSetting.kFullScreenView, extId= 176, hideHUD=true
}

setting_view.CombineGameTeamVictoryPanel = {
	presentor = "CombineGameTeamVictoryPanelPresentor", showMode=ViewSetting.kFullScreenView,extId = 65,noPadMask=true
}
setting_view.CombineGameVictoryPanel = {
	presentor = "CombineGameVictoryPanelPresentor", showMode=ViewSetting.kFullScreenView,extId = 65,noPadMask=true
}
setting_view.CombineGameResultPanel = {
	presentor = "CombineGameResultPanelPresentor", showMode=ViewSetting.kModalView,extId = 65
}

setting_view.IceSnowShop = {
	presentor = "IceSnowShopPresentor", showMode=ViewSetting.kFullScreenView,
	extId= -1, hideHUD=true, supportBack = ViewSetting.BackBig, showRead=true
}
setting_view.IceSnowMap = {
	presentor = "IceSnowMapPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, supportBack=101
}

setting_view.FrameSyncSettingView = {
	presentor = "FrameSyncSettingViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.IceSnowTaskStoryView = {
	presentor = "IceSnowTaskStoryPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true, extId = -1
}

setting_view.CatchStarActivityView = {
	presentor = "CatchStarActivityPresentor", showMode=ViewSetting.kFullScreenView,
	extId= -1, hideHUD=true, supportBack = ViewSetting.BackBig, showRead=true
}

setting_view.SnowmanView = {
	presentor = "SnowmanViewPresentor", showMode=ViewSetting.kFullScreenView,extId = 207,noPadMask=true
}

setting_view.SnowmanActivityView = {
	presentor = "SnowmanActivityViewPresentor", showMode=ViewSetting.kFullScreenView ,extId = 207 ,noPadMask=true
}

setting_view.SnowmanTransitionView = {
	presentor = "SnowmanTransitionViewPresentor", showMode = ViewSetting.kNormalView ,extId = 207
}

setting_view.NewYearTimeDownView = {
	presentor = "NewYearTimeDownPresentor",showMode = ViewSetting.kNormalView,extId = 211
}
setting_view.NewYearMsgView = {
	presentor = "NewYearMsgViewPresentor",showMode = ViewSetting.kModalWindow,extId = 211,hideHUD = true, supportBack =102
}

setting_view.NewYearGoToView = {
	presentor = "NewYearGoToViewPresenter",showMode = ViewSetting.kModalWindow,hideHUD = true
}

setting_view.SnowmanSettlementVIew = {
	presentor = "SnowmanSettlementVIewPresentor", showMode = ViewSetting.kModalWindow ,extId = 207
}

setting_view.SnowmanCollectSpecialPanel = {
	presentor = "SnowmanCollectSpeciallViewPresentor", showMode = ViewSetting.kNormalView ,extId = 207
}

setting_view.SnowmanCollectionView = {
	presentor = "SnowmanCollectionViewPresentor", showMode = ViewSetting.kModalView ,extId = 207
}

setting_view.BlessingFestivalLotteryView = {
	presentor = "BlessingFestivalLotteryPresentor",showMode = ViewSetting.kFullScreenView,extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.PrayDaySigninView = {
	presentor = "PrayDaySigninViewPresentor", showMode = ViewSetting.kModalView ,extId = 138
}

setting_view.BlessingFestivalMainView = {
	presentor = "BlessingFestivalMainViewPresentor", showMode=ViewSetting.kFullScreenView,syncCloseList = {"ActivityRuleCommon"},supportBack=101
}
setting_view.BlessingFestivalShop = {
	presentor = "BlessingFestivalShopPresentor", showMode=ViewSetting.kNormalView,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}

setting_view.BlessingFestivalWorkShopView = {
	presentor = "BlessingFestivalWorkShopViewPresentor", showMode=ViewSetting.kNormalView
}

setting_view.BlessingFestivalTaskView = {
	presentor = "BlessingFestivalTaskViewPresentor", showMode=ViewSetting.kNormalView,
}
setting_view.CommonRankingView = {
	presentor = "CommonRankingPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.CommonRankingAwardPreView = {
	presentor = "CommonRankingAwardPreViewPresentor", showMode = ViewSetting.kModalWindow, extId = -1, supportBack = ViewSetting.BackSmall
}

setting_view.NewYearHongBaoRainView = {
	presentor = "NewYearHongBaoRainViewPresentor", showMode=ViewSetting.kModalWindow,extId = 213 ,showRead=true
}

setting_view.NewYearHongBaoGameView = {
	presentor = "NewYearHongBaoGameViewPresentor", showMode=ViewSetting.kModalView,extId = 213 ,showRead=true
}

setting_view.SF_AssemblyLineView = {
	presentor = "SF_AssemblyLinePresentor", showMode=ViewSetting.kNormalView,extId=-1
}

setting_view.AssemblyExtAnimView = {
	presentor = "AssemblyExtAnimPresentor", showMode=ViewSetting.kWindow,extId = -1
}

setting_view.AssemblyLineActivityView = {
	presentor = "AssemblyLineActivityPresentor", showMode=ViewSetting.kFullScreenView,
	extId= -1, hideHUD=true, supportBack = ViewSetting.BackBig, showRead=true
}

-- setting_view.FengHuaTurntable = {
-- 	presentor = "FengHuaTurntablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
-- 	syncCloseList={"TurnTableTaskView","TiredGiftView"}
-- }

-- setting_view.MusicTurnTable = {
-- 	presentor = "MusicTurnTablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
-- 	syncCloseList={"TurnTableTaskView","TiredGiftView"}
-- }

setting_view.TraditionTurnTable = {
	presentor = "TraditionTurntablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
	syncCloseList={"TurnTableTaskView","TiredGiftView"}
}

-- setting_view.DragonTurnTable = {
-- 	presentor = "DragonTurnTablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
-- 	syncCloseList={"TurnTableTaskView","TiredGiftView","DragonTurnTableRedBag"}
-- }

setting_view.DragonTurnTableRedBag = {
	presentor = "DragonTurnTableRedBagViewPresentor", showMode=ViewSetting.kModalView,extId=161
}

setting_view.DragonRedBagSharePanel = {
	presentor = "TurntableRedBagShareViewPresentor", showMode=ViewSetting.kModalView, hideHUD=true
}

-- setting_view.MirrorTurnTable = {
-- 	presentor = "MirrorTurnTablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
-- 	syncCloseList={"TurnTableTaskView","TiredGiftView"}
-- }

-- setting_view.SnakeTurnTable = {
-- 	presentor = "SnakeTurnTablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
-- 	syncCloseList={"TurnTableTaskView","TiredGiftView"}
-- }

-- setting_view.PerfumeTurnTable = {
-- 	presentor = "PerfumeTurnTablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
-- 	syncCloseList={"TurnTableTaskView","TiredGiftView"}
-- }

setting_view.PlanetTurnTable = {
	presentor = "PlanetTurnTablePresentor", showMode=ViewSetting.kFullScreenView,extId=161,supportBack = ViewSetting.BackBig,
	syncCloseList={"TurnTableTaskView","TiredGiftView"}
}



setting_view.CommonActTaskStoryView = {
	presentor = "CommonActTaskStoryPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true, extId = -1
}

setting_view.ValentinesDayTaskStoryView = {
	presentor = "ValentinesDayTaskStoryPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true, extId = -1
}

--风华节商店
setting_view.ValentineDayShop = {
	presentor = "ValentineDayShopPresentor", showMode=ViewSetting.kFullScreenView,
	extId= -1, hideHUD=true, supportBack = ViewSetting.BackBig, showRead=true
}
--风华纪事
setting_view.SpringFestivalTaskStoryView = {
	presentor = "SpringFestivalTaskStoryPresentor", showMode=ViewSetting.kFullScreenView,supportBack=101, showRead=true, extId = -1
}

--游仙传集卡送卡界面
setting_view.CCTVCardSendView = {
	presentor="CCTVCardSendViewPresentor",showMode=ViewSetting.kModalView,extId=48,hideHUD = true
}

--游仙传集卡界面
setting_view.CCTVCardMainView = {
	presentor = "CCTVCardMainViewPresentor", showMode=ViewSetting.kModalWindow,supportBack=101, extId = -1,
}

--集卡红包界面
setting_view.CCTVCardRewardView =  {
	presentor="CCTVCardRewardViewPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

--集卡卡片详情界面
setting_view.CCTVCardInfoView =  {
	presentor="CCTVCardInfoViewPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

--游仙集卡途径界面
setting_view.CCTVCardGetPathView =  {
	presentor="CCTVCardGetPathViewPresentor",showMode=ViewSetting.kModalWindow,extId=4
}

--游仙集卡获取弹窗
setting_view.CCTVCardPopView =  {
	presentor="CCTVCardPopViewPresentor",showMode=ViewSetting.kModalWindow,extId=4
}
--游仙集卡分享
setting_view.CCTVCardSharePanel = {
	presentor = "CCTVCardSharePanelPresentor", showMode=ViewSetting.kModalView,hideHUD=true
}

setting_view.CCTVActFatigueView = {
	presentor = "CCTVActFatiguePresentor", showMode=ViewSetting.kModalWindow,supportBack = ViewSetting.BackBig,hideHUD = true
}

setting_view.CCTVActStoryView = {
	presentor = "CCTVActStoryViewPresentor", showMode=ViewSetting.kModalView,hideHUD=true,supportBack = ViewSetting.BackBig,hideHUD = true
}

setting_view.CCTVMapPanel={
	presentor = "CCTVMapPanelPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackBig,hideHUD = true
}
setting_view.CCTVShopPanel={
	presentor = "CCTVShopPanelPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackBig,hideHUD = true
}

setting_view.ReunionCircleMainView = {
	presentor = "ReunionCirclePresentor", showMode=ViewSetting.kFullScreenView, extId= 176, hideHUD=true
}

setting_view.HappyRestaurantView = {
	presentor = "HappyRestaurantViewPresentor", showMode=ViewSetting.kFullScreenView ,supportBack = ViewSetting.BackBig, extId = 214, hideHUD=true
}

setting_view.HappyRestaurantGameView = {
	presentor = "HappyRestaurantGameViewPresentor", showMode=ViewSetting.kNormalView, extId = 214, hideHUD=true
}

setting_view.HappyRestaurantResultView = {
	presentor = "HappyRestaurantResultViewPresentor", showMode=ViewSetting.kModalView, extId = 214, hideHUD=true
}

setting_view.HappyRestaurantBuffView = {
	presentor = "HappyRestaurantBuffViewPresentor", showMode=ViewSetting.kModalView, extId = 214, hideHUD=true
}

setting_view.OptionalGiftBagView = {
	presentor = "OptionalGiftBagViewPresentor", showMode=ViewSetting.kNormalView, extId = -1,
}

setting_view.ActivityRabbitGiftView =  {
	presentor="ActivityRabbitGiftPresentor",showMode=ViewSetting.kNormalView,extId=100,
}

setting_view.ActivityRabbitSpeakersView =  {
	presentor="ActivityRabbitSpeakersPresentor",showMode=ViewSetting.kNormalView,extId=100,
}

setting_view.LanternShop = {
	presentor = "LanternShopPresentor", showMode=ViewSetting.kModalView,extId=48,hideHUD = true,syncCloseList = {"ItemUse","ActivityRuleCommon","RechargeGiftBag"}
}
setting_view.LanternShop2 = {
	presentor = "LanternShopPresentor2", showMode=ViewSetting.kModalView,extId=48,hideHUD = true,syncCloseList = {"ItemUse","ActivityRuleCommon","RechargeGiftBag"}
}

setting_view.LanternShopShare = {
	presentor="LanternShopSharePresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.LanternRiddleView = {
	presentor = "LanternRiddleViewPresentor", showMode=ViewSetting.kModalView
}

setting_view.LanternRiddleActivityView = {
	presentor = "LanternRiddleActivityViewPresentor", showMode=ViewSetting.kNormalView
}

setting_view.LanternRiddleResultView = {
	presentor = "LanternRiddleResultViewPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.ActivityMap1 = {
	presentor = "ValentineDayMapPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, supportBack=101
}

setting_view.CgView = {
	presentor = "CgPresentor", showMode=ViewSetting.kFullScreenView,extId= -1
}

setting_view.Snake2EndGameTips = {
	presentor = "Snake2EndGameTipsViewPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.Sole_SnakeShowPlayerView = {
	presentor = "Sole_SnakeShowPlayerViewPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.Mult_SnakeShowTeamView = {
	presentor = "Mult_SnakeShowTeamViewPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.Sole_SnakeResultPanel = {
	presentor = "Sole_SnakeResultPanelPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.Mult_SnakeResultPanel = {
	presentor = "Mult_SnakeResultPanelPresentor", showMode=ViewSetting.kFullScreenView
}

setting_view.SettingtAffinityView = {
	presentor = "SettingtAffinityPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.FishSongMap = {
	presentor = "FishSongMapPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, supportBack=101
}
setting_view.FishSongShop = {
	presentor = "FishSongShopPresentor", showMode=ViewSetting.kFullScreenView,
	extId= -1, hideHUD=true, supportBack = ViewSetting.BackBig, showRead=true
}

--孵蛋
setting_view.IncubateView = {
	presentor = "IncubatePresentor", showMode=ViewSetting.kFullScreenView,supportBack = ViewSetting.BackBig
}
setting_view.IncubateProgressAwardView = {
	presentor = "IncubateProgressAwardPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.IncubateCultivateView = {
	presentor = "IncubateCultivatePresentor", showMode=ViewSetting.kFullScreenView,supportBack = ViewSetting.BackSmall
}
setting_view.IncubateGainPetView = {
	presentor = "IncubateGainPetPresentor", showMode=ViewSetting.kFullScreenWindow
}

setting_view.BuildStageView = {
	presentor = "BuildStageViewPresentor", showMode=ViewSetting.kFullScreenView, extId = 7, hideHUD = true
}

setting_view.PetWelfareMapPanel = {
	presentor = "PetWelfareMapPanelPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackBig,hideHUD = true
}
setting_view.PetWelfareTaskPanel = {
	presentor = "PetWelfareTaskPanelPresentor", showMode=ViewSetting.kModalView,hideHUD=true,supportBack = ViewSetting.BackBig
}
setting_view.PetWelfareRechargePanel = {
	presentor = "PetWelfareRechargePanelPresentor", showMode=ViewSetting.kModalView,hideHUD=true,supportBack = ViewSetting.BackBig
}
setting_view.PetWelfareProcessPanel = {
	presentor = "PetWelfareProcessPanelPresentor", showMode=ViewSetting.kModalView,hideHUD=true,supportBack = ViewSetting.BackBig
}
setting_view.PetWelfareSharePanel = {
	presentor = "PetWelfareSharePanelPresentor", showMode=ViewSetting.kModalView,hideHUD=true
}

setting_view.DreamBoxMapPanel = {
	presentor = "DreamBoxMapPanelPresentor", showMode=ViewSetting.kModalView, showRead = true, supportBack= ViewSetting.BackBig, --noPadMask = true,
}

setting_view.DreamBoxSubMapPanel = {
	presentor = "DreamBoxSubMapPanelPresentor", showMode=ViewSetting.kModalWindow,hideHUD=true,syncCloseList = {"ActivityRuleCommon"}, supportBack= ViewSetting.BackBig
}

setting_view.DreamBoxMapPanel_1 = {
	presentor = "DreamBoxMapPanelPresentor_1", showMode=ViewSetting.kModalWindow, supportBack=101, showRead = true, extId = -1, hideHUD = true
}

setting_view.DreamBoxFatigue = {
	presentor = "DreamBoxFatiguePresentor", showMode=ViewSetting.kModalWindow,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}

setting_view.DreamBoxStoryView = {
	presentor = "DreamBoxStoryViewPresentor", showMode=ViewSetting.kModalView, hideHUD=true
}

setting_view.DreamBoxStoryView_1 = {
	presentor = "DreamBoxStoryViewPresentor_1", showMode=ViewSetting.kModalView,hideHUD=true
}

setting_view.DreamBoxWorkShopView = {
	presentor = "DreamBoxWorkShopViewPresentor", showMode=ViewSetting.kModalWindow, supportBack=101
}

setting_view.WorkShoppingView = {
	presentor = "WorkShoppingViewPresentor", showMode=ViewSetting.kModalWindow, supportBack=101
}

setting_view.MakeDreamShop = {
	presentor = "MakeDreamShopPresentor", showMode=ViewSetting.kNormalView,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}

setting_view.MakeDreamTable = {
	presentor = "MakeDreamTableViewPresentor", showMode=ViewSetting.kFullScreenView,extId=234,supportBack = 101
}

setting_view.MakeDreamTableRewards = {
	presentor="MakeDreamTableRewardsPresentor",showMode=2,extId=-1,openSound=141098
}

setting_view.TangramGame = {
	presentor = "TangramGamePresentor", showMode=ViewSetting.kFullScreenView,extId=233,hideHUD=true,supportBack = ViewSetting.BackBig
}

setting_view.FlopGameResultView = {
	presentor = "FlopGameResultViewPresentor", showMode = ViewSetting.kModalWindow,extId = 173
}

setting_view.PuzzleGameMainView = {
	presentor="PuzzleGameMainViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, supportBack = ViewSetting.BackBig
}

setting_view.PuzzleGameResultView = {
	presentor="PuzzleGameResultViewPresentor",showMode= 2, extId = -1
}

setting_view.PuzzleGameView = {
	presentor="PuzzleGameViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1
}

setting_view.MusicGameMainView = {
	presentor = "MusicGameMainPresentor", showMode=ViewSetting.kWindow,extId= -1,hideHUD=true
}

setting_view.MusicGameActionEquipView = {
	presentor = "MusicGameActionEquipPresentor", showMode=ViewSetting.kModalWindow,extId= -1,hideHUD=true
}
setting_view.MusicGameSetView = {
	presentor = "MusicGameSetPresentor", showMode=ViewSetting.kModalWindow,extId= -1,hideHUD=true
}
setting_view.MusicGameadJustView = {
	presentor = "MusicGameadJustPresentor", showMode=ViewSetting.kModalWindow,extId= -1,hideHUD=true
}

setting_view.MusicGameCreateRoomPanel = {
	presentor = "MusicGameCreateRoomPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.MusicGameMatchModeSelectPanel = {
	presentor = "MusicGameMatchModeSelectPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.MusicGameSelectMusicPanel = {
	presentor = "MusicGameSelectMusicPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.MusicGameMusicRecordPanel = {
	presentor = "MusicGameSelectMusicPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.MusicGameSoloResultPanel = {
	presentor = "MusicGameSoloResultPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.MusicGameTeamResultPanel = {
	presentor = "MusicGameTeamResultPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.MusicGameRecordPanel = {
	presentor = "MusicGameRecordPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.MusicGameVictoryPanel = {
	presentor = "MusicGameVictoryPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.BallDropGameView = {
	presentor = "BallDropGameViewPresentor", showMode=ViewSetting.kFullScreenView,noPadMask=true,supportBack=ViewSetting.BackBig
}

setting_view.BallDropEnterView = {
	presentor = "BallDropEnterViewPresentor", showMode=ViewSetting.kFullScreenView,supportBack=ViewSetting.BackBig
}

setting_view.MusicCoffeeMap = {
	presentor = "MusicCoffeeMapPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, supportBack=101
}

setting_view.MusicCoffeeShop = {
	presentor = "MusicCoffeeShopPresentor", showMode=ViewSetting.kFullScreenView,supportBack=ViewSetting.BackBig,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}
setting_view.AobiReturn2View = {
	presentor = "AobiReturn2ViewPresentor", showMode=ViewSetting.kNormalView,extId=235
}

setting_view.AobiReturn2ShareView = {
	presentor = "AobiReturn2ShareViewPresentor", showMode=ViewSetting.kFullScreenView,extId=235
}

setting_view.BallDropGameResultView = {
	presentor = "BallDropGameResultViewPresentor", showMode=ViewSetting.kModalView
}
setting_view.MusicPartyTakePhotoView = {
	presentor="MusicPartyTakePhotoPresentor",showMode=ViewSetting.kWindow,extId=23,hideHUD=true
}
setting_view.MusicPartyTakePhotoShowView = {
	presentor="MusicPartyTakePhotoShowPresentor",showMode=ViewSetting.kModalView,extId=23
}


setting_view.MusicPartyActivityView = {
	presentor="MusicPartyActivityPresentor",showMode=ViewSetting.kModalWindow,extId=-1,hideHUD=true
}
setting_view.MusicPartyDanceView = {
	presentor="MusicPartyDancePresentor",showMode=ViewSetting.kNormalView,extId=-1
}
setting_view.MusicPartyTipsView = {
	presentor="MusicPartyTipsPresentor",showMode=ViewSetting.kWindow,extId=-1
}
setting_view.MusicPartyStoryView = {
	presentor="MusicPartyStoryPresentor",showMode=ViewSetting.kWindow,extId=-1
}

setting_view.MusicPartyCalendarPreView = {
	presentor="MusicPartyCalendarPreViewPresentor",showMode=ViewSetting.kNormalView,extId=-1
}

setting_view.ReturnStoryPanel = {
	presentor="ReturnStoryPanelPresentor",showMode=ViewSetting.kModalWindow,hideHUD=true,noPadMask=true
}

setting_view.AobiReturnPersonal = {
	presentor="AobiReturnPersonalPresentor",showMode=ViewSetting.kNormalView
}
setting_view.AobiReturnTask2Panel = {
	presentor="AobiReturnTask2Presentor",showMode=ViewSetting.kNormalView
}
setting_view.AobiReturnTeamMgrView = {
	presentor="AobiReturnTeamMgrPresentor",showMode=ViewSetting.kModalView,supportBack=ViewSetting.BackNoRecord
}
setting_view.AobiReturnTaskRewards = {
	presentor="AobiReturnTaskRewardsPresentor",showMode=ViewSetting.kModalView
}

setting_view.AobiReturnWelcomeView = {
	presentor="AobiReturnWelcomeViewPresentor",showMode=ViewSetting.kNormalView
}
setting_view.Return_TurnTableView = {
	presentor="Return_TurnTableViewPresentor",showMode=ViewSetting.kNormalView
}
setting_view.UserInputPanel = {
	presentor="UserInputPanelPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.SpaceMoveView = {
	presentor = "SpaceMoveViewPresentor", showMode=ViewSetting.kModalWindow, extId= 7, hideHUD=true
}
setting_view.MusicADTask = {
	presentor = "MusicADPresentor", showMode=ViewSetting.kFullScreenView, extId= 176, hideHUD=true,supportBack=ViewSetting.BackBig
}

setting_view.MusicNameShowView = {
	presentor = "MusicNameShowPresentor", showMode=ViewSetting.kModalWindow,hideHUD=true
}

setting_view.CommonActivityShop = {
	presentor = "ActivityGroceryPresentor2_1", showMode=ViewSetting.kFullScreenView,supportBack=ViewSetting.BackBig,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}

setting_view.CommonSmallShop = {
	presentor = "CommonSmallShopPresentor", showMode=ViewSetting.kModalWindow,supportBack=ViewSetting.BackBig,extId=48,syncCloseList = {"ItemUse","ActivityRuleCommon"}
}

setting_view.LumberJackGamePanel = {
	presentor = "LumberJackGamePanelPresentor", showMode=ViewSetting.kNormalView, hideHUD=true, noPadMask=true
}
setting_view.LumberJackEntryPanel = {
	presentor = "LumberJackEntryPanelPresentor", showMode=ViewSetting.kFullScreenView, hideHUD=true, supportBack=ViewSetting.BackBig,noPadMask=true
}
setting_view.LumberJackRecordPanel = {
	presentor = "LumberJackRecordPanelPresentor", showMode=ViewSetting.kModalWindow, supportBack=ViewSetting.BackSmall
}
setting_view.LumberJackResultPanel = {
	presentor = "LumberJackResultPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.LumberJackNewbieGuidePanel = {
	presentor = "LumberJackNewbieGuidePresentor", showMode=ViewSetting.kNormalView
}

setting_view.ToyHouseMap = {
	presentor = "ToyHouseMapPresentor", showMode=ViewSetting.kModalWindow,extId= -1, hideHUD=true, supportBack=101, openSound = 141286
}

setting_view.GardenPartyStoryView = {
	presentor = "GardenPartyStoryViewPresentor", showMode=ViewSetting.kModalView,hideHUD=true,supportBack = ViewSetting.BackBig,hideHUD = true
}

setting_view.GardenPartyCardView = {
	presentor = "GardenPartyCardViewPresentor", showMode=ViewSetting.kModalWindow,extId=157,hideHUD = true
}

setting_view.GardenPartyGiftView = {
	presentor = "GardenPartyGiftViewPresentor", showMode=ViewSetting.kFullScreenWindow,extId=157
}

setting_view.MLInteractThreeDecode = {
	presentor = "MLInteractThreeDecodePresentor", showMode=ViewSetting.kFullScreenView, hideHUD=true
}

setting_view.MLInteractThree = {
	presentor = "MLInteractThreePresentor", showMode=ViewSetting.kFullScreenView, hideHUD=true
}

setting_view.MondayGift = {
	presentor = "MondayGiftPresentor", showMode=ViewSetting.kModalWindow, extId= -1
}
setting_view.PleasanceCollectStampView = {
	presentor = "PleasanceCollectStampPresentor", showMode=ViewSetting.kModalView,hideHUD=true,supportBack = ViewSetting.BackBig
}

setting_view.TempleBeneficenceView = {
	presentor = "TempleBeneficenceViewPresentor", showMode=ViewSetting.kModalView,hideHUD=true,supportBack = ViewSetting.BackBig
}

setting_view.TempleAwardView = {
	presentor="TempleAwardViewPresentor",showMode=ViewSetting.kModalWindow,extId=12,autoDestroyTime=10,supportBack=ViewSetting.BackSmall,autoDestroyTime=0.1
}

setting_view.PleasanceCollectStampTaskView = {
	presentor = "PleasanceCollectStampTaskPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.PleasanceStarWishCardView = {
	presentor = "PleasanceStarWishCardPresentor",showMode = ViewSetting.kFullScreenView,extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.MakeChipView = {
	presentor = "MakeChipViewPresentor", showMode=ViewSetting.kModalWindow, extId= 7, hideHUD=true
}

setting_view.WorkshopOneKeyMakeView = {
	presentor = "WorkshopOneKeyMakePresentor", showMode = ViewSetting.kFullScreenView,supportBack = ViewSetting.BackSmall
}

setting_view.CatchingInsectView = {
	presentor = "CatchingInsectViewPresentor", showMode = ViewSetting.kWindow
}

setting_view.CatchingInsectActivityView = {
	presentor = "CatchingInsectActivityViewPresentor", showMode = ViewSetting.kFullScreenView ,showRead=true
}

setting_view.CatchingInsectInfoView = {
	presentor = "CatchingInsectInfoViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.CatchingInsectResultView = {
	presentor = "CatchingInsectResultViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.CatchingInsectShareView = {
	presentor = "CatchingInsectShareViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.MatchWaitingView = {
	presentor = "MatchWaitingViewPresentor", showMode=ViewSetting.kModalWindow, extId= -1, hideHUD=true
}

setting_view.GobangMatchSucc = {
	presentor = "GobangMatchSuccPresentor", showMode=ViewSetting.kModalWindow, extId= 60, hideHUD=true
}

setting_view.GobangBanSelect = {
	presentor = "GobangBanSelectPresentor", showMode=ViewSetting.kModalWindow, extId= 60
}

setting_view.GobangFurSetting = {
	presentor = "GobangFurSettingPresentor", showMode=ViewSetting.kModalWindow, extId= 60
}

setting_view.PredownloadRW = {
	presentor = "PredownloadRWPresentor", showMode=ViewSetting.kModalWindow, extId= 247
}

setting_view.ActivityPlayerGuideView = {
	presentor = "ActivityPlayerGuideViewPresentor", showMode=ViewSetting.kWindow, extId= 247
}

setting_view.RunningGameMainView = {
	presentor="RunningGameMainViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, supportBack = ViewSetting.BackBig
}

setting_view.RunningGameView = {
	presentor="RunningGameViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, autoDestroyTime=0.01
}

setting_view.TipItemView = {
	presentor="TipItemPresentor",showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.FindCloverView = {
	presentor="FindCloverViewPresentor",showMode=ViewSetting.kFullScreenView,extId=7,hideHUD=true
}

setting_view.CrossingThornsView = {
	presentor="CrossingThornsPresentor",showMode=ViewSetting.kFullScreenView,extId=7,hideHUD=true
}

setting_view.FindMaterialsView = {
	presentor="FindMaterialsPresentor",showMode=ViewSetting.kFullScreenView,hideHUD=true
}

setting_view.GalaFlyView = {
	presentor = "GalaFlyPresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.CarnivalPartyTipView = {
	presentor = "CarnivalPartyTipPresentor", showMode = ViewSetting.kWindow,extId = -1
}

setting_view.CarnivalPartyEnterView = {
	presentor = "CarnivalPartyEnterPresentor", showMode = ViewSetting.kWindow,extId = -1
}


setting_view.MoveOrDieCreateRoomPanel = {
	presentor = "MoveOrDieCreateRoomPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.MoveOrDieGamePanel = {
	presentor = "MoveOrDieGamePanelPresentor", showMode = ViewSetting.kNormalView
}
setting_view.MoveOrDieRuleSelectPanel = {
	presentor = "MoveOrDieRuleSelectPanelPresentor", showMode = ViewSetting.kFullScreenView
}
setting_view.MoveOrDieConfirmPanel = {
	presentor = "MoveOrDieConfirmPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.MoveOrDieVictoryPanel = {
	presentor = "MoveOrDieVictoryPanelPresentor", showMode = ViewSetting.kNormalView
}
setting_view.MoveOrDieResultPanel = {
	presentor = "MoveOrDieResultPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.MoveOrDieEntryPanel = {
	presentor = "MoveOrDieEntryPanelPresentor", showMode = ViewSetting.kFullScreenView,supportBack = ViewSetting.BackBig
}
setting_view.MoveOrDieRewardPreviewPanel = {
	presentor = "MoveOrDieRewardPreviewPanelPresentor", showMode = ViewSetting.kModalWindow--,supportBack = ViewSetting.BackBig
}
setting_view.MoveOrDieNewbieGuidePanel = {
	presentor = "MoveOrDieNewbieGuidePresentor", showMode=ViewSetting.kNormalView
}
setting_view.MoveOrDieNewbieEndPanel = {
	presentor = "MoveOrDieNewbieEndPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.StoryTellerBoardPanel = {
	presentor = "StoryTellerBoardPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerChapterSelectPanel = {
	presentor = "StoryTellerChapterSelectPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerSelectPlotPanel = {
	presentor = "StoryTellerSelectPlotPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerRoleSelectPanel = {
	presentor = "StoryTellerRoleSelectPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerEffTestPanel = {
	presentor = "StoryTellerEffTestPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerMenuPanel = {
	presentor = "StoryTellerMenuPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerDressPreviewPanel = {
	presentor = "StoryTellerDressPreviewPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerRolePreviewPanel = {
	presentor = "StoryTellerRolePreviewPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerPosterEditPanel = {
	presentor = "StoryTellerPosterEditPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerBookPanel = {
	presentor = "StoryTellerBookPanelPresentor", showMode = ViewSetting.kModalWindow, supportBack = ViewSetting.BackSmall
}

setting_view.StoryTellerEditSuccPanel = {
	presentor = "StoryTellerEditSuccPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerWatchStoryPanel = {
	presentor = "StoryTellerWatchStoryPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerPopularPanel = {
	presentor = "StoryTellerPopularPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.StoryTellerStoryInfoPanel = {
	presentor = "StoryTellerStoryInfoPanelPresentor", showMode = ViewSetting.kModalWindow, supportBack = ViewSetting.BackSmall
}

setting_view.StoryTellerRoleInfoPanel = {
	presentor = "StoryTellerRoleInfoPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.StoryTellerRoleListPanel = {
	presentor = "StoryTellerRoleListPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.StoryTellerFilterPanel = {
	presentor = "StoryTellerFilterPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.StoryTellerWatchStoryCommentListPanel = {
	presentor="StoryTellerWatchStoryCommentListPanelPresentor",showMode=ViewSetting.kWindow
}

setting_view.StoryTellerRewardPreviewPanel = {
	presentor = "StoryTellerRewardPreviewPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.SeaMileStoneMainView = {
	presentor = "SeaMileStoneMainViewPresentor", showMode = ViewSetting.kFullScreenView,supportBack = ViewSetting.BackBig
}

setting_view.BuildAobiSquarePromoPanel = {
	presentor = "BuildAobiSquarePromoPanelPresentor", showMode = ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.CommonDetailTipsPanel = {
	presentor = "CommonDetailTipsPanelPresentor", showMode = ViewSetting.kWindow
}

setting_view.PumpTurtleEntryPanel = {
	presentor = "PumpTurtleEntryPanelPresentor", showMode = ViewSetting.kFullScreenView,supportBack = ViewSetting.BackBig
}

setting_view.PumpTurtleMainView = {
	presentor = "PumpTurtleMainViewPresentor", showMode=ViewSetting.kNormalView,extId = -1, autoDestroyTime=0.1,  hideHUD = true
}

setting_view.PumpTurtleAwardView = {
	presentor = "PumpTurtleAwardViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.PumpTurtleCreateRoomPanel = {
	presentor = "PumpTurtleCreateRoomPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.PumpTurtleResultPanel = {
	presentor = "PumpTurtleResultPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.PumpTurtleRecordView = {
	presentor = "PumpTurtleRecordViewPresentor", showMode=ViewSetting.kModalWindow,extId = -1, autoDestroyTime=0.1
}

setting_view.PumpTurtleNegativeRulePanel = {
	presentor = "PumpTurtleNegativeRulePanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.ThrowCakeGameView = {
	presentor="ThrowCakeGameViewPresentor",showMode=ViewSetting.kFullScreenView,extId=252,hideHUD=true
}

setting_view.ThrowCakeGamePrepareView = {
	presentor="ThrowCakeGamePreparePresentor",showMode=ViewSetting.kFullScreenView,extId=252,hideHUD=true
}

setting_view.ThrowCakeGameResultView = {
	presentor="ThrowCakeGameResultViewPresentor", showMode=ViewSetting.kModalView
}
setting_view.CarnivalPartyChooseView = {
	presentor="CarnivalPartyChoosePresentor",showMode=ViewSetting.kModalWindow
}

setting_view.CarnivalPartyEmojiView = {
	presentor="CarnivalPartyEmojiPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.AnniversaryGalaColorGridView = {
	presentor="AnniversaryGalaColorGridViewPresentor",showMode=ViewSetting.kWindow
}

setting_view.GalaPartyPerformView = {
	presentor="GalaPartyPerformViewPresentor",showMode=ViewSetting.kNormalView,hideHUD = true
}

setting_view.CarnivalPartyFlowerCarView = {
	presentor="CarnivalPartyFlowerCarPresentor",showMode=ViewSetting.kNormalView,hideHUD = true
}

setting_view.CarnivalFriendPanel = {
	presentor="CarnivalFriendPanelPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.BeautyMakerLevelView = {
	presentor = "BeautyMakerLevelPresentor", showMode = ViewSetting.kFullScreenView, extId = 258, hideHUD = true, supportBack=ViewSetting.BackBig
}

setting_view.BeautyMakerGameView = {
	presentor = "BeautyMakerGamePresentor", showMode = ViewSetting.kFullScreenView, extId = 258, hideHUD = true, supportBack=ViewSetting.BackBig
}

setting_view.BeautyMakerResultView = {
	presentor = "BeautyMakerResultPresentor", showMode = ViewSetting.kModalWindow, extId = 258
}

setting_view.AnniversaryMelodyView = {
	presentor = "AnniversaryMelodyPresentor", showMode=ViewSetting.kModalWindow, extId = -1
}

setting_view.AnniversaryCostumeView = {
	presentor = "AnniversaryCostumePresentor", showMode=ViewSetting.kModalWindow, extId = -1
}

setting_view.CarnivalPartyCakeView = {
	presentor = "CarnivalPartyCakePresentor", showMode = ViewSetting.kModalWindow, extId = 251
}

setting_view.CommonProgressAwardListView = {
	presentor = "CommonProgressAwardListPresentor", showMode = ViewSetting.kModalWindow, extId = -1
}

setting_view.ThrowCakeDoubleRankView = {
	presentor = "ThrowCakeDoubleRankViewPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.AnniversaryRankingView = {
	presentor = "AnniversaryRankingPresentor", showMode=ViewSetting.kModalWindow,supportBack = ViewSetting.BackSmall
}

setting_view.WorkshopUpgradeTipView = {
	presentor = "WorkshopUpgradeTipPresentor", showMode=ViewSetting.kModalWindow,supportBack = ViewSetting.BackSmall
}

setting_view.DrinksMakeingGameView = {
	presentor = "DrinksMakeingGamePresentor", showMode=ViewSetting.kFullScreenView,supportBack = ViewSetting.BackBig, extId = 260
}

setting_view.DrinksMakeingView = {
	presentor = "DrinksMakeingPresentor", showMode=ViewSetting.kFullScreenView,supportBack = ViewSetting.BackBig, extId = 260
}

setting_view.InGameUpdateTaskView = {
	presentor = "InGameUpdateTaskViewPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.InterstellarScienceGameView = {
	presentor = "InterstellarScienceGamePresentor", showMode=ViewSetting.kNormalView,extId = -1
}
setting_view.InterstellarScienceActView = {
	presentor = "InterstellarScienceActPresentor", showMode=ViewSetting.kFullScreenWindow,extId = -1,showRead=true, supportBack = ViewSetting.BackBig
}
setting_view.InterstellarScienceGameResultView = {
	presentor = "InterstellarScienceGameResultPresentor", showMode=ViewSetting.kModalWindow,extId = -1
}

setting_view.FlappyBirdMainView = {
	presentor="FlappyBirdMainViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, supportBack = ViewSetting.BackBig
}

setting_view.FlappyBirdGameView = {
	presentor="FlappyBirdGameViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, autoDestroyTime=0.1
}
setting_view.FlappyBirdGameGuideView = {
	presentor="FlappyBirdGameGuideViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, autoDestroyTime=0.1
}
setting_view.FlappyBirdRecordView = {
	presentor = "FlappyBirdRecordViewPresentor", showMode=ViewSetting.kModalWindow,extId = -1, autoDestroyTime=0.1
}
setting_view.FlappyBirdResultView = {
	presentor = "FlappyBirdResultViewPresentor", showMode=ViewSetting.kModalWindow,extId = -1, autoDestroyTime=0.1
}
setting_view.FlappyBirdResurgenceView = {
	presentor = "FlappyBirdResurgenceViewPresentor", showMode=ViewSetting.kModalWindow,extId = -1, autoDestroyTime=0.1
}

setting_view.SettingPassword = {
	presentor = "SettingPasswordPresentor", showMode=ViewSetting.kModalWindow,extId = -1
}

setting_view.JukeboxMainPanel = {
	presentor = "JukeboxMainPanelPresentor", showMode=ViewSetting.kFullScreenView,extId=-1
}setting_view.JukeboxSongDetailPanel = {
	presentor = "JukeboxSongDetailPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.JukeboxFlyTextPanel = {
		presentor = "JukeboxFlyTextPanelPresentor", showMode=ViewSetting.kNormalView,extId=-1
}

setting_view.IronHeartView = {
	presentor="IronHeartViewPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, supportBack = 101
}

setting_view.StarGameRankingView = {
	presentor = "StarGameRankingPresentor", showMode=ViewSetting.kModalWindow,extId = -1,supportBack = ViewSetting.BackSmall
}
setting_view.WerewolfStarGameEntryPanel = {
	presentor = "WerewolfStarGameEntryPanelPresentor", showMode=ViewSetting.kFullScreenView,extId = -1,supportBack = ViewSetting.BackBig
}
setting_view.WerewolfStarGameRewardPreviewPanel = {
	presentor = "WerewolfStarGameRewardPreviewPresentor", showMode=ViewSetting.kModalWindow,extId = -1
}

setting_view.CalendarLinkage = {
    presentor = "CalendarLinkageViewPresentor", showMode = ViewSetting.kNormalView, extId = -1
}

setting_view.CalendarTransportDressView = {
    presentor = "CalendarTransportDressViewPresentor", showMode = ViewSetting.kNormalView, extId = -1
}

setting_view.GuessTVView = {
	presentor="GuessTVViewPresentor",showMode=ViewSetting.kFullScreenView,extId=7,hideHUD=true
}

setting_view.TermbeginsPanel = {
	presentor="TermbeginsPanelPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, supportBack = ViewSetting.BackBig
}

setting_view.TermbeginsSigninView = {
	presentor = "TermbeginsSigninViewPresentor", showMode=ViewSetting.kNormalView,extId=196,supportBack = ViewSetting.BackSmall
}

setting_view.TermbeginsProgressView = {
	presentor = "TermbeginsProgressViewPresentor", showMode=ViewSetting.kNormalView,supportBack = ViewSetting.BackSmall
}

setting_view.TermbeginsTaskView = {
	presentor = "TermbeginsTaskViewPresentor", showMode = ViewSetting.kNormalView,supportBack = ViewSetting.BackSmall
}

setting_view.NewTermbeginsPanel = {
	presentor="NewTermbeginsPanelPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, supportBack = ViewSetting.BackBig
}

setting_view.NewTermbeginsResigninPanel = {
	presentor="NewTermbeginsResigninPanelPresentor",showMode=ViewSetting.kModalWindow,extId = -1
}

setting_view.NewTermbeginsAnswerPanel = {
	presentor="NewTermbeginsAnswerPanelPresentor",showMode=ViewSetting.kFullScreenView,extId = -1, supportBack = ViewSetting.BackBig
}

setting_view.NewTermbeginsAnswerResultPanel = {
	presentor="NewTermbeginsAnswerResultPanelPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.NewTermbeginsAnswerPreviewPanel = {
	presentor="NewTermbeginsAnswerPreviewPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.RhythmBattlesTestPanel = {
	presentor="RhythmBattlesTestPanelPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.RhythmBattlesTeachingPanel = {
	presentor="RhythmBattlesTeachingPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.RhythmBattlesGameCommonPanel = {
	presentor="RhythmBattlesGameCommonPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattles_Lvl1Panel = {
	presentor="RhythmBattles_Lvl1PanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattles_Lvl2Panel = {
	presentor="RhythmBattles_Lvl2PanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattles_MusicGamePanel = {
	presentor="RhythmBattles_MusicGamePanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattles_LvlTransitionPanel = {
	presentor="RhythmBattles_LvlTransitionPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattlesCharmingPanel = {
	presentor="RhythmBattlesCharmingPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattlesResultPanel = {
	presentor="RhythmBattlesResultPanelPresentor", showMode=ViewSetting.kFullScreenView
}

setting_view.RhythmBattlesEffTitlePanel = {
	presentor="RhythmBattlesEffTitlePanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.RhythmBattlesGameBonusPanel = {
	presentor="RhythmBattlesGameBonusPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattlesGmSettingPanel = {
	presentor="RhythmBattlesGmSettingPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattleRewardPreviewPanel = {
	presentor="RhythmBattleRewardPreviewPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.RhythmBattlesRankingPanel = {
	presentor="RhythmBattlesRankingPanelPresentor", showMode=ViewSetting.kFullScreenView
}

setting_view.RhythmBattlesMatchingPanel = {
	presentor="RhythmBattlesMatchingPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.RhythmBattlesNewbiePanel = {
	presentor="RhythmBattlesNewbiePanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.PetBattlesTransLvlPanel = {
	presentor="PetBattlesTransLvlPanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.PetBattles_GamePanel = {
	presentor="PetBattles_GamePanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.PetBattles_EditLvlPanel = {
	presentor="PetBattles_EditLvlPanelPresentor", showMode=ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.PetBattles_PetSelectPanel = {
	presentor="PetBattles_PetSelectPanelPresentor", showMode=ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.PetBattles_Pet5DPanel = {
	presentor="PetBattles_Pet5DPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.PetBattlesLoadingPanel = {
	presentor="PetBattlesLoadingPanelPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.PetBattlesResultPanel = {
	presentor="PetBattlesResultPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.PetBattlesRecordPanel = {
	presentor="PetBattlesRecordPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.PetBattlesRewardPreviewPanel = {
	presentor="PetBattlesRewardPreviewPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.PetBattleExercisePanel = {
	presentor = "PetBattleExercisePanelPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.PetBattleAttrPanel = {
	presentor = "PetBattleAttrPanelPresentor", showMode=ViewSetting.kModalWindow,
}
setting_view.PetBattlesMatchSuccPanel = {
	presentor="PetBattlesMatchSuccPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.DollHouseGameMainPanel = {
	presentor="DollHouseGameMainPanelPresentor", showMode=ViewSetting.kNormalView
}
setting_view.DollHouseRoleSelectPanel = {
	presentor="DollHouseRoleSelectPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.DollHouseTaskingPanel = {
	presentor="DollHouseTaskingPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.DollHouseStartPanel = {
	presentor="DollHouseStartPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.DollHouseVictoryPanel = {
	presentor="DollHouseVictoryPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.DollHouseResultPanel = {
	presentor="DollHouseResultPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.DollHouseLuckyStoryPanel = {
	presentor="DollHouseLuckyStoryPanelPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.DollHouseQuickMsgPanel = {
	presentor = "DollHouseQuickMsgPanelPresentor", showMode=ViewSetting.kNormalView
}
setting_view.DollHouseMvpPanel = {
	presentor = "DollHouseMvpPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.PianoJumpPanel = {
	presentor = "PianoJumpPanelPresentor", showMode=ViewSetting.kNormalView,extId=-1
}
setting_view.LumberJackMatchResultPanel = {
	presentor = "LumberJackMatchResultPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.GameRoomStarGameMatchSuccPanel = {
	presentor = "GameRoomStarGameMatchSuccPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.PoemFillingView = {
	presentor = "PoemFillingPresentor", showMode=ViewSetting.kFullScreenView,extId=272,hideHUD=true
}

setting_view.QixiCollectView = {
	presentor = "QixiCollectPresentor", showMode=ViewSetting.kFullScreenView,extId=192,hideHUD=true,supportBack = ViewSetting.BackBig
}

setting_view.QixiCollectFriendView = {
	presentor = "QixiCollectFriendPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.QixiCollectGiftView = {
	presentor = "QixiCollectGiftPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.QixiCollectShareView = {
	presentor = "QixiCollectSharePresentor", showMode=ViewSetting.kModalWindow
}

setting_view.MakeDreamTableRewardsView = {
	presentor = "MakeDreamTableRewardsPresentor",extId=-1,showMode=ViewSetting.kModalWindow
}

setting_view.MagpieFestivalInterviewView = {
	presentor = "MagpieFestivalInterviewViewPresentor",showMode=ViewSetting.kFullScreenView,extId=268
}
setting_view.DreamBoxSpeakerView = {
	presentor = "DreamBoxSpeakerViewPresentor",extId=-1,showMode=ViewSetting.kModalWindow,supportBack = 101,hideHUD = true
}
setting_view.MagpieFestivalNpcTipsView = {
	presentor = "MagpieFestivalNpcTipsViewPresentor",showMode=ViewSetting.kModalWindow,extId=268
}

setting_view.MagpieSumbitView = {
	presentor = "MagpieSumbitViewPresentor",showMode=ViewSetting.kModalView,extId=268,hideHUD = true,supportBack=ViewSetting.BackBig
}

setting_view.TetrisGameView = {
	presentor = "TetrisGamePresentor", showMode=ViewSetting.kFullScreenView,extId=-1,hideHUD=true,supportBack=ViewSetting.BackBig
}
setting_view.TetrisGameResultView = {
	presentor = "TetrisGameResultPresentor", showMode=ViewSetting.kModalWindow,extId=-1,hideHUD=true
}

setting_view.TetrisActView = {
	presentor = "TetrisActPresentor", showMode=ViewSetting.kFullScreenView,extId=-1,hideHUD=true,supportBack=ViewSetting.BackBig
}
setting_view.TetrisCreateRoomView = {
	presentor = "TetrisCreateRoomPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.TetrisSkillView = {
	presentor = "TetrisSkillViewPresentor", showMode = ViewSetting.kModalWindow,extId=278
}

setting_view.ElfDressPanel = {
	presentor = "ElfDressPanelPresentor",showMode=3,extId=-1, supportBack = 101,logId=123, showRead=true, openSound=141084
}

setting_view.CommonBtnView = {
	presentor = "CommonBtnPresentor",showMode=ViewSetting.kNormalView
}

setting_view.WeekPollView = {
	presentor = "WeekPollViewPresentor", showMode=ViewSetting.kWindow
}

setting_view.HarvestSeasonView = {
	presentor = "HarvestSeasonPresentor", showMode = ViewSetting.kFullScreenView, extId = -1,supportBack=ViewSetting.BackBig
}

setting_view.NewInviteFriendsView = {
	presentor="NewInviteFriendPresentor",showMode=2,hideHUD = true
}

setting_view.ActivityWeekLifePanel = {
	presentor="ActivityWeekLifePanelPresentor",showMode=ViewSetting.kFullScreenView,syncCloseList= {"ActivityWeekLifeUnlockPanel"}
}

setting_view.ActivityWeekLifeUnlockPanel = {
	presentor = "ActivityWeekLifeUnlockPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.LanternMoonRiddleView = {
	presentor = "LanternMoonRiddleViewPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.FullMoonCommemorationView = {
	presentor = "FullMoonCommemorationViewPresentor", showMode=ViewSetting.kFullScreenView, extId=-1, hideHUD=true, supportBack=ViewSetting.BackBig
}

setting_view.AnniversarySignView = {
	presentor = "AnniversarySignPresentor", showMode=ViewSetting.kFullScreenView, supportBack = ViewSetting.BackBig
}

setting_view.AnniversaryEveningPanel = {
	presentor = "AnniversaryEveningPanelPresentor", showMode=ViewSetting.kFullScreenView, extId=-1, hideHUD=true, supportBack=ViewSetting.BackBig
}
setting_view.AnniversarySharePanel = {
	presentor = "AnniversarySharePanelPresentor", showMode=ViewSetting.kFullScreenView, extId=-1, hideHUD=true, supportBack=ViewSetting.BackBig
}

setting_view.LanternMoonRiddleResultView = {
	presentor="LanternMoonRiddleResultViewPresentor",showMode=ViewSetting.kModalWindow,extId=1533,openSound=141098
}

setting_view.MoveOrDiePlayerView = {
	presentor = "MoveOrDiePlayerPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.DreamlandDuelView = {
	presentor = "DreamlandDuelViewPresentor", showMode=ViewSetting.kFullScreenView, extId=-1, hideHUD=true
}

setting_view.CatchingInsectTaskView = {
	presentor = "CatchingInsectTaskViewPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.CatchingInsectJoinView = {
	presentor = "CatchingInsectJoinViewPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.DWOLView = {
	presentor = "DrawWithOneLineViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 285, hideHUD = true
}

setting_view.GiveAwayFriendView = {
	presentor="GiveAwayFriendPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}
setting_view.ActivityElfSigninPanel = {
	presentor="ActivityElfSigninPanelPresentor",showMode=ViewSetting.kFullScreenView,extId=-1
}
setting_view.ActivityElfSigninBuyPanel = {
	presentor="ActivityElfSigninBuyPanelPresentor",showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.RockingboatMainView = {
	presentor = "RockingboatMainViewPresentor", showMode=ViewSetting.kWindow,syncCloseList= {"RockingboatTaskView"}, extId = 287
}

setting_view.RockingboatTaskView = {
	presentor = "RockingboatTaskViewPresentor", showMode=ViewSetting.kModalWindow, extId = 287
}

setting_view.UDrawIGuessTestView = {
	presentor = "UDrawIGuessTestViewPresentor", showMode=ViewSetting.kFullScreenView
}

setting_view.GMNPCFavorabilityPanel = {
	presentor="GMNPCFavorabilityPanelPresentor",showMode=2,extId=255,
}

setting_view.SelectFollowerView = {
	presentor="SelectFollowerViewPresentor",showMode=ViewSetting.kModalWindow,
}

setting_view.CCTVCardMainView_1 = {
	presentor="CCTVCardMainViewPresentor_1",showMode=ViewSetting.kModalWindow,supportBack=101, showRead=true, extId = -1,hideHUD=true
}

setting_view.RecruitBoardView = {
	presentor="RecruitBoardPresentor",showMode=ViewSetting.kModalWindow,extId=-1,supportBack=ViewSetting.BackBig
}

setting_view.RecentlyTeamListView = {
	presentor="RecentlyTeamListPresentor",showMode=ViewSetting.kModalWindow,extId=-1,supportBack=ViewSetting.BackSmall
}

setting_view.RecruitBoardMateView = {
	presentor="RecruitBoardMatePresentor",showMode=ViewSetting.kWindow,extId=-1
}

setting_view.CrossDressPhoto = {
	presentor="CrossDressPhotoPresentor",showMode=3,extId=288, supportBack = 101, openSound=141084, autoDestroyTime=0.1
}

setting_view.CrossDressAwardPreView = {
	presentor="CrossDressAwardPreViewPresentor",showMode=ViewSetting.kModalWindow,extId=288, supportBack = ViewSetting.BackSmall
}

setting_view.CrossDressEntrance = {
	presentor="CrossDressEntrancePresentor",showMode=ViewSetting.kFullScreenWindow,extId=288, supportBack = ViewSetting.BackBig
}

setting_view.DressMatchEntrance = {
	presentor="DressMatchEntrancePresentor",showMode=ViewSetting.kFullScreenWindow,extId=288
}

setting_view.CrossDressRecord = {
	presentor="CrossDressRecordPresentor",showMode=ViewSetting.kModalWindow,extId=288, supportBack = ViewSetting.BackSmall
}

setting_view.CrossDressSelectModel = {
	presentor="CrossDressSelectModelPresentor",showMode=ViewSetting.kModalWindow,extId=288
}

setting_view.CrossDressVote = {
	presentor="CrossDressVotePresentor",showMode=ViewSetting.kFullScreenWindow,extId=288, supportBack = ViewSetting.BackBig
}

setting_view.CrossDressRank = {
	presentor="CrossDressRankPresentor",showMode=ViewSetting.kFullScreenWindow,extId=288, supportBack = ViewSetting.BackBig
}

setting_view.CrossPreview = {
	presentor="CrossPreviewPresentor",showMode=ViewSetting.kFullScreenWindow,extId=288, supportBack = ViewSetting.BackBig
}

setting_view.CrossDressThanks = {
	presentor="CrossDressThanksMsgPresentor",showMode=ViewSetting.kModalWindow,extId=288
}

setting_view.SurpriseSignInView =  {
	presentor="SurpriseSignInPresentor",showMode=ViewSetting.kModalView,extId=-1,
}

setting_view.MagicCrystalBottleView =  {
	presentor="MagicCrystalBottlePresentor", showMode=ViewSetting.kFullScreenView,extId = -1
}
setting_view.Return_MagicCrystalBottleView =  {
	presentor="Return_MagicCrystalBottlePresentor", showMode=ViewSetting.kFullScreenView,extId = -1
}

setting_view.RockingboatJumpView = {
	presentor="RockingboatJumpViewPresentor",showMode=ViewSetting.kFullScreenWindow,extId=287,autoDestroyTime=10
}

setting_view.PayAttentionToWeComView = {
	presentor = "PayAttentionToWeComViewPresentor", showMode = ViewSetting.kNormalView, extId = 289
}
setting_view.CBossRewardsPreview = {
	presentor = "CBossRewardsPreviewPresentor", showMode = ViewSetting.kModalView, extId = 59
}

setting_view.CrossDressActivityView = {
	presentor = "CrossDressActivityPresentor", showMode = ViewSetting.kModalView, extId = 289
}

setting_view.DrawAndGuessCreateRoomPanel = {
	presentor = "DrawAndGuessCreateRoomPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.DrawAndGuessSelectQuestionView = {
	presentor = "DrawAndGuessSelectQuestionViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.Magicalland4GameView = {
	presentor = "Magicalland4GamePresentor", showMode = ViewSetting.kFullScreenView, extId = 77, supportBack = 101, noPadMask = true
}

setting_view.Magicalland4GuideView = {
	presentor = "Magicalland4GuidePresentor", showMode = ViewSetting.kFullScreenView, extId = -1
}

setting_view.Magicalland5GuideView = {
	presentor = "Magicalland5GuidePresentor", showMode = ViewSetting.kFullScreenView, extId = -1
}

setting_view.DrawAndGuessVoteView = {
	presentor = "DrawAndGuessVoteViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 78, supportBack = ViewSetting.BackBig,hideHUD=true,noPadMask=true
}

setting_view.DrawAndGuessGameView = {
	presentor = "DrawAndGuessGameViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 78 , supportBack = ViewSetting.BackBig,hideHUD=true,noPadMask=true
}

setting_view.DrawAndGuessFinishView = {
	presentor = "DrawAndGuessFinishViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 78 ,supportBack = ViewSetting.BackBig,hideHUD=true,noPadMask=true
}

setting_view.MagicalLandInteractionFour = {
	presentor = "MagicalLandInteractionFourPresentor", showMode=ViewSetting.kFullScreenView, hideHUD=true
}
setting_view.NewYearSelectFireworkView = {
	presentor = "NewYearSelectFireworkViewPresentor", showMode=ViewSetting.kModalWindow, hideHUD=true
}
setting_view.NewYearMsgRememberView = {
	presentor = "NewYearMsgRememberViewPresentor", showMode=ViewSetting.kModalWindow, hideHUD=true
}

setting_view.Magicalland4Book = {
	presentor = "Magicalland4BookViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.Magicalland5Book = {
	presentor = "Magicalland5BookViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.DrawAndGuessResultPanel = {
	presentor = "DrawAndGuessResultPanelPresentor", showMode = ViewSetting.kModalWindow
}
setting_view.NewYear24MsgSendView = {
	presentor = "NewYear24MsgSendViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.DrawAndGuessAwardView = {
	presentor = "DrawAndGuessAwardViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.DrawAndGuessRoomSettingPanel = {
	presentor = "DrawAndGuessRoomSettingPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.TaskSignView = {
	presentor = "TaskSignViewPresentor", showMode=ViewSetting.kModalWindow,  extId=293
}
setting_view.NewYearTimeLineView = {
	presentor = "NewYearTimeLineViewPresentor", showMode=ViewSetting.kModalWindow, hideHUD = true
}

setting_view.EcoParkEntranceView = {
	presentor = "EcoParkEntranceViewPresentor", showMode = ViewSetting.kFullScreenView, extId = 79, showRead = true
}

setting_view.EcoParkPreviewPanel = {
	presentor = "EcoParkPreviewPanelPresentor", showMode = ViewSetting.kModalWindow, extId = 79
}

setting_view.EcoParkEditView = {
	presentor = "EcoParkEditViewPresentor", showMode = ViewSetting.kWindow, extId = 79, showRead = true, hideHUD = true
}

setting_view.EcoParkUnlockAreaPanel = {
	presentor = "EcoParkUnlockAreaPanelPresentor", showMode = ViewSetting.kModalWindow, extId = 79
}

setting_view.EcoParkUsePotionPanel = {
	presentor = "EcoParkUsePotionPanelPresentor", showMode = ViewSetting.kModalWindow, extId = 79
}

setting_view.EcoParkAddCapacityPanel = {
	presentor = "EcoParkAddCapacityPanelPresentor", showMode = ViewSetting.kModalWindow, extId = 79
}

setting_view.EcoParkArchiveRewardsPanel = {
	presentor="EcoParkArchiveRewardsPanelPresentor",showMode=2,extId=-1,openSound=141098
}

setting_view.EcoParkGroceryPanel = {
	presentor = "EcoParkGroceryPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79,supportBack = ViewSetting.BackBig, showRead=true,hideHUD=true
}

setting_view.EcoParkGrocerySelectPanel = {
	presentor = "EcoParkGrocerySelectPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkGrocerySelectInfoPanel = {
	presentor = "EcoParkGrocerySelectInfoPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkIslandGainPanel = {
	presentor = "EcoParkIslandGainPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkGainPanel = {
	presentor = "EcoParkGainPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkSpeciesInfoPanel = {
	presentor = "EcoParkSpeciesInfoPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79, supportBack=ViewSetting.BackSmall
}

setting_view.EcoParkRecipeIconInfo = {
	presentor = "EcoParkRecipeInfoPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkSpeciesDetailPanel = {
	presentor = "EcoParkSpeciesDetailPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkArchivePanel = {
	presentor = "EcoParkArchivePanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkArchiveInfoPanel = {
	presentor = "EcoParkArchiveInfoPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkArchiveSuitPanel = {
	presentor = "EcoParkArchiveSuitPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkArchiveBuffPanel = {
	presentor = "EcoParkArchiveBuffPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkGatherPanel = {
	presentor = "EcoParkGatherPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkBuffInfoPanel = {
	presentor = "EcoParkBuffInfoPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkDiaryPanel = {
	presentor = "EcoParkDiaryPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkRechargeView = {
	presentor = "EcoParkRechargeViewPresentor",showMode = ViewSetting.kNormalView,supportBack = ViewSetting.BackSmall, extId = 296
}

setting_view.EcoParkEditDelItemPanel = {
	presentor = "EcoParkEditDelItemPanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.EcoParkGuidePanel = {
	presentor = "EcoParkGuidePanelPresentor", showMode=ViewSetting.kModalWindow,extId=79
}

setting_view.AobiReturnTeamShop = {
	presentor = "AobiReturnTeamShopPresentor", showMode=ViewSetting.kModalWindow,supportBack = ViewSetting.BackSmall
}

setting_view.returnShopView = {
	presentor = "ReturnShopViewPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.WealthGodFollow = {
	presentor = "WealthGodFollowGamePresentor",showMode = ViewSetting.kWindow,extId = -1
}

setting_view.WealthGodGoToView = {
	presentor = "WealthGodGoToPresentor", showMode=ViewSetting.kModalWindow, hideHUD = true
}

setting_view.RoralPalaceSedanPanel = {
	presentor = "RoralPalaceSedanPanelPresentor", showMode=ViewSetting.kModalWindow, hideHUD = true, showRead=true,supportBack = ViewSetting.BackSmall
}

setting_view.HotpotLovePanel = {
	presentor="HotpotLoveLimitPanelPresentor", showMode=ViewSetting.kWindow, extId=103,
}

setting_view.DIYCanvasBagActView = {
	presentor="DIYCanvasBagActPresentor", showMode=ViewSetting.kFullScreenView, extId=-1,
}
setting_view.DIYCanvasBagStickerView = {
	presentor="DIYCanvasBagStickerPresentor", showMode=ViewSetting.kModalView, extId=-1,
}
setting_view.DIYStickersBookView = {
	presentor="DIYStickersBookPresentor", showMode=ViewSetting.kModalView, extId=-1,
}
setting_view.DIYCanvasBagGameView = {
	presentor="DIYCanvasBagGamePresentor", showMode=ViewSetting.kFullScreenView, extId=-1,supportBack = ViewSetting.BackBig
}
setting_view.PuzzleBobbleDataTestView = {
	presentor="PuzzleBobbleDataTestViewPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.AINpcSurveyView = {
	presentor="AINpcSurveyViewPresentor", showMode=ViewSetting.kModalWindow
}
setting_view.AINpcChatView = {
	presentor="AINpcChatViewPresentor", showMode=ViewSetting.kFullScreenView, supportBack=101
}

setting_view.NewWorkShoppingView = {
	presentor="NewWorkShoppingPresentor", showMode=ViewSetting.kModalWindow, supportBack=101
}

setting_view.ConnectLineGamePanel = {
	presentor = "ConnectLineGamePanelPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}
setting_view.SampleReplace =  {
	presentor="SampleReplacePresentor",showMode=ViewSetting.kModalView,extId=8
}

setting_view.PassDungeonView = {
	presentor = "PassDungeonViewPresentor", showMode=ViewSetting.kFullScreenView,extId= 7, hideHUD=true
}


setting_view.FurnitureTempalteSquare = {
	presentor="FurnitureTempalteSquarePresentor", showMode=ViewSetting.kModalWindow, extId = 82, hideHUD = true, supportBack=101
}

setting_view.FurnitureTemplateSelectLabel = {
	presentor="FurnitureTemplateSelectLabelPresentor", showMode=ViewSetting.kModalView, extId = 82
}

setting_view.FurnitureTemplateDetail = {
	presentor="FurnitureTemplateDetailPresentor",showMode=ViewSetting.kModalView,extId=82, supportBack=ViewSetting.BackSmall
}

setting_view.TemplatePreviewHUD = {
	presentor="TemplatePreviewHUDPresentor",showMode=ViewSetting.kWindow,extId=82, supportBack=ViewSetting.BackBig
}

setting_view.EraseFlowerView = {
	presentor="EraseFlowerPresentor", showMode=ViewSetting.kFullScreenView, extId=404
}
setting_view.TinyGameTimeView = {
	presentor="TinyGameTimeViewPresentor", showMode=ViewSetting.kNormalView, extId=409
}

setting_view.EraseFlowerGameView = {
	presentor="EraseFlowerGamePresentor", showMode=ViewSetting.kFullScreenView, supportBack=ViewSetting.BackBig
}

setting_view.CollectCardPermissionPanel = {
	presentor = "CollectCardPermissionPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardSearchPanel = {
	presentor = "CollectCardSearchPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardFilterPanel = {
	presentor = "CollectCardFilterPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardThanksPanel = {
	presentor = "CollectCardThanksPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardDressPreviewPanel = {
	presentor = "CollectCardDressPreviewPanelPresentor", showMode=ViewSetting.kFullScreenView,extId=405
}

setting_view.CollectCardRewardPanel = {
	presentor = "CollectCardRewardPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardSelectPanel = {
	presentor = "CollectCardSelectPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardEntrancePanel = {
	presentor = "CollectCardEntrancePanelPresentor", showMode=ViewSetting.kFullScreenView,extId=405
}

setting_view.CollectCardThemeListPanel = {
	presentor = "CollectCardThemeListPanelPresentor", showMode=ViewSetting.kFullScreenView,extId=405
}

setting_view.CollectCardThemeContentPanel = {
	presentor = "CollectCardThemeContentPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardDetailPanel = {
	presentor = "CollectCardDetailPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardBoxBagPanel = {
	presentor = "CollectCardBoxBagPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardGroceryPanel = {
	presentor = "CollectCardGroceryPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardPathPanel = {
	presentor = "CollectCardPathPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardRecordPanel = {
	presentor = "CollectCardRecordPanelPresentor", showMode=ViewSetting.kModalWindow,extId=405
}

setting_view.CollectCardArchivePanel = {
	presentor = "CollectCardArchivePanelPresentor", showMode=ViewSetting.kFullScreenView,extId=405
}

setting_view.RhythmBattles_Lvl3Panel = {
	presentor="RhythmBattles_Lvl3PanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattles_Lvl4Panel = {
	presentor="RhythmBattles_Lvl4PanelPresentor", showMode=ViewSetting.kNormalView
}

setting_view.RhythmBattles_ReactionGamePanel = {
	presentor="RhythmBattles_ReactionGamePanelPresentor", showMode=ViewSetting.kNormalView
}


setting_view.SeasonChainBagView = {
	presentor = "SeasonChainBagViewPresentor",showMode = ViewSetting.kNormalView,supportBack = ViewSetting.BackSmall, extId = 410
}

setting_view.TakePhotoDressView = {
	presentor="TakePhotoDressViewPresentor", showMode=ViewSetting.kFullScreenView
}

setting_view.CreatePortalPanel = {
	presentor="CreatePortalPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.SceneInvitePanel = {
	presentor="SceneInvitePanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.MinesWeeperGameView = {
	presentor="MinesWeeperGamePresentor", showMode = ViewSetting.kNormalView
}

setting_view.MinesWeeperActView = {
	presentor="MinesWeeperActPresentor", showMode = ViewSetting.kNormalView
}

setting_view.CommonGameResultView = {
	presentor="CommonGameResultPresentor", showMode = ViewSetting.kModalView
}

setting_view.GMShareTestView = {
	presentor="GMShareTestViewPresentor", showMode = ViewSetting.kNormalView
}

setting_view.PuzzleBobbleGameView = {
	presentor="PuzzleBobbleGameViewPresentor", showMode=ViewSetting.kFullScreenView,autoDestroyTime = 0.1
}
setting_view.PuzzleBobbleMatchView = {
	presentor="PuzzleBobbleMatchPresentor", showMode=ViewSetting.kFullScreenView, supportBack=ViewSetting.BackBig,
}
setting_view.PuzzleBobbleResultView = {
	presentor="PuzzleBobbleResultPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.FlappyBirdCreateRoomView = {
	presentor = "FlappyBirdCreateRoomPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FurnitureTemplateThank = {
	presentor="FurnitureTemplateThankPresentor", showMode = ViewSetting.kModalView
}

setting_view.ClassifyReNameView =  {
	presentor="ClassifyReNamePresentor",showMode=ViewSetting.kModalWindow,extId=1,autoDestroyTime=10
}

setting_view.ActivitySelectOrderPanel = {
	presentor = "ActivitySelectOrderPanelPresentor", showMode=ViewSetting.kFullScreenView,extId=-1,supportBack= ViewSetting.BackBig
}

setting_view.ThroughSpaceTimeView = {
	presentor = "ThroughSpaceTimePresentor", showMode=ViewSetting.kModalWindow,extId= 7
}

setting_view.TwoZeroFourEightActivityView =  {
	presentor="TwoZeroFourEightActivityViewPresentor",showMode=ViewSetting.kFullScreenView,extId= 412
}

setting_view.TwoZeroFourEightGameView =  {
	presentor="TwoZeroFourEightGameViewPresentor",showMode=ViewSetting.kFullScreenView,extId= 412
}

setting_view.PuzzleBobbleRoomView = {
	presentor = "PuzzleBobbleRoomViewPresentor", showMode=ViewSetting.kModalWindow,extId= 411
}

setting_view.ActivityOverView = {
	presentor = "ActivityOverViewPresentor", showMode=ViewSetting.kNormalView,extId=-1
}

setting_view.JoinUmbrellaPanel = {
	presentor = "JoinUmbrellaPanelPresentor", showMode=ViewSetting.kWindow
}

setting_view.FindTheItemLevelView = {
	presentor = "FindTheItemLevelPresentor", showMode=ViewSetting.kFullScreenView, extId=417, supportBack=ViewSetting.BackBig
}

setting_view.FindTheItemGameView = {
	presentor = "FindTheItemGamePresentor", showMode=ViewSetting.kFullScreenView, extId=417, supportBack=ViewSetting.BackBig
}

setting_view.FindTheItemResultView = {
	presentor = "FindTheItemResultPresentor", showMode=ViewSetting.kModalView, extId=-1
}

setting_view.SeaChooseDepthPanel = {
	presentor = "SeaChooseDepthPanelPresentor", showMode = ViewSetting.kWindow, extId = 86
}

setting_view.SeaMainView = {
	presentor = "SeaMainViewPresentor", showMode = ViewSetting.kWindow, extId = 86, 
}

setting_view.SeaQte1View = {
	presentor = "SeaQte1ViewPresentor", showMode = ViewSetting.kWindow, extId = 86, hideHUD = true
}

setting_view.SeaQte2View = {
	presentor = "SeaQte2ViewPresentor", showMode = ViewSetting.kWindow, extId = 86, hideHUD = true
}

setting_view.SeaFishSucView = {
	presentor = "SeaFishSucViewPresentor", showMode = ViewSetting.kWindow, extId = 86
}

setting_view.SeaTakePhotoView = {
	presentor = "SeaTakePhotoPresentor",showMode=ViewSetting.kWindow,extId=23,supportBack=101,logId=129
}

setting_view.SeaPlotCollectionView = {
	presentor = "SeaPlotCollectionPresentor",showMode = ViewSetting.kWindow,hideHUD = true
}

setting_view.SeaTechTreeView = {
	presentor = "SeaTechTreePresentor",showMode=ViewSetting.kFullScreenView,supportBack = 101
}

setting_view.SeaTaskDetailsView = {
	presentor = "SeaTaskDetailsPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.SeaCloverView = {
	presentor = "SeaLoverPresentor",showMode=ViewSetting.kModalWindow
}

setting_view.SeaOrderUpgradeView = {
	presentor = "SeaOrderUpgradePresentor",showMode=2,supportBack = 102
}

setting_view.ActivitySeaBossView = {
	presentor = "ActivitySeaBossViewPresentor", showMode = ViewSetting.kNormalView
}

setting_view.ActivitySeaFishTaskView = {
	presentor = "ActivitySeaFishTaskViewPresentor", showMode = ViewSetting.kNormalView
}

setting_view.ActivitySeaFishTaskRewardView = {
	presentor = "ActivitySeaFishTaskRewardViewPresentor", showMode = ViewSetting.kModalView
}

setting_view.ActivitySeaShopView = {
	presentor = "ActivitySeaShopViewPresentor", showMode = ViewSetting.kNormalView
}

setting_view.ActivitySeaRewardStartView = {
	presentor = "ActivitySeaRewardStartViewPresentor", showMode = ViewSetting.kModalView
}

setting_view.ActivitySeaRewardView = {
	presentor = "ActivitySeaRewardViewPresentor", showMode = ViewSetting.kModalWindow, extId = -1, openSound = 141098
}

setting_view.ActivitySeaRaceView = {
	presentor = "ActivitySeaRaceViewPresentor", showMode = ViewSetting.kNormalView
}

setting_view.ActivitySeaRaceResultView = {
	presentor = "ActivitySeaRaceResultViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.ActivitySeaRaceRankingView = {
	presentor = "ActivitySeaRaceRankingViewPresentor", showMode = ViewSetting.kModalWindow, extId = -1, supportBack = ViewSetting.BackSmall
}

setting_view.DuommMainView = {
	presentor = "DuommMainViewPresentor", showMode = ViewSetting.kNormalView, extId = 92
}

setting_view.DuommResultPanel = {
	presentor = "DuommResultPanelPresentor", showMode = ViewSetting.kModalWindow, extId = 92
}

setting_view.DuommSkillTipsView = {
	presentor = "DuommSkillTipsViewPresentor", showMode = ViewSetting.kWindow, extId = 92
}

setting_view.DuommPvView = {
	presentor = "DuommPvViewPresentor", showMode = ViewSetting.kWindow, extId = 92
}

setting_view.CarnivalPartyMapView = {
	presentor = "CarnivalPartyMapPresentor", showMode=ViewSetting.kModalWindow, extId= -1, hideHUD=true, supportBack= ViewSetting.BackBig, noPadMask = true
}
--拯救小水妖
setting_view.SmallSeaSpiritGameView = {
	presentor = "SmallSeaSpiritGameViewPresenter",showMode=ViewSetting.kFullScreenWindow, supportBack= ViewSetting.BackBig
}
--拯救小水妖活动面板
setting_view.SmallSeaSpiritActivityView = {
	presentor = "SmallSeaSpiritActivityViewPresentor",showMode=ViewSetting.kFullScreenWindow, supportBack= ViewSetting.BackBig
}
setting_view.SeaDanceMainPanel = {
	presentor = "SeaDanceMainPanelPresentor", showMode=ViewSetting.kWindow,extId=419
}

setting_view.SeaDanceClickPanel = {
	presentor = "SeaDanceClickPanelPresentor", showMode=ViewSetting.kWindow,extId=419
}

setting_view.SeaDanceResultPanel = {
	presentor = "SeaDanceResultPanelPresentor", showMode=ViewSetting.kModalWindow,extId=419
}

setting_view.SeaDanceCountdownPanel = {
	presentor = "SeaDanceCountdownPanelPresentor", showMode=ViewSetting.kNormalView,extId=419
}

setting_view.SeaDanceTeamPanel = {
	presentor = "SeaDanceTeamPanelPresentor", showMode=ViewSetting.kWindow,extId=419
}

setting_view.SeaDanceTogetherPanel = {
	presentor = "SeaDanceTogetherPanelPresentor", showMode=ViewSetting.kWindow,extId=419
}

setting_view.SeaDanceActivityPanel = {
	presentor = "SeaDanceActivityPanelPresentor", showMode=ViewSetting.kFullScreenView,extId=419
}

setting_view.SeaDanceLoadingPanel = {
	presentor = "SeaDanceLoadingPanelPresentor", showMode=ViewSetting.kWindow,extId=419
}

setting_view.WishingTreeView = {
	presentor = "WishingTreeViewPresentor", showMode=ViewSetting.kModalWindow,extId=10001,supportBack= ViewSetting.BackBig
}

setting_view.WishingTreeItemView = {
	presentor = "WishingTreeItemViewPresentor", showMode=ViewSetting.kModalWindow,extId=10001
}

setting_view.WishingTreeWishView = {
	presentor = "WishingTreeWishViewPresentor", showMode=ViewSetting.kModalWindow,extId=10001
}

setting_view.CashRedEnvelopeEntry = {
	presentor = "CashRedEnvelopeEntryPresentor", showMode=ViewSetting.kModalWindow, extId=418
}

setting_view.CashRedEnvelopeRecord = {
	presentor = "CashRedEnvelopeRecordPresentor", showMode=ViewSetting.kModalWindow, extId=418
}

setting_view.CashRedEnvelopeDetail = {
	presentor = "CashRedEnvelopeDetailPresentor", showMode=ViewSetting.kModalWindow, extId=418
}

setting_view.WishingTreeTipsView = 
{
	presentor = "WishingTreeTipsViewPresentor", showMode=ViewSetting.kModalWindow,extId=10001
}

setting_view.SeaTaskRobView = {
	presentor = "SeaTaskRobPresentor",showMode = ViewSetting.kWindow
}


setting_view.MergeWatermelon = {
	presentor = "MergeWatermelonPresentor",showMode = ViewSetting.kWindow
}

setting_view.MergeWatermelonActView = {
	presentor = "MergeWatermelonActViewPresenter",showMode = ViewSetting.kFullScreenView, supportBack= ViewSetting.BackBig
}

setting_view.MergeWatermelonNewbie = {
	presentor = "MergeWatermelonNewbiePresentor",showMode = ViewSetting.kWindow
}
setting_view.MergeWatermelonShowView = {
	presentor = "MergeWatermelonShowViewPresentor",showMode = ViewSetting.kModalWindow
}
setting_view.MergeWatermelonRoomView = {
	presentor = "MergeWatermelonRoomViewPresentor",showMode = ViewSetting.kModalWindow
}
setting_view.MergeWatermelonResultView = {
	presentor = "MergeWatermelonResultPresentor",showMode = ViewSetting.kModalWindow
}

setting_view.RoadSideBakerActivityView = {
	presentor = "RoadSideBakerActivityViewPresentor",showMode = ViewSetting.kFullScreenView,extId = 422, supportBack= ViewSetting.BackBig
}

setting_view.RoadSideBakerGameView = {
	presentor = "RoadSideBakerGameViewPresentor",showMode = ViewSetting.kFullScreenView,extId = 422, supportBack= ViewSetting.BackBig
}

setting_view.SeaFishShowView = {
	presentor = "SeaFishShowPresentor",showMode = ViewSetting.kModalWindow
}

setting_view.DonateAct = {
	presentor = "DonateActPresentor",showMode = ViewSetting.kModalWindow, extId = 421, supportBack= ViewSetting.BackBig
}

setting_view.DonateActTask = {
	presentor = "DonateActTaskPresentor",showMode = ViewSetting.kModalWindow,extId = 421
}

setting_view.PetCircusLevelView = {
	presentor = "PetCircusLevelPresentor", showMode=ViewSetting.kFullScreenView, extId= 423, hideHUD=true, supportBack= ViewSetting.BackBig, noPadMask = true
}

setting_view.PetCircusGameView = {
	presentor = "PetCircusGamePresentor", showMode=ViewSetting.kFullScreenView, extId= 423, hideHUD=true, supportBack= ViewSetting.BackBig, noPadMask = true
}

setting_view.PetCircusCreateRoomView = {
	presentor = "PetCircusCreateRoomPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.SeaBossView = {
	presentor = "SeaBossPresentor",showMode = ViewSetting.kWindow
}

setting_view.PetCircusRecordView = {
	presentor = "PetCircusRecordPresentor", showMode=ViewSetting.kModalWindow, supportBack=ViewSetting.BackSmall
}

setting_view.FireFireballView = {
	presentor = "FireFireballViewPresentor",showMode = ViewSetting.kFullScreenView
}

setting_view.Act425MainView = {
	presentor = "Activity425MainPresentor", showMode = ViewSetting.kFullScreenView, supportBack=ViewSetting.BackBig
}

setting_view.Act429MainView = {
	presentor = "Act429MainPresentor", showMode = ViewSetting.kFullScreenView, supportBack=ViewSetting.BackBig
}

setting_view.Act457MainView = {
	presentor = "Activity457ViewPresentor", showMode = ViewSetting.kNormalView
}

setting_view.Act457ConfirmView = {
	presentor = "Act457ConfirmViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.Act459MainView = {
	presentor = "Act459MainViewPresentor", showMode = ViewSetting.kNormalView
}

setting_view.Act459SelectView = {
	presentor = "Act459SelectViewPresentor",showMode = ViewSetting.kModalView
}

setting_view.FireFireballView = {
	presentor = "FireFireballViewPresentor",showMode = ViewSetting.kFullScreenView
}

setting_view.ScrollingMachineView = {
	presentor = "ScrollingMachineViewPresentor",showMode = ViewSetting.kFullScreenView,supportBack= ViewSetting.BackBig
}

setting_view.ScrollingMachineGetView = {
	presentor = "ScrollingMachineGetViewPresentor",showMode = ViewSetting.kModalWindow
}

setting_view.ScrollingMachineShopView = {
	presentor = "ScrollingMachineShopViewPresentor",showMode = ViewSetting.kFullScreenView,supportBack= ViewSetting.BackBig
}

setting_view.ScrollingMachineCardView = {
	presentor = "ScrollingMachineCardViewPresentor",showMode = ViewSetting.kModalWindow
}

setting_view.SeaBossEventView = {
	presentor = "SeaBossEventPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FatigueGroceryPanel = {
	presentor = "FatigueGroceryPanelPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.SeaTaskHelpView =  {
	presentor="SeaTaskHelpPresentor",showMode=ViewSetting.kWindow,extId=-1,
}

setting_view.ActivityTotalRecharge430Panel = {
	presentor = "ActivityTotalRecharge430PanelPresentor", showMode=ViewSetting.kModalWindow,extId=430
}

setting_view.SpliceEquipmentView = {
	presentor = "SpliceEquipmentPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}

setting_view.WaterPipesRepairView = {
	presentor = "WaterPipesRepairViewPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}

setting_view.SeaTaskHelpView =  {
	presentor="SeaTaskHelpPresentor",showMode=ViewSetting.kWindow,extId=-1,
}

setting_view.ActivityTotalRecharge430Panel = {
	presentor = "ActivityTotalRecharge430PanelPresentor", showMode=ViewSetting.kModalWindow,extId=430
}

setting_view.SeaBusRuleView = {
	presentor = "SeaBusRuleViewPresentor", showMode=ViewSetting.kModalWindow,extId=430
}

setting_view.ActivitySeaBusView = {
	presentor = "ActivitySeaBusViewPresentor",showMode = ViewSetting.kNormalView
}

setting_view.Anniversary15Main = {
	presentor = "Anniversary15MainPresentor", showMode=ViewSetting.kModalWindow,extId=188
}

setting_view.Anniversary15Sign = {
	presentor = "Anniversary15SignPresentor", showMode=ViewSetting.kModalWindow,extId=188
}

setting_view.Anniversary15Cake = {
	presentor = "Anniversary15CakePresentor", showMode=ViewSetting.kModalWindow,extId=188
}

setting_view.Anniversary15Story = {
	presentor = "Anniversary15StoryPresentor", showMode=ViewSetting.kModalWindow,extId=188
}

setting_view.HappySigninView = {
	presentor = "HappySigninPresentor", showMode=ViewSetting.kFullScreenView,extId=434
}

setting_view.UnrealAreaMainPanel = {
	presentor = "UnrealAreaMainPanelPresentor", showMode=ViewSetting.kFullScreenView,supportBack= ViewSetting.BackBig,extId=431,noPadMask=true
}

setting_view.UnrealAreaSelectPanel = {
	presentor = "UnrealAreaSelectPanelPresentor", showMode=ViewSetting.kFullScreenView,supportBack= ViewSetting.BackBig,extId=431,noPadMask=true
}

setting_view.UnrealAreaLevelPanel = {
	presentor = "UnrealAreaLevelPanelPresentor", showMode=ViewSetting.kFullScreenView,supportBack= ViewSetting.BackBig,extId=431,noPadMask=true
}

setting_view.UnrealAreaMultiBuyPanel = {
	presentor = "UnrealAreaMultiBuyPanelPresentor", showMode=ViewSetting.kModalWindow,extId=431
}

setting_view.UnrealAreaPreviewPanel = {
	presentor = "UnrealAreaPreviewPanelPresentor", showMode=ViewSetting.kFullScreenView,extId=431
}

setting_view.UnrealAreaLevelUpPanel = {
	presentor = "UnrealAreaLevelUpPanelPresentor", showMode=ViewSetting.kModalWindow,extId=431
}

setting_view.UnrealAreaGiftPanel = {
	presentor = "UnrealAreaGiftPanelPresentor", showMode=ViewSetting.kModalWindow,extId=431
}

setting_view.UnrealAreaAddExpAniPanel = {
	presentor = "UnrealAreaAddExpAniPanelPresentor", showMode=ViewSetting.kModalWindow,extId=431
}

setting_view.SeaBusHudPanel = {
	presentor = "SeaBusHudPresentor", showMode=ViewSetting.kWindow
}

setting_view.EraseFlowerDialogView =  {
	presentor="EraseFlowerDialogPresentor",showMode=2,extId=-1,openSound=SoundManager.DefaultShowPanel,closeSound=SoundManager.DefaultClosePanel
}

setting_view.DollHouseAwardView = {
	presentor = "DollHouseAwardPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.Activity435Main =  {
	presentor="Activity435MainPresentor", showMode=ViewSetting.kModalWindow, syncOpenList = {"Activity435Game"}, syncCloseList = {"Activity435Game"}, extId=435,supportBack= ViewSetting.BackBig
}

setting_view.Activity435Game =  {
	presentor="Activity435GamePresentor", showMode=ViewSetting.kModalWindow, extId=435
}

setting_view.Activity435Result =  {
	presentor="Activity435ResultPresentor", showMode=ViewSetting.kModalWindow, extId=435
}

setting_view.Activity435Reward =  {
	presentor="Activity435RewardPresentor", showMode=ViewSetting.kModalWindow, extId=435
}

setting_view.RoadSideBakerMiddleActRewardView =  {
	presentor="RoadSideBakerMiddleActRewardViewPresentor", showMode=ViewSetting.kModalWindow, extId=420
}

setting_view.BreakoutGameView =  {
	presentor="BreakoutGameViewPresentor",showMode=ViewSetting.kFullScreenView
}

setting_view.GMXXLMapEditorView =  {
	presentor="GMXXLMapEditorPresentor",showMode=ViewSetting.kFullScreenView
}
setting_view.RankingListEntryPanel =  {
	presentor="RankingListEntryPresentor",showMode=ViewSetting.kFullScreenView,supportBack= ViewSetting.BackBig
}

setting_view.Magicalland5GameView = {
	presentor = "Magicalland5GamePresentor", showMode = ViewSetting.kFullScreenView,noPadMask = true,hideHUD = true,supportBack = 101
}

--showMode = ViewSetting.kFullScreenView, extId = 62, supportBack = 101, noPadMask = true
setting_view.BreakoutEnterView = {
	presentor = "BreakoutEnterViewPresentor", showMode = ViewSetting.kFullScreenView,extId=435,supportBack=ViewSetting.BackBig
}

setting_view.GameReviveView = {
	presentor = "GameReviveViewPresentor", showMode = ViewSetting.kWindow
}

setting_view.BreakoutSkillView = {
	presentor = "BreakoutSkillViewPresentor", showMode = ViewSetting.kModalWindow,extId=435
}

setting_view.BreakoutCreateRoomPanel = {
	presentor = "BreakoutCreateRoomPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.BreakoutResultView = {
	presentor = "BreakoutResultViewPresentor", showMode = ViewSetting.kModalWindow,extId=435
}

setting_view.BreakoutRecordView = {
	presentor = "BreakoutRecordViewPresentor", showMode = ViewSetting.kModalWindow,extId=435
}

setting_view.BreakoutSkillSmallPanel = {
	presentor = "BreakoutSkillSmallPanelPresentor", showMode = ViewSetting.kModalWindow,extId=435
}

setting_view.BreakoutUnlockSkillView = {
	presentor = "BreakoutUnlockSkillViewPresentor", showMode = ViewSetting.kModalWindow,extId=435
}

setting_view.BreakoutBlockView = {
	presentor = "BreakoutBlockViewPresentor", showMode = ViewSetting.kModalWindow,extId=435
}

setting_view.BreakoutMatchView = {
	presentor="BreakoutMatchViewPresentor", showMode=ViewSetting.kModalWindow,extId=435
}

setting_view.IceSkatingGameCreateRoom = {
	presentor = "IceSkatingGameCreateRoomPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.IceSkatingGame = {
	presentor = "IceSkatingGamePresentor", showMode = ViewSetting.kWindow, noPadMask = true, hideHUD = true, supportBack= ViewSetting.BackBig,extId=440
}

setting_view.IceSkatingEntry = {
	presentor = "IceSkatingEntryPresentor", showMode = ViewSetting.kWindow, noPadMask = true, hideHUD = true, supportBack= ViewSetting.BackBig,extId=440
}

setting_view.IceSkatingResult = {
	presentor = "IceSkatingResultPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.IceSkatingRecord = {
	presentor = "IceSkatingRecordPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.LandlordGamePanel = {
	presentor = "LandlordGamePanelPresentor", showMode=ViewSetting.kFullScreenView,extId=-1,autoDestroyTime=0.1
}

setting_view.LandlordGameResultPanel = {
	presentor = "LandlordGameResultPanelPresentor", showMode=ViewSetting.kFullScreenView,extId=-1
}

setting_view.LandlordPanel = {
	presentor = "LandlordPanelPresentor", showMode=ViewSetting.kFullScreenView,supportBack= ViewSetting.BackBig,extId=-1
}

setting_view.LandlordMatchSuccPanel = {
	presentor = "LandlordMatchSuccPanelPresentor", showMode=ViewSetting.kModalView,extId=-1
}

setting_view.LandlordCreateRoomPanel = {
	presentor = "LandlordCreateRoomPanelPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.LandlordArenaAwardPanel = {
	presentor = "LandlordArenaAwardPanelPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.LandlordRecordPanel = {
	presentor = "LandlordRecordPanelPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.LandlordAwardPanel = {
	presentor = "LandlordAwardPanelPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.LandlordQuickMessagePanel = {
	presentor = "LandlordQuickMessagePanelPresentor", showMode=ViewSetting.kWindow,extId=-1
}

setting_view.XiaoXiaoLeGameView =  {
	presentor="XiaoXiaoLeGamePresentor",showMode=ViewSetting.kFullScreenView,extId=-1
}

setting_view.ArtExhibitionPanel = {
	presentor = "ArtExhibitionPanelPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.MagicallandInteractionFive = {
	presentor = "MagicallandInteractionFivePresentor", showMode=ViewSetting.kFullScreenView, hideHUD=true
}

setting_view.MeddleActView = {
	presentor = "MiddleActPresentor", showMode=ViewSetting.kFullScreenView
}
setting_view.TownSimulatorUpgradeView = {
	presentor = "TownSimulatorUpgradeViewPresenter", showMode=ViewSetting.kModalWindow
}

setting_view.TownSimulatorTaskView = {
	presentor = "TownSimulatorTaskPresentor",showMode = ViewSetting.kModalWindow,extId = 125,supportBack = ViewSetting.BackSmall,
	syncCloseList = {"MonopolyChange","MonopolyTask"}
}


setting_view.BonfirePartySignUpPanel = {
	presentor = "BonfirePartySignUpPanelPresentor", showMode=ViewSetting.kModalWindow,extId=445
}

setting_view.BonfirePartyTimePanel = {
	presentor = "BonfirePartyTimePanelPresentor", showMode=ViewSetting.kModalWindow,extId=445
}

setting_view.BonfirePartyHudPanel = {
	presentor = "BonfirePartyHudPanelPresentor", showMode=ViewSetting.kWindow,extId=445
}

setting_view.LongStreetBanquetActView = {
	presentor = "LongStreetBanquetActViewPresentor", showMode=ViewSetting.kFullScreenView,extId=444
}

setting_view.LongStreetBanquetRewardView = {
	presentor = "LongStreetBanquetRewardViewPresentor", showMode=ViewSetting.kModalWindow,extId=444
}
setting_view.UpgradeRewardsView = {
	presentor = "UpgradeRewardsViewPresenter", showMode=ViewSetting.kModalWindow
}
setting_view.NewYearGuide = {
	presentor = "NewYearGuidePresenter", showMode=ViewSetting.kModalWindow
}
setting_view.NewYearGuideRewardPreview = {
	presentor = "NewYearGuideRewardPreview", showMode=ViewSetting.kModalWindow
}

setting_view.NewYearOpeningView = {
	presentor = "NewYearOpeningViewPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.LongStreetBanquetTimeBar = {
	presentor = "LongStreetBanquetTimeBarViewPresentor", showMode=ViewSetting.kWindow,extId=444
}

setting_view.XiaoXiaoLeMatchingPanel = {
	presentor="XiaoXiaoLeMatchingPanelPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeGiftView = {
	presentor = "XiaoXiaoLeGiftViewPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLePlotPanel = {
	presentor = "XiaoXiaoLePlotPanelPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeRecordView = {
	presentor = "XiaoXiaoLeRecordViewPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeAwardView = {
	presentor = "XiaoXiaoLeAwardViewPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.BonfirePartyShowPanel = {
	presentor = "BonfirePartyShowPanelPresentor", showMode=ViewSetting.kModalWindow,extId=445
}

setting_view.XiaoXiaoLeShopView = {
	presentor = "XiaoXiaoLeShopPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeWinView = {
	presentor = "XiaoXiaoLeWinViewPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeFailView = {
	presentor = "XiaoXiaoLeFailViewPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeGetCardView = {
	presentor = "XiaoXiaoLeGetCardPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeMatchResultView = {
	presentor = "XiaoXiaoLeMatchResultPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.XiaoXiaoLeOutView = {
	presentor = "XiaoXiaoLeOutPresentor", showMode=ViewSetting.kModalWindow,extId=448
}

setting_view.NewYearGuideShareView = {
	presentor = "NewYearGuideShareViewPresentor", showMode=ViewSetting.kModalWindow,extId=451
}

setting_view.LandlordGameTestView = {
		presentor = "LandlordGameTestViewPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}
setting_view.ActivitySeaHomeTaskView = {
	presentor = "ActivitySeaHomeTaskPresentor", showMode=ViewSetting.kFullScreenView
}

setting_view.ActivityShareStreetTaskView = {
	presentor = "ActShareStreetTaskViewPresentor", showMode=ViewSetting.kFullScreenView,supportBack = ViewSetting.BackBig
}

setting_view.ActivitySeaHomeView = {
	presentor = "ActivitySeaHomePresentor", showMode=ViewSetting.kModalWindow,supportBack = ViewSetting.BackSmall
}

setting_view.ChestRandomSelect = {
	presentor = "ChestRandomSelectPresentor", showMode=ViewSetting.kModalWindow
}

setting_view.TetrisEntryView = {
	presentor = "TetrisEntryViewPresentor", showMode = ViewSetting.kFullScreenWindow, noPadMask = true, hideHUD = true, supportBack= ViewSetting.BackBig,extId=278
}

setting_view.TetrisRankingView = {
	presentor = "TetrisRankingPresentor", showMode = ViewSetting.kModalWindow, extId = -1,supportBack = ViewSetting.BackSmall
}
setting_view.UnlockSkillView = {
	presentor = "UnlockSkillViewPresentor", showMode = ViewSetting.kModalWindow, extId = -1,supportBack = ViewSetting.BackSmall
}
setting_view.SkillSmallPanel = {
	presentor = "SkillSmallPanelPresentor", showMode = ViewSetting.kModalWindow, extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.Chapter3OnePlotInteractive = {
	presentor = "Chapter3OnePlotInteractivePresentor", showMode = ViewSetting.kModalWindow, extId = -1,supportBack = ViewSetting.BackSmall
}

setting_view.DuommAwardView = {
	presentor = "DuommAwardViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.DuommCreateRoomView = {
	presentor = "DuommCreateRoomPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.DuommMatchSuccPanel = {
	presentor = "DuommMatchSuccPanelPresentor", showMode=ViewSetting.kModalView,extId=-1
}

setting_view.ActivityWelfareView = {
	presentor = "ActivityWelfarePresentor", showMode = ViewSetting.kFullScreenWindow, extId = 454
}

setting_view.ActWelfareSubmitView = {
	presentor = "ActWelfareSubmitPresentor", showMode = ViewSetting.kModalView, hideHUD = true, supportBack=ViewSetting.BackBig
}

setting_view.ActWelfareAwardView = {
	presentor = "ActWelfareAwardPresentor", showMode = ViewSetting.kModalView
}

setting_view.ArtShowPreview = {
	presentor = "ArtShowPreviewPresentor", showMode = ViewSetting.kModalView
}

setting_view.ArtShowGetArchive = {
	presentor = "ArtShowGetArchivePresentor", showMode = ViewSetting.kModalView
}

setting_view.ArtShowArchive = {
	presentor = "ArtShowArchivePresentor", showMode = ViewSetting.kModalView
}

setting_view.ArtShowBuy = {
	presentor = "ArtShowBuyPresentor", showMode = ViewSetting.kModalView
}

setting_view.ArtShowExchange = {
	presentor = "ArtShowExchangePresentor", showMode = ViewSetting.kModalView
}

setting_view.RoadSideBakerSelectTimeView = {
	presentor = "RoadSideBakerSelectTimeViewPresentor", showMode = ViewSetting.kWindow
}

setting_view.RoadSideBakerExchangeView = {
	presentor = "RoadSideBakerExchangeViewPresentor", showMode = ViewSetting.kModalView
}

setting_view.PacManGameView = {
	presentor = "PacManGamePresentor", showMode = ViewSetting.kNormalView,noPadMask = true,hideHUD = true
}

setting_view.CrosstheCrossroadsView = {
	presentor = "CrosstheCrossroadsViewPresentor", showMode = ViewSetting.kFullScreenWindow
}
setting_view.Act458View = {
	presentor = "Act458ViewPresentor", showMode = ViewSetting.kFullScreenView,extId = 100, supportBack = ViewSetting.BackBig
}

setting_view.PacManActivityView = {
	presentor = "PacManActivityViewPresentor", showMode = ViewSetting.kFullScreenView,extId = 456, supportBack = ViewSetting.BackBig
}

setting_view.PacManCreateRoomPanel = {
	presentor = "PacManCreateRoomPanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FlyingChessCreateRoom = {
	presentor = "FlyingChessCreateRoomPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FlyingChessGame = {
	presentor = "FlyingChessGamePresentor", showMode = ViewSetting.kWindow, noPadMask = true, hideHUD = true, supportBack= ViewSetting.BackBig, extId = 462
}

setting_view.GamePose = {
	presentor = "GamePosePresentor", showMode = ViewSetting.kWindow
}

setting_view.TaskResignView = {
	presentor = "TaskResignViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.TaskResignAwardView = {
	presentor = "TaskResignAwardViewPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.XiaoXiaoLeLevelView = {
	presentor = "XiaoXiaoLeLevelPresentor", showMode = ViewSetting.kFullScreenView, extId = 448, hideHUD = true, supportBack=ViewSetting.BackBig
}

setting_view.ComGameRoomCreateView = {
	presentor = "ComGameRoomCreatePresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FlyingChessEntry = {
	presentor = "FlyingChessEntryPresentor", showMode = ViewSetting.kWindow, noPadMask = true, extId = 462
}

setting_view.FlyingChessVictory = {
	presentor = "FlyingChessVictoryPresentor", showMode = ViewSetting.kWindow, noPadMask = true, hideHUD = true, supportBack= ViewSetting.BackBig, extId = 462
}

setting_view.FlyingChessResult = {
	presentor = "FlyingChessResultPresentor", showMode = ViewSetting.kWindow, noPadMask = true, hideHUD = true, supportBack= ViewSetting.BackBig, extId = 462
}

setting_view.FlyingChessReady = {
	presentor = "FlyingChessReadyPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FlyingChessVehicleSelect = {
	presentor = "FlyingChessVehicleSelectPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FlyingChessAward = {
	presentor = "FlyingChessAwardPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.FlyingChessRecord = {
	presentor = "FlyingChessRecordPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.LiarsBarGameView = {
	presentor = "LiarsBarGamePresentor", showMode=ViewSetting.kFullScreenView,extId=-1
}

setting_view.LiarsBarGameBombView = {
	presentor = "LiarsBarGameBombPresentor", showMode=ViewSetting.kWindow,extId=-1
}


setting_view.LiarsBarGameGuideView = {
	presentor = "LiarsBarGameGuidePresentor", showMode=ViewSetting.kFullScreenView,extId=-1
}

setting_view.LiarsBarGameHallView = {
	presentor = "LiarsBarGameHallPresentor", showMode = ViewSetting.kModalView, noPadMask = true,hideHUD=true
}

setting_view.LiarsBarAwardView = {
	presentor = "LiarsBarAwardPresentor", showMode = ViewSetting.kModalWindow
}


setting_view.PuzzleBobbleSkillView = {
	presentor = "PuzzleBobbleSkillViewPresentor", showMode=ViewSetting.kModalWindow,extId=411
}

setting_view.ActivityLotteryView = {
	presentor = "ActivityLotteryPresentor", showMode=ViewSetting.kFullScreenView,extId=465
}

setting_view.ActLotteryNumberView = {
	presentor = "ActLotteryNumberPresentor", showMode=ViewSetting.kModalWindow,extId=-1
}

setting_view.RemoteCommandView = {
	presentor = "RemoteCommandPresentor", showMode=ViewSetting.kModalWindow,extId= 7, hideHUD=true
}

setting_view.LiarsBarGameSettlementView = {
	presentor = "LiarsBarGameSettlementPresentor", showMode=ViewSetting.kFullScreenView,extId=-1
}

setting_view.LiarsBarCreateRoomPanel = {
	presentor = "LiarsBarCreateRoomPanelPresentor", showMode = ViewSetting.kModalWindow,extId=-1
}

setting_view.ShareStreetCreateView = {
	presentor = "ShareStreetCreateViewPresentor", showMode=ViewSetting.kModalView,extId=93
}

setting_view.ShareStreetMapView = {
	presentor = "ShareStreetMapViewPresentor", showMode=ViewSetting.kFullScreenView,extId=93, supportBack=ViewSetting.BackBig
}

setting_view.ShareStreetInviteView = {
	presentor = "ShareStreetInviteViewPresentor", showMode=ViewSetting.kModalView,extId=93
}

setting_view.ShareStreetJoinView = {
	presentor = "ShareStreetJoinViewPresentor", showMode=ViewSetting.kModalView,extId=93
}

setting_view.ShareStreetRankView = {
	presentor = "ShareStreetRankViewPresentor", showMode=ViewSetting.kFullScreenView,extId=93, supportBack=ViewSetting.BackBig
}

setting_view.ShareStreetDetailView = {
	presentor = "ShareStreetDetailViewPresentor", showMode=ViewSetting.kModalView,extId=93, supportBack=ViewSetting.BackSmall
}

setting_view.ShareStreetInfoView = {
	presentor = "ShareStreetInfoViewPresentor", showMode=ViewSetting.kModalView,extId=93
}

setting_view.ShareStreetEditNameView = {
	presentor = "ShareStreetEditNameViewPresentor", showMode=ViewSetting.kModalView,extId=93
}

setting_view.ShareStreetEditDescView = {
	presentor = "ShareStreetEditDescViewPresentor", showMode=ViewSetting.kModalView,extId=93
}

setting_view.ShareStreetAddFurnitureView = {
	presentor = "ShareStreetAddFurnitureViewPresentor", showMode=ViewSetting.kModalView,extId=93, supportBack=ViewSetting.BackBig
}

setting_view.ShareStreetFurnitureView = {
	presentor = "ShareStreetFurnitureViewPresentor", showMode=ViewSetting.kModalView,extId=93, supportBack=ViewSetting.BackBig
}

setting_view.ShareStreetMemberView = {
	presentor = "ShareStreetMemberViewPresentor", showMode=ViewSetting.kModalView,extId=93
}

setting_view.TetrisAnniversaryActView = {
	presentor = "TetrisAnniversaryActViewPresentor", showMode=ViewSetting.kFullScreenView,extId=-1
}

setting_view.LimitRechargeView = {
	presentor = "LimitRechargePresentor", showMode=ViewSetting.kModalWindow, extId= 430
}

setting_view.GamePromote = {
	presentor = "GamePromotePresentor", showMode=ViewSetting.kFullScreenView,extId=469, supportBack=ViewSetting.BackBig
}
setting_view.GameRoomCommonNegativeRulePanel = {
	presentor = "GameRoomCommonNegativeRulePanelPresentor", showMode = ViewSetting.kModalWindow
}

setting_view.StreetPromotionView = {
	presentor = "StreetPromotionPresentor", showMode=ViewSetting.kNormalView, extId= 181
}

setting_view.GamePromoteTab = {
	presentor = "GamePromoteTabPresentor", showMode=ViewSetting.kNormalView, extId=469, supportBack=ViewSetting.BackBig
}

setting_view.ExploreGameClockView = {
	presentor = "ExploreGameClockViewPresentor", showMode=ViewSetting.kModalWindow, extId=467
}

setting_view.ExploreGameWordView = {
	presentor = "ExploreGameWordViewPresentor", showMode=ViewSetting.kModalWindow, extId=467
}

setting_view.ExploreGameDrawView = {
	presentor = "ExploreGameDrawViewPresentor", showMode=ViewSetting.kModalWindow, extId=467
}

setting_view.SiginBoardView = {
	presentor = "SiginBoardPresentor", showMode=ViewSetting.kFullScreenView, extId= 466
}

setting_view.SigninBoardDetailView = {
	presentor = "SigninBoardDetailPresentor", showMode=ViewSetting.kModalWindow, extId= 466
}

setting_view.ExploeGamePenBoxView = {
	presentor = "ExploeGamePenBoxViewPresentor", showMode=ViewSetting.kModalWindow, extId= 467
}

setting_view.ExploreGameActView = {
	presentor = "ExploreGameActViewPresentor", showMode=ViewSetting.kFullScreenView, extId= 467
}

setting_view.ExploreGameRewardView = {
	presentor = "ExploreGameRewardViewPresentor", showMode=ViewSetting.kModalWindow, extId= 467
}

setting_view.CarnivalCakeShareView = {
	presentor = "CarnivalCakeSharePresentor", showMode = ViewSetting.kWindow
}

return setting_view