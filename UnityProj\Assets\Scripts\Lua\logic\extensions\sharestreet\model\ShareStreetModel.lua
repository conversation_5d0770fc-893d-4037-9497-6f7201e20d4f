module("logic.extensions.sharestreet.model.ShareStreetModel",package.seeall)

local ShareStreetModel = class("ShareStreetModel")

function ShareStreetModel:ctor()
	self._userInfoData = {}
    self._isLikeData = {}
end

function ShareStreetModel:setUserInfo(streetInfo)
    if not streetInfo then
        printWarn("streetInfo is nil")
        return
    end
    local ownerId = streetInfo.ownerId
    if not self._userInfoData[ownerId] then
        self._userInfoData[ownerId] = ShareStreetInfo.New()
    end
    local info = self._userInfoData[ownerId]
    info:setInfo(streetInfo)

    for _, memberInfo in ipairs(info:getMemberList()) do
        self._userInfoData[memberInfo.roleInfo.id] = info
    end
    return info
end

function ShareStreetModel:setIsLike(ownerId, isLike)
    self._isLikeData[ownerId] = isLike
end

function ShareStreetModel:getIsLike(ownerId)
    return self._isLikeData[ownerId]
end

function ShareStreetModel:setHasInvited(bool)
    self._hasInvited = bool
end
---是否存在邀请
function ShareStreetModel:getHasInvited()
    return self._hasInvited
end

function ShareStreetModel:setHasApply(bool)
    self._hasApply = bool
end
---是否存在申请
function ShareStreetModel:getHasApply()
    return self._hasApply
end

function ShareStreetModel:setMyStreetId(str)
    self._myStreetId = str
end

function ShareStreetModel:getMyStreetId()
    return self._myStreetId
end

function ShareStreetModel:setModifyNameCount(count)
    self._modifyNameCount = count
end

function ShareStreetModel:getModifyNameCount()
    return self._modifyNameCount
end

function ShareStreetModel:hasCreatedStreet()
    return not string.nilorempty(self._myStreetId)
end

function ShareStreetModel:isStreetInfoInited()
    return self._myStreetId ~= nil
end

function ShareStreetModel:unlockArea(ownerId)
    self:getUserInfo(ownerId):unlockArea()
end
--- 获取玩家所在的街区信息 playerId可以是owner, 也可以是member
function ShareStreetModel:getUserInfo(playerId)
    playerId = playerId or UserInfo.userId
    return self._userInfoData[playerId]
end

function ShareStreetModel:setTakePhotoTime()
    self._takePhotoTime = ServerTime.now()
end

function ShareStreetModel:getTakePhotoTime()
    return self._takePhotoTime
end

ShareStreetModel.instance = ShareStreetModel.New()
return ShareStreetModel