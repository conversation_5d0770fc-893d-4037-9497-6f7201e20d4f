module("logic.extensions.activity.flyingchess.controller.state.FlyingChessPerformingGameState", package.seeall)

local FlyingChessPerformingGameState = class("FlyingChessPerformingGameState", GameStateBase)

function FlyingChessPerformingGameState:onEnter()
    self._unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
    self._curRoundRouteInfo = FlyingChessGameModel.instance.curRoundRouteInfo
    --短线重连没有回合数据，这里不播表现，等待其他端表现结束或者最大表现时间到了后端通知切游戏状态
    if self._curRoundRouteInfo == nil then
        return
    end
    self._startGridIdx = self._curRoundRouteInfo.routeStartGridIdx
    self._playerPosIdx = FlyingChessPosUtils.userTimes2PosIdx(self._curRoundRouteInfo.curUserTimes)
    self._isSelfPos = self._playerPosIdx == FlyingChessGameModel.instance:getSelfPosIdx()
    self._vehicleUnit = self._unitMgr:getUnit(SceneUnitType.FlyingChessVehicleUnit, self._playerPosIdx)
    local apearanceId = FlyingChessGameModel.instance:getApearanceId(self._playerPosIdx)
    self._animList = FlyingChessConfig.Vehicle2PlayerAnimList[apearanceId]
    
    --检查旧的叠棋表现
    local overlapChessIds = FlyingChessGameModel.instance:getOverlapChessIds(self._playerPosIdx, self._startGridIdx)
    if overlapChessIds then
        local len = #overlapChessIds
        local isShow = len > 1
        for k, v in ipairs(overlapChessIds) do
            local unit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPiece, v)
            unit:showChessCount(isShow, len)
            unit:setVisible(FlyingChessGameModel.instance:isOverlapAndShow(unit))
        end
    end

    
    local chessIds = self._curRoundRouteInfo.chessIds
    local gridIdx = FlyingChessGameModel.instance:getChessGridIdx(chessIds[1])
    overlapChessIds = FlyingChessGameModel.instance:getOverlapChessIds(self._playerPosIdx, gridIdx)
    local count = #overlapChessIds
    --是否跳过表现
    self._isSkipPerforming = gridIdx == FlyingChessPosUtils.flyingGridIdx
    self._units = {}
    for k, v in ipairs(chessIds) do
        local pos = FlyingChessGameController.instance:getChessPiecePos(self._playerPosIdx, v)
        local unit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPiece, v)
        unit:setPos(pos.x, pos.y, pos.z)
        --移动都是载具和熊模在移动，这里隐藏，跳过动画时不隐藏
        unit:setVisible(false)
        unit:showChessCount(count > 1, count)
        table.insert(self._units, unit)
    end
    --跳过动画,直接结束表演
    if self._isSkipPerforming then
        self:setMoveChessShow()
        self._vehicleUnit:appendInterval(0.5)
        self._vehicleUnit:appendCallback(function()
            FlyingChessGameController.instance:endPerforming()
        end)
    else
        self:doPerforming()
    end
end

function FlyingChessPerformingGameState:doPerforming()
    local routes = self._curRoundRouteInfo.routes
    local chessIds = self._curRoundRouteInfo.chessIds
    local userId = FlyingChessGameModel.instance:getUserIdByPosIdx(self._playerPosIdx)
    self._playerUnit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPlayerUnit, userId)
    self._vehicleUnit:playAnimation(self._animList[2][1], true, false, true, true)
    self._playerUnit.skinView:addTempClothes(FlyingChessConfig.posIdx2TempClothes[self._playerPosIdx])
    
    local actionId = FlyingChessGameModel.instance:getDelayEvent(self._playerPosIdx)
    local vehicleId = FlyingChessGameModel.instance:getApearanceId(self._playerPosIdx)
    if actionId == 5 then
        local aniName = FlyingChessConfig.eventId2PlayerAniNameMap[actionId][vehicleId]
        self._playerUnit:playAnimation(aniName, true, false, true, true)
    else
        self._playerUnit:playAnimation(self._animList[2][2], true, false, true, true)
    end
    self._playerUnit:playChessMove2HandEffect()
    local gridIdx = FlyingChessGameModel.instance:getChessGridIdx(chessIds[1])
    FlyingChessGameController.instance:setChessAimEffect(self._playerPosIdx, true, gridIdx)
    --设置移动开始位置
    local startPos = nil
    if self._startGridIdx == FlyingChessPosUtils.homeGridIdx then
        startPos = FlyingChessGameModel.instance:getChessHomePos(chessIds[1])
    else
        startPos = FlyingChessGameController.instance:getGridPos(self._playerPosIdx, FlyingChessPosUtils.getClientGridIdx(self._playerPosIdx, self._startGridIdx))
    end
    self._vehicleUnit:setPos(startPos.x, startPos.y)
    self._eventSceneObjIds = {}
    self._crashChessUnitMap = {}
    local len = #routes
    for i=1, len do
        local mo = routes[i]
        if mo.specialEventId ~= nil and mo.specialEventId ~= 0 then
            local sceneObjId = FlyingChessGameController.instance:getEventSceneId(mo.specialEventId, mo.startIdx)
            table.insert(self._eventSceneObjIds, sceneObjId)
            self._vehicleUnit:appendCallback(function()
                local ownerName = self._isSelfPos and lang("你") or FlyingChessConfig.posIdx2Str[self._playerPosIdx]
                local str2 = ""
                local str =  lang("{1}触发了{2}", ownerName, FlyingChessConfig.getEventConfig(mo.specialEventId).eventName)
                if mo.specialEventId == 4 then
                    str2 = lang("下回合无法移动")
                elseif mo.specialEventId == 5 then
                    str2 = lang("下回合投掷的点数×2")
                else
                    local footStr = mo.footCount > 0 and lang("前进") or lang("倒退")
                    str2 = lang("当前移动的棋子{1}{2}步", footStr, math.abs(mo.footCount))
                end
                SoundManager.instance:playEffect(142177)
                FlyingChessGameController.instance:localNotify(FlyingChessNotify.OnShowTips, str, str2)
            end)
            local params = {eventId = mo.specialEventId, startIdx = mo.startIdx, playerPosIdx = self._playerPosIdx}
            self._vehicleUnit:appendEventPerform(params)
            self._vehicleUnit:appendCallback(function()
                self._unitMgr:removeUnit(SceneUnitType.FlyingChessEventUnit, sceneObjId)
            end)
        elseif mo.crashChessId then
            local segment = 10
            local interval = 0.05
            local crashChessUnit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPiece, mo.crashChessId)
            if self._crashChessUnitMap[crashChessUnit] == nil then
                self._crashChessUnitMap[crashChessUnit] = true

                local startPos = crashChessUnit:getPos()
                local endPos = FlyingChessGameModel.instance:getChessHomePos(mo.crashChessId)
                if (startPos.x - endPos.x) ^ 2 + (startPos.y - endPos.y) ^ 2 < 2 ^ 2 then
                    segment = 4
                end
                self._vehicleUnit:appendCallback(function()
                    if crashChessUnit:getVisible() then
                        crashChessUnit.nameBar:showHitEffect()
                    end
                    
                    SoundManager.instance:playEffect(142175)
                end)
                --播放撞击效果的时间
                self._vehicleUnit:appendInterval(0.2)

                self._vehicleUnit:appendCallback(function()
                    crashChessUnit:showChessCount(false)
                    -- 
                    local topPos = (startPos + endPos) / 2
                    local tempY = startPos.y > endPos.y and startPos.y or endPos.y
                    topPos.y = tempY + 1
                    local poslist = self:_calculateBezierPath(startPos, topPos, endPos, segment)
                    crashChessUnit:appendMoveRoute(poslist, interval, true)
                    crashChessUnit:appendCallback(function()
                        crashChessUnit:stopAllState()
                        crashChessUnit:setVisible(true)
                        local homePos = FlyingChessGameModel.instance:getChessHomePos(crashChessUnit.info.id)
                        crashChessUnit:setPos(homePos.x, homePos.y, homePos.z)
                    end)
                end)
                self._vehicleUnit:appendInterval(segment*interval)
            end

        else
            if mo.moveType == FlyingChessMoveType.MOVE or mo.moveType == FlyingChessMoveType.EventChangePos then
                local routes = FlyingChessPosUtils.calcuteChessRoute(self._playerPosIdx, mo.startIdx, mo.endIdx)
                local poslist = {}
                for k, v in ipairs(routes) do
                    local pos = FlyingChessGameController.instance:getGridPos(self._playerPosIdx, v)
                    table.insert(poslist, pos)
                end
                local len2 = #poslist
                for j=1, len2 do
                    local count = len2 - j
                    --显示步数
                    self._vehicleUnit:appendCallback(function()
                        self._playerUnit.nameBar:showCountDown(count)
                    end)
                    self._vehicleUnit:appendSingleMoveRoute(poslist[j], 0.2)
                end
                self._vehicleUnit:appendCallback(function()
                    self._playerUnit.nameBar:showCountDown(0)
                end)
            else
                local pos = FlyingChessGameController.instance:getGridPos(self._playerPosIdx, FlyingChessPosUtils.getClientGridIdx(self._playerPosIdx, mo.endIdx))
                if mo.moveType == FlyingChessMoveType.FLYING then
                    self._vehicleUnit:appendCallback(function()
                        SoundManager.instance:playEffect(142176)
                    end)
                    self._vehicleUnit:appendSingleMoveRoute(pos, 0.8)
                elseif mo.moveType == FlyingChessMoveType.JUMP then
                    self._vehicleUnit:appendInterval(0.2)
                    -- self._vehicleUnit:appendCallback(function()
                    --     self._vehicleUnit:setPos(pos.x, pos.y, pos.z)
                    -- end)

                    local segment = 5
                    local interval = 0.05
                    local startPos =  FlyingChessGameController.instance:getGridPos(self._playerPosIdx, FlyingChessPosUtils.getClientGridIdx(self._playerPosIdx, mo.startIdx))
                    local topPos = (startPos + pos) / 2
                    local tempY = startPos.y > pos.y and startPos.y or pos.y
                    topPos.y = tempY + 1
                    local poslist = self:_calculateBezierPath(startPos, topPos, pos, segment)
                    self._vehicleUnit:appendCallback(function()
                        SoundManager.instance:playEffect(142176)
                    end)
                    self._vehicleUnit:appendMoveRoute(poslist, interval, true)
                    self._vehicleUnit:appendInterval(0.2)
                end
            end
        end
    end

    self._vehicleUnit:appendCallback(function()
        FlyingChessGameController.instance:setChessAimEffect(self._playerPosIdx, false)
        --放下棋子动画
        self._playerUnit:playAnimation(self._animList[3][2], false, false, true, true)
        self._vehicleUnit:playAnimation(self._animList[3][1], false, false, true, true)
    end)
    --熊模放下棋子动画时间
    self._vehicleUnit:appendInterval(1.4)

    -- self._vehicleUnit:appendCallback(function()
    --     self:recoverPlayerUnitState()
    -- end)
    -- self._vehicleUnit:appendInterval(0.1)
    self._vehicleUnit:appendCallback(function()
        self:setMoveChessShow()
        SoundManager.instance:playEffect(142174)
        for k, v in ipairs(self._units) do
            v:showAppearEffect()
            v:playAnimation("chuxian", false, false, true, true)
        end
    end)

    --飞回自己位置
    local startPos = FlyingChessGameController.instance:getChessPiecePos(self._playerPosIdx, chessIds[1])
    local originPos = FlyingChessGameController.instance:getPlayerPos(self._playerPosIdx)
    local topPos = (startPos + originPos) / 2
    local tempY = startPos.y > originPos.y and startPos.y or originPos.y
    topPos.y = tempY + 1
    local segment = 10
    if (startPos.x - originPos.x) ^ 2 + (startPos.y - originPos.y) ^ 2 < 2 ^ 2 then
        segment = 4
    end
    local poslist = self:_calculateBezierPath(startPos, topPos, originPos, segment)
    self._vehicleUnit:appendMoveRoute(poslist, 0.05, true)
    self._vehicleUnit:appendCallback(function()
        self:recoverPlayerUnitState()
    end)
    
    --到达终点
    if FlyingChessGameModel.instance:isArriveEnd(chessIds[1]) then
        self._vehicleUnit:appendInterval(0.2)
        self._vehicleUnit:appendCallback(function()
            SoundManager.instance:playEffect(142178)
            for k, v in ipairs(self._units) do
                v:showChessCount(false)
                v:setWinEffectIsShow(true)
                v:playAnimation("xiaoshi", false, false, true, true)
            end
        end)
        self._vehicleUnit:appendInterval(1)
        self._vehicleUnit:appendCallback(function()
            self:setMoveChessWin()
        end)
    end
    self._isPerformingCompelete = false
    self._vehicleUnit:appendCallback(function()
        self._isPerformingCompelete = true
        FlyingChessGameController.instance:endPerforming()
    end)
end


function FlyingChessPerformingGameState:_bezier(v1,v2,v3,t)
	local pos1 = v1 * (1-t) + v2 * t
	local pos2 = v2 * (1-t) + v3 * t
	local result = pos1 * (1-t) + pos2 * t
	return result
end

function FlyingChessPerformingGameState:_calculateBezierPath(v1,v2,v3,segment)
	local paths = {}
	for i = 1, segment do
		local t = i/segment
		local pos = self:_bezier(v1,v2,v3,t)
		table.insert(paths,pos)
	end
	return paths
end

--恢复显示
function FlyingChessPerformingGameState:setMoveChessShow()
    
    local chessIds = FlyingChessGameController.instance:getChessIdsByPlayerIdx(self._playerPosIdx)
    for k, v in ipairs(chessIds) do
        local unit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPiece, v)
        local gridIdx = FlyingChessGameModel.instance:getChessGridIdx(v)
        local overlapChessIds = FlyingChessGameModel.instance:getOverlapChessIds(self._playerPosIdx, gridIdx)
        local count = overlapChessIds ~= nil and #overlapChessIds or 1
        unit:showChessCount(count > 1, count)
        unit:setVisible(FlyingChessGameModel.instance:isOverlapAndShow(unit))
        local pos = FlyingChessGameController.instance:getChessPiecePos(self._playerPosIdx, unit.info.id)
        unit:setPos(pos.x, pos.y)
    end
end

function FlyingChessPerformingGameState:setMoveChessWin()
    local chessIds = self._curRoundRouteInfo.chessIds
    for i=1, #chessIds do
        local chessId = chessIds[i]
        local unit = self._unitMgr:getUnit(SceneUnitType.FlyingChessPiece, chessId)
        unit:setVisible(true)
        unit:setWinEffectIsShow(false)
        unit:setWinGoIsShow(true)
        local pos = FlyingChessGameModel.instance:getChessHomePos(chessId)
        unit:setPos(pos.x, pos.y, pos.z)
        unit:setSpineGoIsShow(false)
        unit:showChessCount(false)
        FlyingChessGameModel.instance:setArriveChess(chessId)
        FlyingChessGameModel.instance:setChessGridIdx(chessId, FlyingChessPosUtils.homeGridIdx)
    end
end

function FlyingChessPerformingGameState:recoverPlayerUnitState()
    --待机动画
    self._playerUnit:playAnimation(self._animList[1][2], true, false, true, true)
    self._vehicleUnit:playAnimation(self._animList[1][1], true, false, true, true)

    local chessIds = self._curRoundRouteInfo.chessIds
    self._playerUnit.nameBar:showCountDown(0)
    local originPos = FlyingChessGameController.instance:getPlayerPos(self._playerPosIdx)
    self._vehicleUnit:setPos(originPos.x, originPos.y, originPos.z)
    local clientPosIdx = FlyingChessGameModel.instance:getClientPosIdx(self._playerPosIdx) 
    local dir = FlyingChessPosUtils.clientPlayerIdx2Dir[clientPosIdx]
    self._playerUnit:setDirection(dir)
    self._vehicleUnit:setDirection(dir)
    self._playerUnit.skinView:removeTempClothes(FlyingChessConfig.posIdx2TempClothes[self._playerPosIdx])
end

function FlyingChessPerformingGameState:onExit()
    --短线重连没有回合数据，这里不播表现，等待其他端表现结束或者最大表现时间到了后端通知切游戏状态
    if self._curRoundRouteInfo ~= nil and not self._isSkipPerforming then
        --提前结束表现,这里需要把状态恢复
        if not self._isPerformingCompelete then
            self._vehicleUnit:stopAllState()
            self:recoverPlayerUnitState()
            self:setMoveChessShow()
            FlyingChessGameController.instance:setChessAimEffect(self._playerPosIdx, false)
            if FlyingChessGameModel.instance:isArriveEnd(self._curRoundRouteInfo.chessIds[1]) then
                self:setMoveChessWin()
            end
            for k,v in ipairs(self._eventSceneObjIds) do
                self._unitMgr:removeUnit(SceneUnitType.FlyingChessEventUnit, sceneObjId)
            end
            for k,v in pairs(self._crashChessUnitMap) do
                k:stopAllState()
                local homePos = FlyingChessGameModel.instance:getChessHomePos(k.info.id)
                k:setPos(homePos.x, homePos.y, homePos.z)
            end
        end
    end
end

return FlyingChessPerformingGameState