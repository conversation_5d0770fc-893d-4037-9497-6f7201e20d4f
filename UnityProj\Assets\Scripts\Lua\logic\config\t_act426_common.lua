-- {excel:426滚滚机.xlsx, sheetName:export_滚滚机杂项}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act426_common", package.seeall)

local title = {activityId=1,key=2,value=3}

local dataList = {
	{2019, "lotterySpentItem", "16000646:1"},
	{2019, "rewardItemId", "16000650"},
	{2019, "graduateNeedCount", "15000"},
	{2019, "graduateReward", "12005603:1"},
	{2019, "dailyFreeTimes", "0"},
	{2019, "guaranteeTimes", "3:10,2:5"},
	{2019, "dailyMaxTimes", "1000"},
	{2019, "buyTicketNeedItems", "2:60"},
	{2019, "patternTips", "600,300,200"},
	{2019, "giftIds", "504157,504158"},
	{2019, "groceryId", "318"},
	{2104, "lotterySpentItem", "16000677:1"},
	{2104, "rewardItemId", "16000678"},
	{2104, "graduateNeedCount", "15000"},
	{2104, "graduateReward", "12005830:1"},
	{2104, "dailyFreeTimes", "0"},
	{2104, "guaranteeTimes", "3:10,2:5"},
	{2104, "dailyMaxTimes", "1000"},
	{2104, "buyTicketNeedItems", "2:60"},
	{2104, "patternTips", "600,300,200"},
	{2104, "giftIds", "504176,504177"},
	{2104, "groceryId", "335"},
	{2146, "lotterySpentItem", "16000694:1"},
	{2146, "rewardItemId", "16000695"},
	{2146, "graduateNeedCount", "15000"},
	{2146, "graduateReward", "12005971:1"},
	{2146, "dailyFreeTimes", "0"},
	{2146, "guaranteeTimes", "3:10,2:5"},
	{2146, "dailyMaxTimes", "1000"},
	{2146, "buyTicketNeedItems", "2:60"},
	{2146, "patternTips", "600,300,200"},
	{2146, "giftIds", "504204,504205"},
	{2146, "groceryId", "341"},
	{2183, "lotterySpentItem", "16000699:1"},
	{2183, "rewardItemId", "16000700"},
	{2183, "graduateNeedCount", "15000"},
	{2183, "graduateReward", "12006100:1"},
	{2183, "dailyFreeTimes", "0"},
	{2183, "guaranteeTimes", "3:10,2:5"},
	{2183, "dailyMaxTimes", "1000"},
	{2183, "buyTicketNeedItems", "2:60"},
	{2183, "patternTips", "600,300,200"},
	{2183, "giftIds", "504214,504215"},
	{2183, "groceryId", "354"},
	{2189, "lotterySpentItem", "16000708:1"},
	{2189, "rewardItemId", "16000709"},
	{2189, "graduateNeedCount", "15000"},
	{2189, "graduateReward", "12006229:1"},
	{2189, "dailyFreeTimes", "0"},
	{2189, "guaranteeTimes", "3:10,2:5"},
	{2189, "dailyMaxTimes", "1000"},
	{2189, "buyTicketNeedItems", "2:60"},
	{2189, "patternTips", "600,300,200"},
	{2189, "giftIds", "504231,504232"},
	{2189, "groceryId", "357"},
	{2277, "lotterySpentItem", "16000741:1"},
	{2277, "rewardItemId", "16000742"},
	{2277, "graduateNeedCount", "15000"},
	{2277, "graduateReward", "12006491:1"},
	{2277, "dailyFreeTimes", "0"},
	{2277, "guaranteeTimes", "3:10,2:5"},
	{2277, "dailyMaxTimes", "1000"},
	{2277, "buyTicketNeedItems", "2:60"},
	{2277, "patternTips", "600,300,200"},
	{2277, "giftIds", "504268,504269"},
	{2277, "groceryId", "374"},
	{2312, "lotterySpentItem", "16000747:1"},
	{2312, "rewardItemId", "16000748"},
	{2312, "graduateNeedCount", "15000"},
	{2312, "graduateReward", "12006577:1"},
	{2312, "dailyFreeTimes", "0"},
	{2312, "guaranteeTimes", "3:10,2:5"},
	{2312, "dailyMaxTimes", "1000"},
	{2312, "buyTicketNeedItems", "2:60"},
	{2312, "patternTips", "600,300,200"},
	{2312, "giftIds", "504285,504286"},
	{2312, "groceryId", "386"},
	{2312, "previewIds", "12006451,12006450,12006452,12006449,12006447,12006448"},
	{2312, "specialGoods", "1813810"},
	{2355, "lotterySpentItem", "16000762:1"},
	{2355, "rewardItemId", "16000763"},
	{2355, "graduateNeedCount", "15000"},
	{2355, "graduateReward", "12006813:1"},
	{2355, "dailyFreeTimes", "0"},
	{2355, "guaranteeTimes", "3:10,2:5"},
	{2355, "dailyMaxTimes", "1000"},
	{2355, "buyTicketNeedItems", "2:60"},
	{2355, "patternTips", "600,300,200"},
	{2355, "giftIds", "504308,504309"},
	{2355, "groceryId", "394"},
	{2355, "specialGoods", "1813940"},
	{2496, "lotterySpentItem", "16000813:1"},
	{2496, "rewardItemId", "16000814"},
	{2496, "graduateNeedCount", "15000"},
	{2496, "graduateReward", "12007471:1"},
	{2496, "dailyFreeTimes", "0"},
	{2496, "guaranteeTimes", "3:10,2:5"},
	{2496, "dailyMaxTimes", "1000"},
	{2496, "buyTicketNeedItems", "2:60"},
	{2496, "patternTips", "600,300,200"},
	{2496, "giftIds", "504419,504420,504421"},
	{2496, "groceryId", "424"},
	{2496, "specialGoods", "1814540"},
}

local t_act426_common = {
	[2019] = {
		["lotterySpentItem"] = dataList[1],
		["rewardItemId"] = dataList[2],
		["graduateNeedCount"] = dataList[3],
		["graduateReward"] = dataList[4],
		["dailyFreeTimes"] = dataList[5],
		["guaranteeTimes"] = dataList[6],
		["dailyMaxTimes"] = dataList[7],
		["buyTicketNeedItems"] = dataList[8],
		["patternTips"] = dataList[9],
		["giftIds"] = dataList[10],
		["groceryId"] = dataList[11],
	},
	[2104] = {
		["lotterySpentItem"] = dataList[12],
		["rewardItemId"] = dataList[13],
		["graduateNeedCount"] = dataList[14],
		["graduateReward"] = dataList[15],
		["dailyFreeTimes"] = dataList[16],
		["guaranteeTimes"] = dataList[17],
		["dailyMaxTimes"] = dataList[18],
		["buyTicketNeedItems"] = dataList[19],
		["patternTips"] = dataList[20],
		["giftIds"] = dataList[21],
		["groceryId"] = dataList[22],
	},
	[2146] = {
		["lotterySpentItem"] = dataList[23],
		["rewardItemId"] = dataList[24],
		["graduateNeedCount"] = dataList[25],
		["graduateReward"] = dataList[26],
		["dailyFreeTimes"] = dataList[27],
		["guaranteeTimes"] = dataList[28],
		["dailyMaxTimes"] = dataList[29],
		["buyTicketNeedItems"] = dataList[30],
		["patternTips"] = dataList[31],
		["giftIds"] = dataList[32],
		["groceryId"] = dataList[33],
	},
	[2183] = {
		["lotterySpentItem"] = dataList[34],
		["rewardItemId"] = dataList[35],
		["graduateNeedCount"] = dataList[36],
		["graduateReward"] = dataList[37],
		["dailyFreeTimes"] = dataList[38],
		["guaranteeTimes"] = dataList[39],
		["dailyMaxTimes"] = dataList[40],
		["buyTicketNeedItems"] = dataList[41],
		["patternTips"] = dataList[42],
		["giftIds"] = dataList[43],
		["groceryId"] = dataList[44],
	},
	[2189] = {
		["lotterySpentItem"] = dataList[45],
		["rewardItemId"] = dataList[46],
		["graduateNeedCount"] = dataList[47],
		["graduateReward"] = dataList[48],
		["dailyFreeTimes"] = dataList[49],
		["guaranteeTimes"] = dataList[50],
		["dailyMaxTimes"] = dataList[51],
		["buyTicketNeedItems"] = dataList[52],
		["patternTips"] = dataList[53],
		["giftIds"] = dataList[54],
		["groceryId"] = dataList[55],
	},
	[2277] = {
		["lotterySpentItem"] = dataList[56],
		["rewardItemId"] = dataList[57],
		["graduateNeedCount"] = dataList[58],
		["graduateReward"] = dataList[59],
		["dailyFreeTimes"] = dataList[60],
		["guaranteeTimes"] = dataList[61],
		["dailyMaxTimes"] = dataList[62],
		["buyTicketNeedItems"] = dataList[63],
		["patternTips"] = dataList[64],
		["giftIds"] = dataList[65],
		["groceryId"] = dataList[66],
	},
	[2312] = {
		["lotterySpentItem"] = dataList[67],
		["rewardItemId"] = dataList[68],
		["graduateNeedCount"] = dataList[69],
		["graduateReward"] = dataList[70],
		["dailyFreeTimes"] = dataList[71],
		["guaranteeTimes"] = dataList[72],
		["dailyMaxTimes"] = dataList[73],
		["buyTicketNeedItems"] = dataList[74],
		["patternTips"] = dataList[75],
		["giftIds"] = dataList[76],
		["groceryId"] = dataList[77],
		["previewIds"] = dataList[78],
		["specialGoods"] = dataList[79],
	},
	[2355] = {
		["lotterySpentItem"] = dataList[80],
		["rewardItemId"] = dataList[81],
		["graduateNeedCount"] = dataList[82],
		["graduateReward"] = dataList[83],
		["dailyFreeTimes"] = dataList[84],
		["guaranteeTimes"] = dataList[85],
		["dailyMaxTimes"] = dataList[86],
		["buyTicketNeedItems"] = dataList[87],
		["patternTips"] = dataList[88],
		["giftIds"] = dataList[89],
		["groceryId"] = dataList[90],
		["specialGoods"] = dataList[91],
	},
	[2496] = {
		["lotterySpentItem"] = dataList[92],
		["rewardItemId"] = dataList[93],
		["graduateNeedCount"] = dataList[94],
		["graduateReward"] = dataList[95],
		["dailyFreeTimes"] = dataList[96],
		["guaranteeTimes"] = dataList[97],
		["dailyMaxTimes"] = dataList[98],
		["buyTicketNeedItems"] = dataList[99],
		["patternTips"] = dataList[100],
		["giftIds"] = dataList[101],
		["groceryId"] = dataList[102],
		["specialGoods"] = dataList[103],
	},
}

t_act426_common.dataList = dataList
local mt
if Act426CommonDefine then
	mt = {
		__cname =  "Act426CommonDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act426CommonDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act426_common