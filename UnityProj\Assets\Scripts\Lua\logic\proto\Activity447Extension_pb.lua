-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity447Extension_pb", package.seeall)


local tb = {}
GETACT447INFOREQUEST_MSG = protobuf.Descriptor()
tb.GETACT447INFOREQUEST_ACTID_FIELD = protobuf.FieldDescriptor()
GETACT447INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD = protobuf.FieldDescriptor()
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD = protobuf.FieldDescriptor()
GETACT447REWARDREPLY_MSG = protobuf.Descriptor()
tb.GETACT447REWARDREPLY_CHANGEID_FIELD = protobuf.FieldDescriptor()
GETACT447REWARDREQUEST_MSG = protobuf.Descriptor()
tb.GETACT447REWARDREQUEST_ACTID_FIELD = protobuf.FieldDescriptor()

tb.GETACT447INFOREQUEST_ACTID_FIELD.name = "actId"
tb.GETACT447INFOREQUEST_ACTID_FIELD.full_name = ".GetAct447InfoRequest.actId"
tb.GETACT447INFOREQUEST_ACTID_FIELD.number = 1
tb.GETACT447INFOREQUEST_ACTID_FIELD.index = 0
tb.GETACT447INFOREQUEST_ACTID_FIELD.label = 1
tb.GETACT447INFOREQUEST_ACTID_FIELD.has_default_value = false
tb.GETACT447INFOREQUEST_ACTID_FIELD.default_value = 0
tb.GETACT447INFOREQUEST_ACTID_FIELD.type = 5
tb.GETACT447INFOREQUEST_ACTID_FIELD.cpp_type = 1

GETACT447INFOREQUEST_MSG.name = "GetAct447InfoRequest"
GETACT447INFOREQUEST_MSG.full_name = ".GetAct447InfoRequest"
GETACT447INFOREQUEST_MSG.filename = "Activity447Extension"
GETACT447INFOREQUEST_MSG.nested_types = {}
GETACT447INFOREQUEST_MSG.enum_types = {}
GETACT447INFOREQUEST_MSG.fields = {tb.GETACT447INFOREQUEST_ACTID_FIELD}
GETACT447INFOREQUEST_MSG.is_extendable = false
GETACT447INFOREQUEST_MSG.extensions = {}
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.name = "hasGetRewards"
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.full_name = ".GetAct447InfoReply.hasGetRewards"
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.number = 1
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.index = 0
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.label = 3
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.has_default_value = false
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.default_value = {}
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.type = 5
tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD.cpp_type = 1

tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.name = "lastGetTime"
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.full_name = ".GetAct447InfoReply.lastGetTime"
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.number = 2
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.index = 1
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.label = 1
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.has_default_value = false
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.default_value = 0
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.type = 5
tb.GETACT447INFOREPLY_LASTGETTIME_FIELD.cpp_type = 1

GETACT447INFOREPLY_MSG.name = "GetAct447InfoReply"
GETACT447INFOREPLY_MSG.full_name = ".GetAct447InfoReply"
GETACT447INFOREPLY_MSG.filename = "Activity447Extension"
GETACT447INFOREPLY_MSG.nested_types = {}
GETACT447INFOREPLY_MSG.enum_types = {}
GETACT447INFOREPLY_MSG.fields = {tb.GETACT447INFOREPLY_HASGETREWARDS_FIELD, tb.GETACT447INFOREPLY_LASTGETTIME_FIELD}
GETACT447INFOREPLY_MSG.is_extendable = false
GETACT447INFOREPLY_MSG.extensions = {}
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.name = "changeId"
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.full_name = ".GetAct447RewardReply.changeId"
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.number = 1
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.index = 0
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.label = 1
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.has_default_value = false
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.default_value = 0
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.type = 5
tb.GETACT447REWARDREPLY_CHANGEID_FIELD.cpp_type = 1

GETACT447REWARDREPLY_MSG.name = "GetAct447RewardReply"
GETACT447REWARDREPLY_MSG.full_name = ".GetAct447RewardReply"
GETACT447REWARDREPLY_MSG.filename = "Activity447Extension"
GETACT447REWARDREPLY_MSG.nested_types = {}
GETACT447REWARDREPLY_MSG.enum_types = {}
GETACT447REWARDREPLY_MSG.fields = {tb.GETACT447REWARDREPLY_CHANGEID_FIELD}
GETACT447REWARDREPLY_MSG.is_extendable = false
GETACT447REWARDREPLY_MSG.extensions = {}
tb.GETACT447REWARDREQUEST_ACTID_FIELD.name = "actId"
tb.GETACT447REWARDREQUEST_ACTID_FIELD.full_name = ".GetAct447RewardRequest.actId"
tb.GETACT447REWARDREQUEST_ACTID_FIELD.number = 1
tb.GETACT447REWARDREQUEST_ACTID_FIELD.index = 0
tb.GETACT447REWARDREQUEST_ACTID_FIELD.label = 1
tb.GETACT447REWARDREQUEST_ACTID_FIELD.has_default_value = false
tb.GETACT447REWARDREQUEST_ACTID_FIELD.default_value = 0
tb.GETACT447REWARDREQUEST_ACTID_FIELD.type = 5
tb.GETACT447REWARDREQUEST_ACTID_FIELD.cpp_type = 1

GETACT447REWARDREQUEST_MSG.name = "GetAct447RewardRequest"
GETACT447REWARDREQUEST_MSG.full_name = ".GetAct447RewardRequest"
GETACT447REWARDREQUEST_MSG.filename = "Activity447Extension"
GETACT447REWARDREQUEST_MSG.nested_types = {}
GETACT447REWARDREQUEST_MSG.enum_types = {}
GETACT447REWARDREQUEST_MSG.fields = {tb.GETACT447REWARDREQUEST_ACTID_FIELD}
GETACT447REWARDREQUEST_MSG.is_extendable = false
GETACT447REWARDREQUEST_MSG.extensions = {}

GetAct447InfoReply = protobuf.Message(GETACT447INFOREPLY_MSG)
GetAct447InfoRequest = protobuf.Message(GETACT447INFOREQUEST_MSG)
GetAct447RewardReply = protobuf.Message(GETACT447REWARDREPLY_MSG)
GetAct447RewardRequest = protobuf.Message(GETACT447REWARDREQUEST_MSG)

return _G["logic.proto.Activity447Extension_pb"]
