module("logic.extensions.tetris.TetrisController", package.seeall)
---@class TetrisController
local TetrisController = class("TetrisController", CommonSceneGameControllerBase)

function TetrisController:onInit()
	self:registerNotify(GlobalNotify.getActivityInfoFinish, self._activityInfoUpdate, self)
	self:registerNotify(GlobalNotify.onServerPushActivity, self._activityInfoUpdate, self)
	self:registerNotify(GlobalNotify.OpenScene, self._openScene, self)
end

function TetrisController:onReset()
	self:unregisterNotify(GlobalNotify.getActivityInfoFinish, self._activityInfoUpdate, self)
	self:unregisterNotify(GlobalNotify.onServerPushActivity, self._activityInfoUpdate, self)
	self:unregisterNotify(GlobalNotify.OpenScene, self._openScene, self)
	self._openResultHandle = nil
end

--五点
function TetrisController:_activityInfoUpdate()
	SceneTimer:setTimer(
		3,
		function()
			TetrisModel.instance:checkIsFirstClick()
		end,
		nil,
		false
	)
end

--键盘按键
function TetrisController:setDebug()
	self._isDebug = not self._isDebug
	self:setDebugListener()
	printInfo("俄罗斯，self._isDebug：" .. tostring(self._isDebug))
end
--debug侦听
function TetrisController:setDebugListener()
	UpdateBeat:Remove(self._update, self)
	local sceneId = SceneManager.instance:getCurSceneId()
	if self._isDebug and sceneId and sceneId == 61 then
		UpdateBeat:Add(self._update, self)
	end
end

--1.房间数据创建完成
function TetrisController:onPreloadFinished()
	self._gameStarted = false
	self._gameOver = false
	self._isDead = false
	self._setDisSync = false
	self._isSendResult = false
	self._isQuickinkGame = false
	self._pauseGame = false
	self._curPauseTime = nil
	self._totalPauseTime = 0
	self:_setGameProcedure(TetrisNotify.gameProcedure.prepare)
	self._prepareOperations = {
		["openViewOperation"] = false,
		["createPoolOperation"] = false,
		["createGame"] = false
	}
	GlobalDispatcher:addListener(GlobalNotify.OnApplicationResume, self._onApplicationResume, self)
	GlobalDispatcher:addListener(GlobalNotify.OnApplicationPause, self._onApplicationPause, self)
	self:registerGameCentreNotify()
	local players = GameCentreModel.instance:getAllPlayer()
	local scene = SceneManager.instance:getCurScene()
	for userId, playerInfo in pairs(players) do
		if not playerInfo.roleLeave then
			local player = scene:getUnitMgr():addUnit(SceneUnitType.Player, playerInfo)
			if userId == UserInfo.userId then
				scene:setUserPlayer(player)
			end
		end
	end
	self:_prepareGame()
end
--1.1注册：游戏状态变更
function TetrisController:registerGameCentreNotify()
	local gameCentre = GameCentreController.instance
	gameCentre:registerLocalNotify(GameRoomDefine.Notify_OnGameStateChanged, self.updateRoomState, self)
end
--1.2移除注册：游戏状态变更
function TetrisController:unregisterGameCentreNotify()
	local gameCentre = GameCentreController.instance
	gameCentre:unregisterLocalNotify(GameRoomDefine.Notify_OnGameStateChanged, self.updateRoomState, self)
end
--1.3游戏玩法准备
function TetrisController:_prepareGame()
	GlobalDispatcher:addListener(TetrisNotify.openViewOperation, self._checkPrepareFinish, self)
	TetrisConfig.initConfigs()
	self:_openGameView()
end

--2.设置游戏数据：游戏砖块数据
function TetrisController:onTetrisShapeData(tetrisInfo)
	TetrisModel.instance:setShapeInfos(tetrisInfo)
end

--3.准备进入游戏场景,还没有进入
function TetrisController:onEnterSceneFinished(sceneId, bornX, bornZ)
	CommonHUDFacade.instance:hideHUD("Tetris")
	local cameraCtrl = SceneManager.instance:getCurScene():getCameraCtrl()
	cameraCtrl:setFollower(nil)
	cameraCtrl:setPos(0, 0)
	self.enterScenefinished = true
	local roomState = GameCentreModel.instance:getRoomState()
	self:refreshGameState(roomState)
	self:registerNotify(GlobalNotify.OnDisconnected, self._onDisconnected, self)
	self:registerNotify(GlobalNotify.ReConnectFinish, self._reConnectFinish, self)

	self:setDebugListener()
	SoundManager.instance:stopBGM()
end

--已经进入场景
function TetrisController:_openScene()
	local sceneId = SceneManager.instance:getCurSceneId()
	if sceneId == TetrisConfig.getWaitSceneId() and self._openResultHandle then
		self._openResultHandle()
		self._openResultHandle = nil
	end
	if sceneId ~= TetrisConfig.getMapSceneId() then
		return
	end
	self:openResultView()
end

--4.游戏房间状态变更
function TetrisController:updateRoomState(state, oldState)
	self:refreshGameState(state, oldState)
end
--5.刷新游戏房间状态
function TetrisController:refreshGameState(state, oldState)
	printInfo("LumberJackController:refreshGameState(state, oldState)", tostring(state), tostring(oldState))
	if not self.enterScenefinished or self._gameStarted then
		return
	end
	if state == GameEnum.GameRoomSystemStateEnum.TETRIS_START then
		self:tryStartGame()
	end
end

--6.检车是否可以开始游戏
function TetrisController:tryStartGame()
	TetrisGameViewModel.instance:reset()
	TetrisModel.instance:setResultInfo(nil)
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if isStandalone then
		local levelId = TetrisUIModel.instance:getNowLevelId()
		if levelId then
			local isPass = TetrisUIController.instance:isPassLevel(levelId)
			local levelCfg = TetrisConfig.getLevelConfigById(levelId)
			if levelCfg.helpName and not isPass then
				self:setGamePause(true)
				ViewMgr.instance:open(
					"HelpView",
					levelCfg.helpName,
					nil,
					function()
						self:setGamePause(false)
						self:onGameStart()
					end
				)
			else
				self:onGameStart()
			end
		else
			self:onGameStart()
		end
	else
		self:onGameStart()
	end
end

--7.开始游戏，进入游戏开始倒计时
function TetrisController:onGameStart()
	--- todo Check FirstTime
	local gameStartTime = math.floor(GameCentreModel.instance:getCurRoomStateChangeTime())
	local curRoomTime = math.floor(GameCentreController.instance:getRoomTime())
	local countDown = 5000
	local gameTypeDefine = GameRoomConfig.getGameRoomeDefine(GameEnum.GameRoomType.TETRIS)
	if gameTypeDefine then
		countDown = gameTypeDefine:getCfg().countDown * 1000
	end
	local costTime = curRoomTime - gameStartTime
	if costTime >= countDown then
		self:onCountDownFinished()
		return
	end
	local remainTime = math.ceil((countDown - costTime) / 1000)
	if remainTime >= 1 then
		ViewMgr.instance:open("ColaHelpPanel", handler(self.onCountDownFinished, self), remainTime, {2.5, -233.2, 0})
	end
end
--7.1在游戏开始倒计时的时候后台了
function TetrisController:applicationResume()
	if self._gameStarted then
		return
	end
	--ViewMgr.instance:close("ColaHelpPanel")
	self:onGameStart()
end

--8.游戏开始倒计时结束，创建游戏
function TetrisController:onCountDownFinished()
	if self._gameStarted then
		return
	end
	self._gameStarted = true
	ViewMgr.instance:close("ColaHelpPanel")
	-- 游戏实际开始了才驱动世界逻辑
	self:_setGameProcedure(TetrisNotify.gameProcedure.playing)
	self:_createGame()
	SoundManager.instance:playBGM(120096)
end

--9.检测房间数据，游戏数据，游戏所需资源是否准备齐全
function TetrisController:_checkPrepareFinish(key)
	if key == "openViewOperation" then
		self:_createPool(
			function()
				self:_checkPrepareFinish("createPoolOperation")
			end
		)
	end
	self._prepareOperations[key] = true
	local isFinish = true
	for k, v in pairs(self._prepareOperations) do
		if not v then
			isFinish = false
			break
		end
	end
	if isFinish then
		self:_prepareFinish()
	end
end
--9.1所有需要准备完成
function TetrisController:_prepareFinish()
	self:_startGame()
	GlobalDispatcher:removeListener(TetrisNotify.openViewOperation, self._checkPrepareFinish, self)
	self._prepareOperations = nil
end

--10.所有需要准备完成，可以开始体验小游戏
function TetrisController:_startGame()
	TetrisModel.instance:startGame()
	for i = 1, #self._gameMgrList do
		self._gameMgrList[i]:startGame()
	end
end

--10.1游戏操作帧
function TetrisController:_update()
	if self._pauseGame then
		return
	end
	if self._gameMgrList then
		self._gameMgrList[1]:update(self._setDisSync)
	end
end
--10.2游戏逻辑帧
function TetrisController:_lateUpdate()
	if self._pauseGame then
		return
	end
	if self._gameMgrList then
		self._gameMgrList[1]:lateUpdate(self._setDisSync)
	end
end
--10.3给自己上个debuff
function TetrisController:punish()
	if self._gameMgrList then
		self._gameMgrList[1]:punish()
	end
end

--11.是否死亡
function TetrisController:setIsDead(isDead)
	if self._isDead and isDead then
		return
	end
	self._isDead = isDead
	if self._gameMgrList then
		for i = 1, #self._gameMgrList do
			self._gameMgrList[i]:setIsDead(isDead)
		end
	end
	if self._isDead then
		printInfo("精灵碰碰死亡", debug.traceback())
		self:_excuteDead()
	end
end
--11.1执行死亡
function TetrisController:_excuteDead()
	TetrisModel.instance:setCurScore(self._gameMgrList[1]:getCurScore())
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if isStandalone then
		local score = self._gameMgrList[1]:getCurScore()
		self._isSendResult = true
		LoadingMask.instance:show()
		Activity278Agent.instance:sendUpdateTetrisStateRequest(
			true,
			nil,
			nil,
			score,
			nil,
			nil,
			nil,
			function(msg)
				LoadingMask.instance:close()
				local info = {}
				info.isStandalone = true
				info.isDead = msg.isDead
				info.score = msg.score
				info.gameOver = msg.gameOver
				info.changeId = msg.changeId
				info.newScore = msg.newScore
				info.levelId = msg.levelId
				TetrisModel.instance:setResultInfo(info)
				TetrisController.instance:setGameOver(true, info)
			end
		)
	end
end

--12.是否游戏结束
function TetrisController:setGameOver(isGameOver, info)
	if SceneManager.instance:isEnetering() then
		return
	end

	local sceneId = SceneManager.instance:getCurSceneId()
	if sceneId ~= TetrisConfig.getMapSceneId() then
		self:openResultView()
		return
	end
	if self._gameOver then
		return
	end
	printInfo("精灵碰碰正常游戏结束 : " .. tostring(isGameOver))
	self._gameOver = isGameOver
	if isGameOver then
		ViewMgr.instance:close("ColaHelpPanel")
		removetimer(self.openResultView, self)
		settimer(2, self.openResultView, self, false)
		self:_setGameProcedure(TetrisNotify.gameProcedure.gameOver)
	end
end

--13.不玩了，退出游戏、退出场景
function TetrisController:quickGame(roomType)
	if self._isQuickinkGame then
		return
	end
	self._isQuickinkGame = true
	removetimer(self.openResultView, self)
	GlobalDispatcher:removeListener(TetrisNotify.openViewOperation, self._checkPrepareFinish, self)

	if roomType == GameEnum.GameRoomCreateType.CUSTOM then
		self._openResultHandle = function()
			TetrisController.instance:openResultView()
		end
		GameRoomController.instance:backToWaitingRoom(9)
	else
		TetrisGameViewModel.instance:reset()
		-- SceneManager.instance:loadScene(lastScene,nil,nil,nil,quickFinishCb)
		GameCentreModel.instance:clearData()
		local mapId = GameCentreModel.instance:getMapId()
		local lastScene = TetrisModel.instance:getLastSceneId()
		local quickFinishCb = function()
			local actIsOpen = ActivityFacade.instance:getActivityIsOpen(GameEnum.ActivityEnum.ACTIVITY_278)
			if not actIsOpen then
				return
			end
			TetrisController.instance:openActView(false, mapId)
		end
		GameRoomController.instance:tryQuitGame(GameEnum.GameRoomType.TETRIS, nil, nil, lastScene, quickFinishCb)
	end
end

-- 退出场景时的处理回调
function TetrisController:onExitScene()
	self._prepareOperations = nil
	self._pauseRealTime = nil
	self:unregisterNotify(GlobalNotify.OnDisconnected, self._onDisconnected, self)
	self:unregisterNotify(GlobalNotify.ReConnectFinish, self._reConnectFinish, self)
	LateUpdateBeat:Remove(self._lateUpdate, self)
	UpdateBeat:Remove(self._update, self)
	if self._gameMgrList then
		for i = 1, #self._gameMgrList do
			self._gameMgrList[i]:disposeGame()
		end
		self._gameMgrList = nil
	end
	self:_setGameProcedure(TetrisNotify.gameProcedure.exitScene)
	removetimer(self.openResultView, self)
	TetrisModel.instance:disposeGame()
	CommonHUDFacade.instance:showHUD("Tetris")
	ViewMgr.instance:close("ColaHelpPanel")
	ViewMgr.instance:close("TetrisGameView")
	ViewMgr.instance:clearBackStack()
	-- ViewMgr.instance:closeAllModalViews()
	if self._pool then
		self._pool:disposePool()
		self._pool = nil
	end
	self:unregisterGameCentreNotify()
	SoundManager.instance:playSceneBGM()
	GlobalDispatcher:removeListener(GlobalNotify.OnApplicationResume, self._onApplicationResume, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnApplicationPause, self._onApplicationPause, self)
end

-- 处理玩家提前退出游戏过程中收到结算推送
function TetrisController:checkGameOverPushLegally()
	local sceneId = SceneManager.instance:getCurSceneId()
	if sceneId ~= TetrisConfig.getMapSceneId() then
		return
	end
	if SceneManager.instance:isEnetering() then
		return
	end

	--local curRoomId = GameCentreModel.instance:getRoomId()
	--if curRoomId == 0 then
	--print("LumberJackController:onLumberJackGameTreePush Failed !! -=>> curRoom:" .. curRoomId)
	--return
	--end

	return true
end

--活动是否已经开启
function TetrisController:checkActivityIsOpen(showTips)
	local isOpen = ActivityModel.instance:getActivityIsShow(GameEnum.ActivityEnum.ACTIVITY_278)
	if not isOpen and showTips then
		FlyTextManager.instance:showFlyText(lang("抱歉，活动未开启。"))
	end
	return isOpen
end

--设置小游戏玩法流程
function TetrisController:_setGameProcedure(procedure)
	if self.gameProcedure == procedure then
		return
	end
	self.gameProcedure = procedure
	GlobalDispatcher:dispatch(TetrisNotify.gameProcedureChange, procedure)
end

--设置gameview,游戏未开始前就会设置
function TetrisController:setGameView(gameView)
	self._gameView = gameView
end

--暂停游戏
function TetrisController:setGamePause(pauseState)
	if not self._gameStarted then
		return
	end
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if not isStandalone then
		return
	end
	if pauseState then
		self._curPauseTime = Time.realtimeSinceStartup
	else
		if self._curPauseTime then
			local curTime = Time.realtimeSinceStartup
			self._totalPauseTime = self._totalPauseTime + curTime - self._curPauseTime
			self._curPauseTime = nil
		end
	end
	self._pauseGame = pauseState
end

function TetrisController:isPausingGame()
	return self._pauseGame
end

--是否游戏结束
function TetrisController:isGameOver()
	return self._gameOver
end

--玩家自身是否死亡
function TetrisController:isDead()
	return self._isDead
end

--房间游戏是否可以开始
function TetrisController:isGameStarted()
	return self._gameStarted
end

--游戏进行的总时间
function TetrisController:getRoomSpentTime()
	local offsetGameTimer = GameCentreController.instance:getRoomTime() - GameCentreModel.instance:getGameStartTime()
	local countDown = 5000
	local gameTypeDefine = GameRoomConfig.getGameRoomeDefine(GameEnum.GameRoomType.TETRIS)
	if gameTypeDefine then
		countDown = gameTypeDefine:getCfg().countDown * 1000
	end
	return GameCentreModel.instance:getGameStartTime() + offsetGameTimer - countDown
end

--整局下来暂停的总时间
function TetrisController:getPauseSpendTime()
	return self._totalPauseTime
end

--获取当前运动的砖块
function TetrisController:getCurShape()
	if self._gameMgrList then
		return self._gameMgrList[1]:getCurShape()
	end
end

--获取当前运动的技能
function TetrisController:getSkillComp()
	if self._gameMgrList and self._gameMgrList[1] then
		return self._gameMgrList[1]:getSkillComp()
	end
end

--以下方法需要在gameView初始化之后才能调用
--获取砖块出生点
function TetrisController:getBornPoint()
	return self._gameView._bornPoint
end
--获取展示点位置
function TetrisController:getDisplayPoint()
	return self._gameView._displayPoint
end
--获取砖块最终落下位置
function TetrisController:getFinalCoordShape()
	return self._gameView._finalCoordShape
end
--获取砖块最终落下位置
function TetrisController:getShapeParent()
	return self._gameView._shapeParent
end
--获取展示区惩罚点
function TetrisController:getDisplayPunishPoint()
	return self._gameView._displayPunishPoint
end
--获取玩家区惩罚点
function TetrisController:getPlayerPunishPoint()
	return self._gameView._playerPunishPoint
end

------------------- 游戏界面 -------------------
--打开游戏面板
function TetrisController:_openGameView()
	ViewMgr.instance:clearBackStack()
	ViewMgr.instance:open("TetrisGameView")
end

--打开活动界面
function TetrisController:openActView(isShowTips, mapId)
	if not self:checkActivityIsOpen(isShowTips) then
		return
	end
	local info = {}
	info.mapId = mapId
	TetrisUIController.instance:openEntry()
end

--打开结算界面
function TetrisController:openResultView()
	if SceneManager.instance:isEnetering() then
		return
	end
	local info = TetrisModel.instance:getResultInfo()
	if info == nil then
		return
	end
	self._gameOver = true
	ViewMgr.instance:close("ColaHelpPanel")
	local isStandalone = info.isStandalone
	if isStandalone then
		self:tryPlayEndingStory(
			function()
				ViewMgr.instance:open("TetrisGameResultView", info)
			end
		)
	else
		local score = TetrisModel.instance:getCurScore()
		local endType = info.endType
		local winType = info.winType
		info.score = score
		local sceneId = SceneManager.instance:getCurSceneId()
		local isInGameScene = sceneId == TetrisConfig.getMapSceneId()
		if winType == 1 then
			SoundManager.instance:playEffect(141423)
		else
			SoundManager.instance:playEffect(141421)
		end

		if endType == GameEnum.Act278GameEndType.NORMAL then
			ViewMgr.instance:open("TetrisGameResultView", info)
			return
		elseif endType == GameEnum.Act278GameEndType.TIMEOUT then
			if winType == 1 then
				FlyTextManager.instance:showFlyText(lang("俄罗斯方块对方超时断开"))
			else
				FlyTextManager.instance:showFlyText(lang("俄罗斯方块自己超时断开"))
			end
			ViewMgr.instance:open("TetrisGameResultView", info)
			return
		elseif endType == GameEnum.Act278GameEndType.DISCONNECT then
			FlyTextManager.instance:showFlyText(lang("疯狂伐木工匹配异常结束类型2"))
			ViewMgr.instance:open("TetrisGameResultView", info)
			return
		elseif endType == GameEnum.Act278GameEndType.GIVE_UP then
			if winType == 1 then
				FlyTextManager.instance:showFlyText(lang("俄罗斯方块对方中途退出"))
			end
			ViewMgr.instance:open("TetrisGameResultView", info)
			return
		elseif endType == GameEnum.Act278GameEndType.ACTIVITY_END then
			if isInGameScene then
				local msg = lang("俄罗斯方块结算时活动结束")
				DialogHelper.showMsg(msg, handler(self.quickGame, self), "退出游戏")
			end
			return
		else
			if isInGameScene then
				local msg = lang("疯狂伐木工匹配异常结束类型3")
				DialogHelper.showMsg(msg, handler(self.quickGame, self), "退出游戏")
			end
			return
		end
	end
end

function TetrisController:tryExitCurGame()
	self:quickGame()
end

function TetrisController:tryPlayEndingStory(handler)
	local levelId = TetrisUIModel.instance:getNowLevelId()
	local isPass = TetrisUIController.instance:isPassLevel(levelId)
	if isPass then
		local nowLevelConfig = TetrisConfig.getLevelConfigById(levelId)
		if nowLevelConfig and nowLevelConfig.endingStoryId and nowLevelConfig.endingStoryId > 0 then
			local re, cmd = TaskCmdFacade.instance:tryStartCmd(nowLevelConfig.endingStoryId)
			if not re then
				printWarn("剧情播放失败,id:" .. nowLevelConfig.endingStoryId)
				handler()
				return
			end
			cmd:setFinishCallback(handler, self)
		else
			handler()
		end
	else
		handler()
	end
end

--单机排行榜
function TetrisController:openRankingView(levelId)
	local rankingIds = {
		GameEnum.RankingTypeEnum.ACTIVITY_278_LEVEL_1_RANKING,
		GameEnum.RankingTypeEnum.ACTIVITY_278_LEVEL_2_RANKING,
		GameEnum.RankingTypeEnum.ACTIVITY_278_LEVEL_3_RANKING
	}
	local actId = GameEnum.ActivityEnum.ACTIVITY_278
	local rankingId = rankingIds[levelId]
	local rankingType = {CommonRankingNotify.rankingConfigs.OverallList, CommonRankingNotify.rankingConfigs.HappyBreadList}
	ViewMgr.instance:open("CommonRankingView", actId, {rankingId, rankingId}, rankingType)

	rankingIds = nil
end

------------------- 进行游戏需要准备的数据or资源 -------------------
--创建一个对象池
function TetrisController:_createPool(handle)
	self._pool =
		TetrisPool.New(
		TetrisNotify.poolsInfo,
		self:getShapeParent(),
		function()
			if handle then
				handle()
			end
		end,
		true
	)
end
--获取对象池
function TetrisController:getPool()
	return self._pool
end

--创建游戏  mode:游戏类型（单机or联机
function TetrisController:_createGame()
	LateUpdateBeat:Add(self._lateUpdate, self)
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if self._gameMgrList then
		for i = 1, #self._gameMgrList do
			self._gameMgrList[i]:disposeGame()
		end
	end
	self._gameMgrList = {}
	self._gameMgrList[1] = TetrisGameManager.New(1)
	if not isStandalone then
		self._gameMgrList[2] = TetrisGameDisplayMgr.New(2)
	end
	self:_checkPrepareFinish("createGame")
end

------------------- 后台处理、断线重连 -------------------
--去到后台
function TetrisController:_onApplicationPause()
	self._pauseRealTime = Time.realtimeSinceStartup
end
--后台回来
function TetrisController:_onApplicationResume()
	--需要添加惩罚
	SceneTimer:setTimer(
		0,
		function()
			if TetrisController.instance._pauseRealTime == nil then
				return
			end
			local time = Time.realtimeSinceStartup
			local interval = time - TetrisController.instance._pauseRealTime
			TetrisController.instance:applicationResume()
			TetrisController.instance:speedupGame(interval)
		end,
		nil,
		false
	)
end

--加快游戏（主要用来追上后台丢失时间）
function TetrisController:speedupGame(interval)
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if isStandalone then
		return
	end
	if interval <= 0 then
		return
	end
	if self:isGameOver() then
		return
	end
	if self:isDead() then
		return
	end
	--在游戏开始倒计时的时候后台，此时游戏未开始
	if self._gameMgrList then
		local curFallTime = self._gameMgrList[1]:getNomalFallTime()
		if interval < curFallTime then
			return
		end
		local steps = math.min(math.floor(interval / curFallTime), 120)
		self._gameMgrList[1]:setSpeedupGame(steps)
	end
end

--检查游戏房间是否还存在
function TetrisController:_checkRoomIsExist()
	if self._isSendResult then
		return
	end
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if not isStandalone then
		return
	end
	local getRoomRsp = function(msg, isSuccess)
		-- 处理正常回到等待房间的过程中又触发了断线重连的情况
		if SceneManager.instance:isEnetering() then
			print("GameRoom _onReconnectFinish 222222222 -=>> Scene is Entering ~!~!~!~!")
			return
		end

		if not isSuccess then
			return
		end
		local roomId = msg.roomId
		local bDisconnect = msg.disconnect
		local gameId = msg.gameId
		if bDisconnect and roomId > 0 then
			--- 断了但是房间还在
			return
		end
		if roomId == 0 then
			--- 房间都没了
			DialogHelper.showErrorMsg(5839)
			self:quickGame()
			return
		end
	end
	--- 看下有没有房间
	GameRoomAgent.instance:sendGetGameRoomJoinInfoRequest(getRoomRsp)
end

--断线
function TetrisController:_onDisconnected()
	self._setDisSync = true
end

--重连
function TetrisController:_reConnectFinish()
	self._setDisSync = false
	self:_checkRoomIsExist()
end

function TetrisController:openSelectSkillView()
	local h = function(selectSkillId, callback)
		Activity278Agent.instance:sendTetrisChangeSkillRequest(selectSkillId, callback)
	end
	local viewType = 1
	local openSelect = 1
	ViewMgr.instance:open("TetrisSkillView", viewType, openSelect, h)
end

TetrisController.instance = TetrisController.New()
return TetrisController
