module("logic.extensions.throwcake.config.ThrowCakeConfig", package.seeall)
local ThrowCakeConfig = class("ThrowCakeConfig")

ThrowCakeConfig.animTime = 2
ThrowCakeConfig.showCakeTime = 2

local commonConfig = ConfigLoader.New("act252_common_config")
local sonLevelConfig = ConfigLoader.New("act252_son_level")
local modeConfig = ConfigLoader.New("act252_mode")
local rewardCfg = ConfigLoader.New("act252_reward")
local cakeConfig = ConfigLoader.New("act252_appearance")

function ThrowCakeConfig.getActivityId()
    return 2444
end

function ThrowCakeConfig.getCommonConfig(key, actId)
    local activityId = actId or ThrowCakeConfig.getActivityId()
    return commonConfig:getConfig()[activityId][key].value
end

function ThrowCakeConfig.getShowCakeTime()
    return tonumber(ThrowCakeConfig.getCommonConfig("newSonLevelTime"))
end

local playerAnimCfg = string.split(ThrowCakeConfig.getCommonConfig("playerAnims"), ",")
local elfAnimCfg = string.split(ThrowCakeConfig.getCommonConfig("elfAnims"), ",")
local playerHappyName = playerAnimCfg[1]
local playerSadName = playerAnimCfg[2]
local elfHappyAnimName = elfAnimCfg[1]
local elfSadAnimName = elfAnimCfg[2]

local playerThrowName = nil
local elfThrowAnim = "2.0_pao"
local playerHoldName = nil
local elfHoldAnimName = "2.0_juqi"

function ThrowCakeConfig.getHoldAnimName()
    return playerHoldName, elfHoldAnimName
end

function ThrowCakeConfig.getHappyAnimName()
    return playerHappyName, elfHappyAnimName
end

function ThrowCakeConfig.getSadAnimName()
    return playerSadName, elfSadAnimName
end

function ThrowCakeConfig.getThrowAnimName()
    return playerThrowName, elfThrowAnim
end

function ThrowCakeConfig.getGameCfgInfo(mode, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    local cfg = modeConfig:getConfig()[activityId][mode]
    local info = {}
    info.isSingle = cfg.gameType ~= 3
    info.isEndless = cfg.gameType ~= 1
    info.sonLevels = cfg.sonLevel
    info.hp = cfg.heart
    return info
end

function ThrowCakeConfig.getRemindTime()
    return 3
end
function ThrowCakeConfig.getMaxRoundTime()
    -- 3秒动画时间
    return tonumber(ThrowCakeConfig.getCommonConfig("stepTime")) - ThrowCakeConfig.animTime
end

function ThrowCakeConfig.getCakeInitY()
    return -40
end

function ThrowCakeConfig.getLeftCakeInitX()
    return -460
end

function ThrowCakeConfig.getRightCakeInitX()
    return 460
end

function ThrowCakeConfig.getTriggerY()
    return -40
end

function ThrowCakeConfig.getCake0Y()
    return -88
end
-- function ThrowCakeConfig.getDownY()
--     return 350
-- end

function ThrowCakeConfig.getCakeXYbyType(type, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    local cfg = cakeConfig:getConfig()[activityId][type]
    return cfg.width, cfg.height, cfg.showWidth
end

function ThrowCakeConfig.getImageNameByCakeType(type, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    local cfg = cakeConfig:getConfig()[activityId][type]
    return cfg.imagename
end

function ThrowCakeConfig.getCakeProportion(type, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    return cakeConfig:getConfig()[activityId][type].heightProportion
    -- local rusult = {tonumber(cfg)}
    -- return {0.4, 0.3}
end

function ThrowCakeConfig.getMaxLayers(levelId, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    local cfg = sonLevelConfig:getConfig()[activityId][levelId]
    return cfg.targetLayer
end

function ThrowCakeConfig.getCakeType(levelId, layer, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    local cfg = sonLevelConfig:getConfig()[activityId][levelId]
    if layer == cfg.targetLayer then
        return cfg.finalAppearance
    else
        local types = cfg.appearance
        local index = layer % (#types)
        if index == 0 then
            index = #types
        end
        return types[index]
    end
end

function ThrowCakeConfig.getLevelCfg(levelId, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    return modeConfig:getConfig()[activityId][levelId]
end

local rewardMap = {}

-- 获取奖励
function ThrowCakeConfig.getRewardByLevel(levelId, activityId)
    activityId = activityId or ThrowCakeConfig.getActivityId()
    if rewardMap[activityId] == nil then
        rewardMap[activityId] = {}
    end
    if rewardMap[activityId][levelId] == nil then
        local cfg = rewardCfg:getConfig()[activityId]
        local rewards = {}
        for i, v in ipairs(cfg) do
            if v.levelId == levelId then
                table.insert(rewards, v)
            end
        end
        rewardMap[activityId][levelId] = rewards
    end
    return rewardMap[activityId][levelId]
end

local scoreCfgStr = ThrowCakeConfig.getCommonConfig("judgeScore")
local scoreCfgList = string.split(scoreCfgStr, ",")
local scoreCfg = {}
for i, v in ipairs(scoreCfgList) do
    local re = string.split(v, ":")
    local re1 = {
        dis = tonumber(re[1]),
        score = tonumber(re[2])
    }
    table.insert(scoreCfg, re1)
end

function ThrowCakeConfig.getEvaluateLevel(score)
    local level = 4
    for i, v in ipairs(scoreCfg) do
        if score == v.score then
            level = i
            break
        end
    end
    return level
end

function ThrowCakeConfig.getScorebyDis(dis)
    -- print(dis, #scoreCfg)
    local score = tonumber(ThrowCakeConfig.getCommonConfig("minSucScore"))
    local level = 4
    for i, v in ipairs(scoreCfg) do
        if dis <= v.dis then
            level = i
            score = v.score
            break
        end
    end
    print(score, level)
    return score, level
end

local windCfgStr = ThrowCakeConfig.getCommonConfig("windChangeRule")
local windCfgList = string.split(windCfgStr, ",")
local windCfg = {}
for i, v in ipairs(windCfgList) do
    local re = string.split(v, ":")
    local re1 = {
        startLayer = tonumber(re[1]),
        factor = tonumber(re[2])
    }
    table.insert(windCfg, re1)
end

function ThrowCakeConfig.getWindFactor(layer)
    local index
    for i, v in ipairs(windCfg) do
        if layer >= v.startLayer then
            if i == #windCfg then
                index = i
            else
                if layer < windCfg[i + 1].startLayer then
                    index = i
                    break
                end
            end
        end
    end
    return windCfg[index].factor
end

local powerSpeedCfgStr = ThrowCakeConfig.getCommonConfig("powerSpeedChangeRule")
local powerSpeedCfgList = string.split(powerSpeedCfgStr, ",")
local powerSpeedCfg = {}
for i, v in ipairs(powerSpeedCfgList) do
    local re = string.split(v, ":")
    local re1 = {
        startLayer = tonumber(re[1]),
        factor = tonumber(re[2])
    }
    table.insert(powerSpeedCfg, re1)
end

function ThrowCakeConfig.getPowerSpeedFactor(layer)
    local index = #powerSpeedCfg
    for i, v in ipairs(powerSpeedCfg) do
        if layer >= v.startLayer and (powerSpeedCfg[i + 1] and layer < powerSpeedCfg[i + 1].startLayer) then
            index = i
            break
        end
    end
    return powerSpeedCfg[index].factor
end

function ThrowCakeConfig.getEmojiCfg()
    return string.split(ThrowCakeConfig.getCommonConfig("emojiIDs"), ",")
end

return ThrowCakeConfig
