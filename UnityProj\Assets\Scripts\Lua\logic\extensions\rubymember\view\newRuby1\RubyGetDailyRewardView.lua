module("logic.extensions.rubymember.view.newRuby1.RubyGetDailyRewardView",package.seeall)
local RubyGetDailyRewardView = class("RubyGetDailyRewardView", ViewComponent)

function RubyGetDailyRewardView:buildUI()
	self._content = self:getGo("content")
    self.dailyRewardIcons = {}
    local info = RubyMemberConfig.getDailyGift()
    for i = 1, 3, 1 do
        local icon = CommonIconMgr.instance:fetchCommonIcon()
        icon:setWidthAndHeight(96):showCount(true):showRarenessGo(true)
        table.insert(self.dailyRewardIcons, icon)
        local container = self:getGo("everyDayAwardGo/awardItem_" .. i)
        goutil.addChildToParent(icon:getPrefab(), self._content)
        icon:buildData(info.gifts[i])
    end
end

function RubyGetDailyRewardView:destroyUI()
    for i, v in ipairs(self.dailyRewardIcons) do
        CommonIconMgr.instance:returnCommonIcon(v)
    end
end

function RubyGetDailyRewardView:onClickOutside()
    local callBack = self:getFirstParam()
    VipAgent.instance:sendDrawVipDailyRewardRequest(callBack)
    self:close()
end

return RubyGetDailyRewardView