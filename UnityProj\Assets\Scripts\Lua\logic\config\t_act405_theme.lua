-- {excel:405赛季集卡活动.xlsx, sheetName:export_赛季集卡主题配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act405_theme", package.seeall)

local title = {activityId=1,themeId=2,themeName=3,themeImage=4,themeRank=5,rewardIconScale=6,cardIds=7,rewards=8}

local dataList = {
	{1806, 1, "奇妙视界", "1", 1, 1, {16100011,16100012,16100013,16100014,16100015,16100016,16100017,16100018,16100019}, {{count=1,id=12004831},{count=5,id=16000034},{count=20000,id=1}}},
	{1806, 2, "生日派对", "2", 1, 1, {16100021,16100022,16100023,16100024,16100025,16100026,16100027,16100028,16100029}, {{count=1,id=26010154},{count=1,id=16000055},{count=5,id=16000316}}},
	{1806, 3, "幽灵酒店", "3", 1, 1, {16100031,16100032,16100033,16100034,16100035,16100036,16100037,16100038,16100039}, {{count=2,id=16000212},{count=5,id=16000038},{count=10,id=29012001}}},
	{1806, 4, "爱与死亡", "4", 1, 1, {16100041,16100042,16100043,16100044,16100045,16100046,16100047,16100048,16100049}, {{count=1,id=13003613},{count=1,id=16000053},{count=5,id=16000318}}},
	{1806, 5, "圣诞捣蛋夜", "5", 1, 1, {16100051,16100052,16100053,16100054,16100055,16100056,16100057,16100058,16100059}, {{count=1,id=12004830},{count=1,id=16000005},{count=10,id=29012011}}},
	{1806, 6, "午夜乐园", "6", 1, 1, {16100061,16100062,16100063,16100064,16100065,16100066,16100067,16100068,16100069}, {{count=1,id=13003612},{count=20,id=16000557},{count=5,id=16000320}}},
	{1806, 7, "书中秘旅", "7", 1, 1, {16100071,16100072,16100073,16100074,16100075,16100076,16100077,16100078,16100079}, {{count=1,id=18000132},{count=10,id=16000352},{count=5,id=16000145}}},
	{1806, 8, "爱与实验", "8", 1, 1, {16100081,16100082,16100083,16100084,16100085,16100086,16100087,16100088,16100089}, {{count=1,id=13003610},{count=5,id=16000039},{count=5,id=16000321}}},
	{1806, 9, "孤独月球", "9", 1, 1, {16100091,16100092,16100093,16100094,16100095,16100096,16100097,16100098,16100099}, {{count=1,id=12004805},{count=1,id=16000007},{count=5,id=16000146}}},
	{1806, 10, "秘密庄园", "10", 1, 1, {16100101,16100102,16100103,16100104,16100105,16100106,16100107,16100108,16100109}, {{count=1,id=13003611},{count=5,id=16000035},{count=5,id=16000319}}},
	{1806, 11, "捉迷藏", "11", 1, 1, {16100111,16100112,16100113,16100114,16100115,16100116,16100117,16100118,16100119}, {{count=1,id=30005002},{count=3,id=16000558},{count=3,id=16000541}}},
	{1806, 12, "一个人的生日", "12", 1, 1, {16100121,16100122,16100123,16100124,16100125,16100126,16100127,16100128,16100129}, {{count=1,id=12004806},{count=1,id=16000007},{count=5,id=16000317}}},
	{1806, 13, "坏熊都市", "13", 1, 1, {16100131,16100132,16100133,16100134,16100135,16100136,16100137,16100138,16100139}, {{count=200,id=2},{count=3,id=29012012},{count=3,id=16000542}}},
	{1806, 14, "夜幕餐馆", "14", 1, 1, {16100141,16100142,16100143,16100144,16100145,16100146,16100147,16100148,16100149}, {{count=1,id=12004905},{count=1,id=16000054},{count=3,id=29012002}}},
	{1806, 15, "闪钻之镜", "15", 1, 1, {16100151,16100152,16100153,16100154,16100155,16100156,16100157,16100158,16100159}, {{count=1,id=25000038},{count=1,id=16000006},{count=3,id=16000543}}},
	{1949, 1, "快乐萌二", "21", 3, 1, {16101011,16101012,16101013,16101014,16101015,16101016,16101017,16101018,16101019}, {{count=1,id=12005506},{count=5,id=16000034},{count=20000,id=1}}},
	{1949, 2, "赏花时刻", "22", 3, 1, {16101021,16101022,16101023,16101024,16101025,16101026,16101027,16101028,16101029}, {{count=1,id=26010175},{count=1,id=16000055},{count=5,id=16000316}}},
	{1949, 3, "萌二运动", "23", 3, 1, {16101031,16101032,16101033,16101034,16101035,16101036,16101037,16101038,16101039}, {{count=2,id=16000212},{count=5,id=16000038},{count=10,id=29012001}}},
	{1949, 4, "萌二甜点屋", "24", 3, 1, {16101041,16101042,16101043,16101044,16101045,16101046,16101047,16101048,16101049}, {{count=1,id=13003766},{count=1,id=16000053},{count=5,id=16000318}}},
	{1949, 5, "复古玩具", "25", 3, 1, {16101051,16101052,16101053,16101054,16101055,16101056,16101057,16101058,16101059}, {{count=1,id=12005103},{count=1,id=16000005},{count=10,id=29012011}}},
	{1949, 6, "萌二春节", "26", 3, 1, {16101061,16101062,16101063,16101064,16101065,16101066,16101067,16101068,16101069}, {{count=1,id=13003765},{count=20,id=16000557},{count=5,id=16000320}}},
	{1949, 7, "小小梦想", "27", 3, 1, {16101071,16101072,16101073,16101074,16101075,16101076,16101077,16101078,16101079}, {{count=1,id=18000143},{count=10,id=16000352},{count=5,id=16000145}}},
	{1949, 8, "回转甜点屋", "28", 3, 1, {16101081,16101082,16101083,16101084,16101085,16101086,16101087,16101088,16101089}, {{count=1,id=13003768},{count=5,id=16000039},{count=5,id=16000321}}},
	{1949, 9, "花园交响曲", "29", 1, 0.92, {16101091,16101092,16101093,16101094,16101095,16101096,16101097,16101098,16101099}, {{count=1,id=12005154},{count=1,id=16000007},{count=5,id=16000146}}},
	{1949, 10, "爱心日", "210", 3, 1, {16101101,16101102,16101103,16101104,16101105,16101106,16101107,16101108,16101109}, {{count=1,id=13003767},{count=5,id=16000035},{count=5,id=16000319}}},
	{1949, 11, "美味之舞", "211", 3, 1, {16101111,16101112,16101113,16101114,16101115,16101116,16101117,16101118,16101119}, {{count=1,id=30005003},{count=3,id=16000558},{count=3,id=16000541}}},
	{1949, 12, "梦游时刻", "212", 1, 0.92, {16101121,16101122,16101123,16101124,16101125,16101126,16101127,16101128,16101129}, {{count=1,id=12005153},{count=1,id=16000007},{count=5,id=16000317}}},
	{1949, 13, "雪花飘飘", "213", 3, 1, {16101131,16101132,16101133,16101134,16101135,16101136,16101137,16101138,16101139}, {{count=200,id=2},{count=3,id=29012012},{count=3,id=16000542}}},
	{1949, 14, "趣味童话", "214", 2, 0.92, {16101141,16101142,16101143,16101144,16101145,16101146,16101147,16101148,16101149}, {{count=1,id=12005428},{count=1,id=16000054},{count=3,id=29012002}}},
	{1949, 15, "快乐时光", "215", 2, 0.8, {16101151,16101152,16101153,16101154,16101155,16101156,16101157,16101158,16101159}, {{count=1,id=25000043},{count=1,id=16000006},{count=3,id=16000543}}},
	{2126, 1, "美味诱惑", "31", 3, 1, {16102011,16102012,16102013,16102014,16102015,16102016,16102017,16102018,16102019}, {{count=1,id=12005858},{count=5,id=16000034},{count=20000,id=1}}},
	{2126, 2, "松饼出击", "32", 3, 1, {16102021,16102022,16102023,16102024,16102025,16102026,16102027,16102028,16102029}, {{count=1,id=26010201},{count=1,id=16000055},{count=5,id=16000316}}},
	{2126, 3, "花儿与快乐", "33", 3, 0.7, {16102031,16102032,16102033,16102034,16102035,16102036,16102037,16102038,16102039}, {{count=2,id=16000212},{count=5,id=16000038},{count=10,id=29012001}}},
	{2126, 4, "派对之星", "34", 3, 1, {16102041,16102042,16102043,16102044,16102045,16102046,16102047,16102048,16102049}, {{count=1,id=13004288},{count=1,id=16000053},{count=5,id=16000318}}},
	{2126, 5, "暗夜惊袭", "35", 3, 1, {16102051,16102052,16102053,16102054,16102055,16102056,16102057,16102058,16102059}, {{count=1,id=12005859},{count=1,id=16000005},{count=10,id=29012011}}},
	{2126, 6, "寿司派对", "36", 3, 1, {16102061,16102062,16102063,16102064,16102065,16102066,16102067,16102068,16102069}, {{count=1,id=13004289},{count=20,id=16000557},{count=5,id=16000320}}},
	{2126, 7, "美美的一天", "37", 3, 0.85, {16102071,16102072,16102073,16102074,16102075,16102076,16102077,16102078,16102079}, {{count=1,id=18000173},{count=10,id=16000352},{count=5,id=16000145}}},
	{2126, 8, "黑魔法", "38", 3, 1, {16102081,16102082,16102083,16102084,16102085,16102086,16102087,16102088,16102089}, {{count=1,id=13004290},{count=5,id=16000039},{count=5,id=16000321}}},
	{2126, 9, "居家生活", "39", 1, 0.92, {16102091,16102092,16102093,16102094,16102095,16102096,16102097,16102098,16102099}, {{count=1,id=12005904},{count=1,id=16000007},{count=5,id=16000146}}},
	{2126, 10, "生日世界", "310", 3, 1, {16102101,16102102,16102103,16102104,16102105,16102106,16102107,16102108,16102109}, {{count=1,id=13004291},{count=5,id=16000035},{count=5,id=16000319}}},
	{2126, 11, "睡意绵绵", "311", 3, 1, {16102111,16102112,16102113,16102114,16102115,16102116,16102117,16102118,16102119}, {{count=1,id=30005005},{count=3,id=16000558},{count=3,id=16000541}}},
	{2126, 12, "冬雪的邀请", "312", 1, 0.92, {16102121,16102122,16102123,16102124,16102125,16102126,16102127,16102128,16102129}, {{count=1,id=12005905},{count=1,id=16000007},{count=5,id=16000317}}},
	{2126, 13, "超市购物", "313", 3, 0.7, {16102131,16102132,16102133,16102134,16102135,16102136,16102137,16102138,16102139}, {{count=200,id=2},{count=3,id=29012012},{count=3,id=16000542}}},
	{2126, 14, "烘焙飘香", "314", 2, 0.92, {16102141,16102142,16102143,16102144,16102145,16102146,16102147,16102148,16102149}, {{count=1,id=12005969},{count=1,id=16000054},{count=3,id=29012002}}},
	{2126, 15, "放松的一天", "315", 2, 0.8, {16102151,16102152,16102153,16102154,16102155,16102156,16102157,16102158,16102159}, {{count=1,id=25000050},{count=1,id=16000006},{count=3,id=16000543}}},
	{2243, 1, "游乐园", "41", 3, 1, {16103011,16103012,16103013,16103014,16103015,16103016,16103017,16103018,16103019}, {{count=1,id=12006387},{count=5,id=16000034},{count=20000,id=1}}},
	{2243, 2, "海洋世界", "42", 3, 1, {16103021,16103022,16103023,16103024,16103025,16103026,16103027,16103028,16103029}, {{count=1,id=26010221},{count=1,id=16000055},{count=5,id=16000316}}},
	{2243, 3, "甜甜之梦", "43", 3, 0.7, {16103031,16103032,16103033,16103034,16103035,16103036,16103037,16103038,16103039}, {{count=2,id=16000212},{count=5,id=16000038},{count=10,id=29012001}}},
	{2243, 4, "动物乐园", "44", 3, 1, {16103041,16103042,16103043,16103044,16103045,16103046,16103047,16103048,16103049}, {{count=1,id=13004535},{count=1,id=16000053},{count=5,id=16000318}}},
	{2243, 5, "思绪空间", "45", 3, 1, {16103051,16103052,16103053,16103054,16103055,16103056,16103057,16103058,16103059}, {{count=1,id=12006388},{count=1,id=16000005},{count=10,id=29012011}}},
	{2243, 6, "少女的浪漫", "46", 3, 1, {16103061,16103062,16103063,16103064,16103065,16103066,16103067,16103068,16103069}, {{count=1,id=13004536},{count=20,id=16000557},{count=5,id=16000320}}},
	{2243, 7, "庆祝时刻", "47", 3, 0.85, {16103071,16103072,16103073,16103074,16103075,16103076,16103077,16103078,16103079}, {{count=1,id=18000190},{count=10,id=16000352},{count=5,id=16000145}}},
	{2243, 8, "大大梦想", "48", 3, 1, {16103081,16103082,16103083,16103084,16103085,16103086,16103087,16103088,16103089}, {{count=1,id=13004537},{count=5,id=16000039},{count=5,id=16000321}}},
	{2243, 9, "癫狂之境", "49", 1, 0.92, {16103091,16103092,16103093,16103094,16103095,16103096,16103097,16103098,16103099}, {{count=1,id=12006433},{count=1,id=16000007},{count=5,id=16000146}}},
	{2243, 10, "马戏团", "410", 3, 1, {16103101,16103102,16103103,16103104,16103105,16103106,16103107,16103108,16103109}, {{count=1,id=13004538},{count=5,id=16000035},{count=5,id=16000319}}},
	{2243, 11, "奇幻异界", "411", 3, 1, {16103111,16103112,16103113,16103114,16103115,16103116,16103117,16103118,16103119}, {{count=1,id=30005006},{count=3,id=16000558},{count=3,id=16000541}}},
	{2243, 12, "秘密小屋", "412", 1, 0.92, {16103121,16103122,16103123,16103124,16103125,16103126,16103127,16103128,16103129}, {{count=1,id=12006434},{count=1,id=16000007},{count=5,id=16000317}}},
	{2243, 13, "梦幻缤纷", "413", 3, 0.7, {16103131,16103132,16103133,16103134,16103135,16103136,16103137,16103138,16103139}, {{count=200,id=2},{count=3,id=29012012},{count=3,id=16000542}}},
	{2243, 14, "精灵之森", "414", 2, 0.92, {16103141,16103142,16103143,16103144,16103145,16103146,16103147,16103148,16103149}, {{count=1,id=12006576},{count=1,id=16000054},{count=3,id=29012002}}},
	{2243, 15, "虚空世界", "415", 2, 0.8, {16103151,16103152,16103153,16103154,16103155,16103156,16103157,16103158,16103159}, {{count=1,id=25000056},{count=1,id=16000006},{count=3,id=16000543}}},
	{2371, 1, "米花小屋", "51", 3, 1, {16104011,16104012,16104013,16104014,16104015,16104016,16104017,16104018,16104019}, {{count=1,id=12006835},{count=5,id=16000034},{count=20000,id=1}}},
	{2371, 2, "照镜子", "52", 3, 1, {16104021,16104022,16104023,16104024,16104025,16104026,16104027,16104028,16104029}, {{count=1,id=26010241},{count=1,id=16000055},{count=5,id=16000316}}},
	{2371, 3, "送礼物", "53", 3, 0.7, {16104031,16104032,16104033,16104034,16104035,16104036,16104037,16104038,16104039}, {{count=2,id=16000212},{count=5,id=16000038},{count=10,id=29012001}}},
	{2371, 4, "泡泡浪漫", "54", 3, 1, {16104041,16104042,16104043,16104044,16104045,16104046,16104047,16104048,16104049}, {{count=1,id=13004811},{count=1,id=16000053},{count=5,id=16000318}}},
	{2371, 5, "美食变装秀", "55", 3, 1, {16104051,16104052,16104053,16104054,16104055,16104056,16104057,16104058,16104059}, {{count=1,id=12006836},{count=1,id=16000005},{count=10,id=29012011}}},
	{2371, 6, "暗夜怪物", "56", 3, 1, {16104061,16104062,16104063,16104064,16104065,16104066,16104067,16104068,16104069}, {{count=1,id=13004810},{count=20,id=16000557},{count=5,id=16000320}}},
	{2371, 7, "深夜时分", "57", 3, 0.85, {16104071,16104072,16104073,16104074,16104075,16104076,16104077,16104078,16104079}, {{count=1,id=18000210},{count=10,id=16000352},{count=5,id=16000145}}},
	{2371, 8, "舞台表演", "58", 3, 1, {16104081,16104082,16104083,16104084,16104085,16104086,16104087,16104088,16104089}, {{count=1,id=13004812},{count=5,id=16000039},{count=5,id=16000321}}},
	{2371, 9, "马戏团", "59", 1, 0.92, {16104091,16104092,16104093,16104094,16104095,16104096,16104097,16104098,16104099}, {{count=1,id=12006859},{count=1,id=16000007},{count=5,id=16000146}}},
	{2371, 10, "蛋糕派对", "510", 3, 1, {16104101,16104102,16104103,16104104,16104105,16104106,16104107,16104108,16104109}, {{count=1,id=13004813},{count=5,id=16000035},{count=5,id=16000319}}},
	{2371, 11, "下雨天", "511", 3, 1, {16104111,16104112,16104113,16104114,16104115,16104116,16104117,16104118,16104119}, {{count=1,id=30005007},{count=3,id=16000558},{count=3,id=16000541}}},
	{2371, 12, "悠闲生活", "512", 1, 0.92, {16104121,16104122,16104123,16104124,16104125,16104126,16104127,16104128,16104129}, {{count=1,id=12006860},{count=1,id=16000007},{count=5,id=16000317}}},
	{2371, 13, "冬雪趣味", "513", 3, 0.7, {16104131,16104132,16104133,16104134,16104135,16104136,16104137,16104138,16104139}, {{count=200,id=2},{count=3,id=29012012},{count=3,id=16000542}}},
	{2371, 14, "洗刷刷", "514", 2, 0.92, {16104141,16104142,16104143,16104144,16104145,16104146,16104147,16104148,16104149}, {{count=1,id=12006834},{count=1,id=16000054},{count=3,id=29012002}}},
	{2371, 15, "幸福米花", "515", 2, 0.8, {16104151,16104152,16104153,16104154,16104155,16104156,16104157,16104158,16104159}, {{count=1,id=25000060},{count=1,id=16000006},{count=3,id=16000543}}},
	{2462, 1, "游戏世界", "61", 3, 1, {16105011,16105012,16105013,16105014,16105015,16105016,16105017,16105018,16105019}, {{count=1,id=12006934},{count=5,id=16000034},{count=20000,id=1}}},
	{2462, 2, "咖啡小店", "62", 3, 1, {16105021,16105022,16105023,16105024,16105025,16105026,16105027,16105028,16105029}, {{count=1,id=26010257},{count=1,id=16000055},{count=5,id=16000316}}},
	{2462, 3, "玩偶甜心", "63", 3, 0.7, {16105031,16105032,16105033,16105034,16105035,16105036,16105037,16105038,16105039}, {{count=2,id=16000212},{count=5,id=16000038},{count=10,id=29012001}}},
	{2462, 4, "鼠鼠甜品店", "64", 3, 1, {16105041,16105042,16105043,16105044,16105045,16105046,16105047,16105048,16105049}, {{count=1,id=13004838},{count=1,id=16000053},{count=5,id=16000318}}},
	{2462, 5, "奶茶小铺", "65", 3, 1, {16105051,16105052,16105053,16105054,16105055,16105056,16105057,16105058,16105059}, {{count=1,id=12006933},{count=1,id=16000005},{count=10,id=29012011}}},
	{2462, 6, "甜蜜之家", "66", 3, 1, {16105061,16105062,16105063,16105064,16105065,16105066,16105067,16105068,16105069}, {{count=1,id=13004839},{count=20,id=16000557},{count=5,id=16000320}}},
	{2462, 7, "花蝶之恋", "67", 3, 0.85, {16105071,16105072,16105073,16105074,16105075,16105076,16105077,16105078,16105079}, {{count=1,id=18000226},{count=10,id=16000352},{count=5,id=16000145}}},
	{2462, 8, "兔兔恋人", "68", 3, 1, {16105081,16105082,16105083,16105084,16105085,16105086,16105087,16105088,16105089}, {{count=1,id=13004840},{count=5,id=16000039},{count=5,id=16000321}}},
	{2462, 9, "甜甜小熊", "69", 1, 0.92, {16105091,16105092,16105093,16105094,16105095,16105096,16105097,16105098,16105099}, {{count=1,id=12006931},{count=1,id=16000007},{count=5,id=16000146}}},
	{2462, 10, "浪漫甜点", "610", 3, 1, {16105101,16105102,16105103,16105104,16105105,16105106,16105107,16105108,16105109}, {{count=1,id=13004841},{count=5,id=16000035},{count=5,id=16000319}}},
	{2462, 11, "美味森野", "611", 3, 1, {16105111,16105112,16105113,16105114,16105115,16105116,16105117,16105118,16105119}, {{count=1,id=30005009},{count=3,id=16000558},{count=3,id=16000541}}},
	{2462, 12, "忘忧森吧", "612", 1, 0.92, {16105121,16105122,16105123,16105124,16105125,16105126,16105127,16105128,16105129}, {{count=1,id=12006932},{count=1,id=16000007},{count=5,id=16000317}}},
	{2462, 13, "可爱因子", "613", 3, 0.7, {16105131,16105132,16105133,16105134,16105135,16105136,16105137,16105138,16105139}, {{count=200,id=2},{count=3,id=29012012},{count=3,id=16000542}}},
	{2462, 14, "花园精灵", "614", 2, 0.92, {16105141,16105142,16105143,16105144,16105145,16105146,16105147,16105148,16105149}, {{count=1,id=12006979},{count=1,id=16000054},{count=3,id=29012002}}},
	{2462, 15, "多巴胺魔女", "615", 2, 0.8, {16105151,16105152,16105153,16105154,16105155,16105156,16105157,16105158,16105159}, {{count=1,id=25000065},{count=1,id=16000006},{count=3,id=16000543}}},
}

local t_act405_theme = {
	[1806] = {
		[1] = dataList[1],
		[2] = dataList[2],
		[3] = dataList[3],
		[4] = dataList[4],
		[5] = dataList[5],
		[6] = dataList[6],
		[7] = dataList[7],
		[8] = dataList[8],
		[9] = dataList[9],
		[10] = dataList[10],
		[11] = dataList[11],
		[12] = dataList[12],
		[13] = dataList[13],
		[14] = dataList[14],
		[15] = dataList[15],
	},
	[1949] = {
		[1] = dataList[16],
		[2] = dataList[17],
		[3] = dataList[18],
		[4] = dataList[19],
		[5] = dataList[20],
		[6] = dataList[21],
		[7] = dataList[22],
		[8] = dataList[23],
		[9] = dataList[24],
		[10] = dataList[25],
		[11] = dataList[26],
		[12] = dataList[27],
		[13] = dataList[28],
		[14] = dataList[29],
		[15] = dataList[30],
	},
	[2126] = {
		[1] = dataList[31],
		[2] = dataList[32],
		[3] = dataList[33],
		[4] = dataList[34],
		[5] = dataList[35],
		[6] = dataList[36],
		[7] = dataList[37],
		[8] = dataList[38],
		[9] = dataList[39],
		[10] = dataList[40],
		[11] = dataList[41],
		[12] = dataList[42],
		[13] = dataList[43],
		[14] = dataList[44],
		[15] = dataList[45],
	},
	[2243] = {
		[1] = dataList[46],
		[2] = dataList[47],
		[3] = dataList[48],
		[4] = dataList[49],
		[5] = dataList[50],
		[6] = dataList[51],
		[7] = dataList[52],
		[8] = dataList[53],
		[9] = dataList[54],
		[10] = dataList[55],
		[11] = dataList[56],
		[12] = dataList[57],
		[13] = dataList[58],
		[14] = dataList[59],
		[15] = dataList[60],
	},
	[2371] = {
		[1] = dataList[61],
		[2] = dataList[62],
		[3] = dataList[63],
		[4] = dataList[64],
		[5] = dataList[65],
		[6] = dataList[66],
		[7] = dataList[67],
		[8] = dataList[68],
		[9] = dataList[69],
		[10] = dataList[70],
		[11] = dataList[71],
		[12] = dataList[72],
		[13] = dataList[73],
		[14] = dataList[74],
		[15] = dataList[75],
	},
	[2462] = {
		[1] = dataList[76],
		[2] = dataList[77],
		[3] = dataList[78],
		[4] = dataList[79],
		[5] = dataList[80],
		[6] = dataList[81],
		[7] = dataList[82],
		[8] = dataList[83],
		[9] = dataList[84],
		[10] = dataList[85],
		[11] = dataList[86],
		[12] = dataList[87],
		[13] = dataList[88],
		[14] = dataList[89],
		[15] = dataList[90],
	},
}

t_act405_theme.dataList = dataList
local mt
if Act405ThemeDefine then
	mt = {
		__cname =  "Act405ThemeDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act405ThemeDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act405_theme