module("logic.extensions.sharestreet.cfg.ShareStreetCfg",package.seeall)

local ShareStreetCfg = class("ShareStreetCfg")

local commonCfg = ConfigLoader.New("share_street_commonconfig")
local areaCfg = ConfigLoader.New("share_street_area")
local share_street_position = ConfigLoader.New("share_street_position")

function ShareStreetCfg:ctor()
	self._areaCfgList = RoomConfig.getAllAreaCnofigs(0, 2)
    table.sort(self._areaCfgList, function(a, b) 
        return b.id > a.id
    end)
    self._areaCfgMap = {}
    for i, areaCfg in ipairs(self._areaCfgList) do
        self._areaCfgMap[areaCfg.id] = areaCfg
    end
    self._upgradeCfgList = RoomConfig.getAllUpgradeConfig(0, 2)
    self._upgradeCfgMap = {}
    for i, upgradeCfg in ipairs(self._upgradeCfgList) do
        self._upgradeCfgMap[upgradeCfg.id] = upgradeCfg
    end

end

function ShareStreetCfg:getCommonConfigValue(key)
    return commonCfg:getConfig()[key].value
end

function ShareStreetCfg:getAreaCount()
    return #self._areaCfgList
end

function ShareStreetCfg:getAreaConfigByIndex(index)
    return self._areaCfgList[index]
end

function ShareStreetCfg:getAreaConfigById(id)
    return self._areaCfgMap[id]
end

function ShareStreetCfg:getAreaConfigList()
    return self._areaCfgList
end

function ShareStreetCfg:getUpgradeConfigList()
    return self._upgradeCfgList
end

function ShareStreetCfg:getUpgradeConfigById(id)
    return self._upgradeCfgMap[id]
end

function ShareStreetCfg:getAllSceneItem()
    return share_street_position:getConfig().dataList
end

function ShareStreetCfg:getSceneItem(pos)
    return share_street_position:getConfig()[pos]
end

function ShareStreetCfg:getSceneItemByFurType(subTypeId)
    local list = share_street_position:getConfig().dataList
    for i, cfg in ipairs(list) do
        if cfg.furSubType == subTypeId then
            return cfg
        end
    end
end

function ShareStreetCfg.getSkinPath(itemId)
    return string.format("scene/pjab_scene_prefab/block/skin/bg%d.prefab", itemId)
end

function ShareStreetCfg.getUnlockPath(itemId)
    return string.format("scene/pjab_scene_prefab/block/skin/unlock%d.prefab", itemId)
end

function ShareStreetCfg.getWeilanPath(itemId)
    return string.format("scene/pjab_scene_prefab/block/skin/weilan%d.prefab", itemId)
end

ShareStreetCfg.instance = ShareStreetCfg.New()
return ShareStreetCfg