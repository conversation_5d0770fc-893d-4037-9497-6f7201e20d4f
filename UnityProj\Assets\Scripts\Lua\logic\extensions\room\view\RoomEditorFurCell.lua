module("logic.extensions.room.view.RoomEditorFurCell", package.seeall)
local RoomEditorFurCell = class("RoomEditorFurCell", ListBinderDragCell, BaseLuaComponent)

function RoomEditorFurCell:Awake()
	RoomEditorFurCell.super.Awake(self)
	self._spNum = self:getGo("down/spNum")
	self._txtNum = self:getText("down/spNum/txtNum")
	self._txtComfort = self:getText("down/txtComfort")
	self._imgFree = self:getGo("freeGo")
	self._imgNewBubble = self:getGo("imgNewBubble")
	self._iconComfort = self:getGo("down/txtComfort/iconComfort")
	self._iconFood = self:getGo("down/txtComfort/iconFood")
	self._iconSize = self:getGo("down/txtComfort/iconSize")
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnEditItemNumChange, self._onNumChange, self)
	local dragRotation = 30
	if GameUtils.getCommonConfig("DragFurnitureOutRotation") then
		dragRotation = tonumber(GameUtils.getCommonConfig("DragFurnitureOutRotation"))
	end
	self.__yEdge = math.tan(dragRotation / 180 * math.pi) * self:_xEdge()
	self._imgShare = self:getGo("imgShare")
end

function RoomEditorFurCell:onSetMo(mo)
	self._furnitureMO = mo
	self._furniture = mo:getDefine()
	print("setFurMo :", self._furniture.id, self._furniture.name)
	ItemService.instance:setIcon(self._go, self._furniture)
	self._spNum:SetActive(self._listView.showNum and self._listView.showNum())
	self._imgShare:SetActive(self._furnitureMO.isShare)
	local num = RoomEditModel.instance:getItemNum(self._furniture.id, self._furnitureMO.isShare)
	if num > 99 then
		num = "99+"
	end
	self._txtNum.text = num
	local foodConfig = FoodConfig.getFoodById(self._furniture.id)
	local isFood = foodConfig ~= nil
	local showSize = self:_showSize()
	if showSize then
		local w, h = RoomConfig.getFurnitureSize(self._furniture.id)
		self._txtComfort.text = w .. "x" .. h
	else
		if isFood then
			self._txtComfort.text = foodConfig.count
		else
			local comfort = self._furniture.comfort
			if self._furniture.ext and self._furniture.ext.workShop then
				local workShop = WorkshopService:getWorskhopMoById(self._furniture.ext.workShop)
				if workShop == nil or workShop.level == 0 then
					comfort = WorkshopConfig.getWorkshopUpgradeCfg(self._furniture.ext.workShop) [1].comfort
				else
					comfort = WorkshopConfig.getWorkshopUpgradeCfg(workShop.id) [workShop.level].comfort
				end
			end
			self._txtComfort.text = comfort
		end
	end
	self._txtComfort.gameObject:SetActive(not RoomConfig.isSurface(mo.id))
	self._imgNewBubble:SetActive(ItemService.instance:getNewState(self._furniture.id))
	self._iconComfort:SetActive(not showSize and not isFood)
	self._iconFood:SetActive(not showSize and isFood)
	self._iconSize:SetActive(showSize)
	self._imgFree:SetActive(self._furniture.hasCapacity)
end

function RoomEditorFurCell:_showSize()
	return self._listView.sortType == RoomEditorSorter.SortType.Size
end

function RoomEditorFurCell:_xEdge()
	return 30
end

function RoomEditorFurCell:_onNumChange(id, num)
	if self._furniture and self._furniture.id == id then
		-- local num = RoomEditModel.instance:getItemNum(self._furniture.id)
		if num > 99 then
			num = "99+"
		end
		self._txtNum.text = num
	end
end

function RoomEditorFurCell:_handleClick()
	if self._listView.onClickCell then
		self._listView:onClickCell(self._furnitureMO)
	end
end

function RoomEditorFurCell:_handleBeginDrag(event)
	if self._listView.onBeginDragCell then
		self._listView:onBeginDragCell(self._furnitureMO, event.position)
	end
end

function RoomEditorFurCell:_handleDrag(event)
	if self._listView.onDragCell then
		if not self._listView:onDragCell(self._furnitureMO, event.position) then
			self:_onDragScrollRect(event)
		end
	end
end

function RoomEditorFurCell:_handleEndDrag(event)
	if self._listView.onEndDragCell then
		self._listView:onEndDragCell(self._furnitureMO, event.position)
	end
end

function RoomEditorFurCell:OnDestroy()
	RoomEditorFurCell.super.OnDestroy(self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnEditItemNumChange, self._onNumChange, self)
end

return RoomEditorFurCell 