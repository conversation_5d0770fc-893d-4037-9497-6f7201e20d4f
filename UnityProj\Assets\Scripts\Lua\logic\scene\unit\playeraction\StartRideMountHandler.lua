module("logic.scene.unit.playeraction.StartRideMountHandler",package.seeall)
local StartRideMountHandler = class("StartRideMountHandler", PlayerActionHandlerBase)
StartRideMountHandler.CushionUnitNamePrefix = "Mount_"

function StartRideMountHandler:onStart()
    self.scene = SceneManager.instance:getCurScene()
	self.unitFactory = self.scene.unitFactory
    self.mountId = tonumber(self.info.params[1])
	self.dir = 1
	local unitId = StartRideMountHandler.CushionUnitNamePrefix .. self.unit.id
	self.mount = self.unitFactory:addUnit(SceneUnitType.SeaMountUnit, {id = unitId, ownerUnit=self.unit, mountId = self.mountId})
	self.mount.dir = self.dir
	-- self.mount:setMountId(self.mountId)
	self.mount:setView(self.mountId, self.dir, self._onLoadUnit, self)
	self.unit:addListener(UnitNotify.DirectionChange, self.onDirChange, self)
	self.unit.walkSoundComp:setSpecialMove(true)
end

function StartRideMountHandler:onStop()
	if self.scene and self.unit.id == UserInfo.userId then
		self.scene:setOverrideClick(nil)
	end
	if self.mount == nil then return end
	self.unitFactory:removeUnit(SceneUnitType.SeaMountUnit, StartRideMountHandler.CushionUnitNamePrefix .. self.unit.id)
	self.mount = nil
	self.unitFactory = nil
	self.scene = nil
	self.unit.walkSoundComp:setSpecialMove(false)
	self.unit:removeListener(UnitNotify.DirectionChange, self.onDirChange, self)
end

function StartRideMountHandler:onDirChange()
	local dir = self.unit:getDirection()
	self.mount:setDir(dir)
end

function StartRideMountHandler:getSitPos(userId)
	return 0
end

function StartRideMountHandler:hasPlayer(sitPos)
	return not string.nilorempty(self.info.params[sitPos + 2])
end

function StartRideMountHandler:onUpdate()
	-- local nextPos = self:getSitPos(self.unit.id)
	-- if self.currentPos == nextPos then return end
	-- self.currentPos = nextPos
	-- self.mount:setPlayer(self.unit, nextPos)
end

function StartRideMountHandler:_onLoadUnit()
	if self.mount == nil then return end
	self.currentPos = self:getSitPos(self.unit.id)
	self.mount:setPlayer(self.unit, self.currentPos)
	if self.unit.id == UserInfo.userId then
		self.scene:setOverrideClick(self.onClickEmpty, self)
	end
	self.mount:addClickPosListener(self._onClickSitPos, self)
end

function StartRideMountHandler:onClickEmpty()
	--DialogHelper.showConfirmDlg(lang("是否收回坐骑？"), function(isOk)
	--	if isOk then
	--
	--	end
	--end)
	print("onClickEmpty")
	SceneController.instance:stopAction()
end

function StartRideMountHandler:_onClickSitPos()
	if self.unit.isUser then
		return
	end
	SceneController.instance:joinRideMount(self.unit.id, self.dir)
end

function StartRideMountHandler:_meIsInCushion()
	return self:getSitPos() > 0
end

function StartRideMountHandler:onStartMove()
	self.unit.skinView:setAnimation("dhs_jump_1", true)
end

function StartRideMountHandler:onStopMove()
	self.unit.skinView:setAnimation("liaoli", true)
end
return StartRideMountHandler