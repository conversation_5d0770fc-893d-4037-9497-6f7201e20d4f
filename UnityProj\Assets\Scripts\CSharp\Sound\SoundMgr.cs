using System;
using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using LuaInterface;
using Framework;
using FMODUnity;
using FMOD.Studio;
using System.IO;
using Framework.HotUpdate;

public class SoundMgr : SingletonMonoBehaviour<SoundMgr>
{
    [HideInInspector]
    public string SOUND_PATH = "sound";
    [HideInInspector]
    public string BGM_EVENT = "event:/music/music";
    [HideInInspector]
    public string BGM_EVENT_PARAM = "music_switch";
    [HideInInspector]
    public string BGM_TIME_PARAM = "bgm_day_night";
    [HideInInspector]
    public string BUS_BGM_PATH = "bus:/music";
    [HideInInspector]
    public string BUS_EFFECT_PATH = "bus:/effect";
    [HideInInspector]
    public string BUS_WALK_PATH = "bus:/footstep";
    [HideInInspector]
    public string BUS_VOICE_PATH = "bus:/vo";
    [HideInInspector]
    public static float DEFAULT_BGM_VOLUME = 1f;
    [HideInInspector]
    public static float DEFAULT_SCENE_VOLUME_RATE = 1f;
    [HideInInspector]
    public static float DEFAULT_EFFECT_VOLUME = 1f;
    [HideInInspector]
    public static float WeakenRatio = 10;
    [HideInInspector]
    public GameObject _eventContain;
    [HideInInspector]
    public bool isQuitting = false;
    [HideInInspector]
    public EventInstance _defaultClickInstance;
    [HideInInspector]
    public Bus _bgmBus;
    [HideInInspector]
    public Bus _effectBus;
    [HideInInspector]
    public bool _isBgmMute;
    [HideInInspector]
    public bool _isEffectMute;
    [HideInInspector]
    public float _bgmVolume = DEFAULT_BGM_VOLUME;
    [HideInInspector]
    public float _sceneVolumeRate = DEFAULT_SCENE_VOLUME_RATE;
    [HideInInspector]
    public float _effectVolume = DEFAULT_EFFECT_VOLUME;
    [HideInInspector]
    public string _defaultClickEvent;


    protected override void Awake()
    {
        base.Awake();
        _eventContain = new GameObject("SoundEventInsContain");
        _eventContain.AddComponent<StudioListener>();
        DontDestroyOnLoad(_eventContain);
    }

    void OnApplicationQuit()
    {
        isQuitting = true;
    }

    protected override void OnDestroy()
    {
        if (isQuitting) return;

        ClearBank();
    }

    #region 加载
    public void LoadBank(string bankName)
    {
        string resPath = string.Format("{0}/{1}.bank", SOUND_PATH, bankName);
#if UNITY_EDITOR
        string path = FileUtils.Instance.FullPathForFile(resPath, ResourceType.BYTES);
#elif UNITY_ANDROID
        bool isLocalNew = VersionMgr.Instance.IsLocalCacheFile(resPath);
        string path;
        if (isLocalNew)
        {
            path = FileUtils.LocalResRootPath + resPath;
        }
        else
        {
            path = string.Format("file:///android_asset/{0}/{1}/{2}.bank", OSDef.RunOS, SOUND_PATH, bankName);
        }
#else
        string path = FileUtils.Instance.FullPathForFile(resPath, ResourceType.BYTES);
#endif
        RuntimeManager.LoadBank(bankName, path);
    }

    public void OnAllBankLoaded()
    {
        _bgmBus = RuntimeManager.GetBus(BUS_BGM_PATH);
        _effectBus = RuntimeManager.GetBus(BUS_EFFECT_PATH);
    }

    public void ClearBank()
    {
        RuntimeManager.ClearBank();
    }
    
    public void CacheSound(string evt)
    {
        if (!string.IsNullOrEmpty(evt))
        {
            EventDescription desc = RuntimeManager.GetEventDescription(evt);
            if (desc.isValid())
            {
                desc.loadSampleData();
            }
        }
    }

    public void UnCacheSound(string evt)
    {
        if (!string.IsNullOrEmpty(evt))
        {
            EventDescription desc = RuntimeManager.GetEventDescription(evt);
            if (desc.isValid())
            {
                desc.unloadSampleData();
            }
        }
    }

    public void CacheDefaultClickSound(string evt)
    {
        if (string.IsNullOrEmpty(evt))
        {
            return;
        }
        _defaultClickEvent = evt;
        EventDescription desc = RuntimeManager.GetEventDescription(evt);
        if (desc.isValid())
        {
            desc.createInstance(out _defaultClickInstance);
        }
    }

    public void CacheBankSampleData(string bankName)
    {
        if (!string.IsNullOrEmpty(bankName))
        {
            RuntimeManager.LoadBankSampleData(bankName);
        }
    }

    public void UncacheBankSampleData(string bankName)
    {
        if (!string.IsNullOrEmpty(bankName))
        {
            RuntimeManager.UnloadBankSampleData(bankName);
        }
    }
    #endregion

    #region Sound
    //public void PlaySound(string evt)
    //{
    //    PlaySound(evt, null, null, null);
    //}

    //public void PlaySound(string evt, GameObject go)
    //{
    //    PlaySound(evt, go, null, null);
    //}

    //public void PlaySound(string evt, GameObject go, SoundEventParam[] arrayParam)
    //{
    //    PlaySound(evt, go, arrayParam, null);
    //}

    public void PlaySound(string evt, GameObject go, LuaTable arrayParam, Action soundLoadCallback)
    {
        if (string.IsNullOrEmpty(evt)) return;
        SoundEventParam[] soundEventParams = null;
        if (arrayParam != null)
        {
            var dict = arrayParam.ToDictTable();
            List<SoundEventParam> listParams = new List<SoundEventParam>();
            foreach (var item in dict)
            {
                listParams.Add(new SoundEventParam() { Name = (string)item.Key, Value = Convert.ToSingle(item.Value) });
            }
            soundEventParams = listParams.ToArray();
        }
        if (go != null || soundLoadCallback != null)
        {
            if (go == null) go = _eventContain;
            SoundEventInstance ins = GetOrCreateInstance(evt, go);
            if (ins)
            {
                ins.Play(soundEventParams, soundLoadCallback);
            }
        }
        else
        {
            EventDescription desc = RuntimeManager.GetEventDescription(evt);
            if (!desc.isValid()) return;
            bool isoneshot;
            desc.isOneshot(out isoneshot);
            if (isoneshot)
            {
                EventInstance ins;
                desc.createInstance(out ins);
                if (soundEventParams != null)
                {
                    foreach (SoundEventParam p in soundEventParams)
                    {
                        ins.setParameterByName(p.Name, p.Value);
                    }
                }
                bool is3d;
                desc.is3D(out is3d);
                if (is3d)
                {
                    ins.set3DAttributes(RuntimeUtils.To3DAttributes(_eventContain.transform.position));
                }
                ins.start();
                ins.release();
            }
            else
            {
                SoundEventInstance ins = GetOrCreateInstance(evt, _eventContain);
                if (ins)
                {
                    ins.Play(soundEventParams, soundLoadCallback);
                }
            }
        }
    }

    private SoundEventInstance GetOrCreateInstance(string evt, GameObject go)
    {
        foreach (SoundEventInstance ins in go.GetComponents<SoundEventInstance>())
        {
            if (ins.Event == evt)
            {
                var s = ins.State;
                if (s == PLAYBACK_STATE.STOPPED || s == PLAYBACK_STATE.STOPPING)
                {
                    return ins;
                }
            }
        }
        SoundEventInstance newIns = SoundEventInstance.Add(evt, go);
        return newIns;
    }


    public void PlayClickSound(string evt)
    {
        if (evt == _defaultClickEvent)
        {
            PlayDefaultClickSound();
        }
        else
        {
            PlaySound(evt, null, null, null);
        }
    }

    public void PlayDefaultClickSound()
    {
        if (_defaultClickInstance.isValid())
        {
            _defaultClickInstance.start();
        }
    }

    public void StopSound(string evt, GameObject go, int stopMode = 0)
    {
        if (isQuitting) return;
        if (go == null) go = _eventContain;
        foreach (SoundEventInstance ins in go.GetComponents<SoundEventInstance>())
        {
            if (ins.Event == evt)
            {
                var s = ins.State;
                if (s == PLAYBACK_STATE.STARTING || s == PLAYBACK_STATE.PLAYING)
                {
                    ins.Stop((FMOD.Studio.STOP_MODE)stopMode);
                    return;
                }
            }
        }
        EventDescription desc = RuntimeManager.GetEventDescription(evt);
        if (desc.getInstanceList(out EventInstance[] arr) == FMOD.RESULT.OK)
        {
            for (int i = 0; i < arr.Length; i++)
            {
                arr[i].stop((FMOD.Studio.STOP_MODE)stopMode);
            }
        } 

    }

    public void ForceStopAllSound()
    {
        if (isQuitting) return;
        FMOD.Studio.Bus masterBus;
        if (RuntimeManager.StudioSystem.getBus("bus:/", out masterBus) == FMOD.RESULT.OK)
        {
            masterBus.stopAllEvents(FMOD.Studio.STOP_MODE.IMMEDIATE);
        }
    }

    public void ClearSoundCache()
    {
        foreach (var ins in _eventContain.GetComponents<SoundEventInstance>())
        {
            Destroy(ins);
        }
    }

    #endregion

    #region Volume
    public void SetEffectMute(bool mute)
    {
        if (_isEffectMute != mute)
        {
            _isEffectMute = mute;
            _effectBus.setMute(_isEffectMute);
        }
    }
    public void SetEffectVolume(float v)
    {
        if (_effectVolume != v)
        {
            _effectVolume = v;
            _effectBus.setVolume(_effectVolume);
        }
    }

    public void SetBgmMute(bool mute)
    {
        if (_isBgmMute != mute)
        {
            _isBgmMute = mute;
            _bgmBus.setMute(_isBgmMute);
        }
    }
    
    public void SetBgmVolume(float v)
    {
        if (_bgmVolume != v)
        {
            _bgmVolume = v;
            _bgmBus.setVolume(_bgmVolume);
        }
    }

    public void SetWalkSoundMute(bool mute)
    {
        var bus = RuntimeManager.GetBus(BUS_WALK_PATH);
        bus.setMute(mute);
    }

    public void SetVoiceSoundMute(bool mute)
    {
        var bus = RuntimeManager.GetBus(BUS_VOICE_PATH);
        bus.setMute(mute);
    }
    #endregion

    #region Get
    public int GetEventLength(string eventName)
    {
        EventDescription eventDescription = RuntimeManager.GetEventDescription(eventName);
        int len;
        eventDescription.getLength(out len);
        return len;
    }
    
    public SoundEventInstance GetSoundEventInstance(string evt, GameObject go)
    {
        go = go ?go: _eventContain;
        foreach (SoundEventInstance ins in go.GetComponents<SoundEventInstance>())
        {
            if (ins.Event == evt)
            {
                return ins;
            }
        }
        return null;
    }
    #endregion
}
