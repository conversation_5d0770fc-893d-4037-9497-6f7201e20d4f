-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
local FOODEXTENSION_PB = require("logic.proto.FoodExtension_pb")
local USEREXTENSION_PB = require("logic.proto.UserExtension_pb")
local TRAVELLEREXTENSION_PB = require("logic.proto.TravellerExtension_pb")
local SCENEEXTENSION_PB = require("logic.proto.SceneExtension_pb")
module("logic.proto.HouseExtension_pb", package.seeall)


local tb = {}
tb.FURNITUREMARK_ENUM = protobuf.EnumDescriptor()
tb.FURNITUREMARK_MARK_MUSIC_SWITCH_ENUMITEM = protobuf.EnumValueDescriptor()
CHANGEDEFAULTVISITAREAREPLY_MSG = protobuf.Descriptor()
DECORATEHOUSEREQUEST_MSG = protobuf.Descriptor()
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD = protobuf.FieldDescriptor()
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD = protobuf.FieldDescriptor()
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD = protobuf.FieldDescriptor()
HOUSEFURNITURESWITCHMARKREQUEST_MSG = protobuf.Descriptor()
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD = protobuf.FieldDescriptor()
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD = protobuf.FieldDescriptor()
GETALLHOUSEINFOREQUEST_MSG = protobuf.Descriptor()
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
CHANGEHOUSEREQUEST_MSG = protobuf.Descriptor()
tb.CHANGEHOUSEREQUEST_AREAID_FIELD = protobuf.FieldDescriptor()
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
HOUSEFURNITURESWITCHMARKREPLY_MSG = protobuf.Descriptor()
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD = protobuf.FieldDescriptor()
PILLARINFONO_MSG = protobuf.Descriptor()
tb.PILLARINFONO_DEFINEID_FIELD = protobuf.FieldDescriptor()
tb.PILLARINFONO_ROTATION_FIELD = protobuf.FieldDescriptor()
CHANGEHOUSEREPLY_MSG = protobuf.Descriptor()
TAKEHEARTDROP_MSG = protobuf.Descriptor()
tb.TAKEHEARTDROP_ID_FIELD = protobuf.FieldDescriptor()
tb.TAKEHEARTDROP_DROPID_FIELD = protobuf.FieldDescriptor()
tb.TAKEHEARTDROP_DROPCOUNT_FIELD = protobuf.FieldDescriptor()
HOUSESHOWAREAINFO_MSG = protobuf.Descriptor()
tb.HOUSESHOWAREAINFO_AREAID_FIELD = protobuf.FieldDescriptor()
tb.HOUSESHOWAREAINFO_AREANAME_FIELD = protobuf.FieldDescriptor()
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD = protobuf.FieldDescriptor()
tb.HOUSESHOWAREAINFO_LEVEL_FIELD = protobuf.FieldDescriptor()
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD = protobuf.FieldDescriptor()
HOUSEFURNITURECHANGECLOTHESREPLY_MSG = protobuf.Descriptor()
COMFORTVALUENO_MSG = protobuf.Descriptor()
tb.COMFORTVALUENO_PLUS_FIELD = protobuf.FieldDescriptor()
tb.COMFORTVALUENO_MINUS_FIELD = protobuf.FieldDescriptor()
tb.COMFORTVALUENO_GRID_FIELD = protobuf.FieldDescriptor()
GETALLUNLOCKHOUSEREQUEST_MSG = protobuf.Descriptor()
GETDEFAULTVISITAREAREQUEST_MSG = protobuf.Descriptor()
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
DECORATEHOUSEREPLY_MSG = protobuf.Descriptor()
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD = protobuf.FieldDescriptor()
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
ROOMINFONO_MSG = protobuf.Descriptor()
tb.ROOMINFONO_ROOMID_FIELD = protobuf.FieldDescriptor()
tb.ROOMINFONO_FLOOR_FIELD = protobuf.FieldDescriptor()
tb.ROOMINFONO_WALLPAPER_FIELD = protobuf.FieldDescriptor()
GETALLHOUSEINFOREPLY_MSG = protobuf.Descriptor()
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD = protobuf.FieldDescriptor()
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD = protobuf.FieldDescriptor()
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD = protobuf.FieldDescriptor()
GETHOUSEINFOREQUEST_MSG = protobuf.Descriptor()
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREQUEST_NPC_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD = protobuf.FieldDescriptor()
SETHOUSENAMEREPLY_MSG = protobuf.Descriptor()
CHANGEDEFAULTVISITAREAREQUEST_MSG = protobuf.Descriptor()
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
CHANGESHOWAREAEFFECTSREQUEST_MSG = protobuf.Descriptor()
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD = protobuf.FieldDescriptor()
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD = protobuf.FieldDescriptor()
SETHOUSENAMEREQUEST_MSG = protobuf.Descriptor()
tb.SETHOUSENAMEREQUEST_AREAID_FIELD = protobuf.FieldDescriptor()
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD = protobuf.FieldDescriptor()
HOUSEUPGRADEREQUEST_MSG = protobuf.Descriptor()
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG = protobuf.Descriptor()
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD = protobuf.FieldDescriptor()
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD = protobuf.FieldDescriptor()
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG = protobuf.Descriptor()
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD = protobuf.FieldDescriptor()
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD = protobuf.FieldDescriptor()
GETALLUNLOCKHOUSEREPLY_MSG = protobuf.Descriptor()
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD = protobuf.FieldDescriptor()
HOUSEUPGRADEREPLY_MSG = protobuf.Descriptor()
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG = protobuf.Descriptor()
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD = protobuf.FieldDescriptor()
FURNITUREINFONO_MSG = protobuf.Descriptor()
tb.FURNITUREINFONO_ID_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_FURNITUREID_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_POSX_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_POSY_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_ROTATION_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_FURNITUREMARK_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_FLOOR_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_SHARE_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_ACCESSTAG_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_AREAID_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_CLOTHES_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFONO_PILLARID_FIELD = protobuf.FieldDescriptor()
HOUSEINFONO_MSG = protobuf.Descriptor()
tb.HOUSEINFONO_HOUSEID_FIELD = protobuf.FieldDescriptor()
tb.HOUSEINFONO_ROOMS_FIELD = protobuf.FieldDescriptor()
tb.HOUSEINFONO_FURNITURE_FIELD = protobuf.FieldDescriptor()
tb.HOUSEINFONO_BACKGROUND_FIELD = protobuf.FieldDescriptor()
tb.HOUSEINFONO_LEVEL_FIELD = protobuf.FieldDescriptor()
tb.HOUSEINFONO_HOUSETYPE_FIELD = protobuf.FieldDescriptor()
tb.HOUSEINFONO_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
HOUSECHANGEDPUSH_MSG = protobuf.Descriptor()
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.HOUSECHANGEDPUSH_COMFORT_FIELD = protobuf.FieldDescriptor()
GETDEFAULTVISITAREAREPLY_MSG = protobuf.Descriptor()
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD = protobuf.FieldDescriptor()
CHANGESHOWAREAEFFECTSREPLY_MSG = protobuf.Descriptor()
HOUSETAKEHEARTREPLY_MSG = protobuf.Descriptor()
tb.HOUSETAKEHEARTREPLY_HEART_FIELD = protobuf.FieldDescriptor()
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD = protobuf.FieldDescriptor()
UNLOCKHOUSEREPLY_MSG = protobuf.Descriptor()
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD = protobuf.FieldDescriptor()
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD = protobuf.FieldDescriptor()
GETHOUSEINFOREPLY_MSG = protobuf.Descriptor()
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_COMFORT_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_SECTORS_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_HEART_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD = protobuf.FieldDescriptor()
tb.GETHOUSEINFOREPLY_STREETID_FIELD = protobuf.FieldDescriptor()
UNLOCKHOUSEREQUEST_MSG = protobuf.Descriptor()
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD = protobuf.FieldDescriptor()
CHANGEELFHOUSEREQUEST_MSG = protobuf.Descriptor()
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
HOUSETAKEHEARTREQUEST_MSG = protobuf.Descriptor()
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD = protobuf.FieldDescriptor()
CHANGEELFHOUSEREPLY_MSG = protobuf.Descriptor()

tb.FURNITUREMARK_MARK_MUSIC_SWITCH_ENUMITEM.name = "MARK_MUSIC_SWITCH"
tb.FURNITUREMARK_MARK_MUSIC_SWITCH_ENUMITEM.index = 0
tb.FURNITUREMARK_MARK_MUSIC_SWITCH_ENUMITEM.number = 1
tb.FURNITUREMARK_ENUM.name = "FurnitureMark"
tb.FURNITUREMARK_ENUM.full_name = ".FurnitureMark"
tb.FURNITUREMARK_ENUM.values = {tb.FURNITUREMARK_MARK_MUSIC_SWITCH_ENUMITEM}
CHANGEDEFAULTVISITAREAREPLY_MSG.name = "ChangeDefaultVisitAreaReply"
CHANGEDEFAULTVISITAREAREPLY_MSG.full_name = ".ChangeDefaultVisitAreaReply"
CHANGEDEFAULTVISITAREAREPLY_MSG.filename = "HouseExtension"
CHANGEDEFAULTVISITAREAREPLY_MSG.nested_types = {}
CHANGEDEFAULTVISITAREAREPLY_MSG.enum_types = {}
CHANGEDEFAULTVISITAREAREPLY_MSG.fields = {}
CHANGEDEFAULTVISITAREAREPLY_MSG.is_extendable = false
CHANGEDEFAULTVISITAREAREPLY_MSG.extensions = {}
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.name = "houseInfo"
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.full_name = ".DecorateHouseRequest.houseInfo"
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.number = 1
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.index = 0
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.label = 2
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.has_default_value = false
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.default_value = nil
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.message_type = HOUSEINFONO_MSG
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.type = 11
tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD.cpp_type = 10

tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.name = "sellingFurnitures"
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.full_name = ".DecorateHouseRequest.sellingFurnitures"
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.number = 2
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.index = 1
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.label = 3
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.has_default_value = false
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.default_value = {}
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.type = 5
tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD.cpp_type = 1

tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.name = "takeBackFurnitures"
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.full_name = ".DecorateHouseRequest.takeBackFurnitures"
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.number = 3
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.index = 2
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.label = 3
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.has_default_value = false
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.default_value = {}
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.type = 5
tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD.cpp_type = 1

tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.name = "itemLockPassword"
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.full_name = ".DecorateHouseRequest.itemLockPassword"
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.number = 4
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.index = 3
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.label = 1
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.has_default_value = false
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.default_value = ""
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.type = 9
tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD.cpp_type = 9

DECORATEHOUSEREQUEST_MSG.name = "DecorateHouseRequest"
DECORATEHOUSEREQUEST_MSG.full_name = ".DecorateHouseRequest"
DECORATEHOUSEREQUEST_MSG.filename = "HouseExtension"
DECORATEHOUSEREQUEST_MSG.nested_types = {}
DECORATEHOUSEREQUEST_MSG.enum_types = {}
DECORATEHOUSEREQUEST_MSG.fields = {tb.DECORATEHOUSEREQUEST_HOUSEINFO_FIELD, tb.DECORATEHOUSEREQUEST_SELLINGFURNITURES_FIELD, tb.DECORATEHOUSEREQUEST_TAKEBACKFURNITURES_FIELD, tb.DECORATEHOUSEREQUEST_ITEMLOCKPASSWORD_FIELD}
DECORATEHOUSEREQUEST_MSG.is_extendable = false
DECORATEHOUSEREQUEST_MSG.extensions = {}
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.name = "houseId"
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.full_name = ".HouseFurnitureSwitchMarkRequest.houseId"
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.number = 1
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.index = 0
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.label = 1
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.has_default_value = false
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.default_value = 0
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.type = 5
tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD.cpp_type = 1

tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.name = "furnitureUniqueId"
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.full_name = ".HouseFurnitureSwitchMarkRequest.furnitureUniqueId"
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.number = 2
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.index = 1
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.label = 1
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.has_default_value = false
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.default_value = 0
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.type = 5
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.cpp_type = 1

tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.name = "furnitureMark"
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.full_name = ".HouseFurnitureSwitchMarkRequest.furnitureMark"
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.number = 3
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.index = 2
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.label = 1
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.has_default_value = false
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.default_value = 0
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.type = 5
tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.cpp_type = 1

HOUSEFURNITURESWITCHMARKREQUEST_MSG.name = "HouseFurnitureSwitchMarkRequest"
HOUSEFURNITURESWITCHMARKREQUEST_MSG.full_name = ".HouseFurnitureSwitchMarkRequest"
HOUSEFURNITURESWITCHMARKREQUEST_MSG.filename = "HouseExtension"
HOUSEFURNITURESWITCHMARKREQUEST_MSG.nested_types = {}
HOUSEFURNITURESWITCHMARKREQUEST_MSG.enum_types = {}
HOUSEFURNITURESWITCHMARKREQUEST_MSG.fields = {tb.HOUSEFURNITURESWITCHMARKREQUEST_HOUSEID_FIELD, tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD, tb.HOUSEFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD}
HOUSEFURNITURESWITCHMARKREQUEST_MSG.is_extendable = false
HOUSEFURNITURESWITCHMARKREQUEST_MSG.extensions = {}
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.name = "userId"
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.full_name = ".GetAllHouseInfoRequest.userId"
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.number = 1
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.index = 0
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.label = 1
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.has_default_value = false
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.default_value = ""
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.type = 9
tb.GETALLHOUSEINFOREQUEST_USERID_FIELD.cpp_type = 9

GETALLHOUSEINFOREQUEST_MSG.name = "GetAllHouseInfoRequest"
GETALLHOUSEINFOREQUEST_MSG.full_name = ".GetAllHouseInfoRequest"
GETALLHOUSEINFOREQUEST_MSG.filename = "HouseExtension"
GETALLHOUSEINFOREQUEST_MSG.nested_types = {}
GETALLHOUSEINFOREQUEST_MSG.enum_types = {}
GETALLHOUSEINFOREQUEST_MSG.fields = {tb.GETALLHOUSEINFOREQUEST_USERID_FIELD}
GETALLHOUSEINFOREQUEST_MSG.is_extendable = false
GETALLHOUSEINFOREQUEST_MSG.extensions = {}
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.name = "areaId"
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.full_name = ".ChangeHouseRequest.areaId"
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.number = 1
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.index = 0
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.label = 1
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.has_default_value = false
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.default_value = 0
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.type = 5
tb.CHANGEHOUSEREQUEST_AREAID_FIELD.cpp_type = 1

tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.name = "houseId"
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.full_name = ".ChangeHouseRequest.houseId"
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.number = 2
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.index = 1
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.label = 1
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.has_default_value = false
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.default_value = 0
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.type = 5
tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD.cpp_type = 1

CHANGEHOUSEREQUEST_MSG.name = "ChangeHouseRequest"
CHANGEHOUSEREQUEST_MSG.full_name = ".ChangeHouseRequest"
CHANGEHOUSEREQUEST_MSG.filename = "HouseExtension"
CHANGEHOUSEREQUEST_MSG.nested_types = {}
CHANGEHOUSEREQUEST_MSG.enum_types = {}
CHANGEHOUSEREQUEST_MSG.fields = {tb.CHANGEHOUSEREQUEST_AREAID_FIELD, tb.CHANGEHOUSEREQUEST_HOUSEID_FIELD}
CHANGEHOUSEREQUEST_MSG.is_extendable = false
CHANGEHOUSEREQUEST_MSG.extensions = {}
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.name = "updateFurnitureInfo"
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.full_name = ".HouseFurnitureSwitchMarkReply.updateFurnitureInfo"
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.number = 1
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.index = 0
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.label = 2
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.has_default_value = false
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.default_value = nil
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.message_type = FURNITUREINFONO_MSG
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.type = 11
tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.cpp_type = 10

HOUSEFURNITURESWITCHMARKREPLY_MSG.name = "HouseFurnitureSwitchMarkReply"
HOUSEFURNITURESWITCHMARKREPLY_MSG.full_name = ".HouseFurnitureSwitchMarkReply"
HOUSEFURNITURESWITCHMARKREPLY_MSG.filename = "HouseExtension"
HOUSEFURNITURESWITCHMARKREPLY_MSG.nested_types = {}
HOUSEFURNITURESWITCHMARKREPLY_MSG.enum_types = {}
HOUSEFURNITURESWITCHMARKREPLY_MSG.fields = {tb.HOUSEFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD}
HOUSEFURNITURESWITCHMARKREPLY_MSG.is_extendable = false
HOUSEFURNITURESWITCHMARKREPLY_MSG.extensions = {}
tb.PILLARINFONO_DEFINEID_FIELD.name = "defineId"
tb.PILLARINFONO_DEFINEID_FIELD.full_name = ".PillarInfoNO.defineId"
tb.PILLARINFONO_DEFINEID_FIELD.number = 1
tb.PILLARINFONO_DEFINEID_FIELD.index = 0
tb.PILLARINFONO_DEFINEID_FIELD.label = 1
tb.PILLARINFONO_DEFINEID_FIELD.has_default_value = false
tb.PILLARINFONO_DEFINEID_FIELD.default_value = 0
tb.PILLARINFONO_DEFINEID_FIELD.type = 5
tb.PILLARINFONO_DEFINEID_FIELD.cpp_type = 1

tb.PILLARINFONO_ROTATION_FIELD.name = "rotation"
tb.PILLARINFONO_ROTATION_FIELD.full_name = ".PillarInfoNO.rotation"
tb.PILLARINFONO_ROTATION_FIELD.number = 2
tb.PILLARINFONO_ROTATION_FIELD.index = 1
tb.PILLARINFONO_ROTATION_FIELD.label = 1
tb.PILLARINFONO_ROTATION_FIELD.has_default_value = false
tb.PILLARINFONO_ROTATION_FIELD.default_value = 0
tb.PILLARINFONO_ROTATION_FIELD.type = 17
tb.PILLARINFONO_ROTATION_FIELD.cpp_type = 1

PILLARINFONO_MSG.name = "PillarInfoNO"
PILLARINFONO_MSG.full_name = ".PillarInfoNO"
PILLARINFONO_MSG.filename = "HouseExtension"
PILLARINFONO_MSG.nested_types = {}
PILLARINFONO_MSG.enum_types = {}
PILLARINFONO_MSG.fields = {tb.PILLARINFONO_DEFINEID_FIELD, tb.PILLARINFONO_ROTATION_FIELD}
PILLARINFONO_MSG.is_extendable = false
PILLARINFONO_MSG.extensions = {}
CHANGEHOUSEREPLY_MSG.name = "ChangeHouseReply"
CHANGEHOUSEREPLY_MSG.full_name = ".ChangeHouseReply"
CHANGEHOUSEREPLY_MSG.filename = "HouseExtension"
CHANGEHOUSEREPLY_MSG.nested_types = {}
CHANGEHOUSEREPLY_MSG.enum_types = {}
CHANGEHOUSEREPLY_MSG.fields = {}
CHANGEHOUSEREPLY_MSG.is_extendable = false
CHANGEHOUSEREPLY_MSG.extensions = {}
tb.TAKEHEARTDROP_ID_FIELD.name = "id"
tb.TAKEHEARTDROP_ID_FIELD.full_name = ".TakeHeartDrop.id"
tb.TAKEHEARTDROP_ID_FIELD.number = 1
tb.TAKEHEARTDROP_ID_FIELD.index = 0
tb.TAKEHEARTDROP_ID_FIELD.label = 1
tb.TAKEHEARTDROP_ID_FIELD.has_default_value = false
tb.TAKEHEARTDROP_ID_FIELD.default_value = 0
tb.TAKEHEARTDROP_ID_FIELD.type = 5
tb.TAKEHEARTDROP_ID_FIELD.cpp_type = 1

tb.TAKEHEARTDROP_DROPID_FIELD.name = "dropId"
tb.TAKEHEARTDROP_DROPID_FIELD.full_name = ".TakeHeartDrop.dropId"
tb.TAKEHEARTDROP_DROPID_FIELD.number = 2
tb.TAKEHEARTDROP_DROPID_FIELD.index = 1
tb.TAKEHEARTDROP_DROPID_FIELD.label = 1
tb.TAKEHEARTDROP_DROPID_FIELD.has_default_value = false
tb.TAKEHEARTDROP_DROPID_FIELD.default_value = 0
tb.TAKEHEARTDROP_DROPID_FIELD.type = 5
tb.TAKEHEARTDROP_DROPID_FIELD.cpp_type = 1

tb.TAKEHEARTDROP_DROPCOUNT_FIELD.name = "dropCount"
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.full_name = ".TakeHeartDrop.dropCount"
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.number = 3
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.index = 2
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.label = 1
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.has_default_value = false
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.default_value = 0
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.type = 5
tb.TAKEHEARTDROP_DROPCOUNT_FIELD.cpp_type = 1

TAKEHEARTDROP_MSG.name = "TakeHeartDrop"
TAKEHEARTDROP_MSG.full_name = ".TakeHeartDrop"
TAKEHEARTDROP_MSG.filename = "HouseExtension"
TAKEHEARTDROP_MSG.nested_types = {}
TAKEHEARTDROP_MSG.enum_types = {}
TAKEHEARTDROP_MSG.fields = {tb.TAKEHEARTDROP_ID_FIELD, tb.TAKEHEARTDROP_DROPID_FIELD, tb.TAKEHEARTDROP_DROPCOUNT_FIELD}
TAKEHEARTDROP_MSG.is_extendable = false
TAKEHEARTDROP_MSG.extensions = {}
tb.HOUSESHOWAREAINFO_AREAID_FIELD.name = "areaId"
tb.HOUSESHOWAREAINFO_AREAID_FIELD.full_name = ".HouseShowAreaInfo.areaId"
tb.HOUSESHOWAREAINFO_AREAID_FIELD.number = 1
tb.HOUSESHOWAREAINFO_AREAID_FIELD.index = 0
tb.HOUSESHOWAREAINFO_AREAID_FIELD.label = 2
tb.HOUSESHOWAREAINFO_AREAID_FIELD.has_default_value = false
tb.HOUSESHOWAREAINFO_AREAID_FIELD.default_value = 0
tb.HOUSESHOWAREAINFO_AREAID_FIELD.type = 5
tb.HOUSESHOWAREAINFO_AREAID_FIELD.cpp_type = 1

tb.HOUSESHOWAREAINFO_AREANAME_FIELD.name = "areaName"
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.full_name = ".HouseShowAreaInfo.areaName"
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.number = 2
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.index = 1
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.label = 1
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.has_default_value = false
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.default_value = ""
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.type = 9
tb.HOUSESHOWAREAINFO_AREANAME_FIELD.cpp_type = 9

tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.name = "houseId"
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.full_name = ".HouseShowAreaInfo.houseId"
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.number = 3
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.index = 2
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.label = 1
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.has_default_value = false
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.default_value = 0
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.type = 5
tb.HOUSESHOWAREAINFO_HOUSEID_FIELD.cpp_type = 1

tb.HOUSESHOWAREAINFO_LEVEL_FIELD.name = "level"
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.full_name = ".HouseShowAreaInfo.level"
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.number = 4
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.index = 3
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.label = 1
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.has_default_value = false
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.default_value = 0
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.type = 5
tb.HOUSESHOWAREAINFO_LEVEL_FIELD.cpp_type = 1

tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.name = "effectsItem"
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.full_name = ".HouseShowAreaInfo.effectsItem"
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.number = 5
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.index = 4
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.label = 1
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.has_default_value = false
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.default_value = 0
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.type = 5
tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD.cpp_type = 1

HOUSESHOWAREAINFO_MSG.name = "HouseShowAreaInfo"
HOUSESHOWAREAINFO_MSG.full_name = ".HouseShowAreaInfo"
HOUSESHOWAREAINFO_MSG.filename = "HouseExtension"
HOUSESHOWAREAINFO_MSG.nested_types = {}
HOUSESHOWAREAINFO_MSG.enum_types = {}
HOUSESHOWAREAINFO_MSG.fields = {tb.HOUSESHOWAREAINFO_AREAID_FIELD, tb.HOUSESHOWAREAINFO_AREANAME_FIELD, tb.HOUSESHOWAREAINFO_HOUSEID_FIELD, tb.HOUSESHOWAREAINFO_LEVEL_FIELD, tb.HOUSESHOWAREAINFO_EFFECTSITEM_FIELD}
HOUSESHOWAREAINFO_MSG.is_extendable = false
HOUSESHOWAREAINFO_MSG.extensions = {}
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.name = "HouseFurnitureChangeClothesReply"
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.full_name = ".HouseFurnitureChangeClothesReply"
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.filename = "HouseExtension"
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.nested_types = {}
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.enum_types = {}
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.fields = {}
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.is_extendable = false
HOUSEFURNITURECHANGECLOTHESREPLY_MSG.extensions = {}
tb.COMFORTVALUENO_PLUS_FIELD.name = "plus"
tb.COMFORTVALUENO_PLUS_FIELD.full_name = ".ComfortValueNO.plus"
tb.COMFORTVALUENO_PLUS_FIELD.number = 1
tb.COMFORTVALUENO_PLUS_FIELD.index = 0
tb.COMFORTVALUENO_PLUS_FIELD.label = 1
tb.COMFORTVALUENO_PLUS_FIELD.has_default_value = false
tb.COMFORTVALUENO_PLUS_FIELD.default_value = 0
tb.COMFORTVALUENO_PLUS_FIELD.type = 5
tb.COMFORTVALUENO_PLUS_FIELD.cpp_type = 1

tb.COMFORTVALUENO_MINUS_FIELD.name = "minus"
tb.COMFORTVALUENO_MINUS_FIELD.full_name = ".ComfortValueNO.minus"
tb.COMFORTVALUENO_MINUS_FIELD.number = 2
tb.COMFORTVALUENO_MINUS_FIELD.index = 1
tb.COMFORTVALUENO_MINUS_FIELD.label = 1
tb.COMFORTVALUENO_MINUS_FIELD.has_default_value = false
tb.COMFORTVALUENO_MINUS_FIELD.default_value = 0
tb.COMFORTVALUENO_MINUS_FIELD.type = 5
tb.COMFORTVALUENO_MINUS_FIELD.cpp_type = 1

tb.COMFORTVALUENO_GRID_FIELD.name = "grid"
tb.COMFORTVALUENO_GRID_FIELD.full_name = ".ComfortValueNO.grid"
tb.COMFORTVALUENO_GRID_FIELD.number = 3
tb.COMFORTVALUENO_GRID_FIELD.index = 2
tb.COMFORTVALUENO_GRID_FIELD.label = 1
tb.COMFORTVALUENO_GRID_FIELD.has_default_value = false
tb.COMFORTVALUENO_GRID_FIELD.default_value = 0
tb.COMFORTVALUENO_GRID_FIELD.type = 5
tb.COMFORTVALUENO_GRID_FIELD.cpp_type = 1

COMFORTVALUENO_MSG.name = "ComfortValueNO"
COMFORTVALUENO_MSG.full_name = ".ComfortValueNO"
COMFORTVALUENO_MSG.filename = "HouseExtension"
COMFORTVALUENO_MSG.nested_types = {}
COMFORTVALUENO_MSG.enum_types = {}
COMFORTVALUENO_MSG.fields = {tb.COMFORTVALUENO_PLUS_FIELD, tb.COMFORTVALUENO_MINUS_FIELD, tb.COMFORTVALUENO_GRID_FIELD}
COMFORTVALUENO_MSG.is_extendable = false
COMFORTVALUENO_MSG.extensions = {}
GETALLUNLOCKHOUSEREQUEST_MSG.name = "GetAllUnlockHouseRequest"
GETALLUNLOCKHOUSEREQUEST_MSG.full_name = ".GetAllUnlockHouseRequest"
GETALLUNLOCKHOUSEREQUEST_MSG.filename = "HouseExtension"
GETALLUNLOCKHOUSEREQUEST_MSG.nested_types = {}
GETALLUNLOCKHOUSEREQUEST_MSG.enum_types = {}
GETALLUNLOCKHOUSEREQUEST_MSG.fields = {}
GETALLUNLOCKHOUSEREQUEST_MSG.is_extendable = false
GETALLUNLOCKHOUSEREQUEST_MSG.extensions = {}
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.name = "userId"
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.full_name = ".GetDefaultVisitAreaRequest.userId"
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.number = 1
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.index = 0
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.label = 1
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.has_default_value = false
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.default_value = ""
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.type = 9
tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD.cpp_type = 9

GETDEFAULTVISITAREAREQUEST_MSG.name = "GetDefaultVisitAreaRequest"
GETDEFAULTVISITAREAREQUEST_MSG.full_name = ".GetDefaultVisitAreaRequest"
GETDEFAULTVISITAREAREQUEST_MSG.filename = "HouseExtension"
GETDEFAULTVISITAREAREQUEST_MSG.nested_types = {}
GETDEFAULTVISITAREAREQUEST_MSG.enum_types = {}
GETDEFAULTVISITAREAREQUEST_MSG.fields = {tb.GETDEFAULTVISITAREAREQUEST_USERID_FIELD}
GETDEFAULTVISITAREAREQUEST_MSG.is_extendable = false
GETDEFAULTVISITAREAREQUEST_MSG.extensions = {}
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.name = "furnitureInfo"
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.full_name = ".DecorateHouseReply.furnitureInfo"
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.number = 1
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.index = 0
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.label = 3
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.has_default_value = false
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.default_value = {}
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.message_type = FURNITUREINFONO_MSG
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.type = 11
tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD.cpp_type = 10

tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.full_name = ".DecorateHouseReply.foodItemInfos"
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.number = 2
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.index = 1
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.label = 3
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.has_default_value = false
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.default_value = {}
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.type = 11
tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD.cpp_type = 10

DECORATEHOUSEREPLY_MSG.name = "DecorateHouseReply"
DECORATEHOUSEREPLY_MSG.full_name = ".DecorateHouseReply"
DECORATEHOUSEREPLY_MSG.filename = "HouseExtension"
DECORATEHOUSEREPLY_MSG.nested_types = {}
DECORATEHOUSEREPLY_MSG.enum_types = {}
DECORATEHOUSEREPLY_MSG.fields = {tb.DECORATEHOUSEREPLY_FURNITUREINFO_FIELD, tb.DECORATEHOUSEREPLY_FOODITEMINFOS_FIELD}
DECORATEHOUSEREPLY_MSG.is_extendable = false
DECORATEHOUSEREPLY_MSG.extensions = {}
tb.ROOMINFONO_ROOMID_FIELD.name = "roomId"
tb.ROOMINFONO_ROOMID_FIELD.full_name = ".RoomInfoNO.roomId"
tb.ROOMINFONO_ROOMID_FIELD.number = 1
tb.ROOMINFONO_ROOMID_FIELD.index = 0
tb.ROOMINFONO_ROOMID_FIELD.label = 2
tb.ROOMINFONO_ROOMID_FIELD.has_default_value = false
tb.ROOMINFONO_ROOMID_FIELD.default_value = 0
tb.ROOMINFONO_ROOMID_FIELD.type = 5
tb.ROOMINFONO_ROOMID_FIELD.cpp_type = 1

tb.ROOMINFONO_FLOOR_FIELD.name = "floor"
tb.ROOMINFONO_FLOOR_FIELD.full_name = ".RoomInfoNO.floor"
tb.ROOMINFONO_FLOOR_FIELD.number = 2
tb.ROOMINFONO_FLOOR_FIELD.index = 1
tb.ROOMINFONO_FLOOR_FIELD.label = 1
tb.ROOMINFONO_FLOOR_FIELD.has_default_value = false
tb.ROOMINFONO_FLOOR_FIELD.default_value = 0
tb.ROOMINFONO_FLOOR_FIELD.type = 5
tb.ROOMINFONO_FLOOR_FIELD.cpp_type = 1

tb.ROOMINFONO_WALLPAPER_FIELD.name = "wallpaper"
tb.ROOMINFONO_WALLPAPER_FIELD.full_name = ".RoomInfoNO.wallpaper"
tb.ROOMINFONO_WALLPAPER_FIELD.number = 3
tb.ROOMINFONO_WALLPAPER_FIELD.index = 2
tb.ROOMINFONO_WALLPAPER_FIELD.label = 1
tb.ROOMINFONO_WALLPAPER_FIELD.has_default_value = false
tb.ROOMINFONO_WALLPAPER_FIELD.default_value = 0
tb.ROOMINFONO_WALLPAPER_FIELD.type = 5
tb.ROOMINFONO_WALLPAPER_FIELD.cpp_type = 1

ROOMINFONO_MSG.name = "RoomInfoNO"
ROOMINFONO_MSG.full_name = ".RoomInfoNO"
ROOMINFONO_MSG.filename = "HouseExtension"
ROOMINFONO_MSG.nested_types = {}
ROOMINFONO_MSG.enum_types = {}
ROOMINFONO_MSG.fields = {tb.ROOMINFONO_ROOMID_FIELD, tb.ROOMINFONO_FLOOR_FIELD, tb.ROOMINFONO_WALLPAPER_FIELD}
ROOMINFONO_MSG.is_extendable = false
ROOMINFONO_MSG.extensions = {}
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.name = "houseInfo"
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.full_name = ".GetAllHouseInfoReply.houseInfo"
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.number = 1
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.index = 0
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.label = 3
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.has_default_value = false
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.default_value = {}
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.message_type = HOUSEINFONO_MSG
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.type = 11
tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD.cpp_type = 10

tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.name = "isAllowToEat"
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.full_name = ".GetAllHouseInfoReply.isAllowToEat"
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.number = 2
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.index = 1
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.label = 1
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.has_default_value = false
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.default_value = false
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.type = 8
tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.cpp_type = 7

tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.name = "houseShowArea"
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.full_name = ".GetAllHouseInfoReply.houseShowArea"
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.number = 3
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.index = 2
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.label = 3
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.has_default_value = false
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.default_value = {}
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.message_type = HOUSESHOWAREAINFO_MSG
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.type = 11
tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD.cpp_type = 10

tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.name = "defaultVisitScene"
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.full_name = ".GetAllHouseInfoReply.defaultVisitScene"
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.number = 4
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.index = 3
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.label = 1
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.has_default_value = false
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.default_value = 0
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.type = 5
tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD.cpp_type = 1

GETALLHOUSEINFOREPLY_MSG.name = "GetAllHouseInfoReply"
GETALLHOUSEINFOREPLY_MSG.full_name = ".GetAllHouseInfoReply"
GETALLHOUSEINFOREPLY_MSG.filename = "HouseExtension"
GETALLHOUSEINFOREPLY_MSG.nested_types = {}
GETALLHOUSEINFOREPLY_MSG.enum_types = {}
GETALLHOUSEINFOREPLY_MSG.fields = {tb.GETALLHOUSEINFOREPLY_HOUSEINFO_FIELD, tb.GETALLHOUSEINFOREPLY_ISALLOWTOEAT_FIELD, tb.GETALLHOUSEINFOREPLY_HOUSESHOWAREA_FIELD, tb.GETALLHOUSEINFOREPLY_DEFAULTVISITSCENE_FIELD}
GETALLHOUSEINFOREPLY_MSG.is_extendable = false
GETALLHOUSEINFOREPLY_MSG.extensions = {}
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.name = "houseId"
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.full_name = ".GetHouseInfoRequest.houseId"
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.number = 1
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.index = 0
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.label = 1
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.has_default_value = false
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.default_value = 0
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.type = 5
tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD.cpp_type = 1

tb.GETHOUSEINFOREQUEST_USERID_FIELD.name = "userId"
tb.GETHOUSEINFOREQUEST_USERID_FIELD.full_name = ".GetHouseInfoRequest.userId"
tb.GETHOUSEINFOREQUEST_USERID_FIELD.number = 2
tb.GETHOUSEINFOREQUEST_USERID_FIELD.index = 1
tb.GETHOUSEINFOREQUEST_USERID_FIELD.label = 1
tb.GETHOUSEINFOREQUEST_USERID_FIELD.has_default_value = false
tb.GETHOUSEINFOREQUEST_USERID_FIELD.default_value = ""
tb.GETHOUSEINFOREQUEST_USERID_FIELD.type = 9
tb.GETHOUSEINFOREQUEST_USERID_FIELD.cpp_type = 9

tb.GETHOUSEINFOREQUEST_NPC_FIELD.name = "npc"
tb.GETHOUSEINFOREQUEST_NPC_FIELD.full_name = ".GetHouseInfoRequest.npc"
tb.GETHOUSEINFOREQUEST_NPC_FIELD.number = 3
tb.GETHOUSEINFOREQUEST_NPC_FIELD.index = 2
tb.GETHOUSEINFOREQUEST_NPC_FIELD.label = 1
tb.GETHOUSEINFOREQUEST_NPC_FIELD.has_default_value = false
tb.GETHOUSEINFOREQUEST_NPC_FIELD.default_value = false
tb.GETHOUSEINFOREQUEST_NPC_FIELD.type = 8
tb.GETHOUSEINFOREQUEST_NPC_FIELD.cpp_type = 7

tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.name = "isSeafloor"
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.full_name = ".GetHouseInfoRequest.isSeafloor"
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.number = 4
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.index = 3
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.label = 1
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.has_default_value = false
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.default_value = false
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.type = 8
tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD.cpp_type = 7

GETHOUSEINFOREQUEST_MSG.name = "GetHouseInfoRequest"
GETHOUSEINFOREQUEST_MSG.full_name = ".GetHouseInfoRequest"
GETHOUSEINFOREQUEST_MSG.filename = "HouseExtension"
GETHOUSEINFOREQUEST_MSG.nested_types = {}
GETHOUSEINFOREQUEST_MSG.enum_types = {}
GETHOUSEINFOREQUEST_MSG.fields = {tb.GETHOUSEINFOREQUEST_HOUSEID_FIELD, tb.GETHOUSEINFOREQUEST_USERID_FIELD, tb.GETHOUSEINFOREQUEST_NPC_FIELD, tb.GETHOUSEINFOREQUEST_ISSEAFLOOR_FIELD}
GETHOUSEINFOREQUEST_MSG.is_extendable = false
GETHOUSEINFOREQUEST_MSG.extensions = {}
SETHOUSENAMEREPLY_MSG.name = "SetHouseNameReply"
SETHOUSENAMEREPLY_MSG.full_name = ".SetHouseNameReply"
SETHOUSENAMEREPLY_MSG.filename = "HouseExtension"
SETHOUSENAMEREPLY_MSG.nested_types = {}
SETHOUSENAMEREPLY_MSG.enum_types = {}
SETHOUSENAMEREPLY_MSG.fields = {}
SETHOUSENAMEREPLY_MSG.is_extendable = false
SETHOUSENAMEREPLY_MSG.extensions = {}
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.full_name = ".ChangeDefaultVisitAreaRequest.decorationSceneId"
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.number = 1
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.index = 0
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

CHANGEDEFAULTVISITAREAREQUEST_MSG.name = "ChangeDefaultVisitAreaRequest"
CHANGEDEFAULTVISITAREAREQUEST_MSG.full_name = ".ChangeDefaultVisitAreaRequest"
CHANGEDEFAULTVISITAREAREQUEST_MSG.filename = "HouseExtension"
CHANGEDEFAULTVISITAREAREQUEST_MSG.nested_types = {}
CHANGEDEFAULTVISITAREAREQUEST_MSG.enum_types = {}
CHANGEDEFAULTVISITAREAREQUEST_MSG.fields = {tb.CHANGEDEFAULTVISITAREAREQUEST_DECORATIONSCENEID_FIELD}
CHANGEDEFAULTVISITAREAREQUEST_MSG.is_extendable = false
CHANGEDEFAULTVISITAREAREQUEST_MSG.extensions = {}
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.name = "areaId"
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.full_name = ".ChangeShowAreaEffectsRequest.areaId"
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.number = 1
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.index = 0
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.label = 1
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.has_default_value = false
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.default_value = 0
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.type = 5
tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD.cpp_type = 1

tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.name = "effectsItem"
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.full_name = ".ChangeShowAreaEffectsRequest.effectsItem"
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.number = 2
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.index = 1
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.label = 1
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.has_default_value = false
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.default_value = 0
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.type = 5
tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD.cpp_type = 1

CHANGESHOWAREAEFFECTSREQUEST_MSG.name = "ChangeShowAreaEffectsRequest"
CHANGESHOWAREAEFFECTSREQUEST_MSG.full_name = ".ChangeShowAreaEffectsRequest"
CHANGESHOWAREAEFFECTSREQUEST_MSG.filename = "HouseExtension"
CHANGESHOWAREAEFFECTSREQUEST_MSG.nested_types = {}
CHANGESHOWAREAEFFECTSREQUEST_MSG.enum_types = {}
CHANGESHOWAREAEFFECTSREQUEST_MSG.fields = {tb.CHANGESHOWAREAEFFECTSREQUEST_AREAID_FIELD, tb.CHANGESHOWAREAEFFECTSREQUEST_EFFECTSITEM_FIELD}
CHANGESHOWAREAEFFECTSREQUEST_MSG.is_extendable = false
CHANGESHOWAREAEFFECTSREQUEST_MSG.extensions = {}
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.name = "areaId"
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.full_name = ".SetHouseNameRequest.areaId"
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.number = 1
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.index = 0
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.label = 2
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.has_default_value = false
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.default_value = 0
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.type = 5
tb.SETHOUSENAMEREQUEST_AREAID_FIELD.cpp_type = 1

tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.name = "newName"
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.full_name = ".SetHouseNameRequest.newName"
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.number = 2
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.index = 1
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.label = 2
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.has_default_value = false
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.default_value = ""
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.type = 9
tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD.cpp_type = 9

SETHOUSENAMEREQUEST_MSG.name = "SetHouseNameRequest"
SETHOUSENAMEREQUEST_MSG.full_name = ".SetHouseNameRequest"
SETHOUSENAMEREQUEST_MSG.filename = "HouseExtension"
SETHOUSENAMEREQUEST_MSG.nested_types = {}
SETHOUSENAMEREQUEST_MSG.enum_types = {}
SETHOUSENAMEREQUEST_MSG.fields = {tb.SETHOUSENAMEREQUEST_AREAID_FIELD, tb.SETHOUSENAMEREQUEST_NEWNAME_FIELD}
SETHOUSENAMEREQUEST_MSG.is_extendable = false
SETHOUSENAMEREQUEST_MSG.extensions = {}
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.name = "houseId"
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.full_name = ".HouseUpgradeRequest.houseId"
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.number = 1
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.index = 0
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.label = 1
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.has_default_value = false
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.default_value = 0
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.type = 5
tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD.cpp_type = 1

HOUSEUPGRADEREQUEST_MSG.name = "HouseUpgradeRequest"
HOUSEUPGRADEREQUEST_MSG.full_name = ".HouseUpgradeRequest"
HOUSEUPGRADEREQUEST_MSG.filename = "HouseExtension"
HOUSEUPGRADEREQUEST_MSG.nested_types = {}
HOUSEUPGRADEREQUEST_MSG.enum_types = {}
HOUSEUPGRADEREQUEST_MSG.fields = {tb.HOUSEUPGRADEREQUEST_HOUSEID_FIELD}
HOUSEUPGRADEREQUEST_MSG.is_extendable = false
HOUSEUPGRADEREQUEST_MSG.extensions = {}
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.name = "houseId"
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.full_name = ".HouseFurnitureChangeClothesRequest.houseId"
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.number = 1
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.index = 0
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.label = 2
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.has_default_value = false
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.default_value = 0
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.type = 5
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD.cpp_type = 1

tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.name = "id"
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.full_name = ".HouseFurnitureChangeClothesRequest.id"
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.number = 2
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.index = 1
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.label = 2
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.has_default_value = false
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.default_value = 0
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.type = 5
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD.cpp_type = 1

tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.name = "clothes"
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.full_name = ".HouseFurnitureChangeClothesRequest.clothes"
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.number = 3
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.index = 2
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.label = 3
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.has_default_value = false
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.default_value = {}
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.type = 5
tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.cpp_type = 1

HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.name = "HouseFurnitureChangeClothesRequest"
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.full_name = ".HouseFurnitureChangeClothesRequest"
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.filename = "HouseExtension"
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.nested_types = {}
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.enum_types = {}
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.fields = {tb.HOUSEFURNITURECHANGECLOTHESREQUEST_HOUSEID_FIELD, tb.HOUSEFURNITURECHANGECLOTHESREQUEST_ID_FIELD, tb.HOUSEFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD}
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.is_extendable = false
HOUSEFURNITURECHANGECLOTHESREQUEST_MSG.extensions = {}
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.name = "houseId"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.full_name = ".HouseChangeCustomFurnitureParamsRequest.houseId"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.number = 1
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.index = 0
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.label = 1
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.has_default_value = false
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.default_value = 0
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.type = 5
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD.cpp_type = 1

tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.name = "furnitureUniqueId"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.full_name = ".HouseChangeCustomFurnitureParamsRequest.furnitureUniqueId"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.number = 2
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.index = 1
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.label = 1
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.has_default_value = false
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.default_value = 0
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.type = 5
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.cpp_type = 1

tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.name = "customParams"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.full_name = ".HouseChangeCustomFurnitureParamsRequest.customParams"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.number = 3
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.index = 2
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.label = 3
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.has_default_value = false
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.default_value = {}
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.type = 5
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.cpp_type = 1

HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.name = "HouseChangeCustomFurnitureParamsRequest"
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.full_name = ".HouseChangeCustomFurnitureParamsRequest"
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.filename = "HouseExtension"
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.nested_types = {}
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.enum_types = {}
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.fields = {tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_HOUSEID_FIELD, tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD, tb.HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD}
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.is_extendable = false
HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.extensions = {}
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.name = "houseShowArea"
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.full_name = ".GetAllUnlockHouseReply.houseShowArea"
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.number = 1
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.index = 0
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.label = 3
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.has_default_value = false
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.default_value = {}
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.message_type = HOUSESHOWAREAINFO_MSG
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.type = 11
tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD.cpp_type = 10

GETALLUNLOCKHOUSEREPLY_MSG.name = "GetAllUnlockHouseReply"
GETALLUNLOCKHOUSEREPLY_MSG.full_name = ".GetAllUnlockHouseReply"
GETALLUNLOCKHOUSEREPLY_MSG.filename = "HouseExtension"
GETALLUNLOCKHOUSEREPLY_MSG.nested_types = {}
GETALLUNLOCKHOUSEREPLY_MSG.enum_types = {}
GETALLUNLOCKHOUSEREPLY_MSG.fields = {tb.GETALLUNLOCKHOUSEREPLY_HOUSESHOWAREA_FIELD}
GETALLUNLOCKHOUSEREPLY_MSG.is_extendable = false
GETALLUNLOCKHOUSEREPLY_MSG.extensions = {}
HOUSEUPGRADEREPLY_MSG.name = "HouseUpgradeReply"
HOUSEUPGRADEREPLY_MSG.full_name = ".HouseUpgradeReply"
HOUSEUPGRADEREPLY_MSG.filename = "HouseExtension"
HOUSEUPGRADEREPLY_MSG.nested_types = {}
HOUSEUPGRADEREPLY_MSG.enum_types = {}
HOUSEUPGRADEREPLY_MSG.fields = {}
HOUSEUPGRADEREPLY_MSG.is_extendable = false
HOUSEUPGRADEREPLY_MSG.extensions = {}
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.name = "updateFurnitureInfo"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.full_name = ".HouseChangeCustomFurnitureParamsReply.updateFurnitureInfo"
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.number = 1
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.index = 0
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.label = 2
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.has_default_value = false
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.default_value = nil
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.message_type = FURNITUREINFONO_MSG
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.type = 11
tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.cpp_type = 10

HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.name = "HouseChangeCustomFurnitureParamsReply"
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.full_name = ".HouseChangeCustomFurnitureParamsReply"
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.filename = "HouseExtension"
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.nested_types = {}
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.enum_types = {}
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.fields = {tb.HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD}
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.is_extendable = false
HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG.extensions = {}
tb.FURNITUREINFONO_ID_FIELD.name = "id"
tb.FURNITUREINFONO_ID_FIELD.full_name = ".FurnitureInfoNO.id"
tb.FURNITUREINFONO_ID_FIELD.number = 1
tb.FURNITUREINFONO_ID_FIELD.index = 0
tb.FURNITUREINFONO_ID_FIELD.label = 1
tb.FURNITUREINFONO_ID_FIELD.has_default_value = false
tb.FURNITUREINFONO_ID_FIELD.default_value = 0
tb.FURNITUREINFONO_ID_FIELD.type = 5
tb.FURNITUREINFONO_ID_FIELD.cpp_type = 1

tb.FURNITUREINFONO_FURNITUREID_FIELD.name = "furnitureId"
tb.FURNITUREINFONO_FURNITUREID_FIELD.full_name = ".FurnitureInfoNO.furnitureId"
tb.FURNITUREINFONO_FURNITUREID_FIELD.number = 2
tb.FURNITUREINFONO_FURNITUREID_FIELD.index = 1
tb.FURNITUREINFONO_FURNITUREID_FIELD.label = 1
tb.FURNITUREINFONO_FURNITUREID_FIELD.has_default_value = false
tb.FURNITUREINFONO_FURNITUREID_FIELD.default_value = 0
tb.FURNITUREINFONO_FURNITUREID_FIELD.type = 5
tb.FURNITUREINFONO_FURNITUREID_FIELD.cpp_type = 1

tb.FURNITUREINFONO_POSX_FIELD.name = "posX"
tb.FURNITUREINFONO_POSX_FIELD.full_name = ".FurnitureInfoNO.posX"
tb.FURNITUREINFONO_POSX_FIELD.number = 3
tb.FURNITUREINFONO_POSX_FIELD.index = 2
tb.FURNITUREINFONO_POSX_FIELD.label = 1
tb.FURNITUREINFONO_POSX_FIELD.has_default_value = false
tb.FURNITUREINFONO_POSX_FIELD.default_value = 0
tb.FURNITUREINFONO_POSX_FIELD.type = 17
tb.FURNITUREINFONO_POSX_FIELD.cpp_type = 1

tb.FURNITUREINFONO_POSY_FIELD.name = "posY"
tb.FURNITUREINFONO_POSY_FIELD.full_name = ".FurnitureInfoNO.posY"
tb.FURNITUREINFONO_POSY_FIELD.number = 4
tb.FURNITUREINFONO_POSY_FIELD.index = 3
tb.FURNITUREINFONO_POSY_FIELD.label = 1
tb.FURNITUREINFONO_POSY_FIELD.has_default_value = false
tb.FURNITUREINFONO_POSY_FIELD.default_value = 0
tb.FURNITUREINFONO_POSY_FIELD.type = 17
tb.FURNITUREINFONO_POSY_FIELD.cpp_type = 1

tb.FURNITUREINFONO_ROTATION_FIELD.name = "rotation"
tb.FURNITUREINFONO_ROTATION_FIELD.full_name = ".FurnitureInfoNO.rotation"
tb.FURNITUREINFONO_ROTATION_FIELD.number = 5
tb.FURNITUREINFONO_ROTATION_FIELD.index = 4
tb.FURNITUREINFONO_ROTATION_FIELD.label = 1
tb.FURNITUREINFONO_ROTATION_FIELD.has_default_value = false
tb.FURNITUREINFONO_ROTATION_FIELD.default_value = 0
tb.FURNITUREINFONO_ROTATION_FIELD.type = 17
tb.FURNITUREINFONO_ROTATION_FIELD.cpp_type = 1

tb.FURNITUREINFONO_FURNITUREMARK_FIELD.name = "furnitureMark"
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.full_name = ".FurnitureInfoNO.furnitureMark"
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.number = 6
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.index = 5
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.label = 1
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.has_default_value = false
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.default_value = 0
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.type = 5
tb.FURNITUREINFONO_FURNITUREMARK_FIELD.cpp_type = 1

tb.FURNITUREINFONO_FLOOR_FIELD.name = "floor"
tb.FURNITUREINFONO_FLOOR_FIELD.full_name = ".FurnitureInfoNO.floor"
tb.FURNITUREINFONO_FLOOR_FIELD.number = 7
tb.FURNITUREINFONO_FLOOR_FIELD.index = 6
tb.FURNITUREINFONO_FLOOR_FIELD.label = 1
tb.FURNITUREINFONO_FLOOR_FIELD.has_default_value = false
tb.FURNITUREINFONO_FLOOR_FIELD.default_value = 0
tb.FURNITUREINFONO_FLOOR_FIELD.type = 5
tb.FURNITUREINFONO_FLOOR_FIELD.cpp_type = 1

tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.name = "furnitureCustomParams"
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.full_name = ".FurnitureInfoNO.furnitureCustomParams"
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.number = 8
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.index = 7
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.label = 3
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.has_default_value = false
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.default_value = {}
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.type = 5
tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD.cpp_type = 1

tb.FURNITUREINFONO_SHARE_FIELD.name = "share"
tb.FURNITUREINFONO_SHARE_FIELD.full_name = ".FurnitureInfoNO.share"
tb.FURNITUREINFONO_SHARE_FIELD.number = 9
tb.FURNITUREINFONO_SHARE_FIELD.index = 8
tb.FURNITUREINFONO_SHARE_FIELD.label = 1
tb.FURNITUREINFONO_SHARE_FIELD.has_default_value = false
tb.FURNITUREINFONO_SHARE_FIELD.default_value = false
tb.FURNITUREINFONO_SHARE_FIELD.type = 8
tb.FURNITUREINFONO_SHARE_FIELD.cpp_type = 7

tb.FURNITUREINFONO_ACCESSTAG_FIELD.name = "accessTag"
tb.FURNITUREINFONO_ACCESSTAG_FIELD.full_name = ".FurnitureInfoNO.accessTag"
tb.FURNITUREINFONO_ACCESSTAG_FIELD.number = 11
tb.FURNITUREINFONO_ACCESSTAG_FIELD.index = 9
tb.FURNITUREINFONO_ACCESSTAG_FIELD.label = 1
tb.FURNITUREINFONO_ACCESSTAG_FIELD.has_default_value = false
tb.FURNITUREINFONO_ACCESSTAG_FIELD.default_value = 0
tb.FURNITUREINFONO_ACCESSTAG_FIELD.type = 5
tb.FURNITUREINFONO_ACCESSTAG_FIELD.cpp_type = 1

tb.FURNITUREINFONO_AREAID_FIELD.name = "areaId"
tb.FURNITUREINFONO_AREAID_FIELD.full_name = ".FurnitureInfoNO.areaId"
tb.FURNITUREINFONO_AREAID_FIELD.number = 21
tb.FURNITUREINFONO_AREAID_FIELD.index = 10
tb.FURNITUREINFONO_AREAID_FIELD.label = 1
tb.FURNITUREINFONO_AREAID_FIELD.has_default_value = false
tb.FURNITUREINFONO_AREAID_FIELD.default_value = 0
tb.FURNITUREINFONO_AREAID_FIELD.type = 5
tb.FURNITUREINFONO_AREAID_FIELD.cpp_type = 1

tb.FURNITUREINFONO_CLOTHES_FIELD.name = "clothes"
tb.FURNITUREINFONO_CLOTHES_FIELD.full_name = ".FurnitureInfoNO.clothes"
tb.FURNITUREINFONO_CLOTHES_FIELD.number = 22
tb.FURNITUREINFONO_CLOTHES_FIELD.index = 11
tb.FURNITUREINFONO_CLOTHES_FIELD.label = 3
tb.FURNITUREINFONO_CLOTHES_FIELD.has_default_value = false
tb.FURNITUREINFONO_CLOTHES_FIELD.default_value = {}
tb.FURNITUREINFONO_CLOTHES_FIELD.type = 5
tb.FURNITUREINFONO_CLOTHES_FIELD.cpp_type = 1

tb.FURNITUREINFONO_PILLARID_FIELD.name = "pillarId"
tb.FURNITUREINFONO_PILLARID_FIELD.full_name = ".FurnitureInfoNO.pillarId"
tb.FURNITUREINFONO_PILLARID_FIELD.number = 23
tb.FURNITUREINFONO_PILLARID_FIELD.index = 12
tb.FURNITUREINFONO_PILLARID_FIELD.label = 3
tb.FURNITUREINFONO_PILLARID_FIELD.has_default_value = false
tb.FURNITUREINFONO_PILLARID_FIELD.default_value = {}
tb.FURNITUREINFONO_PILLARID_FIELD.message_type = PILLARINFONO_MSG
tb.FURNITUREINFONO_PILLARID_FIELD.type = 11
tb.FURNITUREINFONO_PILLARID_FIELD.cpp_type = 10

FURNITUREINFONO_MSG.name = "FurnitureInfoNO"
FURNITUREINFONO_MSG.full_name = ".FurnitureInfoNO"
FURNITUREINFONO_MSG.filename = "HouseExtension"
FURNITUREINFONO_MSG.nested_types = {}
FURNITUREINFONO_MSG.enum_types = {}
FURNITUREINFONO_MSG.fields = {tb.FURNITUREINFONO_ID_FIELD, tb.FURNITUREINFONO_FURNITUREID_FIELD, tb.FURNITUREINFONO_POSX_FIELD, tb.FURNITUREINFONO_POSY_FIELD, tb.FURNITUREINFONO_ROTATION_FIELD, tb.FURNITUREINFONO_FURNITUREMARK_FIELD, tb.FURNITUREINFONO_FLOOR_FIELD, tb.FURNITUREINFONO_FURNITURECUSTOMPARAMS_FIELD, tb.FURNITUREINFONO_SHARE_FIELD, tb.FURNITUREINFONO_ACCESSTAG_FIELD, tb.FURNITUREINFONO_AREAID_FIELD, tb.FURNITUREINFONO_CLOTHES_FIELD, tb.FURNITUREINFONO_PILLARID_FIELD}
FURNITUREINFONO_MSG.is_extendable = false
FURNITUREINFONO_MSG.extensions = {}
tb.HOUSEINFONO_HOUSEID_FIELD.name = "houseId"
tb.HOUSEINFONO_HOUSEID_FIELD.full_name = ".HouseInfoNO.houseId"
tb.HOUSEINFONO_HOUSEID_FIELD.number = 1
tb.HOUSEINFONO_HOUSEID_FIELD.index = 0
tb.HOUSEINFONO_HOUSEID_FIELD.label = 2
tb.HOUSEINFONO_HOUSEID_FIELD.has_default_value = false
tb.HOUSEINFONO_HOUSEID_FIELD.default_value = 0
tb.HOUSEINFONO_HOUSEID_FIELD.type = 5
tb.HOUSEINFONO_HOUSEID_FIELD.cpp_type = 1

tb.HOUSEINFONO_ROOMS_FIELD.name = "rooms"
tb.HOUSEINFONO_ROOMS_FIELD.full_name = ".HouseInfoNO.rooms"
tb.HOUSEINFONO_ROOMS_FIELD.number = 2
tb.HOUSEINFONO_ROOMS_FIELD.index = 1
tb.HOUSEINFONO_ROOMS_FIELD.label = 3
tb.HOUSEINFONO_ROOMS_FIELD.has_default_value = false
tb.HOUSEINFONO_ROOMS_FIELD.default_value = {}
tb.HOUSEINFONO_ROOMS_FIELD.message_type = ROOMINFONO_MSG
tb.HOUSEINFONO_ROOMS_FIELD.type = 11
tb.HOUSEINFONO_ROOMS_FIELD.cpp_type = 10

tb.HOUSEINFONO_FURNITURE_FIELD.name = "furniture"
tb.HOUSEINFONO_FURNITURE_FIELD.full_name = ".HouseInfoNO.furniture"
tb.HOUSEINFONO_FURNITURE_FIELD.number = 3
tb.HOUSEINFONO_FURNITURE_FIELD.index = 2
tb.HOUSEINFONO_FURNITURE_FIELD.label = 3
tb.HOUSEINFONO_FURNITURE_FIELD.has_default_value = false
tb.HOUSEINFONO_FURNITURE_FIELD.default_value = {}
tb.HOUSEINFONO_FURNITURE_FIELD.message_type = FURNITUREINFONO_MSG
tb.HOUSEINFONO_FURNITURE_FIELD.type = 11
tb.HOUSEINFONO_FURNITURE_FIELD.cpp_type = 10

tb.HOUSEINFONO_BACKGROUND_FIELD.name = "background"
tb.HOUSEINFONO_BACKGROUND_FIELD.full_name = ".HouseInfoNO.background"
tb.HOUSEINFONO_BACKGROUND_FIELD.number = 4
tb.HOUSEINFONO_BACKGROUND_FIELD.index = 3
tb.HOUSEINFONO_BACKGROUND_FIELD.label = 1
tb.HOUSEINFONO_BACKGROUND_FIELD.has_default_value = false
tb.HOUSEINFONO_BACKGROUND_FIELD.default_value = 0
tb.HOUSEINFONO_BACKGROUND_FIELD.type = 5
tb.HOUSEINFONO_BACKGROUND_FIELD.cpp_type = 1

tb.HOUSEINFONO_LEVEL_FIELD.name = "level"
tb.HOUSEINFONO_LEVEL_FIELD.full_name = ".HouseInfoNO.level"
tb.HOUSEINFONO_LEVEL_FIELD.number = 5
tb.HOUSEINFONO_LEVEL_FIELD.index = 4
tb.HOUSEINFONO_LEVEL_FIELD.label = 1
tb.HOUSEINFONO_LEVEL_FIELD.has_default_value = false
tb.HOUSEINFONO_LEVEL_FIELD.default_value = 0
tb.HOUSEINFONO_LEVEL_FIELD.type = 5
tb.HOUSEINFONO_LEVEL_FIELD.cpp_type = 1

tb.HOUSEINFONO_HOUSETYPE_FIELD.name = "houseType"
tb.HOUSEINFONO_HOUSETYPE_FIELD.full_name = ".HouseInfoNO.houseType"
tb.HOUSEINFONO_HOUSETYPE_FIELD.number = 7
tb.HOUSEINFONO_HOUSETYPE_FIELD.index = 5
tb.HOUSEINFONO_HOUSETYPE_FIELD.label = 1
tb.HOUSEINFONO_HOUSETYPE_FIELD.has_default_value = false
tb.HOUSEINFONO_HOUSETYPE_FIELD.default_value = 0
tb.HOUSEINFONO_HOUSETYPE_FIELD.type = 5
tb.HOUSEINFONO_HOUSETYPE_FIELD.cpp_type = 1

tb.HOUSEINFONO_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.full_name = ".HouseInfoNO.foodItemInfos"
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.number = 8
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.index = 6
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.label = 3
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.has_default_value = false
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.default_value = {}
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.type = 11
tb.HOUSEINFONO_FOODITEMINFOS_FIELD.cpp_type = 10

HOUSEINFONO_MSG.name = "HouseInfoNO"
HOUSEINFONO_MSG.full_name = ".HouseInfoNO"
HOUSEINFONO_MSG.filename = "HouseExtension"
HOUSEINFONO_MSG.nested_types = {}
HOUSEINFONO_MSG.enum_types = {}
HOUSEINFONO_MSG.fields = {tb.HOUSEINFONO_HOUSEID_FIELD, tb.HOUSEINFONO_ROOMS_FIELD, tb.HOUSEINFONO_FURNITURE_FIELD, tb.HOUSEINFONO_BACKGROUND_FIELD, tb.HOUSEINFONO_LEVEL_FIELD, tb.HOUSEINFONO_HOUSETYPE_FIELD, tb.HOUSEINFONO_FOODITEMINFOS_FIELD}
HOUSEINFONO_MSG.is_extendable = false
HOUSEINFONO_MSG.extensions = {}
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.name = "houseInfo"
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.full_name = ".HouseChangedPush.houseInfo"
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.number = 1
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.index = 0
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.label = 1
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.has_default_value = false
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.default_value = nil
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.message_type = HOUSEINFONO_MSG
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.type = 11
tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD.cpp_type = 10

tb.HOUSECHANGEDPUSH_COMFORT_FIELD.name = "comfort"
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.full_name = ".HouseChangedPush.comfort"
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.number = 2
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.index = 1
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.label = 1
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.has_default_value = false
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.default_value = nil
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.message_type = COMFORTVALUENO_MSG
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.type = 11
tb.HOUSECHANGEDPUSH_COMFORT_FIELD.cpp_type = 10

HOUSECHANGEDPUSH_MSG.name = "HouseChangedPush"
HOUSECHANGEDPUSH_MSG.full_name = ".HouseChangedPush"
HOUSECHANGEDPUSH_MSG.filename = "HouseExtension"
HOUSECHANGEDPUSH_MSG.nested_types = {}
HOUSECHANGEDPUSH_MSG.enum_types = {}
HOUSECHANGEDPUSH_MSG.fields = {tb.HOUSECHANGEDPUSH_HOUSEINFO_FIELD, tb.HOUSECHANGEDPUSH_COMFORT_FIELD}
HOUSECHANGEDPUSH_MSG.is_extendable = false
HOUSECHANGEDPUSH_MSG.extensions = {}
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.full_name = ".GetDefaultVisitAreaReply.decorationSceneId"
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.number = 2
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.index = 0
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.label = 1
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.has_default_value = false
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.default_value = 0
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.type = 5
tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD.cpp_type = 1

tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.name = "canVisit"
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.full_name = ".GetDefaultVisitAreaReply.canVisit"
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.number = 3
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.index = 1
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.label = 1
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.has_default_value = false
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.default_value = 0
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.type = 5
tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD.cpp_type = 1

GETDEFAULTVISITAREAREPLY_MSG.name = "GetDefaultVisitAreaReply"
GETDEFAULTVISITAREAREPLY_MSG.full_name = ".GetDefaultVisitAreaReply"
GETDEFAULTVISITAREAREPLY_MSG.filename = "HouseExtension"
GETDEFAULTVISITAREAREPLY_MSG.nested_types = {}
GETDEFAULTVISITAREAREPLY_MSG.enum_types = {}
GETDEFAULTVISITAREAREPLY_MSG.fields = {tb.GETDEFAULTVISITAREAREPLY_DECORATIONSCENEID_FIELD, tb.GETDEFAULTVISITAREAREPLY_CANVISIT_FIELD}
GETDEFAULTVISITAREAREPLY_MSG.is_extendable = false
GETDEFAULTVISITAREAREPLY_MSG.extensions = {}
CHANGESHOWAREAEFFECTSREPLY_MSG.name = "ChangeShowAreaEffectsReply"
CHANGESHOWAREAEFFECTSREPLY_MSG.full_name = ".ChangeShowAreaEffectsReply"
CHANGESHOWAREAEFFECTSREPLY_MSG.filename = "HouseExtension"
CHANGESHOWAREAEFFECTSREPLY_MSG.nested_types = {}
CHANGESHOWAREAEFFECTSREPLY_MSG.enum_types = {}
CHANGESHOWAREAEFFECTSREPLY_MSG.fields = {}
CHANGESHOWAREAEFFECTSREPLY_MSG.is_extendable = false
CHANGESHOWAREAEFFECTSREPLY_MSG.extensions = {}
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.name = "heart"
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.full_name = ".HouseTakeHeartReply.heart"
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.number = 1
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.index = 0
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.label = 1
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.has_default_value = false
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.default_value = 0
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.type = 5
tb.HOUSETAKEHEARTREPLY_HEART_FIELD.cpp_type = 1

tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.name = "drops"
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.full_name = ".HouseTakeHeartReply.drops"
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.number = 2
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.index = 1
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.label = 3
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.has_default_value = false
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.default_value = {}
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.message_type = TAKEHEARTDROP_MSG
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.type = 11
tb.HOUSETAKEHEARTREPLY_DROPS_FIELD.cpp_type = 10

HOUSETAKEHEARTREPLY_MSG.name = "HouseTakeHeartReply"
HOUSETAKEHEARTREPLY_MSG.full_name = ".HouseTakeHeartReply"
HOUSETAKEHEARTREPLY_MSG.filename = "HouseExtension"
HOUSETAKEHEARTREPLY_MSG.nested_types = {}
HOUSETAKEHEARTREPLY_MSG.enum_types = {}
HOUSETAKEHEARTREPLY_MSG.fields = {tb.HOUSETAKEHEARTREPLY_HEART_FIELD, tb.HOUSETAKEHEARTREPLY_DROPS_FIELD}
HOUSETAKEHEARTREPLY_MSG.is_extendable = false
HOUSETAKEHEARTREPLY_MSG.extensions = {}
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.name = "changeId"
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.full_name = ".UnlockHouseReply.changeId"
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.number = 1
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.index = 0
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.label = 1
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.has_default_value = false
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.default_value = 0
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.type = 5
tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD.cpp_type = 1

tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.name = "houseId"
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.full_name = ".UnlockHouseReply.houseId"
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.number = 2
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.index = 1
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.label = 1
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.has_default_value = false
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.default_value = 0
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.type = 5
tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD.cpp_type = 1

UNLOCKHOUSEREPLY_MSG.name = "UnlockHouseReply"
UNLOCKHOUSEREPLY_MSG.full_name = ".UnlockHouseReply"
UNLOCKHOUSEREPLY_MSG.filename = "HouseExtension"
UNLOCKHOUSEREPLY_MSG.nested_types = {}
UNLOCKHOUSEREPLY_MSG.enum_types = {}
UNLOCKHOUSEREPLY_MSG.fields = {tb.UNLOCKHOUSEREPLY_CHANGEID_FIELD, tb.UNLOCKHOUSEREPLY_HOUSEID_FIELD}
UNLOCKHOUSEREPLY_MSG.is_extendable = false
UNLOCKHOUSEREPLY_MSG.extensions = {}
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.name = "houseInfo"
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.full_name = ".GetHouseInfoReply.houseInfo"
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.number = 1
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.index = 0
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.label = 1
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.default_value = nil
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.message_type = HOUSEINFONO_MSG
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.type = 11
tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD.cpp_type = 10

tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.name = "roleInfo"
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.full_name = ".GetHouseInfoReply.roleInfo"
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.number = 2
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.index = 1
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.label = 1
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.default_value = nil
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLEINFONO_MSG
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.type = 11
tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD.cpp_type = 10

tb.GETHOUSEINFOREPLY_COMFORT_FIELD.name = "comfort"
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.full_name = ".GetHouseInfoReply.comfort"
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.number = 3
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.index = 2
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.label = 1
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.default_value = nil
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.message_type = COMFORTVALUENO_MSG
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.type = 11
tb.GETHOUSEINFOREPLY_COMFORT_FIELD.cpp_type = 10

tb.GETHOUSEINFOREPLY_SECTORS_FIELD.name = "sectors"
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.full_name = ".GetHouseInfoReply.sectors"
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.number = 4
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.index = 3
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.label = 3
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.default_value = {}
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.type = 5
tb.GETHOUSEINFOREPLY_SECTORS_FIELD.cpp_type = 1

tb.GETHOUSEINFOREPLY_HEART_FIELD.name = "heart"
tb.GETHOUSEINFOREPLY_HEART_FIELD.full_name = ".GetHouseInfoReply.heart"
tb.GETHOUSEINFOREPLY_HEART_FIELD.number = 5
tb.GETHOUSEINFOREPLY_HEART_FIELD.index = 4
tb.GETHOUSEINFOREPLY_HEART_FIELD.label = 1
tb.GETHOUSEINFOREPLY_HEART_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_HEART_FIELD.default_value = false
tb.GETHOUSEINFOREPLY_HEART_FIELD.type = 8
tb.GETHOUSEINFOREPLY_HEART_FIELD.cpp_type = 7

tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.name = "isAllowToEat"
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.full_name = ".GetHouseInfoReply.isAllowToEat"
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.number = 6
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.index = 5
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.label = 1
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.default_value = false
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.type = 8
tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD.cpp_type = 7

tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.name = "travellerInfos"
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.full_name = ".GetHouseInfoReply.travellerInfos"
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.number = 7
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.index = 6
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.label = 3
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.default_value = {}
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.message_type = TRAVELLEREXTENSION_PB.TRAVELLERNO_MSG
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.type = 11
tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD.cpp_type = 10

tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.name = "takePhoto"
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.full_name = ".GetHouseInfoReply.takePhoto"
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.number = 8
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.index = 7
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.label = 1
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.default_value = false
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.type = 8
tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD.cpp_type = 7

tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.name = "houseAreaId"
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.full_name = ".GetHouseInfoReply.houseAreaId"
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.number = 9
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.index = 8
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.label = 1
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.default_value = 0
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.type = 5
tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD.cpp_type = 1

tb.GETHOUSEINFOREPLY_STREETID_FIELD.name = "streetId"
tb.GETHOUSEINFOREPLY_STREETID_FIELD.full_name = ".GetHouseInfoReply.streetId"
tb.GETHOUSEINFOREPLY_STREETID_FIELD.number = 10
tb.GETHOUSEINFOREPLY_STREETID_FIELD.index = 9
tb.GETHOUSEINFOREPLY_STREETID_FIELD.label = 1
tb.GETHOUSEINFOREPLY_STREETID_FIELD.has_default_value = false
tb.GETHOUSEINFOREPLY_STREETID_FIELD.default_value = ""
tb.GETHOUSEINFOREPLY_STREETID_FIELD.type = 9
tb.GETHOUSEINFOREPLY_STREETID_FIELD.cpp_type = 9

GETHOUSEINFOREPLY_MSG.name = "GetHouseInfoReply"
GETHOUSEINFOREPLY_MSG.full_name = ".GetHouseInfoReply"
GETHOUSEINFOREPLY_MSG.filename = "HouseExtension"
GETHOUSEINFOREPLY_MSG.nested_types = {}
GETHOUSEINFOREPLY_MSG.enum_types = {}
GETHOUSEINFOREPLY_MSG.fields = {tb.GETHOUSEINFOREPLY_HOUSEINFO_FIELD, tb.GETHOUSEINFOREPLY_ROLEINFO_FIELD, tb.GETHOUSEINFOREPLY_COMFORT_FIELD, tb.GETHOUSEINFOREPLY_SECTORS_FIELD, tb.GETHOUSEINFOREPLY_HEART_FIELD, tb.GETHOUSEINFOREPLY_ISALLOWTOEAT_FIELD, tb.GETHOUSEINFOREPLY_TRAVELLERINFOS_FIELD, tb.GETHOUSEINFOREPLY_TAKEPHOTO_FIELD, tb.GETHOUSEINFOREPLY_HOUSEAREAID_FIELD, tb.GETHOUSEINFOREPLY_STREETID_FIELD}
GETHOUSEINFOREPLY_MSG.is_extendable = false
GETHOUSEINFOREPLY_MSG.extensions = {}
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.name = "areaId"
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.full_name = ".UnlockHouseRequest.areaId"
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.number = 1
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.index = 0
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.label = 1
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.has_default_value = false
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.default_value = 0
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.type = 5
tb.UNLOCKHOUSEREQUEST_AREAID_FIELD.cpp_type = 1

UNLOCKHOUSEREQUEST_MSG.name = "UnlockHouseRequest"
UNLOCKHOUSEREQUEST_MSG.full_name = ".UnlockHouseRequest"
UNLOCKHOUSEREQUEST_MSG.filename = "HouseExtension"
UNLOCKHOUSEREQUEST_MSG.nested_types = {}
UNLOCKHOUSEREQUEST_MSG.enum_types = {}
UNLOCKHOUSEREQUEST_MSG.fields = {tb.UNLOCKHOUSEREQUEST_AREAID_FIELD}
UNLOCKHOUSEREQUEST_MSG.is_extendable = false
UNLOCKHOUSEREQUEST_MSG.extensions = {}
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.name = "houseId"
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.full_name = ".ChangeElfHouseRequest.houseId"
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.number = 1
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.index = 0
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.label = 1
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.has_default_value = false
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.default_value = 0
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.type = 5
tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD.cpp_type = 1

CHANGEELFHOUSEREQUEST_MSG.name = "ChangeElfHouseRequest"
CHANGEELFHOUSEREQUEST_MSG.full_name = ".ChangeElfHouseRequest"
CHANGEELFHOUSEREQUEST_MSG.filename = "HouseExtension"
CHANGEELFHOUSEREQUEST_MSG.nested_types = {}
CHANGEELFHOUSEREQUEST_MSG.enum_types = {}
CHANGEELFHOUSEREQUEST_MSG.fields = {tb.CHANGEELFHOUSEREQUEST_HOUSEID_FIELD}
CHANGEELFHOUSEREQUEST_MSG.is_extendable = false
CHANGEELFHOUSEREQUEST_MSG.extensions = {}
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.name = "userId"
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.full_name = ".HouseTakeHeartRequest.userId"
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.number = 1
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.index = 0
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.label = 1
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.has_default_value = false
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.default_value = ""
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.type = 9
tb.HOUSETAKEHEARTREQUEST_USERID_FIELD.cpp_type = 9

tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.name = "npc"
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.full_name = ".HouseTakeHeartRequest.npc"
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.number = 2
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.index = 1
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.label = 1
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.has_default_value = false
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.default_value = false
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.type = 8
tb.HOUSETAKEHEARTREQUEST_NPC_FIELD.cpp_type = 7

HOUSETAKEHEARTREQUEST_MSG.name = "HouseTakeHeartRequest"
HOUSETAKEHEARTREQUEST_MSG.full_name = ".HouseTakeHeartRequest"
HOUSETAKEHEARTREQUEST_MSG.filename = "HouseExtension"
HOUSETAKEHEARTREQUEST_MSG.nested_types = {}
HOUSETAKEHEARTREQUEST_MSG.enum_types = {}
HOUSETAKEHEARTREQUEST_MSG.fields = {tb.HOUSETAKEHEARTREQUEST_USERID_FIELD, tb.HOUSETAKEHEARTREQUEST_NPC_FIELD}
HOUSETAKEHEARTREQUEST_MSG.is_extendable = false
HOUSETAKEHEARTREQUEST_MSG.extensions = {}
CHANGEELFHOUSEREPLY_MSG.name = "ChangeElfHouseReply"
CHANGEELFHOUSEREPLY_MSG.full_name = ".ChangeElfHouseReply"
CHANGEELFHOUSEREPLY_MSG.filename = "HouseExtension"
CHANGEELFHOUSEREPLY_MSG.nested_types = {}
CHANGEELFHOUSEREPLY_MSG.enum_types = {}
CHANGEELFHOUSEREPLY_MSG.fields = {}
CHANGEELFHOUSEREPLY_MSG.is_extendable = false
CHANGEELFHOUSEREPLY_MSG.extensions = {}

ChangeDefaultVisitAreaReply = protobuf.Message(CHANGEDEFAULTVISITAREAREPLY_MSG)
ChangeDefaultVisitAreaRequest = protobuf.Message(CHANGEDEFAULTVISITAREAREQUEST_MSG)
ChangeElfHouseReply = protobuf.Message(CHANGEELFHOUSEREPLY_MSG)
ChangeElfHouseRequest = protobuf.Message(CHANGEELFHOUSEREQUEST_MSG)
ChangeHouseReply = protobuf.Message(CHANGEHOUSEREPLY_MSG)
ChangeHouseRequest = protobuf.Message(CHANGEHOUSEREQUEST_MSG)
ChangeShowAreaEffectsReply = protobuf.Message(CHANGESHOWAREAEFFECTSREPLY_MSG)
ChangeShowAreaEffectsRequest = protobuf.Message(CHANGESHOWAREAEFFECTSREQUEST_MSG)
ComfortValueNO = protobuf.Message(COMFORTVALUENO_MSG)
DecorateHouseReply = protobuf.Message(DECORATEHOUSEREPLY_MSG)
DecorateHouseRequest = protobuf.Message(DECORATEHOUSEREQUEST_MSG)
FurnitureInfoNO = protobuf.Message(FURNITUREINFONO_MSG)
GetAllHouseInfoReply = protobuf.Message(GETALLHOUSEINFOREPLY_MSG)
GetAllHouseInfoRequest = protobuf.Message(GETALLHOUSEINFOREQUEST_MSG)
GetAllUnlockHouseReply = protobuf.Message(GETALLUNLOCKHOUSEREPLY_MSG)
GetAllUnlockHouseRequest = protobuf.Message(GETALLUNLOCKHOUSEREQUEST_MSG)
GetDefaultVisitAreaReply = protobuf.Message(GETDEFAULTVISITAREAREPLY_MSG)
GetDefaultVisitAreaRequest = protobuf.Message(GETDEFAULTVISITAREAREQUEST_MSG)
GetHouseInfoReply = protobuf.Message(GETHOUSEINFOREPLY_MSG)
GetHouseInfoRequest = protobuf.Message(GETHOUSEINFOREQUEST_MSG)
HouseChangeCustomFurnitureParamsReply = protobuf.Message(HOUSECHANGECUSTOMFURNITUREPARAMSREPLY_MSG)
HouseChangeCustomFurnitureParamsRequest = protobuf.Message(HOUSECHANGECUSTOMFURNITUREPARAMSREQUEST_MSG)
HouseChangedPush = protobuf.Message(HOUSECHANGEDPUSH_MSG)
HouseFurnitureChangeClothesReply = protobuf.Message(HOUSEFURNITURECHANGECLOTHESREPLY_MSG)
HouseFurnitureChangeClothesRequest = protobuf.Message(HOUSEFURNITURECHANGECLOTHESREQUEST_MSG)
HouseFurnitureSwitchMarkReply = protobuf.Message(HOUSEFURNITURESWITCHMARKREPLY_MSG)
HouseFurnitureSwitchMarkRequest = protobuf.Message(HOUSEFURNITURESWITCHMARKREQUEST_MSG)
HouseInfoNO = protobuf.Message(HOUSEINFONO_MSG)
HouseShowAreaInfo = protobuf.Message(HOUSESHOWAREAINFO_MSG)
HouseTakeHeartReply = protobuf.Message(HOUSETAKEHEARTREPLY_MSG)
HouseTakeHeartRequest = protobuf.Message(HOUSETAKEHEARTREQUEST_MSG)
HouseUpgradeReply = protobuf.Message(HOUSEUPGRADEREPLY_MSG)
HouseUpgradeRequest = protobuf.Message(HOUSEUPGRADEREQUEST_MSG)
MARK_MUSIC_SWITCH = 1
PillarInfoNO = protobuf.Message(PILLARINFONO_MSG)
RoomInfoNO = protobuf.Message(ROOMINFONO_MSG)
SetHouseNameReply = protobuf.Message(SETHOUSENAMEREPLY_MSG)
SetHouseNameRequest = protobuf.Message(SETHOUSENAMEREQUEST_MSG)
TakeHeartDrop = protobuf.Message(TAKEHEARTDROP_MSG)
UnlockHouseReply = protobuf.Message(UNLOCKHOUSEREPLY_MSG)
UnlockHouseRequest = protobuf.Message(UNLOCKHOUSEREQUEST_MSG)

return _G["logic.proto.HouseExtension_pb"]
