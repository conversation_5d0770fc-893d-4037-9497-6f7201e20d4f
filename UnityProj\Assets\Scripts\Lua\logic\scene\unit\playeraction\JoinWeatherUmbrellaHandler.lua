module("logic.scene.unit.playeraction.JoinWeatherUmbrellaHandler",package.seeall)

local JoinWeatherUmbrellaHandler = class("JoinWeatherUmbrellaHandler",PlayerActionHandlerBase)

function JoinWeatherUmbrellaHandler:onStart()
    if SceneManager.instance:isInSea() then
        self._seaSceneZ = GameUtils.getLocalPos(self.unit.go).z
    end
    self.umbrellaConfig = InteractionItemConfig.getWeatherUmbrellaConfigById(tonumber(self.info.params[2]))
    self.unit.skinView:hideHandItem()
    self.unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
	local targetId = self.info.params[1]
    self:setTartget(targetId)
    --self.unit:lockMove(true)
    self.unit:playAnimation(self.umbrellaConfig.idleAnim .. "-2", true,false,true)
    self.oldSpeed = self.unit:getSpeed()
	self.unit:setSpeed(2.5)	
    self:onUpdate()
    self.unit:setNavEnable(false)
    
    --self.unit.walkEffect:setSpecialMove(true) --关闭脚印
    self.unit.notSort = true
    UpdateBeat:Add(self.updateAttach,self)
end

function JoinWeatherUmbrellaHandler:onStop()
    UpdateBeat:Remove(self.updateAttach,self)
	self.unit.notSort = false
    self.unit.skinView:showHandItem()	

    local curScene = SceneManager.instance:getCurScene()
    self.unit.followComp:setFollowTarget(nil)
    self.unit:setSpeed(self.oldSpeed)
    --self.unit:lockMove(false)
    
    local x,y = self.unit:getPos()
    local walkable = curScene:getMapMgr():isWalkable(x,y)
    if not walkable and self._master then 
        local x,y = self._master:getPos()
        self.unit:setPos(x,y)
    end
    --self.unit.walkEffect:setSpecialMove(false)
    if SceneManager.instance:isInSea() then
        local nowPos = GameUtils.getLocalPos(self.unit.go)
        GameUtils.setLocalPos(self.unit.go,nowPos.x,nowPos.y,self._seaSceneZ)
    end
    self.unit:setNavEnable(true)
end

function JoinWeatherUmbrellaHandler:onUpdate()

end

function JoinWeatherUmbrellaHandler:onShortActionStart()

end

function JoinWeatherUmbrellaHandler:onAttachMove()
    self._isAttachMoveing = true
    self.unit:onStartMove()
end

function JoinWeatherUmbrellaHandler:onAttachStop()
    self._isAttachMoveing = false
    self.unit:onStopMove()
end


function JoinWeatherUmbrellaHandler:onStartMove()
    self.unit:playAnimation(self.umbrellaConfig.walkAnim .. "-2", true,false,true)
    if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_move")
	end
end

function JoinWeatherUmbrellaHandler:onStopMove()
    self.unit:playAnimation(self.umbrellaConfig.idleAnim .. "-2", true,false,true)	
    if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_idle")
		self.unit.idleComp:setEnable(false)
	end
end

function JoinWeatherUmbrellaHandler:setTartget(userId)
    local curScene = SceneManager.instance:getCurScene()
    self.unit.followComp:setFollowTarget(nil)
    local master = curScene:getPlayer(userId)
    if not master then 
        return
    end
    self._master = master
    local dir = self._master:getDirection()

    --z轴参数只在3d场景生效，其他场景在followComp中有特殊处理
    if dir == 2 or dir == 0 then 
        self.unit.followComp:setAttachTarget(self._master,-0.8,0.065)
    elseif dir == 3 or dir == 1 then
        self.unit.followComp:setAttachTarget(self._master,0.8,0.065)
    end

    --self.unit.followComp:setQueueTarget(master,0.3)
end

function JoinWeatherUmbrellaHandler:updateAttach()
    if not self._master then 
        return
    end
    local dir = self._master:getDirection()
    if dir == 2 or dir == 0 then 
        self.unit.followComp:setAttachTarget(self._master,-0.8,0.065)
    elseif dir == 3 or dir == 1 then
        self.unit.followComp:setAttachTarget(self._master,0.8,0.065)
    end

    if self._master:isMoving() and not self._isAttachMoveing then  
        self:onAttachMove()
    elseif not self._master:isMoving() and self._isAttachMoveing then 
        self:onAttachStop()
    end
end

return JoinWeatherUmbrellaHandler