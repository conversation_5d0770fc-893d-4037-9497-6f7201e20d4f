module("logic.extensions.throwcake.view.ThrowCakeGameRewardView", package.seeall)
local ThrowCakeGameRewardView = class("ThrowCakeGameRewardView", ViewComponent)

local iconSize = 72

--- view初始化时会执行
function ThrowCakeGameRewardView:buildUI()
    self._txtTitle = self:getText("txtGo/txtName")
    self._txtDes = self:getText("txtGo/txtDesc")
    self._txtMaxScore = self:getText("txtGo/txtMaxScore")

    self.rewardContainers = {}

    for i = 1, 3 do
        local container = {}
        container.scoreText = self:getText("rightContent/scorerewarditem" .. i .. "/txtScore")
        container.awardlist = self:getGo("rightContent/scorerewarditem" .. i .. "/itemList")
        container._btnReceive = self:getBtn("rightContent/scorerewarditem" .. i .. "/btnReceive")
        container._btnReceive:AddClickListener(self.clickReceive, self, {i})
        container._imgNotReached = self:getGo("rightContent/scorerewarditem" .. i .. "/imgNotReach")
        container._imgOver = self:getGo("rightContent/scorerewarditem" .. i .. "/imgReceived")
        container.icons = {}
        container.canvansGroup = self:getGo("rightContent/scorerewarditem" .. i):GetComponent("CanvasGroup")
        self.rewardContainers[i] = container
    end

    self._awardGo = self:getGo("awardGo")
    self._txtHint1 = self:getText("txtGo/txtHint1")
end

function ThrowCakeGameRewardView:destroyUI()

end

function ThrowCakeGameRewardView:bindEvents()
    self:registerLocalNotify(ThrowCakeNotify.SetGameLevel, self.updateView, self)
    self:registerLocalNotify(ThrowCakeNotify.UpdateView, self.updateView, self)
end

function ThrowCakeGameRewardView:unbindEvents()
    self:unregisterLocalNotify(ThrowCakeNotify.SetGameLevel, self.updateView, self)
    self:unregisterLocalNotify(ThrowCakeNotify.UpdateView, self.updateView, self)
end

function ThrowCakeGameRewardView:updateView()
    self.level = ThrowCakeController.instance:getGameLevel()
    local cfg = ThrowCakeConfig.getLevelCfg(self.level)
    self._txtTitle.text = cfg.levelName
    self._txtDes.text = cfg.levelDesc

    local maxScore = ThrowCakeController.instance:getMaxScore()
    if maxScore > 0 then
        self._txtMaxScore.text = lang("最高得分：<color=#c76728>{1}</color>",maxScore)
    else
        self._txtMaxScore.text = lang("待通关")
    end

    self:buildRewards()
    -- 通关奖励
    if self.levelAwardIcon then
        CommonIconMgr.instance:returnCommonIcon(self.levelAwardIcon)
        self.levelAwardIcon = nil
    end
    local reward = cfg.levelAward[1]
    local needScore = cfg.dailyTargetScore
    self._txtHint1.text = lang("得分达到\n<color=#C76728>{1}</color>", needScore)
    local commonIcon = CommonIconMgr.instance:fetchCommonIcon()
    commonIcon:setWidthAndHeight(iconSize):showRarenessGo(true)
    goutil.addChildToParent(commonIcon:getPrefab(), self._awardGo)
    commonIcon:buildData(reward)
    self.levelAwardIcon = commonIcon
end
-- f6f4c3
function ThrowCakeGameRewardView:buildRewards()
    local rewardsCfg = ThrowCakeConfig.getRewardByLevel(self.level)
    local text = {
        [1] = "得分达到\n<color=#ffef94>{1}</color>",
        [2] = "得分达到\n<color=#ffef94>{1}</color>"
    }

    for i, v in ipairs(rewardsCfg) do
        self:clearIcons(i)
        local state = 1
        if v.needScore <= ThrowCakeController.instance:getMaxScore() then
            if ThrowCakeController.instance:getRewardHasGet(v.rewardId) then
                state = 3
            else
                state = 2
            end
        end
        print(lang("{1}关{2}档奖励状态：{3}", self.level, i, state))
        -- 1,2,3 未达成，可领取，已领取
        self.rewardContainers[i]._btnReceive.gameObject:SetActive(state == 2)
        self.rewardContainers[i]._imgNotReached:SetActive(state == 1)
        self.rewardContainers[i]._imgOver:SetActive(state == 3)
        local alpha  = 1
        if state == 3 then
            self.rewardContainers[i].scoreText.text = lang(text[2], v.needScore)
            alpha = 0.5 
        else
            self.rewardContainers[i].scoreText.text = lang(text[1], v.needScore)
        end
        self.rewardContainers[i].canvansGroup.alpha = alpha
        
        for j, reward in ipairs(v.reward) do
            local commonIcon = CommonIconMgr.instance:fetchCommonIcon()
            commonIcon:setWidthAndHeight(iconSize):showRarenessGo(true)
            goutil.addChildToParent(commonIcon:getPrefab(), self.rewardContainers[i].awardlist)
            commonIcon:buildData(reward)
            -- if state == 3 then
            --     commonIcon:showAwardState(CommonIcon.State_Received)
            -- end
            table.insert(self.rewardContainers[i].icons, commonIcon)
        end
        
    end
end

function ThrowCakeGameRewardView:clickReceive(params)
    local index = params[1]
    print(index)
    local reward = ThrowCakeConfig.getRewardByLevel(self.level)[index]
    ThrowCakeController.instance:receiveReward(reward.rewardId)
end

function ThrowCakeGameRewardView:clearIcons(i)
    for i, v in ipairs(self.rewardContainers[i].icons) do
        CommonIconMgr.instance:returnCommonIcon(v)
    end
    self.rewardContainers[i].icons = {}
end

function ThrowCakeGameRewardView:onExit()
    for i = 1, 3 do
        self:clearIcons(i)
    end
    if self.levelAwardIcon then
        CommonIconMgr.instance:returnCommonIcon(self.levelAwardIcon)
        self.levelAwardIcon = nil
    end
end

return ThrowCakeGameRewardView
