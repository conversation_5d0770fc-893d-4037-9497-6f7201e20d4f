module("logic.extensions.signinboard.view.SigninBoardEffectPool",package.seeall)

local SigninBoardEffectPool = class("SigninBoardEffectPool")

function SigninBoardEffectPool:ctor(infos)
    self.pools = {}
    self.infos = infos
	for k,v in ipairs(infos) do
        local pool = ObjectPool.New(
            v.capacity,
            handlerWithParams(self._createGo, self, {v.type}),
            handler(self._dispose, self),
            handler(self._reset, self)
        )
        self.pools[v.type] = pool
    end
end

function SigninBoardEffectPool:_createGo(type)
    for k,v in ipairs(self.infos) do
        if v.type == type then
            return v.createFunc()
        end
    end
    return
end

function SigninBoardEffectPool:_dispose(obj)
    goutil.destroy(obj)
end

function SigninBoardEffectPool:_reset(obj)
    goutil.setActive(obj, false)
end

function SigninBoardEffectPool:fetchObject(type)
    if not self.pools[type] then
        printError("不存在"..tostring(type).."类型的对象池")
        return
    end
    return self.pools[type]:fetchObject()
end

function SigninBoardEffectPool:returnObject(obj, type)
    if not self.pools[type] then
        printError("不存在"..tostring(type).."类型的对象池")
        return
    end
    self.pools[type]:returnObject(obj)
end

return SigninBoardEffectPool