module("logic.scene.common.interaction.CelebPartyInteraction", package.seeall)

local CelebPartyInteraction = class("CelebPartyInteraction", SceneInteractionBase)

function CelebPartyInteraction:parseInfo(info)
    Activity471Model.instance:setShowId(info.showId)
    Activity471Model.instance:setPath(info.path)
    Activity471Model.instance:setStep(info.step)
    Activity471Model.instance:setStepLeftMillTime(info.stepLeftMillTime)
    Activity471Model.instance:setQueueInfos(info.queueInfos)
end

function CelebPartyInteraction:clearInfo()
    Activity471Model.instance:setShowId(nil)
    Activity471Model.instance:setPath(nil)
    Activity471Model.instance:setStep(nil)
    Activity471Model.instance:setStepLeftMillTime(nil)
    Activity471Model.instance:setQueueInfos(nil)
end

function CelebPartyInteraction:onBegin(info, enter)
    self:parseInfo(info)
    CelebPartyMainManager.instance:begin(enter)
    ViewMgr.instance:open("CelebPartyHudView")
    Activity471Model.instance:setHasInteraction(true)
    GlobalDispatcher:addListener(GlobalNotify.LeaveScene, self.onLeaveScene, self)
    GlobalDispatcher:addListener(GlobalNotify.ReloadPlayer, self.onReloadPlayer, self)
end

function CelebPartyInteraction:onStop(leave)
    self:clearInfo()
    CelebPartyMainManager.instance:stop(leave)
    ViewMgr.instance:close("CelebPartyHudView")
    Activity471Model.instance:setHasInteraction(false)
    GlobalDispatcher:removeListener(GlobalNotify.LeaveScene, self.onLeaveScene, self)
    GlobalDispatcher:removeListener(GlobalNotify.ReloadPlayer, self.onReloadPlayer, self)
end

function CelebPartyInteraction:onChange(info)
    self:parseInfo(info)
    CelebPartyMainManager.instance:change()
    Activity471Controller.instance:localNotify(CelebPartyNotify.onInteractionChange)
end

function CelebPartyInteraction:onLeaveScene()
    self:onStop(true)
end

function CelebPartyInteraction:onReloadPlayer()
    CelebPartyMainManager.instance:restart()
end

return CelebPartyInteraction
