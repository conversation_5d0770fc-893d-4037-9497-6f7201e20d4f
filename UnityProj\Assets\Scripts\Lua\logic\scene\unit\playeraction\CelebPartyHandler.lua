module("logic.scene.unit.playeraction.CelebPartyHandler", package.seeall)

local CelebPartyHandler = class("CelebPartyHandler", PlayerActionHandlerBase)

local K = {
    ["1"] = "anim_1",
    ["2"] = "anim_2",
    ["3"] = "anim_3",
    ["4"] = "anim_4"
}

function CelebPartyHandler:onStart()
    self:onExecute()
end

function CelebPartyHandler:onStop()
    local animHelper = self.unit.skinView:getAnimHelper()
    if animHelper then
        animHelper:removeAllCallback()
    end
    local slot = self.unit.effectLoader:getSlot(Activity471Config.EffectKey)
    slot:clear()
end

function CelebPartyHandler:onUpdate()
    self:onExecute()
end

function CelebPartyHandler:onExecute()
    if not Activity471Model.instance:getHasInteraction() then
        return
    end
    local showId = Activity471Model.instance:getShowId()
    local path = Activity471Model.instance:getPath()
    local step = Activity471Model.instance:getStep()
    local showDefine = Activity471Config.getShow(showId)
    local pathDefine = Activity471Config.getPath(path, step)
    local attireDefine = Activity471Config.getAttire(showDefine.attire)
    if pathDefine.action or pathDefine.perform then
        return
    end
    local key = self.info.params[2]
    local once = self.info.params[3]
    local anim = attireDefine[K[key]]
    once = (tonumber(once) == 1)
    self:onPlayAnim(anim, once)
end

function CelebPartyHandler:onPlayAnim(anim, once)
    local slot = self.unit.effectLoader:getSlot(Activity471Config.HandKey)
    if slot.hand then
        slot.hand:setAnimation(anim, not once, 0, true)
    end
    self.unit:playAnimation(anim, not once, false, true)
    if once then
        local animHelper = self.unit.skinView:getAnimHelper()
        if animHelper then
            animHelper:removeCompleteCallback(anim, self.onAnimComplete, self)
            animHelper:addCompleteCallback(anim, self.onAnimComplete, self, true)
        end
    end
    local effect = self.unit.effectLoader:getSlot(Activity471Config.EffectKey)
    effect:load(string.format(Activity471Config.EffectResPath, anim))
end

function CelebPartyHandler:onAnimComplete(complete)
    if not Activity471Model.instance:getHasInteraction() then
        return
    end
    local showId = Activity471Model.instance:getShowId()
    local path = Activity471Model.instance:getPath()
    local step = Activity471Model.instance:getStep()
    local showDefine = Activity471Config.getShow(showId)
    local pathDefine = Activity471Config.getPath(path, step)
    local attireDefine = Activity471Config.getAttire(showDefine.attire)
    if pathDefine.action or pathDefine.perform then
        return
    end
    if complete then
        local effect = self.unit.effectLoader:getSlot(Activity471Config.EffectKey)
        effect:clear()
        local roleInfo = Activity471Controller.instance:getRoleInfo(self.unit.id)
        if roleInfo.mode == CelebPartyEnum.Mode.Walk then
            self:onPlayAnim(attireDefine.anim_3, false)
        elseif roleInfo.mode == CelebPartyEnum.Mode.Yell then
            self:onPlayAnim(attireDefine.anim_2, false)
        end
    end
end

return CelebPartyHandler
