module("logic.extensions.signinboard.view.SigninBoardDetailView",package.seeall)

local SigninBoardDetailView = class("SigninBoardDetailView", ViewComponent)

SigninBoardDetailView.bgIds = {1,3,5} -- 用大喇叭那边的底框id

--- view初始化时会执行
function SigninBoardDetailView:buildUI()
    SigninBoardDetailView.super.buildUI(self)
    self._btnClose = self:getBtn("btnclose/btnClose")
    self._btnhelp = self:getBtn("btnhelp")
    self._btnStart = self:getBtn("prgGo/btnStart")
    self._txtPrg = self:getText("prgGo/txtPrg")
    self._imgHeadIcon = self:getGo("prgGo/mainsigin/imgHeadIcon")
    self._mainsigin = self:getGo("prgGo/mainsigin")
    self._txtDes = self:getText("prgGo/mainsigin/txtDes")
    self._btnAward = self:getBtn("prgGo/btnAward")
    self._prgGo = self:getGo("prgGo")
    self._signinGo = self:getGo("signinGo")
    self._slider = self:getSlider("prgGo/progressGo/Slider")
    self._danmuGo = self:getGo("danmuGo")
    self._danmu = self:getGo("danmuGo/danmu")
    self._danmu:SetActive(false)
    self._prg_imgPreviewBG = self:getGo("prgGo/mainsigin/imgPreviewBG")
    self._prg_effect = self:getGo("prgGo/mainsigin/imgPreviewBG/effect")
    self._signin_imgPreviewBG = self:getGo("signinGo/imgPreviewBG")
    self._signin_effect = self:getGo("signinGo/imgPreviewBG/effect")
    self._input = self:getInput("signinGo/inputGo")
    self._txtTextCount = self:getText("signinGo/txtTextCount")
    self._btnSend = self:getBtn("signinGo/btnSend")
    self._btnBack = self:getBtn("signinGo/btnBack")
    self._itemContent = self:getGo("signinGo/topicGo/content")
    self._item_raw = self:getBtn("signinGo/topicGo/content/item_raw")
    self._item_raw.gameObject:SetActive(false)
    self._btnShare = self:getBtn("btnShare")
    self._Placeholder = self:getText("signinGo/inputGo/Placeholder")
    self._signin_txtEmpty = self:getText("signinGo/inputGo/txtEmpty")

    self._rewardCfg = Activity466Config.getAllAwardConfig()
    self._maxProgress = tonumber(Activity466Config.getCommonConfig("maxProgressValue"))
    self._messageCfg = Activity466Config.getMessageConfig()
    self._flyTime = tonumber(Activity466Config.getCommonConfig("flyTime"))
    self._messageMaxLength = tonumber(Activity466Config.getCommonConfig("messageMaxLength"))
    self._barragePool = ObjectPool.New(
        300,
        handler(self._createGo, self),
        handler(self._dispose, self),
        handler(self._reset, self)
    )
    self._typeInfos = {
        {type = 1, capacity=300, createFunc=handlerWithParams(self.getResInstance, self, {SigninBoardDetailPresentor.effectPaths[1]})},
        {type = 3, capacity=300, createFunc=handlerWithParams(self.getResInstance, self, {SigninBoardDetailPresentor.effectPaths[3]})},
        {type = 5, capacity=300, createFunc=handlerWithParams(self.getResInstance, self, {SigninBoardDetailPresentor.effectPaths[5]})},
    }
    self._effectPool = SigninBoardEffectPool.New(self._typeInfos)
    self._typeBtns = {}
    for i=1,#self._typeInfos do
        local btn = {}
        btn.type = self._typeInfos[i].type
        btn.btn = self:getBtn("signinGo/bubbleList/item_raw_"..btn.type)
        btn.imgSelect = self:getGo("signinGo/bubbleList/item_raw_"..btn.type.."/imgSelect")
        btn.imgSelect:SetActive(false)
        table.insert(self._typeBtns, btn)
    end
    self._canSelectMsgCfg = Activity466Config.getAllCanSelectMsgConfig()
    self._canSelectItems = {}
    for k,v in ipairs(self._canSelectMsgCfg) do
        local item = {}
        item.go = goutil.clone(self._item_raw.gameObject)
        item.go:SetActive(true)
        item.btn = Framework.ButtonAdapter.GetFrom(item.go, "")
        item.txt = goutil.findChildTextComponent(item.go, "topic/Text")
        item.txt.text = v.msg
        goutil.addChildToParent(item.go, self._itemContent)
        table.insert(self._canSelectItems, item)
    end
    self._showingBarrage = {}
end

--- view初始化时会执行，在buildUI之后
function SigninBoardDetailView:bindEvents()
    SigninBoardDetailView.super.bindEvents(self)
    self._btnClose:AddClickListener(self.close, self)
    self._btnhelp:AddClickListener(self._onClickHelp, self)
    self._btnStart:AddClickListener(self._onClickStart, self)
    self._btnAward:AddClickListener(self._onClickAward, self)
    self._btnSend:AddClickListener(self._onClickSend, self)
    self._btnBack:AddClickListener(self._onClickBack, self)
    self._input:AddOnValueChanged(self._onValueChange, self)
    for k,v in ipairs(self._typeBtns) do
        v.btn:AddClickListener(self._onClickBubble, self, {v.type})
    end
    for k,v in ipairs(self._canSelectItems) do
        v.btn:AddClickListener(self._onClickItem, self, {k})
    end
end

--- view销毁时会执行，在destroyUI之前
function SigninBoardDetailView:unbindEvents()
    SigninBoardDetailView.super.unbindEvents(self)
    self._btnClose:RemoveClickListener()
    self._btnhelp:RemoveClickListener()
    self._btnStart:RemoveClickListener()
    self._btnAward:RemoveClickListener()
    self._btnSend:RemoveClickListener()
    self._btnBack:RemoveClickListener()
    self._input:RemoveOnValueChanged()
    for k,v in ipairs(self._typeBtns) do
        v.btn:RemoveClickListener()
    end
    for k,v in ipairs(self._canSelectItems) do
        v.btn:RemoveClickListener()
    end
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function SigninBoardDetailView:onEnter()
    SigninBoardDetailView.super.onEnter(self)
    self._hasMessage = false
    self._selectedWordsLen = 0
    self._input:SetText("")
    self:_getInfo()
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function SigninBoardDetailView:onExit()
    SigninBoardDetailView.super.onExit(self)
    removetimer(self._showOneBarrage, self)
    self:_endBarrage()
    if self.prg_curEffectGo then
        self._effectPool:returnObject(self.prg_curEffectGo, self._selectedBgType)
    end
    if self.signin_curEffectGo then
        self._effectPool:returnObject(self.signin_curEffectGo, self.selectedType)
    end
    for k,v in ipairs(self._typeBtns) do
        v.imgSelect:SetActive(false)
    end
    self.selectedType = nil
    self.signin_curEffectGo = nil
    self.prg_curEffectGo = nil
end

function SigninBoardDetailView:_getInfo()
    Activity466Agent.instance:sendGetAct466InfoRequest(handler(self._onGetInfo, self))
end

function SigninBoardDetailView:_onGetInfo(curglobalprogress, selfmessage, backgroundindex, gainedglobalprogressrewardids)
    self._curGlobalProgress = curglobalprogress
    self._hasGetRewardIds = gainedglobalprogressrewardids or {}
    self._selectedBgType = backgroundindex
    -- 后端没校验，前端加个校验，如果底板不是1 3 5中的一种，那默认为1
    if self._selectedBgType ~= 0 and self._selectedBgType ~= 1 and self._selectedBgType ~= 3 and self._selectedBgType ~= 5 then
        self._selectedBgType = 1
    end
    if string.nilorempty(selfmessage) then
        self._hasMessage = false
        self._mainsigin:SetActive(false)
    else
        self._hasMessage = true
        self._mainsigin:SetActive(true)
        IconLoader.setSpriteToImg(self._prg_imgPreviewBG, GameUrl.getChatWorldHornBGPath(backgroundindex, false))
        self.prg_curEffectGo = self._effectPool:fetchObject(backgroundindex)
        self.prg_curEffectGo:SetActive(true)
        goutil.addChildToParent(self.prg_curEffectGo, self._prg_effect)
        HeadPortraitHelper.instance:setHeadPortraitWithUserId(self._imgHeadIcon, UserInfo.userId)
        self._txtDes.text = selfmessage
        local color = self._selectedBgType == 5 and parsecolor("#0c405e") or parsecolor("#ffffff")
        self._txtDes.color = color
    end
    self._btnAward.gameObject:SetActive(self._hasMessage and self:_hasProgressReward())
    self._btnStart.gameObject:SetActive(not self._hasMessage)
    self._progress = curglobalprogress/self._maxProgress
    self._progress = self._progress > 1 and 1 or self._progress
    self._txtPrg.text = tostring(math.floor(self._progress*100)).."%"
    self._slider:SetValue(self._progress)
    self:_onClickBack()
end

-- 是否有可领取的进度奖励
function SigninBoardDetailView:_hasProgressReward()
    for k,v in ipairs(self._rewardCfg) do
        if self._curGlobalProgress >= v.progressValue and table.indexof(self._hasGetRewardIds, v.id) == false then
            return true
        end
    end
    return false
end

function SigninBoardDetailView:_onClickHelp()
    local title = lang("SigninBoardDetailView_rule_title")
    local content = lang("SigninBoardDetailView_rule_content")
    ViewMgr.instance:open("ActivityRuleCommon", title, content)
end

function SigninBoardDetailView:_onClickStart()
    self:_endBarrage()
    self._prgGo:SetActive(false)
    self._signinGo:SetActive(true)
    self._btnShare.gameObject:SetActive(false)
    -- 默认选中下标为3的背景（策划要求）
    if not self.selectedType then
        self._typeBtns[3].imgSelect:SetActive(true)
        self.selectedType = 5
        IconLoader.setSpriteToImg(self._signin_imgPreviewBG, GameUrl.getChatWorldHornBGPath(self.selectedType, false))
        self.signin_curEffectGo = self._effectPool:fetchObject(self.selectedType)
        self.signin_curEffectGo:SetActive(true)
        goutil.addChildToParent(self.signin_curEffectGo, self._signin_effect)
        local color = parsecolor("#0c405e")
        self._Placeholder.color = color
        self._signin_txtEmpty.color = color
    end
end

function SigninBoardDetailView:_onClickAward()
    Activity466Agent.instance:sendGainAct466ProgressRewardRequest(handler(self._onGetAwardInfo, self))
end

function SigninBoardDetailView:_onGetAwardInfo(changesetid)
    if changesetid ~= 0 then
        DialogHelper.showRewardsDlgByCI(changesetid)
    end
    self:_getInfo()
end

-- 切换标题背景
function SigninBoardDetailView:_onClickBubble(params)
    local type = params[1]
    if self.selectedType == type then
        return
    end
    if self.signin_curEffectGo then
        self._effectPool:returnObject(self.signin_curEffectGo, self.selectedType)
    end
    self.selectedType = type
    for k,v in ipairs(self._typeBtns) do
        v.imgSelect:SetActive(v.type == self.selectedType)
    end
    local color = self.selectedType == 5 and parsecolor("#0c405e") or parsecolor("#ffffff")
    self._Placeholder.color = color
    self._signin_txtEmpty.color = color
    IconLoader.setSpriteToImg(self._signin_imgPreviewBG, GameUrl.getChatWorldHornBGPath(self.selectedType, false))
    self.signin_curEffectGo = self._effectPool:fetchObject(self.selectedType)
    self.signin_curEffectGo:SetActive(true)
    goutil.addChildToParent(self.signin_curEffectGo, self._signin_effect)
end

-- 点击可选词条
function SigninBoardDetailView:_onClickItem(params)
    local index = params[1]
    local cfg = self._canSelectMsgCfg[index]
    if self:_getWordsLen(cfg.msg) + self._selectedWordsLen > self._messageMaxLength then
		FlyTextManager.instance:showFlyText(lang("超过字数限制"))
		return
	end
    local input = self._input:GetText()
    self._input:SetText(input..cfg.msg)
    self._selectedWordsLen = self:_getWordsLen(self._input:GetText())
    self._txtTextCount.text = string.format("%s/%s", self._selectedWordsLen, self._messageMaxLength / 2)
end

function SigninBoardDetailView:_onValueChange(inputStr)
    inputStr = GameUtils.getBriefName(inputStr, self._messageMaxLength, '')
    self._input:SetText(inputStr)
    self._selectedWordsLen = self:_getWordsLen(self._input:GetText())
    self._txtTextCount.text = string.format("%s/%s", self._selectedWordsLen, self._messageMaxLength / 2)
end

-- 留言
function SigninBoardDetailView:_onClickSend()
    local input = self._input:GetText()
    if string.nilorempty(input) then
        FlyTextManager.instance:showFlyText(lang("请输入内容"))
        return
    end
    local msg = lang("是否发送当前留言？")
    DialogHelper.showConfirmDlg(msg,function(isOk)
        if isOk then
            Activity466Agent.instance:sendAct466LeaveMessageRequest(input, self.selectedType, handler(self._onGetSendInfo, self))
        end
    end)
end

function SigninBoardDetailView:_onGetSendInfo(changesetid)
    DialogHelper.showRewardsDlgByCI(changesetid)
    self:_getInfo()
end

function SigninBoardDetailView:_onClickBack()
    self._prgGo:SetActive(true)
    self._signinGo:SetActive(false)
    self._btnShare.gameObject:SetActive(true)
    self:_startBarrage()
end

-- 开始显示弹幕
function SigninBoardDetailView:_startBarrage()
    local progress = math.floor(self._progress*100)
    progress = progress > 100 and 100 or progress
    local interval = Activity466Config.getMessageSpeed(progress)
    settimer(interval, self._showOneBarrage, self, true)
end

-- 结束弹幕
function SigninBoardDetailView:_endBarrage()
    removetimer(self._showOneBarrage, self)
    for k,v in ipairs(self._showingBarrage) do
        if v.tween then
            v.tween:Kill()
        end
        self._effectPool:returnObject(v.effectGo, v.type)
        self._barragePool:returnObject(v.barrageGo)
    end
    self._showingBarrage = {}
end

function SigninBoardDetailView:_showOneBarrage()
    local barrageGo = self._barragePool:fetchObject()
    barrageGo:SetActive(true)
    local msg = arrayutil.randomOne(self._messageCfg).message
    local type = arrayutil.randomOne(SigninBoardDetailView.bgIds)
    local txtDes = goutil.findChildTextComponent(barrageGo, "txtDes")
    txtDes.text = lang(msg)
    local color = type == 5 and parsecolor("#0c405e") or parsecolor("#ffffff")
    txtDes.color = color
    local imgBgGo = goutil.findChild(barrageGo, "imgPreviewBG")
    IconLoader.setSpriteToImg(imgBgGo, GameUrl.getChatWorldHornBGPath(type, false))
    -- 加载特效
    local effect = goutil.findChild(barrageGo, "imgPreviewBG/effect")
    local effectGo = self._effectPool:fetchObject(type)
    effectGo:SetActive(true)
    goutil.addChildToParent(effectGo, effect)
    GameUtils.setLocalPos(barrageGo, 1000, math.random(-140, 140), 0)
    local barrageInfo = {barrageGo=barrageGo, effectGo=effectGo, type=type}
    table.insert(self._showingBarrage, barrageInfo)
    local tween = barrageGo.transform:DOLocalMoveX(-1000, self._flyTime, false):SetEase(DG.Tweening.Ease.Linear):OnComplete(function()
        self._effectPool:returnObject(effectGo, type)
        self._barragePool:returnObject(barrageGo)
        table.removebyvalue(self._showingBarrage, barrageInfo)
    end)
    barrageInfo.tween = tween
end

function SigninBoardDetailView:_getWordsLen(str)
	return math.ceil(GameUtils.getStrLen(str) / 2)
end

function SigninBoardDetailView:_createGo()
    local go = goutil.clone(self._danmu)
    go:SetActive(true)
    goutil.addChildToParent(go, self._danmuGo)
    return go
end

function SigninBoardDetailView:_dispose(obj)
    goutil.destroy(obj)
end

function SigninBoardDetailView:_reset(obj)
    goutil.setActive(obj, false)
end

return SigninBoardDetailView