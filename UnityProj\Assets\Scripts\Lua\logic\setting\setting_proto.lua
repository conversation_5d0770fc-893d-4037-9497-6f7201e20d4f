--auto generated

local setting_proto = {}

setting_proto = {
    [1] = {
        [255] = "UserExtension",
        [1] = {"GetRoleInfoRequest", "GetRoleInfoReply", ["block"]=true},
        [2] = {"CreateRoleRequest", "CreateRoleReply", ["block"]=true},
        [3] = {"HeartBeatRequest", "HeartBeatReply", ["block"]=true},
        [4] = {"SyncServerTimeRequest", "SyncServerTimeReply", ["block"]=true},
        [5] = {"ModifyInformationCardRequest", "ModifyInformationCardReply"},
        [6] = {"GetRoleInformationCardRequest", "GetRoleInformationCardReply"},
        [7] = {"ModifyNickNameRequest", "ModifyNickNameReply"},
        [8] = {"SelectRoleInfoByUserIdRequest", "SelectRoleInfoByUserIdReply"},
        [9] = {"ChangeTitleRequest", "ChangeTitleReply"},
        [10] = {"ReportPlatformInfoRequest", "ReportPlatformInfoReply", ["block"]=true},
        [11] = {"RoleLevelOrExpChangePush"},
        [12] = {"ServerRefreshPush"},
        [13] = {"ServerShutdownPush"},
        [14] = {"SurveyRewardPush"},
        [15] = {"ForbidSpeakPush"},
        [16] = {"GMCloseSystemPush"},
        [17] = {"ClientHotVersionPush"},
        [21] = {"LogoutRequest", "LogoutReply", ["block"]=true},
        [22] = {"RealNameFinishRequest", "RealNameFinishReply"},
        [23] = {"ClientReportRequest", "ClientReportReply", ["block"]=true},
        [24] = {"FeedbackRequest", "FeedbackReply"},
        [25] = {"ExchangeActiveCodeRequest", "ExchangeActiveCodeReply"},
        [26] = {"ChangeHeadWindowsRequest", "ChangeHeadWindowsReply"},
        [27] = {"GetAllHeadWindowsRequst", "GetAllHeadWindowsReply"},
        [28] = {"GetUserIsOnlineRequest", "GetUserIsOnlineReply"},
        [29] = {"ReportUserRequest", "ReportUserReply"},
        [30] = {"GetSurveyRequest", "GetSurveyReply"},
        [41] = {"ChooseIslandRacialRequest", "ChooseIslandRacialReply"},
        [42] = {"GetIslandRacialInfoRequest", "GetIslandRacialInfoReply"},
        [43] = {"ModifySexRequest", "ModifySexReply"},
        [44] = {"CheckHasSurveyRequest", "CheckHasSurveyReply"},
        [45] = {"GetUserSimpleInfoCardRequest", "GetUserSimpleInfoCardReply"},
        [46] = {"CheckContentSensitiveTextRequest", "CheckContentSensitiveTextReply"},
        [47] = {"FinishHappyBirthdayPartyRequest", "FinishHappyBirthdayPartyReply"}
    },
    [2] = {
        [255] = "SceneExtension",
        [1] = {"JoinSceneRequest", "JoinSceneReply"},
        [2] = {"MoveRequest", "MoveReply"},
        [3] = {"LeaveSceneRequest", "LeaveSceneReply"},
        [4] = {"SceneShareRequest", "SceneShareReply"},
        [5] = {"CancelSceneShareRequest", "CancelSceneShareReply"},
        [7] = {"BroadcastStateRequest", "BroadcastStateReply"},
        [8] = {"GetCurrentSceneRequest", "GetCurrentSceneReply"},
        [9] = {"GetPublicSceneChannelsRequest", "GetPublicSceneChannelsReply"},
        [10] = {"GetRoleInSceneInfoRequest", "GetRoleInSceneInfoReply"},
        [11] = {"SceneChangePush"},
        [12] = {"SceneStateInfoPush"},
        [13] = {"BroadcastStateInfoPush"},
        [14] = {"SceneShareDropPush"},
        [15] = {"SceneEntryInfoPush"},
        [16] = {"SceneKickOutPush"},
        [21] = {"NpcControlInfoSyncRequest", "NpcControlInfoSyncReply"},
        [22] = {"RoleHeadBubbleRequest", "RoleHeadBubbleReply"},
        [23] = {"MakeBubbleRequest", "MakeBubbleReply"},
        [24] = {"CancelRoleHeadBubbleRequest", "CancelRoleHeadBubbleReply"},
        [25] = {"GetSceneTeamInfoRequest", "GetSceneTeamInfoReply"},
        [26] = {"StartSceneTeamCopyRequest", "StartSceneTeamCopyReply"},
        [27] = {"KickSceneTeamUserRequest", "KickSceneTeamUserReply"},
        [28] = {"RanVisitUserIdRequest", "RanVisitUserIdReply"},
        [29] = {"GetCustomMatRewardRecordRequest", "GetCustomMatRewardRecordReply"},
        [31] = {"SameGroupSceneChangePush"}
    },
    [3] = {
        [255] = "HouseExtension",
        [1] = {"GetHouseInfoRequest", "GetHouseInfoReply"},
        [2] = {"DecorateHouseRequest", "DecorateHouseReply"},
        [4] = {"HouseTakeHeartRequest", "HouseTakeHeartReply"},
        [5] = {"HouseUpgradeRequest", "HouseUpgradeReply"},
        [6] = {"GetAllHouseInfoRequest", "GetAllHouseInfoReply"},
        [7] = {"ChangeHouseRequest", "ChangeHouseReply"},
        [8] = {"UnlockHouseRequest", "UnlockHouseReply"},
        [9] = {"SetHouseNameRequest", "SetHouseNameReply"},
        [10] = {"GetAllUnlockHouseRequest", "GetAllUnlockHouseReply"},
        [11] = {"HouseChangedPush"},
        [21] = {"HouseFurnitureChangeClothesRequest", "HouseFurnitureChangeClothesReply"},
        [22] = {"HouseFurnitureSwitchMarkRequest", "HouseFurnitureSwitchMarkReply"},
        [23] = {"ChangeElfHouseRequest", "ChangeElfHouseReply"},
        [24] = {"ChangeShowAreaEffectsRequest", "ChangeShowAreaEffectsReply"},
        [25] = {"ChangeDefaultVisitAreaRequest", "ChangeDefaultVisitAreaReply"},
        [26] = {"GetDefaultVisitAreaRequest", "GetDefaultVisitAreaReply"},
        [27] = {"HouseChangeCustomFurnitureParamsRequest", "HouseChangeCustomFurnitureParamsReply"}
    },
    [4] = {
        [255] = "ClothesExtension",
        [1] = {"ChangeClothesRequest", "ChangeClothesReply"},
        [2] = {"GetClothesCollectionsRequest", "GetClothesCollectionsReply"},
        [3] = {"CollectCurrentClothesRequest", "CollectCurrentClothesReply"},
        [4] = {"DeleteClothesCollectionsRequest", "DeleteClothesCollectionsReply"},
        [5] = {"GetClothesShowInfoRequest", "GetClothesShowInfoReply"},
        [6] = {"EditClothesShowRequest", "EditClothesShowReply"},
        [7] = {"UnlockClothesShowRequest", "UnlockClothesShowReply"},
        [8] = {"LikeOrUnlikeClothesShowRequest", "LikeOrUnlikeClothesShowReply"},
        [9] = {"ChangeModelRequest", "ChangeModelReply"},
        [10] = {"GetChangeTimesRequest", "GetChangeTimesReply"},
        [11] = {"RoleSimpleInfoChangePush"},
        [21] = {"GetClothesDyePlateInfoRequest", "GetClothesDyePlateInfoReply"},
        [22] = {"UnlockClothesDyePlateRequest", "UnlockClothesDyePlateReply"},
        [23] = {"ClothesDyeRequest", "ClothesDyeReply"},
        [24] = {"GetAllClothesDyeRequest", "GetAllClothesDyeReply"},
        [25] = {"GetAllClothesDyePlateInfoRequest", "GetAllClothesDyePlateInfoReply"},
        [26] = {"ClothesDyeBatchRequest", "ClothesDyeBatchReply"},
        [27] = {"UnlockClothesDyePlateBatchRequest", "UnlockClothesDyePlateBatchReply"},
        [31] = {"UseClothesTeleportRequest", "UseClothesTeleportReply"},
        [32] = {"ChangeClothesTeleportLimitRequest", "ChangeClothesTeleportLimitReply"},
        [33] = {"GetClothesTeleportLimitRequest", "GetClothesTeleportLimitReply"},
        [34] = {"GetClothesTeleportRewardRequest", "GetClothesTeleportRewardReply"},
        [41] = {"ClothesDyeChangeInfoPush"}
    },
    [5] = {
        [255] = "BackpackExtension",
        [1] = {"GetAllItemsRequest", "GetAllItemsReply"},
        [2] = {"SellItemRequest", "SellItemReply"},
        [3] = {"ExtendBackpackCapacityRequest", "ExtendBackpackCapacityReply"},
        [4] = {"UseItemRequest", "UseItemReply"},
        [5] = {"ExchangeItemRequest", "ExchangeItemReply"},
        [6] = {"ReduceTiredValueRequest", "ReduceTiredValueReply"},
        [7] = {"HolidayRequest", "HolidayReply"},
        [8] = {"GetInteractiveProduceInfoRequest", "GetInteractiveProduceInfoReply"},
        [9] = {"InteractiveProduceRequest", "InteractiveProduceReply"},
        [10] = {"GetChestOwnItemRequest", "GetChestOwnItemReply"},
        [11] = {"BackpackChangeInfoPush"},
        [12] = {"GainLimitChangePush"},
        [21] = {"GetInteractivePropCollectListRequest", "GetInteractivePropCollectListReply"},
        [22] = {"CollectInteractivePropRequest", "CollectInteractivePropReply"}
    },
    [6] = {
        [255] = "WorkShopExtension",
        [1] = {"ArticleCompoundRequest", "ArticleCompoundReply"},
        [2] = {"UpgradeWorkshopRequest", "UpgradeWorkshopReply"},
        [3] = {"CollectArticleRequset", "CollectArticleReply"},
        [4] = {"GetWorkShopInfoRequest", "GetWorkShopInfoReply"},
        [5] = {"QuickUpgradeWorkshopRequest", "QuickUpgradeWorkshopReply"},
        [6] = {"UpgradeCompleteWorkshopRequest", "UpgradeCompleteWorkshopReply"},
        [7] = {"GetWorkShopUpgradeConditionRequest", "GetWorkShopUpgradeConditionReply"},
        [8] = {"GetCompoundArticleRequest", "GetCompoundArticleReply"},
        [9] = {"GetWorkshopMysteryInfoRequest", "GetWorkshopMysteryInfoReply"},
        [10] = {"GetWorkshopAllMysteryInfoRequest", "GetWorkshopAllMysteryInfoReply"},
        [11] = {"WorkshopInfoPush"},
        [21] = {"WorkShopQuickCompoundRequest", "WorkShopQuickCompoundReply"},
        [22] = {"WorkShopCancelCompoundRequest", "WorkShopCancelCompoundReply"}
    },
    [7] = {
        [255] = "TaskExtension",
        [1] = {"TaskInfoRequest", "TaskInfoReply"},
        [2] = {"StartTaskRequest", "StartTaskReply"},
        [3] = {"IncrTargetRequest", "IncrTargetReply"},
        [4] = {"FinishStepRequest", "FinishStepReply"},
        [5] = {"SubmitItemRequest", "SubmitItemReply"},
        [6] = {"ReceiveTaskAwardsRequest", "ReceiveTaskAwardsReply"},
        [7] = {"TaskCommandRequest", "TaskCommandReply"},
        [8] = {"TaskQuickCompletionTargetRequest", "TaskQuickCompletionTargetReply"},
        [9] = {"ReceiveTaskChapterAwardsRequest", "ReceiveTaskChapterAwardsReply"},
        [11] = {"FinishTaskRewardPush"},
        [12] = {"UpdateTaskPush"},
        [21] = {"ReceiveDailyTaskProcessAwardsRequest", "ReceiveDailyTaskProcessAwardsReply"},
        [22] = {"GetDailyTaskRemedyRewardDateRequest", "GetDailyTaskRemedyRewardDateReply"},
        [23] = {"RemedyDailyTaskRequest", "RemedyDailyTaskReply"},
        [41] = {"UpdateIslanderEventProgressRequest", "UpdateIslanderEventProgressReply"},
        [42] = {"GiveUpIslanderEventRequest", "GiveUpIslanderEventReply"},
        [43] = {"IslanderEventInfoRequest", "IslanderEventInfoReply"}
    },
    [8] = {
        [255] = "IslandExtension",
        [1] = {"GetIslandInfoRequest", "GetIslandInfoReply"},
        [2] = {"DecorateIslandRequest", "DecorateIslandReply"},
        [3] = {"ManorPlantRequest", "ManorPlantReply"},
        [4] = {"ManorGatherCropRequest", "ManorGatherCropReply"},
        [5] = {"ManorCleanRubbishRequest", "ManorCleanRubbishReply"},
        [6] = {"ManorSpeedUpCropRequest", "ManorSpeedUpCropReply"},
        [7] = {"ManorUnlockSectorRequest", "ManorUnlockSectorReply"},
        [8] = {"ManorMoveAnimalRequest", "ManorMoveAnimalReply"},
        [9] = {"ManorGetRubbishInfoRequest", "ManorGetRubbishInfoReply"},
        [10] = {"ManorCompleteUnlockSectorRequest", "ManorCompleteUnlockSectorReply"},
        [11] = {"CropItemChangePush"},
        [12] = {"FurnitureInfoChangePush"},
        [21] = {"FriendTreeFosterRequest", "FriendTreeFosterReply"},
        [22] = {"FriendTreeGainRequest", "FriendTreeGainReply"},
        [23] = {"OpenButterflyViewRequest", "OpenButterflyViewReply"},
        [24] = {"CatchButterflyRequest", "CatchButterflyReply"},
        [25] = {"TakeButterflyBoxHeartRequest", "TakeButterflyBoxHeartReply"},
        [26] = {"GetAllIslandDiaryRequest", "GetAllIslandDiaryReply"},
        [27] = {"IsLandFurnitureChangeClothesRequest", "IsLandFurnitureChangeClothesReply"},
        [28] = {"IslandFurnitureSwitchMarkRequest", "IslandFurnitureSwitchMarkReply"},
        [29] = {"IslandSaveFurnitureTemplateRequest", "IslandSaveFurnitureTemplateReply"},
        [30] = {"IslandGetAllFurnitureTemplateRequest", "IslandGetAllFurnitureTemplateReply"},
        [31] = {"IslandModifyFurnitureTemplateNameRequest", "IslandModifyFurnitureTemplateNameReply"},
        [32] = {"IslandUnlockFurnitureTemplateRequest", "IslandUnlockFurnitureTemplateReply"},
        [33] = {"IslandDecoratePillarRequest", "IslandDecoratePillarReply"},
        [34] = {"IslandChangeCustomFurnitureParamsRequest", "IslandChangeCustomFurnitureParamsReply"}
    },
    [9] = {
        [255] = "CollectionExtension",
        [1] = {"GetAllCollectionPointRequest", "GetAllCollectionPointReply"},
        [2] = {"CollectionItemRequest", "CollectionItemReply"},
        [3] = {"CleanCollectionCdRequest", "CleanCollectionCdReply"},
        [4] = {"GetAllSceneCollectionInfoRequest", "GetAllSceneCollectionInfoReply"},
        [5] = {"GetCollectionInfoRequest", "GetCollectionInfoReply"},
        [11] = {"SceneCollectionPointPush"}
    },
    [10] = {
        [255] = "StoreExtension",
        [1] = {"GetStoreInfoRequest", "GetStoreInfoReply"},
        [2] = {"StoreBuyRequest", "StoreBuyReply"}
    },
    [11] = {
        [255] = "FriendExtension",
        [1] = {"GetFriendSummaryInfoRequest", "GetFriendSummaryInfoReply"},
        [2] = {"GetFriendDetailInfoRequest", "GetFriendDetailInfoReply"},
        [3] = {"AskFriendRequest", "AskFriendReply"},
        [4] = {"HandleFriendAskRequest", "HandleFriendAskReply"},
        [5] = {"RemoveFriendRequest", "RemoveFriendReply"},
        [6] = {"AddBlackRequest", "AddBlackReply"},
        [7] = {"RemoveBlackRequest", "RemoveBlackReply"},
        [8] = {"ToggleFollowIdolRequest", "ToggleFollowIdolReply"},
        [9] = {"ToggleStarFriendRequest", "ToggleStarFriendReply"},
        [10] = {"CloseFriendTabRequest", "CloseFriendTabReply"},
        [21] = {"GetFriendInfoBeforeShareRequest", "GetFriendInfoBeforeShareReply"},
        [22] = {"GetBlackInfoRequest", "GetBlackInfoReply"},
        [23] = {"SearchUserRequest", "SearchUserReply"},
        [24] = {"RecommandUserRequest", "RecommandUserReply"},
        [25] = {"BatchHandleFriendAskRequest", "BatchHandleFriendAskReply"},
        [26] = {"CloakAskRequest", "CloakAskReply"},
        [27] = {"GetFriendClothesRequest", "GetFriendClothesReply"},
        [28] = {"FriendSetNotesRequest", "FriendSetNotesReply"},
        [29] = {"FriendDeleteFansRequest", "FriendDeleteFansReply"},
        [41] = {"GetFriendshipPowerPrizeRequest", "GetFriendshipPowerPrizeReply"},
        [42] = {"UnlockFriendshipPoseRequest", "UnlockFriendshipPoseReply"},
        [43] = {"GetUnlockedFriendshipPoseRequest", "GetUnlockedFriendshipPoseReply"},
        [44] = {"AskAddRelativeRequest", "AskAddRelativeReply"},
        [45] = {"HandleRelativeAskRequest", "HandleRelativeAskReply"},
        [46] = {"RemoveRelativeRequest", "RemoveRelationReply"},
        [61] = {"SendRelativeWishRequest", "SendRelativeWishReply"},
        [62] = {"GetAllRelativeWishRequest", "GetAllRelativeWishReply"},
        [63] = {"GetRelativeWishInfoRequest", "GetRelativeWishInfoReply"},
        [64] = {"ReceiveRelativeWishRequest", "ReceiveRelativeWishReply"},
        [65] = {"GetSendWishInfoRequest", "GetSendWishInfoReply"},
        [66] = {"GetFriendInfoRequest", "GetFriendInfoReply"},
        [67] = {"GetRelationShipLimitRequest", "GetRelationShipLimitReply"},
        [68] = {"ModifyLimitRequest", "ModifyLimitReply"},
        [69] = {"AskHasLimitRequest", "AskHasLimitReply"},
        [70] = {"GetFriendInviteRequest", "GetFriendInviteReply"},
        [71] = {"ModifyFriendInviteRequest", "ModifyFriendInviteReply"},
        [72] = {"AskHasFriendInviteRequest", "AskHasFriendInviteReply"},
        [73] = {"GetGlobalLimitSettingRequest", "GetGlobalLimitSettingReply"},
        [74] = {"ModifyGlobalLimitSettingRequest", "ModifyGlobalLimitSettingReply"},
        [75] = {"AskHasGlobalLimitRequest", "AskHasGlobalLimitReply"},
        [81] = {"CreateFriendGroupRequest", "CreateFriendGroupReply"},
        [82] = {"DeleteFriendGroupRequest", "DeleteFriendGroupReply"},
        [83] = {"ModifyFriendGroupRequest", "ModifyFriendGroupReply"},
        [11] = {"FriendAddedPush"},
        [12] = {"FriendRemovedPush"},
        [13] = {"FriendAskAddedPush"},
        [14] = {"FanAddedPush"},
        [15] = {"FriendshipCountChangePush"},
        [16] = {"FriendRelativeAddPush"},
        [17] = {"RelativeAskAddPush"},
        [18] = {"SendRelativeWishPush"},
        [19] = {"FriendIdolDeletePush"}
    },
    [12] = {
        [255] = "LotteryEggExtension",
        [1] = {"LeGetDisplayRequest", "LeGetDisplayReply"},
        [2] = {"LeLotteryRequest", "LeLotteryReply"},
        [3] = {"CreditsExchangeRequest", "CreditsExchangeReply"},
        [4] = {"BuyTicketsRequest", "BuyTicketsReply"},
        [5] = {"GetActivityLotteryEggHistoryRequest", "GetActivityLotteryEggHistoryReply"},
        [6] = {"ChooseLotteryRequest", "ChooseLotteryReply"},
        [7] = {"GetLotteryProgressRewardRequest", "GetLotteryProgressRewardReply"}
    },
    [13] = {
        [255] = "ChatExtension",
        [1] = {"LoadChatChannelMessageRequest", "LoadChatChannelMessageReply"},
        [2] = {"ChatRequest", "ChatReply"},
        [3] = {"GetTemplateMessageInfoRequest", "GetTemplateMessageInfoReply"},
        [4] = {"SendChatMessageRequest", "SendChatMessageReply"},
        [5] = {"GetLoopMarqueeMessageRequest", "GetLoopMarqueeMessageReply"},
        [6] = {"SelectWorldChannelRequest", "SelectWorldChannelReply"},
        [7] = {"GetCurrentWorldChannelUserNumRequest", "GetCurrentWorldChannelUserNumReply"},
        [8] = {"GetPrivateMsgHistoryRequest", "GetPrivateMsgHistoryReply"},
        [9] = {"ChangeChatWindowsRequest", "ChangeChatWindowsReply"},
        [10] = {"IntelligentQuestionAnsweringRequest", "IntelligentQuestionAnsweringReply"},
        [11] = {"ChatChannelMessagePush"},
        [12] = {"MarqueeMessagePush"},
        [13] = {"SceneChatRoomPush"},
        [14] = {"SceneChatRoomBeKickedPush"},
        [21] = {"GetSceneChatRoomInfoRequest", "GetSceneChatRoomInfoReply"},
        [22] = {"GetSceneChatRoomUserInfoRequest", "GetSceneChatRoomUserInfoReply"},
        [23] = {"CreateSceneChatRoomRequest", "CreateSceneChatRoomReply"},
        [24] = {"ChangeSceneChatRoomRequest", "ChangeSceneChatRoomReply"},
        [25] = {"KickOutChatRoomUserRequest", "KickOutChatRoomUserReply"},
        [26] = {"ModifyChatRoomTitleRequest", "ModifyChatRoomTitleReply"},
        [27] = {"LikeIntelligentAnswerRequest", "LikeIntelligentAnswerReply"}
    },
    [14] = {
        [255] = "GoFishingExtension",
        [1] = {"StartFishingRequest", "StartFishingReply"},
        [2] = {"CompleteFishingRequest", "CompleteFishingReply"},
        [3] = {"GetFishInfoRequest", "GetFishInfoReply"},
        [4] = {"PutFishIntoSeaAreaRequest", "PutFishIntoSeaAreaReply"}
    },
    [15] = {
        [255] = "PartyExtension",
        [1] = {"GetHostPartyRequest", "GetHostPartyReply"},
        [2] = {"GetPartyDetailInfoRequest", "GetPartyDetailInfoReply"},
        [3] = {"CreateNewPartyRequest", "CreateNewPartyReply"},
        [4] = {"GetPartyInfoRequest", "GetPartyInfoReply"},
        [5] = {"EndPartyRequest", "EndPartyReply"},
        [6] = {"StrewFlowerRequest", "StrewFlowerReply"},
        [7] = {"GetPartyPrizeRequest", "GetPartyPrizeReply"},
        [8] = {"ModifyPartyDescRequest", "ModifyPartyDescReply"},
        [9] = {"SendGiftRequest", "SendGiftReply"},
        [10] = {"GetPartyRankingRequest", "GetPartyRankingReply"},
        [11] = {"PartyStatePush"},
        [12] = {"StrewFlowerPush"},
        [13] = {"PartyGiftPush"},
        [14] = {"GetPartyHistoryRankingTimeRequest", "GetPartyHistoryRankingTimeReply"},
        [15] = {"GetPartyHistoryRankingDetailInfoRequest", "GetPartyHistoryRankingDetailInfoReply"},
        [16] = {"GetWeeklyPopRewardInfoRequest", "GetWeeklyPopRewardInfoReply"},
        [17] = {"GetWeeklyPopRewardRequest", "GetWeeklyPopRewardReply"},
        [18] = {"GetPartyMainInfoRequest", "GetPartyMainInfoReply"},
        [19] = {"GetPartyGiftRankInfoRequest", "GetPartyGiftRankInfoReply"},
        [20] = {"GetNextPartyDetailInfoRequest", "GetNextPartyDetailInfoReply"}
    },
    [16] = {
        [255] = "MailExtension",
        [1] = {"GetItemRequest", "GetItemReply"},
        [2] = {"GetMailListRequest", "GetMailListReply"},
        [3] = {"DeleteMailRequest", "DeleteMailReply"},
        [4] = {"GetAllItemMailRequest", "GetAllItemMailReply"},
        [5] = {"DeleteAllMailRequest", "DeleteAllMailReply"}
    },
    [17] = {
        [255] = "FoodExtension",
        [1] = {"GetFoodItemCountRequest", "GetFoodItemCountReply"},
        [2] = {"CleanEmptyFoodItemRequest", "CleanEmptyFoodItemReply"},
        [3] = {"RemoveFoodItemRequest", "RemoveFoodItemReply"},
        [4] = {"ModifyFoodItemRequest", "ModifyFoodItemReply"},
        [5] = {"CheckConsumerRequest", "CheckConsumerReply"},
        [6] = {"PullAvailableNPCListRequest", "PullAvailableNPCListReply"},
        [7] = {"ClearCDRequest", "ClearCDReply"},
        [8] = {"InviteNPCRequest", "InviteNPCReply"},
        [9] = {"ConsumeFoodItemRequest", "ConsumeFoodItemReply"}
    },
    [18] = {
        [255] = "OrderExtension",
        [1] = {"GetOrderInfoRequest", "GetOrderInfoReply"},
        [2] = {"CreateOrderRequest", "CreateOrderReply"},
        [3] = {"CommitOrderRequest", "CommitOrderReply"},
        [4] = {"RejectOrderRequest", "RejectOrderReply"},
        [5] = {"FinishOrderRequest", "FinishOrderReply"},
        [6] = {"CancelOrderRequest", "CancelOrderReply"},
        [7] = {"RejectAllOrderRequest", "RejectAllOrderReply"},
        [8] = {"ClearNpcOrderWaitingRequest", "ClearNpcOrderWaitingReply"},
        [9] = {"BuyCommitTimesRequest", "BuyCommitTimesReply"}
    },
    [19] = {
        [255] = "FavorabilityExtension",
        [1] = {"GetPrizeRequest", "GetPrizeReply"},
        [2] = {"GetFavorabilityInfoRequest", "GetFavorabilityInfoReply"},
        [3] = {"GiveGiftToNpcRequest", "GiveGiftToNpcReply"},
        [4] = {"ChatWithNpcRequest", "ChatWithNpcReply"},
        [11] = {"FavorabilityPushInfo"}
    },
    [20] = {
        [255] = "TinyGameExtension",
        [1] = {"LaunchGameRequest", "LaunchGameReply"},
        [2] = {"StartGameRequest", "StartGameReply"},
        [3] = {"JoinGameRequest", "JoinGameReply"},
        [4] = {"KickOutGameRequest", "KickOutGameReply"},
        [5] = {"ExitGameRequest", "ExitGameReply"},
        [11] = {"GameInfoNOPush"},
        [12] = {"KickOutGamePush"},
        [13] = {"TinyGamePrizePush"},
        [21] = {"ShakingColaRequest", "ShakingColaReply"},
        [26] = {"ColaGameInfoNoPush"},
        [31] = {"AttackMoleRequest", "AttackMoleReply"},
        [32] = {"GetAttackMoleScoreRequest", "GetAttackMoleScoreReply"},
        [36] = {"MoleAttackInfoPush"},
        [37] = {"MoleAttackResultPush"},
        [41] = {"SnowballHitPersonRequest", "SnowballHitPersonReply"},
        [42] = {"ThrowSnowballRequest", "ThrowSnowballReply"},
        [43] = {"SpeedUpRequest", "SpeedUpReply"},
        [46] = {"SnowballHitPersonPush"},
        [47] = {"ThrowSnowballPush"},
        [48] = {"SnowballPlayerSpeedUpPush"},
        [51] = {"SelectGridRequest", "SelectGridReply"},
        [52] = {"GetFightRankingListRequest", "GetFightRankingListReply"},
        [53] = {"GetChallengeFightScoreInfoRequest", "GetChallengeFightScoreInfoReply"},
        [54] = {"GetChallengeFightScoreRewardRequest", "GetChallengeFightScoreRewardReply"},
        [56] = {"WeedOutInfoPush"},
        [57] = {"SelectGridInfoPush"},
        [58] = {"StartFightInfoPush"},
        [61] = {"HitRiceCakesRequest", "HitRiceCakesReply"},
        [62] = {"PutRiceCakesRequest", "PutRiceCakesReply"},
        [66] = {"RiceCakesStatePush"},
        [67] = {"RiceCakesStartPush"},
        [71] = {"ChooseFlagDirectionRequest", "ChooseFlagDirectionReply"},
        [76] = {"ReverseFlagScorePush"},
        [77] = {"ChooseFlagDirectionPush"},
        [78] = {"ReverseFlagStartPush"},
        [81] = {"TransferWheatRequest", "TransferWheatReply"},
        [86] = {"HandOutWheatPush"},
        [87] = {"TransferWheatPush"},
        [91] = {"ChooseBeehiveRequest", "ChooseBeehiveReply"},
        [96] = {"GatherHoneyCalculatePush"},
        [101] = {"FlowerCloudLootChairRequest", "FlowerCloudLootChairReply"},
        [106] = {"FlowerCloudChairRefreshPush"},
        [107] = {"FlowerCloudRoundCalculatePush"},
        [111] = {"StackCakeThrowRequest", "StackCakeThrowReply"},
        [116] = {"StackCakeThrowPush"}
    },
    [21] = {
        [255] = "BuffExtension",
        [1] = {"GetAllBuffRequest", "GetAllBuffReply"},
        [11] = {"BuffChangePush"}
    },
    [22] = {
        [255] = "SceneGameExtension",
        [1] = {"StartSceneGameRequest", "StartSceneGameReply"},
        [2] = {"EndSceneGameRequest", "EndSceneGameReply"},
        [21] = {"PassTimeBombRequest", "PassTimeBombReply"}
    },
    [23] = {
        [255] = "ImageExtension",
        [1] = {"GetUploadTokenRequest", "GetUploadTokenReply"},
        [2] = {"UploadSuccessRequest", "UploadSuccessReply"},
        [3] = {"GetDownloadUrlRequest", "GetDownloadUrlReply"}
    },
    [24] = {
        [255] = "TradingExtension",
        [1] = {"ExpandSaleCountRequest", "ExpandSaleCountReply"},
        [2] = {"GetRegisteredGoodsRequest", "GetRegisteredGoodsReply"},
        [3] = {"RegisterSaleRequest", "RegisterSaleReply"},
        [4] = {"UnregisterSaleRequest", "UnregisterSaleReply"},
        [5] = {"PurchaseTransactionalGoodsRequest", "PurchaseTransactionalGoodsReply"},
        [6] = {"CompleteTransactionRequest", "CompleteTransactionReply"},
        [7] = {"SearchRegisteredGoodsCountRequest", "SearchRegisteredGoodsCountReply"},
        [8] = {"SearchRegisteredGoodsRequest", "SearchRegisteredGoodsReply"},
        [11] = {"PurchasedSuccessTradingPush"}
    },
    [25] = {
        [255] = "GroceryExtension",
        [1] = {"GetGroceryInfoRequest", "GetGroceryInfoReply"},
        [2] = {"RefreshGoodsCdTimeRequest", "RefreshGoodsCdTimeReply"},
        [3] = {"RefreshAllRandomGoodsRequest", "RefreshAllRandomGoodsReply"},
        [4] = {"BuyGroceryGoodsRequest", "BuyGroceryGoodsReply"},
        [11] = {"GroceryRefreshPush"}
    },
    [26] = {
        [255] = "MomentExtension",
        [1] = {"GetMessageBoardRequest", "GetMessageBoardReply"},
        [2] = {"LeaveMessageRequest", "LeaveMessageReply"},
        [3] = {"DeleteMessageRequest", "DeleteMessageReply"},
        [4] = {"JumpMessageRequest", "JumpMessageReply"},
        [11] = {"MessageInfoPush"},
        [21] = {"GetMomentsRequest", "GetMomentsReply"},
        [22] = {"GetMomentDetailRequest", "GetMomentDetailReply"},
        [23] = {"PublishMomentRequest", "PublishMomentReply"},
        [24] = {"LeaveMomentCommentRequest", "LeaveMomentCommentReply"},
        [25] = {"DeleteMomentOrCommentRequest", "DeleteMomentOrCommentReply"},
        [26] = {"LikeMomentRequest", "LikeMomentReply"},
        [27] = {"JumpMomentRequest", "JumpMomentReply"},
        [31] = {"MomentMessageInfoPush"},
        [41] = {"GetMomentMessageTipsRequest", "GetMomentMessageTipsReply"},
        [42] = {"GetAllMomentMessageTipIdsRequest", "GetAllMomentMessageTipIdsReply"}
    },
    [27] = {
        [255] = "RedPointExtension",
        [1] = {"GetRedPointInfoRequest", "GetRedPointInfoReply"},
        [2] = {"ClearRedPointRequest", "ClearRedPointReply"},
        [3] = {"ClearRedPointParamsRequest", "ClearRedPointParamsReply"},
        [11] = {"RedPointPushNo"}
    },
    [28] = {
        [255] = "AchievementExtension",
        [1] = {"GetAchievementInfoRequest", "GetAchievementInfoReply"},
        [2] = {"GetAchievementPrizeRequest", "GetAchievementPrizeReply"},
        [3] = {"GetLastCompleteAchievementRequest", "GetLastCompleteAchievementReply"},
        [4] = {"SwitchShowAchievementRequest", "SwitchShowAchievementReply"},
        [5] = {"GetAchievementSimpleInfoRequest", "GetAchievementSimpleInfoReply"},
        [6] = {"GetAchievementPointPrizeRequest", "GetAchievementPointPrizeReply"},
        [7] = {"AddClientAchievementCountRequest", "AddClientAchievementCountReply"}
    },
    [29] = {
        [255] = "ArchiveExtension",
        [1] = {"GetArchiveSuiteInfoRequest", "GetArchiveSuiteInfoReply"},
        [2] = {"GetArchiveRewardRequest", "GetArchiveRewardReply"},
        [3] = {"GetArchiveItemRequest", "GetArchiveItemReply"},
        [4] = {"GetArchiveDataInfosRequest", "GetArchiveDataInfosReply"},
        [5] = {"GetArchiveSimpleInfoRequest", "GetArchiveSimpleInfoReply"},
        [6] = {"GetArchiveAnimalRequest", "GetArchiveAnimalReply"},
        [7] = {"GetEcoparkArchiveSuiteInfoRequest", "GetEcoparkArchiveSuiteInfoReply"},
        [8] = {"GetEcoparkArchiveSuiteRewardRequest", "GetEcoparkArchiveSuiteRewardReply"},
        [9] = {"GetEcoparkArchiveProgressRewardRequest", "GetEcoparkArchiveProgressRewardReply"},
        [10] = {"GetSeaSceneFishArchiveInfoRequest", "GetSeaSceneFishArchiveInfoReply"},
        [11] = {"PushNewArchieve"},
        [21] = {"ClientAddArchiveRequest", "ClientAddArchiveReply"}
    },
    [30] = {
        [255] = "PetExtension",
        [1] = {"GetPetInfoRequest", "GetPetInfoReply"},
        [2] = {"ExpandPetCapacityRequest", "ExpandPetCapacityReply"},
        [3] = {"PetSwitchHomeIndexRequest", "PetSwitchHomeIndexReply"},
        [4] = {"ModifyPetNameRequest", "ModifyPetNameReply"},
        [5] = {"PetChangeStateRequest", "PetChangeStateReply"},
        [6] = {"PetHarvestRequest", "PetHarvestReply"},
        [7] = {"PetEvolutionRequest", "PetEvolutionReply"},
        [8] = {"PetReleaseRequest", "PetReleaseReply"},
        [9] = {"LurePetRequest", "LurePetReply"},
        [10] = {"CatchPetRequest", "CatchPetReply"},
        [11] = {"FeedPetRequest", "FeedPetReply"},
        [12] = {"ClearFeedPetCdTimeRequest", "ClearFeedPetCdTimeReply"},
        [13] = {"TakePetHeartRequest", "TakePetHeartReply"},
        [14] = {"GetNonCatchPetRequest", "GetNonCatchPetReply"},
        [15] = {"GetPetExpeditionSceneInfoRequest", "GetPetExpeditionSceneInfoReply"},
        [16] = {"StartPetExpeditionRequest", "StartPetExpeditionReply"},
        [17] = {"ReceiveExpeditionAwardsRequest", "ReceiveExpeditionAwardsReply"},
        [18] = {"RecallPetExpeditionTeamRequest", "RecallPetExpeditionTeamReply"},
        [19] = {"ChangePetSkinRequest", "ChangePetSkinReply"},
        [20] = {"RefreshPetExpeditionSceneRequest", "RefreshPetExpeditionSceneReply"},
        [21] = {"SwitchFollowShowRequest", "SwitchFollowShowReply"},
        [22] = {"ChangePetSportMedalRequest", "ChangePetSportMedalReply"},
        [23] = {"GetPetPlayGameActIdsRequest", "GetPetPlayGameActIdsReply"},
        [24] = {"PutPetInSceneRequest", "PutPetInSceneReply"},
        [25] = {"TakeBackAllScenePetRequest", "TakeBackAllScenePetReply"},
        [31] = {"PetInfoPush"}
    },
    [31] = {
        [255] = "RecycleExtension",
        [1] = {"RecycleItemRequest", "RecycleItemReply"},
        [2] = {"DecomposeItemRequest", "DecomposeItemReply"}
    },
    [32] = {
        [255] = "FuncUnlockExtension",
        [1] = {"SelectLockFuncRequest", "SelectLockFuncReply"},
        [2] = {"ClientPlotReportRequest", "ClientPlotReportReply"},
        [11] = {"FuncUnlockPush"}
    },
    [33] = {
        [255] = "RechargeExtension",
        [1] = {"GetPayGoodsInfoRequest", "GetPayGoodsInfoReply"},
        [2] = {"GetPayOrderRequest", "GetPayOrderReply"},
        [3] = {"GetPayGiftRequest", "GetPayGiftReply"},
        [4] = {"ExchangeCurrencyRequest", "ExchangeCurrencyReply"},
        [5] = {"GetThunderPayOrderRequest", "GetThunderPayOrderReply"},
        [6] = {"GetRechargeRewardInfoRequest", "GetRechargeRewardInfoReply"},
        [7] = {"ReceiveRechargeRewardRequest", "ReceiveRechargeRewardReply"},
        [8] = {"VoucherByRMBGiftRequest", "VoucherByRMBGiftReply"},
        [9] = {"GainVipWeeklyRewardRequest", "GainVipWeeklyRewardReply"},
        [11] = {"RechargePush"},
        [12] = {"PopGiftPush"},
        [13] = {"RechargeLiveStreamGiftPush"}
    },
    [34] = {
        [255] = "DecorationMatchExtension",
        [1] = {"GetDecorationInfoRequest", "GetDecorationInfoReply"},
        [2] = {"JoinMatchRequest", "JoinMatchReply"},
        [3] = {"GetPKDecorationInfoRequest", "GetPKDecorationInfoReply"},
        [4] = {"VoteRequest", "VoteReply"},
        [5] = {"GetRankListRequest", "GetRankListReply"},
        [6] = {"GetVoteRewardRequest", "GetVoteRewardReply"},
        [7] = {"ExchangeRewardRequest", "ExchangeRewardReply"},
        [8] = {"GetDecorationMatchShopLimitRequest", "GetDecorationMatchShopLimitReply"}
    },
    [35] = {
        [255] = "ElfExtension",
        [1] = {"GetElfInfoRequest", "GetElfInfoReply"},
        [2] = {"SelectElfEggRequest", "SelectElfEggReply"},
        [3] = {"ElfEggHatchRequest", "ElfEggHatchReply"},
        [4] = {"ElfInteractRequest", "ElfInteractReply"},
        [5] = {"DrawOthersRewardRequest", "DrawOthersRewardReply"},
        [6] = {"ElfChangeNameRequest", "ElfChangeNameReply"},
        [7] = {"ElfEditWelcomeNoticeRequest", "ElfEditWelcomeNoticeReply"},
        [8] = {"ElfChangeStateRequest", "ElfChangeStateReply"},
        [9] = {"DrawVisitElfHouseRewardsRequest", "DrawVisitElfHouseRewardsReply"},
        [11] = {"ElfInfoPush"},
        [21] = {"ElfChangeClothesRequest", "ElfChangeClothesReply"},
        [22] = {"DrawElfFullJoyRewardsRequest", "DrawElfFullJoyRewardsReply"},
        [23] = {"ElfChangeIdColorRequest", "ElfChangeIdColorReply"}
    },
    [36] = {
        [255] = "DragonChildExtension",
        [1] = {"InvestItemRequest", "InvestItemReply"},
        [2] = {"GetInvestRewardRequest", "GetInvestRewardReply"},
        [3] = {"GetInvestInfoRequest", "GetInvestInfoReply"}
    },
    [37] = {
        [255] = "WishCloverExtension",
        [1] = {"GetWishCloverInfoRequest", "GetWishCloverInfoReply"},
        [2] = {"LeavingWishCloverRequest", "LeavingWishCloverReply"},
        [3] = {"DeleteWishCloverRequest", "DeleteWishCloverReply"},
        [4] = {"LikeWishCloverRequest", "LikeWishCloverReply"},
        [5] = {"OwnWishCloverRequest", "OwnWishCloverReply"},
        [6] = {"CancelOwnWishCloverRequest", "CancelOwnWishCloverReply"},
        [11] = {"WishCloverPush"}
    },
    [38] = {
        [255] = "SceneInteractiveExtension",
        [1] = {"TakePurikuraRequest", "TakePurikuraReply"},
        [2] = {"SceneInteractiveInfoRequest", "SceneInteractiveInfoReply"},
        [3] = {"SceneInteractivePlayRequest", "SceneInteractivePlayReply"},
        [4] = {"CottonMakeRequest", "CottonMakeReply"},
        [5] = {"GetPurikuraImageInfosRequest", "GetPurikuraImageInfosReply"},
        [6] = {"GetPhotoAlbumInfoRequest", "GetPhotoAlbumInfoReply"},
        [7] = {"DeletePhotoRequest", "DeletePhotoReply"},
        [8] = {"GetWeatherRequest", "GetWeatherReply"},
        [9] = {"SceneUsePlacingItemRequest", "SceneUsePlacingItemReply"},
        [11] = {"SceneBindTroublePush"},
        [12] = {"SceneTroubleRewardPush"},
        [13] = {"PhotoAlbumChangePush"}
    },
    [39] = {
        [255] = "TravellerExtension",
        [1] = {"GetTravellerInfoRequest", "GetTravellerInfoReply"},
        [2] = {"GetLeaveMessageInfoRequest", "GetLeaveMessageInfoReply"},
        [3] = {"UnlockTravellerRequest", "UnlockTravellerReply"},
        [4] = {"SignOrCancelStarRequest", "SignOrCancelStarReply"},
        [5] = {"UpgradeTravellerTalentRequest", "UpgradeTravellerTalentReply"},
        [6] = {"LeaveMessageTravellerRequest", "LeaveMessageTravellerReply"},
        [7] = {"ChangeTravellerLeaveMessageStateRequest", "ChangeTravellerLeaveMessageStateReply"},
        [8] = {"ChangeSkinRequest", "ChangeSkinReply"},
        [9] = {"ChangeSceneRequest", "ChangeSceneReply"},
        [10] = {"ChangeTreasureRequest", "ChangeTreasureReply"},
        [11] = {"LeaveMessagePush"},
        [12] = {"TravellerInfoPush"},
        [13] = {"TravellerScorePush"},
        [21] = {"DrawUpgradeTalentRewardRequest", "DrawUpgradeTalentRewardReply"},
        [22] = {"ExchangeFragmentRequest", "ExchangeFragmentReply"},
        [23] = {"TravellerLeisureRequest", "TravellerLeisureReply"},
        [24] = {"TravellerFragmentRecycleRequest", "TravellerFragmentRecycleReply"},
        [25] = {"GetTravellerTeamInfoRequest", "GetTravellerTeamInfoReply"},
        [26] = {"EditTravellerTeamRequest", "EditTravellerTeamReply"},
        [27] = {"InviteTravellerTeamOnIslandRequest", "InviteTravellerTeamOnIslandReply"},
        [28] = {"DeleteTravellerTeamRequest", "DeleteTravellerTeamReply"}
    },
    [40] = {
        [255] = "TravellerTreasureExtension",
        [1] = {"GetTravellerTreasureInfoRequest", "GetTravellerTreasureInfoReply"},
        [2] = {"TravellerTreasureUpgradeLevelRequest", "TravellerTreasureUpgradeLevelReply"},
        [3] = {"TravellerTreasureUpgradeStarRequest", "TravellerTreasureUpgradeStarReply"},
        [4] = {"TravellerTreasureChangeStateRequest", "TravellerTreasureChangeStateReply"},
        [11] = {"TravellerTreasureInfoPush"}
    },
    [41] = {
        [255] = "FlowerGardenExtension",
        [1] = {"SendFlowerRequest", "SendFlowerReply"},
        [2] = {"GetAllFlowerMsgRequest", "GetAllFlowerMsgReply"},
        [3] = {"GetFlowerRankingRequest", "GetFlowerRankingReply"},
        [4] = {"MakeFlowerRequest", "MakeFlowerReply"},
        [6] = {"GetHistoryRankingTimeRequest", "GetHistoryRankingTimeReply"},
        [7] = {"GetHistoryRankingDetailInfoRequest", "GetHistoryRankingDetailInfoReply"},
        [8] = {"GetReceiveFlowerLimitRequest", "GetReceiveFlowerLimitReply"},
        [9] = {"DeleteFlowerMsgRequest", "DeleteFlowerMsgReply"},
        [11] = {"ReceiveFlowerPush"}
    },
    [42] = {
        [255] = "FriendTaskExtension",
        [1] = {"GetFriendTaskInvitationInfoRequest", "GetFriendTaskInvitationInfoReply"},
        [2] = {"FriendTaskInvitationInviteRequest", "FriendTaskInvitationInviteReply"},
        [3] = {"FriendTaskInvitationReceiveRequest", "FriendTaskInvitationReceiveReply"},
        [4] = {"FriendTaskInvitationCancelRequest", "FriendTaskInvitationCancelReply"},
        [5] = {"FriendTaskInvitationIgnoreRequest", "FriendTaskInvitationIgnoreReply"},
        [11] = {"FriendTaskInvitationInfoPush"},
        [21] = {"GetFriendTaskInfoRequest", "GetFriendTaskInfoReply"},
        [22] = {"FriendTaskIncrTargetRequest", "FriendTaskIncrTargetReply"},
        [23] = {"FriendTaskFinishStepRequest", "FriendTaskFinishStepReply"},
        [24] = {"FriendTaskSubmitItemRequest", "FriendTaskSubmitItemReply"},
        [25] = {"FriendTaskReceiveAwardRequest", "FriendTaskReceiveAwardReply"},
        [26] = {"FriendTaskQuickCompletionTargetRequest", "FriendTaskQuickCompletionTargetReply"},
        [31] = {"UpdateFriendTaskInfoPush"}
    },
    [43] = {
        [255] = "DreamPalaceExtension",
        [1] = {"GetDreamPalaceInfoRequest", "GetDreamPalaceInfoReply"},
        [2] = {"BuyEnterTicketsRequest", "BuyEnterTicketsReply"},
        [3] = {"GetPalaceCommonInfoRequest", "GetPalaceCommonInfoReply"},
        [4] = {"CostDiamondsRequest", "CostDiamondsReply"}
    },
    [44] = {
        [255] = "FirstPalaceExtension",
        [1] = {"EnterFirstPalaceInfoRequest", "EnterFirstPalaceInfoReply"},
        [2] = {"EnterPalaceLevelRequest", "EnterPalaceLevelReply"},
        [3] = {"SkipMapCellRequest", "SkipMapCellReply"},
        [4] = {"FinishCellEventRequest", "FinishCellEventReply"},
        [5] = {"ExitFirstPalaceLevelRequest", "ExitFirstPalaceLevelReply"},
        [6] = {"BuyPowerRequest", "BuyPowerReply"},
        [7] = {"SwitchNpcRequest", "SwitchNpcReply"},
        [8] = {"CostGreenDiamondsRequest", "CostGreenDiamondsReply"}
    },
    [45] = {
        [255] = "CouncilExtension",
        [1] = {"GetCouncilInfoRequest", "GetCouncilInfoReply"},
        [2] = {"GetRecommendInfoListRequest", "GetRecommendInfoListReply"},
        [3] = {"SearchCouncilInfoRequest", "SearchCouncilInfoReply"},
        [4] = {"GetCouncilMemberInfoListRequest", "GetCouncilMemeberInfoListReply"},
        [5] = {"CreateCouncilRequest", "CreateCouncilReply"},
        [6] = {"ApplyCouncilRequest", "ApplyCouncilReply"},
        [7] = {"ManageCouncilRequest", "ManageCouncilReply"},
        [8] = {"GetCouncilLogRequest", "GetCouncilLogReply"},
        [9] = {"GetCouncilApplicationRequest", "GetCouncilApplicationReply"},
        [10] = {"GetCouncilBackpackInfoRequest", "GetCouncilBackpackInfoReply"},
        [11] = {"CouncilChangePush"},
        [12] = {"CouncilItemChangePush"},
        [13] = {"CouncilDonateRewardsChangePush"},
        [14] = {"CouncilDonateShareRewardsItemChangePush"},
        [21] = {"SubmitCouncilOrderRequest", "SubmitCouncilOrderReply"},
        [22] = {"BuyMemberShopGoodsRequest", "BuyMemberShopGoodsReply"},
        [23] = {"SupplyMemberShopGoodsRequest", "SupplyMemberShopGoodsReply"},
        [24] = {"GetActivityAndRewardStatusRequest", "GetActivityAndRewardStatusReply"},
        [25] = {"GetActivityRewardsRequest", "GetActivityRewardsReply"},
        [26] = {"GetCouncilDonateInfoRequest", "GetCouncilDonateInfoReply"},
        [27] = {"DonateRequest", "DonateReply"},
        [28] = {"GetSingleRewardsRequest", "GetSingleRewardsReply"},
        [31] = {"GetUserCouncilInfoRequest", "GetUserCouncilInfoReply"},
        [32] = {"GetCouncilAreaInfoRequest", "GetCouncilAreaInfoReply"},
        [33] = {"UnlockCouncilAreaRequest", "UnlockCouncilAreaReply"},
        [34] = {"DecorateCouncilAreaRequest", "DecorateCouncilAreaReply"},
        [35] = {"GetCouncilAreaDecorationsRequest", "GetCouncilAreaDecorationsReply"},
        [36] = {"UnlockCouncilAreaDecorationRequest", "UnlockCouncilAreaDecorationReply"},
        [37] = {"GetCouncilSpaRewardsRequest", "GetCouncilSpaRewardsReply"},
        [38] = {"GetLastExitCouncilTimeRequest", "GetLastExitCouncilTimeReply"},
        [39] = {"GetAllCouncilMemberInfoListRequest", "GetAllCouncilMemberInfoListReply"}
    },
    [46] = {
        [255] = "ATMExtension",
        [1] = {"GetATMAccountInfoRequest", "GetATMAccountInfoReply"},
        [2] = {"CreatATMAccountRequest", "CreatATMAccountReply"},
        [3] = {"BuyFundRequest", "BuyFundReply"},
        [4] = {"RedeemFundRequest", "RedeemFundReply"},
        [5] = {"GetFundInfoRequest", "GetFundInfoReply"}
    },
    [47] = {
        [255] = "FriendshipCheckExtension",
        [1] = {"JoinFriendshipCheckRequest", "JoinFriendshipCheckReply"},
        [2] = {"StartFriendshipCheckRequest", "StartFriendshipCheckReply"},
        [3] = {"ExitFriendshipCheckTeamRequest", "ExitFriendshipCheckTeamReply"},
        [4] = {"CompleteFriendshipCheckRequest", "CompleteFriendshipCheckReply"},
        [5] = {"ChooseRoleSkinRequest", "ChooseRoleSkinReply"},
        [6] = {"FriendshipGameStateSyncRequest", "FriendshipGameStateSyncReply"},
        [7] = {"FriendshipCheckParamsSyncRequest", "FriendshipCheckParamsSyncReply"},
        [11] = {"FriendshipCheckTeamInfoPush"},
        [12] = {"FriendshipCheckStartPush"},
        [13] = {"FriendshipGameStateSyncPush"},
        [14] = {"FriendshipCheckParamsSyncPush"},
        [21] = {"FriendshipCheckNpcSyncRequest", "FriendshipCheckNpcSyncReply"},
        [32] = {"FriendshipCheckNpcSyncPush"},
        [41] = {"FriendshipCheckMoveRequest", "FriendshipCheckMoveReply"},
        [42] = {"FriendCheckAddScoreRequest", "FriendCheckAddScoreReply"},
        [43] = {"FriendCheckChatRequest", "FriendCheckChatReply"},
        [44] = {"FriendCheckAddRoundRequest", "FriendCheckAddRoundReply"},
        [51] = {"FriendshipCheckMoveInfoPush"},
        [52] = {"FriendshipCheckAddScorePush"},
        [53] = {"FriendCheckChatPush"}
    },
    [48] = {
        [255] = "VipExtension",
        [1] = {"GetVipCardInfoRequest", "GetVipCardInfoReply"},
        [2] = {"DrawVipDailyRewardRequest", "DrawVipDailyRewardReply"},
        [3] = {"GetVipCardConsumeInfoRequest", "GetVipCardConsumeInfoReply"},
        [11] = {"VipInfoChangePush"}
    },
    [49] = {
        [255] = "ParttimeExtension",
        [1] = {"GetParttimeInfoRequest", "GetParttimeInfoReply"},
        [2] = {"CompleteParttimeRequest", "CompleteParttimeReply"}
    },
    [50] = {
        [255] = "ElfMagicExtension",
        [1] = {"GetElfMagicLevelInfoRequest", "GetElfMagicLevelInfoReply"},
        [2] = {"StudyElfMagicRequest", "StudyElfMagicReply"},
        [3] = {"JoinElfMagicFightMatchRequest", "JoinElfMagicFightMatchReply"},
        [4] = {"ExitElfMagicFightMatchRequest", "ExitElfMagicFightMatchReply"},
        [5] = {"GetElfFightDailyPrizeCountRequest", "GetElfFightDailyPrizeCountReply"},
        [6] = {"GetElfFightRankingRequest", "GetElfFightRankingReply"},
        [7] = {"GetElfStudyMagicCountRequest", "GetElfStudyMagicCountReply"},
        [11] = {"ElfMagicFightInfoPush"}
    },
    [51] = {
        [255] = "TalentExtension",
        [1] = {"GetTalentInfoRequest", "GetTalentInfoReply"},
        [2] = {"GetQuestionRequest", "GetQuestionReply"},
        [3] = {"AnswerRequest", "AnswerReply"},
        [4] = {"ClientFinishTalentTargetRequest", "ClientFinishTalentTargetReply"},
        [5] = {"GetTalentStarRewardRequest", "GetTalentStarRewardReply"},
        [6] = {"ClearTalentRedPointParamsRequest", "ClearTalentRedPointParamsReply"},
        [7] = {"GetCanThumbsUpRequest", "GetCanThumbsUpReply"},
        [8] = {"ThumbsUpTalentGuiderRequest", "ThumbsUpTalentGuiderReply"},
        [9] = {"GetBeThumbsUpInfoRequest", "GetBeThumbsUpInfoReply"},
        [11] = {"TalentInfoPush"},
        [12] = {"TalentGuideIdentityPush"},
        [21] = {"GetTalentGuideQuestionInfoRequest", "GetTalentGuideQuestionInfoReply"},
        [22] = {"GetTalentGuideAnswerRequest", "GetTalentGuideAnswerReply"},
        [23] = {"SendTalentGuideQuestionRequest", "SendTalentGuideQuestionReply"},
        [24] = {"AnswerTalentGuideQuestionRequest", "AnswerTalentGuideQuestionReply"},
        [25] = {"GetTalentGuideAnswerHistoryRequest", "GetTalentGuideAnswerHistoryReply"},
        [26] = {"ChangeTalentQuestionStateRequest", "ChangeTalentQuestionStateReply"}
    },
    [52] = {
        [255] = "IslandBuildExtension",
        [1] = {"GetIslandRewardsRequest", "GetIslandRewardsReply"},
        [2] = {"UpgradeQualificationRequest", "UpgradeQualificationReply"},
        [3] = {"UpgradeLicenseRequest", "UpgradeLicenseReply"},
        [4] = {"GetIslandLevelRewardsRecordRequest", "GetIslandLevelRewardsRecordReply"},
        [5] = {"GetBadgeInfoRequest", "GetBadgeInfoReply"},
        [6] = {"GetMemoryRequest", "GetMemoryReply"}
    },
    [53] = {
        [255] = "RankExtension",
        [1] = {"GetRankInfoRequest", "GetRankInfoReply"},
        [2] = {"GetFriendRankRequest", "GetFriendRankReply"},
        [3] = {"GetIntimacyRankRequest", "GetIntimacyRankReply"},
        [4] = {"GetFriendIntimacyRankRequest", "GetFriendIntimacyRankReply"},
        [5] = {"GetUserClothesInfoRequest", "GetUserClothesInfoReply"},
        [6] = {"GetFamilyRankInfoRequest", "GetFamilyRankInfoReply"},
        [7] = {"GetUserHeaderInfoRequest", "GetUserHeaderInfoReply"},
        [8] = {"GetUserFriendshipRequest", "GetUserFriendshipReply"},
        [9] = {"GetActRankInfoRequest", "GetActRankInfoReply"},
        [10] = {"GetActFriendRankInfoRequest", "GetActFriendRankInfoReply"},
        [11] = {"GetHistoryRankTimeRequest", "GetHistoryRankTimeReply"},
        [12] = {"GetHistoryRankDetailInfoRequest", "GetHistoryRankDetailInfoReply"},
        [13] = {"GetOwnerRankInfosRequest", "GetOwnerRankInfosReply"},
        [14] = {"GetDoublePersonRankInfoRequest", "GetDoublePersonRankInfoReply"},
        [15] = {"GetAllRankFirstRequest", "GetAllRankFirstReply"}
    },
    [54] = {
        [255] = "FoggyForestExtension",
        [1] = {"FoggyForestTrickItemRequest", "FoggyForestTrickItemReply"},
        [2] = {"CancelFoggyForestStateRequest", "CancelFoggyForestStateReply"},
        [3] = {"GetFoggyForestTeamIdRequest", "GetFoggyForestTeamIdReply"},
        [4] = {"GetFoggyForestWeekGainRequest", "GetFoggyForestWeekGainReply"},
        [5] = {"FoggyForestAllowOtherJoinRequest", "FoggyForestAllowOtherJoinReply"}
    },
    [55] = {
        [255] = "WerewolfExtension",
        [1] = {"JoinWerewolfMatchRequest", "JoinWerewolfMatchReply"},
        [2] = {"ExitWerewolfMatchRequest", "ExitWerewolfMatchReply"},
        [3] = {"ConfirmJoinRequest", "ConfirmJoinReply"},
        [21] = {"GetWerewolfRoomIdRequest", "GetWerewolfRoomIdReply"},
        [22] = {"JoinWerewolfRoomRequest", "JoinWerewolfRoomReply"},
        [23] = {"PrepareWerewolfGameRequest", "PrepareWerewolfGameReply"},
        [26] = {"ExitWerewolfRoomRequest", "ExitWerewolfRoomReply"},
        [27] = {"ChooseWerewolfIdentityRequest", "ChooseWerewolfIdentityReply"},
        [28] = {"GetWerewolfRoomTimeOffsetRequest", "GetWerewolfRoomTimeOffsetReply"},
        [29] = {"GetWerewolfRoomSimpleInfoRequest", "GetWerewolfRoomSimpleInfoReply"},
        [34] = {"GetWerewolfMoveInfoRequest", "GetWerewolfMoveInfoReply"},
        [35] = {"WerewolfPunishUserRequest", "WerewolfPunishUserReply"},
        [41] = {"WerewolfMoveRequest", "WerewolfMoveReply"},
        [42] = {"WerewolfAttackRequest", "WerewolfAttackReply"},
        [43] = {"WerewolfVoteRequest", "WerewolfVoteReply"},
        [44] = {"ReportDeadBodyRequest", "ReportDeadBodyReply"},
        [45] = {"StartOperateBuildingRequest", "StartOperateBuildingReply"},
        [46] = {"CompleteOperateBuildingRequest", "CompleteOperateBuildingReply"},
        [47] = {"GetWerewolfRoomMessageRequest", "GetWerewolfRoomMessageReply"},
        [48] = {"SendWerewolfRoomMessageRequest", "SendWerewolfRoomMessageReply"},
        [49] = {"WerewolfClientShareRequest", "WerewolfClientShareReply"},
        [50] = {"WerewolfBoatRequest", "WerewolfBoatReply"},
        [51] = {"WerewolfSpeechEndRequest", "WerewolfSpeechEndReply"},
        [52] = {"BreakingBuildingRequest", "BreakingBuildingReply"},
        [61] = {"WerewolfInfoPush"},
        [62] = {"WerewolfMatchingStatePush"},
        [64] = {"WerewolfTaskInfoPush"},
        [65] = {"WerewolfMessagePush"},
        [66] = {"WerewolfCalculatePush"},
        [67] = {"WerewolfIdentityPush"},
        [68] = {"WerewolfAttackPush"},
        [71] = {"WerewolfMovePush"}
    },
    [56] = {
        [255] = "ExternalShareExtension",
        [1] = {"GetExternalShareInfoRequest", "GetExternalShareInfoReply"},
        [2] = {"ExternalShareRequest", "ExternalShareReply"},
        [3] = {"BeforeOpenServiceShareRequest", "BeforeOpenServiceShareReply"}
    },
    [57] = {
        [255] = "FreeRiceExtension",
        [1] = {"HavingDinnerRequest", "HavingDinnerReply"},
        [2] = {"GetStatusRequest", "GetStatusReply"}
    },
    [58] = {
        [255] = "GameRoomExtension",
        [1] = {"GameRoomCreateRequest", "GameRoomCreateReply"},
        [2] = {"GameRoomModifyInfoRequest", "GameRoomModifyInfoReply"},
        [3] = {"GameRoomKickOutUserRequest", "GameRoomKickOutUserReply"},
        [4] = {"GameRoomInviteUserRequest", "GameRoomInviteUserReply"},
        [5] = {"GameRoomPrepareRequest", "GameRoomPrepareReply"},
        [6] = {"GameRoomCancelPrepareRequest", "GameRoomCancelPrepareReply"},
        [7] = {"GameRoomStartRequest", "GameRoomStartReply"},
        [8] = {"GameRoomQuickJoinRequest", "GameRoomQuickJoinReply"},
        [9] = {"GameRoomGetRoomListRequest", "GameRoomGetRoomListReply"},
        [10] = {"GameRoomExitQuickJoinRequest", "GameRoomExitQuickJoinReply"},
        [11] = {"GameRoomQuickJoinPush"},
        [12] = {"GameRoomInvitePush"},
        [13] = {"GameRoomStartPush"},
        [14] = {"GameRoomPersonMatchPush"},
        [15] = {"GameRoomMovePush"},
        [16] = {"GameRoomInfoPush"},
        [17] = {"GameRoomMessagePush"},
        [18] = {"GameRoomRewardPush"},
        [19] = {"GameRoomBroadcastStateInfoPush"},
        [20] = {"GameRoomChangeIndexApplyPush"},
        [21] = {"GameRoomChangeIndexRequest", "GameRoomChangeIndexReply"},
        [22] = {"GameRoomGetWaitRoomInfoRequest", "GameRoomGetWaitRoomInfoReply"},
        [23] = {"GameRoomGetGameInfoRequest", "GameRoomGetGameInfoReply"},
        [24] = {"GameRoomSearchRoomRequest", "GameRoomSearchRoomReply"},
        [25] = {"GameRoomChangeRoomOwnerRequest", "GameRoomChangeRoomOwnerReply"},
        [26] = {"JoinGameRoomMatchRequest", "JoinGameRoomMatchReply"},
        [27] = {"ExitGameRoomMatchRequest", "ExitGameRoomMatchReply"},
        [28] = {"GameRoomConfirmJoinRequest", "GameRoomConfirmJoinReply"},
        [31] = {"GameRoomCreditChangePush"},
        [32] = {"GameRoomCommonSyncPush"},
        [41] = {"GameRoomJoinRoomSceneRequest", "GameRoomJoinRoomSceneReply"},
        [42] = {"ExitGameRoomSceneRequest", "ExitGameRoomSceneReply"},
        [43] = {"GetGameRoomJoinInfoRequest", "GetGameRoomJoinInfoReply"},
        [44] = {"GetGameRoomTimeOffsetRequest", "GetGameRoomTimeOffsetReply"},
        [45] = {"GameRoomMoveRequest", "GameRoomMoveReply"},
        [46] = {"GameRoomSendMessageRequest", "GameRoomSendMessageReply"},
        [47] = {"GameRoomGetDisconnectRoomInfoRequest", "GameRoomGetDisconnectRoomInfoReply"},
        [48] = {"GetGameCreditInfoRequest", "GetGameCreditInfoReply"},
        [49] = {"GameRoomBroadcastStateRequest", "GameRoomBroadcastStateReply"},
        [50] = {"GameRoomSetClientCacheMsgRequest", "GameRoomSetClientCacheMsgReply"},
        [51] = {"GameRoomGetClientCacheMsgRequest", "GameRoomGetClientCacheMsgReply"},
        [52] = {"GameRoomGetVersionRequest", "GameRoomGetVersionReply"},
        [53] = {"GameRoomChangeExtParamRequest", "GameRoomChangeExtParamReply"},
        [54] = {"GameRoomHandleChangeIndexApplyRequest", "GameRoomHandleChangeIndexApplyReply"},
        [55] = {"GameRoomSingleStartRequest", "GameRoomSingleStartReply"},
        [56] = {"GetGameRoomReportRequest", "GetGameRoomReportReply"},
        [57] = {"GameRoomCommonSyncRequest", "GameRoomCommonSyncReply"}
    },
    [59] = {
        [255] = "CouncilDefendExtension",
        [1] = {"GetCouncilDefendPanelInfoRequest", "GetCouncilDefendPanelInfoReply"},
        [2] = {"CouncilDefendAttackBossRequest", "CouncilDefendAttackBossReply"},
        [3] = {"CouncilDefendPickUpItemRequest", "CouncilDefendPickUpItemReply"},
        [4] = {"CouncilDefendStopTransportItemRequest", "CouncilDefendStopTransportItemReply"},
        [5] = {"CouncilDefendOccupyWeaponRequest", "CouncilDefendOccupyWeaponReply"},
        [6] = {"CouncilDefendFixWeaponRequest", "CouncilDefendFixWeaponReply"},
        [7] = {"CouncilDefendUseAidItemRequest", "CouncilDefendUseAidItemReply"},
        [8] = {"CouncilDefendMemberRebornRequest", "CouncilDefendMemberRebornReply"},
        [9] = {"CouncilDefendAllowOtherJoinRequest", "CouncilDefendAllowOtherJoinReply"},
        [10] = {"CouncilDefendDrawAssistExtraRewardsRequest", "CouncilDefendDrawAssistExtraRewardsReply"},
        [11] = {"CouncilDefendEndPush"}
    },
    [60] = {
        [255] = "GobangExtension",
        [1] = {"GobangAddRoomRequest", "GobangAddRoomReply"},
        [2] = {"GobangLeaveRoomRequest", "GobangLeaveRoomReply"},
        [3] = {"GobangHeartbeatRequest", "GobangHeartbeatReply"},
        [4] = {"GobangBeginRequest", "GobangBeginReply"},
        [5] = {"GobangChessRequest", "GobangChessReply"},
        [6] = {"GobangRequestDrawRequest", "GobangRequestDrawReply"},
        [7] = {"GobangAnswerDrawRequest", "GobangAnswerDrawReply"},
        [8] = {"GobangTakeBackRequest", "GobangTakeBackReply"},
        [9] = {"GobangAnswerTakeBackRequest", "GobangAnswerTakeBackReply"},
        [10] = {"GobangChatRequest", "GobangChatReply"},
        [11] = {"GobangPersonMatchPush"},
        [21] = {"JoinGobangMatchRequest", "JoinGobangMatchReply"},
        [22] = {"ExitGobangMatchRequest", "ExitGobangMatchReply"},
        [23] = {"GobangConfirmJoinRequest", "GobangConfirmJoinReply"},
        [24] = {"GobangFurnitureReadyRequest", "GobangFurnitureReadyReply"},
        [25] = {"GobangFurnitureReadyCancelRequest", "GobangFurnitureReadyCancelReply"},
        [26] = {"GetGobangFurnitureStateRequest", "GetGobangFurnitureStateReply"},
        [27] = {"GobangFurnitureRuleChangeRequest", "GobangFurnitureRuleChangeReply"},
        [28] = {"GobangCheckBanRequest", "GobangCheckBanReply"},
        [31] = {"PushGobangWatchNumReply"},
        [32] = {"PushGobangBeginReply"},
        [33] = {"PushGobangChessReply"},
        [34] = {"PushGobangRequestDrawReply"},
        [35] = {"PushGobangAnswerDrawReply"},
        [36] = {"PushGobangTakeBackReply"},
        [37] = {"PushGobangAnswerTakeBackReply"},
        [38] = {"PushGobangChatReply"},
        [39] = {"PushGobangResultReply"},
        [40] = {"PushGobangInitReply"}
    },
    [62] = {
        [255] = "SecondPalaceExtension",
        [1] = {"GetSecondPalaceInfoRequest", "GetSecondPalaceInfoReply"},
        [2] = {"GetSecondPalaceGameInfoRequest", "GetSecondPalaceGameInfoReply"},
        [3] = {"ChooseOrderRequest", "ChooseOrderReply"},
        [4] = {"BuyEnergyRequest", "BuyEnergyReply"},
        [5] = {"SavePointRequest", "SavePointReply"},
        [6] = {"SecondPalaceGameFinishRequest", "SecondPalaceGameFinishReply"},
        [7] = {"SecondPalaceExitHalfWayRequest", "SecondPalaceExitHalfWayReply"}
    },
    [63] = {
        [255] = "CatchStarExtension",
        [1] = {"GetCatchStarInfoRequest", "GetCatchStarInfoReply"},
        [2] = {"StartCatchStarRequest", "StartCatchStarReply"},
        [3] = {"EndCatchStarRequest", "EndCatchStarReply"}
    },
    [64] = {
        [255] = "MoveHouseExtension",
        [1] = {"OperationFurnitureRequest", "OperationFurnitureReply"},
        [2] = {"SelectFurnitureDirectionRequest", "SelectFurnitureDirectionReply"},
        [3] = {"MoveFurnitureRequest", "MoveFurnitureReply"},
        [11] = {"MoveHouseGameCalculatePush"},
        [21] = {"MoveHouseGetActivityInfoRequest", "MoveHouseGetActivityInfoReply"},
        [22] = {"MoveHouseGetDailyRewardRequest", "MoveHouseGetDailyRewardReply"},
        [23] = {"MoveHouseGetScoreRewardRequest", "MoveHouseGetScoreRewardReply"}
    },
    [65] = {
        [255] = "MiaoHouseExtension",
        [1] = {"MHPickUpGridFromGroundRequest", "MHPickUpGridFromGroundReply"},
        [2] = {"MHPickUpGridFromPoolRequest", "MHPickUpGridFromPoolReply"},
        [3] = {"MHPutDownGridRequest", "MHPutDownGridReply"},
        [4] = {"MHSubmitGridToPoolRequest", "MHSubmitGridToPoolReply"},
        [5] = {"MHSubmitGridToOrderRequest", "MHSubmitGridToOrderReply"},
        [7] = {"MHPickUpItemRequest", "MHPickUpItemReply"},
        [8] = {"MHPickUpItemFromBoxRequest", "MHPickUpItemFromBoxReply"},
        [9] = {"MHPutDownItemRequest", "MHPutDownItemReply"},
        [10] = {"MHOpenMagicDoorRequest", "MHOpenMagicDoorReply"},
        [11] = {"MHUseBoomRequest", "MHUseBoomReply"},
        [12] = {"MHShiftingRequest", "MHShiftingReply"},
        [13] = {"MHUseBuffItemRequest", "MHUseBuffItemReply"},
        [14] = {"MHRobMouseRequest", "MHRobMouseReply"},
        [15] = {"MHDeathRequest", "MHDeathReply"},
        [16] = {"MHSetPlayerEagleRequest", "MHSetPlayerEagleReply"},
        [21] = {"MHRoomChangeInfoPush"},
        [22] = {"MHSettlementPVEPush"},
        [23] = {"MHSettlementPVPPush"}
    },
    [66] = {
        [255] = "RedEnvelopeExtension",
        [1] = {"GetMySendRedEnvelopeInfoRequest", "GetMySendRedEnvelopeInfoReply"},
        [2] = {"GetMyReceiveRedEnvelopeInfoRequest", "GetMyReceiveRedEnvelopeInfoReply"},
        [3] = {"GetLeakRedEnvelopeInfoRequest", "GetLeakRedEnvelopeInfoReply"},
        [4] = {"SendRedEnvelopeRequest", "SendRedEnvelopeReply"},
        [5] = {"ReceiveRedEnvelopeRequest", "ReceiveRedEnvelopeReply"},
        [6] = {"GetRedEnvelopeDetailRequest", "GetRedEnvelopeDetailReply"}
    },
    [67] = {
        [255] = "MusicGameExtension",
        [1] = {"GetMusicGameActionInfoRequest", "GetMusicGameActionInfoReply"},
        [2] = {"ChangeMusicGameActionRequest", "ChangeMusicGameActionReply"},
        [3] = {"GetMusicGameRecordRequest", "GetMusicGameRecordReply"},
        [4] = {"MusicGameStateUpdateRequest", "MusicGameStateUpdateReply"},
        [5] = {"MusicGameFailStateChangeRequest", "MusicGameFailStateChangeReply"},
        [6] = {"MusicGamePunishRequest", "MusicGamePunishReply"},
        [11] = {"MusicGameEndDataRoomPush"}
    },
    [68] = {
        [255] = "Backflow2Extension",
        [2] = {"StartBackflowPartyRequest", "StartBackflowPartyReply"},
        [3] = {"GetBackflowPartyInviteListRequest", "GetBackflowPartyInviteListReply"},
        [4] = {"GetBackflowPartyFoodAwardRequest", "GetBackflowPartyFoodAwardReply"},
        [5] = {"GetBackflowPartyTimeAwardRequest", "GetBackflowPartyTimeAwardReply"},
        [6] = {"BackflowPartyInviteNpcRequest", "BackflowPartyInviteNpcReply"},
        [7] = {"BackflowPartyLeaveNpcRequest", "BackflowPartyLeaveNpcReply"},
        [8] = {"GetBackflowPartyProgressRequest", "GetBackflowPartyProgressReply"},
        [9] = {"BackflowPartySubmitTaskRequest", "BackflowPartySubmitTaskReply"},
        [10] = {"BackflowPartyGetNpcChatAwardRequest", "BackflowPartyGetNpcChatAwardReply"},
        [21] = {"BackflowPartyInvitePlayerRequest", "BackflowPartyInvitePlayerReply"},
        [22] = {"BackflowPartyGetPartyRewardRequest", "BackflowPartyGetPartyRewardReply"},
        [23] = {"BackflowPartyChangeItemRequest", "BackflowPartyChangeItemReply"},
        [24] = {"BackflowCompleteMissionRequest", "BackflowCompleteMissionReply"},
        [25] = {"GetBackflowSevenTaskInfoRequest", "GetBackflowSevenTaskInfoReply"},
        [26] = {"GetBackflowSevenTaskProgesssRewardRequest", "GetBackflowSevenTaskProgressRewardReply"},
        [27] = {"GetBackflowPartyLimitInfoRequest", "GetBackflowPartyLimitInfoReply"}
    },
    [69] = {
        [255] = "ThirdPalaceExtension",
        [1] = {"GetThirdPalaceInfoRequest", "GetThirdPalaceInfoReply"},
        [2] = {"GetThirdPalaceGameInfoRequest", "GetThirdPalaceGameInfoReply"},
        [3] = {"ThirdPalaceEnterRoomRequest", "ThirdPalaceEnterRoomReply"},
        [4] = {"ThirdPalaceOpenBoxRequest", "ThirdPalaceOpenBoxReply"},
        [5] = {"ThirdPalaceEnterNextLayerRequest", "ThirdPalaceEnterNextLayerReply"},
        [6] = {"ThirdPalaceBuyHpRequest", "ThirdPalaceBuyHpReply"},
        [7] = {"ThirdPalaceGameFinishRequest", "ThirdPalaceGameFinishReply"},
        [8] = {"ThirdPalaceExitHalfWayRequest", "ThirdPalaceExitHalfWayReply"},
        [9] = {"ThirdPalaceUnlockRequest", "ThirdPalaceUnlockReply"}
    },
    [71] = {
        [255] = "PotionsRefineExtension",
        [1] = {"GetPotionsRefineInfosRequest", "GetPotionsRefineInfosReply"},
        [2] = {"RefinePotionsRequest", "RefinePotionsReply"},
        [3] = {"DrawPotionsRequest", "DrawPotionsReply"},
        [4] = {"RefreshPotionsRefineRequest", "RefreshPotionsRefineReply"},
        [5] = {"SpeedUpFinishRefineRequest", "SpeedUpFinishRefineReply"}
    },
    [72] = {
        [255] = "MoveOrDieExtension",
        [1] = {"SelectRuleRequest", "SelectRuleReply"},
        [2] = {"PrepareRequest", "PrepareReply"},
        [3] = {"StartMoveOrDieGameRequest", "StartMoveOrDieGameReply"},
        [4] = {"SettleMoveOrDieGameRequest", "SettleMoveOrDieGameReply"},
        [11] = {"SettleMoveOrDieGamePush"}
    },
    [73] = {
        [255] = "ItemLockExtension",
        [1] = {"GetItemLockInfoRequest", "GetItemLockInfoReply"},
        [2] = {"SetItemLockPasswordRequest", "SetItemLockPasswordReply"},
        [3] = {"ChangeLockSwitchStateRequest", "ChangeLockSwitchStateReply"},
        [4] = {"ForceResetItemLockRequest", "ForceResetItemLockReply"},
        [5] = {"VerifyItemLockPasswordRequest", "VerifyItemLockPasswordReply"}
    },
    [74] = {
        [255] = "JukeboxExtension",
        [1] = {"GetCurJukeboxInfoRequest", "GetCurJukeboxInfoReply"},
        [2] = {"UseJukeboxRequest", "UseJukeboxReply"},
        [11] = {"JukeboxNewSongPush"},
        [12] = {"JukeboxContentBroadCastPush"}
    },
    [75] = {
        [255] = "SurpriseSignExtension",
        [1] = {"GetSignInfoRequest", "GetSignInfoReply"},
        [2] = {"GetSignRewardRequest", "GetSignRewardReply"}
    },
    [76] = {
        [255] = "MultiRoomListExtension",
        [1] = {"GameRoomGetRecentTeamPlayersRequest", "GameRoomGetRecentTeamPlayersReply"},
        [2] = {"GetCommonRoomListRequest", "GetCommonRoomListReply"},
        [3] = {"CommonRoomListQuickJoinRequest", "CommonRoomListQuickJoinReply"},
        [4] = {"CommonRoomListQuickExitRequest", "CommonRoomListQuickExitReply"},
        [11] = {"CommonRoomListQuickJoinPush"}
    },
    [77] = {
        [255] = "FourthPalaceExtension",
        [1] = {"GetFourthPalaceInfoRequest", "GetFourthPalaceInfoReply"},
        [2] = {"GetFourthPalaceGameInfoRequest", "GetFourthPalaceGameInfoReply"},
        [3] = {"FourthPalaceBoxChangeRequest", "FourthPalaceBoxChangeReply"},
        [4] = {"FourthPalaceBuyHpRequest", "FourthPalaceBuyHpReply"},
        [5] = {"FourthPalaceGameFinishRequest", "FourthPalaceGameFinishReply"},
        [6] = {"FourthPalaceExitHalfWayRequest", "FourthPalaceExitHalfWayReply"},
        [7] = {"FourthPalaceUnlockRequest", "FourthPalaceUnlockReply"}
    },
    [78] = {
        [255] = "DrawAndGuessExtension",
        [1] = {"DrawAndGuessDrawCardRequest", "DrawAndGuessDrawCardReply"},
        [2] = {"DrawAndGuessGuessCardRequest", "DrawAndGuessGuessCardReply"},
        [3] = {"DrawAndGuessSendGiftRequest", "DrawAndGuessSendGiftReply"},
        [4] = {"DrawAndGuessQuestionTypeVoteRequest", "DrawAndGuessQuestionTypeVoteReply"},
        [5] = {"DrawAndGuessChooseQuestionRequest", "DrawAndGuessChooseQuestionReply"},
        [6] = {"DrawAndGuessLikeRequest", "DrawAndGuessLikeReply"},
        [7] = {"DrawAndGuessGetDrawResultRequest", "DrawAndGuessGetDrawResultReply"},
        [8] = {"DrawAndGuessGetCurrentDrawRequest", "DrawAndGuessGetCurrentDrawReply"},
        [9] = {"DrawAndGuessConfirmDisplayRequest", "DrawAndGuessConfirmDisplayReply"},
        [12] = {"DrawAndGuessLikePush"},
        [13] = {"DrawAndGuessSendGiftPush"},
        [14] = {"DrawAndGuessSettlePush"}
    },
    [79] = {
        [255] = "EcoparkExtension",
        [1] = {"GetEcoparkSimpleInfoRequest", "GetEcoparkSimpleInfoReply"},
        [2] = {"GetEcoparkInfoRequest", "GetEcoparkInfoReply"},
        [3] = {"EcoparkDecorateRequest", "EcoparkDecorateReply"},
        [4] = {"GetEcoparkWarehouseRequest", "GetEcoparkWarehouseReply"},
        [5] = {"ExpandEcoparkWarehouseRequest", "ExpandEcoparkWarehouseReply"},
        [6] = {"DropWarehouseItemRequest", "DropWarehouseItemReply"},
        [7] = {"GetRewardRequest", "GetRewardReply"},
        [8] = {"GetEcoparkOPRecordRequest", "GetEcoparkOPRecordReply"},
        [9] = {"EcoparkUnlockAreaRequest", "EcoparkUnlockAreaReply"},
        [10] = {"EcoparkUseItemRequest", "EcoparkUseItemReply"},
        [11] = {"EcoparkChangePush"},
        [12] = {"EcoparkShowSpeciesInfoPush"},
        [21] = {"GetEcoparkSpeciesInfoRequest", "GetEcoparkSpeciesInfoReply"}
    },
    [80] = {
        [255] = "DrawTortoiseGameExtension",
        [1] = {"DrawTortoiseDrawRequest", "DrawTortoiseDrawReply"},
        [2] = {"DrawTortoiseShuffleRequest", "DrawTortoiseShuffleReply"},
        [3] = {"DrawTortoiseQTERequest", "DrawTortoiseQTEReply"},
        [4] = {"DrawTortoiseQuickAutoRequest", "DrawTortoiseQuickAutoReply"},
        [5] = {"DrawTortoiseClientPushRequest", "DrawTortoiseClientPushReply"},
        [11] = {"DrawTortoiseDrawPush"},
        [12] = {"DrawTortoiseEndPush"}
    },
    [81] = {
        [255] = "Backflow3Extension",
        [1] = {"GetBackflow3TeamInfoRequest", "GetBackflow3TeamInfoReply"},
        [2] = {"Backflow3RemoveMemberRequest", "Backflow3RemoveMemberReply"},
        [3] = {"Backflow3InviteMemberRequest", "Backflow3InviteMemberReply"},
        [4] = {"Backflow3ModifyTeamApplyStateRequest", "Backflow3ModifyTeamApplyStateReply"},
        [5] = {"GetBackflow3UserRecommendListRequest", "GetBackflow3UserRecommendListReply"},
        [6] = {"Backflow3CompletePlayRequest", "Backflow3CompletePlayReply"},
        [21] = {"GetBackflow3TeamListRequest", "GetBackflow3TeamListReply"},
        [22] = {"Backflow3ApplyJoinTeamRequest", "Backflow3ApplyJoinTeamReply"},
        [23] = {"Backflow3QuitTeamRequest", "Backflow3QuitTeamReply"},
        [24] = {"Backflow3GetDailyRewardRequest", "Backflow3GetDailyRewardReply"},
        [25] = {"Backflow3SearchTeamRequest", "Backflow3SearchTeamReply"},
        [31] = {"Backflow3GetDailyTaskProgressRewardRequest", "Backflow3GetDailyTaskProgressRewardReply"},
        [32] = {"Backflow3GetLotteryInfoRequest", "Backflow3GetLotteryInfoReply"},
        [33] = {"Backflow3LotteryRequest", "Backflow3LotteryReply"},
        [34] = {"Backflow3GetDiamondSaveInfoRequest", "Backflow3GetDiamondSaveInfoReply"},
        [35] = {"Backflow3GetWishRewardRequest", "Backflow3GetWishRewardReply"},
        [36] = {"Backflow3GetBackWishRewardRequest", "Backflow3GetBackWishRewardReply"},
        [37] = {"Backflow3GetWishListRequest", "Backflow3GetWishListReply"},
        [38] = {"Backflow3GetBackWishListRequest", "Backflow3GetBackWishListReply"}
    },
    [82] = {
        [255] = "FurnitureTemplateShareSquareExtension",
        [1] = {"GetAllShareFurnitureTemplateRequest", "GetAllShareFurnitureTemplateReply"},
        [2] = {"UploadShareFurnitureTemplateRequest", "UploadShareFurnitureTemplateReply"},
        [3] = {"CancelShareFurnitureTemplateRequest", "CancelShareFurnitureTemplateReply"},
        [4] = {"LikeShareFurnitureTemplateRequest", "LikeShareFurnitureTemplateReply"},
        [5] = {"DownloadShareFurnitureTemplateRequest", "DownloadShareFurnitureTemplateReply"},
        [6] = {"CollectShareFurnitureTemplateRequest", "CollectShareFurnitureTemplateReply"},
        [7] = {"GetShareFurnitureTemplateListRequest", "GetShareFurnitureTemplateListReply"},
        [8] = {"GetShareFurnitureTemplateDetailInfoRequest", "GetShareFurnitureTemplateDetailInfoReply"},
        [9] = {"SearchShareFurnitureTemplateRequest", "SearchShareFurnitureTemplateReply"},
        [20] = {"GetFriendFurnitureInfoRequest", "GetFriendFurnitureInfoReply"},
        [21] = {"SendFurnitureTemplateDesignRequest", "SendFurnitureTemplateDesignReply"},
        [22] = {"CollectFurnitureTemplateDesignRequest", "CollectFurnitureTemplateDesignReply"},
        [23] = {"CancelFurnitureTemplateDesignRequest", "CancelFurnitureTemplateDesignReply"},
        [24] = {"GetFurnitureTemplateDesignDetailInfoRequest", "GetFurnitureTemplateDesignDetailInfoReply"},
        [25] = {"SaveFurnitureTemplateDesignRequest", "SaveFurnitureTemplateDesignReply"},
        [26] = {"ShareFurnitureTemplateDesignRequest", "ShareFurnitureTemplateDesignReply"},
        [27] = {"ModifyFurnitureTemplateDesignPermissionRequest", "ModifyFurnitureTemplateDesignPermissionReply"}
    },
    [83] = {
        [255] = "RhythmBattlesExtension",
        [1] = {"RhythmBattleUpdateScoreRequest", "RhythmBattleUpdateScoreReply"},
        [2] = {"OccupyExtraGradingScoreRequest", "OccupyExtraGradingScoreReply"},
        [3] = {"RhythmBattlesReportRobotScoreRequest", "RhythmBattlesReportRobotScoreReply"},
        [4] = {"RhythmBattlesReportRobotDieRequest", "RhythmBattlesReportRobotDieReply"},
        [11] = {"OccupyExtraGradingScoreResultPush"},
        [12] = {"RhythmBattlesTurnsSettlePush"},
        [13] = {"RhythmBattlesDieSettlePush"}
    },
    [84] = {
        [255] = "StoryTellerExtension",
        [1] = {"GetRecommendStoryRequest", "GetRecommendStoryReply"},
        [2] = {"GetDetailStoryRequest", "GetDetailStoryReply"},
        [3] = {"GetRecommendDressRequest", "GetRecommendDressReply"},
        [4] = {"GetIsLikedStoryRoleDressRequest", "GetIsLikedStoryRoleDressReply"},
        [5] = {"GetStoryTellerSelectRoleInfoRequest", "GetStoryTellerSelectRoleInfoReply"},
        [6] = {"GetStoryTellerRewardInfoRequest", "GetStoryTellerRewardInfoReply"},
        [7] = {"SaveStoryTellerRoleDressRequest", "SaveStoryTellerRoleDressReply"},
        [8] = {"SaveSelfStoryRequest", "SaveSelfStoryReply"},
        [9] = {"UploadStoryTellerDressRequest", "UploadStoryTellerDressReply"},
        [10] = {"UploadStoryTellerStoryRequest", "UploadStoryTellerStoryReply"},
        [21] = {"LikeOrCommentStoryTellerRequest", "LikeOrCommentStoryReply"},
        [22] = {"DeletedStoryOrDressRequest", "DeletedStoryOrDressReply"}
    },
    [85] = {
        [255] = "SeafloorIslandExtension",
        [1] = {"GetSeafloorIslandInfoRequest", "GetSeafloorIslandInfoReply"},
        [2] = {"DecorateSeafloorIslandRequest", "DecorateSeafloorIslandReply"},
        [3] = {"GetSeafloorIslandBuildingInfoRequest", "GetSeafloorIslandBuildingInfoReply"}
    },
    [86] = {
        [255] = "SeaSceneExtension",
        [1] = {"GetSeaSceneInfoRequest", "GetSeaSceneInfoReply"},
        [2] = {"SeaSceneFireRequest", "SeaSceneFireReply"},
        [3] = {"SeaSceneFishRequest", "SeaSceneFishReply"},
        [4] = {"SeaSceneFishAwardRequest", "SeaSceneFishAwardReply"},
        [5] = {"SeaSceneOpenBoxRequest", "SeaSceneOpenBoxReply"},
        [6] = {"SeaSceneGetPlotCollectionRequest", "SeaSceneGetPlotCollectionReply"},
        [7] = {"SeaSceneCollectPlotRequest", "SeaSceneCollectPlotReply"},
        [8] = {"SeaSceneUseProfitItemRequest", "SeaSceneUseProfitItemReply"},
        [9] = {"SeaSceneReceiveTaskRequest", "SeaSceneReceiveTaskReply"},
        [10] = {"SeaSceneTaskProgressRequest", "SeaSceneTaskProgressReply"},
        [11] = {"SeaSceneDrawTaskRewardRequest", "SeaSceneDrawTaskRewardReply"},
        [12] = {"SeaSceneGetAllFishDetailRequest", "SeaSceneGetAllFishDetailReply"},
        [13] = {"SeaSceneGetDailyRewardsRequest", "SeaSceneGetDailyRewardsReply"}
    },
    [87] = {
        [255] = "SeafloorTechExtension",
        [1] = {"GetSeafloorTechInfoRequest", "GetSeafloorTechInfoReply"},
        [2] = {"SeafloorTechUpgradeRequest", "SeafloorTechUpgradeReply"},
        [3] = {"SeafloorTechUpgradeFinishRequest", "SeafloorTechUpgradeFinishReply"},
        [4] = {"SeafloorTechBuildingUpgradeRequest", "SeafloorTechBuildingUpgradeReply"},
        [5] = {"SeafloorTechBuildingUpgradeFinishRequest", "SeafloorTechBuildingUpgradeFinishReply"}
    },
    [88] = {
        [255] = "SeabedOrderExtension",
        [1] = {"GetSeabedOrderInfoRequest", "GetSeabedOrderInfoReply"},
        [2] = {"CommitSeabedOrderRequest", "CommitSeabedOrderReply"},
        [3] = {"RejectSeabedOrderRequest", "RejectSeabedOrderReply"},
        [4] = {"RejectAllSeabedOrderRequest", "RejectAllSeabedOrderReply"},
        [5] = {"ClearSeabedNpcOrderWaitingRequest", "ClearSeabedNpcOrderWaitingReply"}
    },
    [89] = {
        [255] = "DollHouseExtension",
        [1] = {"DollHouseLoadCompleteRequest", "DollHouseLoadCompleteReply"},
        [2] = {"DollHouseChooseIdentityRequest", "DollHouseChooseIdentityReply"},
        [3] = {"DollHouseUseItemRequest", "DollHouseUseItemReply"},
        [4] = {"DollHouseUseSkillRequest", "DollHouseUseSkillReply"},
        [5] = {"DollHouseSkillUpdateRequest", "DollHouseSkillUpdateReply"},
        [6] = {"DollHouseStopSkillRequest", "DollHouseStopSkillReply"},
        [7] = {"DollHouseSyncActionRequest", "DollHouseSyncActionReply"},
        [8] = {"DollHouseStartUseBuildingRequest", "DollHouseStartUseBuildingReply"},
        [9] = {"DollHouseUpdateBuildingRequest", "DollHouseUpdateBuildingReply"},
        [10] = {"DollHouseEndUseBuildingRequest", "DollHouseEndUseBuildingReply"},
        [21] = {"DollHouseSwitchRunRequest", "DollHouseSwitchRunReply"},
        [22] = {"DollHouseUpdateHoldTimeRequest", "DollHouseUpdateHoldTimeReply"},
        [23] = {"DollHousePunishUserRequest", "DollHousePunishUserReply"},
        [24] = {"DollHouseTriggerSkillHitRequest", "DollHouseTriggerSkillHitReply"},
        [25] = {"DollHouseVoteSurrenderRequest", "DollHouseVoteSurrenderReply"},
        [11] = {"DollHouseSettlePush"},
        [12] = {"DollHouseSkillTargetPush"}
    },
    [90] = {
        [255] = "FifthPalaceExtension",
        [1] = {"GetFifthPalaceInfoRequest", "GetFifthPalaceInfoReply"},
        [2] = {"GetFifthPalaceGameInfoRequest", "GetFifthPalaceGameInfoReply"},
        [3] = {"FifthPalaceSyncGameInfoRequest", "FifthPalaceSyncGameInfoReply"},
        [4] = {"FifthPalaceOpenBoxRequest", "FifthPalaceOpenBoxReply"},
        [5] = {"FifthPalaceReceiveProfitItemRequest", "FifthPalaceReceiveProfitItemReply"},
        [6] = {"FifthPalaceBuyHpRequest", "FifthPalaceBuyHpReply"},
        [7] = {"FifthPalaceGameFinishRequest", "FifthPalaceGameFinishReply"}
    },
    [91] = {
        [255] = "ClothesCodeExtension",
        [1] = {"GetClothesCodeInfoRequest", "GetClothesCodeInfoReply"},
        [2] = {"GenerateClothesCodeRequest", "GenerateClothesCodeReply"},
        [3] = {"GetClothesCodeDetailRequest", "GetClothesCodeDetailReply"}
    },
    [92] = {
        [255] = "DuoMMExtension",
        [1] = {"DuoMMUseSkillRequest", "DuoMMUseSkillReply"},
        [2] = {"DuoMMSkillHitRequest", "DuoMMSkillHitReply"},
        [3] = {"DuoMMStartUseBuildingRequest", "DuoMMStartUseBuildingReply"},
        [4] = {"DuoMMLoadCompleteRequest", "DuoMMLoadCompleteReply"},
        [5] = {"DuoMMVoiceRequest", "DuoMMVoiceReply"},
        [6] = {"DuoMMSetRotateRequest", "DuoMMSetRotateReply"},
        [11] = {"DuoMMSettlePush"},
        [12] = {"DuoMMUseSkillPush"},
        [13] = {"DuoMMUseBuildingPush"},
        [14] = {"DuoMMSkillHitPush"},
        [15] = {"DuoMMVoicePush"}
    },
    [93] = {
        [255] = "ShareStreetExtension",
        [1] = {"GetShareStreetInfoRequest", "GetShareStreetInfoReply"},
        [2] = {"ApplyJoinShareStreetRequest", "ApplyJoinShareStreetReply"},
        [3] = {"GetStreetReceiveAskListRequest", "GetStreetReceiveAskListReply"},
        [4] = {"AgreeJoinShareStreetRequest", "AgreeJoinShareStreetReply"},
        [5] = {"RefuseJoinShareStreetRequest", "RefuseJoinShareStreetReply"},
        [6] = {"QuitShareStreetRequest", "QuitShareStreetReply"},
        [7] = {"KickOutShareStreetRequest", "KickOutShareStreetReply"},
        [8] = {"InviteJoinShareStreetRequest", "InviteJoinShareStreetReply"},
        [9] = {"AgreeInviteJoinShareStreetRequest", "AgreeInviteJoinShareStreetReply"},
        [10] = {"CreateShareStreetRequest", "CreateShareStreetReply"},
        [11] = {"StreetFurnitureInfoChangePush"},
        [21] = {"StreetShareFurnitureRequest", "StreetShareFurnitureReply"},
        [22] = {"StreetCancelShareFurnitureRequest", "StreetCancelShareFurnitureReply"},
        [23] = {"StreetUnlockAreaRequest", "StreetUnlockAreaReply"},
        [24] = {"StreetUpdateInfoRequest", "StreetUpdateInfoReply"},
        [25] = {"GetShareStreetFurnitureInfoRequest", "GetShareStreetFurnitureInfoReply"},
        [26] = {"DecorateShareStreetRequest", "DecorateShareStreetReply"},
        [27] = {"ChangeShareStreetHouseRequest", "ChangeShareStreetHouseReply"},
        [29] = {"GetPersonShareFurnitureCountRequest", "GetPersonShareFurnitureCountReply"},
        [30] = {"GetStreetShareFurnitureCountRequest", "GetStreetShareFurnitureCountReply"},
        [31] = {"VisitShareStreetRequest", "VisitShareStreetReply"},
        [32] = {"GetStreetRecommendListRequest", "GetStreetRecommendListReply"},
        [33] = {"SearchShareStreetRequest", "SearchShareStreetReply"},
        [34] = {"GetUserJoinedStreetIdRequest", "GetUserJoinedStreetIdReply"},
        [35] = {"GetStreetApplyListRequest", "GetStreetApplyListReply"},
        [36] = {"LikeShareStreetRequest", "LikeShareStreetReply"},
        [37] = {"ModifyStreetAcceptAskRequest", "ModifyStreetAcceptAskReply"},
        [38] = {"GetStreetPopularityRankRequest", "GetStreetPopularityRankReply"},
        [39] = {"GetFriendStreetListRequest", "GetFriendStreetListReply"},
        [40] = {"GetStreetReceiveInviteListRequest", "GetStreetReceiveInviteListReply"},
        [41] = {"CancelApplyJoinShareStreetRequest", "CancelApplyJoinShareStreetReply"}
    },
    [98] = {
        [255] = "SnakeExtension",
        [1] = {"StartSoloGameRequest", "StartSoloGameReply"},
        [2] = {"ReviveRequest", "ReviveReply"},
        [3] = {"SettleSoloGameRequest", "SettleSoloGameReply"},
        [4] = {"SettleMultiPlayerGameRequest", "SettleMultiPlayerGameReply"},
        [11] = {"SettleMultiPlayerGamePush"}
    },
    [99] = {
        [255] = "FrameExtension",
        [1] = {"EnterBattleRequest", "EnterBattleReply"},
        [2] = {"CreateBattleRequest", "CreateBattleReply"},
        [3] = {"EndBattleRequest", "EndBattleReply"},
        [4] = {"ExitBattleRequest", "ExitBattleReply"},
        [5] = {"CommandRequest", "CommandReply"},
        [6] = {"NetFrameDataPush"},
        [7] = {"GetLostFrameDataRequest", "GetLostFrameDataReply"},
        [8] = {"BattleServerHeartbeatRequest", "BattleServerHeartbeatReply"}
    },
    [100] = {
        [255] = "ActivityExtension1",
        [1] = {"GetOpenActivityInfoRequest", "GetOpenActivityInfoReply"},
        [2] = {"GetActivityCalendarRewardRequest", "GetActivityCalendarRewardReply"},
        [3] = {"GetActivityCalendarRewardInfoRequest", "GetActivityCalendarRewardInfoReply"},
        [4] = {"GetInviteCodeRoleInfoRequest", "GetInviteCodeRoleInfoReply"},
        [11] = {"ActivityChangeStatePush"},
        [12] = {"ActivityInfoRefreshPush"},
        [13] = {"ActivityGainLimitPush"},
        [61] = {"SignRequest", "SignReply"},
        [71] = {"SignPush"},
        [81] = {"LoginGiftRequest", "LoginGiftReply"},
        [82] = {"GetLoginGiftRequest", "GetLoginGiftReply"},
        [91] = {"LoginGiftPush"}
    },
    [101] = {
        [255] = "Activity101Extension",
        [1] = {"GetActivity101DataRequest", "GetActivity101DataReply"},
        [2] = {"GetActivity101CollectionRequest", "GetActivity101CollectionReply"},
        [3] = {"LightUpActivity101PartialRequest", "LightUpActivity101PartialReply"}
    },
    [102] = {
        [255] = "Activity102Extension",
        [1] = {"GetAobiBankInfoRequest", "GetAobiBankInfoReply"},
        [2] = {"DrawAobiBankRequest", "DrawAobiBankReply"},
        [11] = {"AobiBankInvestPush"}
    },
    [103] = {
        [255] = "Activity103Extension",
        [1] = {"Get103RewardRequest", "Get103RewardReply"},
        [2] = {"GetHotSpringSnowCountRequest", "GetHotSpringSnowCountReply"}
    },
    [107] = {
        [255] = "Activity107Extension",
        [1] = {"GetHappyRefleshSceneRequest", "GetHappyRefleshSceneReply"},
        [2] = {"GetHappyFightMissionRequest", "GetHappyFightMissionReply"},
        [3] = {"DrawHappyBoxRewardRequest", "DrawHappyBoxRewardReply"},
        [4] = {"GetHappyRankInfoRequest", "GetHappyRankInfoReply"},
        [11] = {"ExplosionRateGainPush"}
    },
    [109] = {
        [255] = "Activity109Extension",
        [1] = {"GetActivityLoginInfoRequest", "GetActivityLoginInfoReply"},
        [2] = {"GetActivityLoginRewardRequest", "GetActivityLoginRewardReply"}
    },
    [110] = {
        [255] = "Activity110Extension",
        [1] = {"GetFireworksPartyGiftRequest", "GetFireworksPartyGiftReply"}
    },
    [111] = {
        [255] = "Activity111Extension",
        [1] = {"GetAobiLifeInfoRequest", "GetAobiLifeInfoReply"},
        [2] = {"GetAllFriendsExtremeStateRequest", "GetAllFriendsExtremeStateReply"},
        [3] = {"DrawAobiLifeLevelRewardRequest", "DrawAobiLifeLevelRewardReply"},
        [4] = {"BuyAobiLifeLevelRequest", "BuyAobiLifeLevelReply"},
        [5] = {"RefreshOneTaskRequest", "RefreshOneTaskReply"},
        [6] = {"GetExtraRewardsRequest", "GetExtraRewardsReply"},
        [7] = {"GetAobiLifeDailyRewardRequest", "GetAobiLifeDailyRewardReply"},
        [11] = {"AobiLifeInfoChangePush"}
    },
    [114] = {
        [255] = "Activity114Extension",
        [1] = {"GetFirstChargeInfoRequest", "GetFirstChargeInfoReply"},
        [2] = {"DrawFirstChargeRewardRequest", "DrawFirstChargeRewardReply"},
        [11] = {"FirstChargeCountPush"}
    },
    [115] = {
        [255] = "Activity115Extension",
        [1] = {"GetSevenDayChargeInfoRequest", "GetSevenDayChargeInfoReply"},
        [2] = {"DrawSevenDayChargeRewardRequest", "DrawSevenDayChargeRewardReply"},
        [11] = {"SevenDayChargePush"}
    },
    [116] = {
        [255] = "Activity116Extension",
        [1] = {"GetLevelAwardInfoRequest", "GetLevelAwardInfoReply"},
        [2] = {"GetLevelAwardRequest", "GetLevelAwardReply"}
    },
    [117] = {
        [255] = "Activity117Extension",
        [1] = {"GetSevenDayTaskProcessAwardRequest", "GetSevenDayTaskProcessAwardReply"},
        [2] = {"GetSevenDayTaskProcessInfoRequest", "GetSevenDayTaskProcessInfoReply"}
    },
    [122] = {
        [255] = "Activity122Extension",
        [1] = {"GetRankRequest", "GetRankReply"},
        [2] = {"GetGameReportRequest", "GetGameReportReply"},
        [11] = {"RemoteErrorPush"}
    },
    [124] = {
        [255] = "Activity124Extension",
        [1] = {"GetFirstRecharge2InfoRequest", "GetFirstRecharge2InfoReply"},
        [2] = {"GetFirstRecharge2RewardsRequest", "GetFirstRecharge2RewardsReply"}
    },
    [125] = {
        [255] = "Activity125Extension",
        [1] = {"GetGameInfoRequest", "GetGameInfoReply"},
        [2] = {"StartRollRequest", "StartRollReply"},
        [3] = {"GetPointsRewardsRequest", "GetPointsRewardsReply"},
        [4] = {"BuyDiceRequest", "BuyDiceReply"}
    },
    [126] = {
        [255] = "Activity126Extension",
        [1] = {"GetRewardsInfoRequest", "GetRewardsInfoReply"},
        [2] = {"GetRewardsRequest", "GetRewardsReply"}
    },
    [128] = {
        [255] = "Activity128Extension",
        [1] = {"GetAct128SignInfoRequest", "GetAct128SignInfoReply"},
        [2] = {"GetAct128RewardsRequest", "GetAct128RewardsReply"}
    },
    [130] = {
        [255] = "Activity130Extension",
        [1] = {"GetActivity130DataRequest", "GetActivity130DataReply"},
        [2] = {"GetActivity130CollectionRequest", "GetActivity130CollectionReply"},
        [3] = {"LightUpActivity130PartialRequest", "LightUpActivity130PartialReply"}
    },
    [131] = {
        [255] = "Activity131Extension",
        [1] = {"SelectFlowerCloudRankingRequest", "SelectFlowerCloudRankingReply"},
        [2] = {"GetFlowerCloudRewardInfoRequest", "GetFlowerCloudRewardInfoReply"},
        [3] = {"GetFlowerCloudRewardRequest", "GetFlowerCloudRewardReply"}
    },
    [132] = {
        [255] = "Activity132Extension",
        [1] = {"GetAct132InfoRequest", "GetAct132InfoReply"},
        [2] = {"GetAct132RewardsRequest", "GetAct132RewardsReply"}
    },
    [133] = {
        [255] = "Activity133Extension",
        [1] = {"GetGodFlowerRefleshSceneRequest", "GetGodFlowerRefleshSceneReply"},
        [2] = {"GetGodFlowerMissionRequest", "GetGodFlowerMissionReply"},
        [3] = {"DrawGodFlowerRewardRequest", "DrawGodFlowerRewardReply"},
        [4] = {"GetGodFlowerRankInfoRequest", "GetGodFlowerRankInfoReply"},
        [11] = {"GodFlowerExplosionRateGainPush"}
    },
    [135] = {
        [255] = "Activity135Extension",
        [1] = {"GetAct135GiftRequest", "GetAct135GiftReply"}
    },
    [138] = {
        [255] = "Activity138Extension",
        [1] = {"GetAct138SignInfoRequest", "GetAct138SignInfoReply"},
        [2] = {"GetAct138RewardsRequest", "GetAct138RewardsReply"}
    },
    [140] = {
        [255] = "Activity140Extension",
        [1] = {"StarMatchChooseTeamRequest", "StarMatchChooseTeamReply"},
        [2] = {"StarMatchUploadScoreRequest", "StarMatchUploadScoreReply"},
        [3] = {"StarMatchGetAllTeamInfoRequest", "StarMatchGetAllTeamInfoReply"},
        [4] = {"StarMatchGetMineInfoRequest", "StarMatchGetMineInfoReply"},
        [5] = {"StarMatchGetFriendTeamInfoRequest", "StarMatchGetFriendTeamInfoReply"},
        [6] = {"GetStarMatchHistoryScoreRequest", "GetStarMatchHistoryScoreReply"},
        [7] = {"StarSupportVoteRequest", "StarSupportVoteReply"},
        [8] = {"GetStarSupportInfoRequest", "GetStarSupportInfoReply"},
        [21] = {"GetAwardCeremonyInfoRequest", "GetAwardCeremonyInfoReply"},
        [22] = {"DrawAwardCeremonyRequest", "DrawAwardCeremonyReply"},
        [23] = {"GetFarewellPartyInfoRequest", "GetFarewellPartyInfoReply"},
        [24] = {"FarewellPartySendOffRequest", "FarewellPartySendOffReply"}
    },
    [143] = {
        [255] = "Activity143Extension",
        [1] = {"GetActivity143InfoRequest", "GetActivity143InfoReply"},
        [2] = {"GetActivity143QuestionRequest", "GetActivity143QuestionReply"},
        [3] = {"StareAnswerRequest", "StareAnswerReply"},
        [4] = {"GetAct143DailyFirstRewardRequest", "GetAct143DailyFirstRewardReply"},
        [5] = {"GetAct143AccumulateRewardRequest", "GetAct143AccumulateRewardReply"}
    },
    [144] = {
        [255] = "Activity144Extension",
        [1] = {"GetAct144GameInfoRequest", "GetAct144GameInfoReply"},
        [2] = {"StartAct144ShootRequest", "StartAct144ShootReply"},
        [3] = {"FinishAct144ShootRequest", "FinishAct144ShootReply"},
        [4] = {"GetAct144RewardRequest", "GetAct144RewardReply"}
    },
    [145] = {
        [255] = "Activity145Extension",
        [1] = {"GetAct145ReportInfoRequest", "GetAct145ReportInfoReply"},
        [2] = {"GetAct145RewardsRequest", "GetAct145RewardsReply"}
    },
    [146] = {
        [255] = "Activity146Extension",
        [1] = {"GetAct146InfoRequest", "GetAct146InfoReply"},
        [2] = {"BuyAct146GoodsRequest", "BuyAct146GoodsReply"}
    },
    [150] = {
        [255] = "Activity150Extension",
        [1] = {"StartAct150GameRequest", "StartAct150GameReply"},
        [2] = {"FinishAct150GameRequest", "FinishAct150GameReply"}
    },
    [152] = {
        [255] = "Activity152Extension",
        [1] = {"GetAct152FinallyRewardRequest", "GetAct152FinallyRewardReply"},
        [2] = {"GetAct152RewardInfoRequest", "GetAct152RewardInfoReply"}
    },
    [153] = {
        [255] = "Activity153Extension",
        [1] = {"GetAct153InfoRequest", "GetAct153InfoReply"},
        [2] = {"AddAct153ScoreRequest", "AddAct153ScoreReply"},
        [3] = {"GetAct153RewardRequest", "GetAct153RewardReply"}
    },
    [156] = {
        [255] = "Activity156Extension",
        [1] = {"GetAct156InfoRequest", "GetAct156InfoReply"},
        [2] = {"Act156FinishCollectionRequest", "Act156FinishCollectionReply"}
    },
    [157] = {
        [255] = "Activity157Extension",
        [1] = {"GetAct157InfoRequest", "GetAct157InfoReply"},
        [2] = {"Act157CollectRequest", "Act157CollectReply"},
        [3] = {"Act157DrawWishRewardRequest", "Act157DrawWishRewardReply"}
    },
    [158] = {
        [255] = "Activity158Extension",
        [1] = {"GetAct158InfoRequest", "GetAct158InfoReply"},
        [2] = {"Act158RewardRequest", "Act158RewardReply"},
        [3] = {"Act158JoinCopyRequest", "Act158JoinCopyReply"},
        [4] = {"Act158KickRequest", "Act158KickReply"},
        [11] = {"Act158KickPush"}
    },
    [159] = {
        [255] = "Activity159Extension",
        [1] = {"GetAct159InfoRequest", "GetAct159InfoReply"},
        [2] = {"GetAct159SignRewardRequest", "GetAct159SignRewardReply"},
        [3] = {"GetAct159RechargeRewardRequest", "GetAct159RechargeRewardReply"}
    },
    [160] = {
        [255] = "Activity160Extension",
        [1] = {"GetAct160InfoRequest", "GetAct160InfoReply"},
        [2] = {"BuyAct160TicketsRequest", "BuyAct160TicketsReply"},
        [3] = {"StartAct160LotteryRequest", "StartAct160LotteryReply"},
        [4] = {"Act160GetAccumulateRewardRequest", "Act160GetAccumulateRewardReply"}
    },
    [161] = {
        [255] = "Activity161Extension",
        [1] = {"GetAct161InfoRequest", "GetAct161InfoReply"},
        [2] = {"Act161LotteryRequest", "Act161LotteryReply"},
        [3] = {"Act161DrawProcessRequest", "Act161DrawProcessReply"},
        [4] = {"Act161ConvertLotteryItemRequest", "Act161ConvertLotteryItemReply"},
        [5] = {"Act161DrawFatigueRewardRequest", "Act161DrawFatigueRewardReply"},
        [6] = {"Act161QueryLotteryRecordsRequest", "Act161QueryLotteryRecordsReply"},
        [7] = {"Act161DrawLotteryScoreRewardRequest", "Act161DrawLotteryScoreRewardReply"},
        [8] = {"Act161GetRedEnvelopeInfoRequest", "Act161GetRedEnvelopeInfoReply"},
        [9] = {"Act161GetRedEnvelopeDrawRecordsRequest", "Act161GetRedEnvelopeDrawRecordsReply"},
        [10] = {"Act161GetRedEnvelopeSendStatisticsRequest", "Act161GetRedEnvelopeSendStatisticsReply"},
        [11] = {"Act161DrawRedEnvelopeRequest", "Act161DrawRedEnvelopeReply"},
        [12] = {"Act161LikeRedEnvelopeRequest", "Act161LikeRedEnvelopeReply"},
        [13] = {"Act161GetGlobalLotteryRecordsRequest", "Act161GetGlobalLotteryRecordsReply"}
    },
    [163] = {
        [255] = "Activity163Extension",
        [1] = {"GetAct163InfoRequest", "GetAct163InfoReply"},
        [2] = {"Act163MatchRequest", "Act163MatchCommonReply"},
        [3] = {"Act163CancelMatchRequest", "Act163CancelMatchReply"},
        [4] = {"Act163DressRequest", "Act163DressCommonReply"},
        [5] = {"Act163ChatRequest", "Act163ChatCommonReply"},
        [6] = {"Act163GradeRequest", "Act163GradeCommonReply"},
        [7] = {"Act163ThumpUpRequest", "Act163ThumpUpCommonReply"},
        [8] = {"GetAct163UserListRequest", "GetAct163UserListReply"},
        [9] = {"GetAct163StatusRequest", "GetAct163StatusReply"},
        [10] = {"GetAct163TimesRequest", "GetAct163TimesReply"},
        [21] = {"Act163MatchReply"},
        [22] = {"GameStatusReply"},
        [23] = {"Act163GradeReply"},
        [24] = {"Act163ThumpUpReply"},
        [25] = {"Act163ChatReply"},
        [26] = {"Act163DressReply"}
    },
    [166] = {
        [255] = "Activity166Extension",
        [1] = {"Act166MakeItemRequest", "Act166MakeItemReply"}
    },
    [170] = {
        [255] = "Activity170Extension",
        [1] = {"GetAct170InfoRequest", "GetAct170InfoReply"},
        [2] = {"Act170DrawDaysRewardRequest", "Act170DrawDaysRewardReply"}
    },
    [173] = {
        [255] = "Activity173Extension",
        [1] = {"GetAct173FlopGameInfoRequest", "GetAct173FlopGameInfoReply"},
        [2] = {"StartAct173FlopGameRequest", "StartAct173FlopGameReply"},
        [3] = {"FinishAct173FlopGameRequest", "FinishAct173FlopGameReply"},
        [4] = {"GetAct173FirstPassRewardRequest", "GetAct173FirstPassRewardReply"}
    },
    [175] = {
        [255] = "Activity175Extension",
        [1] = {"GetAct175InfoRequest", "GetAct175InfoReply"},
        [2] = {"Act175VerifyGuessRequest", "Act175VerifyGuessReply"},
        [3] = {"Act175DerivationConclusionRequest", "Act175DerivationConclusionReply"},
        [4] = {"Act175DrawRewardsRequest", "Act175DrawRewardsReply"}
    },
    [178] = {
        [255] = "Activity178Extension",
        [1] = {"GetAct178FindDiffInfoRequest", "GetAct178FindDiffInfoReply"},
        [2] = {"StartAct178FindDiffRequest", "StartAct178FindDiffReply"},
        [3] = {"FinishAct178FindDiffRequest", "FinishAct178FindDiffReply"},
        [4] = {"GetAct178FirstPassRewardRequest", "GetAct178FirstPassRewardReply"}
    },
    [179] = {
        [255] = "BackflowExtension",
        [1] = {"ActIsOpenRequest", "ActIsOpenReply"},
        [2] = {"GetActUserInfoRequest", "GetActUserInfoReply"},
        [3] = {"SignInRequest", "SignInReply"},
        [4] = {"DailyActiveRewardRequest", "DailyActiveRewardReply"}
    },
    [180] = {
        [255] = "Activity180Extension",
        [1] = {"GetAct180CollectRequest", "GetAct180CollectReply"},
        [11] = {"Act180RateGainPush"}
    },
    [183] = {
        [255] = "Activity183Extension",
        [1] = {"GetAct183InfoRequest", "GetAct183InfoReply"},
        [2] = {"GetAct183RewardRequest", "GetAct183RewardReply"},
        [11] = {"Act183AddScorePush"}
    },
    [188] = {
        [255] = "Activity188Extension",
        [1] = {"GetAct188UserInfoRequest", "GetAct188UserInfoReply"},
        [2] = {"Act188SignInRequest", "Act188SignInReply"}
    },
    [189] = {
        [255] = "HappyBreadExtension",
        [1] = {"BeginHappyBreadRequest", "BeginHappyBreadReply"},
        [2] = {"EndHappyBreadRequest", "EndHappyBreadReply"},
        [3] = {"GetHappyBreadInfoRequest", "GetHappyBreadInfoReply"},
        [4] = {"GetHappyBreadRewardRequest", "GetHappyBreadRewardReply"}
    },
    [190] = {
        [255] = "Activity190Extension",
        [1] = {"GetAllVoteInfoRequest", "GetAllVoteInfoReply"},
        [2] = {"UCGVoteRequest", "UCGVoteReply"}
    },
    [191] = {
        [255] = "Activity191Extension",
        [1] = {"GetAct191InfoRequest", "GetAct191InfoReply"},
        [2] = {"BindInvitationRequest", "BindInvitationReply"},
        [3] = {"GetAct191RewardRequest", "GetAct191RewardReply"},
        [4] = {"GetInvitationUserInfoRequest", "GetInvitationUserInfoReply"}
    },
    [192] = {
        [255] = "Activity192Extension",
        [1] = {"GetActivity192DataRequest", "GetActivity192DataReply"},
        [2] = {"GetActivity192CollectionRequest", "GetActivity192CollectionReply"},
        [3] = {"LightUpActivity192PartialRequest", "LightUpActivity192PartialReply"},
        [4] = {"Act192BindFriendRequest", "Act192BindFriendReply"},
        [5] = {"Act192SendFriendGiftRequest", "Act192SendFriendGiftReply"}
    },
    [193] = {
        [255] = "Activity193Extension",
        [1] = {"GetActivityDetailInfoRequest", "GetActivityDetailInfoReply"},
        [2] = {"GetAct193RewardRequest", "GetAct193RewardReply"}
    },
    [194] = {
        [255] = "Activity194Extension",
        [1] = {"GetAct194UserInfoRequest", "GetAct194UserInfoReply"},
        [2] = {"Act194OpenBlindBoxRequest", "Act194OpenBlindBoxReply"}
    },
    [195] = {
        [255] = "Activity195Extension",
        [1] = {"GetAct195InfoRequest", "GetAct195InfoReply"},
        [2] = {"Act195FinishOrderRequest", "Act195FinishOrderReply"}
    },
    [196] = {
        [255] = "Activity196Extension",
        [1] = {"GetAct196SignInfoRequest", "GetAct196SignInfoReply"},
        [2] = {"GetAct196RewardsRequest", "GetAct196RewardsReply"}
    },
    [198] = {
        [255] = "Activity198Extension",
        [1] = {"GetAct198InfoRequest", "GetAct198InfoReply"},
        [2] = {"GetAct198RewardsRequest", "GetAct198RewardsReply"}
    },
    [199] = {
        [255] = "Activity199Extension",
        [1] = {"GetAct199SignInfoRequest", "GetAct199SignInfoReply"},
        [2] = {"GetAct199RewardsRequest", "GetAct199RewardsReply"},
        [3] = {"Act199LateSignRequest", "Act199LateSignReply"}
    },
    [200] = {
        [255] = "Activity200Extension",
        [1] = {"GetActivity200TaskProcessAwardRequest", "GetActivity200TaskProcessAwardReply"},
        [2] = {"GetActivity200TaskProcessInfoRequest", "GetActivity200TaskProcessInfoReply"}
    },
    [202] = {
        [255] = "Activity202Extension",
        [1] = {"BeginLinkGameRequest", "BeginLinkGameReply"},
        [2] = {"EndLinkGameRequest", "EndLinkGameReply"},
        [3] = {"GetLinkGameRequest", "GetLinkGameReply"},
        [4] = {"GetLinkGameRewardRequest", "GetLinkGameRewardReply"}
    },
    [204] = {
        [255] = "Activity204Extension",
        [1] = {"GetAct204JumpInfoRequest", "GetAct204JumpInfoReply"},
        [2] = {"StartAct204JumpRequest", "StartAct204JumpReply"},
        [3] = {"AddAct204JumpCountRequest", "AddAct204JumpCountReply"},
        [4] = {"FinishAct204GameRequest", "FinishAct204GameReply"},
        [5] = {"GetAct204ScoreTargetRewardRequest", "GetAct204ScoreTargetRewardReply"}
    },
    [205] = {
        [255] = "Activity205Extension",
        [1] = {"GetPaintingInfoRequest", "GetPaintingInfoReply"},
        [2] = {"SubmitGraffitiRequest", "SubmitGraffitiReply"},
        [3] = {"GetCoatingCollectInfoRequest", "GetCoatingCollectInfoReply"},
        [11] = {"CoatingDropPush"}
    },
    [207] = {
        [255] = "Activity207Extension",
        [1] = {"GetAct207GameInfoRequest", "GetAct207GameInfoReply"},
        [2] = {"StartAct207GameRequest", "StartAct207GameReply"},
        [3] = {"FinishAct207GameRequest", "FinishAct207GameReply"},
        [4] = {"GetAct207ScoreTargetRewardRequest", "GetAct207ScoreTargetRewardReply"},
        [11] = {"Act207CollecPush"}
    },
    [209] = {
        [255] = "Activity209Extension",
        [1] = {"GetChallengeInfoRequest", "GetChallengeInfoReply"},
        [2] = {"GainRewardRequest", "GainRewardReply"}
    },
    [211] = {
        [255] = "Activity211Extension",
        [1] = {"GetActivityInfoRequest", "GetActivityInfoReply"},
        [2] = {"WriteStoneMsgRequest", "WriteStoneMsgReply"},
        [3] = {"GetStoneDetailRequest", "GetStoneDetailReply"},
        [4] = {"ReadStoneMsgRequest", "ReadStoneMsgReply"}
    },
    [213] = {
        [255] = "Activity213Extension",
        [1] = {"StartAct213GameRequest", "StartAct213GameReply"},
        [2] = {"FinishAct213GameRequest", "FinishAct213GameReply"},
        [3] = {"GetAct213InfoRequest", "GetAct213InfoReply"}
    },
    [214] = {
        [255] = "Activity214Extension",
        [1] = {"Act214GetInfoRequest", "Act214GetInfoReply"},
        [2] = {"Act214DrawChapterRewardRequest", "Act214DrawChapterRewardReply"},
        [3] = {"Act214StartGameRequest", "Act214StartGameReply"},
        [4] = {"Act214ClickInteractionRequest", "Act214ClickInteractionReply"},
        [5] = {"Act214EndGameRequest", "Act214EndGameReply"},
        [6] = {"Act214GetGameInfoRequest", "Act214GetGameInfoReply"},
        [11] = {"Act214GamePush"}
    },
    [215] = {
        [255] = "Activity215Extension",
        [1] = {"GetAct215TotalServerProcessRequest", "GetAct215TotalServerProcessReply"},
        [2] = {"GetAct215RewardRequest", "GetAct215RewardReply"},
        [11] = {"Act215PipeLineFinalRewardPush"}
    },
    [217] = {
        [255] = "Activity217Extension",
        [1] = {"GetAct217GameInfoRequest", "GetAct217GameInfoReply"},
        [2] = {"AnswerAct217QuestionRequest", "AnswerAct217QuestionReply"},
        [3] = {"GetAct217ScoreRewardRequest", "GetAct217ScoreRewardReply"}
    },
    [218] = {
        [255] = "Activity218Extension",
        [1] = {"Act218GetInfoRequest", "Act218GetInfoReply"},
        [11] = {"Act218ExplosionRateGainPush"}
    },
    [219] = {
        [255] = "Activity219Extension",
        [1] = {"Act219GetRefreshSceneRequest", "Act219GetRefreshSceneReply"},
        [2] = {"Act219DrawRewardRequest", "Act219DrawRewardReply"},
        [3] = {"Act219DrawAchievementRewardRequest", "Act219DrawAchievementRewardReply"},
        [4] = {"Act219GetRankInfoRequest", "Act219GetRankInfoReply"}
    },
    [220] = {
        [255] = "Activity220Extension",
        [1] = {"Activity220CompoundRequest", "Activity220CompoundReply"},
        [2] = {"Activity220GetRewardRequest", "Activity220GetRewardReply"},
        [3] = {"GetActivity220InfoRequest", "GetActivity220InfoReply"},
        [4] = {"GetActivity220ProcessRewardRequest", "GetActivity220ProcessRewardReply"},
        [5] = {"Activity220SendCardRequest", "Activity220SendCardReply"},
        [6] = {"GetActivity220CardRewardRequest", "GetActivity220CardRewardReply"}
    },
    [221] = {
        [255] = "Activity221Extension",
        [1] = {"Act221GetInfoRequest", "Act221GetInfoReply"},
        [2] = {"Act221DrawRewardRequest", "Act221DrawRewardReply"}
    },
    [222] = {
        [255] = "Activity222Extension",
        [1] = {"GetAct222SignInfoRequest", "GetAct222SignInfoReply"},
        [2] = {"GetAct222RewardsRequest", "GetAct222RewardsReply"}
    },
    [227] = {
        [255] = "Activity227Extension",
        [1] = {"GetActivity227InfoRequest", "GetActivity227InfoReply"},
        [2] = {"GetIncubationInfoRequest", "GetIncubationInfoReply"},
        [3] = {"PolishEggRequest", "PolishEggReply"},
        [4] = {"IncrEnvironmentValRequest", "IncrEnvironmentValReply"},
        [5] = {"GainProcessRewardRequest", "GainProcessRewardReply"},
        [6] = {"GainPetRewardRequest", "GainPetRewardReply"}
    },
    [228] = {
        [255] = "Activity228Extension",
        [1] = {"Act228GetInfoRequest", "Act228GetInfoReply"},
        [2] = {"Act228DrawRewardRequest", "Act228DrawRewardReply"}
    },
    [229] = {
        [255] = "Activity229Extension",
        [1] = {"GetAct229SignInfoRequest", "GetAct229SignInfoReply"},
        [2] = {"GetAct229AccumulateRewardsRequest", "GetAct229AccumulateRewardsReply"},
        [3] = {"GetAct229DailyRewardRequest", "GetAct229DailyRewardReply"}
    },
    [231] = {
        [255] = "Activity231Extension",
        [1] = {"GetAct231InfoRequest", "GetAct231InfoReply"},
        [2] = {"GainDailyRewardRequest", "GainDailyRewardReply"}
    },
    [232] = {
        [255] = "Activity232Extension",
        [1] = {"GetActivity232InfoRequest", "GetActivity232InfoReply"},
        [2] = {"ClickLevelRequest", "ClickLevelReply"},
        [3] = {"UnlockLevelRequest", "UnlockLevelReply"},
        [4] = {"StartChallengeRequest", "StartChallengeReply"},
        [5] = {"EndChallengeRequest", "EndChallengeReply"},
        [6] = {"GainStarRewardRequest", "GainStarRewardReply"}
    },
    [233] = {
        [255] = "Activity233Extension",
        [1] = {"GetAct233SevenGameInfoRequest", "GetAct233SevenGameInfoReply"},
        [2] = {"StartAct233SevenGameRequest", "StartAct233SevenGameReply"},
        [3] = {"FinishAct233SevenGameRequest", "FinishAct233SevenGameReply"}
    },
    [234] = {
        [255] = "Activity234Extension",
        [1] = {"GetAct234InfoRequest", "GetAct234InfoReply"},
        [2] = {"FeedNpcRequest", "FeedNpcReply"}
    },
    [235] = {
        [255] = "Activity235Extension",
        [1] = {"GetAct235InfoRequest", "GetAct235InfoReply"},
        [2] = {"SwitchAct235ShowRolePosRequest", "SwitchAct235ShowRolePosReply"},
        [3] = {"BindInviteCodeRequest", "BindInviteCodeReply"}
    },
    [236] = {
        [255] = "Activity236Extension",
        [1] = {"GetAct236FallBallInfoRequest", "GetAct236FallBallInfoReply"},
        [2] = {"StartAct236FallBallRequest", "StartAct236FallBallReply"},
        [3] = {"AddAct236FallScoreRequest", "AddAct236FallScoreReply"},
        [4] = {"FinishAct236GameRequest", "FinishAct236GameReply"},
        [5] = {"GetAct236ScoreTargetRewardRequest", "GetAct236ScoreTargetRewardReply"}
    },
    [237] = {
        [255] = "Activity237Extension",
        [1] = {"Act237GetInfoRequest", "Act237GetInfoReply"},
        [2] = {"Act237DrawRewardRequest", "Act237DrawRewardReply"}
    },
    [238] = {
        [255] = "Activity238Extension",
        [1] = {"GetMusicPartyDetailRequest", "GetMusicPartyDetailReply"},
        [2] = {"WritePhotoStoryRequest", "WritePhotoStoryReply"},
        [3] = {"SendBarrageRequest", "SendBarrageReply"},
        [4] = {"HiFunRequest", "HiFunReply"},
        [5] = {"EnterDanceAreaRequest", "EnterDanceAreaReply"},
        [6] = {"ExitDanceAreaRequest", "ExitDanceAreaReply"},
        [7] = {"DancerHiFunRequest", "DancerHiFunReply"},
        [8] = {"GetMyPhotoStoryInfoRequest", "GetMyPhotoStoryInfoReply"},
        [9] = {"GetMusicPartyPrizeRequest", "GetMusicPartyPrizeReply"},
        [10] = {"ChangeMyDanceRateRequest", "ChangeMyDanceRateReply"},
        [11] = {"OpenBarrageRequest", "OpenBarrageReply"},
        [12] = {"OpenDanceAutoRequest", "OpenDanceAutoReply"},
        [13] = {"MusicPartyInfoChangePush"},
        [14] = {"MusicPartyDancerHiFunPush"},
        [15] = {"MusicPartyPhotoStoryPush"},
        [16] = {"MusicPartyBarragePush"},
        [21] = {"GetProgressRewardRequest", "GetProgressRewardReply"},
        [22] = {"GetProgressRewardInfoRequest", "GetProgressRewardInfoReply"}
    },
    [241] = {
        [255] = "Activity241Extension",
        [1] = {"GetAct241InfoRequest", "GetAct241InfoReply"},
        [2] = {"Act241BindInviteCodeRequest", "Act241BindInviteCodeReply"},
        [3] = {"Act241GetRewardRequest", "Act241GetRewardReply"}
    },
    [242] = {
        [255] = "Activity242Extension",
        [1] = {"LumberJackGameUpdateOperatorRequest", "LumberJackGameUpdateOperatorReply"},
        [2] = {"LumberJackGameGetNextTreeDataRequest", "LumberJackGameGetNextTreeDataReply"},
        [3] = {"LumberJackGameGetLevelInfoRequest", "LumberJackGameGetLevelInfoReply"},
        [4] = {"LumberJackGameGetLevelAwardRequest", "LumberJackGameGetLevelAwardReply"},
        [5] = {"LumberJackGameGetMatchReportRequest", "LumberJackGameGetMatchReportReply"},
        [6] = {"LumberJackGameGetWinAwardRequest", "LumberJackGameGetWinAwardReply"},
        [7] = {"LumberJackGameGetMatchAwardRequest", "LumberJackGameGetMatchAwardReply"},
        [11] = {"LumberJackGameTreePush"},
        [12] = {"LumberJackGameEndPush"}
    },
    [243] = {
        [255] = "Activity243Extension",
        [1] = {"GetAct243InfoRequest", "GetAct243InfoReply"},
        [2] = {"Act243LotteryRequest", "Act243LotteryReply"}
    },
    [244] = {
        [255] = "Activity244Extension",
        [1] = {"GetAct244InfoRequest", "GetAct244InfoReply"},
        [2] = {"Act244LightPuzzleRequest", "Act244LightPuzzleReply"},
        [3] = {"Act244GetRewardRequest", "Act244GetRewardReply"}
    },
    [245] = {
        [255] = "Activity245Extension",
        [1] = {"Act245DrawRewardRequest", "Act245DrawRewardReply"}
    },
    [246] = {
        [255] = "Activity246Extension",
        [1] = {"GetAct246InfoRequest", "GetAct246InfoReply"},
        [2] = {"GetAct246SceneInfoRequest", "GetAct246SceneInfoReply"},
        [3] = {"CatchAct246InsectRequest", "CatchAct246InsectReply"},
        [4] = {"GetAct246RewardRequest", "GetAct246RewardReply"}
    },
    [247] = {
        [255] = "Activity247Extension",
        [1] = {"GetAct247InfoRequest", "GetAct247InfoReply"}
    },
    [249] = {
        [255] = "Activity249Extension",
        [1] = {"GetAct249InfoRequest", "GetAct249InfoReply"},
        [2] = {"GainAct249LoginRewardRequest", "GainAct249LoginRewardReply"}
    },
    [250] = {
        [255] = "Activity250Extension",
        [1] = {"GetAct250AvoidGameInfoRequest", "GetAct250AvoidGameInfoReply"},
        [2] = {"StartAct250AvoidRequest", "StartAct250AvoidReply"},
        [3] = {"FinishAct250AvoidGameRequest", "FinishAct250AvoidGameReply"},
        [4] = {"GetAct250ScoreRewardRequest", "GetAct250ScoreRewardReply"}
    },
    [251] = {
        [255] = "Activity251Extension",
        [1] = {"GetAct251InfoRequest", "GetAct251InfoReply"},
        [2] = {"GetAct251RewardRequest", "GetAct251RewardReply"},
        [3] = {"Act251SubmitItemRequest", "Act251SubmitItemReply"},
        [4] = {"Act251GetFinishDailyRewardRequest", "Act251GetFinishDailyRewardReply"}
    },
    [252] = {
        [255] = "Activity252Extension",
        [1] = {"GetAct252RewardInfoRequest", "GetAct252RewardInfoReply"},
        [2] = {"StartAct252GameRequest", "StartAct252GameReply"},
        [3] = {"Act252EndlessThrowCakeRequest", "Act252EndlessThrowCakeReply"},
        [4] = {"FinishAct252GameRequest", "FinishAct252GameReply"},
        [5] = {"GetAct252RewardRequest", "GetAct252RewardReply"},
        [6] = {"GetAct252RoleAndElfInfoRequest", "GetAct252RoleAndElfInfoReply"},
        [11] = {"Act252GameEndPush"}
    },
    [254] = {
        [255] = "Activity254Extension",
        [1] = {"GetAct254InfoRequest", "GetAct254InfoReply"},
        [2] = {"Act254LotteryRequest", "Act254LotteryReply"},
        [3] = {"Act254ExchangeMinimumBoxRequest", "Act254ExchangeMinimumBoxReply"},
        [4] = {"Act254LockPoolTypeRequest", "Act254LockPoolTypeReply"},
        [5] = {"Act254WishLotteryItemRequest", "Act254WishLotteryItemReply"},
        [6] = {"Act254ConvertLotteryItemRequest", "Act254ConvertLotteryItemReply"},
        [7] = {"Act254DrawFatigueRewardRequest", "Act254DrawFatigueRewardReply"}
    },
    [255] = {
        [255] = "Activity255Extension",
        [1] = {"GetAct255CarnivalInfoRequest", "GetAct255CarnivalInfoReply"},
        [2] = {"EnterAct255ShowSeatRequest", "EnterAct255ShowSeatReply"},
        [3] = {"LeaveAct255ShowSeatRequest", "LeaveAct255ShowSeatReply"},
        [4] = {"WaitFlowerCarRequest", "WaitFlowerCarReply"},
        [5] = {"ChooseFlowerCarRequest", "ChooseFlowerCarReply"},
        [6] = {"LeaveWaitFlowerCarRequest", "LeaveWaitFlowerCarReply"},
        [7] = {"GetInToFlowerCarRequest", "GetInToFlowerCarReply"},
        [8] = {"LeaveFlowerCarRequest", "LeaveFlowerCarReply"},
        [9] = {"JoinGalaParadeTeamRequest", "JoinGalaParadeTeamReply"},
        [10] = {"LeaveGalaParadeTeamRequest", "LeaveGalaParadeTeamReply"},
        [11] = {"UpdateMyEnergyBallFollowTimeRequest", "UpdateMyEnergyBallFollowTimeReply"},
        [12] = {"ChangeColourGridRequest", "ChangeColourGridReply"},
        [13] = {"GetAct255UserDataRequest", "GetAct255UserDataReply"},
        [14] = {"GetAct255CoinRewardRequest", "GetAct255CoinRewardReply"},
        [15] = {"SendLinkEmojiRequest", "SendLinkEmojiReply"},
        [21] = {"Act255CarnivalInfoChangePush"},
        [22] = {"Act255GalaParadeTeamChangePush"},
        [23] = {"Act255GridChangePush"},
        [24] = {"Act255EnergyBallInfoChangePush"},
        [25] = {"Act255NoticFireWorksBeginTipPush"},
        [26] = {"Act255NoticFlowerCarBeginTipPush"},
        [27] = {"Act255LinkEmojiPush"},
        [28] = {"Act255MainAreaClosePush"},
        [29] = {"Act255RankChangePush"},
        [30] = {"Act255ClearGridPush"}
    },
    [256] = {
        [255] = "Activity256Extension",
        [1] = {"FinishAct256TaskRequest", "FinishAct256TaskReply"},
        [2] = {"GetAct256RewardRequest", "GetAct256RewardReply"},
        [3] = {"GetAct256InfoRequest", "GetAct256InfoReply"}
    },
    [257] = {
        [255] = "Activity257Extension",
        [1] = {"GetAct257InfoRequest", "GetAct257InfoReply"},
        [2] = {"Act257HelpBargainRequest", "Act257HelpBargainReply"},
        [3] = {"GainAct257RewardRequest", "GainAct257RewardReply"}
    },
    [258] = {
        [255] = "Activity258Extension",
        [1] = {"GetAct258DressGameInfoRequest", "GetAct258DressGameInfoReply"},
        [2] = {"StartAct258GameRequest", "StartAct258GameReply"},
        [3] = {"FinishAct258GameRequest", "FinishAct258GameReply"},
        [4] = {"GetAct258ScoreRewardRequest", "GetAct258ScoreRewardReply"}
    },
    [259] = {
        [255] = "Activity259Extension",
        [1] = {"GetAct259InfoRequest", "GetAct259InfoReply"},
        [2] = {"FinishAct259TaskRequest", "FinishAct259TaskReply"},
        [3] = {"GetAct259TaskRewardRequest", "GetAct259TaskRewardReply"}
    },
    [260] = {
        [255] = "Activity260Extension",
        [1] = {"GetActivity260InfoRequest", "GetActivity260InfoReply"},
        [2] = {"CompleteActivity260Request", "CompleteActivity260Reply"}
    },
    [261] = {
        [255] = "Activity261Extension",
        [1] = {"Activity261SelectTeamRequest", "Activity261SelectTeamReply"},
        [2] = {"Activity261GetTeamInfoRequest", "Activity261GetTeamInfoReply"},
        [3] = {"Activity261GetShareRewardRequest", "Activity261GetShareRewardReply"}
    },
    [262] = {
        [255] = "Activity262Extension",
        [1] = {"GetAct262InfoRequest", "GetAct262InfoReply"},
        [2] = {"GainAct262DailyPlayRewardRequest", "GainAct262DailyPlayRewardReply"},
        [3] = {"GainAct262TotalPlayRewardRequest", "GainAct262TotalPlayRewardReply"}
    },
    [263] = {
        [255] = "Activity263Extension",
        [1] = {"GetActivity263InfoRequest", "GetActivity263InfoReply"},
        [2] = {"Activity263SelectTeamRequest", "Activity263SelectTeamReply"},
        [3] = {"Activity263AwardRequest", "Activity263AwardReply"},
        [4] = {"Activity263GetRankInfoRequest", "Activity263GetRankInfoReply"},
        [5] = {"Activity263NpcDialogAwardRequest", "Activity263NpcDialogAwardReply"}
    },
    [264] = {
        [255] = "Activity264Extension",
        [1] = {"GetAct264RewardInfoRequest", "GetAct264RewardInfoReply"}
    },
    [265] = {
        [255] = "Activity265Extension",
        [1] = {"Act265GetInfoRequest", "Act265GetInfoReply"},
        [2] = {"Act265SelectAnswerRequest", "Act265SelectAnswerReply"},
        [3] = {"Act265DrawAchievementRequest", "Act265DrawAchievementReply"},
        [11] = {"Act265AnswerSettlePush"}
    },
    [266] = {
        [255] = "Activity266Extension",
        [1] = {"Act266GameUpdateOperatorRequest", "Act266GameUpdateOperatorReply"},
        [2] = {"Act266RebornRequest", "Act266RebornReply"},
        [3] = {"Act266GetInfoRequest", "Act266GetInfoReply"},
        [4] = {"Act266DrawProcessRewardsRequest", "Act266DrawProcessRewardsReply"},
        [5] = {"Act266GetMatchReportRequest", "Act266GetMatchReportReply"},
        [11] = {"Act266GameOperatorPush"},
        [12] = {"Act266GameEndPush"}
    },
    [267] = {
        [255] = "Activity267Extension",
        [1] = {"GetAct267InfoRequest", "GetAct267InfoReply"},
        [2] = {"Act267DonateRequest", "Act267DonateReply"},
        [3] = {"GainAct267ProgressRewardRequest", "GainAct267ProgressRewardReply"}
    },
    [268] = {
        [255] = "Activity268Extension",
        [1] = {"GetAct268ReportInfoRequest", "GetAct268ReportInfoReply"},
        [2] = {"GetAct268DailyRewardsRequest", "GetAct268DailyRewardsReply"},
        [3] = {"GetAct268AccumulateRewardsRequest", "GetAct268AccumulateRewardsReply"}
    },
    [272] = {
        [255] = "Activity272Extension",
        [1] = {"GetAct272InfoRequest", "GetAct272InfoReply"},
        [2] = {"Act272StartGameRequest", "Act272StartGameReply"},
        [3] = {"Act272EndGameRequest", "Act272EndGameReply"}
    },
    [276] = {
        [255] = "Activity276Extension",
        [1] = {"GetAct276InfoRequest", "GetAct276InfoReply"},
        [2] = {"GetAct276SignRewardRequest", "GetAct276SignRewardReply"},
        [3] = {"GetAct276TaskRewardInfoRequest", "GetAct276TaskRewardInfoReply"}
    },
    [277] = {
        [255] = "Activity277Extension",
        [1] = {"GetAct277InfoRequest", "GetAct277InfoReply"},
        [2] = {"Act277GainRewardRequest", "Act277GainRewardReply"}
    },
    [278] = {
        [255] = "Activity278Extension",
        [1] = {"GetTetrisGameInfoRequest", "GetTetrisGameInfoReply"},
        [2] = {"GetTetrisTargetRewardRequest", "GetTetrisTargetRewardReply"},
        [3] = {"GetTetrisWinCountRewardRequest", "GetTetrisWinCountRewardReply"},
        [4] = {"UpdateTetrisStateRequest", "UpdateTetrisStateReply"},
        [5] = {"SendDebuffRequest", "SendDebuffReply"},
        [6] = {"TetrisChangeSkillRequest", "TetrisChangeSkillReply"},
        [11] = {"TetrisDebuffPush"},
        [12] = {"TetrisGameStatePush"},
        [13] = {"TetrisGameEndPush"}
    },
    [279] = {
        [255] = "Activity279Extension",
        [1] = {"GetAct279InfoRequest", "GetAct279InfoReply"},
        [2] = {"Act279GainRewardRequest", "Act279GainRewardReply"}
    },
    [280] = {
        [255] = "Activity280Extension",
        [1] = {"GetAct280GameInfoRequest", "GetAct280GameInfoReply"},
        [2] = {"AnswerAct280QuestionRequest", "AnswerAct280QuestionReply"},
        [3] = {"GetAct280AccumulateRewardRequest", "GetAct280AccumulateRewardReply"},
        [4] = {"LightAct280LanternRequest", "LightAct280LanternReply"}
    },
    [281] = {
        [255] = "Activity281Extension",
        [1] = {"Act281GetInfoRequest", "Act281GetInfoReply"},
        [2] = {"Act281CollectRequest", "Act281CollectReply"}
    },
    [282] = {
        [255] = "Activity282Extension",
        [1] = {"GetAct282InfoRequest", "GetAct282InfoReply"},
        [2] = {"GetAct282SceneInfoRequest", "GetAct282SceneInfoReply"},
        [3] = {"CatchAct282GhostRequest", "CatchAct282GhostReply"},
        [4] = {"GetAct282RewardRequest", "GetAct282RewardReply"},
        [5] = {"SendAct282TeamInvitaRequest", "SendAct282TeamInvitaReply"},
        [6] = {"AcceptAct282TeamInvitaRequest", "AcceptAct282TeamInvitaReply"},
        [7] = {"Act282ExitTeamRequest", "Act282ExitTeamReply"},
        [8] = {"QueryAct282TeamInfoRequest", "QueryAct282TeamInfoReply"},
        [11] = {"Act282TeamInfoChangePush"}
    },
    [283] = {
        [255] = "Activity283Extension",
        [1] = {"GetAct283InfoRequest", "GetAct283InfoReply"},
        [2] = {"Act283GainRechargeRewardRequest", "Act283GainRechargeRewardReply"},
        [3] = {"Act283GainLoginRewardRequest", "Act283GainLoginRewardReply"}
    },
    [284] = {
        [255] = "Activity284Extension",
        [1] = {"GetAct284RewardsInfoRequest", "GetAct284RewardsInfoReply"},
        [2] = {"GetAct284SignRewardsRequest", "GetAct284SignRewardsReply"},
        [3] = {"GetAct284AccumulateRewardsRequest", "GetAct284AccumulateRewardsReply"}
    },
    [285] = {
        [255] = "Activity285Extension",
        [1] = {"GetAct285InfoRequest", "GetAct285InfoReply"},
        [2] = {"Act285StartGameRequest", "Act285StartGameReply"},
        [3] = {"Act285EndGameRequest", "Act285EndGameReply"}
    },
    [286] = {
        [255] = "Activity286Extension",
        [1] = {"GetAct286InfoRequest", "GetAct286InfoReply"}
    },
    [287] = {
        [255] = "Activity287Extension",
        [1] = {"GetAct287InfRequest", "GetAct287InfoReply"},
        [2] = {"StartAct287ByBoatRequest", "StartAct287ByBoatReplay"},
        [3] = {"GetAct287AccumulateRewardRequest", "GetAct287AccumulateRewardReply"},
        [4] = {"BuyAct287TicketRequest", "BuyAct287TicketReply"}
    },
    [288] = {
        [255] = "Activity288Extension",
        [1] = {"GetActivity288InfoRequest", "GetActivity288InfoReply"},
        [2] = {"GetModelListRequest", "GetModelListReply"},
        [3] = {"RefreshOtherModelsRequest", "RefreshOtherModelsReply"},
        [4] = {"ChangeCanBeModelOptionRequest", "ChangeCanBeModelOptionReply"},
        [5] = {"GetModelClothesInfoRequest", "GetModelClothesInfoReply"},
        [6] = {"SendDesignRequest", "SendDesignReply"},
        [7] = {"GetSentDesignListRequest", "GetSentDesignListReply"},
        [8] = {"GetReceivedDesignListRequest", "GetReceivedDesignListReply"},
        [9] = {"SendThanksLetterRequest", "SendThanksLetterReply"},
        [10] = {"SubmitEntryRequest", "SubmitEntryReply"},
        [11] = {"GetEntryListRequest", "GetEntryListReply"},
        [12] = {"ChoseLikeEntryRequest", "ChoseLikeEntryReply"},
        [13] = {"LikeFriendEntryRequest", "LikeFriendEntryReply"},
        [14] = {"GainLikeRewardRequest", "GainLikeRewardReply"},
        [15] = {"GetAct288RankListRequest", "GetAct288RankListReply"},
        [16] = {"ReadThanksLetterRequest", "ReadThanksLetterReply"},
        [21] = {"GetGainedDesignCountRewardRequest", "GetGainedDesignCountRewardReply"},
        [22] = {"GainDesignCountRewardRequest", "GainDesignCountRewardReply"}
    },
    [289] = {
        [255] = "Activity289Extension",
        [1] = {"FinishAct289TaskRequest", "FinishAct289TaskReply"},
        [2] = {"GetAct289RewardRequest", "GetAct289RewardReply"},
        [3] = {"GetAct289InfoRequest", "GetAct289InfoReply"}
    },
    [290] = {
        [255] = "Activity290Extension",
        [1] = {"GetActivity290DataRequest", "GetActivity290DataReply"},
        [2] = {"GetActivity290PlayCountRewardRequest", "GetActivity290PlayCountRewardReply"},
        [3] = {"GetActivity290DailyRewardRequest", "GetActivity290DailyRewardReply"}
    },
    [291] = {
        [255] = "Activity291Extension",
        [1] = {"GetAct291SceneInfoRequest", "GetAct291SceneInfoReply"},
        [2] = {"PickUpAct291FireWorkRequest", "PickUpAct291FireWorkReply"},
        [3] = {"Act291SetOffFireWorkRequest", "Act291SetOffFireWorkReply"},
        [4] = {"Act291GetMyNPCWishRequest", "Act291GetMyNPCWishReply"},
        [5] = {"Act291WriteWishRequest", "Act291WriteWishReply"},
        [6] = {"Act291GetSceneStarInfoRequest", "Act291GetSceneStarInfoReply"},
        [7] = {"Act291PickUpStarRequest", "Act291PickUpStarReply"},
        [8] = {"GetFurnitureWishRequest", "GetFurnitureWishReply"}
    },
    [292] = {
        [255] = "Activity292Extension",
        [1] = {"Act292GetSceneShowDataRequest", "Act292GetSceneShowDataReply"}
    },
    [293] = {
        [255] = "Activity293Extension",
        [1] = {"GetAct293InfoRequest", "GetAct293InfoReply"},
        [2] = {"Act293FinishTaskRequest", "Act293FinishTaskReply"}
    },
    [294] = {
        [255] = "Activity294Extension",
        [1] = {"GetActivity294DataRequest", "GetActivity294DataReply"},
        [2] = {"GetAct294DailyRewardRequest", "GetAct294DailyRewardReply"},
        [3] = {"GetAct294AccumulateRewardRequest", "GetAct294AccumulateRewardReply"}
    },
    [295] = {
        [255] = "Activity295Extension",
        [1] = {"GetAct295InfoRequest", "GetAct295InfoReply"},
        [2] = {"Act295JoinQueueRequest", "Act295JoinQueueReply"},
        [3] = {"Act295ExitQueueRequest", "Act295ExitQueueReply"},
        [4] = {"Act295GainRewardRequest", "Act295GainRewardReply"}
    },
    [296] = {
        [255] = "Activity296Extension",
        [1] = {"GetAct296InfoRequest", "GetAct296InfoReply"},
        [2] = {"GetAct296GridRewardRequest", "GetAct296GridRewardReply"}
    },
    [297] = {
        [255] = "Activity297Extension",
        [1] = {"GetAct297InfoRequest", "GetAct297InfoReply"},
        [2] = {"Act297FinishDIYRequest", "Act297FinishDIYReply"},
        [3] = {"Act297GetRewardRequest", "Act297GetRewardReply"}
    },
    [298] = {
        [255] = "Activity298Extension",
        [1] = {"GetAct298InfoRequest", "GetAct298InfoReply"},
        [2] = {"GetAct298ProcessRewardRequest", "GetAct298ProcessRewardReply"},
        [3] = {"GetAct298SignRewardRequest", "GetAct298SignRewardReply"}
    },
    [299] = {
        [255] = "Activity299Extension",
        [1] = {"GetAct299InfoRequest", "GetAct299InfoReply"},
        [2] = {"Act299GetRewardRequest", "Act299GetRewardReply"}
    },
    [401] = {
        [255] = "Activity401Extension",
        [1] = {"Act401LeGetDisplayRequest", "Act401LeGetDisplayReply"},
        [2] = {"Act401LeLotteryRequest", "Act401LeLotteryReply"},
        [3] = {"Act401GetLotteryHistoryRequest", "Act401GetLotteryHistoryReply"},
        [4] = {"Act401DrawStoryRewardRequest", "Act401DrawStoryRewardReply"}
    },
    [403] = {
        [255] = "Activity403Extension",
        [1] = {"GetAct403InfoRequest", "GetAct403InfoReply"},
        [2] = {"FinishAct403GameRequest", "FinishAct403GameReply"}
    },
    [404] = {
        [255] = "Activity404Extension",
        [1] = {"GetAct404InfoRequest", "GetAct404InfoReply"},
        [2] = {"StartAct404GameRequest", "StartAct404GameReply"},
        [3] = {"FinishAct404SonLevelRequest", "FinishAct404SonLevelReply"},
        [4] = {"FinishAct404GameRequest", "FinishAct404GameReply"},
        [5] = {"GetAct404TargetRewardRequest", "GetAct404TargetRewardReply"}
    },
    [405] = {
        [255] = "Activity405Extension",
        [1] = {"GetAct405InfoRequest", "GetAct405InfoReply"},
        [2] = {"GetAct405RewardRequest", "GetAct405RewardReply"},
        [3] = {"UseAct405RandomBoxRequest", "UseAct405RandomBoxReply"},
        [4] = {"GetAct405ReceiveInfoRequest", "GetAct405ReceiveInfoReply"},
        [5] = {"ModifyAct405ReceiveLimitRequest", "ModifyAct405ReceiveLimitReply"},
        [6] = {"Act405GetLevelRewardRequest", "Act405GetLevelRewardReply"}
    },
    [406] = {
        [255] = "Activity406Extension",
        [1] = {"GetAct406InfoRequest", "GetAct406InfoReply"},
        [2] = {"GainAct406DailyPlayRewardRequest", "GainAct406DailyPlayRewardReply"},
        [3] = {"GainAct406TotalPlayRewardRequest", "GainAct406TotalPlayRewardReply"},
        [4] = {"GetProvinceRankingListRequest", "GetProvinceRankingListReply"},
        [5] = {"FinishNewbieGameTeachingRequest", "FinishNewbieGameTeachingReply"},
        [6] = {"GainNewbieGameTeachingFinalRewardRequest", "GainNewbieGameTeachingFinalRewardReply"}
    },
    [407] = {
        [255] = "Activity407Extension",
        [1] = {"GetAct407InfoRequest", "GetAct407InfoReply"},
        [2] = {"GetAct407DailyRewardRequest", "GetAct407DailyRewardReply"}
    },
    [408] = {
        [255] = "Activity408Extension",
        [1] = {"Act408GetInfoRequest", "Act408GetInfoReply"},
        [2] = {"Act408StartGameRequest", "Act408StartGameReply"},
        [3] = {"Act408EndGameRequest", "Act408EndGameReply"},
        [4] = {"Act408DrawLevelScoreRewardRequest", "Act408DrawLevelScoreRewardReply"}
    },
    [409] = {
        [255] = "Activity409Extension",
        [1] = {"GetAct409InfRequest", "GetAct409InfoReply"},
        [2] = {"StartAct409LotteryRequest", "StartAct409LotteryReplay"}
    },
    [410] = {
        [255] = "Activity410Extension",
        [1] = {"GetAct410InfoRequest", "GetAct410InfoReply"},
        [2] = {"GetAct410GridRewardRequest", "GetAct410GridRewardReply"}
    },
    [411] = {
        [255] = "Activity411Extension",
        [1] = {"GetAct411GameInfoRequest", "GetAct411GameInfoReply"},
        [2] = {"GetAct411TargetRewardRequest", "GetAct411TargetRewardReply"},
        [3] = {"GetAct411WinCountRewardRequest", "GetAct411WinCountRewardReply"},
        [4] = {"UpdateAct411GameStateRequest", "UpdateAct411GameStateReply"},
        [5] = {"SendAct411DebuffRequest", "SendAct411DebuffReply"},
        [6] = {"ChangeAct411SkillRequest", "ChangeAct411SkillReply"},
        [11] = {"Act411DebuffPush"},
        [12] = {"Act411GameStatePush"},
        [13] = {"Act411GameEndPush"}
    },
    [412] = {
        [255] = "Activity412Extension",
        [1] = {"GetAct412InfoRequest", "GetAct412InfoReply"},
        [2] = {"FinishAct412GameRequest", "FinishAct412GameReply"},
        [3] = {"GetAct412TargetRewardRequest", "GetAct412TargetRewardReply"}
    },
    [414] = {
        [255] = "Activity414Extension",
        [1] = {"GetAct414InfoRequest", "GetAct414InfoReply"},
        [2] = {"Act414SubmitItemRequest", "Act414SubmitItemReply"}
    },
    [415] = {
        [255] = "Activity415Extension",
        [1] = {"GetAct415InfoRequest", "GetAct415InfoReply"},
        [2] = {"GainAct415TotalRewardRequest", "GainAct415TotalRewardReply"}
    },
    [416] = {
        [255] = "Activity416Extension",
        [1] = {"GetAct416InfoRequest", "GetAct416InfoReply"},
        [2] = {"GainAct416GlobalTargetRewardRequest", "GainAct416GlobalTargetRewardReply"}
    },
    [417] = {
        [255] = "Activity417Extension",
        [1] = {"GetAct417InfoRequest", "GetAct417InfoReply"},
        [2] = {"StartAct417GameRequest", "StartAct417GameReply"},
        [3] = {"FinishAct417GameRequest", "FinishAct417GameReply"},
        [4] = {"GetAct417TargetRewardRequest", "GetAct417TargetRewardReply"}
    },
    [418] = {
        [255] = "Activity418Extension",
        [1] = {"GetAct418InfoRequest", "GetAct418InfoReply"},
        [2] = {"JoinAct418SceneRequest", "JoinAct418SceneReply"},
        [3] = {"PreparePickUpAct418BoxRequest", "PreparePickUpAct418BoxReply"},
        [4] = {"PickUpAct418BoxRequest", "PickUpAct418BoxReply"},
        [11] = {"AppearAct418BoxPush"},
        [12] = {"PickUpAct418BoxPush"}
    },
    [419] = {
        [255] = "Activity419Extension",
        [1] = {"SeaDanceGetInfoRequest", "SeaDanceGetInfoReply"},
        [2] = {"SeaDanceGetRewardRequest", "SeaDanceGetRewardReply"},
        [3] = {"SeaDanceGetTimesRewardRequest", "SeaDanceGetTimesRewardReply"},
        [4] = {"SeaDanceGetHeartRewardRequest", "SeaDanceGetHeartRewardReply"}
    },
    [420] = {
        [255] = "Activity420Extension",
        [1] = {"GetAct420InfoRequest", "GetAct420InfoReply"},
        [2] = {"StartAct420GameRequest", "StartAct420GameReply"},
        [3] = {"FinishAct420GameRequest", "FinishAct420GameReply"},
        [4] = {"GetAct420TargetRewardRequest", "GetAct420TargetRewardReply"}
    },
    [421] = {
        [255] = "Activity421Extension",
        [1] = {"GetAct421InfoRequest", "GetAct421InfoReply"},
        [2] = {"Act421DonateRequest", "Act421DonateReply"},
        [3] = {"Act421GetRewardRequest", "Act421GetRewardReply"}
    },
    [423] = {
        [255] = "Activity423Extension",
        [1] = {"GetActivity423InfoRequest", "GetActivity423InfoReply"},
        [2] = {"GetAct423LevelRewardRequest", "GetAct423LevelRewardReply"},
        [11] = {"PetCircusSettlePush"}
    },
    [424] = {
        [255] = "Activity424Extension",
        [1] = {"GetActivity424InfoRequest", "GetActivity424InfoReply"},
        [2] = {"GetAct424LevelRewardRequest", "GetAct424LevelRewardReply"},
        [11] = {"MergeWatermelonSettlePush"}
    },
    [425] = {
        [255] = "Activity425Extension",
        [1] = {"GetAct425GameInfoRequest", "GetAct425GameInfoReply"},
        [2] = {"StartAct425RollRequest", "StartAct425RollReply"},
        [3] = {"GetAct425SpecialCellRewardRequest", "GetAct425SpecialCellRewardReply"},
        [4] = {"GetAct425TargetTimesRewardRequest", "GetAct425TargetTimesRewardReply"}
    },
    [426] = {
        [255] = "Activity426Extension",
        [1] = {"GetAct426InfoRequest", "GetAct426InfoReply"},
        [2] = {"BuyAct426TicketsRequest", "BuyAct426TicketsReply"},
        [3] = {"StartAct426LotteryRequest", "StartAct426LotteryReply"},
        [4] = {"Act426GetGraduateRewardRequest", "Act426GetGraduateRewardReply"},
        [5] = {"Act426GetTargetRewardRequest", "Act426GetTargetRewardReply"}
    },
    [427] = {
        [255] = "Activity427Extension",
        [1] = {"GetAct427InfoRequest", "GetAct427InfoReply"},
        [2] = {"SetGameCompetitorsRequest", "SetGameCompetitorsReply"},
        [3] = {"IncentivePetRequest", "IncentivePetReply"},
        [4] = {"CancelIncentivePetRequest", "CancelIncentivePetReply"},
        [5] = {"GetCompeteRecordRequest", "GetCompeteRecordReply"},
        [6] = {"EndPetBattleRequest", "EndPetBattleReply"},
        [11] = {"PetSpTrainingRequest", "PetSpTrainingReply"},
        [12] = {"ResetPetSpTrainingRequest", "ResetPetSpTrainingReply"}
    },
    [428] = {
        [255] = "Activity428Extension",
        [1] = {"Act428GetInfoRequest", "Act428GetInfoReply"},
        [2] = {"Act428InteractRequest", "Act428InteractReply"},
        [3] = {"Act428CancelInteractRequest", "Act428CancelInteractReply"},
        [11] = {"Act428GameEndPush"}
    },
    [429] = {
        [255] = "Activity429Extension",
        [1] = {"GetAct429InfoRequest", "GetAct429InfoReply"},
        [2] = {"GenerateAct429ItemRequest", "GenerateAct429ItemReply"},
        [3] = {"ComposeAct429ItemRequest", "ComposeAct429ItemReply"},
        [4] = {"SubmitAct429OrderRequest", "SubmitAct429OrderReply"},
        [5] = {"GainAct429RewardRequest", "GainAct429RewardReply"}
    },
    [430] = {
        [255] = "Activity430Extension",
        [1] = {"GetAct430InfoRequest", "GetAct430InfoReply"},
        [2] = {"GetAct430RewardRequest", "GetAct430RewardReply"}
    },
    [431] = {
        [255] = "Activity431Extension",
        [1] = {"GetAct431InfoRequest", "GetAct431InfoReply"},
        [2] = {"AddAct431ExpRequest", "AddAct431ExpReply"},
        [3] = {"GetAct431RewardRequest", "GetAct431RewardReply"},
        [4] = {"GetAct431MirageOnceRewardRequest", "GetAct431MirageOnceRewardReply"}
    },
    [432] = {
        [255] = "Activity432Extension",
        [1] = {"GetAct432InfoRequest", "GetAct432InfoReply"},
        [2] = {"GainAct432TargetRewardRequest", "GainAct432TargetRewardReply"}
    },
    [433] = {
        [255] = "Activity433Extension",
        [1] = {"Act433GetInfoRequest", "Act433GetInfoReply"},
        [2] = {"Act433GetOnCarRequest", "Act433GetOnCarReply"},
        [3] = {"Act433GetOffCarRequest", "Act433GetOffCarReply"},
        [4] = {"Act433AwardRequest", "Act433AwardReply"}
    },
    [434] = {
        [255] = "Activity434Extension",
        [1] = {"GetAct434SignInfoRequest", "GetAct434SignInfoReply"},
        [2] = {"GetAct434RewardsRequest", "GetAct434RewardsReply"},
        [3] = {"Act434LateSignRequest", "Act434LateSignReply"}
    },
    [435] = {
        [255] = "Activity435Extension",
        [1] = {"GetAct435InfoRequest", "GetAct435InfoReply"},
        [2] = {"OpenAct435GridRequest", "OpenAct43GridReply"},
        [3] = {"GainAct435RewardRequest", "GainAct435RewardReply"}
    },
    [436] = {
        [255] = "Activity436Extension",
        [1] = {"GetAct436InfoRequest", "GetAct436InfoReply"},
        [2] = {"FinishChallengeLevelStoryRequest", "FinishChallengeLevelStoryReply"},
        [3] = {"StartChallengeGameRequest", "StartChallengeGameReply"},
        [4] = {"EndChallengeGameRequest", "EndChallengeGameReply"},
        [5] = {"StartEndlessGameRequest", "StartEndlessGameReply"},
        [6] = {"EndEndlessGameRequest", "EndEndlessGameReply"},
        [7] = {"EndlessGameResurrectRequest", "EndlessGameResurrectReply"},
        [8] = {"GainAct436RewardRequest", "GainAct436RewardReply"},
        [9] = {"SelectMatchGameSkillRequest", "SelectMatchGameSkillReply"},
        [11] = {"MatchGameSettlePush"}
    },
    [437] = {
        [255] = "Activity437Extension",
        [1] = {"GetActivity437InfoRequest", "GetActivity437InfoReply"},
        [2] = {"GetActivity437RewardRequest", "GetActivity437RewardReply"},
        [3] = {"GetActivity437DailyRewardRequest", "GetActivity437DailyRewardReply"}
    },
    [438] = {
        [255] = "Activity438Extension",
        [1] = {"GetAct438InfoRequest", "GetAct438InfoReply"},
        [2] = {"Act438StartGameRequest", "Act438StartGameReply"},
        [3] = {"Act438EndGameRequest", "Act438EndGameReply"},
        [4] = {"GetAct438DailyRewardRequest", "GetAct438DailyRewardReply"}
    },
    [439] = {
        [255] = "Activity439Extension",
        [1] = {"GetAct439InfoRequest", "GetAct439InfoReply"},
        [2] = {"BidLandlordRequest", "BidLandlordReply"},
        [3] = {"PlayCardRequest", "PlayCardReply"},
        [4] = {"HostingRequest", "HostingReply"},
        [5] = {"GainAct439PlayCountRewardRequest", "GainAct439PlayCountRewardReply"},
        [6] = {"GainAct439DailyRewardRequest", "GainAct439DailyRewardReply"},
        [7] = {"StartDouDiZhuSingleGameRequest", "StartDouDiZhuSingleGameReply"},
        [8] = {"EndDouDiZhuSingleGameRequest", "EndDouDiZhuSingleGameReply"},
        [9] = {"InteractRequest", "InteractReply"},
        [14] = {"DouDizhuSettlePush"},
        [15] = {"InteractPush"}
    },
    [440] = {
        [255] = "Activity440Extension",
        [1] = {"GetAct440InfoRequest", "GetAct440InfoReply"},
        [2] = {"Act440SpecialEventRequest", "Act440SpecialEventReply"},
        [3] = {"Act440HeartAndMonsterMoveRequest", "Act440HeartAndMonsterMoveReply"},
        [4] = {"Act440GetTargetRewardRequest", "Act440GetTargetRewardReply"},
        [11] = {"Act440EndPush"},
        [12] = {"Act440SonLevelEndPush"},
        [13] = {"Act440MonsterMovePush"}
    },
    [441] = {
        [255] = "Activity441Extension",
        [1] = {"GetAct441InfoRequest", "GetAct441InfoReply"},
        [2] = {"Act441StartGameRequest", "Act441StartGameReply"},
        [3] = {"Act441EndGameRequest", "Act441EndGameReply"}
    },
    [442] = {
        [255] = "Activity442Extension",
        [1] = {"GetActivity442InfoRequest", "GetActivity442InfoReply"},
        [2] = {"Act442GetAllCoinRequest", "Act442GetAllCoinReply"},
        [3] = {"Act442UpgradeShopRequest", "Act442UpgradeShopReply"}
    },
    [444] = {
        [255] = "Activity444Extension",
        [1] = {"GetActivity444InfoRequest", "GetActivity444InfoReply"},
        [2] = {"Act444EatDinnerRequest", "Act444EatDinnerReply"}
    },
    [445] = {
        [255] = "Activity445Extension",
        [1] = {"GetActivity445InfoRequest", "GetActivity445InfoReply"},
        [2] = {"Act445GetRewardRequest", "Act445GetRewardReply"},
        [4] = {"Act445SignUpShowRequest", "Act445SignUpShowReply"}
    },
    [446] = {
        [255] = "Activity446Extension",
        [1] = {"GetAct446InfoRequest", "GetAct446InfoReply"},
        [2] = {"GainAct446DailyRewardRequest", "GainAct446DailyRewardReply"}
    },
    [447] = {
        [255] = "Activity447Extension",
        [1] = {"GetAct447InfoRequest", "GetAct447InfoReply"},
        [2] = {"GetAct447RewardRequest", "GetAct447RewardReply"}
    },
    [448] = {
        [255] = "Activity448Extension",
        [1] = {"GetAct448InfoRequest", "GetAct448InfoReply"},
        [2] = {"Act448SpecialEventRequest", "Act448SpecialEventReply"},
        [3] = {"Act448ChangeSkillCardRequest", "Act448ChangeSkillCardReply"},
        [4] = {"Act448GetTargetRewardRequest", "Act448GetTargetRewardReply"},
        [5] = {"Act448GetDailyRewardRequest", "Act448GetDailyRewardReply"},
        [6] = {"Act448ForgetDuringRecordRequest", "Act448ForgetDuringRecordReply"},
        [7] = {"Act448GetPassAllRewardRequest", "Act448GetPassAllRewardReply"},
        [8] = {"Act448GetSummarizeInfoRequest", "Act448GetSummarizeInfoReply"},
        [9] = {"Act448GetOnePlotRewardRequest", "Act448GetOnePlotRewardReply"},
        [11] = {"Act448EndPush"}
    },
    [449] = {
        [255] = "Activity449Extension",
        [1] = {"GetActivity449InfoRequest", "GetActivity449InfoReply"},
        [2] = {"Act449GetRewardRequest", "Act449GetRewardReply"}
    },
    [451] = {
        [255] = "Activity451Extension",
        [1] = {"GetAct451InfoRequest", "GetAct451InfoReply"},
        [2] = {"GainAct451FirstRewardRequest", "GainAct451FirstRewardReply"},
        [3] = {"GainAct451DailyRewardRequest", "GainAct451DailyRewardReply"},
        [4] = {"GainAct451ModuleRewardRequest", "GainAct451ModuleRewardReply"}
    },
    [452] = {
        [255] = "Activity452Extension",
        [1] = {"GetActivity452InfoRequest", "GetActivity452InfoReply"},
        [2] = {"Act452GetRewardRequest", "Act452GetRewardReply"}
    },
    [454] = {
        [255] = "Activity454Extension",
        [1] = {"GetAct454InfoRequest", "GetAct454InfoReply"},
        [2] = {"SubmitAct454ItemRequest", "SubmitAct454ItemReply"}
    },
    [455] = {
        [255] = "Activity455Extension",
        [1] = {"GetAct455InfoRequest", "GetAct455InfoReply"},
        [2] = {"Act455GetDailyRewardRequest", "Act455GetDailyRewardReply"},
        [3] = {"Act455GetRewardRequest", "Act455GetRewardReply"}
    },
    [456] = {
        [255] = "Activity456Extension",
        [1] = {"GetAct456InfoRequest", "GetAct456InfoReply"},
        [2] = {"Act456GetSingleLevelRewardRequest", "Act456GetSingleLevelRewardReply"},
        [3] = {"Act456GetRewardRequest", "Act456GetRewardReply"},
        [4] = {"PacmanMoveRequest", "PacmanMoveReply"},
        [5] = {"PacmanSwallowRequest", "PacmanSwallowReply"},
        [6] = {"PacmanChangeRoomRequest", "PacmanChangeRoomReply"},
        [11] = {"PacmanMapInfoPush"},
        [12] = {"PacmanSettlePush"}
    },
    [457] = {
        [255] = "Activity457Extension",
        [1] = {"GetAct457InfoRequest", "GetAct457InfoReply"},
        [2] = {"Act457BuyGoodsRequest", "Act457BuyGoodsReply"}
    },
    [458] = {
        [255] = "Activity458Extension",
        [1] = {"GetAct458InfoRequest", "GetAct458InfoReply"},
        [2] = {"OpenAct458GridRequest", "OpenAct458GridReply"},
        [3] = {"GetAct458GraduateRewardRequest", "GetAct458GraduateRewardReply"},
        [4] = {"MarkAct458GridRequest", "MarkAct458GridReply"},
        [5] = {"BuyAct458ItemRequest", "BuyAct458ItemReply"}
    },
    [461] = {
        [255] = "Activity461Extension",
        [1] = {"GetAct461InfoRequest", "GetAct461InfoReply"},
        [2] = {"Act461LotteryRequest", "Act461LotteryReply"},
        [3] = {"GetAct461LotteryRecordRequest", "GetAct461LotteryRecordReply"},
        [4] = {"Act461ConvertLotteryItemRequest", "Act461ConvertLotteryItemReply"},
        [5] = {"Act461DrawFatigueRewardRequest", "Act461DrawFatigueRewardReply"},
        [6] = {"GetAct461maxTimesLeftRewardRequest", "GetAct461maxTimesLeftRewardReply"},
        [7] = {"GetAct461GraduateRewardRequest", "GetAct461GraduateRewardReply"}
    },
    [462] = {
        [255] = "Activity462Extension",
        [1] = {"GetFlyingChessInfoRequest", "GetFlyingChessInfoReply"},
        [2] = {"GetFlyingChessTargetRewardRequest", "GetFlyingChessTargetRewardReply"},
        [3] = {"FlyingChessStartDiceRequest", "FlyingChessStartDiceReply"},
        [4] = {"FlyingChessSelectMoveChessRequest", "FlyingChessSelectMoveChessReply"},
        [5] = {"FlyingChessChangeAutoStateRequest", "FlyingChessChangeAutoStateReply"},
        [6] = {"FlyingChessClientMoveEndRequest", "FlyingChessClientMoveEndReply"},
        [7] = {"FlyingChessChangeAppearanceRequest", "FlyingChessChangeAppearanceReply"},
        [8] = {"GetFlyingChessDailyRewardRequest", "GetFlyingChessDailyRewardReply"},
        [9] = {"Click462ClientEventRequest", "Click462ClientEventReply"},
        [11] = {"FlyingChessEndPush"}
    },
    [463] = {
        [255] = "Activity463Extension",
        [1] = {"GetAct463InfoRequest", "GetAct463InfoReply"},
        [2] = {"GainAct463PlayCountRewardRequest", "GainAct463PlayCountRewardReply"},
        [3] = {"GainAct463DailyRewardRequest", "GainAct463DailyRewardReply"},
        [11] = {"LiarsBarPlayCardRequest", "LiarsBarPlayCardReply"},
        [12] = {"LiarsBarQuestionRequest", "LiarsBarQuestionReply"},
        [13] = {"LiarsBarPlayRussiaRouletteRequest", "LiarsBarPlayRussiaRouletteReply"},
        [14] = {"LiarsBarEarlySettleRequest", "LiarsBarEarlySettleReply"},
        [15] = {"LiarsBarSettlePush"}
    },
    [464] = {
        [255] = "Activity464Extension",
        [1] = {"Get464FirstRechargeInfoRequest", "Get464FirstRechargeInfoReply"},
        [2] = {"Get464FirstRechargeRewardsRequest", "Get464FirstRechargeRewardsReply"}
    },
    [465] = {
        [255] = "Activity465Extension",
        [1] = {"GetAct465InfoRequest", "GetAct465InfoReply"},
        [2] = {"GetAct465RewardRequest", "GetAct465RewardReply"}
    },
    [466] = {
        [255] = "Activity466Extension",
        [1] = {"GetAct466InfoRequest", "GetAct466InfoReply"},
        [2] = {"GainAct466ProgressRewardRequest", "GainAct466ProgressRewardReply"},
        [3] = {"Act466LeaveMessageRequest", "Act466LeaveMessageReply"}
    },
    [467] = {
        [255] = "Activity467Extension",
        [1] = {"GetAct467InfoRequest", "GetAct467InfoReply"},
        [2] = {"OpenAct467TreasureRequest", "OpenAct467TreasureReply"},
        [3] = {"GainAct467TreasureRewardRequest", "GainAct467TreasureRewardReply"}
    },
    [468] = {
        [255] = "Activity468Extension",
        [1] = {"GetAct468InfoRequest", "GetAct468InfoReply"},
        [2] = {"GainAct468RewardRequest", "GainAct468RewardReply"}
    },
    [469] = {
        [255] = "Activity469Extension",
        [1] = {"GetAct469InfoRequest", "GetAct469InfoReply"},
        [2] = {"GainAct469RewardRequest", "GainAct469RewardReply"}
    },
    [471] = {
        [255] = "Activity471Extension",
        [1] = {"JoinCelebPartyQueueRequest", "JoinCelebPartyQueueReply"},
        [2] = {"LeaveCelebPartyQueueRequest", "LeaveCelebPartyQueueReply"},
        [3] = {"ChangeCelebPartyStateRequest", "ChangeCelebPartyStateReply"},
        [4] = {"GetAct471InfoRequest", "GetAct471InfoReply"},
        [5] = {"Act471DrawDailyRewardRequest", "Act471DrawDailyRewardReply"},
        [6] = {"Act471DrawTotalJoinRewardRequest", "Act471DrawTotalJoinRewardReply"}
    },
    [10001] = {
        [255] = "WishExtension",
        [1] = {"PublishWishRequest", "PublishWishReply"},
        [2] = {"CopyWishRequest", "CopyWishReply"},
        [3] = {"BlessWishRequest", "BlessWishReply"},
        [4] = {"SearchWishRequest", "SearchWishReply"},
        [5] = {"RecommendWishRequest", "RecommendWishReply"},
        [6] = {"GetLuckyRewardsRequest", "GetLuckyRewardsReply"},
        [7] = {"GetWishInfoRequest", "GetWishInfoReply"},
        [8] = {"DeleteWishRequest", "DeleteWishReply"}
    },
    [20001] = {
        [255] = "TinyModuleExtension",
        [1] = {"GetNewspaperInfoRequest", "GetNewspaperInfoReply"},
        [2] = {"GetNewspaperRewardRequest", "GetNewspaperRewardReply"}
    },
    [23333] = {
        [255] = "GMExtension",
        [1] = {"ModifyItemRequest", "ModifyItemReply"},
        [2] = {"ClearTaskStepRequest", "ClearTaskStepReply"},
        [3] = {"ClearBackpackAllItemsRequest", "ClearBackpackAllItemsReply"},
        [4] = {"ForceStartTaskRequest", "ForceStartTaskReply"},
        [5] = {"ForceLogoutRequest", "ForceLogoutReply", ["block"]=true},
        [6] = {"AddMailRequest", "AddMailReply"},
        [7] = {"AddFavorabilityRequest", "AddFavorabilityReply"},
        [8] = {"RepairTaskRequest", "RepairTaskReply"},
        [9] = {"AddWorkShopLevelRequest", "AddWorkShopLevelReply"},
        [10] = {"RefreshDailyTaskAndEventRequest", "RefreshDailyTaskAndEventReply"},
        [11] = {"RepairArchiveRequest", "RepairArchiveReply"},
        [12] = {"TestSystemMarqueeMessageRequest", "TestSystemMarqueeMessageReply"},
        [13] = {"RepairHouseShowAreaRequest", "RepairHouseShowAreaReply"},
        [14] = {"UnlockAllAreaRequest", "UnlockAllAreaReply"},
        [15] = {"RechargeCallbackRequest", "RechargeCallbackReply"},
        [16] = {"RechargeCallbackAllRequest", "RechargeCallbackAllReply"},
        [17] = {"AddFriendshipCountRequest", "AddFriendshipCountReply"},
        [18] = {"TravellerLeaveMessageRequest", "TravellerLeaveMessageReply"},
        [19] = {"TravellerMessageStateRequest", "TravellerMessageStateReply"},
        [20] = {"ClearFriendRelativeWishLimitRequest", "ClearFriendRelativeWishLimitReply"},
        [21] = {"GetBuffRequest", "GetBuffReply"},
        [22] = {"AddBuffRequest", "AddBuffReply"},
        [23] = {"ResetRegisterTimeRequest", "ResetRegisterTimeReply"},
        [24] = {"DreamPalaceEntryRequest", "DreamPalaceEntryReply"},
        [25] = {"ClearAllMomentMessageBoardRequest", "ClearAllMomentMessageBoardReply"},
        [26] = {"ClearAllMomentRequest", "ClearAllMomentReply"},
        [27] = {"VipResetRequest", "VipResetReply"},
        [28] = {"DissoluteAllCouncilRequest", "DissoluteAllCouncilReply"},
        [29] = {"FixProblemFurnitureRequest", "FixProblemFurnitureReply"},
        [30] = {"AddCouncilActivityRequest", "AddCouncilActivityReply"},
        [31] = {"CompleteFriendshipCheckLevelRequest", "CompleteFriendshipCheckLevelReply"},
        [32] = {"AddElfExpRequest", "AddElfExpReply"},
        [33] = {"ClearAct125DataRequest", "ClearAct125DataReply"},
        [34] = {"StartElfFightRequest", "StartElfFightReply"},
        [35] = {"SetIslandBuildInfoRequest", "SetIslandBuildInfoReply"},
        [36] = {"StartOrEndFoggyForestRequest", "StartOrEndFoggyForestReply"},
        [37] = {"ResetFishCountRequest", "ResetFishCountReply"},
        [38] = {"ResetTradingCountRequest", "ResetTradingCountReply"},
        [39] = {"ResetOrderCountRequest", "ResetOrderCountReply"},
        [40] = {"ClearElfHatchCdRequest", "ClearElfHatchCdReply"},
        [41] = {"FinishTaskRequest", "FinishTaskReply"},
        [42] = {"ResetAchievementRequest", "ResetAchievementReply"},
        [43] = {"GeneratePurikuraRequest", "GeneratePurikuraReply"},
        [44] = {"ResetLotteryEggNumRequest", "ResetLotteryEggNumReply"},
        [45] = {"AddAobiLifeExpRuest", "AddAobiLifeExpReply"},
        [46] = {"ModifyAchievementPointRequest", "ModifyAchievementPointReply"},
        [47] = {"ResetCouncilExitCdRequest", "ResetCouncilExitCdReply"},
        [48] = {"ResetParttimeCountRequest", "ResetParttimeCountReply"},
        [49] = {"PassFriendRelativeWishRequest", "PassFriendRelativeWishReply"},
        [50] = {"SendCouncilSystemMsgRequest", "SendCouncilSystemMsgReply"},
        [51] = {"OpenBossActivityRequest", "OpenBossActivityReply"},
        [52] = {"OpenCouncilDefendRequest", "OpenCouncilDefendReply"},
        [53] = {"SecondPalaceModifySavePointRequest", "SecondPalaceModifySavePointReply"},
        [54] = {"CouncilDefendLaunchSkillRequest", "CouncilDefendLaunchSkillReply"},
        [55] = {"SecondPalaceGMJoinMemberRequest", "SecondPalaceGMJoinMemberReply"},
        [56] = {"SecondPalaceGMDeleteMemberRequest", "SecondPalaceGMDeleteMemberReply"},
        [57] = {"MiaoHouseGMAddGridRequest", "MiaoHouseGMAddGridReply"},
        [58] = {"MiaoHouseGMEndGameRequest", "MiaoHouseGMEndGameReply"},
        [59] = {"MiaoHouseGMAddGridToPoolRequest", "MiaoHouseGMAddGridToPoolReply"},
        [60] = {"ResetAct213RedPaperRainRequest", "ResetAct213RedPaperRainReply"},
        [61] = {"RandomAct213RedPaperRainBatchRequest", "RandomAct213RedPaperRainBatchReply"},
        [62] = {"ClearUserRequest", "ClearUserReply"},
        [63] = {"FrameSyncStartMatchRequest", "FrameSyncStartMatchReply"},
        [64] = {"FrameSyncStartGamePush"},
        [65] = {"ResetAct217Request", "ResetAct217Reply"},
        [66] = {"EndMusicGameRequest", "EndMusicGameReply"},
        [67] = {"ResetBackflowRequest", "ResetBackflowReply"},
        [68] = {"ChangeAct238LightIdRequest", "ChangeAct238LightIdReply"},
        [69] = {"SetAct242MatchScoreRequest", "SetAct242MatchScoreReply"},
        [70] = {"Act243AddLotteryCountRequest", "Act243AddLotteryCountReply"},
        [71] = {"ThirdPalaceGMFinishGameRequest", "ThirdPalaceGMFinishGameReply"},
        [72] = {"ThirdPalaceGMEnterNextLayerRequest", "ThirdPalaceGMEnterNextLayerReply"},
        [73] = {"ResetGroceryRefreshTimeRequest", "ResetGroceryRefreshTimeReply"},
        [74] = {"Act255AddEnergyBallScoreRequest", "Act255AddEnergyBallScoreReply"},
        [75] = {"Act254SetPoolLotteryItemCountRequest", "Act254SetPoolLotteryItemCountReply"},
        [76] = {"Act254GMLotteryRequest", "Act254GMLotteryReply"},
        [77] = {"SceneRobotRequest", "SceneRobotReply"},
        [78] = {"ElfChangeStatusAndJoyRequest", "ElfChangeStatusAndJoyReply"},
        [79] = {"ChangeAct282TeamScoreRequest", "ChangeAct282TeamScoreReply"},
        [80] = {"ChangeAct282MyScoreRequest", "ChangeAct282MyScoreReply"},
        [81] = {"DailyResetAct288UserDataRequest", "DailyResetAct288UserDataReply"},
        [82] = {"CreateBackflow3TestDataRequest", "CreateBackflow3TestDataReply"},
        [83] = {"InsertEcoparkArchiveRequest", "InsertEcoparkArchiveReply"},
        [84] = {"DouyinGiftRechargeRequest", "DouyinGiftRechargeReply"},
        [85] = {"Act161GMLotteryRequest", "Act161GMLotteryReply"},
        [86] = {"Act405GMLotteryRequest", "Act405GMLotteryReply"},
        [87] = {"SeaSceneOpenTaskEventRequest", "SeaSceneOpenTaskEventReply"},
        [88] = {"FifthPalaceSelectMapRequest", "FifthPalaceSelectMapReply"},
        [89] = {"FifthPalaceTestMapRequest", "FifthPalaceTestMapReply"},
        [90] = {"GMChangeActivityDataRequest", "GMChangeActivityDataReply"},
        [91] = {"GMCopyNPCDecorationRequest", "GMCopyNPCDecorationReply"},
        [92] = {"GMAct461AllRewardCheckRequest", "GMAct461AllRewardCheckReply"}
    }
}

setglobal("setting_proto", setting_proto)
return setting_proto