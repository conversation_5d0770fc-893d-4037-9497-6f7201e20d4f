module(..., package.seeall)

local TaskUtil = {}

function TaskUtil.replaceAllRed(text)
    return string.gsub(text, "&(.-)&", '<color="#ff7e7e">%1</color>')
end

function TaskUtil.removeAllRed(text)
    return string.gsub(text, "&(.-)&", "%1")
end
--需要下划线，能点击弹出Tip的任务目标物品
function TaskUtil.stepTargetDefineItem(targetType, targetData)
    if targetType == GameEnum.TaskStepTargetTypeEnum.FindItem then
        return targetData.id
    end
    if targetType == GameEnum.TaskStepTargetTypeEnum.OwnItem then
        return targetData.id
    end
    if targetType == GameEnum.TaskStepTargetTypeEnum.UseItem then
        return targetData.item
    end
    if targetType == GameEnum.TaskStepTargetTypeEnum.PresentItem then
        return targetData.item
    end
    return false
end

function TaskUtil.parseSigns(text)
    text = string.gsub(text, "{r}", "\r\r\r") --这个可能很多，会影响性能，在这里一次处理完
    local need = true
    while need do
        need, text = TaskUtil._parseSignOnce(text)
    end
    text = TaskUtil.replaceAllRed(text)
    return text
end

function TaskUtil._parseSignOnce(text)
    local iBegin = string.find(text, "{")
    local iEnd = string.find(text, "}")
    if not (iBegin and iEnd) then
        return false, text
    end
    local piece1 = string.sub(text, 1, iBegin - 1)
    local piece2 = string.sub(text, iEnd + 1, -1)
    local pieceMiddle = ""
    local iMiddle = string.find(text, ":", iBegin, true)
    local sign = nil
    local param = ""
    if iMiddle and iMiddle < iEnd then
        sign = string.sub(text, iBegin + 1, iMiddle - 1)
        param = string.sub(text, iMiddle + 1, iEnd - 1)
    else
        sign = string.sub(text, iBegin + 1, iEnd - 1)
    end
    if sign == "player" or sign == "username" or sign == "rolename" then
        -- elseif sign == "sex" then
        --     local sharp = string.find(param, "#")
        --     local male = string.sub(param, 1, sharp - 1)
        --     local female = string.sub(param, sharp + 1, -1)
        --     if RoleFacade.instance:getRoleSex() == 0 then
        --         pieceMiddle = male
        --     else
        --         pieceMiddle = female
        --     end
        -- elseif sign == "action" then
        --     if string.byte(text) == string.byte("A") then
        --         SceneMainPlayer.instance:getMainPlayer().pose:play(param)
        --     else
        --         if self._npcId then
        --             SceneFacade.instance:npcInteract(self._npcId, param)
        --         end
        --     end
        -- elseif sign == "audio" then
        --     local audioId = tonumber(param)
        --     if audioId then
        --         AudioPlayer.instance:playAudioEffect(audioId)
        --     end
        pieceMiddle = "<color=#952735FF>" .. UserInfo.nickname .. "</color>"
    elseif sign == "N" or sign == "n" then
        pieceMiddle = "\n"
    end
    text = piece1 .. pieceMiddle .. piece2
    return true, text
end

function TaskUtil.showGOThenHideOthersByName(parentTr, goName)
    local go
    local activeGOList = {}
    for childIndex = 0, parentTr.childCount - 1 do
        go = parentTr:GetChild(childIndex).gameObject
        if go.name == tostring(goName) then
            go:SetActive(true)
            table.insert(activeGOList, go)
        else
            go:SetActive(false)
        end
    end
    return activeGOList
end

function TaskUtil.showGOThenHideOthers(go)
    local parentTr = go.transform.parent
    if parentTr and parentTr.childCount > 0 then
        for childIndex = 0, parentTr.childCount - 1 do
            parentTr:GetChild(childIndex).gameObject:SetActive(false)
        end
    end
    go:SetActive(true)
end

function TaskUtil.setObjectiveIcon(goIconContainer, watcher)
    if not string.nilorempty(watcher:getConfigIconName()) then
        local go = TaskUtil.showGOThenHideOthersByName(goIconContainer.transform, "objectiveIcon")[1]
        IconLoader.setSpriteToImg(go, GameUrl.getTaskObjectiveIconUrl(watcher:getConfigIconName()))
        return
    end

    local type, id = watcher:getIconTypeAndId()
    local go = TaskUtil.showGOThenHideOthersByName(goIconContainer.transform, type)[1]
    if type == TaskStepObjectiveIconType.Item then
        IconLoader.setIconForItem(go, id)
    elseif type == TaskStepObjectiveIconType.NPC then
        local npcDefine = NpcConfig.getNpcDefine(id)
        IconLoader.setSpriteToImg(go, GameUrl.getNpcIconBorderURL(npcDefine.resId == 0 and npcDefine.id or npcDefine.resId))
    end
end

function TaskUtil.unactiveAllChildren(go)
    local tr = go.transform
    local childCount = tr.childCount
    for i = 0, childCount - 1 do
        tr:GetChild(i).gameObject:SetActive(false)
    end
end

function TaskUtil.stepId2TaskId(stepId)
    return TaskStepConfig.instance:getStepCO(stepId).taskId
end

function TaskUtil.taskId2FirstStepId(taskId)
    return taskId * 1000 + 1
end

function TaskUtil.showView(viewName)
    if viewName == "Lottery" then
        LotteryController.instance:openLotteryMainView()
        return
    end
    if viewName == "InfoCardPanel" then
        ViewFacade.showUserInfo(UserInfo.userId)
        return
    end
    ViewMgr.instance:open(viewName)
end

function TaskUtil.getTaskDesc(desc, color)
    color = color or "#89D5C9FF"
    return string.gsub(desc, "{username}", string.format("<color=%s>%s</color>", color, UserInfo.nickname))
end

function TaskUtil.getNPCAvatarViewWithUIOffset(npcId, parentGO)
    local avatarView = GameUtils.createUINpcView(npcId, parentGO, true)
    local npcDefine = NpcConfig.getNpcDefine(npcId)
    if npcDefine.resId > 0 then
        npcDefine = NpcConfig.getNpcDefine(npcDefine.resId)
    end
    local offset = npcDefine.uiOffset
    if npcDefine.uiOffset then
        avatarView.go.transform.anchoredPosition = gvec2(offset[1], offset[2])
        avatarView.go.transform.localScale = gvec3(offset[3], offset[3], 1)
    end
    return avatarView
end

function TaskUtil.SetNPCPhotoPosition(photo, npcId, dir)
    local npcDefine = NpcConfig.getNpcDefine(npcId)
    if npcDefine == nil then
        photo:SetCameraPosition(0, 3.5, -4)
        return
    end
    if npcDefine.resId > 0 then
        npcDefine = NpcConfig.getNpcDefine(npcDefine.resId)
    end
    local offset = npcDefine.dialogueOffset
    if offset then
        photo:SetCameraPosition(dir == UnitSpineDir.Right and -offset[1] or offset[1], offset[2], offset[3])
    else
        photo:SetCameraPosition(0, 3.5, -4)
    end
end

function TaskUtil.SetPlayerPhotoPosition(photo, camX, camY, camZ)
    camX = camX or 0
    camY = camY or 3.26
    camZ = camZ or -3.8
    photo:SetCameraPosition(camX, camY, camZ)
end

function TaskUtil.CheckIfDiamondEnough(requireItemNum)
    if ItemService.instance:getItemNum(2) < requireItemNum then
        DialogHelper.showNegativeMsg(lang("钻石不足"), handler)
        return false
    end
    return true
end

function TaskUtil.ShowStoryBlack(steps, autoClose, onEnd, onEndObj)
    print("TaskUtil.ShowStoryBlack", debug.traceback())
    if ViewMgr.instance:isOpen("StoryBlack") then
        TaskController.instance:localNotify(TaskLocalNotify.StoryBlack_UpdateParams, steps, autoClose, onEnd, onEndObj)
    else
        ViewMgr.instance:open("StoryBlack", steps, autoClose, onEnd, onEndObj)
    end
end

function TaskUtil.CloseStoryBlack()
    ViewMgr.instance:close("StoryBlack")
end

function TaskUtil.BlockClick(isBlock, blockKey)
    print("TaskUtil.BlockClick", isBlock, blockKey)
    if enableLog and (type(isBlock) ~= "boolean" or type(blockKey) ~= "string") then
        printError("传错参数")
    end
    ViewBlockMgr.instance:blockClick(isBlock, blockKey)
end
--character = 玩家(0) + npc(大于0)
function TaskUtil.CreateCharacterUnit(characterId)
end

function TaskUtil.CheckGetCustomParams(customParams, key, showErrorMsg)
    if customParams then
        local v = customParams[key]
        if v then
            return v
        end
    end
    if showErrorMsg ~= false then
        printError("缺少必要的customParams key:", key)
    end
end

function TaskUtil.parseRewardConfigStr(itemListStr)
    local itemConfigs = {}
    if not string.nilorempty(itemListStr) then
        for _, itemStr in ipairs(string.split(itemListStr, ",")) do
            local arrItem = string.split(itemStr, ":")
            table.insert(itemConfigs, Item.New(tonumber(arrItem[1]), tonumber(arrItem[2])))
        end
    end
    return itemConfigs
end

function TaskUtil.isStartConsumeEnough(taskId)
    local taskCO = TaskConfig.instance:getTaskCO(taskId)
    local rewardItems = TaskUtil.parseRewardConfigStr(taskCO.startConsume)
    local requiredTokenNum = rewardItems[1] and rewardItems[1].num or 0
    local requiredTokenId = rewardItems[1] and rewardItems[1].id or CurrencyId.TaskTicket
    local myNum = ItemService.instance:getItemNum(requiredTokenId)
    local isEnough = myNum >= requiredTokenNum
    return isEnough, requiredTokenId, requiredTokenNum, myNum
end

function TaskUtil.isActivityTask(taskId)
    local taskType = TaskConfig.instance:getTaskCO(taskId).type
    return TaskUtil.isActivityTaskType(taskType)
end

function TaskUtil.isActivityTaskType(taskType)
    return taskType > 200000 and taskType < 300000
end

function TaskUtil.taskId2TaskType(taskId)
    local taskCO = TaskConfig.instance:getTaskCO(taskId)
    return taskCO.type
end

function TaskUtil.chapterId2TaskType(chapterId)
    local chapterCO = TaskChapterConfig.instance:getChapterCO(chapterId)
    return chapterCO.type
end

function TaskUtil.needFinger()
    local cfgValue = GameUtils.getCommonConfig("TaskStoryFinger")
    local cfgArr = string.splitToNumber(cfgValue, ",")
    return TaskChapterModel.instance:isComplete(cfgArr[1]) and TaskModel.instance:isTaskUnopen(cfgArr[2])
end

function TaskUtil.getNextChapterCO(chapterId)
    local taskType = TaskUtil.chapterId2TaskType(chapterId)
    local chapterList = TaskChapterModel.instance:getChapterCOList(taskType)
    for index, co in ipairs(chapterList) do
        if co.id == chapterId then
            if index ~= #chapterList then
                local nextChapterCO = chapterList[index + 1]
                if nextChapterCO and
                    TaskChapterConfig.instance:getChapterSeriesId(chapterId) == TaskChapterConfig.instance:getChapterSeriesId(nextChapterCO.id) then
                    return nextChapterCO
                else
                   return nil
                end
            end
        end
    end
    return nil
end

function TaskUtil.getNewUnlockChapterId(chapterType, ignoreDoing)
    local chapterType = chapterType or TaskChapterType.Story
    local chapterList = TaskChapterModel.instance:getChapterCOList(chapterType)
    local newUnlockChapterId
    for _, chapterCO in ipairs(chapterList) do
        if not ignoreDoing and not TaskChapterModel.instance:isComplete(chapterCO.id) then
            break
        end
        if TaskChapterModel.instance:isUnopen(chapterCO.id, true) then
            newUnlockChapterId = chapterCO.id
            break
        end
    end
    return newUnlockChapterId
end

function TaskUtil.getChapterListByMinLevel(chapterType, level)
    chapterType = chapterType or TaskChapterType.Story
    level = level or 0
    local chapterList = TaskChapterModel.instance:getChapterCOList(chapterType)
    local resultList = {}
    for _, chapterCO in ipairs(chapterList) do
        local taskCO = TaskConfig.instance:getTaskCO(chapterCO.taskIds[1])
        if taskCO.minLevel == level then
            table.insert(resultList, chapterCO)
        end
    end
    return resultList
end

function TaskUtil.isTicketEnough(taskId)
    local taskCO = TaskConfig.instance:getTaskCO(taskId)
    if not taskCO.startConsume then
        print("缺少开启物品配置 taskId:" .. taskId)
        return false
    end
    local rewardItems = TaskUtil.parseRewardConfigStr(taskCO.startConsume)
    local requiredTokenNum = rewardItems[1] and rewardItems[1].num or 0
    local requiredTokenId = rewardItems[1] and rewardItems[1].id or CurrencyId.TaskTicket
    local myNum = ItemService.instance:getItemNum(requiredTokenId)
    local isEnough = myNum >= requiredTokenNum
    return isEnough
end

function TaskUtil.tryTriggerTaskFromNPC(npcId)
    local taskIdList = TaskNPCModel.instance:getTaskIdListFromNPC(npcId)
    if taskIdList and #taskIdList == 1 and TaskNPCModel.instance:checkTriggerTask(npcId, taskIdList[1]) then
        TaskNPCModel.instance:addTriggerCount(npcId, taskIdList[1])
        local taskconfig =  TaskConfig.instance:getTaskCO(taskIdList[1])
        if taskconfig.extParam and taskconfig.extParam.eventId and taskconfig.extParam.eventId > 0 then
            GlobalDispatcher:dispatch(GlobalNotify.onClickNpcToTalk, taskIdList[1], npcId)
        else
            RoleDialogueController.instance:triggerClickTaskBtn(taskIdList[1])
        end
        return true
    end
    return false
end

function TaskUtil.isLookbackEnable(watcher)
    local defineId = watcher:getObjectiveDefineId()
    print(watcher:isObjectiveCompleted(), defineId)
    if watcher:isObjectiveCompleted() and (defineId == 10000 or defineId == 10001) then
        return true
    end
    return false
end

function TaskUtil.triggerLookback(watcher)
    local defineId = watcher:getObjectiveDefineId()
    if defineId == 10000 then
        SceneController.instance:stopAction()
        TaskCmdFacade.instance:startCmd(watcher:getStepId(), TaskCmdTriggerType.BeforeShowTaskPanel)
    elseif defineId == 10001 then
        TaskCmdFacade.instance:tryStartCmd(
            watcher:getStepId(),
            TaskCmdTriggerType["ObjectiveFinished_" .. watcher:getIndex()]
        )
    end
end

function TaskUtil.isCheckDownloadTaskType(taskType)
    return TaskSetting.CheckDownloadTypesMap[taskType] == true
end

function TaskUtil.tryShowDownload(chapterId, callBack)
    local seriesId = TaskChapterConfig.instance:getChapterSeriesId(chapterId)
	local number = table.indexof(TaskChapterConfig.instance:getChapterSeriesConfig()[seriesId].chapterIds, chapterId)
	if seriesId > 1 or (seriesId == 1 and number >= 7) then
		InGameUpdateController.instance:getUpdateTaskAndShowDownloadViewAsync("story" .. seriesId, callBack)
    else
        callBack(true)
    end
end

function TaskUtil.gotoNPCFavorView(npcId)
    npcId = tonumber(npcId)
    TaskRedPointController.instance:cancelNPCNew(npcId)
    if not FavorabilityModel.instance:getNpcItemMoById(npcId).isUnlock then
        print("npc好感度未开启",npcId)
        local unlockTaskId = NpcConfig.getNpcFavorUnlockTaskId(npcId)
        local taskConfig = TaskConfig.instance:getTaskCO(unlockTaskId)
        local chapterConfig = TaskChapterModel.instance:getChapterConfigByTaskId(unlockTaskId)
        local chapterId = chapterConfig.id
        local seriesId, number = TaskChapterConfig.instance:getChapterSeriesIdAndIndex(chapterId)
        local msg
        if seriesId and number then
            if seriesId == 1 then
				number = number - 1
			end
            msg = lang("好感度解锁提示", seriesId, number == 0 and lang("序章") or lang("第{1}章", number), table.indexof(chapterConfig.taskIds, unlockTaskId), taskConfig.name)
        else
            msg = "无法找到对应章节 taskId:" .. tostring(unlockTaskId)
        end
        FlyTextManager.instance:showFlyText(msg)
        return false
    end
    ViewMgr.instance:open("FavorabilityView", npcId)
    return true
end

function TaskUtil.log(...)
    print(...)
end

return TaskUtil
