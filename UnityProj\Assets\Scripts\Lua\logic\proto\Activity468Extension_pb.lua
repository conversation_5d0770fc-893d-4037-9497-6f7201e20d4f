-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity468Extension_pb", package.seeall)


local tb = {}
GAINACT468REWARDREQUEST_MSG = protobuf.Descriptor()
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD = protobuf.FieldDescriptor()
GETACT468INFOREQUEST_MSG = protobuf.Descriptor()
GETACT468INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETACT468INF<PERSON>EPLY_GAINEDREWARDIDS_FIELD = protobuf.FieldDescriptor()
GAINACT468REWARDREPLY_MSG = protobuf.Descriptor()
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()

tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.name = "rewardId"
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.full_name = ".GainAct468RewardRequest.rewardId"
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.number = 1
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.index = 0
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.label = 2
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.has_default_value = false
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.default_value = 0
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.type = 5
tb.GAINACT468REWARDREQUEST_REWARDID_FIELD.cpp_type = 1

GAINACT468REWARDREQUEST_MSG.name = "GainAct468RewardRequest"
GAINACT468REWARDREQUEST_MSG.full_name = ".GainAct468RewardRequest"
GAINACT468REWARDREQUEST_MSG.filename = "Activity468Extension"
GAINACT468REWARDREQUEST_MSG.nested_types = {}
GAINACT468REWARDREQUEST_MSG.enum_types = {}
GAINACT468REWARDREQUEST_MSG.fields = {tb.GAINACT468REWARDREQUEST_REWARDID_FIELD}
GAINACT468REWARDREQUEST_MSG.is_extendable = false
GAINACT468REWARDREQUEST_MSG.extensions = {}
GETACT468INFOREQUEST_MSG.name = "GetAct468InfoRequest"
GETACT468INFOREQUEST_MSG.full_name = ".GetAct468InfoRequest"
GETACT468INFOREQUEST_MSG.filename = "Activity468Extension"
GETACT468INFOREQUEST_MSG.nested_types = {}
GETACT468INFOREQUEST_MSG.enum_types = {}
GETACT468INFOREQUEST_MSG.fields = {}
GETACT468INFOREQUEST_MSG.is_extendable = false
GETACT468INFOREQUEST_MSG.extensions = {}
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.name = "rechargeCount"
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.full_name = ".GetAct468InfoReply.rechargeCount"
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.number = 1
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.index = 0
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.label = 2
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.has_default_value = false
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.default_value = 0
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.type = 5
tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD.cpp_type = 1

tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.name = "gainedRewardIds"
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.full_name = ".GetAct468InfoReply.gainedRewardIds"
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.number = 2
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.index = 1
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.label = 3
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.has_default_value = false
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.default_value = {}
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.type = 5
tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD.cpp_type = 1

GETACT468INFOREPLY_MSG.name = "GetAct468InfoReply"
GETACT468INFOREPLY_MSG.full_name = ".GetAct468InfoReply"
GETACT468INFOREPLY_MSG.filename = "Activity468Extension"
GETACT468INFOREPLY_MSG.nested_types = {}
GETACT468INFOREPLY_MSG.enum_types = {}
GETACT468INFOREPLY_MSG.fields = {tb.GETACT468INFOREPLY_RECHARGECOUNT_FIELD, tb.GETACT468INFOREPLY_GAINEDREWARDIDS_FIELD}
GETACT468INFOREPLY_MSG.is_extendable = false
GETACT468INFOREPLY_MSG.extensions = {}
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.full_name = ".GainAct468RewardReply.changeSetId"
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GAINACT468REWARDREPLY_MSG.name = "GainAct468RewardReply"
GAINACT468REWARDREPLY_MSG.full_name = ".GainAct468RewardReply"
GAINACT468REWARDREPLY_MSG.filename = "Activity468Extension"
GAINACT468REWARDREPLY_MSG.nested_types = {}
GAINACT468REWARDREPLY_MSG.enum_types = {}
GAINACT468REWARDREPLY_MSG.fields = {tb.GAINACT468REWARDREPLY_CHANGESETID_FIELD}
GAINACT468REWARDREPLY_MSG.is_extendable = false
GAINACT468REWARDREPLY_MSG.extensions = {}

GainAct468RewardReply = protobuf.Message(GAINACT468REWARDREPLY_MSG)
GainAct468RewardRequest = protobuf.Message(GAINACT468REWARDREQUEST_MSG)
GetAct468InfoReply = protobuf.Message(GETACT468INFOREPLY_MSG)
GetAct468InfoRequest = protobuf.Message(GETACT468INFOREQUEST_MSG)

return _G["logic.proto.Activity468Extension_pb"]
