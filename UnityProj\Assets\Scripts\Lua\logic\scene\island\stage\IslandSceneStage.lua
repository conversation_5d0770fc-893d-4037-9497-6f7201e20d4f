module("logic.scene.island.stage.IslandSceneStage", package.seeall)
local IslandSceneStage = class("IslandSceneStage", CommonSceneStageBase)

function IslandSceneStage:load(sceneId)
	RoomController.instance:localNotify(RoomNotifyName.PreLoadRoom)
	self._sceneId = sceneId
	local joinSceneParams = SceneManager.instance:getEnterParams()
	if joinSceneParams.roomType == RoomScene.Type.TemplatePreview or SceneManager.instance:getCurSceneId() == 71 then
		HouseModel.instance:setHouse(UserInfo.userId, {furniture = {}}, RoomScene.Type.TemplatePreview)
		local area = {}
		for i = 1, 42 do table.insert(area, {id = i, unlockTime = 0}) end
		local data = {}
		for i = 1, 3 do table.insert(data, {areaId = i, houseId = 0}) end
		IslandModel.instance:setKaweiFromServer(data)
		HouseModel.instance:getAreaModel():setIslandArea(area, {}, UserInfo.userId)
		HouseModel.instance:setRoleInfo(UserInfo.roleInfo)
		if joinSceneParams.friendId ~= nil then
			self:_onGetFriendFurniture(joinSceneParams.furnitures)
			-- FurnitureTemplateShareSquareAgent.instance:sendGetFriendFurnitureInfoRequest(joinSceneParams.friendId,handler(self._onGetFriendFurniture, self))
-- self:_onGetFriendFurniture({{furnitureId=13002632, count = 99}})
		else
			self:_handleGetHouseInfo()
		end
	else
		self.userId = joinSceneParams.userId or SceneManager.instance:getSceneOwner()
		self.isNpc = joinSceneParams.isNpc or false
		if SceneManager.instance:getCurSceneId() == 79 then
			SeafloorIslandAgent.instance:sendGetSeafloorIslandInfoRequest(self.userId, handler(self._handleGetHouseInfo, self))
		elseif SceneManager.instance:getCurSceneId() == 109 then
			ShareStreetAgent.instance:sendGetShareStreetFurnitureInfoRequest(self.userId, handler(self._handleGetHouseInfo, self))
		else
			TravellerController.instance:_onEnterScene(self.userId)
			IslandAgent.instance:sendGetIslandInfoRequest(self.userId, self.isNpc, handler(self._handleGetHouseInfo, self))
		end
	end
end

function IslandSceneStage:_onGetFriendFurniture(furnitures)
	RoomEditModel.instance:setFriendFurnitures(furnitures)
	self:_handleGetHouseInfo()
end

function IslandSceneStage:_handleGetHouseInfo()
	IslandSceneStage.super.load(self, self._sceneId)
end

function IslandSceneStage:getResList()
	local houseType = HouseModel.instance:getUserProperty(HouseUserProperty.HouseType)
	local islandType = HouseModel.instance:getUserProperty(HouseUserProperty.IslandType, 0)
	self.mapDataUrl = RoomConfig.getHouseWalkDataUrl(houseType, islandType)
	return {
		self.mapDataUrl,
		RoomConfig.FurPlaneUrl,
		RoomConfig.FurRectUrl,
		RoomFurnitureOpView.Url,
		CropsInfoView.Url,
		HarvestToolView.viewUrl,
		PlantProgressView.viewUrl,
		HarvestConfig.Effect_ChengShou_ChiXu,
		HarvestConfig.Effect_ChengShou_ChiXu_Big,
		HarvestConfig.Effect_ChengShou_DianJi,
		RoomCountToOpView.url,
		RoomBatchBuyView.url,
		FoodHandler.ItemUrl,
		PropInfoView.Url,
		RoomCompAreaCleaner.AreaIconUrl,
		WorkShopHandler.IconUrl,
		FSTHandler.IconUrl,
		IslandButterflySceneComp.IconUrl,
		FarmController.fingerViewUrl,
		IslandButterflySceneComp.InsetUrl,
		IslandDiaryController.CameraPath,
		RoomCompKawei.PaiziUrl,
		UprootToolView.viewUrl,
		RoomMultiDragOpView.Url,
	}
end

return IslandSceneStage 