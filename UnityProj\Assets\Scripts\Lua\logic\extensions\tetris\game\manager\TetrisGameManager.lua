module("logic.extensions.tetris.game.manager.TetrisGameManager", package.seeall)

local TetrisGameManager = class("TetrisGameManager")

function TetrisGameManager:ctor(id)
	self._id = id
	self._controller = TetrisController.instance
	self._model = TetrisModel.instance
	self._isStandalone = self._model:getGameModeIsStandalone()
	self._compList = {}
	self.compTypeMap = {}
	self:_InitGame()
end

--初始化游戏
function TetrisGameManager:_InitGame()
	self._curShape = nil
	self._finalShape = nil
	self._fixSyncShape = {} --固定并且未同步给后端的砖块
	self._shapeList = {}
	self._mPunishInfos = {}
	self._shapeIndex = 0
	self._blockHalfW = TetrisNotify.blockSize / 2
	self._mapHalfH = TetrisNotify.maxRows * self._blockHalfW
	self._mapHalfW = TetrisNotify.maxColumns * self._blockHalfW
	self._syncCalTime = 0
	--本游戏的实际游戏时间
	self._gameTime = 0
	--自动添加的惩罚方块最后时间
	self:setPunishTime(30)
	self._isClickDown = false
	self._isClickLeft = false
	self._isClickRight = false
	self._mineScore = SafeNum.New(0)
	--技能组件，为了不让本类过于混乱，这个组件起到一个外观类的作用
	--如果以后有技能 234 就new 234个skillcomp
	--self:_addComp("_skillComp1", TetrisSkillComp, {self, 1})
	--子弹组件
	self:_addComp("_bulletComp", TetrisBulletComp, {self})

	self:setIsDead(false)
	self:_addComp("_effectComp", TetrisEffectComp, {TetrisNotify.blockSize})
	self:_activeCompIfIsPlot()
	self:_createMapData()
end

function TetrisGameManager:_activeCompIfIsPlot()
	if self._isStandalone then
		local levelId = TetrisUIModel.instance:getNowLevelId()
		local levelCfg = TetrisConfig.getLevelConfigById(levelId)
		if levelCfg and levelCfg.isPlot then -- 剧情
			self:_addComp("endingHandlerComp", TetrisStoryEndingHandlerComp, {self})
		end
	end
end

function TetrisGameManager:_addComp(compName, compClass, params)
	local compInst
	if params then
		compInst = compClass.New(unpack(params))
	else
		compInst = compClass.New()
	end
	self[compName] = compInst
	table.insert(self._compList, compInst)
	self.compTypeMap[compClass] = self[compName]
	return compInst
end

--开始游戏
function TetrisGameManager:startGame()
	self:_createInitialBlocks()
	self._finalShapeGo = self._controller:getFinalCoordShape()
	self._finalShape = TetrisFinalShape.New(self._finalShapeGo)
	self:fallNewShape()
	self._isStartingGame = true
end

--暂停游戏
--function TetrisGameManager:_stopGame()
--self._stopGame = true
--end

--重新开始游戏，只有单机才允许再来一局
--function TetrisGameManager:_restartGame()
--if not self._isStandalone then
--return
--end
--self:disposeGame()
--self:_InitGame()
--self:_startGame()
--end

--游戏结束
function TetrisGameManager:_endGame()
end

--退出游戏
function TetrisGameManager:disposeGame()
	self:_endGame()
	self._isStartingGame = false
	self._curShape = nil
	self._mapInfos = nil
	for i, comp in ipairs(self._compList) do
		trycall(comp.dispose, comp)
	end
	self._compList = nil
	self.compTypeMap = nil

	self._mineScore = nil
	self._fixSyncShape = nil
	self._mPunishInfos = nil
	if self._shapeList then
		for k, v in pairs(self._shapeList) do
			v:dispose()
			self._controller:getPool():pushItem(v)
		end
		self._shapeList = nil
	end
	if self._speedupEffectList then
		for i = 1, #self._speedupEffectList do
			self._controller:getPool():pushItem(self._speedupEffectList[i])
		end
		self._speedupEffectList = nil
	end
end

--游戏操作帧
function TetrisGameManager:update(disSync)
	if not self._isStartingGame then
		return
	end
	self:_keyDown()
end

--游戏逻辑帧
function TetrisGameManager:lateUpdate(disSync)
	if self:_checkIsDeadOrGameOver() then
		return
	end
	if not self._isStartingGame then
		return
	end
	self._bulletComp:lateUpdate()

	if self._curShape then
		self._curShape:lateUpdate()
	else
		self:fallNewShape()
	end

	--self._skillComp1:lateUpdate()

	self._gameTime = self._gameTime + Time.deltaTime
	if self._isStandalone then
		if TetrisNotify.punishInterval > 0 then
			--惩罚砖块自动出现计时器
			if self._lastPunishTime < self._gameTime then
				self:setPunishTime(self._lastPunishTime + TetrisNotify.punishInterval)
				self._model:setPunish(1, TetrisNotify.punishRowCount)
				self:punishQuietly()
			end
		end
	end
	TetrisGameViewModel.instance:setValue("NextPunishRemainingTime", self._lastPunishTime - self._gameTime)
	--检查惩罚
	if not self._isClearingShape and self._mPunishInfos and #self._mPunishInfos > 0 then
		for i = 1, #self._mPunishInfos do
			local punishRows = self._mPunishInfos[i].punishRows
			local startY = self._mPunishInfos[i].startY
			local endY = self._mPunishInfos[i].endY
			if self._isStandalone then
				self:_punishHandleInternal(punishRows, startY, endY)
			else
				self:_punishHandle(punishRows, startY, endY)
			end
		end
		self._mPunishInfos = {}
	end
	--多人需要同步的数据
	if not self._isStandalone then
		--if not disSync then
		if not disSync and not self._isClearingShape then
			self._syncCalTime = self._syncCalTime + Time.deltaTime
			if self._syncCalTime >= TetrisNotify.syncGameInfoTime then
				self:_syncMyGameInfo()
				self._syncCalTime = 0
			end
		end
	end
end

--创建地图数据
function TetrisGameManager:_createMapData(id)
	if self._mapInfos then
		return
	end
	self._mapInfos = {}
	for i = 1, TetrisNotify.maxRows do
		table.insert(self._mapInfos, TetrisNotify.emptyRowInfo)
	end
end

--添加子弹
function TetrisGameManager:addBullet(v3)
	self._bulletComp:addBullet(v3)
end

--创建初始方块
function TetrisGameManager:_createInitialBlocks()
	if self._isStandalone then
		--新增debuff砖块并且固定
		local poolKey = "PunishShape"
		local punishRows = TetrisNotify.initialBlockRowCount or 0
		for i = 1, punishRows do
			local shape = self:_addShape(8, poolKey, i)
			shape:_init(8, self, i)
		end
	end
end

--创建新的方块
function TetrisGameManager:fallNewShape()
	-- print("fallNewShape", self._curShape, self._curShape and self._curShape:getIsFix())
	if self:_checkIsDeadOrGameOver() then
		return false
	end

	-- local isClearingRow = self._effectComp:_getClearRowState()
	-- if isClearingRow then
	-- 	print("isClearingRow", isClearingRow)
	-- 	return false
	-- end
	if self._curShape and not self._curShape:getIsFix() then
		print("self._curShape and not self._curShape:getIsFix()")
		return false
	end

	self._model:moveNextShape()
	local type = self._model:getCurShapeType()
	if type == nil then
		print("未达成条件", type)
		return false
	end
	local type = self._model:getCurShapeType()
	self:createNewShape(type)
end

function TetrisGameManager:createNewShape(type)
	local poolKey = TetrisNotify.shapeInfos[type].poolKey
	local shape = self:_addShape(type, poolKey)
	-- print("createNewShape", tostring(shape))
	self._curShape = shape
	self._curShape:_init(type, self)
	GlobalDispatcher:dispatch(TetrisNotify.curShapeTypeChange)
end

function TetrisGameManager:disposeCurrentShape()
	local curShape = self._curShape
	curShape:dispose()
	self._curShape = nil
end

function TetrisGameManager:checkCurrentShapeActive()
	if self:_checkIsDeadOrGameOver() then
		return false
	end
	if self._curShape and self._curShape:getIsFix() then
		return false
	end
	return true
end

--获取当前运动的砖块
function TetrisGameManager:getCurShape()
	return self._curShape
end

--获取当前运动的砖块
function TetrisGameManager:getSkillComp()
	return self._skillComp1
end

--返回每个砖块坐标
--blockList：砖块列表
--clearPos：已经标志为清除的砖块不做判断
--rowStep：下一次操作方块的坐标
--rect：以某个ui的坐标系进行转换
function TetrisGameManager:returnBlockCoord(blockList, clearPos, rowStep, rect)
	local curBlocksCoord = {}
	local nextBlocksCoord = {}
	if rect == nil then
		rect = self._controller:getBornPoint()
	end
	for i = 1, #blockList do
		if not clearPos[i] then
			curBlocksCoord[i] = {}
			nextBlocksCoord[i] = {}
			local pos = blockList[i].transform.position
			pos = rect.transform:InverseTransformVector(pos)
			local x = math.round(pos.x) + self._blockHalfW + self._mapHalfW
			local y = math.round(pos.y) + self._blockHalfW + self._mapHalfH

			local columns = x / TetrisNotify.blockSize
			local row = y / TetrisNotify.blockSize

			curBlocksCoord[i].columns = columns
			curBlocksCoord[i].row = row
			nextBlocksCoord[i].columns = columns
			nextBlocksCoord[i].row = row - rowStep
		end
	end
	return curBlocksCoord, nextBlocksCoord
end

--检测砖块是否在有效位置上
--返回值1：是否有效位置；返回值2：isOffMap，是否在地图外
function TetrisGameManager:_checkIsShapePositionVaild(blocksCoord)
	for k, v in pairs(blocksCoord) do
		local columns = v.columns
		local row = v.row
		local info = self._mapInfos[row]
		if not info then
			return false, true
		end
		if columns <= 0 or columns > TetrisNotify.maxColumns then
			return false, true
		end
		local hasBlock = bitutil.checkBitValue(info, columns - 1)
		if hasBlock then
			return false, false
		end
	end
	return true, false
end

--向下查看 ，看是否含有空格
function TetrisGameManager:_checkLookDownHasHole(row, columns)
	for i = row, 1, -1 do
		local info = self._mapInfos[i]
		local hasBlock = bitutil.checkBitValue(info, columns - 1)
		if not hasBlock then
			print("hasBlock", i)
			return true
		end
	end
	return false
end

function TetrisGameManager:checkIsFullAfterAddOneBlock(row, columns)
	local info = self._mapInfos[row]
	if info then
		local var = bitutil.getNewBitValue(info, columns - 1)
		local isFull = var == TetrisNotify.fullRowInfo
		return isFull
	end
	return false
end

--固定砖块
function TetrisGameManager:fixBlock(blocksCoord)
	if self:_checkIsDeadOrGameOver() then
		return
	end
	for k, v in pairs(blocksCoord) do
		local columns = v.columns
		local row = v.row
		local info = self._mapInfos[row]
		if not info then
			return
		end
		local newInfo = bitutil.getNewBitValue(info, columns - 1)
		if newInfo ~= -1 and newInfo ~= -2 then
			self._mapInfos[row] = newInfo
		end
	end
end

--消除可以满足条件的砖块
function TetrisGameManager:checkIsCanClear()
	if self:_checkIsDeadOrGameOver() then
		return false
	end
	local clearList = nil
	local firstRow = nil
	local clearCount = 0
	for i = 1, #self._mapInfos do
		local info = self._mapInfos[i]
		local isFull = info == TetrisNotify.fullRowInfo
		if isFull then
			if firstRow == nil then
				firstRow = i
			end
			if clearList == nil then
				clearList = {}
			end
			clearList[i] = i
			clearCount = clearCount + 1
		end
	end
	if clearList ~= nil then
		print("checkIsCanClear", firstRow, clearCount)
		self:_executeClearBlock(clearList, firstRow, clearCount) --消除砖块
		return true
	end
	return false
end

--检测是否游戏结束
function TetrisGameManager:_checkIsDeadOrGameOver()
	return self._controller._isDead or self._controller._gameOver
end

function TetrisGameManager:setIsDead(isDead)
	if isDead then
		--游戏结束咯
		if not self._isStandalone then
			self:_syncMyGameInfo()
		end
		self:_endGame()
		printInfo("俄罗斯方块，游戏结束咯")
	end
end

--添加shape
function TetrisGameManager:_addShape(type, poolKey, params)
	local shape = self._controller:getPool():popItem(poolKey)
	local serialId = shape:getSerialId()

	self._shapeList[serialId] = shape
	self._shapeIndex = self._shapeIndex + 1
	if self._shapeIndex >= 2147470000 then
		self._shapeIndex = 0
	end
	return shape
end

--移除shape
function TetrisGameManager:removeShape(item)
	local serialId = item:getSerialId()
	if self._shapeList[serialId] then
		self._shapeList[serialId]:dispose()
		self._shapeList[serialId] = nil
		self._controller:getPool():pushItem(item)
	end
end

--执行消除砖块操作
function TetrisGameManager:_executeClearBlock(clearList, firstRow, clearCount, effectName)
	effectName = effectName or "ClearEffect"
	local preMapInfo = nil
	local preFixDateTypes = nil
	if not self._isStandalone then
		preMapInfo = {}
		preFixDateTypes = {}
		for i = 1, #self._mapInfos do
			table.insert(preMapInfo, self._mapInfos[i])
		end
		for i = 1, #self._fixSyncShape do
			local shape = self._fixSyncShape[i]
			if shape and shape:getIsInit() then
				local shapeType = shape:getShapeType()
				self:_getBlockType(preFixDateTypes, shape:getCurCoord(), shapeType)
			end
		end
	end
	--隐藏block
	for k, v in pairs(self._shapeList) do
		v:clearBlock(clearList)
	end
	--清除底图数据
	for i = firstRow, #self._mapInfos do
		local temp = self._mapInfos[i + clearCount]
		if temp then
			self._mapInfos[i] = temp
		else
			self._mapInfos[i] = TetrisNotify.emptyRowInfo
		end
	end
	--显示特效
	self._isClearingShape = true
	self._effectComp:setClearRowEffect(
		clearList,
		false,
		function()
			self:_executeAllShapeFall(clearList, firstRow) --全体掉落
			self:_syncMyGameInfo(preMapInfo, preFixDateTypes, clearList, true) --同步
			self._isClearingShape = false
		end,
		effectName
	)
	self:_checkSendDebuff(clearList, clearCount)
	self:_gainScore(clearCount) --debuff
end

function TetrisGameManager:testClearList()
	print("testClearList")
	local clearList = {[2] = 2}
	self:_executeClearBlock(clearList, 2, 1)
end

--执行所有固定方块掉落
function TetrisGameManager:_executeAllShapeFall(clearList, firstRow)
	print("执行所有固定方块掉落")
	--其余小砖块往下掉落
	for k, v in pairs(self._shapeList) do
		v:HandleExtraBlock(clearList, firstRow, -1)
	end
end

--检测是否可以给对手debuff
function TetrisGameManager:_checkSendDebuff(clearList, clearCount)
	if self._isStandalone then
		return
	end
	local count = clearCount
	local punishRows = TetrisNotify.clearRowsToPunish[count]
	if not punishRows or punishRows <= 0 then
		return
	end
	--发送debuff
	local times = self._model:getPunishTimes()
	local startY, endY, row = self:_getPunishStartYAndEndY(clearList, clearCount, false)
	row = row * 10000
	Activity278Agent.instance:sendSendDebuffRequest(times, row + punishRows)

	SoundManager.instance:playEffect(141843)
	--播放debuff特效
	self._effectComp:setPunishEffect(
		false,
		function()
			GlobalDispatcher:dispatch(TetrisNotify.sendDebuffFinish, punishRows, startY, endY)
		end,
		startY,
		endY
	)
end

--给自己上个debuff
function TetrisGameManager:punish()
	if self:_checkIsDeadOrGameOver() then
		return
	end

	local count = self._model:getPunish() --惩罚次数
	if count == 0 then
		return
	end
	local punishRows = count % 10000
	local row = math.floor(count / 10000)
	local startY, endY, row = self:_getPunishStartYAndEndY(nil, nil, true, row)
	SoundManager.instance:playEffect(141843)
	self._effectComp:setPunishEffect(
		true,
		function()
			local info = {punishRows = punishRows, startY = startY, endY = endY}
			table.insert(self._mPunishInfos, info)
		end,
		startY,
		endY
	)
end

--惩罚自己，没有特效，有音效
function TetrisGameManager:punishQuietly()
	if self:_checkIsDeadOrGameOver() then
		return
	end

	local count = self._model:getPunish() --惩罚次数
	if count == 0 then
		return
	end
	SoundManager.instance:playEffect(141843)
	local punishRows = count % 10000
	local row = math.floor(count / 10000)
	local startY, endY, row = self:_getPunishStartYAndEndY(nil, nil, true, row)
	local info = {punishRows = punishRows, startY = startY, endY = endY}
	table.insert(self._mPunishInfos, info)
end

--惩罚处理
function TetrisGameManager:_punishHandle(punishRows, startY, endY)
	GlobalDispatcher:dispatch(TetrisNotify.sendDebuffFinish, punishRows, startY, endY, true)
	self:_punishHandleInternal(punishRows, startY, endY)
end

function TetrisGameManager:_punishHandleInternal(punishRows, startY, endY)
	local extraRows = {}
	for i = punishRows, 1, -1 do
		extraRows[i] = i * -1
	end
	--清除下面抬高位置地图数据
	-- for i = 1, #self._mapInfos do
	-- 	local temp = self._mapInfos[i + clearCount]
	-- 	self._mapInfos[i] = TetrisNotify.emptyRowInfo
	-- end
	for i = #self._mapInfos, 1, -1 do
		local index = i - punishRows
		local temp = self._mapInfos[index]
		if temp then
			self._mapInfos[i] = temp
		else
			self._mapInfos[i] = TetrisNotify.emptyRowInfo
		end
	end

	--将固定的小砖块往上移动并固定
	for k, v in pairs(self._shapeList) do
		v:HandleExtraBlock(extraRows, 1, 1)
	end

	--新增debuff砖块并且固定
	local poolKey = "PunishShape"
	for i = 1, punishRows do
		local shape = self:_addShape(8, poolKey, i)
		shape:_init(8, self, i)
	end

	--校验一次当前掉落的砖块位置
	local correctCount = 0
	if self._curShape then
		correctCount = self._curShape:correctPosition(punishRows)
	end

	----检测是否可以消除
	if correctCount ~= 0 then
		self:checkIsCanClear()
	end

	----检测是否有砖块在地图外	检测所有
	for k, v in pairs(self._shapeList) do
		local coord = v:getFixedBlocksCoord()
		if coord then
			local isVaild, isOffMap = self:_checkIsShapePositionVaild(coord)
			if isOffMap then
				self._controller:setIsDead(true)
				self:_syncMyGameInfo(nil, nil, nil, true)
				return
			end
		end
	end

	--掉落新的方块
	if correctCount ~= 0 then
		self:fallNewShape()
	end
	self:_syncMyGameInfo(nil, nil, nil, true)
end

--消除一个砖块
function TetrisGameManager:clearOneBlock(row, col)
	local info = self._mapInfos[row]
	if info and bitutil.checkBitValue(info, col - 1) then
		for k, v in pairs(self._shapeList) do
			if v:clearOneBlock(row, col) then
				local newInfo = bitutil.clearBitValue(info, col - 1)
				if newInfo ~= -1 and newInfo ~= -2 then
					self._mapInfos[row] = newInfo
					return true
				end
			end
		end
	end
	return false
end

--获取展示特效的startY and endY值
function TetrisGameManager:_getPunishStartYAndEndY(clearList, clearCount, isDisplay, punishRow)
	local row = 1
	local startY = 0
	local blockSize = isDisplay and TetrisNotify.displayBlockSize or TetrisNotify.blockSize
	local endPoint = isDisplay and self._controller:getPlayerPunishPoint() or self._controller:getDisplayPunishPoint()
	local endY = endPoint.transform.localPosition.y
	if not punishRow then
		local mid = math.ceil(clearCount / 2)
		local index = 1
		for k, v in pairs(clearList) do
			if index == mid then
				row = v
				break
			end
			index = index + 1
		end
	else
		row = punishRow
	end
	startY = row * blockSize - TetrisNotify.maxRows * blockSize / 2
	return startY, endY, row
end

--设置方块最终落下的坐标
--整个函数应该都是在处理预测砖块位置，传参代表不同的刷新方式
function TetrisGameManager:setFinalBlockCoord(curCoords, shapeType)
	--猜测应该是 游戏结束、坐标变化 固定，交换下落砖块时刷新预测位置
	if self:_checkIsDeadOrGameOver() or curCoords == nil or self._curShape == nil or self._curShape:getIsFix() then
		self._finalShape:_refreshShape()
		return
	end
	local minCoord = curCoords[1]
	if minCoord == nil then
		self._finalShape:_refreshShape()
		return
	end
	local minRow = curCoords[1].row
	for k, v in pairs(curCoords) do
		minRow = math.min(minRow, v.row)
	end
	minRow = minRow - 1
	if minRow <= 0 then
		self._finalShape:_refreshShape()
		return
	end
	local intervalRow = 0
	for i = 1, minRow do
		local isCanShow = true
		for k, v in pairs(curCoords) do
			local mRow = v.row
			local mColumns = v.columns
			local nextRow = mRow - i

			local info = self._mapInfos[nextRow]
			local hasBlock = bitutil.checkBitValue(info, mColumns - 1)
			if hasBlock then
				isCanShow = false
				break
			end
		end
		if isCanShow then
			intervalRow = i
		else
			break
		end
	end
	self._finalShape:_refreshShape(shapeType, intervalRow, self._curShape)
end

--加速游戏(主要为后台回来做处理)
function TetrisGameManager:setSpeedupGame(steps)
	while steps > 0 do
		if self._controller:isDead() then
			return
		end
		if self._controller:isGameOver() then
			return
		end
		if self._curShape and self._curShape:getIsInit() and not self._curShape:getIsFix() then
			steps = self._curShape:fall(steps)
		end
	end
end

--返回当前方块掉落的正常速度
function TetrisGameManager:getNomalFallTime()
	local spentTime = self._controller:getRoomSpentTime() / 1000
	local pauseTime = self._controller:getPauseSpendTime()
	spentTime = spentTime - pauseTime
	spentTime = spentTime <= 0 and 0 or spentTime
	local times = math.floor(spentTime / TetrisNotify.downNormalAddSpeedIntervalTime)
	local decreaseTime = times * TetrisNotify.downNormalAddSpeedRateTime
	local fallTime = TetrisNotify.downNormalStepMaxTime - decreaseTime
	fallTime = math.max(fallTime, TetrisNotify.downNormalStepMinTime)
	return fallTime
end

--消除获得积分
function TetrisGameManager:_gainScore(clearCount)
	local addScore = TetrisNotify.clearRowScoreList[clearCount]
	self:addMineScore(addScore)
end

function TetrisGameManager:addMineScore(addScore)
	if not self._isStandalone then
		return
	end
	local intMaxValue = 2000000000
	local curScore = self._mineScore:get()
	local isOver = intMaxValue - curScore < addScore
	if isOver then
		self._mineScore:set(intMaxValue)
	else
		self._mineScore:set(curScore + addScore)
	end
	--self._skillComp1:addSkillPoint(addScore)
	GlobalDispatcher:dispatch(TetrisNotify.gameScoreChange, self._mineScore:get(), isOver)
end
--获取当前积分
function TetrisGameManager:getCurScore()
	return self._mineScore:get()
end

--缓存固定且未同步给后端的方块
function TetrisGameManager:setFixShape(shape)
	if self._isStandalone then
		return
	end
	table.insert(self._fixSyncShape, shape)
end
--获取所有需要同步的方块图案
function TetrisGameManager:getAllSyncBlockType(isSyncAll, preFixDateTypes)
	local preShapeTypes = nil --消除前的数据/当前移动的砖块数据
	local curShapeTypes = {} --当前数据
	local maxRow = 0
	local isDead = TetrisController.instance:isDead()
	if isDead and self._curShape ~= nil then
		preShapeTypes = {}
		local shapeType = self._curShape:getShapeType()
		local curBlocksCoord = self._curShape:getCurCoord()
		self:_getBlockType(preShapeTypes, curBlocksCoord, shapeType)
	end
	if isSyncAll then
		if preShapeTypes == nil then
			preShapeTypes = {}
		end
		if preFixDateTypes then
			for i = 1, #preFixDateTypes do
				table.insert(preShapeTypes, preFixDateTypes[i])
			end
		end
	else
		--for i = 1, #self._fixSyncShape do
		--local shape = self._fixSyncShape[i]
		--if shape and shape:getIsInit() then
		--local shapeType = shape:getShapeType()
		--self:_getBlockType(preShapeTypes,shape:getCurCoord(),shapeType)
		--end
		--end
	end
	for k, v in pairs(self._shapeList) do
		local shape = v
		if shape and shape:getIsInit() then
			local shapeType = shape:getShapeType()
			local coord = shape:getCurCoord()
			for k, v in pairs(coord) do
				local columns = v.columns
				local row = v.row
				local index = (row - 1) * TetrisNotify.maxColumns + columns
				maxRow = math.max(maxRow, row)
				curShapeTypes[index] = shapeType
			end
		end
	end
	self._fixSyncShape = {}
	return preShapeTypes, curShapeTypes, maxRow
end
--获取砖块图案
function TetrisGameManager:_getBlockType(typeList, blocksCoord, shapeType)
	if not blocksCoord then
		return typeList
	end
	for k, v in pairs(blocksCoord) do
		local columns = v.columns * TetrisNotify.Data2SyncOffset
		local row = v.row * TetrisNotify.Data1SyncOffset
		table.insert(typeList, row + columns + shapeType)
	end
	return typeList
end

--同步游戏数据到服务器
function TetrisGameManager:_syncMyGameInfo(preMapInfo, preFixDateTypes, clearList, isSyncAll)
	if self._isStandalone then
		return
	end
	local isDead = self:_checkIsDeadOrGameOver()
	local bitDate = {}
	local finishDebuff = self._model:getFinishPunishIds()

	local mapInfoCount = #self._mapInfos
	for i = 1, mapInfoCount do
		bitDate[i] = self._mapInfos[i]
	end
	bitDate[mapInfoCount + 1] = TetrisNotify.emptyRowInfo
	bitDate[mapInfoCount + 2] = TetrisNotify.emptyRowInfo
	if self._curShape then
		local coord = self._curShape:getCurCoord()
		if coord then
			for k, v in pairs(coord) do
				local row = v.row
				local columns = v.columns
				local info = bitDate[row]
				if info == nil then
					info = TetrisNotify.emptyRowInfo
				end
				local newInfo = bitutil.getNewBitValue(info, columns - 1)
				if newInfo ~= -1 and newInfo ~= -2 then
					bitDate[row] = newInfo
				end
			end
		end
	end
	local clearInfo = 0
	if clearList then
		for i = 1, #preMapInfo do
			table.insert(bitDate, preMapInfo[i])
		end
		for k, v in pairs(clearList) do
			local newInfo = bitutil.getNewBitValue(clearInfo, v - 1)
			if newInfo ~= -1 and newInfo ~= -2 then
				clearInfo = newInfo
			end
		end
	end
	table.insert(bitDate, clearInfo)

	--changeDate:有变化的数据(移动中的砖块、未同步过的固定砖块) or 变化之前的砖块数据（移动中的砖块、变化之前的所有砖块数据）
	local preShapeTypes, curShapeTypes, maxRow = self:getAllSyncBlockType(isSyncAll, preFixDateTypes)
	Activity278Agent.instance:sendUpdateTetrisStateRequest(
		isDead,
		bitDate,
		finishDebuff,
		0,
		preShapeTypes,
		curShapeTypes,
		maxRow
	)
	finishDebuff = nil
end

--按键操作
function TetrisGameManager:_keyDown()
	if TetrisController.instance._isDebug then
		if self._curShape == nil then
			self._isClickDown = false
			return
		end
		if not self._curShape:getIsInit() then
			return
		end
		if self._curShape:getIsFix() then
			return
		end

		if self._isClickLeft then
			if self._isLeftSpeedUp then
				self._leftCalTime = self._leftCalTime + Time.deltaTime
				if self._leftCalTime > TetrisNotify.leftNormalStepTime / TetrisNotify.leftSpeedUpMultiple then
					--执行下降一格指令
					self._leftCalTime = 0
					self._curShape:moveLeftOrRight(-1)
				end
			else
				self._leftCalTime = self._leftCalTime + Time.deltaTime
				if self._leftCalTime > TetrisNotify.leftNormalStepTime then
					--执行下降一格指令
					self._isLeftSpeedUp = true
				end
			end
		end

		if self._isClickRight then
			if self._isRightSpeedUp then
				self._rightCalTime = self._rightCalTime + Time.deltaTime
				if self._rightCalTime > TetrisNotify.leftNormalStepTime / TetrisNotify.leftSpeedUpMultiple then
					--执行下降一格指令
					self._rightCalTime = 0
					self._curShape:moveLeftOrRight(1)
				end
			else
				self._rightCalTime = self._rightCalTime + Time.deltaTime
				if self._rightCalTime > TetrisNotify.leftNormalStepTime then
					--执行下降一格指令
					self._isRightSpeedUp = true
				end
			end
		end

		if UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.LeftArrow) then
			self._curShape:moveLeftOrRight(-1)
			self._leftCalTime = 0
			self._isClickLeft = true
		elseif UnityEngine.Input.GetKeyUp(UnityEngine.KeyCode.LeftArrow) then
			self._isLeftSpeedUp = false
			self._leftCalTime = 0
			self._isClickLeft = false
		end
		if UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.RightArrow) then
			self._curShape:moveLeftOrRight(1)
			self._rightCalTime = 0
			self._isClickRight = true
		elseif UnityEngine.Input.GetKeyUp(UnityEngine.KeyCode.RightArrow) then
			self._isClickRight = false
			self._isRightSpeedUp = false
			self._rightCalTime = 0
		end

		if UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.DownArrow) then
			self._isClickDown = true
			self._curShape:setSpeed(true)
		elseif UnityEngine.Input.GetKeyUp(UnityEngine.KeyCode.DownArrow) then
			self._curShape:setSpeed(false)
			self._isClickDown = false
		end
		if UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.Keypad1) then
		elseif UnityEngine.Input.GetKeyUp(UnityEngine.KeyCode.Keypad1) then
		end

		if UnityEngine.Input.GetKeyDown(UnityEngine.KeyCode.UpArrow) then
			self._curShape:rotateShape()
		end

		if self._isClickDown or self._isClickLeft or self._isClickRight then
			if not UnityEngine.Input.GetMouseButton(0) and UnityEngine.Input.touchCount == 0 and not UnityEngine.Input.anyKey then
				self._isClickLeft = false
				self._isClickRight = false
				self._isClickDown = false
				self._curShape:setSpeed(false)
			end
		end
	--if UnityEngine.Input.anyKey then

	--end
	end
end

--获取加速特效
function TetrisGameManager:getSpeedupEffects()
	if self._speedupEffectList == nil then
		self._speedupEffectList = {}
		for i = 1, 4 do
			local item = self._controller:getPool():popItem("SpeedUpEffect")
			table.insert(self._speedupEffectList, item)
		end
	end
	return self._speedupEffectList
end

function TetrisGameManager:setPunishTime(value)
	self._lastPunishTime = value
end

return TetrisGameManager
