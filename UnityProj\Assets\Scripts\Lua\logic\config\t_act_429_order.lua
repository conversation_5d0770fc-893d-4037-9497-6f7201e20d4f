-- {excel:429合成玩法.xlsx, sheetName:export_订单}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act_429_order", package.seeall)

local title = {activityId=1,id=2,submitItemLevel=3,reward=4}

local dataList = {
	{2108, 1, 4, {{count=10,id=16000679}}},
	{2108, 2, 5, {{count=25,id=16000679}}},
	{2282, 1, 4, {{count=10,id=16000740}}},
	{2282, 2, 5, {{count=25,id=16000740}}},
	{2509, 1, 4, {{count=10,id=16000740}}},
	{2509, 2, 5, {{count=25,id=16000740}}},
}

local t_act_429_order = {
	[2108] = {
		[1] = dataList[1],
		[2] = dataList[2],
	},
	[2282] = {
		[1] = dataList[3],
		[2] = dataList[4],
	},
	[2509] = {
		[1] = dataList[5],
		[2] = dataList[6],
	},
}

t_act_429_order.dataList = dataList
local mt
if Act429OrderDefine then
	mt = {
		__cname =  "Act429OrderDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act429OrderDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act_429_order