module("logic.extensions.tetris.game.view.TetrisGamePreviewShape",package.seeall)

local TetrisGamePreviewShape = class("TetrisGamePreviewShape")

function TetrisGamePreviewShape:ctor(go,index)
	self._go = go
	self._index = index
	self:Awake()
end

function TetrisGamePreviewShape:Awake()
	self._selectGo = goutil.findChild(self._go,"select")
	--self._selectGo:SetActive(false)
	self._shapeGo = goutil.findChild(self._go,"normalshape")
	self._blockList = {}
	self._blockIcons = {}
	for i = 1, 4 do
		local child = goutil.findChild(self._shapeGo,"block" .. i)
		local icon = goutil.findChild(child,"icon")
		table.insert(self._blockList,child)
		table.insert(self._blockIcons,icon)
	end
end

--刷新
function TetrisGamePreviewShape:refresh(shapeType)
	for i, block in ipairs(self._blockList) do
		block:SetActive(false)
	end 
	-- goutil.setActive(self._selectGo,self._index == 1)
	goutil.setActive(self._shapeGo,true)
	--特殊处理9号砖块
	--因为是6个格的砖块，无论是面板、显示、数据都是要特殊处理的
	--所以在这里直接特殊处理
	if shapeType == 9 then
		shapeType = 2
	end

	self._shapeInfo = TetrisNotify.shapeInfos[shapeType]
	local pivot = self._shapeInfo.pivot	--以第几个砖块为中心点
	-- local offsetX = pivot ~= 5 and pivot or 2

	local index = self._shapeInfo.count
	local blockHalfW = TetrisNotify.blockSize / 2
	local totalRow = 16 / self._shapeInfo.baseCount - 1
	local firstX = 0
	local lastX = 0
	local firstY = 0
	local lastY = 0
	local hasMultiRow = false
	local curRow = 0
	for i = 0, 15 do
		local isTrue = bitutil.checkBitValue(self._shapeInfo.shape,i)
		if isTrue then
			-- 根据位位置计算行和列
			local row = totalRow - math.floor(i / self._shapeInfo.baseCount) -- 行索引
			local c = i % self._shapeInfo.baseCount -- 列索引
			local columns = 3 - c - self._shapeInfo.offsetX -- 根据水平偏移进行调整

			local x = columns * TetrisNotify.blockSize + blockHalfW
			local y = row * TetrisNotify.blockSize + blockHalfW
			local go = self._blockList[index]
			go:SetActive(true)
			GameUtils.setLocalPos(go,x,y,0)

			if index == 1 then
				firstX = firstX == 0 and x or firstX
				firstY = firstY == 0 and y or firstY
			end
			if index == self._shapeInfo.count then
				lastX = lastX == 0 and x or lastX
				lastY = lastY == 0 and y or lastY
			end
			if curRow == 0 then
				curRow = row
			else
				if curRow ~= row then
					hasMultiRow = true
				end
			end

			if self._shapeInfo.texture then
				if self._blockIcons[index] then
					IconLoader.setSpriteToImg(self._blockIcons[index],GameUrl.getTetrisShapePath(self._shapeInfo.texture))
				else
					printError(self._shapeInfo.texture)
				end
			end
			index = index - 1
		end
	end
	--纠正位置
	local offsetX = 0
	local offsetY = 0
	if firstY == lastY then
		offsetY = lastY/2 * -1
	else
		offsetY = (firstY - lastY)/2
	end
	if pivot ~= 5 and hasMultiRow then
		offsetX = self._blockList[pivot].transform.localPosition.x
		for i = 1, #self._blockList do
			local pos = self._blockList[i].transform.localPosition
			GameUtils.setLocalPos(self._blockList[i],pos.x - offsetX,pos.y,0)
		end
	end
	GameUtils.setLocalPos(self._shapeGo,0,offsetY,0)
end

function TetrisGamePreviewShape:onDestory()
	self._blockList = nil
	self._blockIcons = nil
end

return TetrisGamePreviewShape