-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity462Extension_pb", package.seeall)


local tb = {}
tb.FLYINGCHESSUSERSTATETYPE_ENUM = protobuf.EnumDescriptor()
tb.FLYINGCHESSUSERSTATETYPE_NORMAL_ENUMITEM = protobuf.EnumValueDescriptor()
tb.FLYINGCHESSUSERSTATETYPE_AUTO_ENUMITEM = protobuf.EnumValueDescriptor()
tb.FLYINGCHESSUSERSTATETYPE_PASSIVE_ENUMITEM = protobuf.EnumValueDescriptor()
FLYINGCHESSPOSNO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSPOSNO_ID_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSPOSNO_POS_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSSTARTDICEREQUEST_MSG = protobuf.Descriptor()
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSROOMINFONO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSCLIENTCLICKEVENTNO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSEVTMAPREFRESHNO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSUSERENDNO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSUSERENDNO_RANK_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD = protobuf.FieldDescriptor()
GETFLYINGCHESSINFOREQUEST_MSG = protobuf.Descriptor()
FLYINGCHESSDELAYEVENTNO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSCLIENTMOVEENDREPLY_MSG = protobuf.Descriptor()
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG = protobuf.Descriptor()
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG = protobuf.Descriptor()
GETFLYINGCHESSTARGETREWARDREPLY_MSG = protobuf.Descriptor()
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSSELECTMOVECHESSREPLY_MSG = protobuf.Descriptor()
FLYINGCHESSPOINTNO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG = protobuf.Descriptor()
GETFLYINGCHESSDAILYREWARDREPLY_MSG = protobuf.Descriptor()
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
CLICK462CLIENTEVENTREPLY_MSG = protobuf.Descriptor()
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD = protobuf.FieldDescriptor()
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSMOVEROUTENO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG = protobuf.Descriptor()
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG = protobuf.Descriptor()
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD = protobuf.FieldDescriptor()
GETFLYINGCHESSTARGETREWARDREQUEST_MSG = protobuf.Descriptor()
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD = protobuf.FieldDescriptor()
CLICK462CLIENTEVENTREQUEST_MSG = protobuf.Descriptor()
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG = protobuf.Descriptor()
GETFLYINGCHESSDAILYREWARDREQUEST_MSG = protobuf.Descriptor()
FLYINGCHESSENDPUSH_MSG = protobuf.Descriptor()
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD = protobuf.FieldDescriptor()
FLYINGCHESSSTARTDICEREPLY_MSG = protobuf.Descriptor()
FLYINGCHESSONCEMOVENO_MSG = protobuf.Descriptor()
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD = protobuf.FieldDescriptor()
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD = protobuf.FieldDescriptor()
GETFLYINGCHESSINFOREPLY_MSG = protobuf.Descriptor()
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD = protobuf.FieldDescriptor()
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD = protobuf.FieldDescriptor()
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD = protobuf.FieldDescriptor()
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD = protobuf.FieldDescriptor()
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD = protobuf.FieldDescriptor()
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD = protobuf.FieldDescriptor()
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD = protobuf.FieldDescriptor()
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD = protobuf.FieldDescriptor()

tb.FLYINGCHESSUSERSTATETYPE_NORMAL_ENUMITEM.name = "NORMAL"
tb.FLYINGCHESSUSERSTATETYPE_NORMAL_ENUMITEM.index = 0
tb.FLYINGCHESSUSERSTATETYPE_NORMAL_ENUMITEM.number = 1
tb.FLYINGCHESSUSERSTATETYPE_AUTO_ENUMITEM.name = "AUTO"
tb.FLYINGCHESSUSERSTATETYPE_AUTO_ENUMITEM.index = 1
tb.FLYINGCHESSUSERSTATETYPE_AUTO_ENUMITEM.number = 2
tb.FLYINGCHESSUSERSTATETYPE_PASSIVE_ENUMITEM.name = "PASSIVE"
tb.FLYINGCHESSUSERSTATETYPE_PASSIVE_ENUMITEM.index = 2
tb.FLYINGCHESSUSERSTATETYPE_PASSIVE_ENUMITEM.number = 3
tb.FLYINGCHESSUSERSTATETYPE_ENUM.name = "FlyingChessUserStateType"
tb.FLYINGCHESSUSERSTATETYPE_ENUM.full_name = ".FlyingChessUserStateType"
tb.FLYINGCHESSUSERSTATETYPE_ENUM.values = {tb.FLYINGCHESSUSERSTATETYPE_NORMAL_ENUMITEM,tb.FLYINGCHESSUSERSTATETYPE_AUTO_ENUMITEM,tb.FLYINGCHESSUSERSTATETYPE_PASSIVE_ENUMITEM}
tb.FLYINGCHESSPOSNO_ID_FIELD.name = "id"
tb.FLYINGCHESSPOSNO_ID_FIELD.full_name = ".FlyingChessPosNO.id"
tb.FLYINGCHESSPOSNO_ID_FIELD.number = 1
tb.FLYINGCHESSPOSNO_ID_FIELD.index = 0
tb.FLYINGCHESSPOSNO_ID_FIELD.label = 1
tb.FLYINGCHESSPOSNO_ID_FIELD.has_default_value = false
tb.FLYINGCHESSPOSNO_ID_FIELD.default_value = 0
tb.FLYINGCHESSPOSNO_ID_FIELD.type = 5
tb.FLYINGCHESSPOSNO_ID_FIELD.cpp_type = 1

tb.FLYINGCHESSPOSNO_POS_FIELD.name = "pos"
tb.FLYINGCHESSPOSNO_POS_FIELD.full_name = ".FlyingChessPosNO.pos"
tb.FLYINGCHESSPOSNO_POS_FIELD.number = 2
tb.FLYINGCHESSPOSNO_POS_FIELD.index = 1
tb.FLYINGCHESSPOSNO_POS_FIELD.label = 1
tb.FLYINGCHESSPOSNO_POS_FIELD.has_default_value = false
tb.FLYINGCHESSPOSNO_POS_FIELD.default_value = 0
tb.FLYINGCHESSPOSNO_POS_FIELD.type = 5
tb.FLYINGCHESSPOSNO_POS_FIELD.cpp_type = 1

FLYINGCHESSPOSNO_MSG.name = "FlyingChessPosNO"
FLYINGCHESSPOSNO_MSG.full_name = ".FlyingChessPosNO"
FLYINGCHESSPOSNO_MSG.filename = "Activity462Extension"
FLYINGCHESSPOSNO_MSG.nested_types = {}
FLYINGCHESSPOSNO_MSG.enum_types = {}
FLYINGCHESSPOSNO_MSG.fields = {tb.FLYINGCHESSPOSNO_ID_FIELD, tb.FLYINGCHESSPOSNO_POS_FIELD}
FLYINGCHESSPOSNO_MSG.is_extendable = false
FLYINGCHESSPOSNO_MSG.extensions = {}
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.name = "diceTimes"
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.full_name = ".FlyingChessStartDiceRequest.diceTimes"
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.number = 1
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.index = 0
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.label = 1
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.has_default_value = false
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.default_value = 0
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.type = 5
tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.name = "gmPoints"
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.full_name = ".FlyingChessStartDiceRequest.gmPoints"
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.number = 2
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.index = 1
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.label = 1
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.has_default_value = false
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.default_value = 0
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.type = 5
tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD.cpp_type = 1

FLYINGCHESSSTARTDICEREQUEST_MSG.name = "FlyingChessStartDiceRequest"
FLYINGCHESSSTARTDICEREQUEST_MSG.full_name = ".FlyingChessStartDiceRequest"
FLYINGCHESSSTARTDICEREQUEST_MSG.filename = "Activity462Extension"
FLYINGCHESSSTARTDICEREQUEST_MSG.nested_types = {}
FLYINGCHESSSTARTDICEREQUEST_MSG.enum_types = {}
FLYINGCHESSSTARTDICEREQUEST_MSG.fields = {tb.FLYINGCHESSSTARTDICEREQUEST_DICETIMES_FIELD, tb.FLYINGCHESSSTARTDICEREQUEST_GMPOINTS_FIELD}
FLYINGCHESSSTARTDICEREQUEST_MSG.is_extendable = false
FLYINGCHESSSTARTDICEREQUEST_MSG.extensions = {}
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.name = "userTimes"
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.full_name = ".FlyingChessRoomInfoNO.userTimes"
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.number = 1
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.index = 0
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.label = 1
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.has_default_value = false
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.default_value = 0
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.type = 5
tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.name = "diceTimes"
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.full_name = ".FlyingChessRoomInfoNO.diceTimes"
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.number = 2
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.index = 1
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.label = 1
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.has_default_value = false
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.default_value = 0
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.type = 5
tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.name = "dicePoints"
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.full_name = ".FlyingChessRoomInfoNO.dicePoints"
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.number = 3
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.index = 2
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.label = 1
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.has_default_value = false
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.default_value = 0
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.type = 5
tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD.cpp_type = 1

tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.name = "nextAutoTime"
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.full_name = ".FlyingChessRoomInfoNO.nextAutoTime"
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.number = 4
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.index = 3
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.label = 1
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.has_default_value = false
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.default_value = 0
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.type = 5
tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD.cpp_type = 1

tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.name = "chessPos"
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.full_name = ".FlyingChessRoomInfoNO.chessPos"
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.number = 5
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.index = 4
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.label = 3
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.has_default_value = false
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.default_value = {}
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.message_type = FLYINGCHESSPOSNO_MSG
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.type = 11
tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD.cpp_type = 10

tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.name = "eventPos"
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.full_name = ".FlyingChessRoomInfoNO.eventPos"
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.number = 6
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.index = 5
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.label = 3
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.has_default_value = false
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.default_value = {}
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.message_type = FLYINGCHESSPOSNO_MSG
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.type = 11
tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD.cpp_type = 10

tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.name = "clientEventPos"
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.full_name = ".FlyingChessRoomInfoNO.clientEventPos"
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.number = 7
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.index = 6
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.label = 3
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.has_default_value = false
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.default_value = {}
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.message_type = FLYINGCHESSPOSNO_MSG
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.type = 11
tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD.cpp_type = 10

FLYINGCHESSROOMINFONO_MSG.name = "FlyingChessRoomInfoNO"
FLYINGCHESSROOMINFONO_MSG.full_name = ".FlyingChessRoomInfoNO"
FLYINGCHESSROOMINFONO_MSG.filename = "Activity462Extension"
FLYINGCHESSROOMINFONO_MSG.nested_types = {}
FLYINGCHESSROOMINFONO_MSG.enum_types = {}
FLYINGCHESSROOMINFONO_MSG.fields = {tb.FLYINGCHESSROOMINFONO_USERTIMES_FIELD, tb.FLYINGCHESSROOMINFONO_DICETIMES_FIELD, tb.FLYINGCHESSROOMINFONO_DICEPOINTS_FIELD, tb.FLYINGCHESSROOMINFONO_NEXTAUTOTIME_FIELD, tb.FLYINGCHESSROOMINFONO_CHESSPOS_FIELD, tb.FLYINGCHESSROOMINFONO_EVENTPOS_FIELD, tb.FLYINGCHESSROOMINFONO_CLIENTEVENTPOS_FIELD}
FLYINGCHESSROOMINFONO_MSG.is_extendable = false
FLYINGCHESSROOMINFONO_MSG.extensions = {}
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.name = "gridId"
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.full_name = ".FlyingChessClientClickEventNO.gridId"
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.number = 1
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.index = 0
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.label = 1
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.has_default_value = false
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.default_value = 0
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.type = 5
tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD.cpp_type = 1

tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.name = "eventId"
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.full_name = ".FlyingChessClientClickEventNO.eventId"
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.number = 2
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.index = 1
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.label = 1
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.has_default_value = false
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.default_value = 0
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.type = 5
tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD.cpp_type = 1

FLYINGCHESSCLIENTCLICKEVENTNO_MSG.name = "FlyingChessClientClickEventNO"
FLYINGCHESSCLIENTCLICKEVENTNO_MSG.full_name = ".FlyingChessClientClickEventNO"
FLYINGCHESSCLIENTCLICKEVENTNO_MSG.filename = "Activity462Extension"
FLYINGCHESSCLIENTCLICKEVENTNO_MSG.nested_types = {}
FLYINGCHESSCLIENTCLICKEVENTNO_MSG.enum_types = {}
FLYINGCHESSCLIENTCLICKEVENTNO_MSG.fields = {tb.FLYINGCHESSCLIENTCLICKEVENTNO_GRIDID_FIELD, tb.FLYINGCHESSCLIENTCLICKEVENTNO_EVENTID_FIELD}
FLYINGCHESSCLIENTCLICKEVENTNO_MSG.is_extendable = false
FLYINGCHESSCLIENTCLICKEVENTNO_MSG.extensions = {}
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.name = "diceTimes"
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.full_name = ".FlyingChessEvtMapRefreshNO.diceTimes"
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.number = 1
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.index = 0
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.label = 1
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.has_default_value = false
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.default_value = 0
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.type = 5
tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.name = "evtPosNos"
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.full_name = ".FlyingChessEvtMapRefreshNO.evtPosNos"
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.number = 2
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.index = 1
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.label = 3
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.has_default_value = false
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.default_value = {}
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.message_type = FLYINGCHESSPOSNO_MSG
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.type = 11
tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD.cpp_type = 10

FLYINGCHESSEVTMAPREFRESHNO_MSG.name = "FlyingChessEvtMapRefreshNO"
FLYINGCHESSEVTMAPREFRESHNO_MSG.full_name = ".FlyingChessEvtMapRefreshNO"
FLYINGCHESSEVTMAPREFRESHNO_MSG.filename = "Activity462Extension"
FLYINGCHESSEVTMAPREFRESHNO_MSG.nested_types = {}
FLYINGCHESSEVTMAPREFRESHNO_MSG.enum_types = {}
FLYINGCHESSEVTMAPREFRESHNO_MSG.fields = {tb.FLYINGCHESSEVTMAPREFRESHNO_DICETIMES_FIELD, tb.FLYINGCHESSEVTMAPREFRESHNO_EVTPOSNOS_FIELD}
FLYINGCHESSEVTMAPREFRESHNO_MSG.is_extendable = false
FLYINGCHESSEVTMAPREFRESHNO_MSG.extensions = {}
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.name = "userNetId"
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.full_name = ".FlyingChessUserEndNO.userNetId"
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.number = 1
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.index = 0
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.label = 1
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.has_default_value = false
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.default_value = ""
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.type = 9
tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD.cpp_type = 9

tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.name = "score"
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.full_name = ".FlyingChessUserEndNO.score"
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.number = 2
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.index = 1
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.label = 1
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.has_default_value = false
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.default_value = 0
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.type = 5
tb.FLYINGCHESSUSERENDNO_SCORE_FIELD.cpp_type = 1

tb.FLYINGCHESSUSERENDNO_RANK_FIELD.name = "rank"
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.full_name = ".FlyingChessUserEndNO.rank"
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.number = 3
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.index = 2
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.label = 1
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.has_default_value = false
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.default_value = 0
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.type = 5
tb.FLYINGCHESSUSERENDNO_RANK_FIELD.cpp_type = 1

tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.name = "isNoOperateAuto"
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.full_name = ".FlyingChessUserEndNO.isNoOperateAuto"
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.number = 4
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.index = 3
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.label = 1
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.has_default_value = false
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.default_value = false
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.type = 8
tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD.cpp_type = 7

tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.name = "endChessNum"
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.full_name = ".FlyingChessUserEndNO.endChessNum"
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.number = 5
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.index = 4
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.label = 1
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.has_default_value = false
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.default_value = 0
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.type = 5
tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD.cpp_type = 1

tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.name = "isExitGame"
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.full_name = ".FlyingChessUserEndNO.isExitGame"
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.number = 6
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.index = 5
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.label = 1
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.has_default_value = false
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.default_value = false
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.type = 8
tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD.cpp_type = 7

FLYINGCHESSUSERENDNO_MSG.name = "FlyingChessUserEndNO"
FLYINGCHESSUSERENDNO_MSG.full_name = ".FlyingChessUserEndNO"
FLYINGCHESSUSERENDNO_MSG.filename = "Activity462Extension"
FLYINGCHESSUSERENDNO_MSG.nested_types = {}
FLYINGCHESSUSERENDNO_MSG.enum_types = {}
FLYINGCHESSUSERENDNO_MSG.fields = {tb.FLYINGCHESSUSERENDNO_USERNETID_FIELD, tb.FLYINGCHESSUSERENDNO_SCORE_FIELD, tb.FLYINGCHESSUSERENDNO_RANK_FIELD, tb.FLYINGCHESSUSERENDNO_ISNOOPERATEAUTO_FIELD, tb.FLYINGCHESSUSERENDNO_ENDCHESSNUM_FIELD, tb.FLYINGCHESSUSERENDNO_ISEXITGAME_FIELD}
FLYINGCHESSUSERENDNO_MSG.is_extendable = false
FLYINGCHESSUSERENDNO_MSG.extensions = {}
GETFLYINGCHESSINFOREQUEST_MSG.name = "GetFlyingChessInfoRequest"
GETFLYINGCHESSINFOREQUEST_MSG.full_name = ".GetFlyingChessInfoRequest"
GETFLYINGCHESSINFOREQUEST_MSG.filename = "Activity462Extension"
GETFLYINGCHESSINFOREQUEST_MSG.nested_types = {}
GETFLYINGCHESSINFOREQUEST_MSG.enum_types = {}
GETFLYINGCHESSINFOREQUEST_MSG.fields = {}
GETFLYINGCHESSINFOREQUEST_MSG.is_extendable = false
GETFLYINGCHESSINFOREQUEST_MSG.extensions = {}
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.name = "realEventTimes"
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.full_name = ".FlyingChessDelayEventNO.realEventTimes"
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.number = 1
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.index = 0
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.label = 1
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.has_default_value = false
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.default_value = 0
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.type = 5
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.name = "realEventId"
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.full_name = ".FlyingChessDelayEventNO.realEventId"
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.number = 2
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.index = 1
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.label = 1
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.has_default_value = false
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.default_value = 0
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.type = 5
tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD.cpp_type = 1

FLYINGCHESSDELAYEVENTNO_MSG.name = "FlyingChessDelayEventNO"
FLYINGCHESSDELAYEVENTNO_MSG.full_name = ".FlyingChessDelayEventNO"
FLYINGCHESSDELAYEVENTNO_MSG.filename = "Activity462Extension"
FLYINGCHESSDELAYEVENTNO_MSG.nested_types = {}
FLYINGCHESSDELAYEVENTNO_MSG.enum_types = {}
FLYINGCHESSDELAYEVENTNO_MSG.fields = {tb.FLYINGCHESSDELAYEVENTNO_REALEVENTTIMES_FIELD, tb.FLYINGCHESSDELAYEVENTNO_REALEVENTID_FIELD}
FLYINGCHESSDELAYEVENTNO_MSG.is_extendable = false
FLYINGCHESSDELAYEVENTNO_MSG.extensions = {}
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.name = "FlyingChessClientMoveEndReply"
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.full_name = ".FlyingChessClientMoveEndReply"
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.filename = "Activity462Extension"
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.nested_types = {}
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.enum_types = {}
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.fields = {}
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.is_extendable = false
FLYINGCHESSCLIENTMOVEENDREPLY_MSG.extensions = {}
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.name = "moveChessId"
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.full_name = ".FlyingChessSelectMoveChessRequest.moveChessId"
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.number = 1
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.index = 0
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.label = 3
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.has_default_value = false
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.default_value = {}
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.type = 5
tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD.cpp_type = 1

FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.name = "FlyingChessSelectMoveChessRequest"
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.full_name = ".FlyingChessSelectMoveChessRequest"
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.filename = "Activity462Extension"
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.nested_types = {}
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.enum_types = {}
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.fields = {tb.FLYINGCHESSSELECTMOVECHESSREQUEST_MOVECHESSID_FIELD}
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.is_extendable = false
FLYINGCHESSSELECTMOVECHESSREQUEST_MSG.extensions = {}
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.name = "FlyingChessChangeAutoStateRequest"
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.full_name = ".FlyingChessChangeAutoStateRequest"
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.filename = "Activity462Extension"
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.nested_types = {}
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.enum_types = {}
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.fields = {}
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.is_extendable = false
FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG.extensions = {}
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.full_name = ".GetFlyingChessTargetRewardReply.changeSetId"
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GETFLYINGCHESSTARGETREWARDREPLY_MSG.name = "GetFlyingChessTargetRewardReply"
GETFLYINGCHESSTARGETREWARDREPLY_MSG.full_name = ".GetFlyingChessTargetRewardReply"
GETFLYINGCHESSTARGETREWARDREPLY_MSG.filename = "Activity462Extension"
GETFLYINGCHESSTARGETREWARDREPLY_MSG.nested_types = {}
GETFLYINGCHESSTARGETREWARDREPLY_MSG.enum_types = {}
GETFLYINGCHESSTARGETREWARDREPLY_MSG.fields = {tb.GETFLYINGCHESSTARGETREWARDREPLY_CHANGESETID_FIELD}
GETFLYINGCHESSTARGETREWARDREPLY_MSG.is_extendable = false
GETFLYINGCHESSTARGETREWARDREPLY_MSG.extensions = {}
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.name = "FlyingChessSelectMoveChessReply"
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.full_name = ".FlyingChessSelectMoveChessReply"
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.filename = "Activity462Extension"
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.nested_types = {}
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.enum_types = {}
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.fields = {}
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.is_extendable = false
FLYINGCHESSSELECTMOVECHESSREPLY_MSG.extensions = {}
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.name = "userTimes"
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.full_name = ".FlyingChessPointNO.userTimes"
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.number = 1
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.index = 0
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.label = 1
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.has_default_value = false
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.default_value = 0
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.type = 5
tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.name = "diceTimes"
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.full_name = ".FlyingChessPointNO.diceTimes"
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.number = 2
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.index = 1
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.label = 1
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.has_default_value = false
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.default_value = 0
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.type = 5
tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.name = "dicePoints"
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.full_name = ".FlyingChessPointNO.dicePoints"
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.number = 3
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.index = 2
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.label = 1
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.has_default_value = false
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.default_value = 0
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.type = 5
tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD.cpp_type = 1

tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.name = "nextUserTimes"
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.full_name = ".FlyingChessPointNO.nextUserTimes"
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.number = 4
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.index = 3
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.label = 1
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.has_default_value = false
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.default_value = 0
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.type = 5
tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD.cpp_type = 1

FLYINGCHESSPOINTNO_MSG.name = "FlyingChessPointNO"
FLYINGCHESSPOINTNO_MSG.full_name = ".FlyingChessPointNO"
FLYINGCHESSPOINTNO_MSG.filename = "Activity462Extension"
FLYINGCHESSPOINTNO_MSG.nested_types = {}
FLYINGCHESSPOINTNO_MSG.enum_types = {}
FLYINGCHESSPOINTNO_MSG.fields = {tb.FLYINGCHESSPOINTNO_USERTIMES_FIELD, tb.FLYINGCHESSPOINTNO_DICETIMES_FIELD, tb.FLYINGCHESSPOINTNO_DICEPOINTS_FIELD, tb.FLYINGCHESSPOINTNO_NEXTUSERTIMES_FIELD}
FLYINGCHESSPOINTNO_MSG.is_extendable = false
FLYINGCHESSPOINTNO_MSG.extensions = {}
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.name = "FlyingChessChangeAppearanceReply"
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.full_name = ".FlyingChessChangeAppearanceReply"
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.filename = "Activity462Extension"
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.nested_types = {}
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.enum_types = {}
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.fields = {}
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.is_extendable = false
FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG.extensions = {}
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.full_name = ".GetFlyingChessDailyRewardReply.changeSetId"
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GETFLYINGCHESSDAILYREWARDREPLY_MSG.name = "GetFlyingChessDailyRewardReply"
GETFLYINGCHESSDAILYREWARDREPLY_MSG.full_name = ".GetFlyingChessDailyRewardReply"
GETFLYINGCHESSDAILYREWARDREPLY_MSG.filename = "Activity462Extension"
GETFLYINGCHESSDAILYREWARDREPLY_MSG.nested_types = {}
GETFLYINGCHESSDAILYREWARDREPLY_MSG.enum_types = {}
GETFLYINGCHESSDAILYREWARDREPLY_MSG.fields = {tb.GETFLYINGCHESSDAILYREWARDREPLY_CHANGESETID_FIELD}
GETFLYINGCHESSDAILYREWARDREPLY_MSG.is_extendable = false
GETFLYINGCHESSDAILYREWARDREPLY_MSG.extensions = {}
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.name = "eventId"
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.full_name = ".Click462ClientEventReply.eventId"
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.number = 1
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.index = 0
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.label = 1
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.has_default_value = false
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.default_value = 0
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.type = 5
tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD.cpp_type = 1

tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.full_name = ".Click462ClientEventReply.changeSetId"
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.number = 2
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.index = 1
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.label = 1
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.has_default_value = false
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.default_value = 0
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.type = 5
tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD.cpp_type = 1

CLICK462CLIENTEVENTREPLY_MSG.name = "Click462ClientEventReply"
CLICK462CLIENTEVENTREPLY_MSG.full_name = ".Click462ClientEventReply"
CLICK462CLIENTEVENTREPLY_MSG.filename = "Activity462Extension"
CLICK462CLIENTEVENTREPLY_MSG.nested_types = {}
CLICK462CLIENTEVENTREPLY_MSG.enum_types = {}
CLICK462CLIENTEVENTREPLY_MSG.fields = {tb.CLICK462CLIENTEVENTREPLY_EVENTID_FIELD, tb.CLICK462CLIENTEVENTREPLY_CHANGESETID_FIELD}
CLICK462CLIENTEVENTREPLY_MSG.is_extendable = false
CLICK462CLIENTEVENTREPLY_MSG.extensions = {}
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.name = "userTimes"
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.full_name = ".FlyingChessMoveRouteNO.userTimes"
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.number = 1
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.index = 0
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.label = 1
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.has_default_value = false
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.default_value = 0
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.type = 5
tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.name = "diceTimes"
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.full_name = ".FlyingChessMoveRouteNO.diceTimes"
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.number = 2
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.index = 1
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.label = 1
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.has_default_value = false
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.default_value = 0
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.type = 5
tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.name = "moveRouts"
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.full_name = ".FlyingChessMoveRouteNO.moveRouts"
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.number = 3
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.index = 2
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.label = 3
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.has_default_value = false
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.default_value = {}
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.message_type = FLYINGCHESSONCEMOVENO_MSG
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.type = 11
tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD.cpp_type = 10

tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.name = "nextUserTimes"
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.full_name = ".FlyingChessMoveRouteNO.nextUserTimes"
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.number = 4
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.index = 3
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.label = 1
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.has_default_value = false
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.default_value = 0
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.type = 5
tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD.cpp_type = 1

tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.name = "moveChessId"
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.full_name = ".FlyingChessMoveRouteNO.moveChessId"
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.number = 5
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.index = 4
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.label = 3
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.has_default_value = false
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.default_value = {}
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.type = 5
tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD.cpp_type = 1

FLYINGCHESSMOVEROUTENO_MSG.name = "FlyingChessMoveRouteNO"
FLYINGCHESSMOVEROUTENO_MSG.full_name = ".FlyingChessMoveRouteNO"
FLYINGCHESSMOVEROUTENO_MSG.filename = "Activity462Extension"
FLYINGCHESSMOVEROUTENO_MSG.nested_types = {}
FLYINGCHESSMOVEROUTENO_MSG.enum_types = {}
FLYINGCHESSMOVEROUTENO_MSG.fields = {tb.FLYINGCHESSMOVEROUTENO_USERTIMES_FIELD, tb.FLYINGCHESSMOVEROUTENO_DICETIMES_FIELD, tb.FLYINGCHESSMOVEROUTENO_MOVEROUTS_FIELD, tb.FLYINGCHESSMOVEROUTENO_NEXTUSERTIMES_FIELD, tb.FLYINGCHESSMOVEROUTENO_MOVECHESSID_FIELD}
FLYINGCHESSMOVEROUTENO_MSG.is_extendable = false
FLYINGCHESSMOVEROUTENO_MSG.extensions = {}
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.name = "endDiceTimes"
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.full_name = ".FlyingChessClientMoveEndRequest.endDiceTimes"
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.number = 1
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.index = 0
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.label = 1
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.has_default_value = false
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.default_value = 0
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.type = 5
tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD.cpp_type = 1

FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.name = "FlyingChessClientMoveEndRequest"
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.full_name = ".FlyingChessClientMoveEndRequest"
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.filename = "Activity462Extension"
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.nested_types = {}
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.enum_types = {}
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.fields = {tb.FLYINGCHESSCLIENTMOVEENDREQUEST_ENDDICETIMES_FIELD}
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.is_extendable = false
FLYINGCHESSCLIENTMOVEENDREQUEST_MSG.extensions = {}
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.name = "appearanceId"
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.full_name = ".FlyingChessChangeAppearanceRequest.appearanceId"
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.number = 1
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.index = 0
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.label = 1
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.has_default_value = false
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.default_value = 0
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.type = 5
tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD.cpp_type = 1

FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.name = "FlyingChessChangeAppearanceRequest"
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.full_name = ".FlyingChessChangeAppearanceRequest"
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.filename = "Activity462Extension"
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.nested_types = {}
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.enum_types = {}
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.fields = {tb.FLYINGCHESSCHANGEAPPEARANCEREQUEST_APPEARANCEID_FIELD}
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.is_extendable = false
FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG.extensions = {}
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.name = "rewardId"
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.full_name = ".GetFlyingChessTargetRewardRequest.rewardId"
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.number = 1
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.index = 0
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.label = 1
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.has_default_value = false
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.default_value = 0
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.type = 5
tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD.cpp_type = 1

GETFLYINGCHESSTARGETREWARDREQUEST_MSG.name = "GetFlyingChessTargetRewardRequest"
GETFLYINGCHESSTARGETREWARDREQUEST_MSG.full_name = ".GetFlyingChessTargetRewardRequest"
GETFLYINGCHESSTARGETREWARDREQUEST_MSG.filename = "Activity462Extension"
GETFLYINGCHESSTARGETREWARDREQUEST_MSG.nested_types = {}
GETFLYINGCHESSTARGETREWARDREQUEST_MSG.enum_types = {}
GETFLYINGCHESSTARGETREWARDREQUEST_MSG.fields = {tb.GETFLYINGCHESSTARGETREWARDREQUEST_REWARDID_FIELD}
GETFLYINGCHESSTARGETREWARDREQUEST_MSG.is_extendable = false
GETFLYINGCHESSTARGETREWARDREQUEST_MSG.extensions = {}
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.name = "gridId"
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.full_name = ".Click462ClientEventRequest.gridId"
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.number = 1
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.index = 0
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.label = 1
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.has_default_value = false
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.default_value = 0
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.type = 5
tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD.cpp_type = 1

CLICK462CLIENTEVENTREQUEST_MSG.name = "Click462ClientEventRequest"
CLICK462CLIENTEVENTREQUEST_MSG.full_name = ".Click462ClientEventRequest"
CLICK462CLIENTEVENTREQUEST_MSG.filename = "Activity462Extension"
CLICK462CLIENTEVENTREQUEST_MSG.nested_types = {}
CLICK462CLIENTEVENTREQUEST_MSG.enum_types = {}
CLICK462CLIENTEVENTREQUEST_MSG.fields = {tb.CLICK462CLIENTEVENTREQUEST_GRIDID_FIELD}
CLICK462CLIENTEVENTREQUEST_MSG.is_extendable = false
CLICK462CLIENTEVENTREQUEST_MSG.extensions = {}
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.name = "FlyingChessChangeAutoStateReply"
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.full_name = ".FlyingChessChangeAutoStateReply"
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.filename = "Activity462Extension"
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.nested_types = {}
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.enum_types = {}
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.fields = {}
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.is_extendable = false
FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG.extensions = {}
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.name = "GetFlyingChessDailyRewardRequest"
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.full_name = ".GetFlyingChessDailyRewardRequest"
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.filename = "Activity462Extension"
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.nested_types = {}
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.enum_types = {}
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.fields = {}
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.is_extendable = false
GETFLYINGCHESSDAILYREWARDREQUEST_MSG.extensions = {}
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.name = "userEndNos"
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.full_name = ".FlyingChessEndPush.userEndNos"
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.number = 1
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.index = 0
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.label = 3
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.has_default_value = false
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.default_value = {}
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.message_type = FLYINGCHESSUSERENDNO_MSG
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.type = 11
tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD.cpp_type = 10

FLYINGCHESSENDPUSH_MSG.name = "FlyingChessEndPush"
FLYINGCHESSENDPUSH_MSG.full_name = ".FlyingChessEndPush"
FLYINGCHESSENDPUSH_MSG.filename = "Activity462Extension"
FLYINGCHESSENDPUSH_MSG.nested_types = {}
FLYINGCHESSENDPUSH_MSG.enum_types = {}
FLYINGCHESSENDPUSH_MSG.fields = {tb.FLYINGCHESSENDPUSH_USERENDNOS_FIELD}
FLYINGCHESSENDPUSH_MSG.is_extendable = false
FLYINGCHESSENDPUSH_MSG.extensions = {}
FLYINGCHESSSTARTDICEREPLY_MSG.name = "FlyingChessStartDiceReply"
FLYINGCHESSSTARTDICEREPLY_MSG.full_name = ".FlyingChessStartDiceReply"
FLYINGCHESSSTARTDICEREPLY_MSG.filename = "Activity462Extension"
FLYINGCHESSSTARTDICEREPLY_MSG.nested_types = {}
FLYINGCHESSSTARTDICEREPLY_MSG.enum_types = {}
FLYINGCHESSSTARTDICEREPLY_MSG.fields = {}
FLYINGCHESSSTARTDICEREPLY_MSG.is_extendable = false
FLYINGCHESSSTARTDICEREPLY_MSG.extensions = {}
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.name = "beginPos"
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.full_name = ".FlyingChessOnceMoveNO.beginPos"
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.number = 1
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.index = 0
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.label = 1
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.has_default_value = false
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.default_value = 0
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.type = 5
tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD.cpp_type = 1

tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.name = "baseMoveType"
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.full_name = ".FlyingChessOnceMoveNO.baseMoveType"
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.number = 2
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.index = 1
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.label = 1
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.has_default_value = false
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.default_value = 0
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.type = 5
tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD.cpp_type = 1

tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.name = "crashChessId"
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.full_name = ".FlyingChessOnceMoveNO.crashChessId"
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.number = 3
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.index = 2
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.label = 3
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.has_default_value = false
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.default_value = {}
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.type = 5
tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD.cpp_type = 1

tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.name = "specialEventId"
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.full_name = ".FlyingChessOnceMoveNO.specialEventId"
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.number = 4
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.index = 3
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.label = 1
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.has_default_value = false
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.default_value = 0
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.type = 5
tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD.cpp_type = 1

FLYINGCHESSONCEMOVENO_MSG.name = "FlyingChessOnceMoveNO"
FLYINGCHESSONCEMOVENO_MSG.full_name = ".FlyingChessOnceMoveNO"
FLYINGCHESSONCEMOVENO_MSG.filename = "Activity462Extension"
FLYINGCHESSONCEMOVENO_MSG.nested_types = {}
FLYINGCHESSONCEMOVENO_MSG.enum_types = {}
FLYINGCHESSONCEMOVENO_MSG.fields = {tb.FLYINGCHESSONCEMOVENO_BEGINPOS_FIELD, tb.FLYINGCHESSONCEMOVENO_BASEMOVETYPE_FIELD, tb.FLYINGCHESSONCEMOVENO_CRASHCHESSID_FIELD, tb.FLYINGCHESSONCEMOVENO_SPECIALEVENTID_FIELD}
FLYINGCHESSONCEMOVENO_MSG.is_extendable = false
FLYINGCHESSONCEMOVENO_MSG.extensions = {}
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.name = "gameTimes"
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.full_name = ".GetFlyingChessInfoReply.gameTimes"
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.number = 1
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.index = 0
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.label = 1
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.default_value = 0
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.type = 5
tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD.cpp_type = 1

tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.name = "matchRankScore"
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.full_name = ".GetFlyingChessInfoReply.matchRankScore"
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.number = 2
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.index = 1
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.label = 1
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.default_value = 0
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.type = 5
tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD.cpp_type = 1

tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.name = "dailyEffectTimes"
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.full_name = ".GetFlyingChessInfoReply.dailyEffectTimes"
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.number = 3
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.index = 2
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.label = 1
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.default_value = 0
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.type = 5
tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD.cpp_type = 1

tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.name = "dailyHasMatchGame"
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.full_name = ".GetFlyingChessInfoReply.dailyHasMatchGame"
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.number = 4
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.index = 3
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.label = 1
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.default_value = false
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.type = 8
tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD.cpp_type = 7

tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.name = "isGetDailyMatchReward"
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.full_name = ".GetFlyingChessInfoReply.isGetDailyMatchReward"
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.number = 5
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.index = 4
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.label = 1
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.default_value = false
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.type = 8
tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD.cpp_type = 7

tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.name = "hasGetRewards"
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.full_name = ".GetFlyingChessInfoReply.hasGetRewards"
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.number = 6
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.index = 5
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.label = 3
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.default_value = {}
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.type = 5
tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD.cpp_type = 1

tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.name = "appearanceId"
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.full_name = ".GetFlyingChessInfoReply.appearanceId"
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.number = 7
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.index = 6
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.label = 1
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.default_value = 0
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.type = 5
tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD.cpp_type = 1

tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.name = "unlockAppearanceId"
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.full_name = ".GetFlyingChessInfoReply.unlockAppearanceId"
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.number = 8
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.index = 7
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.label = 3
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.has_default_value = false
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.default_value = {}
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.type = 5
tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD.cpp_type = 1

GETFLYINGCHESSINFOREPLY_MSG.name = "GetFlyingChessInfoReply"
GETFLYINGCHESSINFOREPLY_MSG.full_name = ".GetFlyingChessInfoReply"
GETFLYINGCHESSINFOREPLY_MSG.filename = "Activity462Extension"
GETFLYINGCHESSINFOREPLY_MSG.nested_types = {}
GETFLYINGCHESSINFOREPLY_MSG.enum_types = {}
GETFLYINGCHESSINFOREPLY_MSG.fields = {tb.GETFLYINGCHESSINFOREPLY_GAMETIMES_FIELD, tb.GETFLYINGCHESSINFOREPLY_MATCHRANKSCORE_FIELD, tb.GETFLYINGCHESSINFOREPLY_DAILYEFFECTTIMES_FIELD, tb.GETFLYINGCHESSINFOREPLY_DAILYHASMATCHGAME_FIELD, tb.GETFLYINGCHESSINFOREPLY_ISGETDAILYMATCHREWARD_FIELD, tb.GETFLYINGCHESSINFOREPLY_HASGETREWARDS_FIELD, tb.GETFLYINGCHESSINFOREPLY_APPEARANCEID_FIELD, tb.GETFLYINGCHESSINFOREPLY_UNLOCKAPPEARANCEID_FIELD}
GETFLYINGCHESSINFOREPLY_MSG.is_extendable = false
GETFLYINGCHESSINFOREPLY_MSG.extensions = {}

AUTO = 2
Click462ClientEventReply = protobuf.Message(CLICK462CLIENTEVENTREPLY_MSG)
Click462ClientEventRequest = protobuf.Message(CLICK462CLIENTEVENTREQUEST_MSG)
FlyingChessChangeAppearanceReply = protobuf.Message(FLYINGCHESSCHANGEAPPEARANCEREPLY_MSG)
FlyingChessChangeAppearanceRequest = protobuf.Message(FLYINGCHESSCHANGEAPPEARANCEREQUEST_MSG)
FlyingChessChangeAutoStateReply = protobuf.Message(FLYINGCHESSCHANGEAUTOSTATEREPLY_MSG)
FlyingChessChangeAutoStateRequest = protobuf.Message(FLYINGCHESSCHANGEAUTOSTATEREQUEST_MSG)
FlyingChessClientClickEventNO = protobuf.Message(FLYINGCHESSCLIENTCLICKEVENTNO_MSG)
FlyingChessClientMoveEndReply = protobuf.Message(FLYINGCHESSCLIENTMOVEENDREPLY_MSG)
FlyingChessClientMoveEndRequest = protobuf.Message(FLYINGCHESSCLIENTMOVEENDREQUEST_MSG)
FlyingChessDelayEventNO = protobuf.Message(FLYINGCHESSDELAYEVENTNO_MSG)
FlyingChessEndPush = protobuf.Message(FLYINGCHESSENDPUSH_MSG)
FlyingChessEvtMapRefreshNO = protobuf.Message(FLYINGCHESSEVTMAPREFRESHNO_MSG)
FlyingChessMoveRouteNO = protobuf.Message(FLYINGCHESSMOVEROUTENO_MSG)
FlyingChessOnceMoveNO = protobuf.Message(FLYINGCHESSONCEMOVENO_MSG)
FlyingChessPointNO = protobuf.Message(FLYINGCHESSPOINTNO_MSG)
FlyingChessPosNO = protobuf.Message(FLYINGCHESSPOSNO_MSG)
FlyingChessRoomInfoNO = protobuf.Message(FLYINGCHESSROOMINFONO_MSG)
FlyingChessSelectMoveChessReply = protobuf.Message(FLYINGCHESSSELECTMOVECHESSREPLY_MSG)
FlyingChessSelectMoveChessRequest = protobuf.Message(FLYINGCHESSSELECTMOVECHESSREQUEST_MSG)
FlyingChessStartDiceReply = protobuf.Message(FLYINGCHESSSTARTDICEREPLY_MSG)
FlyingChessStartDiceRequest = protobuf.Message(FLYINGCHESSSTARTDICEREQUEST_MSG)
FlyingChessUserEndNO = protobuf.Message(FLYINGCHESSUSERENDNO_MSG)
GetFlyingChessDailyRewardReply = protobuf.Message(GETFLYINGCHESSDAILYREWARDREPLY_MSG)
GetFlyingChessDailyRewardRequest = protobuf.Message(GETFLYINGCHESSDAILYREWARDREQUEST_MSG)
GetFlyingChessInfoReply = protobuf.Message(GETFLYINGCHESSINFOREPLY_MSG)
GetFlyingChessInfoRequest = protobuf.Message(GETFLYINGCHESSINFOREQUEST_MSG)
GetFlyingChessTargetRewardReply = protobuf.Message(GETFLYINGCHESSTARGETREWARDREPLY_MSG)
GetFlyingChessTargetRewardRequest = protobuf.Message(GETFLYINGCHESSTARGETREWARDREQUEST_MSG)
NORMAL = 1
PASSIVE = 3

return _G["logic.proto.Activity462Extension_pb"]
