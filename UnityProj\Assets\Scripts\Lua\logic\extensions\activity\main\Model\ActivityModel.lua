module("logic.extensions.activity.main.Model.ActivityModel", package.seeall)

local ActivityModel = class("ActivityModel", BaseModel)

function ActivityModel:onInit()
	self._currentSignDay = 5
	self._currentLoginDay = 1
	self._isGetSevenDay = true
	self._isGetSign = false
	self._isLoop = false
	self.activityInfoMap = {} -- 以defineId为key
	self.activityInfoIdMap = {} -- 以id为key
	self.activitySimpleInfoList = {}
	self.AIDMap = {}
	self.closeMap = {}
end

function ActivityModel:onReset()
	removetimer(self.notifyActivityUpdate, self)
end

function ActivityModel:setRegisterTime(registerTime)
	self.registerTime = registerTime
end

function ActivityModel:getRegisterTime()
	return self.registerTime
end

function ActivityModel:refreshOpenActivityInfo(activitySimpleInfos)
	print("当前信息中的活动数量", #activitySimpleInfos)
	for i, v in ipairs(activitySimpleInfos) do
		if self.activityInfoMap[v.activityDefineId] == nil then
			local info = ActivityInfo.New()
			self.activityInfoMap[v.activityDefineId] = info
		end
		self.activityInfoMap[v.activityDefineId]:initByServerObj(v)
		self.activitySimpleInfoList[i] = self.activityInfoMap[v.activityDefineId]
		local AID = self.activityInfoMap[v.activityDefineId]:getActivityId()
		self.AIDMap[AID] = self.activityInfoMap[v.activityDefineId]
	end
	self:refreshActivityInfoIdMap(activitySimpleInfos)
	-- 自动获取大主题和中主题的最新活动id
	AutoGetActInfo.refreshActivityId()
	if #activitySimpleInfos > 0 then
		self:setMinUpdateTime()
	end
end

--以activityId为key的list
function ActivityModel:refreshActivityInfoIdMap(activitySimpleInfos)
	for i, v in ipairs(activitySimpleInfos) do
		if self.activityInfoIdMap[v.activityId] == nil then
			local info = ActivityInfo.New()
			self.activityInfoIdMap[v.activityId] = info
		end
		self.activityInfoIdMap[v.activityId]:initByServerObj(v)
	end
end

function ActivityModel:deleteActivityInfo(activitySimpleInfos)
	for i, info in ipairs(activitySimpleInfos) do
		print("删除的活动id为", info.activityDefineId)
		if self.activityInfoMap[info.activityDefineId] ~= nil then
			self.activityInfoMap[info.activityDefineId] = nil
		end
		if self.activityInfoIdMap[info.activityId] ~= nil then
			self.activityInfoIdMap[info.activityId] = nil
		end
	end
end

function ActivityModel:deleteAllActivity()
	self.activityInfoMap = {}
	self.activitySimpleInfoList = {}
	self.AIDMap = {}
	self.activityInfoIdMap = {}
end

function ActivityModel:notifyActivityUpdate()
	self:setMinUpdateTime()
	ActivityController.instance:notifyActivityUpdate(self.minActivityId)
end

function ActivityModel:setMinUpdateTime()
	removetimer(self.notifyActivityUpdate, self)
	local minUpdateTime
	for k, v in pairs(self.activityInfoMap) do
		local interval = v:getMinUpdateTime()
		if interval <= 0 then
		else
			if minUpdateTime == nil or minUpdateTime > interval then
				minUpdateTime = interval
				self.minActivityId = k
			end
		end
	end
	if minUpdateTime == nil then
		return
	end
	settimer(minUpdateTime, self.notifyActivityUpdate, self, false)
end

function ActivityModel:registerCloseActView(actId, handler)
	self.closeMap[actId] = handler
end

function ActivityModel:unregisterCloseActView(actId)
	self.closeMap[actId] = nil
end

function ActivityModel:refreshOpenActivityInfoByServerPush(activityDefineId, state)
	if self.activityInfoMap[activityDefineId] == nil then
		local activitySimpleInfo = ActivityInfo.New()
		self.activityInfoMap[activityDefineId] = activitySimpleInfo
	end
	self.activityInfoMap[activityDefineId]:initByServerPush(state)
end

function ActivityModel:refreshSignData(msg)
	local isAuto = ActivityController.instance:getIsAuto()
	if (isAuto) then
		self._dirtySignMsg = msg
		return
	end

	self._currentSignDay = msg.day
	self._isGetSign = (msg.state == 2)
	self._signDayChangeSetId = msg.changeSetId
end

function ActivityModel:refreshSevenDayData(msg)
	local isAuto = ActivityController.instance:getIsAuto()
	if (isAuto) then
		self._dirtySevenDayMsg = msg
		return
	end
	self._sevendayRewardIndex = msg.rewardIndex
	self._sevendayState = msg.state
	self._currentGid = msg.gId
	self._currentLoginDay = msg.day
	self._isGetSevenDay = (msg.state == 2)
	self._sevendayAllGet = false
	self._sevendayChangeSetId = msg.changeSetId
	self._isLoop = msg.loop
end

function ActivityModel:getSevenIsLoop()
	return self._isLoop
end

function ActivityModel:getSevenDayState()
	return self._sevendayState
end

function ActivityModel:getRewardIndex()
	return self._sevendayRewardIndex
end

function ActivityModel:getSevenDayGid()
	return self._currentGid
end

function ActivityModel:_setDirtySevenDayMsg(msg)
	self._dirtySevenDayMsg = msg
end

function ActivityModel:_setDirtySignMsg(msg)
	self._dirtySignMsg = msg
end

function ActivityModel:refreshDirtyMsg()
	if (self._dirtySevenDayMsg) then
		self:refreshSevenDayData(self._dirtySevenDayMsg)
		self._dirtySevenDayMsg = nil
	end

	if (self._dirtySignMsg) then
		self:refreshSignData(self._dirtySignMsg)
		self._dirtySignMsg = nil
	end
end

function ActivityModel:isSevenDayAllGet()
	return self._sevendayAllGet
end

function ActivityModel:getCurrentSignDay()
	return self._currentSignDay
end

function ActivityModel:getCurrentLoginDay()
	return self._currentLoginDay
end

function ActivityModel:getSignChangeSetId()
	return self._signDayChangeSetId
end

function ActivityModel:isGetSevenDay()
	return self._isGetSevenDay
end

function ActivityModel:isGetSign()
	return self._isGetSign
end

function ActivityModel:getActivityIsOpen(activityDefineId, notDefineId)
	if notDefineId then
		if self.activityInfoIdMap[activityDefineId] == nil then
			-- print("该活动不存在,活动号为: " .. activityDefineId)
			return false
		else
			return self.activityInfoIdMap[activityDefineId]:getIsOpen()
		end
	else
		if self.activityInfoMap[activityDefineId] == nil then
			-- print("该活动不存在,活动号为: " .. activityDefineId)
			return false
		else
			return self.activityInfoMap[activityDefineId]:getIsOpen()
		end
	end
end

function ActivityModel:getActivityIsShow(activityDefineId, notDefineId)
	if notDefineId then
		if self.activityInfoIdMap[activityDefineId] == nil then
			-- print("该活动不存在,活动号为: " .. activityDefineId)
			return false
		else
			return self.activityInfoIdMap[activityDefineId]:getIsShow()
		end
	else
		if self.activityInfoMap[activityDefineId] == nil then
			-- print("该活动不存在,活动号为: " .. activityDefineId)
			return false
		else
			return self.activityInfoMap[activityDefineId]:getIsShow()
		end
	end
end

function ActivityModel:getAllActivitySimpleInfo()
	return self.activitySimpleInfoList
end

--拿活动数据by defineId
function ActivityModel:getActivityInfo(activityDefineId)
	return self.activityInfoMap[activityDefineId]
end

--拿活动数据by Id
function ActivityModel:getActInfoByActId(activityId)
	return self.activityInfoIdMap[activityId]
end

function ActivityModel:getActivityInfoByAID(AID)
	return self.AIDMap[AID]
end

function ActivityModel:getAIDMap()
	return self.AIDMap
end

function ActivityModel:getActivityInfoStartTime(activityDefineId)
	if self.activityInfoMap[activityDefineId] == nil then
		return false
	end
	return self.activityInfoMap[activityDefineId]:getInfoStartTime()
end

function ActivityModel:getActivityInfoEndTime(activityDefineId)
	if self.activityInfoMap[activityDefineId] == nil then
		return false
	end
	return self.activityInfoMap[activityDefineId]:getInfoEndTime()
end

function ActivityModel:getActivityShowStartTime(activityDefineId)
	if self.activityInfoMap[activityDefineId] == nil then
		return false
	end
	return self.activityInfoMap[activityDefineId]:getInfoShowStartTime()
end

function ActivityModel:getActivityShowEndTime(activityDefineId)
	if self.activityInfoMap[activityDefineId] == nil then
		return false
	end
	return self.activityInfoMap[activityDefineId]:getInfoShowEndTime()
end

function ActivityModel:initCalenderInfo()
	self.calenderActivityMap = {}
	self.calenderActivityList = {}
	for i, v in ipairs(ActivityConfig.getCalendarInfo()) do
		self.calenderActivityMap[v.id] = ActivityCalenderInfo.New()
		self.calenderActivityMap[v.id]:init(v)
		table.insert(self.calenderActivityList, self.calenderActivityMap[v.id])
	end
	ActMultiRechargeManager.instance:initCurActId()
	--2.2.2 版本新增一级页签
	self.calenderFirstTabList = ActivityConfig.getCalendarFirstTabInfo()
end

function ActivityModel:getCalenderFirstTabList()
	table.sort(
		self.calenderFirstTabList,
		function(a, b)
			return a.sortId < b.sortId
		end
	)
	return self.calenderFirstTabList
end

function ActivityModel:getCalenderInfoList()
	table.sort(
		self.calenderActivityList,
		function(a, b)
			return a:getSortId() < b:getSortId()
		end
	)
	return self.calenderActivityList
end

function ActivityModel:getFristTabInfoById(id)
	return self.calenderFirstTabList[id]
end

function ActivityModel:getCalenderInfoById(id)
	return self.calenderActivityMap[id]
end

function ActivityModel:getActivityInfoIdMap()
	return self.activityInfoIdMap
end

ActivityModel.instance = ActivityModel.New()

return ActivityModel
