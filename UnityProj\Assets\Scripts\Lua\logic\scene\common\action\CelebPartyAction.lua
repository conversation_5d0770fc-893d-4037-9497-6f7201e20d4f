module("logic.scene.common.action.CelebPartyAction", package.seeall)

local CelebPartyAction = class("CelebPartyAction", SceneActionBase)

function CelebPartyAction:onStart()
    SceneAgent.instance:sendSceneShareRequest(PlayerActionType.CelebParty.typeId, self.params, handler(self.onShareResponse, self))
    self:onShowTeleport1()
end

function CelebPartyAction:onShareResponse(status)
    if status == 0 then
    else
        DialogHelper.showErrorMsg(status)
        self:finish(false)
    end
end

function CelebPartyAction:onStop()
    SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.CelebParty.typeId, handler(self.onCancelResponse, self))
    self:finish(false)
    self:onShowTeleport2()
end

function CelebPartyAction:onCancelResponse(status)
    if status == 0 then
    else
        DialogHelper.showErrorMsg(status)
    end
end

function CelebPartyAction:onShowTeleport1()
    local player = SceneManager.instance:getCurScene():getUserPlayer()
    local slot = player.effectLoader:getSlot(Activity471Config.TeleportKey1)
    local res = string.format(Activity471Config.EffectResPath, "teleport")
    slot:loadAndPlay(res, 2)
end

function CelebPartyAction:onShowTeleport2()
    local player = SceneManager.instance:getCurScene():getUserPlayer()
    local slot = player.effectLoader:getSlot(Activity471Config.TeleportKey2)
    local res = string.format(Activity471Config.EffectResPath, "teleport")
    slot:loadAndPlay(res, 2)
end

function CelebPartyAction:getType()
    return SceneActionType.CelebParty
end

return CelebPartyAction
