module("logic.extensions.throwcake.view.ThrowCakeGamePrepareView", package.seeall)
local ThrowCakeGamePrepareView = class("ThrowCakeGamePrepareView", ViewComponent)

--- view初始化时会执行
function ThrowCakeGamePrepareView:buildUI()
    self._btnClose = self:getBtn("btnclose/btnClose")
    self._btnhelp = self:getBtn("btnhelp")
    self._btnStart = self:getBtn("btnPlay")

    self.ticketLimitText = self:getText("rightTop/txtRatioNum")
    self.actId = ThrowCakeConfig.getActivityId()
    IconLoader.setIconForItem(self:getGo("rightTop/imgSign"), ThrowCakeConfig.getLevelCfg(4).levelAward[1].id)
    self._btnRank = self:getBtn("btnRank")
    self._btnGo = self:getBtn("btnGo")
    self:buildTabs()
end

function ThrowCakeGamePrepareView:buildTabs()
    self.levelTabs = {}
    for i = 1, 4 do
        local tab = {}
        tab.btn = self:getBtn("leftContent/levelitem" .. i)

        tab.lockGo = self:getGo("leftContent/levelitem" .. i .. "/lock")
        tab.unlockGo = self:getGo("leftContent/levelitem" .. i .. "/unlock")

        tab.lockUnSelectGo = self:getGo("leftContent/levelitem" .. i .. "/lock/unSelectGo")
        tab.lockSelectGo = self:getGo("leftContent/levelitem" .. i .. "/lock/selectGo")
        tab.lockUnSelectTxtTime = self:getText("leftContent/levelitem" .. i .. "/lock/unSelectGo/txtTime")
        tab.lockSelectTxtTime = self:getText("leftContent/levelitem" .. i .. "/lock/selectGo/txtTime")

        tab.unlockUnSelectGo = self:getGo("leftContent/levelitem" .. i .. "/unlock/unSelectGo")
        tab.unlockSelectGo = self:getGo("leftContent/levelitem" .. i .. "/unlock/selectGo")
        tab.unlockUnSelectTxtHint = self:getGo("leftContent/levelitem" .. i .. "/unlock/unSelectGo/txtHint")
        tab.unlockSelectTxtHint = self:getGo("leftContent/levelitem" .. i .. "/unlock/selectGo/txtHint")

        tab.unlockSelectScoret = self:getText("leftContent/levelitem" .. i .. "/unlock/unSelectGo/txtScore")
        tab.unlockUnSelectScore = self:getText("leftContent/levelitem" .. i .. "/unlock/selectGo/txtScore")

        tab.unlockSelectMaxScoreText = self:getGo("leftContent/levelitem" .. i .. "/unlock/selectGo/txtMaxScoreText")
        tab.unlockUnSelectMaxScoreText =
            self:getGo("leftContent/levelitem" .. i .. "/unlock/unSelectGo/txtMaxScoreText")

        tab.clickKeyName = "ThrowCake_LastClick_Tab" .. i
        tab.redPoint = self:getGo("leftContent/levelitem" .. i .. "/imgRedPoint")

        self.levelTabs[i] = tab
    end
end

function ThrowCakeGamePrepareView:updateTabs()
    for i = 4, 4 do
        local time = ThrowCakeController.instance:getOpenTime(i)
        local isopen = ServerTime.now() > time
        self.levelTabs[i].lockGo:SetActive(not isopen)
        self.levelTabs[i].unlockGo:SetActive(isopen)
        self.levelTabs[i].isLock = not isopen
        if isopen then
            self:updateOpenGo(i)
            self:updateRedPoint(i)
        else
            self.levelTabs[i].redPoint:SetActive(false)
            self:updateLockGo(i)
        end
    end
end

function ThrowCakeGamePrepareView:updateOpenGo(index)
    local maxScore = ThrowCakeController.instance:getMaxScore(index)
    local tab = self.levelTabs[index]
    if maxScore == 0 then
        tab.unlockUnSelectTxtHint:SetActive(true)
        tab.unlockSelectTxtHint:SetActive(true)
        tab.unlockSelectMaxScoreText:SetActive(false)
        tab.unlockUnSelectMaxScoreText:SetActive(false)
        tab.unlockSelectScoret.gameObject:SetActive(false)
        tab.unlockUnSelectScore.gameObject:SetActive(false)
    else
        tab.unlockUnSelectTxtHint:SetActive(false)
        tab.unlockSelectTxtHint:SetActive(false)
        tab.unlockSelectMaxScoreText:SetActive(true)
        tab.unlockUnSelectMaxScoreText:SetActive(true)
        tab.unlockSelectScoret.gameObject:SetActive(true)
        tab.unlockUnSelectScore.gameObject:SetActive(true)
        tab.unlockSelectScoret.text = maxScore
        tab.unlockUnSelectScore.text = maxScore
    end
end

function ThrowCakeGamePrepareView:updateRedPoint(index)
    local tab = self.levelTabs[index]
    ThrowCakeController.instance:updateClickRedPoint()
    local hasClick = ThrowCakeController.instance:getTabHasClick(index)
    -- 有无奖励
    local hasReward = ThrowCakeController.instance:hasRewardRedPoint(index)
    local hasRedPoint = hasReward or not hasClick
    tab.redPoint:SetActive(hasRedPoint)
    print("level", index, "有无红点", hasReward, not hasClick, hasRedPoint)
end

function ThrowCakeGamePrepareView:updateLockGo(index)
    local remainTime = ThrowCakeController.instance:getOpenTime(index) - ServerTime.now()
    local days = math.floor(remainTime / TimeUtil.SceondsPerDay) + 1
    self.levelTabs[index].lockUnSelectTxtTime.text = lang("{1}天后开启", days)
    self.levelTabs[index].lockSelectTxtTime.text = lang("{1}天后开启", days)
end

function ThrowCakeGamePrepareView:bindEvents()
    self._btnClose:AddClickListener(self.close, self)
    self._btnhelp:AddClickListener(self.onClickHelp, self)
    self._btnStart:AddClickListener(self.onClickStart, self)
    self._btnRank:AddClickListener(self.onClickRank, self)
    self._btnGo:AddClickListener(self.onClickGo, self)

    for i, v in ipairs(self.levelTabs) do
        v.btn:AddClickListener(self.selectLevel, self, {i})
    end

    self:registerNotify(GlobalNotify.OnServerRefresh, self.onServerRefresh, self)
    self:registerLocalNotify(ThrowCakeNotify.UpdateView, self.updateView, self)
end

function ThrowCakeGamePrepareView:onClickRank()
    ViewMgr.instance:open("CommonRankingView", 
    GameEnum.ActivityEnum.ACTIVITY_252, 
    { 
        GameEnum.RankingTypeEnum.ACTIVITY_252_ENDLESS_RANKING, 
        GameEnum.RankingTypeEnum.ACTIVITY_252_ENDLESS_RANKING 
    }, 
    {CommonRankingNotify.rankingConfigs.ThrowCakeList, 
        CommonRankingNotify.rankingConfigs.ThrowCakeFriendList 
    })
end

function ThrowCakeGamePrepareView:onClickGo()
    ViewMgr.instance:clearBackStack()
    self:close()
    TaskSceneCmdHelper.gotoLocation(52, -11, -66)
end

function ThrowCakeGamePrepareView:unbindEvents()
    self:unregisterNotify(GlobalNotify.OnServerRefresh, self.onServerRefresh, self)
    self:unregisterLocalNotify(ThrowCakeNotify.UpdateView, self.updateView, self)
end

function ThrowCakeGamePrepareView:onClickStart()
    print("开始--")
    if self.canStart == false then
        return
    end
    if self._limitProp ~= nil then
        if self._limitProp[1].dailyLimit == self._limitProp[1].totalDailyLimit then
            ViewFacade.showTgNotice(8, function(isOk)
                if isOk then
                    ThrowCakeController.instance:openSingleGameView()
                end
            end)
        else
            ThrowCakeController.instance:openSingleGameView()
        end
    else
        ThrowCakeController.instance:openSingleGameView()
    end
end

function ThrowCakeGamePrepareView:onEnter()
    ThrowCakeController.instance:getGameInfo()
end

function ThrowCakeGamePrepareView:onExit()

end

function ThrowCakeGamePrepareView:onServerRefresh()
    ThrowCakeController.instance:getGameInfo()
end

function ThrowCakeGamePrepareView:updateView()
    self:updateTabs()
    local curLevel = ThrowCakeController.instance:getGameLevel()
    if curLevel > 4 then
        curLevel = 1
    end
    self:selectLevel({curLevel})
    self:updateLimit()
end
-- 显示道具限制
function ThrowCakeGamePrepareView:updateLimit()
    self._limitProp = ActivityFacade.instance:getGainLimitModel({ThrowCakeController.LimitId})
    if self._limitProp ~= nil then
        self.ticketLimitText.text = lang("<color=#c96841>{1}</color>/{2}", self._limitProp[1].dailyLimit,
            self._limitProp[1].totalDailyLimit)
    else
        local limitConfig = ActivityConfig.getGainLimitTotalDailyLimitById(ThrowCakeController.LimitId)
        self.ticketLimitText.text = lang("0/{1}", limitConfig)
    end
end

function ThrowCakeGamePrepareView:selectLevel(params)
    local level = params[1]
    for i, v in ipairs(self.levelTabs) do
        goutil.setActive(v.lockSelectGo, level == i)
        goutil.setActive(v.unlockSelectGo, level == i)
    end
    if self.levelTabs[level].isLock then
        self.canStart = false
    else
        self.canStart = true
        LocalStorage.instance:setValue(StorageKey[self.levelTabs[level].clickKeyName], ServerTime.now())
        self:updateRedPoint(level)
    end
    ThrowCakeController.instance:setGameLevel(level)
    GameUtils.setUIGray(self._btnStart.gameObject, not self.canStart, 1)
    goutil.setActive(self._btnRank.gameObject, level == 4)
    print("nowLevel", level)
end

function ThrowCakeGamePrepareView:onClickHelp()
    local title = lang("天才蛋糕师活动规则")
    local content = lang("天才蛋糕师规则描述")
    ViewMgr.instance:open("ActivityRuleCommon", title, content)
end

return ThrowCakeGamePrepareView
