module("logic.extensions.system.view.infocard.InfoCardPanel", package.seeall)
local InfoCardPanel = class("InfoCardPanel", ViewComponent)

InfoCardPanel.CloseInfoCard = "CloseInfoCard"
InfoCardPanel.ChangeClothes = "ChangeClothes"
InfoCardPanel.ChangeVoice = "ChangeVoice"
InfoCardPanel.SetDetailIcon = "SetDetailIcon"

function InfoCardPanel:buildUI()
	self:getBtn("top/tl/btnClose"):AddClickListener(self.onClickClose, self)
	self.txtFans = self:getText("left/fans/txtFans")
	--以下为设置部分
	-- self._settingBtn = self:getBtn("top/tl/btnSetting")
	-- self._settingBtn:AddClickListener(self._onClickSetting, self)
	self._btnGotoOther = self:getBtn("right/btnGoToOther")
	self._btnGotoOther:AddClickListener(self._onClickGotoOther, self)
	self._btnVoice = self:getGo("right/btnVoice")
	self:getBtn("right/btnVoice"):AddClickListener(self._onClickVoice, self)
	self._txtVoiceLength = self:getText("right/btnPlayVoice/txtVoiceLength")
	self._btnPlayVoice = self:getGo("right/btnPlayVoice")
	self._aniPlayVoice = self._btnPlayVoice:GetComponent("Animation")
	self:getBtn("right/btnPlayVoice"):AddClickListener(self._onClickPlayVoice, self)
	self._recordedGo = self:getGo("right/btnVoice/recordedGo")
	self._notRecordedGo = self:getGo("right/btnVoice/notRecordedGo")
	self._btnDetail = self:getBtn("btnDetail")
	self._btnDetail:AddClickListener(self._onClickDressDetail, self)
	-- 送花
	self._btnFlowers = self:getBtn("left/flowers")
	self._txtFlowers = self:getText("left/flowers/txtFlowers")
	self._btnOtherFlowers = self:getBtn("left/otherflowers")
	self._txtOtherFlowers = self:getText("left/otherflowers/txtFlowers")
	self._btnGiveFlower = self:getBtn("right/btnGiveFlower")
	self._btnIntimacy = self:getBtn("left/intimacy")
	self._txtIntimacyFans = self:getText("left/intimacy/txtFans")
	self._fansTipsGo = self:getGo("left/intimacy/tipsGo")
	self._btnDress = self:getBtn("btnDress")
	self._btnDress:AddClickListener(self._onClickDress, self)

	self._flowerRedPointGo = self:getGo("left/flowers/imgRedPoint")
	self._btnFlowers:AddClickListener(self._onClickFlowers, self)
	self._btnOtherFlowers:AddClickListener(self._onClickFlowers, self)
	self._btnGiveFlower:AddClickListener(self._onClickFlowers, self)
	self._btnIntimacy:AddClickListener(self._showFansTips, self)
	Framework.UIGlobalTouchTrigger.Get(self._btnIntimacy.gameObject):AddIgnoreTargetListener(self._hideFansTips, self)
	self:buildUI_follower()
end

function InfoCardPanel:buildUI_follower()
	self._btnFollow = self:getBtn("right/btnFollow")
	self._redPoint = self:getGo("right/btnFollow/redPoint")
	self._btnFollow:AddClickListener(self._onClickSelectFollower, self)
end

function InfoCardPanel:_showFansTips()
	self._fansTipsGo:SetActive(true)
end

function InfoCardPanel:_hideFansTips()
	self._fansTipsGo:SetActive(false)
end

function InfoCardPanel:bindEvents()
	self:registerLocalNotify(InfoCardPanel.ChangeClothes, self.setModelClothes, self)
	self:registerLocalNotify(InfoCardPanel.ChangeVoice, self.refreshVoiceState, self)
	self:registerLocalNotify(InfoCardPanel.SetDetailIcon, self.setDetailIconVisible, self)
	LeitingVoiceMessageManager.instance:addListener(
		LeitingVoiceMessageManager.StartPlayRecord,
		self.onStartPlayRecord,
		self
	)
	LeitingVoiceMessageManager.instance:addListener(
		LeitingVoiceMessageManager.PlayRecordCompleted,
		self.onPlayRecordCompleted,
		self
	)
end

function InfoCardPanel:unbindEvents()
	self:unregisterLocalNotify(InfoCardPanel.ChangeClothes, self.setModelClothes, self)
	self:unregisterLocalNotify(InfoCardPanel.ChangeVoice, self.refreshVoiceState, self)
	self:unregisterLocalNotify(InfoCardPanel.SetDetailIcon, self.setDetailIconVisible, self)
	LeitingVoiceMessageManager.instance:removeListener(
		LeitingVoiceMessageManager.StartPlayRecord,
		self.onStartPlayRecord,
		self
	)
	LeitingVoiceMessageManager.instance:removeListener(
		LeitingVoiceMessageManager.PlayRecordCompleted,
		self.onPlayRecordCompleted,
		self
	)
end

function InfoCardPanel:onEnter()
	self._fansTipsGo:SetActive(false)
	GlobalDispatcher:dispatch(GlobalNotify.OnTaskViewShow, self._viewPresentor.viewName)
	self.userId = self:getOpenParam()[1]
	self.info = self:getOpenParam()[2]
	self.avatarView = GameUtils.createUIAvtarView()
	self.avatarView:setHasBack(false)
	self.avatarView:setPos(-2.95, -2.23)
	self.avatarView:setScale(0.85)
	self.avatarView:setDirection(1)
	self.avatarView:setIdlePoseEnable(true)
	self.avatarView:isShowPlayIdlePose(true)
	self:refreshMain()
	GlobalDispatcher:addListener(GlobalNotify.OnRedPointPush, self._onRedPointPush, self)
	GlobalDispatcher:addListener(GlobalNotify.OnRefreshUserInfo, self._onRefreshMainInfo, self)
	RedPointController.instance:registerRedPoint(self._redPoint, {"InfoCard_Pet"})
end

function InfoCardPanel:setModelClothes(modelType, avatar)
	if not self.avatarView then
		return
	end
	-- if modelType and avatar then
	-- 	self.avatarView:setModelType(modelType)
	-- 	self.avatarView:setClothes(avatar)
	-- else
	-- 	self.avatarView:setModelType(self.info.roleSimpleInfo.modelId)
	-- 	self.avatarView:setClothes(self.defaultAvatar)
	-- end
	local temAvatar = avatar or self.defaultAvatar

	if not self.lastAvatar or not DressController.instance:isSame(temAvatar, self.lastAvatar) then
		self.lastAvatar = temAvatar
		self.avatarView:setModelType(modelType or self.info.roleSimpleInfo.modelId)
		self.avatarView.skinView:setShowLargeEffect(true)
		self.avatarView:setClothes(self.lastAvatar, handler(self.playShowPose, self))
	end
end

function InfoCardPanel:_onClickSelectFollower()
	RedPointController.instance:setLastClick("InfoCard_Pet")
	local openParams = {}
	if self.info.followerInfo then
		if self.info.followerInfo:HasField("petInfo") and self.info.followerInfo.petInfo.defineId > 0 then
			local petId =
				self.info.followerInfo.petInfo.useSkinId > 0 and self.info.followerInfo.petInfo.useSkinId or
				self.info.followerInfo.petInfo.defineId
			openParams.pet = {
				petId = petId
			}
		end
		if self.info.followerInfo:HasField("elfInfo") then
			local elfInfo = self.info.followerInfo.elfInfo
			local idList = elfInfo.clothes
			self.elfAvatar = ElfAvatar.New(idList)
			self.elfAvatar:setDefaultClothes({})
			self.elfAvatar:addDefaultClothes()
			openParams.elf = {
				elfSkinName = ElfConfig.getElfDefine(elfInfo.elfId).skinName,
				clothesIds = self.elfAvatar:getIdList()
			}
		end
		openParams.callBack = function(followItemType)
			self.info.followerInfo.followItemType = followItemType
			self:_refreshFollower()
		end
		openParams.currentIndex = self.info.followerInfo.followItemType
	end
	dump(openParams)
	ViewMgr.instance:open("SelectFollowerView", openParams)
end

function InfoCardPanel:onExit()
	self.avatarView:stopPose()
	self.avatarView:dispose()
	self.lastAvatar = nil
	self.avatarView = nil
	if self.petView then
		self.petView:dispose()
		self.petView = nil
	end
	if self.elfView then
		self.elfView:dispose()
		self.elfView = nil
	end
	LeitingVoiceMessageManager.instance:stopPlayRecord()
	GlobalDispatcher:removeListener(GlobalNotify.OnRedPointPush, self._onRedPointPush, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnRefreshUserInfo, self._onRefreshMainInfo, self)
	RedPointController.instance:unregisterRedPoint(self._redPoint)
end

function InfoCardPanel:refreshVoiceState(url)
	self._recordedGo:SetActive(not string.nilorempty(url))
	self._notRecordedGo:SetActive(string.nilorempty(url))
end

function InfoCardPanel:onClickClose()
	self:close()
	self:localNotify(InfoCardPanel.CloseInfoCard)
end

-- function InfoCardPanel:_onClickSetting()
-- 	ViewMgr.instance:open("Setting")
-- end
function InfoCardPanel:_onClickGotoOther()
	if not self.info.online then
		FlyTextManager.instance:showFlyText(lang("此用户不在线"))
		return
	end
	SceneController.instance:gotoUserScene(self.userId)
end

function InfoCardPanel:_onClickBlacklist()
	ViewMgr.instance:open("BlacklistView")
end

function InfoCardPanel:_onClickTree()
	FlyTextManager.instance:showFlyText(lang("功能尚未开启"))
end

function InfoCardPanel:_onClickPlayVoice()
	LeitingVoiceMessageManager.instance:playOnlineRecord(
		self.info.InformationCard.voiceUrl,
		self.info.InformationCard.voiceType
	)
end

function InfoCardPanel:onStartPlayRecord()
	self._aniPlayVoice.enabled = true
end

function InfoCardPanel:onPlayRecordCompleted()
	self._aniPlayVoice.enabled = false
end

function InfoCardPanel:_onClickVoice()
	FuncHelper.btnHandlerLock(
		GameEnum.GMBlackFuncTypeEnum.RECORDING_VOICE,
		function()
			ViewMgr.instance:open("VoiceIntroductionPanel")
		end
	)
end

---------------------------------------------------------------------------------
--- 送花
function InfoCardPanel:_onClickFlowers()
	if self.isMySelf then
		GardenFlowerController.instance:checkRoleReceivedFlowerReachLimit()
		ViewMgr.instance:open("GardenFlowerMainPanel", 1)
		ViewMgr.instance:open("GardenReceiveFlower")
	else
		local relation = self.info.relation --这一个人与我什么关系, (低位到高位分别表示: 是否好友 是否关注 是否粉丝)
		local isFriend = bitutil.checkBitValue(relation, 0)
		--- 还能通过陌生人的用户界面打开送花界面，此时就没办法在好友列表数据中拿到了，所以单独在用户界面带了过来
		ViewMgr.instance:open("GardenSendFlower2", isFriend and 2 or 3, self.userId, (not isFriend) and self.info or nil)
	end
end

function InfoCardPanel:_refreshFlowersGotTxt()
	self._txtFlowers.text = self.info.receiveFlowerScore
	self._txtOtherFlowers.text = self.info.receiveFlowerScore
	self._txtFlowers.gameObject:SetActive(self.isMySelf)
	self._txtOtherFlowers.gameObject:SetActive(not self.isMySelf)
	self._btnFlowers.gameObject:SetActive(self.isMySelf)
	self._btnOtherFlowers.gameObject:SetActive(not self.isMySelf)
	self._btnGiveFlower.gameObject:SetActive(not self.isMySelf and not self.isBlock)
end

function InfoCardPanel:_refreshFlowersRedPoint()
	local state = RedPointController.instance:getNodeByName("ReceivedGardenFlower"):isExist()
	self._flowerRedPointGo:SetActive(self.isMySelf and state)
end

---- 红点还要判断当前信息界面打开的是否是玩家自己的信息界面，如果不是的话就不显示红点，所以通过监听红点的push广播来处理第二次的刷新
function InfoCardPanel:_onRedPointPush(name, flag)
	if name == "ReceivedGardenFlower" then
		self._flowerRedPointGo:SetActive(self.isMySelf and flag)
	end
end

---------------------------------------------------------------------------------------
--- refresh main
--- 刷新一次整个界面
function InfoCardPanel:refreshMain()
	self.isMySelf = UserInfo.userId == self.userId
	self.isBlock = self.info.isBlacked
	print("card info", tostring(self.info))
	self.defaultAvatar = AvatarInfo.New(self.info.roleSimpleInfo.clothes)
	self:setModelClothes(self.info.roleSimpleInfo.modelId, self.defaultAvatar)
	-- self._settingBtn.gameObject:SetActive(self.isMySelf)
	self._btnGotoOther.gameObject:SetActive(not self.isMySelf and not self.isBlock)
	if not self.isMySelf then
		GameUtils.setUIGray(self._btnGotoOther.gameObject, not self.info.online, 0.3)
	end
	self._btnPlayVoice:SetActive(
		not self.isMySelf and not string.nilorempty(self.info.InformationCard.voiceUrl) and not self.isBlock
	)
	self:onPlayRecordCompleted()
	self._btnVoice:SetActive(self.isMySelf)
	self._btnDetail.gameObject:SetActive(self.isMySelf)
	self._btnDress.gameObject:SetActive(not self.isMySelf)
	self:refreshVoiceState(self.info.InformationCard.voiceUrl)
	if not self.isMySelf then
		self._txtVoiceLength.text = self.info.InformationCard.voiceDuration
	end
	self.txtFans.text = self.info.fanNum
	self._txtIntimacyFans.text = self.info.roleSimpleInfo.friendshipPower
	self._isAddedFriendFlag = false
	self:_refreshFlowersRedPoint()
	self:_refreshFlowersGotTxt()
	self:_refreshFollower()
	self:_setBlockGo(self.isBlock)
end

function InfoCardPanel:_refreshFollower()
	self._btnFollow.gameObject:SetActive(self.isMySelf)
	if self.info.followerInfo then
		FriendAgent.instance:sendAskHasGlobalLimitRequest(
			self.userId,
			FriendModel.BehaviorGlaobType["InfoCardPet"],
			function(msg)
				if msg.isAllow == false then
					self:followerSetActive(self.petView, false)
					self:followerSetActive(self.elfView, false)
				else
					self:_refreshPet()
					self:_refreshElf()
				end
			end
		)
	else
		self:followerSetActive(self.petView, false)
		self:followerSetActive(self.elfView, false)
	end
end

function InfoCardPanel:followerSetActive(view, value)
	if view and view.go then
		view.go:SetActive(value)
	end
end

function InfoCardPanel:_refreshPet()
	if self.info.followerInfo.followItemType == GameEnum.FollowItemType.PET then
		if self.info.followerInfo:HasField("petInfo") and self.info.followerInfo.petInfo.defineId > 0 then
			self:followerSetActive(self.petView, true)
			local petId =
				self.info.followerInfo.petInfo.useSkinId > 0 and self.info.followerInfo.petInfo.useSkinId or
				self.info.followerInfo.petInfo.defineId
			if self.petView then
				-- goutil.setActive(self.petView.go, true)
				self.petView:setPetInfo(petId)
			else
				self.petView = GameUtils.createUIPetView(petId, nil, false, 1)
				self.petView:setScale(0.85)
				self.petView:setPos(-4.9, -2.97)
				tfutil.SetLZ(self.petView.go, -1)
			end
		end
	else
		self:followerSetActive(self.petView, false)
	end
end

function InfoCardPanel:_refreshElf()
	if self.info.followerInfo.followItemType == GameEnum.FollowItemType.ELF then
		if self.info.followerInfo:HasField("elfInfo") then
			self:followerSetActive(self.elfView, true)
			local elfInfo = self.info.followerInfo.elfInfo
			local idList = elfInfo.clothes
			self.elfAvatar = ElfAvatar.New(idList)
			self.elfAvatar:setDefaultClothes({})
			self.elfAvatar:addDefaultClothes()
			if self.elfView == nil then
				self.elfView = GameUtils.createUIElfView()
				self.elfView:setSkin(ElfConfig.getElfDefine(elfInfo.elfId).skinName)
			end
			self.elfView:setPos(-4.4, -2.29)
			self.elfView:setScale(0.85)
			self.elfView:playIdle()
			self.elfView:setClothes(self.elfAvatar:getIdList())
			tfutil.SetLZ(self.elfView.go, -1)
		end
	else
		self:followerSetActive(self.elfView, false)
	end
end

function InfoCardPanel:_onRefreshMainInfo(userId, userInfo)
	self.userId = userId
	self.info = userInfo
	self:refreshMain()
end

function InfoCardPanel:_setBlockGo(isBlock)
	self:getGo("left/fans"):SetActive(not isBlock)
	self:getGo("left/otherflowers"):SetActive(not isBlock and not self.isMySelf)
	self:getGo("left/intimacy"):SetActive(not isBlock)
end

function InfoCardPanel:setDetailIconVisible(visible)
	self._btnDetail.gameObject:SetActive(visible)
end

function InfoCardPanel:_onClickDressDetail()
	ViewMgr.instance:open("ClothesShowDetailView", self.defaultAvatar, ModelType.Bear)
end

-- 开屏姿势
function InfoCardPanel:playShowPose()
	if not self.lastAvatar then
		return
	end
	local showPosePart = 49
	local showPoseItem = self.lastAvatar:getItemByPart(showPosePart)
	if showPoseItem then
		self.avatarView:setIdlePoseEnable(false)
		self.avatarView:showPose(showPoseItem.id, handler(self.onPlayShowPoseCallBack, self))
	end
end

function InfoCardPanel:onPlayShowPoseCallBack()
	if not self.avatarView then
		return
	end
	self.avatarView:setIdlePoseEnable(true)
	self.avatarView:playIdle()
end

function InfoCardPanel:_onClickDress()
	ViewMgr.instance:open("DressShareView", {avatarInfo = self.lastAvatar:clone(), shareUser = self.userId})
end

return InfoCardPanel
