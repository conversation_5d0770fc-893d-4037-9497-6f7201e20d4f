module("logic.scene.common.trigger.SceneTriggerFactory", package.seeall)
local SceneTriggerFactory = class("SceneTriggerFactory")

local clsMap = {}
clsMap[SceneObjectType.Exit] = ExitTrigger
clsMap[SceneObjectType.Chair] = ChairTrigger
clsMap[SceneObjectType.FishArea] = FishTrigger
clsMap[SceneObjectType.ClickPoint] = ClickPointTrigger
clsMap[SceneObjectType.Bird] = BirdTrigger
clsMap[SceneObjectType.HitEffect] = HitEffectTrigger
clsMap[SceneObjectType.JumpPoint] = JumpTrigger
clsMap[SceneObjectType.PlayingMusicPoint] = PlayingMusicTrigger
clsMap[SceneObjectType.Trap] = TrapTrigger
clsMap[SceneObjectType.Snail] = SnailTrigger
clsMap[SceneObjectType.Parrot] = ParrotTrigger
clsMap[SceneObjectType.BigHeadPoint] = BigHeadMakerTrigger
clsMap[SceneObjectType.MusicStone] = MusicStoneTrigger
clsMap[SceneObjectType.SceneGame] = SceneGameTrigger
clsMap[SceneObjectType.Cotton] = CottonTrigger
clsMap[SceneObjectType.Clock] = ClockTowerTrigger
clsMap[SceneObjectType.MultiInteration] = MultiInteractionTrigger
clsMap[SceneObjectType.CableCar] = CableCarTrigger
clsMap[SceneObjectType.Affinity] = AffinityTrigger
clsMap[SceneObjectType.Blessing] = RelativesBlessingTrigger
clsMap[SceneObjectType.LibraryLottery] = LibraryLotteryTrigger
clsMap[SceneObjectType.WishLottery] = LotteryWishTrigger
clsMap[SceneObjectType.Dandelion] = DandelionTrigger
clsMap[SceneObjectType.HerdSheep] = HerdSheepTrigger
clsMap[SceneObjectType.SwingChair] = SwingChairTrigger
clsMap[SceneObjectType.ElevatorTrigger] = ElevatorTrigger
clsMap[SceneObjectType.AffinityCheckTrigger] = AffinityCheckTrigger
clsMap[SceneObjectType.AffinityCheckTransfiguration] = AffinityCheckTransfiguration
clsMap[SceneObjectType.ClickableExit] = ClickableExitTrigger
clsMap[SceneObjectType.CannonTeleport] = CannonTeleportTrigger
clsMap[SceneObjectType.StarCeremonySoda] = StarCeremonySodaTrigger
clsMap[SceneObjectType.HappyPlanetTeleport] = HappyPlanetTeleportTrigger
clsMap[SceneObjectType.CouncilBoss_Drop] = CouncilBoss_DropTrigger
clsMap[SceneObjectType.GobangVisitorEntry] = GobangVisitorEntryTrigger
clsMap[SceneObjectType.AffinityMap] = AffinityMapTrigger
clsMap[SceneObjectType.AssemblyLine] = AssemblyLineTrigger
clsMap[SceneObjectType.PhantomCreatureHitEffect] = PhantomCreatureHitEffect
clsMap[SceneObjectType.AobiAdvert] = ADTrigger
clsMap[SceneObjectType.MusicParty] = MusicPartyTrigger
clsMap[SceneObjectType.CatchingInsect] = CatchingInsectSceneTrigger
clsMap[SceneObjectType.GobangMatchEntry] = GobangMatchEntryTrigger
clsMap[SceneObjectType.GridArea] = AnniversaryGalaGridAreaTrigger
clsMap[SceneObjectType.RainbowTrail] = RainbowTrailTrigger
clsMap[SceneObjectType.SeaPhoto] = SeaPhotoTrigger
clsMap[SceneObjectType.ActivitySceneItem] = ActivitySceneTrigger
clsMap[SceneObjectType.SeaActivitySceneItem] = SeaActivitySceneTrigger
clsMap[SceneObjectType.TeleportTrigger] = SceneTeleportTrigger


function SceneTriggerFactory.createItem(id, config, triggerGO)
	return clsMap[config.type].New(id, config, triggerGO)
end

return SceneTriggerFactory
