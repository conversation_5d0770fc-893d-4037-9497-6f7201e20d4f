module("logic.extensions.sharestreet.view.ShareStreetEditDescView",package.seeall)
---@class ShareStreetEditDescView
local ShareStreetEditDescView = class("ShareStreetEditDescView",ViewComponent)

function ShareStreetEditDescView:ctor()
	ShareStreetEditDescView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetEditDescView:buildUI()
	ShareStreetEditDescView.super.buildUI(self)

	self._btnClose = self:getBtn("btnClose")
	self._iptWord = self:getInput("iptWord")
	self._txtCount = self:getText("txtCount")
	self._btnOK = self:getBtn("btnOK")

	self._streetDescMaxLength = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetDescMaxLength")) -- 街区描述最大长度
	self._txtPlaceholder = self:getText("iptWord/Placeholder")
end

--- view初始化时会执行，在buildUI之后
function ShareStreetEditDescView:bindEvents()
	ShareStreetEditDescView.super.bindEvents(self)

    self._iptWord:AddOnValueChanged(self._onDescChange, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetEditDescView:onEnter()
	ShareStreetEditDescView.super.onEnter(self)

	self._streetInfo = ShareStreetModel.instance:getUserInfo()

	self._btnClose:AddClickListener(self.close, self)
	self._btnOK:AddClickListener(self._onOK, self)

	self._iptWord:SetText(self._streetInfo:getDesc())
	self._txtCount.text = string.format("(%d/%d)", math.ceil(GameUtils.getStrLen(self._streetInfo:getDesc()) / 2), self._streetDescMaxLength / 2)
	self._txtPlaceholder.text = lang("{1}个字以内", self._streetDescMaxLength / 2)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetEditDescView:onEnterFinished()
	ShareStreetEditDescView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetEditDescView:onExit()
	ShareStreetEditDescView.super.onExit(self)

	self._btnClose:RemoveClickListener()
	self._btnOK:RemoveClickListener()
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetEditDescView:onExitFinished()
	ShareStreetEditDescView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetEditDescView:unbindEvents()
	ShareStreetEditDescView.super.unbindEvents(self)

    self._iptWord:RemoveOnValueChanged()
end

--- view销毁时会执行
function ShareStreetEditDescView:destroyUI()
	ShareStreetEditDescView.super.destroyUI(self)
end

function ShareStreetEditDescView:_onDescChange(inputStr)
    print("ShareStreetEditDescView:_onDescChange", inputStr)
    inputStr = GameUtils.getBriefName(inputStr, self._streetDescMaxLength, '')
    self._iptWord:SetText(inputStr)
    self._txtCount.text = string.format("(%d/%d)", math.ceil(GameUtils.getStrLen(inputStr) / 2), self._streetDescMaxLength / 2)
end

function ShareStreetEditDescView:_onOK()
	local newDesc = self._iptWord:GetText()
	if newDesc == self._streetInfo:getDesc() then
		-- DialogHelper.showMsg(lang("请输入新的街区描述"))
		self:close()
		return
	end
	if not ShareStreetUtil.isStreetDescLegal(newDesc) then
		return
	end
	ShareStreetController.instance:changeDesc(newDesc, handler(self._onRspn, self))
end

function ShareStreetEditDescView:_onRspn()
	FlyTextManager.instance:showFlyText(lang("修改成功"))
	self:close()
end

return ShareStreetEditDescView