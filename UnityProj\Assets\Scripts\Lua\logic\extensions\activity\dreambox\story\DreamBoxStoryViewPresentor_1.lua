module("logic.extensions.activity.dreambox.story.DreamBoxStoryViewPresentor_1", package.seeall)
---@class DreamBoxStoryViewPresentor_1
local DreamBoxStoryViewPresentor_1 = class("DreamBoxStoryViewPresentor_1", ViewPresentor)

--- 配置view需要的资源列表
function DreamBoxStoryViewPresentor_1:dependWhatResources()
	return {"ui/middleact/middleacttaskview.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function DreamBoxStoryViewPresentor_1:buildViews()
	local originTaskId = AutoGetActInfo.getMiddleActStoryId()
	local taskCount = 3
	return {
		DreamBoxStoryView_1.New(originTaskId - 1, taskCount),
		ActivityEndTimeUltimateComp.New(DreamBoxFacade.storyActivityId_1, "txtTime", false, true)
	}
end

--- 配置view所在的ui层
function DreamBoxStoryViewPresentor_1:attachToWhichRoot()
	return ViewRootType.FullScreen
end

return DreamBoxStoryViewPresentor_1
