module("logic.scene.util.SceneDefine",package.seeall)
local SceneDefine = class("SceneDefine")
function SceneDefine:init()
	self.bornPointMap = {}
	self.areaList = {}
	self.defualtBornPos = nil
end

function SceneDefine:getBornPos(lastSceneId)
	lastSceneId = lastSceneId or 0
	local point =self.bornPointMap[lastSceneId] 
	if not point then
		point = self.bornPointMap[0]
	end
	return point
end

function SceneDefine:getSpecialArea()
	return self.areaList
end

function SceneDefine:getTypeCfg()
	return SceneConfig.getSceneTypeCfg(self.sceneType)
end
return SceneDefine