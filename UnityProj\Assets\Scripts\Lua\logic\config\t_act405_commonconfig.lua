-- {excel:405赛季集卡活动.xlsx, sheetName:export_act405杂项配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act405_commonconfig", package.seeall)

local title = {actId=1,key=2,value=3}

local dataList = {
	{1806, "dailyMaxSendTimes", "3"},
	{1806, "dailyMaxReceiveTimes", "2"},
	{1806, "actTotalFinalReward", "3:300,16000353:100,16000352:100,11000573:1,11000572:1"},
	{1806, "repeatCardExtraRewardId", "16000562"},
	{1806, "repeatCardExtraRewardCount", "1:1,2:4,3:9,4:16,5:25"},
	{1806, "repeatSpecialCardExtraRewardId", "16000586"},
	{1806, "repeatSpecialCardExtraRewardCount", "4:10,5:30"},
	{1806, "gotoActivityId", "1835"},
	{1806, "gotoGameActivityId", "1826"},
	{1806, "clothPreviewId1", "11000572"},
	{1806, "clothPreviewId2", "11000573"},
	{1806, "gotoTaskId", "1000523"},
	{1949, "dailyMaxSendTimes", "3"},
	{1949, "dailyMaxReceiveTimes", "2"},
	{1949, "actTotalFinalReward", "3:300,16000353:100,16000352:100,11000602:1,11000606:1"},
	{1949, "repeatCardExtraRewardId", "16000601"},
	{1949, "repeatCardExtraRewardCount", "1:1,2:4,3:9,4:16,5:25"},
	{1949, "repeatSpecialCardExtraRewardId", "16000602"},
	{1949, "repeatSpecialCardExtraRewardCount", "4:10,5:30"},
	{1949, "gotoActivityId", "1933"},
	{1949, "gotoGameActivityId", "1932"},
	{1949, "clothPreviewId1", "11000602"},
	{1949, "clothPreviewId2", "11000606"},
	{1949, "clothPreview1_1", "12000996,12005180,10,4,11,5,0,12005181,12005182,12005183,12005184,12005185,0,1,3,10,7,12005186,0,0,1,2,12005187,10,7,11,7,12005188,1,2,0,10,7,12005189,10,6,13,5,10,1"},
	{1949, "clothPreview1_2", "12000996,12005180,12,5,11,2,0,12005181,12005182,12005183,12005184,12005185,0,12,5,0,12005186,0,0,12,5,12005187,11,2,12,5,12005188,7,2,12,5,11,5,12005189"},
	{1949, "clothPreview1_3", "12000996,12005181,12005182,12005183,12005184,12005185,14,4,0,0,12005186,0,16,1,14,4,12005180,14,4,16,1,0,12005187,9,1,5,5,12005188,14,4,0,0,12005189,14,4,16,1,16,5"},
	{1949, "clothPreview2_1", "12005231,12005232,12005233,12005234,12005235,13,4,0,13,6,12005236,2,1,0,12005237,13,1,13,6,12005238,2,1,13,2,2,3,12005239,12005240,2,1,0,2,1,12000996,12005428"},
	{1949, "clothPreview2_2", "12000996,12005231,12005232,12005233,12005234,12005235,1,1,0,7,4,12005236,1,1,12005237,10,3,10,6,12005238,1,1,10,7,1,7,12005239,12005240,1,1,0,1,1"},
	{1949, "clothPreview2_3", "12005231,12005232,12005233,12005234,12005235,7,2,0,7,2,12005236,7,2,0,12005237,9,1,11,6,12005238,11,6,11,5,12,5,12005239,12005240,11,2,0,11,6,12000996,12005428"},
	{1949, "gotoTaskId", "1000523"},
	{1949, "goodsId1", "504137,504138,504139,504140,504141,504135,504136"},
	{1949, "goodsId2", "504131,504132,504133,504134"},
	{1949, "tanksLimit", "50"},
	{2126, "dailyMaxSendTimes", "3"},
	{2126, "dailyMaxReceiveTimes", "2"},
	{2126, "actTotalFinalReward", "3:300,16000353:100,16000352:100,11000669:1,11000663:1"},
	{2126, "repeatCardExtraRewardId", "16000680"},
	{2126, "repeatCardExtraRewardCount", "1:1,2:4,3:9,4:16,5:25"},
	{2126, "repeatSpecialCardExtraRewardId", "16000681"},
	{2126, "repeatSpecialCardExtraRewardCount", "4:10,5:30"},
	{2126, "gotoActivityId", "2129"},
	{2126, "gotoGameActivityId", "2171,2172,2128"},
	{2126, "clothPreviewId1", "11000663"},
	{2126, "clothPreviewId2", "11000669"},
	{2126, "clothPreview1_1", "12000996,12005942,15,1,12,5,11,5,12005943,11,5,12005944,12005945,12005946,12005947,12005948,15,1,12,5,11,1,12005949,12,5,15,1,11,2,12005950,0,15,1,0,12005951,15,1,9,1,12,5"},
	{2126, "clothPreview1_2", "12000996,12005942,14,4,5,5,0,12005943,16,7,12005944,12005945,12005946,12005947,12005948,14,4,16,1,16,5,12005949,5,6,14,4,16,6,12005950,16,1,14,4,0,12005951,14,4,16,5,16,1"},
	{2126, "clothPreview1_3", "12000996,12005942,11,6,1,1,0,12005943,10,5,12005944,12005945,12005946,12005947,12005948,11,6,1,1,11,5,12005949,11,7,1,1,0,12005950,11,6,10,6,11,5,12005951,11,6,11,5,10,3"},
	{2126, "clothPreview2_1", "12000996,12005988,12,5,12005989,12005990,15,1,12005991,12,5,15,1,0,12005992,12005993,12005994,12,5,15,1,15,1,12005995,15,1,12,5,12,5,12005996,12,5,15,1,15,1,12005997,12,5,15,1,15,2"},
	{2126, "clothPreview2_2", "12000996,12005988,5,5,12005989,12005990,16,3,12005991,14,4,0,0,12005992,12005993,12005994,14,4,0,16,1,12005995,5,5,14,4,14,4,12005996,14,4,16,1,0,12005997,5,5,14,4,16,7"},
	{2126, "clothPreview2_3", "12000996,12005988,12005989,12005990,10,6,12005991,1,1,11,6,10,5,12005992,12005993,12005994,1,1,11,6,11,6,12005995,11,6,1,1,1,1,12005996,11,6,11,5,10,6,12005997,1,1,11,6,11,3"},
	{2126, "gotoTaskId", "1000523"},
	{2126, "goodsId1", "504193,504194,504195,504196,504197,504191,504192"},
	{2126, "goodsId2", "504187,504188,504189,504190"},
	{2126, "tanksLimit", "50"},
	{2243, "dailyMaxSendTimes", "3"},
	{2243, "dailyMaxReceiveTimes", "2"},
	{2243, "actTotalFinalReward", "3:300,16000353:100,16000352:100,11000722:1,11000727:1"},
	{2243, "repeatCardExtraRewardId", "16000716"},
	{2243, "repeatCardExtraRewardCount", "1:1,2:4,3:9,4:16,5:25"},
	{2243, "repeatSpecialCardExtraRewardId", "16000717"},
	{2243, "repeatSpecialCardExtraRewardCount", "4:10,5:30"},
	{2243, "gotoActivityId", "2245"},
	{2243, "gotoGameActivityId", "2246,2247"},
	{2243, "clothPreviewId1", "11000722"},
	{2243, "clothPreviewId2", "11000727"},
	{2243, "clothPreview1_1", "12000996,12006609,16,5,5,6,12006613,14,4,16,5,7,8,12006614,14,4,16,5,0,12006615,14,4,16,5,12006617,14,4,16,1,7,7,12006610,14,4,16,1,12006616,14,4,5,5,16,3,12006618,5,1,14,4,5,5,12006612,12006611"},
	{2243, "clothPreview1_2", "12006612,12006611,12006609,13,5,13,3,12006613,13,4,13,5,2,4,12006614,13,4,13,5,13,1,12006615,13,4,13,5,12006618,13,6,13,3,13,4,12006617,13,4,13,5,13,8,12006610,13,4,13,6,12000996,12006616,13,6,13,4,13,1"},
	{2243, "clothPreview1_3", "12006612,12006611,12006609,11,5,10,6,12006610,15,1,9,1,12006613,15,1,10,1,15,5,12006614,15,1,10,5,0,12006615,15,1,10,1,12006616,9,5,15,1,10,7,12006617,15,1,9,1,11,4,12000996,12006618,11,3,10,2,7,4"},
	{2243, "clothPreview2_1", "12000996,12006654,13,6,2,7,2,3,12006655,2,3,13,6,13,5,12006656,12006657,12006658,12006659,2,4,13,5,2,1,12006660,2,4,13,5,13,3,12006661,13,6,2,4,13,3,12006662,2,2,13,6,13,1,12006663,13,1,2,3,13,6"},
	{2243, "clothPreview2_2", "12000996,12006654,1,1,0,11,7,12006655,1,8,11,6,11,6,12006656,12006657,12006658,12006659,0,11,6,10,8,12006660,7,4,11,7,10,6,12006661,11,7,0,10,3,12006662,11,7,11,5,1,5,12006663,1,1,0,10,6"},
	{2243, "clothPreview2_3", "12000996,12006654,5,5,16,4,14,4,12006655,7,8,16,1,14,4,12006656,12006657,12006658,12006659,7,8,16,6,14,4,12006660,7,8,14,4,16,1,12006661,5,6,7,8,14,4,12006662,14,4,16,3,16,1,12006663,16,1,7,8,14,4"},
	{2243, "gotoTaskId", "1000523"},
	{2243, "goodsId1", "504248,504249,504250,504251,504252,504246,504247"},
	{2243, "goodsId2", "504242,504243,504244,504245"},
	{2243, "tanksLimit", "50"},
	{2371, "dailyMaxSendTimes", "3"},
	{2371, "dailyMaxReceiveTimes", "2"},
	{2371, "actTotalFinalReward", "3:300,16000353:100,16000352:100,11000750:1,11000749:1"},
	{2371, "repeatCardExtraRewardId", "16000771"},
	{2371, "repeatCardExtraRewardCount", "1:1,2:4,3:9,4:16,5:25"},
	{2371, "repeatSpecialCardExtraRewardId", "16000772"},
	{2371, "repeatSpecialCardExtraRewardCount", "4:10,5:30"},
	{2371, "gotoActivityId", "2373"},
	{2371, "gotoGameActivityId", "2374,2375"},
	{2371, "clothPreviewId1", "11000749"},
	{2371, "clothPreviewId2", "11000750"},
	{2371, "clothPreview1_1", "12006938,12006939,12006937,12006936,9,1,9,8,9,6,12006943,13,3,13,1,10,3,12006940,1,1,13,2,12006935,10,3,13,4,13,1,12006942,10,3,13,2,13,3,12006944,1,1,13,2,13,5,12006941,9,7,13,1,13,3,12000996"},
	{2371, "clothPreview1_2", "12006938,12006939,12006937,12006936,16,7,14,4,16,7,12006943,14,3,16,5,16,7,12006940,14,5,16,5,12006935,16,1,14,6,14,5,12006942,14,5,16,1,14,1,12006944,14,4,16,5,14,5,12006941,14,3,16,1,14,1,12000996"},
	{2371, "clothPreview1_3", "12006938,12006939,12006937,12006936,7,5,16,8,16,5,12006943,11,8,16,5,12,5,12006940,11,7,16,5,12006935,16,4,12,5,11,2,12006942,11,6,12,5,11,4,12006944,11,2,10,5,16,5,12006941,16,8,16,4,12,5,12000996"},
	{2371, "clothPreview2_1", "12006948,12006950,12006949,12006947,12000996,12006946,16,1,16,4,12006951,14,3,16,1,16,7,12006952,16,1,16,5,16,6,12006953,5,6,16,5,14,4,12006945,16,7,16,6,14,4,12006954,8,1,16,5,16,5"},
	{2371, "clothPreview2_2", "12006948,12006950,12006949,12006947,12000996,12006946,13,3,11,7,12006951,13,3,11,1,11,5,12006952,11,5,11,2,11,8,12006953,13,3,11,1,11,1,12006945,11,5,11,2,16,5,12006954,11,5,11,6,13,1"},
	{2371, "clothPreview2_3", "12006948,12006950,12006949,12006947,12000996,12006946,11,4,16,1,0,12006951,11,4,16,1,5,5,12006952,5,6,5,1,16,6,12006953,11,4,16,1,16,1,12006945,5,1,11,4,16,1,12006954,11,4,10,5,16,5"},
	{2371, "gotoTaskId", "1000523"},
	{2371, "goodsId1", "504327,504328,504329,504330,504331,504325,504326"},
	{2371, "goodsId2", "504321,504322,504323,504324"},
	{2371, "tanksLimit", "50"},
	{2462, "dailyMaxSendTimes", "3"},
	{2462, "dailyMaxReceiveTimes", "2"},
	{2462, "actTotalFinalReward", "3:300,16000353:100,16000352:100,11000788:1,11000785:1"},
	{2462, "repeatCardExtraRewardId", "16000794"},
	{2462, "repeatCardExtraRewardCount", "1:1,2:4,3:9,4:16,5:25"},
	{2462, "repeatSpecialCardExtraRewardId", "16000795"},
	{2462, "repeatSpecialCardExtraRewardCount", "4:10,5:30"},
	{2462, "gotoActivityId", "2464"},
	{2462, "gotoGameActivityId", "2465,2466"},
	{2462, "clothPreviewId1", "11000785"},
	{2462, "clothPreviewId2", "11000788"},
	{2462, "clothPreview1_1", "12006938,12006939,12006937,12006936,9,1,9,8,9,6,12006943,13,3,13,1,10,3,12006940,1,1,13,2,12006935,10,3,13,4,13,1,12006942,10,3,13,2,13,3,12006944,1,1,13,2,13,5,12006941,9,7,13,1,13,3,12000996"},
	{2462, "clothPreview1_2", "12006938,12006939,12006937,12006936,16,7,14,4,16,7,12006943,14,3,16,5,16,7,12006940,14,5,16,5,12006935,16,1,14,6,14,5,12006942,14,5,16,1,14,1,12006944,14,4,16,5,14,5,12006941,14,3,16,1,14,1,12000996"},
	{2462, "clothPreview1_3", "12006938,12006939,12006937,12006936,7,5,16,8,16,5,12006943,11,8,16,5,12,5,12006940,11,7,16,5,12006935,16,4,12,5,11,2,12006942,11,6,12,5,11,4,12006944,11,2,10,5,16,5,12006941,16,8,16,4,12,5,12000996"},
	{2462, "clothPreview2_1", "12006948,12006950,12006949,12006947,12000996,12006946,16,1,16,4,12006951,14,3,16,1,16,7,12006952,16,1,16,5,16,6,12006953,5,6,16,5,14,4,12006945,16,7,16,6,14,4,12006954,8,1,16,5,16,5"},
	{2462, "clothPreview2_2", "12006948,12006950,12006949,12006947,12000996,12006946,13,3,11,7,12006951,13,3,11,1,11,5,12006952,11,5,11,2,11,8,12006953,13,3,11,1,11,1,12006945,11,5,11,2,16,5,12006954,11,5,11,6,13,1"},
	{2462, "clothPreview2_3", "12006948,12006950,12006949,12006947,12000996,12006946,11,4,16,1,0,12006951,11,4,16,1,5,5,12006952,5,6,5,1,16,6,12006953,11,4,16,1,16,1,12006945,5,1,11,4,16,1,12006954,11,4,10,5,16,5"},
	{2462, "gotoTaskId", "1000523"},
	{2462, "goodsId1", "504399,504400,504401,504402,504403,504404,504405,504397,504398"},
	{2462, "goodsId2", "504393,504394,504395,504396"},
	{2462, "tanksLimit", "50"},
}

local t_act405_commonconfig = {
	[1806] = {
		["dailyMaxSendTimes"] = dataList[1],
		["dailyMaxReceiveTimes"] = dataList[2],
		["actTotalFinalReward"] = dataList[3],
		["repeatCardExtraRewardId"] = dataList[4],
		["repeatCardExtraRewardCount"] = dataList[5],
		["repeatSpecialCardExtraRewardId"] = dataList[6],
		["repeatSpecialCardExtraRewardCount"] = dataList[7],
		["gotoActivityId"] = dataList[8],
		["gotoGameActivityId"] = dataList[9],
		["clothPreviewId1"] = dataList[10],
		["clothPreviewId2"] = dataList[11],
		["gotoTaskId"] = dataList[12],
	},
	[1949] = {
		["dailyMaxSendTimes"] = dataList[13],
		["dailyMaxReceiveTimes"] = dataList[14],
		["actTotalFinalReward"] = dataList[15],
		["repeatCardExtraRewardId"] = dataList[16],
		["repeatCardExtraRewardCount"] = dataList[17],
		["repeatSpecialCardExtraRewardId"] = dataList[18],
		["repeatSpecialCardExtraRewardCount"] = dataList[19],
		["gotoActivityId"] = dataList[20],
		["gotoGameActivityId"] = dataList[21],
		["clothPreviewId1"] = dataList[22],
		["clothPreviewId2"] = dataList[23],
		["clothPreview1_1"] = dataList[24],
		["clothPreview1_2"] = dataList[25],
		["clothPreview1_3"] = dataList[26],
		["clothPreview2_1"] = dataList[27],
		["clothPreview2_2"] = dataList[28],
		["clothPreview2_3"] = dataList[29],
		["gotoTaskId"] = dataList[30],
		["goodsId1"] = dataList[31],
		["goodsId2"] = dataList[32],
		["tanksLimit"] = dataList[33],
	},
	[2126] = {
		["dailyMaxSendTimes"] = dataList[34],
		["dailyMaxReceiveTimes"] = dataList[35],
		["actTotalFinalReward"] = dataList[36],
		["repeatCardExtraRewardId"] = dataList[37],
		["repeatCardExtraRewardCount"] = dataList[38],
		["repeatSpecialCardExtraRewardId"] = dataList[39],
		["repeatSpecialCardExtraRewardCount"] = dataList[40],
		["gotoActivityId"] = dataList[41],
		["gotoGameActivityId"] = dataList[42],
		["clothPreviewId1"] = dataList[43],
		["clothPreviewId2"] = dataList[44],
		["clothPreview1_1"] = dataList[45],
		["clothPreview1_2"] = dataList[46],
		["clothPreview1_3"] = dataList[47],
		["clothPreview2_1"] = dataList[48],
		["clothPreview2_2"] = dataList[49],
		["clothPreview2_3"] = dataList[50],
		["gotoTaskId"] = dataList[51],
		["goodsId1"] = dataList[52],
		["goodsId2"] = dataList[53],
		["tanksLimit"] = dataList[54],
	},
	[2243] = {
		["dailyMaxSendTimes"] = dataList[55],
		["dailyMaxReceiveTimes"] = dataList[56],
		["actTotalFinalReward"] = dataList[57],
		["repeatCardExtraRewardId"] = dataList[58],
		["repeatCardExtraRewardCount"] = dataList[59],
		["repeatSpecialCardExtraRewardId"] = dataList[60],
		["repeatSpecialCardExtraRewardCount"] = dataList[61],
		["gotoActivityId"] = dataList[62],
		["gotoGameActivityId"] = dataList[63],
		["clothPreviewId1"] = dataList[64],
		["clothPreviewId2"] = dataList[65],
		["clothPreview1_1"] = dataList[66],
		["clothPreview1_2"] = dataList[67],
		["clothPreview1_3"] = dataList[68],
		["clothPreview2_1"] = dataList[69],
		["clothPreview2_2"] = dataList[70],
		["clothPreview2_3"] = dataList[71],
		["gotoTaskId"] = dataList[72],
		["goodsId1"] = dataList[73],
		["goodsId2"] = dataList[74],
		["tanksLimit"] = dataList[75],
	},
	[2371] = {
		["dailyMaxSendTimes"] = dataList[76],
		["dailyMaxReceiveTimes"] = dataList[77],
		["actTotalFinalReward"] = dataList[78],
		["repeatCardExtraRewardId"] = dataList[79],
		["repeatCardExtraRewardCount"] = dataList[80],
		["repeatSpecialCardExtraRewardId"] = dataList[81],
		["repeatSpecialCardExtraRewardCount"] = dataList[82],
		["gotoActivityId"] = dataList[83],
		["gotoGameActivityId"] = dataList[84],
		["clothPreviewId1"] = dataList[85],
		["clothPreviewId2"] = dataList[86],
		["clothPreview1_1"] = dataList[87],
		["clothPreview1_2"] = dataList[88],
		["clothPreview1_3"] = dataList[89],
		["clothPreview2_1"] = dataList[90],
		["clothPreview2_2"] = dataList[91],
		["clothPreview2_3"] = dataList[92],
		["gotoTaskId"] = dataList[93],
		["goodsId1"] = dataList[94],
		["goodsId2"] = dataList[95],
		["tanksLimit"] = dataList[96],
	},
	[2462] = {
		["dailyMaxSendTimes"] = dataList[97],
		["dailyMaxReceiveTimes"] = dataList[98],
		["actTotalFinalReward"] = dataList[99],
		["repeatCardExtraRewardId"] = dataList[100],
		["repeatCardExtraRewardCount"] = dataList[101],
		["repeatSpecialCardExtraRewardId"] = dataList[102],
		["repeatSpecialCardExtraRewardCount"] = dataList[103],
		["gotoActivityId"] = dataList[104],
		["gotoGameActivityId"] = dataList[105],
		["clothPreviewId1"] = dataList[106],
		["clothPreviewId2"] = dataList[107],
		["clothPreview1_1"] = dataList[108],
		["clothPreview1_2"] = dataList[109],
		["clothPreview1_3"] = dataList[110],
		["clothPreview2_1"] = dataList[111],
		["clothPreview2_2"] = dataList[112],
		["clothPreview2_3"] = dataList[113],
		["gotoTaskId"] = dataList[114],
		["goodsId1"] = dataList[115],
		["goodsId2"] = dataList[116],
		["tanksLimit"] = dataList[117],
	},
}

t_act405_commonconfig.dataList = dataList
local mt
if Act405CommonDefine then
	mt = {
		__cname =  "Act405CommonDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act405CommonDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act405_commonconfig