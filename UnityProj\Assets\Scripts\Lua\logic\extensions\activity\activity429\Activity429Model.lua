module("logic.extensions.activity.activity429.Activity429Model", package.seeall)

local Activity429Model = class("Activity429Model")


function Activity429Model:ctor()
	self.activity = ActivityModel.instance:getActivityInfo(429)
	self._isGetData = false

	-- self._isGetData = true
	-- self.gridItems = {{index = 1, itemId = 1101}, {index = 2, itemId = 1102}}
	-- self.gainAct429RewardIds = {}
end

function Activity429Model.getItemId()
	return tonumber(CommonConfig.getConfig("act_429_common") [Activity429Model.instance:getActivityId()] ["costMaterialItemId"].value)
end

function Activity429Model.getItemNum()
	return tonumber(CommonConfig.getConfig("act_429_common") [Activity429Model.instance:getActivityId()] ["costMaterialItemNum"].value)
end

function Activity429Model.getComposeAreaWidth()
	return tonumber(CommonConfig.getConfig("act_429_common") [Activity429Model.instance:getActivityId()] ["composeAreaWidth"].value)
end

function Activity429Model.getComposeAreaLength()
	return tonumber(CommonConfig.getConfig("act_429_common") [Activity429Model.instance:getActivityId()] ["composeAreaLength"].value)
end

function Activity429Model:getDataFromServer(callBack, target)
	if self._isGetData then
		callBack(target)
		return
	end
	self._callBack = callBack
	self._target = target
	Activity429Agent.instance:sendGetAct429InfoRequest(handler(self._onGetDataFromServer, self))
end

function Activity429Model:getActivityId()
	if self.activity then
		return self.activity:getActivityId()
	else
		return 2509
	end
end

function Activity429Model:_onGetDataFromServer(msg)
	self.gridItems = {}
	for _, item in ipairs(msg.gridItems) do
		table.insert(self.gridItems, {index = item.index, itemId = item.itemId})
	end
	self.costMaterialsItemNum = msg.costMaterialsItemNum
	self.gainAct429RewardIds = {}
	for _, index in ipairs(msg.gainAct429RewardIds) do
		table.insert(self.gainAct429RewardIds, index)
	end
	self._callBack(self._target)
end


function Activity429Model:resetTaskRed()
	local hasFinish = false
	local taskType = tonumber(CommonConfig.getConfig("act_429_common") [Activity429Model.instance:getActivityId()] ["taskType"].value)
	local doingTasks = TaskModel.instance:getSortedDoingTaskList({taskType}, true)
	for i, taskMo in ipairs(doingTasks) do
		if taskMo:getTaskLimitState() == 1 then
			hasFinish = true
			break
		end
	end
	RedPointController.instance:setRedIsExist("activity429Task", hasFinish)
	if not self._addTaskListener then
		self._addTaskListener = true
		GlobalDispatcher:addListener(GlobalNotify.OnTaskUpdate, self.resetTaskRed, self)
	end
end

function Activity429Model.getItemDefineById(itemId)
	return CommonConfig.getConfig("act_429_item") [Activity429Model.instance:getActivityId()] [itemId]
end

function Activity429Model.getItemMaxLv(itemId)
	local define = Activity429Model.getItemDefineById(itemId)
	local lv = define.level
	local list = CommonConfig.getConfig("act_429_item") [Activity429Model.instance:getActivityId()]
	for id, d in pairs(list) do
		if d.group == define.group then
			lv = math.max(lv, d.level)
		end
	end
	return lv
end

function Activity429Model.getNextLvItem(itemId)
	local define = Activity429Model.getItemDefineById(itemId)
	local list = CommonConfig.getConfig("act_429_item") [Activity429Model.instance:getActivityId()]
	for id, d in pairs(list) do
		if d.group == define.group and d.level == define.level + 1 then
			return d
		end
	end
end

function Activity429Model:getItemInfoByIndex(index)
	for i, item in ipairs(self.gridItems) do
		if item.index == index then
			return item, i
		end
	end
end

function Activity429Model:createItem(callBack, target)
	if #self.gridItems >= Activity429Model.getComposeAreaWidth() * Activity429Model.getComposeAreaLength() then
		FlyTextManager.instance:showFlyText(lang("合成玩法活动生成物品区域已满提示"))
		return
	end
	Activity429Agent.instance:sendGenerateAct429ItemRequest(function(item)
		self.costMaterialsItemNum = self.costMaterialsItemNum + 1
		table.insert(self.gridItems, item)
		callBack(target, item)
	end)
end

function Activity429Model:compose(firstIndex, secondIndex, callBack, target)
	Activity429Agent.instance:sendComposeAct429ItemRequest(firstIndex, secondIndex, function(changeSetId)
		if changeSetId > 0 then
			ItemService.instance:showFlyItemStartByWorldPos(ItemService.instance:popChangeSet(changeSetId))
		end
		local firstItem, firstIndexInList = self:getItemInfoByIndex(firstIndex)
		local secondItem, secondIndexInList = self:getItemInfoByIndex(secondIndex)
		local playAnim = false
		if firstItem ~= nil and secondItem ~= nil then
			local firstDefine = Activity429Model.getItemDefineById(firstItem.itemId)
			local secondDefine = Activity429Model.getItemDefineById(secondItem.itemId)
			if firstDefine.group == secondDefine.group and firstDefine.level == secondDefine.level and firstDefine.level < Activity429Model.getItemMaxLv(firstItem.itemId) then
				--合成
				table.remove(self.gridItems, firstIndexInList)
				secondItem.itemId = Activity429Model.getNextLvItem(secondItem.itemId).id
				playAnim = true
			else
				--交换
				firstItem.index = secondIndex
				secondItem.index = firstIndex
			end
		else
			--交换
			if firstItem then firstItem.index = secondIndex end
			if secondItem then secondItem.index = firstIndex end
		end
		callBack(target, firstIndex, secondIndex, playAnim)
	end)
end

function Activity429Model:canSubmit(itemId)
	local itemDefine = Activity429Model.getItemDefineById(itemId)
	local orderList = CommonConfig.getConfig("act_429_order") [Activity429Model.instance:getActivityId()]
	for i, order in ipairs(orderList) do
		if order.submitItemLevel == itemDefine.level then
			return order.id
		end
	end
	return false
end

function Activity429Model:submit(index, callBack, target)
	local item, indexInList = self:getItemInfoByIndex(index)
	local orderId = self:canSubmit(item.itemId)
	if orderId == false then
		callBack(target, false, index)
		FlyTextManager.instance:showFlyText(lang("合成玩法活动提交物品等级不够提示"))
		return
	end
	Activity429Agent.instance:sendSubmitAct429OrderRequest(index, orderId, function(changeSetId)
		if changeSetId > 0 then
			ItemService.instance:showFlyItemStartByWorldPos(ItemService.instance:popChangeSet(changeSetId))
		end
		table.remove(self.gridItems, indexInList)
		callBack(target, true, index)
	end)
end

Activity429Model.instance = Activity429Model.New()

return Activity429Model 