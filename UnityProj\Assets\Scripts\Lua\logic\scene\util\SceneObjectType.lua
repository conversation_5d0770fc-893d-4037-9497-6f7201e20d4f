module("logic.scene.util.SceneObjectType",package.seeall)

local SceneObjectType = {}
SceneObjectType.BornPoint = 1
SceneObjectType.Chair = 2
SceneObjectType.Exit = 4
SceneObjectType.FishArea = 5
SceneObjectType.ClickPoint = 6
SceneObjectType.Bird = 7
SceneObjectType.HitEffect = 8
SceneObjectType.JumpPoint = 9
SceneObjectType.PlayingMusicPoint = 10
SceneObjectType.Trap = 11
SceneObjectType.Snail = 12
SceneObjectType.Parrot = 13
SceneObjectType.BigHeadPoint = 14
SceneObjectType.MusicStone = 15
SceneObjectType.SceneGame = 16
SceneObjectType.Cotton = 17
SceneObjectType.Clock = 18
SceneObjectType.MultiInteration = 19
SceneObjectType.CableCar = 20
SceneObjectType.DaLeDou = 21
SceneObjectType.Affinity = 22
SceneObjectType.Blessing = 23
SceneObjectType.LibraryLottery = 24
SceneObjectType.WishLottery = 25
SceneObjectType.Dandelion = 26
SceneObjectType.HerdSheep = 27
SceneObjectType.SwingChair = 28
SceneObjectType.ElevatorTrigger = 29
SceneObjectType.AffinityCheckTrigger = 30
SceneObjectType.AffinityCheckTransfiguration = 31
SceneObjectType.ClickableExit = 32
SceneObjectType.CannonTeleport = 33
SceneObjectType.StarCeremonySoda = 34
SceneObjectType.HappyPlanetTeleport = 35
SceneObjectType.CouncilBoss_Drop = 36
SceneObjectType.GobangVisitorEntry = 37
SceneObjectType.AffinityMap = 38
SceneObjectType.AssemblyLine = 39
SceneObjectType.PhantomCreatureHitEffect = 40
SceneObjectType.AobiAdvert = 41
SceneObjectType.MusicParty = 42
SceneObjectType.CatchingInsect = 43
SceneObjectType.GobangMatchEntry = 44
SceneObjectType.GridArea = 45
SceneObjectType.RainbowTrail = 46
SceneObjectType.SeaPhoto = 47
SceneObjectType.ActivitySceneItem = 48
SceneObjectType.SeaActivitySceneItem = 49
SceneObjectType.TeleportTrigger = 50
return SceneObjectType