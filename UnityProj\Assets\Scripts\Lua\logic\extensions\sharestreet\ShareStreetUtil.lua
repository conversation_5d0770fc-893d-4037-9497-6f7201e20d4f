module("logic.extensions.sharestreet.ShareStreetUtil",package.seeall)

local ShareStreetUtil = class("ShareStreetUtil")

function ShareStreetUtil.isStreetNameLegal(streetName)
    local maxLen = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetNameMaxLength")) -- 街区名字最大长度
	local minLen = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetNameMinLength")) -- 街区名字最小长度
    if string.nilorempty(streetName) then
		DialogHelper.showMsg(lang("请输入街区名字"))
		return false
	end
	if GameUtils.getStrLen(streetName) > maxLen then
		DialogHelper.showMsg(lang("街区名字不能超过{1}个字", maxLen))
		return false
	end
	if GameUtils.getStrLen(streetName) < minLen then
		DialogHelper.showMsg(lang("街区名字不能少于{1}个字", minLen))
		return false
	end
	return true
end

function ShareStreetUtil.isStreetDescLegal(streetDesc)
    local maxLen = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetDescMaxLength")) -- 街区描述最大长度
    if string.nilorempty(streetDesc) then
		DialogHelper.showMsg(lang("请输入街区描述"))
		return false
	end
    if GameUtils.getStrLen(streetDesc) > maxLen then
		DialogHelper.showMsg(lang("街区描述不能超过{1}个字", maxLen))
		return false
	end
    return true
end

function ShareStreetUtil.setPic(go, picUrl, width, height)
	go:SetActive(true)
	IconLoader.clearImage(go)
	local comp = go:GetComponent(typeof(DownloadImageComp))
	if comp then
		comp:ClearImage()
	end
	if not string.nilorempty(picUrl) then
		ImageUpAndDownHelper.SetThumbImageToGo(go, picUrl, width, height)
	else
		IconLoader.setSpriteToImg(go, GameUrl.getShareStreetDefaultPic())
	end
end

return ShareStreetUtil