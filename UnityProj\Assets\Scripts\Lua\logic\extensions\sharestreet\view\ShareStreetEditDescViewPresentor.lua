module("logic.extensions.sharestreet.view.ShareStreetEditDescViewPresentor",package.seeall)
---@class ShareStreetEditDescViewPresentor
local ShareStreetEditDescViewPresentor = class("ShareStreetEditDescViewPresentor",ViewPresentor)

function ShareStreetEditDescViewPresentor:ctor()
	ShareStreetEditDescViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetEditDescViewPresentor:dependWhatResources()
	return {"ui/street/editstreetmsgpanel.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetEditDescViewPresentor:buildViews()
	return {ShareStreetEditDescView.New()}
end

--- 配置view所在的ui层
function ShareStreetEditDescViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetEditDescViewPresentor