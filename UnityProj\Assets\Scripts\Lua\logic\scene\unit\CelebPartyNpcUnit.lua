module("logic.scene.unit.CelebPartyNpcUnit", package.seeall)

local CelebPartyNpcUnit = class("CelebPartyNpcUnit", StateObjUnit)

function CelebPartyNpcUnit:defineStateMachine()
    return {
        {
            stateType = "walk",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("walk")
                    self.shadow:SetActive(true)
                end
            }
        },
        {
            stateType = "show1",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("show1")
                    self.shadow:SetActive(true)
                end
            }
        },
        {
            stateType = "show2",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("show2")
                    self.shadow:SetActive(true)
                end
            }
        },
        {
            stateType = "show3",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("show3")
                    self.shadow:SetActive(true)
                end
            }
        },
        {
            stateType = "show5",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("show5")
                    self.shadow:SetActive(true)
                end
            }
        },
        {
            stateType = "show5_1",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("show5_1")
                    self.shadow:SetActive(true)
                end
            }
        },
        {
            stateType = "show5_2",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("show5_2")
                    self.shadow:SetActive(true)
                end
            }
        },
        {
            stateType = "show5_3",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self.animator:Play("show5_3")
                    self.shadow:SetActive(true)
                end
            }
        }
    }
end

function CelebPartyNpcUnit:defineFirstState()
    return "walk"
end

function CelebPartyNpcUnit:isHasPathFinding()
    return false
end

function CelebPartyNpcUnit:onReady()
    local inst = self.view:getInst()
    local go = goutil.findChild(inst, "go")
    self.animator = go:GetComponent("Animator")
    self.shadow = goutil.findChild(inst, "shadow")
    CelebPartyNpcUnit.super.onReady(self)
end

function CelebPartyNpcUnit:onSetInfo()
    self:setView(string.format("prefabs/celebparty/npc/%s.prefab", self.info.res))
end

return CelebPartyNpcUnit
