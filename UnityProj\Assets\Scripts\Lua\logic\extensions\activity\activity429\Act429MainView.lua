module("logic.extensions.activity.activity429.Act429MainView",package.seeall)

local Act429MainView = class("Act429MainView", ViewComponent)

local redIndex2RedPointName = {
	[1] = AutoGetActInfo.getMiddleSigninActRedName(), -- 中主题签到
	[2] = AutoGetActInfo.getMiddleShopActRedName(), -- 中主题商店
	[3] = AutoGetActInfo.getMiddleStoryActRedName(), -- 中主题剧情
	[4] = "act426"--萌粒购
}
-- 配置活动id
local tabId2ActivityId = {
    [1] = {AutoGetActInfo.getMiddleSigninActId()}, -- 中主题签到
    [2] = {AutoGetActInfo.getMiddleShopActId()}, -- 中主题商店
    [3] = {AutoGetActInfo.getMiddleStoryActId()}, -- 中主题剧情
	[4] = {426},--萌粒购
}
-- 配置打开的view
local index2ViewName = {
    [1] = AutoGetActInfo.getMiddleSigninActViewName(), -- 中主题签到
    [2] = AutoGetActInfo.getMiddleShopActViewName(), -- 中主题商店
    [3] = AutoGetActInfo.getMiddleStoryActViewName(), -- 中主题剧情
	[4] = "ScrollingMachineView",
}
-- 配置打开的view
local btnNames = {
    [1] = "btnSign", -- 中主题签到
    [2] = "btnShop", -- 中主题商店
    [3] = "btnStory", -- 中主题剧情
	[4] = "btnTurntable",
}

function Act429MainView:buildUI()
    self:getBtn("btnclose/btnClose"):AddClickListener(self.close, self)
	self:getBtn("btnhelp"):AddClickListener(self.onClickHelp, self)
	self:getBtn("btnBuy"):AddClickListener(self.onClickBuy, self)
	self:getBtn("btnBuyDice"):AddClickListener(self.onClickBuyDice, self)
	self:getBtn("tipsGo/btnTips"):AddClickListener(self.onClickTips, self)

	IconLoader.setIconForItem(self:getGo("btnDice/icon"), Activity429Model.getItemId())
	self._tipsGo = self:getGo("tipsGo/btnTips/tipsGo")
	self:getText("tipsGo/btnTips/tipsGo/txtDes").text = lang("合成玩法活动规则提示")
	Framework.UIGlobalTouchTrigger.Get(self._tipsGo):AddIgnoreTargetListener(self._hidePoseGo, self)
	self._txtTime = self:getText("txtTime")

	self._redPoint = self:getGo("btnBuyDice/redPoint")
	self:getText("btnDice/txtNum").text = Activity429Model.getItemNum()
	for i, name in ipairs(btnNames) do
		local btn = self:getBtn(name)
		btn:AddClickListener(self._onClickOpenView, self, {i})
		RedPointController.instance:registerRedPoint(goutil.findChild(btn.gameObject, "redPoint"), {redIndex2RedPointName[k]})
	end
	RedPointController.instance:registerRedPoint(self._redPoint, {"activity429Reward", "activity429Task"})
	RedPointController.instance:registerRedPoint(self:getGo("btnBuy/redPoint"), {"activity429Goods"})
	RedPointController.instance:registerRedPoint(self:getGo("btnDice/redPoint"), {"activity429Item"})
end

function Act429MainView:destroyUI()
	for i, name in ipairs(btnNames) do
		RedPointController.instance:unregisterRedPoint(self:getGo(name .. "/redPoint"))
	end
	RedPointController.instance:unregisterRedPoint(self._redPoint)
	RedPointController.instance:unregisterRedPoint(self:getGo("btnBuy/redPoint"))
	RedPointController.instance:unregisterRedPoint(self:getGo("btnDice/redPoint"))
end

function Act429MainView:_onClickOpenView(params)
	local index = params[1]
	local viewName = index2ViewName[index]
	if viewName and tabId2ActivityId[index] then
		local info
		if tabId2ActivityId[index][2] then
			info = ActivityModel.instance:getActivityInfo(tabId2ActivityId[index][1])
		else
			info = ActivityModel.instance:getActInfoByActId(tabId2ActivityId[index][1])
		end

		if info and info:getIsOpen() then
			if self.curTabId then
				local viewName = index2ViewName[self.curTabId]
				if viewName then
					ViewMgr.instance:close(viewName)
				end
			end
			self.curTabId = index
			ViewMgr.instance:open(viewName)
			if redIndex2RedPointName[index] then
				RedPointController.instance:setLastClick(redIndex2RedPointName[index], ServerTime.now())
			end
			-- 3.0 按策划要求，增加埋点，点击时记录4位数的活动id
			local actId = info:getActivityId()
			LogService.instance:logCustom("activity_icon_tap", {interface_activity=actId})
		else
			FlyTextManager.instance:showFlyText(lang("活动未开启~"))
		end
	else
		printError("配置一下", index)
	end
end

function Act429MainView:onExit()
	SceneTimer:removeTimer(self._onTickTime, self)
end

function Act429MainView:_hidePoseGo()
	self._tipsGo:SetActive(false)
end

function Act429MainView:onEnter()
	-- if ActivityHelper.closeViewOnActivityClosed(429, self._viewPresentor.viewName, true) then
	-- 	return
	-- end
	self._tipsGo:SetActive(false)
	self:_onTickTime()
	SceneTimer:setTimer(1, self._onTickTime, self, true)
	Activity429Model.instance:getDataFromServer(self._onGetData, self)
	-- 小主题活动商店的红点不需要在setting_short_red_point上配置，会自动在GroceryController:updateLanternShopTab上生成
	if not LocalStorage.instance:getValue(StorageKey.Act429MainHelp) then
		self:onClickTips()
		LocalStorage.instance:setValue(StorageKey.Act429MainHelp, true)
	end
end

function Act429MainView:_onGetData()
	self._viewPresentor.itemChangehelper:addItem("barCoin", tonumber(CommonConfig.getConfig("act_429_common") [Activity429Model.instance:getActivityId()]["CoinId"].value))
	self._viewPresentor.itemChangehelper:addItem("barDiamond", Activity429Model.getItemId())
	self._viewPresentor.game:onGetData()
	Activity429Model.instance:resetTaskRed()
end

function Act429MainView:_onTickTime()
	self._txtTime.text = Activity429Model.instance.activity:getRemainingTimeText()
	if ActivityHelper.closeViewOnActivityClosed(429, self._viewPresentor.viewName, true) then
		return
	end
end

function Act429MainView:onClickTips()
	ViewMgr.instance:open("HelpView", {"yindao_cmhc_01", "yindao_cmhc_02","yindao_cmhc_03"})
end

function Act429MainView:onClickHelp()
	ViewMgr.instance:open("ActivityRuleCommon", lang("合成玩法活动规则标题"), lang("合成玩法活动规则内容"))
end

function Act429MainView:onClickBuyDice()
	Act429RewardView.show()
end
------------互动家具-----------------
function Act429MainView:onClickBuy()
	RedPointController.instance:setLastClick("activity429Goods")
	RechargeFacade.openRechargeView()
end

return Act429MainView