module("logic.extensions.room.view.RoomEditorComfortView", package.seeall)
local RoomEditorComfortView = class("RoomEditorComfortView", ComfortBar)
function RoomEditorComfortView:ctor(viewPath)
	RoomEditorComfortView.super.ctor(self, viewPath)
end

function RoomEditorComfortView:buildUI()
	RoomEditorComfortView.super.buildUI(self)
	self._comfortBar = self:getGo(self.containerPath)
	self._diffView = self:getGo(self.containerPath .. "changeNum")
	Framework.UIClickTrigger.Get(self._comfortBar):AddClickListener(self._onClickComfort, self)
end

function RoomEditorComfortView:onEnter()
	RoomEditorComfortView.super.onEnter(self)
	local comfortBarEnable = HouseModel.instance:getRoomParams(RoomParams.Editor_Comfort_Enable)
	print("enable ============================= ", comfortBarEnable)
	goutil.setActive(self._comfortBar, comfortBarEnable)
end

function RoomEditorComfortView:_updateComfort()
	RoomEditorComfortView.super._updateComfort(self)
	
	local difference = ComfortHelper.getEditComfortChange()
	local isEdit = RoomEditModel.instance:isEdit()
	goutil.setActive(self._diffView, difference ~= 0 and isEdit)
	self:getText(self.containerPath .. "changeNum/txt").text =(difference > 0 and "+" or "") .. difference
end

function RoomEditorComfortView:_onClickComfort()
	RoomComfortDetail.instance:show(false, self:getGo(self.containerPath .. "detailContainer"))
end

return RoomEditorComfortView 