module("logic.extensions.streetpromotion.StreetPromotionView",package.seeall)

local StreetPromotionView = class("StreetPromotionView", ViewComponent)

--- view初始化时会执行
function StreetPromotionView:buildUI()
    StreetPromotionView.super.buildUI(self)
    self._btnClose = self:getBtn("btnclose/btnClose")
    self._btnHelp = self:getBtn("btnHelp")
    self._imgOver = self:getGo("imgOver")
    self._btnGo = self:getBtn("btnGo")
    self._content = self:getGo("content")
    self._btnStreet = self:getBtn("btnStreet")
    self._taskId = 301271
    self._rewards = {}
    self._commonIcons = {}
    local cfg = TaskConfig.instance:getTaskCO(self._taskId)
    if cfg then
        local rewardStr = cfg.rewards
        local temp = string.split(rewardStr, ",")
        for k,v in ipairs(temp) do
            local rewardInfo = string.splitToNumber(v, ":")
            local reward = {id=rewardInfo[1], count=rewardInfo[2]}
            table.insert(self._rewards, reward)
        end
    end
end

--- view初始化时会执行，在buildUI之后
function StreetPromotionView:bindEvents()
    StreetPromotionView.super.bindEvents(self)
    self._btnClose:AddClickListener(self.close, self)
    self._btnHelp:AddClickListener(self._onClickHelp, self)
    self._btnGo:AddClickListener(self._onClickGo, self)
    self._btnStreet:AddClickListener(self._onClickStreet, self)
end

--- view销毁时会执行，在destroyUI之前
function StreetPromotionView:unbindEvents()
    StreetPromotionView.super.unbindEvents(self)
    self._btnClose:RemoveClickListener()
    self._btnHelp:RemoveClickListener()
    self._btnGo:RemoveClickListener()
    self._btnStreet:RemoveClickListener()
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function StreetPromotionView:onEnter()
    StreetPromotionView.super.onEnter(self)
    self._imgOver:SetActive(false)
    for i=1,#self._rewards do
        local icon = CommonIconMgr.instance:fetchCommonIcon()
        icon:setWidthAndHeight(90):showFreeState(false):showRarenessGo(true):showSpecial(true)
        icon:buildData(self._rewards[i])
        goutil.addChildToParent(icon:getPrefab(), self._content)
        table.insert(self._commonIcons, icon)
    end
    local isComplete = TaskModel.instance:isTaskCompleted(self._taskId)
    self._imgOver:SetActive(isComplete)
    self._btnGo.gameObject:SetActive(not isComplete)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function StreetPromotionView:onExit()
    StreetPromotionView.super.onExit(self)
    for i=1,#self._commonIcons do
        CommonIconMgr.instance:returnCommonIcon(self._commonIcons[i])
    end
    self._commonIcons = {}
end

function StreetPromotionView:_onClickHelp()
    local title = lang("StreetPromotionView_rule_title")
    local content = lang("StreetPromotionView_rule_content")
    ViewMgr.instance:open("ActivityRuleCommon", title, content)
end

function StreetPromotionView:_onClickGo()
    local isUnlocked = FuncUnlockFacade.instance:checkIsUnlocked(65, true)
    if not isUnlocked then
        return
    end
    ViewMgr.instance:clearBackStack()
    self:close()
    ViewMgr.instance:open("TaskObjectivePanel", 2, self._taskId)
end

function StreetPromotionView:_onClickStreet()
    local isUnlocked = FuncUnlockFacade.instance:checkIsUnlocked(65, true)
    if not isUnlocked then
        return
    end
    local isComplete = TaskModel.instance:isTaskCompleted(self._taskId)
    if not isComplete then
        FlyTextManager.instance:showFlyText(lang("StreetPromotionView_tips"))
        return
    end
    ShareStreetFacade.instance:showView()
    ViewMgr.instance:clearBackStack()
    ToyHouseGotoUtil.closeMap()
    self:close()
end

return StreetPromotionView