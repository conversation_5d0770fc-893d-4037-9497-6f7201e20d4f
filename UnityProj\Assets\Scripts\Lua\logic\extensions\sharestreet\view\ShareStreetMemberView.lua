module("logic.extensions.sharestreet.view.ShareStreetMemberView",package.seeall)
---@class ShareStreetMemberView
local ShareStreetMemberView = class("ShareStreetMemberView",ViewComponent)

function ShareStreetMemberView:ctor()
	ShareStreetMemberView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetMemberView:buildUI()
	ShareStreetMemberView.super.buildUI(self)

	self._go_5p = self:getGo("5people")
	self._go_3p = self:getGo("3people")
end

--- view初始化时会执行，在buildUI之后
function ShareStreetMemberView:bindEvents()
	ShareStreetMemberView.super.bindEvents(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetMemberView:onEnter()
	ShareStreetMemberView.super.onEnter(self)

	self._infoList = ShareStreetSceneModel.instance.streetDecorationInfoList

	-- 根据已解锁区域数量显示对应的GameObject
	local unlockedAreaCount = ShareStreetSceneModel.instance.unlockAreaCount or 0
	local memberList = ShareStreetSceneModel.instance.streetDecorationInfoList
	local areaId2MemberInfo = {}
	for _, member in ipairs(memberList) do
		areaId2MemberInfo[member.areaId] = member
	end

	if unlockedAreaCount == 5 then
		self._go_5p:SetActive(true)
		self._go_3p:SetActive(false)
		for i = 1, 5 do
			local goPlayer = goutil.findChild(self._go_5p, "player_" .. i)
			local areaCfg = ShareStreetCfg.instance:getAreaConfigByIndex(i)
			print("areaCfg.id", areaCfg.id)
			local member = areaId2MemberInfo[areaCfg.id]
			self:showPlayer(goPlayer, member, i)
		end
	else
		self._go_5p:SetActive(false)
		self._go_3p:SetActive(true)
		for i = 1, 3 do
			local goPlayer = goutil.findChild(self._go_3p, "player_" .. i)
			local areaCfg = ShareStreetCfg.instance:getAreaConfigByIndex(i)
			print("areaCfg.id", areaCfg.id)
			local member = areaId2MemberInfo[areaCfg.id]
			self:showPlayer(goPlayer, member, i)
		end
	end
end

function ShareStreetMemberView:showPlayer(goPlayer, member, index)
	local roleInfo = member and member.areaOwner
	local goHeadIcon = goutil.findChild(goPlayer, "imgHeadIcon")
	local clickTrigger = Framework.UIClickTrigger.Get(goHeadIcon)
	if member == nil then
		TaskUtil.showGOThenHideOthersByName(goPlayer.transform, "imgNull")
		clickTrigger:RemoveClickListener()
		return
	end
	local goNull = goutil.findChild(goPlayer, "imgNull")
	local goImage = goutil.findChild(goPlayer, "Image")
	local txtName = goutil.findChildTextComponent(goPlayer, "txtName")
	local goMaster = goutil.findChild(goPlayer, "imgMaster")
	goNull:SetActive(false)
	goImage:SetActive(true)
	txtName.gameObject:SetActive(true)
	txtName.text = roleInfo.id == UserInfo.userId and string.format("<color=#FEC853>%s</color>", roleInfo.nickname) or roleInfo.nickname
	goMaster:SetActive(index == 1)
	HeadPortraitHelper.instance:setHeadPortraitWithUserId(goHeadIcon, roleInfo.id)
	clickTrigger:AddClickListener(self._onClickPlayer, self, {userId = roleInfo.id})
end

function ShareStreetMemberView:_onClickPlayer(_, param)
	self:close()
	local userId = param.userId
	RoomFacade.instance:enterShareStreet(userId) --用来走向对应区域
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetMemberView:onEnterFinished()
	ShareStreetMemberView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetMemberView:onExit()
	ShareStreetMemberView.super.onExit(self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetMemberView:onExitFinished()
	ShareStreetMemberView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetMemberView:unbindEvents()
	ShareStreetMemberView.super.unbindEvents(self)
end

--- view销毁时会执行
function ShareStreetMemberView:destroyUI()
	ShareStreetMemberView.super.destroyUI(self)
end

return ShareStreetMemberView