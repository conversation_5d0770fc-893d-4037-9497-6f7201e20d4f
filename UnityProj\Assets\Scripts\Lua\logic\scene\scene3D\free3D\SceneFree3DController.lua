module("logic.scene.scene3D.free3D.SceneFree3DController", package.seeall)

local SceneFree3DController = class("SceneFree3DController", CommonSceneController)

local Input = UnityEngine.Input

function SceneFree3DController:onDownScene(pos)
    if not TaskFacade.instance:isNewbieTaskFinish() or self.isLockCamera then
        return
    end
    if self._scene.actionMgr:hasAction(SceneActionType.Joystick) then
        if Input.touchCount > 1 then
            self.lastPos = self:sceneToGlobalUIPos(Input.GetTouch(1).position)
        end
    else
        SceneFree3DController.super.onDownScene(self, pos)
    end
end

function SceneFree3DController:onMultDragScene(offset)
    if not TaskFacade.instance:isNewbieTaskFinish() or self.isLockCamera then
        return
    end
    if self._scene.actionMgr:hasAction(SceneActionType.Joystick) then
        if Input.touchCount > 1 then
            self:onDragScene(Input.GetTouch(1).position)
        end
    else
        SceneFree3DController.super.onMultDragScene(self, offset)
    end
end

function SceneFree3DController:onEndDragScene()
    if not TaskFacade.instance:isNewbieTaskFinish() or self.isLockCamera then
        return
    end
    self.lastPos = nil
end

function SceneFree3DController:setLockCamera(isLock)
    SceneFree3DController.super.setLockCamera(self, isLock)
    if isLock == false then
        self._scene.camera:resetCam()
    end
end

return SceneFree3DController
