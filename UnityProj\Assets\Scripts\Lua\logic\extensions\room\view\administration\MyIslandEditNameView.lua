module("logic.extensions.room.view.administration.MyIslandEditNameView",package.seeall)

local MyIslandEditNameView = class("MyIslandEditNameView", ViewComponent)

function MyIslandEditNameView.show(areaId, callBack)
    DialogHelper.showCustom({"ui/scene/room/editislandnameview.prefab"},{MyIslandEditNameView},nil,
        {areaId=areaId, callBack=callBack})
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function MyIslandEditNameView:onEnter()
    self.areaId = self:getFirstParam().params.areaId
    self.callBack = self:getFirstParam().params.callBack
    self:getBtn("btnSure"):AddClickListener(self.onClickSure, self)
    self:getBtn("btnClose"):AddClickListener(self.close, self)
    self.iptName = self:getInput("iptName")
    self.iptName:SetText(MyHouseData.instance:getArea(self.areaId).name)
    FuncHelper.inputGoLock(11, self.iptName.gameObject)
end

function MyIslandEditNameView:onClickSure()
	FuncHelper.btnHandlerLock(11, handler(self._handleSure, self))
end

function MyIslandEditNameView:_handleSure()
    local inputStr = self.iptName:GetText()
    if inputStr == MyHouseData.instance:getArea(self.areaId).name then
        self:close()
        return
    end
    if GameUtils.isEmptyString(inputStr) then
        FlyTextManager.instance:showFlyText(lang("名字不能为空！"))
        return
    end
    HouseAgent.instance:sendSetHouseNameRequest(self.areaId, inputStr, function()
        MyHouseData.instance:getArea(self.areaId).name = inputStr
        if HouseModel.instance:isInBlock() then
            local kawei = IslandModel.instance:getKaweiByUserId(UserInfo.userId)[1]
            kawei.name = inputStr
        end
        self:close()
        self.callBack(inputStr)
    end)
end

return MyIslandEditNameView