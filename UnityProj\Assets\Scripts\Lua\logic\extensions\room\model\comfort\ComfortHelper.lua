module("logic.extensions.room.model.comfort.ComfortHelper", package.seeall)

local ComfortHelper = class("ComfortHelper")
ComfortHelper.ComfortType = {
	Total = 1,
	Positive = 2,
	Nagative = 3,
}

function ComfortHelper.resetCurComfort()
	local model = HouseModel.instance
	if not model:getRoomParams(RoomParams.Editor_Comfort_Enable) then return end
	local saveList = SceneManager.instance:getCurScene().unitFactory:getHouseMo():getRoomFurnitureList()
	local editList = RoomEditFurnitureModel.instance:getEditFurnitureList()
	local savePositive = ComfortHelper._getTotalComfort(saveList, ComfortHelper.ComfortType.Positive)
	local editPositive = ComfortHelper._getTotalComfort(editList, ComfortHelper.ComfortType.Positive)
	local saveNagative = ComfortHelper._getTotalComfort(saveList, ComfortHelper.ComfortType.Nagative)
	local editNagative = ComfortHelper._getTotalComfort(editList, ComfortHelper.ComfortType.Nagative)
	local changePositive = editPositive - savePositive
	local changeNagative = editNagative - saveNagative
	local oriPositive = model:getUserProperty(HouseUserProperty.ComfortPlus, 0)
	local oriNagative = model:getUserProperty(HouseUserProperty.ComfortMinus, 0)
	model:setUserProperty(HouseUserProperty.ComfortPlus, oriPositive + changePositive)
	model:setUserProperty(HouseUserProperty.ComfortMinus, oriNagative + changeNagative)
end

--获取当前舒适度,
function ComfortHelper.getTotalComfort(comfortType)
	if not HouseModel.instance:getRoomParams(RoomParams.Editor_Comfort_Enable) then return 0 end
	if comfortType == nil then comfortType = ComfortHelper.ComfortType.Total end
	local plus = HouseModel.instance:getUserProperty(HouseUserProperty.ComfortPlus, 0)
	local minus = HouseModel.instance:getUserProperty(HouseUserProperty.ComfortMinus, 0)
	if comfortType == ComfortHelper.ComfortType.Positive then
		return plus
	elseif comfortType == ComfortHelper.ComfortType.Nagative then
		return minus
	elseif comfortType == ComfortHelper.ComfortType.Total then
		return plus + minus
	end
end

function ComfortHelper.onWorkShopUpgrade(id, lv)
	if not HouseModel.instance:isInHouseOrIsland() then return end
	local total = ComfortHelper.getTotalComfort(ComfortHelper.ComfortType.Nagative)
	local config = WorkshopConfig.getWorkshopUpgradeCfg(id)
	local change = config[lv].comfort - config[lv - 1].comfort
	HouseModel.instance:setUserProperty(HouseUserProperty.ComfortMinus, total + change)
end

function ComfortHelper.getLB()
	local all = ComfortHelper.getTotalComfort(ComfortHelper.ComfortType.Positive)
	local minus = ComfortHelper.getTotalComfort(ComfortHelper.ComfortType.Nagative)
	local config = string.splitToNumber(GameUtils.getCommonConfig("ComfortBuffParam"), ",")
	local a, b, c = config[1], config[2], config[3]
	local lb = all /(a * all + b) + c
	-- printInfo("--------开始舒适度计算---------")
	-- printInfo("--------正舒适度：", all)
	-- printInfo("--------负舒适度：", minus)
	-- printInfo(string.format("--------a=%f, b=%f, c=%f", a, b, c))
	local display = math.floor(lb * 100)
	-- printInfo(string.format("--------LB=%d%%", display))
	return display
end

function ComfortHelper.enoughComfortToPlace(furnitureIds, showTips)
	if furnitureIds == nil then return true end
	--不在自己小屋小岛，无法知道舒适度
	local model = HouseModel.instance
	if not model:isMyHouse() or not model:isInHouseOrIsland() or not model:getRoomParams(RoomParams.Editor_Comfort_Enable) then
		return true
	end
	showTips = showTips == nil and true or showTips
	local changeComfort = 0
	for _, furnitureId in ipairs(furnitureIds) do
		changeComfort = changeComfort + FurnitureConfig.getFurnitureDefine(furnitureId):getComfort()
	end
	local canLowerThen0 = changeComfort >= 0
	local leftComfort = ComfortHelper.getTotalComfort()
	local curEditComfort = ComfortHelper.getEditComfortChange()
	local endComfort = leftComfort + curEditComfort + changeComfort
	if not canLowerThen0 and endComfort < 0 then
		if showTips then FlyTextManager.instance:showFlyText(lang("舒适度不足")) end
		return false
	else
		return true
	end
end

function ComfortHelper.getEditComfortChange()
	local comfortEnable = HouseModel.instance:getRoomParams(RoomParams.Editor_Comfort_Enable)
	if comfortEnable and RoomEditModel.instance:isEdit() then
		local saveComfort = ComfortHelper._getTotalComfort(SceneManager.instance:getCurScene().unitFactory:getHouseMo():getRoomFurnitureList())
		local editComfort = ComfortHelper._getTotalComfort(RoomEditFurnitureModel.instance:getEditFurnitureList())
		return editComfort - saveComfort
	else
		return 0
	end
end

function ComfortHelper._getTotalComfort(list, comfortType)
	comfortType = comfortType ~= nil and comfortType or ComfortHelper.ComfortType.Total
	local result = 0
	for _, furnitureMO in ipairs(list) do
		if furnitureMO and((comfortType == ComfortHelper.ComfortType.Total) or
		(comfortType == ComfortHelper.ComfortType.Positive and furnitureMO.cfg:getComfort() > 0) or
		(comfortType == ComfortHelper.ComfortType.Nagative and furnitureMO.cfg:getComfort() < 0)) then
			result = result + furnitureMO.cfg:getComfort()
			local pillar = furnitureMO:getProperty(FurnitureMO.prop.pillarId)
			if pillar ~= nil then
				for i, info in ipairs(pillar) do
					result = result + FurnitureConfig.getFurnitureDefine(info.defineId).comfort
				end
			end
		end
	end
	return result
end

return ComfortHelper 