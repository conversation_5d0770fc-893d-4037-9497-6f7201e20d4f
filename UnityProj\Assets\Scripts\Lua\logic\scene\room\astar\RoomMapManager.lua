module("logic.scene.room.astar.RoomMapManager", package.seeall)
local RoomMapManager = class("RoomMapManager", SceneMapManager)

function RoomMapManager:onEnterScene(sceneId, bornX, bornZ)
	RoomMapManager.super.onEnterScene(self, sceneId, bornX, bornZ)
	self._furnitureNotWalkableList = {}
	self._furnitureRemoveList = {}
	self._houseWalkableList = {}
	self._houseRemoveList = {}
end

function RoomMapManager:onPreloadFinished()
	RoomMapManager.super.onPreloadFinished(self)
	if Framework.OSDef.isEditor then
		self.roomDebugger = PjAobi.RoomSceneDebuger.Get(self._scene.stage.stageGO)
		self.roomDebugger:SetWalkData(self.mapData)
	end
end


local randomPointList = {}

function RoomMapManager:findRandomWalkablePoint(x, y, range)
	if self._scene.unitFactory:getRoomSuite() == nil then
		printError("小屋还没初始化好，就调用随机可行走位置findRandomWalkablePoint")
		return false
	end
	if not self.mapData:IsWorldPosWalkable(x, y) then
		return false
	end
	local maper = self._scene.unitFactory:getRoomSuite():getComponent(RoomCompMaper)
	local coordX, coordY = maper:worldPosToMapPos2(FurnitureType.CoordType.Floor, false, x, y)
	if coordX == nil then
		printError("随机可行走区域时，找不到小岛地图资源：", x, y, range)
		return false
	end
	local randomCount = math.min(20, (range + 1) ^ 2)
	if #randomPointList < randomCount then
		for i= #randomPointList + 1, randomCount do
			randomPointList[i] = {x=0,y=0}
		end
	end
	for i=1, randomCount do
		randomPointList[i].x = math.random(coordX - range, coordX + range)
		randomPointList[i].y = math.random(coordY - range, coordY + range)
	end

	local wPos, wayPoints, find, tx, ty
	for i = 1, randomCount do
		tx, ty = maper:mapPosToWorldPos2(FurnitureType.CoordType.Floor, false, randomPointList[i].x, randomPointList[i].y)
		if self.mapData:IsWorldPosWalkable(tx, ty) then
			wayPoints, find = self:findPathByStartPos(x, y, tx, ty)
			if find then
				return true, Vector2.New(tx, ty)
			end
		end
	end
	return false
end

function RoomMapManager:rebuildHouse()
	self._furnitureNotWalkableList = {}
	self._furnitureRemoveList = {}
	self._houseWalkableList = {}
	self._houseRemoveList = {}
end

function RoomMapManager:clearFurnitureWalkable()
	if self._furnitureNotWalkableList == nil then return end
	if #self._furnitureNotWalkableList > 0 then
		self._furnitureRemoveList = self._furnitureNotWalkableList
	end
	self._furnitureNotWalkableList = {}
end

function RoomMapManager:initGridOffset(maper)
	local owx, owy = maper._ox, maper._oy
	self._offsetCx, self._offsetCy = maper:worldPosToMapPos2(FurnitureType.CoordType.Floor, false, self.mapData.offsetX - owx, self.mapData.offsetY - owy - RoomConfig.FloorCellWidth / 2)
	print("initGridOffset :: ", self.mapData.offsetX, self.mapData.offsetY, owx, owy, self._offsetCx, self._offsetCy)
end

function RoomMapManager:_getWalkDataGride(cx, cy)
	return Vector2.New(cx + self._offsetCx, cy + self._offsetCy)
end

function RoomMapManager:setFurnitureNotWalkable(cx, cy)
	table.insert(self._furnitureNotWalkableList, self:_getWalkDataGride(cx, cy))
end

function RoomMapManager:resetWalkAbleForAnimals(removePos, addPos)
	if removePos and #removePos > 0 then
		self.mapData:setWorldListWalkAble(removePos, true)
	end
	if addPos and #addPos > 0 then
		self.mapData:setWorldListWalkAble(addPos, false)
	end
	self:drawMap()
end

function RoomMapManager:resetFurnitureWalkable()
	if not HouseModel.instance:getRoomParams(RoomParams.Walk_Enable) then
		return
	end
	if self.mapData == nil then return end
	if self._furnitureRemoveList and #self._furnitureRemoveList > 0 then
		self.mapData:setGridListWalkAble(self._furnitureRemoveList, true)
	end
	self._furnitureRemoveList = {}
	if self._houseRemoveList then
		local maper = self._scene.unitFactory:getRoomSuite():getComponent(RoomCompMaper)
		local removeList = {}
		local addList = {}
		for uniqueId, list in pairs(self._houseRemoveList) do
			if list then
				for _, info in ipairs(list) do
					local coord = maper:getMapCell(FurnitureType.CoordType.Floor, false, info.cx, info.cy)
					if coord ~= nil and not coord._fade then
						table.insert(addList, self:_getWalkDataGride(info.cx, info.cy))
					else
						table.insert(removeList, Vector2.New(info.x, info.y))
					end
				end
			end
		end
		if #removeList > 0 then
			self.mapData:setStorageWalkable(removeList)
		end
		if #addList > 0 then
			self.mapData:setGridListWalkAble(addList, true)
		end
	end
	self._houseRemoveList = {}
	if self._furnitureNotWalkableList ~= nil and #self._furnitureNotWalkableList > 0 then
		self.mapData:setGridListWalkAble(self._furnitureNotWalkableList, false)
	end
	local setList = {}
	if self._houseWalkableList ~= nil then
		for uniqueId, list in pairs(self._houseWalkableList) do
			if list then
				for _, info in ipairs(list) do
					table.insert(setList, self:_getWalkDataGride(info.cx, info.cy))
				end
			end
		end
	end
	if #setList > 0 then
		self.mapData:setGridListWalkAble(setList, true)
	end
	self:drawMap()
end

function RoomMapManager:clearExceptWalkable(uniqueId)
	if self._houseWalkableList == nil then return end
	self._houseRemoveList[uniqueId] = self._houseWalkableList[uniqueId]
	self._houseWalkableList[uniqueId] = nil
end

function RoomMapManager:setExceptWalkable(uniqueId, cx, cy, isMirror, expexts)
	local xIndex, yIndex = 1, 2
	if isMirror then
		xIndex, yIndex = 2, 1
	end
	local list = {}
	local maper = self._scene.unitFactory:getRoomSuite():getComponent(RoomCompMaper)
	local worldPos
	for i = 1, #expexts do
		local _cx, _cy = expexts[i] [xIndex] + cx, expexts[i] [yIndex] + cy
		worldPos = maper:mapPosToWorldPos(FurnitureType.CoordType.Floor, false, {x = _cx, y = _cy})
		table.insert(list, {x = worldPos.x, y = worldPos.y, cx = _cx, cy = _cy})
	end
	self._houseWalkableList[uniqueId] = list
	SceneTimer:removeTimer(self.resetFurnitureWalkable, self)
	SceneTimer:setTimer(1, self.resetFurnitureWalkable, self, false)
end

RoomMapManager.DrawMap = true

function RoomMapManager:drawMap()
	if self.roomDebugger and self.mapData and RoomMapManager.DrawMap then
		self.roomDebugger:SetWalkData(self.mapData)
	end
end

function RoomMapManager:onExitScene()
	RoomMapManager.super.onExitScene(self)
	self._furnitureNotWalkableList = nil
	self._furnitureRemoveList = nil
	self._houseWalkableList = nil
	self._houseRemoveList = nil
end

return RoomMapManager 