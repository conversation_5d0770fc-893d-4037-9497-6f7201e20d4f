module("logic.extensions.turntable.fenghua.FengHuaTurntableController", package.seeall)

-- 基础转盘系统的第二个实验品
local FengHuaTurntableController = class("FengHuaTurntableController", TurnTableBaseController)

function FengHuaTurntableController:onInit()
    FengHuaTurntableController.super.onInit(self)
    self.awardPreviewName = lang("奖励预览")
    self.viewName = "PlanetTurnTable"
end

function FengHuaTurntableController.gotoHandler(activityId)
    local info = ActivityModel.instance:getActivityInfo(TurnTableConfig.ActivityDefineId)
    if info and info:getActivityId() == activityId then
        FengHuaTurntableController.instance:openView()
        return true
    end
    return false
end

function FengHuaTurntableController:openView()

    if ActivityFacade.instance:getActivityIsOpen(TurnTableConfig.ActivityDefineId) then
        local info = ActivityModel.instance:getActivityInfo(TurnTableConfig.ActivityDefineId)
        self.actInfo = {
            activityId = info:getActivityId(),
            activityName = info:getActivityName(),
            controller = FengHuaTurntableController.instance
        }
        FengHuaTurntableController.instance:setActInfo(self.actInfo)
        FengHuaTurntableController.instance:setRedName({
            taskRedPointName = "TURNTABLE_TraditionTask",
            tiredGiftRedPointName = "TURNTABLE_TraditionGift"
        })  
        if FuncUnlockFacade:checkIsUnlocked(14) then
            ViewMgr.instance:open(self.viewName)
        else
            FlyTextManager.instance:showFlyText(lang("达到【岛建等级3】 开启【{1}】", self.activityName))
        end
    else
        FlyTextManager.instance:showFlyText(lang("活动未开启~"))
    end
end

-- 获取抽奖结果里的最佳品质
function FengHuaTurntableController:getBestRank()
    local bestRank = 1
    for i, v in ipairs(self.lotteryResult) do
        local rewardType = TurnTableConfig.getBonus(self.activityId)[v].rewardType
        local rank = TurnTableConfig.getPools(self.activityId)[rewardType].bubbleRankId
        if rank > bestRank then
            bestRank = rank
        end
    end
    return bestRank
end

-- 转盘们,直接返回配置的池子类型,按照泡泡背景品质类型排序，由高到低
function FengHuaTurntableController:getTurnTable()
    local pools = deepcopy(TurnTableConfig.getPools(self.activityId))
    -- table.sort(
    --     pools,
    --     function(a, b)
    --         return a.bubbleRankId > b.bubbleRankId
    --     end
    -- )
    local result = {}
    for i, v in ipairs(pools) do
        result[i] = v.id
    end
    return result
end

function FengHuaTurntableController:setRedName(redInfo)
    self.redInfo = redInfo
end

function FengHuaTurntableController:getTaskRedPointName()
    if self.redInfo == nil then
        return "TURNTABLE_FengHuaTask"
    end
    return self.redInfo.taskRedPointName
end

function FengHuaTurntableController:getTiredGiftRedPointName()
    if self.redInfo == nil then
        return "TURNTABLE_FengHuaGift"
    end
    return self.redInfo.tiredGiftRedPointName
end

function FengHuaTurntableController:playAnim()
    self.curState = TurnTableBaseController.PlayingAnimState
    TaskUtil.BlockClick(true, "TurnTableBaseController:playAnim")
    -- if self.isSkip then
    --     self:endAnim()
    -- else
    --     self:localNotify(TurnTableNotify.PlayTurnAnim)
    -- end
    self:localNotify(TurnTableNotify.PlayTurnAnim, self.isSkip)
end

-- 打开抽奖结果界面
function FengHuaTurntableController:openResultView()
    local rewardItems = {}
    print("抽奖结果：")
    for i, v in ipairs(self.lotteryResult) do
        print(i,v)
        local rewardDefine = TurnTableConfig.getBonus(self.activityId)[v]
        local item = {
            id = rewardDefine.rewards.id,
            num = rewardDefine.rewards.count
        }
        table.insert(rewardItems, item)
    end
    local shareType = TurnTableConfig.getShareType(self.activityId)
    print("跳过？跳过没有大大的effect", self.isSkip)
    -- local bigEffectUrl = "ui/promo/serviceopen/turntable/effect/ui_musicturntableview_01.prefab" --音乐转盘特用
    local bigEffectUrl = "ui/promo/serviceopen/turntable/effect/ui_dragonturntableview_lotterymc_001.prefab"
    local cfg = TurnTableConfig.getXuanYaoParams(self.activityId)
    ViewMgr.instance:open(
        "TurnTableJumpReward",
        rewardItems,
        handler(self.updateData, self),
        shareType,
        self.activityId,
        not self.isSkip,
        bigEffectUrl,
        cfg.shareType,
        cfg.lotteryType
    )
end

-- 全服抽奖记录
function FengHuaTurntableController:updateServerRecord()
    TurnTableAgent.instance:sendAct161GetGlobalLotteryRecordsRequest(handler(self.onGetServerRecordInfo, self))
    self.serverRollRecords = nil
end

-- 更新record 
function FengHuaTurntableController:onGetServerRecordInfo(records)
    self.serverRecords = records
    if self.serverRollRecords == nil then
        self.serverRollRecords = Queue.New()
        for i = 1, #records, 1 do
            self.serverRollRecords:push(records[i])
        end
    end
    self:localNotify(TurnTableNotify.OnGetServerRecord)
end

function FengHuaTurntableController:getServerRollRecords()
    local record = self.serverRollRecords:pop()
    self.serverRollRecords:push(record)
    return record
end

function FengHuaTurntableController:getServerRecords()
    local info = self.serverRecords[1]
    return self.serverRecords
end

function FengHuaTurntableController:compareRecord(v1, v2)
    return v1.rewards == v2.rewards and v1.roleSimpleInfo.id == v2.roleSimpleInfo.id
end

FengHuaTurntableController.instance = FengHuaTurntableController.New()
return FengHuaTurntableController
