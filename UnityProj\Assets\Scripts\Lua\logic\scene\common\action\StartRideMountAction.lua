module("logic.scene.common.action.StartRideMountAction", package.seeall)

local StartRideMountAction = class("StartRideMountAction", SceneActionBase)

function StartRideMountAction:onStart()
	self.isWorking = true
	self.isOnLand = SeaFacade.getMountType() == SeaEnum.MountType.Land
	SceneAgent.instance:sendSceneShareRequest(PlayerActionType.StartRideMount.typeId, {self.params.mountId}, handler(self.onShareResponse, self))
end

function StartRideMountAction:onShareResponse(status)
	if status == 0 then
		SceneController.instance:registerLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		SeaController.instance:registerLocalNotify(SeaNotify.onMountHandlerChange, self.onMountHandlerChange, self)
		-- SeaController.instance:registerLocalNotify(SeaNotify.onMountChange, self.onMountIdChange, self)
		GlobalDispatcher:addListener(GlobalNotify.OnTaskStoryStart, self.getinTask, self)
		if self.isOnLand then
			SeaModel.instance:setRidingLandMountId(self.params.mountId)
		else
			SeaModel.instance:setRidingMountId(self.params.mountId)
		end
	else
		DialogHelper.showErrorMsg(status)
		self:finish(false)
	end
	self.isWorking = false
end

function StartRideMountAction:onCancelRequest(status)
	if status == 0 then

		if self.isOnLand then
			SeaModel.instance:setRidingLandMountId(nil)
		else
			SeaModel.instance:setRidingMountId(nil)
		end
	else
		DialogHelper.showErrorMsg(status)
		self:finish(false)
	end


end

function StartRideMountAction:getType()
	return SceneActionType.StartRideMount
end

function StartRideMountAction:onMountHandlerChange(active)
	SeaController.instance:unregisterLocalNotify(SeaNotify.onMountHandlerChange, self.onMountHandlerChange, self)
	if active then
		SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.JoinRideMount.typeId, handler(self.onCancelRequest, self))
	else
		-- self:finish(true)
	end
end

function StartRideMountAction:onActionChange(nowState, oldState)
	if oldState == PlayerActionType.JoinRideMount and nowState ~= PlayerActionType.JoinRideMount then
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		self:finish(true)
	end
end

function StartRideMountAction:finish(succ)
	StartRideMountAction.super.finish(self, succ)
	GlobalDispatcher:removeListener(GlobalNotify.OnTaskStoryStart, self.getinTask, self)
	SeaController.instance:unregisterLocalNotify(SeaNotify.onMountHandlerChange, self.onMountHandlerChange, self)
	SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
	--如果在观光车也要退出观光车状态
	if SceneManager.instance:getCurScene().actionMgr:hasAction(SceneActionType.Track) then
		SceneController.instance:stopAction(SceneActionType.Track)
	end
end

function StartRideMountAction:getinTask()
	self:onMountHandlerChange(true)
end

function StartRideMountAction:onMountIdChange(mountId)
	self._willRidingId = mountId
	SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.JoinRideMount.typeId, handler(self.onSwtichCancelReply, self))
end

function StartRideMountAction:onSwtichCancelReply(status)
	if status == 0 then
		SeaController.instance:unregisterLocalNotify(SeaNotify.onMountHandlerChange, self.onMountHandlerChange, self)
		self.params.mountId = self._willRidingId
		SceneAgent.instance:sendSceneShareRequest(PlayerActionType.StartRideMount.typeId, {self.params.mountId}, handler(self.onSwitchResponse, self))
	else
		SeaController.instance:unregisterLocalNotify(SeaNotify.onMountHandlerChange, self.onMountHandlerChange, self)
		self:onCancelRequest(status)
	end
end

function StartRideMountAction:onSwitchResponse(status)
	if status == 0 then
		SeaModel.instance:setRidingMountId(self.params.mountId)
		SeaController.instance:registerLocalNotify(SeaNotify.onMountHandlerChange, self.onMountHandlerChange, self)
	else
		SeaController.instance:unregisterLocalNotify(SeaNotify.onMountChange, self.onMountIdChange, self)
		self:onShareResponse(status)
	end
end

function StartRideMountAction:onStop()
	-- local nextAction = SceneManager.instance:getCurScene().actionMgr:getNextAction() 
	if not SceneManager.instance._isEnetering then
		SeaController.instance:localNotify(SeaNotify.onMountHandlerChange, true)
	end

end

return StartRideMountAction 