module("logic.extensions.sharestreet.view.ShareStreetInfoView", package.seeall)
---@class ShareStreetInfoView
local ShareStreetInfoView = class("ShareStreetInfoView", ViewComponent)

function ShareStreetInfoView:ctor()
    ShareStreetInfoView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetInfoView:buildUI()
    ShareStreetInfoView.super.buildUI(self)

    self._tgTab = ToggleGroup.New(handler(self._onClickTab, self))
    self._tgTab:addView(self:getGo("tabGo/tab_1"))
    self._tgTab:addView(self:getGo("tabGo/tab_2"))

    self._goPage1 = self:getGo("page_1")
    self._goPage2 = self:getGo("page_2")

    self._goImgDesInput = self:getGo("page_1/content/desGo/desInputField/Image")
    self._goImgNameInput = self:getGo("page_1/content/nameGo/nameInputField/Image")

    self._txtName = self:getText("page_1/content/nameGo/nameInputField/Text")
    self._txtDes = self:getText("page_1/content/desGo/desInputField/Text")

    self._btnCopy = self:getBtn("page_1/content/idGo/btnCopy")
    self._txtStreetId = self:getText("page_1/content/idGo/txtMaster")
    self._txtOwnerName = self:getText("page_1/content/masterGo/txtMaster")

    self._btnChangePic = self:getBtn("page_1/content/photoGo/btnChange")
    self._goPic = self:getGo("page_1/content/photoGo/imgPic")

    self._btnSetDefaultScene = self:getBtn("page_2/socializeListview/content/GoFriend/socializeItem")
    self._goDefaultSceneSelected = self:getGo("page_2/socializeListview/content/GoFriend/socializeItem/imgSelected")

    self._btnChangeName = self:getBtn("page_1/content/nameGo/nameInputField")
    self._btnChangeDes = self:getBtn("page_1/content/desGo/desInputField")
    self._btnClose = self:getBtn("imgGroup/btnClose")
end

--- view初始化时会执行，在buildUI之后
function ShareStreetInfoView:bindEvents()
    ShareStreetInfoView.super.bindEvents(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetInfoView:onEnter()
    ShareStreetInfoView.super.onEnter(self)

    self._isReadOnly = self:getOpenParam()[1]
    self._ownerId = self:getOpenParam()[2]
    self._streetInfo = ShareStreetModel.instance:getUserInfo(self._ownerId)
    self._tgTab:clickViews(true, 1)

    self._goImgNameInput:SetActive(not self._isReadOnly)
    self._goImgDesInput:SetActive(not self._isReadOnly)
    self._btnChangePic.gameObject:SetActive(not self._isReadOnly)
    self._btnChangePic:AddClickListener(handler(self._onClickChangePic, self))

    self._btnClose:AddClickListener(handler(self.close, self))
    self._btnSetDefaultScene:AddClickListener(handler(self._onClickSetDefaultScene, self))
    self._btnChangeName:AddClickListener(handler(self._onClickChangeName, self))
    self._btnChangeDes:AddClickListener(handler(self._onClickChangeDes, self))
	self._btnCopy:AddClickListener(self._onBtnCopyClick, self)

    self:registerLocalNotify(ShareStreetLocalNotify.OnChangeNameSucc, self._onChangeNameSucc, self)
    self:registerLocalNotify(ShareStreetLocalNotify.OnChangeDescSucc, self._onChangeDescSucc, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetInfoView:onEnterFinished()
    ShareStreetInfoView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetInfoView:onExit()
    ShareStreetInfoView.super.onExit(self)

    self._btnClose:RemoveClickListener()
    self._btnChangeName:RemoveClickListener()
    self._btnChangeDes:RemoveClickListener()
    self._btnChangePic:RemoveClickListener()
    self._btnSetDefaultScene:RemoveClickListener()
    self._btnCopy:RemoveClickListener()

    self:unregisterLocalNotify(ShareStreetLocalNotify.OnChangeNameSucc, self._onChangeNameSucc, self)
    self:unregisterLocalNotify(ShareStreetLocalNotify.OnChangeDescSucc, self._onChangeDescSucc, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetInfoView:onExitFinished()
    ShareStreetInfoView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetInfoView:unbindEvents()
    ShareStreetInfoView.super.unbindEvents(self)
end

--- view销毁时会执行
function ShareStreetInfoView:destroyUI()
    ShareStreetInfoView.super.destroyUI(self)
end

function ShareStreetInfoView:_onClickTab(index, isSelected)
    if not isSelected then
        return
    end
    self._selectedIndex = index
    self:_updateView()
end

function ShareStreetInfoView:_updateView()
    self._goPage1:SetActive(self._selectedIndex == 1)
    self._goPage2:SetActive(self._selectedIndex == 2)

    if self._selectedIndex == 1 then
        self._txtName.text = self._streetInfo:getName()
        self._txtDes.text = self._streetInfo:getDesc()
        self._txtStreetId.text = self._streetInfo:getDisplayId()
        self._txtOwnerName.text = self._streetInfo:getOwnerRoleInfo().nickname
        self:_updatePic()
    elseif self._selectedIndex == 2 then
        self._goDefaultSceneSelected:SetActive(ShareStreetFacade.instance:isSet2DefaultScene())
    end
end

function ShareStreetInfoView:_onBtnCopyClick()
	Clipboard.copy(self._streetInfo:getDisplayId())
    FlyTextManager.instance:showFlyText(lang("已复制到剪切板"))
end

function ShareStreetInfoView:_onClickSetDefaultScene()
    ShareStreetFacade.instance:setDefaultScene2ShareStreet(not ShareStreetFacade.instance:isSet2DefaultScene())
    self._goDefaultSceneSelected:SetActive(ShareStreetFacade.instance:isSet2DefaultScene())
end

function ShareStreetInfoView:_onClickChangeName()
    if self._isReadOnly then return end
    ViewMgr.instance:open("ShareStreetEditNameView")
end

function ShareStreetInfoView:_onClickChangeDes()
    if self._isReadOnly then return end
    ViewMgr.instance:open("ShareStreetEditDescView")
end

function ShareStreetInfoView:_onChangeNameSucc()
    self._txtName.text = self._streetInfo:getName()
end

function ShareStreetInfoView:_onChangeDescSucc()
    self._txtDes.text = self._streetInfo:getDesc()
end

function ShareStreetInfoView:_onClickChangePic()
    if not ShareStreetFacade.instance:isInMyStreet() then
        FlyTextManager.instance:showFlyText(lang("请先进入自己的街区"))
        return
    end
    if ShareStreetModel.instance:getTakePhotoTime() and ServerTime.now() - ShareStreetModel.instance:getTakePhotoTime() < 10 then
        FlyTextManager.instance:showFlyText(lang("街区封面上传中，请稍后"))
        return
    end
    DialogHelper.showConfirmDlg(lang("是否确定要重新拍摄街区照片？"), function(isOk)
        if not isOk then
            return
        end
        
        LoadingMask.instance:show()
        print("ShareStreetInfoView", "GameUtils.takePanoramaPhoto")
        GameUtils.takePanoramaPhoto(function(tex)
            print(tex)
            if tex then
                local onUploadCallback = function(succ, uploadInfo)
                    print("ShareStreetInfoView","onUploadCallback", succ)
                    LoadingMask.instance:close()
                    if succ then
                        ShareStreetModel.instance:setTakePhotoTime()
                        local url = uploadInfo[1].picOssUrl
                        print("url", url)
                        self._streetInfo:setPicture(url)
                        self:_updatePic()
                        -- UserAgent.instance:sendReportUserRequest(tostring(self._userId), reportConfig.type, url, reportId,
                        --     extData and extData.speakTime, extData and encodeJson(extData), handler(self._onRspn, self))
                    else
                        FlyTextManager.instance:showFlyText(lang("上传图片失败, 请稍后再试"))
                    end
                end
                if self._imageUploader == nil then
                    self._imageUploader = MultiImageUploader.New(ImageExtension_pb.SHARE_STREET, "gjjqfm", false, false)
                end
                ----这里因为上传的是bytes，所以不需要记录路径
                self._imageUploader:setAllFilePaths({tex})
                self._imageUploader:load(onUploadCallback, nil)
            end
        end)
    end)
end

function ShareStreetInfoView:_updatePic()
    ShareStreetUtil.setPic(self._goPic, self._streetInfo:getPicture(), 310, 178)
end

return ShareStreetInfoView
