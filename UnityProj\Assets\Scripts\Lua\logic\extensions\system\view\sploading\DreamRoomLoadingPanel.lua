module("logic.extensions.system.view.sploading.DreamRoomLoadingPanel",package.seeall)
local DreamRoomLoadingPanel = class("DreamRoomLoadingPanel", ViewComponent)

function DreamRoomLoadingPanel:buildUI()
    self.spineList = {}
    self.spineList[1] = SpineAnimationHelper.New(self:getGo("SkeletonGraphic (back)"))
    self.spineList[2] = SpineAnimationHelper.New(self:getGo("SkeletonGraphic (front)"))
    self.spineList[3] = SpineAnimationHelper.New(self:getGo("SkeletonGraphic (middle)"))
end

function DreamRoomLoadingPanel:destroyUI()

end

function DreamRoomLoadingPanel:onEnter()
    for i = 1, 3 do
        self.spineList[i]:setAnimation("start", false, 0)
        self.spineList[i]:addAnimation("idle", true, 0)
        -- local front = self:getGo("spine"):GetComponent(SpineUnitComp.SkeletonGraphicType)
        -- front:Update(0.1)
    end
end

function DreamRoomLoadingPanel:onExit()

end

return DreamRoomLoadingPanel