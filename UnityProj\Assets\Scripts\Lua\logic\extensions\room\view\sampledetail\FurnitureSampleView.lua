module("logic.extensions.room.view.sampledetail.FurnitureSampleView", package.seeall)

local FurnitureSampleView = class("FurnitureSampleView", ListBinderView)

FurnitureSampleView.ItemUrl = "ui/scene/room/edittemplateviewitem.prefab"

FurnitureSampleView.SortState = {
	state0 = 0 , --无法替换
	state1 = 2 , --已替换
	state2 = 1 , --可替换
	state3 = 3 , --未替换可摆放
}

function FurnitureSampleView.show(sample, templateId, canFocusFurniture, islandType)
	ViewMgr.instance:open("FurnitureSamplePreview", {sample, templateId, canFocusFurniture, islandType})
end

function FurnitureSampleView:ctor()
	FurnitureSampleView.super.ctor(self, BaseListModel.New(), "listView", FurnitureSampleView.ItemUrl, FurnitureSampleCell, {kScrollDirV, 88, 88, 11.8, 10.6, 3})
end

function FurnitureSampleView:onExit()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.SearchFurnitureByIdSuc, self.close, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnSampleReplaceSingleFurniture, self.replaceSingleFurnitureInSample, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnSampleReplaceAllFurniture, self.replaceAllFurnitureIdInSample, self)


	FurnitureSampleView.super.onExit(self)
end

function FurnitureSampleView:destroyUI()
	FurnitureSampleView.super.destroyUI(self)
	self._preview:destroy()
	self._preview = nil
end

function FurnitureSampleView:buildUI()
	FurnitureSampleView.super.buildUI(self)
	self:getBtn("btnClose"):AddClickListener(self.onClickClose, self)
	self._txtName1 = self:getText("txtGounp/txtName1")
	self._txtName2 = self:getText("txtGounp/txtName2")
	self._txtTitle1 = self:getText("txtGounp/txtTitle1")
	self._txtComfort = self:getText("txtGounp/txtGo/txtComfort")
	self._txtSize = self:getText("txtGounp/txtGo/txtSize")
	self._preview = SamplePreview.New(self:getGo("mask/icon"))
	self:getBtn("txtGounp/txtName2/btnModify"):AddClickListener(self._onClickModifyName, self)
	self:getBtn("btnPut"):AddClickListener(self._onClickShow, self)
	self._btnShare = self:getBtn("btnGo/btnShare")
	self._btnShare:AddClickListener(self._onClickShare, self)
	self._btnDel = self:getBtn("btnGo/btnDel")
	self._btnDel:AddClickListener(self._onClickDel, self)
	self._imgShare = self:getGo("imgShare")

	self:getGo("typeGo"):SetActive(false)
	self:getGo("btnLook"):SetActive(false)
end

function FurnitureSampleView:onEnter()
	FurnitureSampleView.super.onEnter(self)
	self.shareFurniture = nil
	self._sample = self:getFirstParam()[1]
	self._templateId = self:getFirstParam()[2]
	self._canFocusFurniture = self:getFirstParam()[3]
	self._isSelf = self._templateId ~= nil
	self._txtName1.text = self._sample[1].name
	self._txtName2.text = self._sample[1].name
	
	local isShare = self._templateId and RoomSampleModel.instance:getSample(self._templateId):isShared()
	self._btnShare.gameObject:SetActive(self._isSelf and not isShare)
	
	self._btnDel.gameObject:SetActive(self._isSelf and isShare)
	self._imgShare:SetActive(self._isSelf and isShare)
	
	self._txtName1.gameObject:SetActive(not self._isSelf)
	self._txtName2.gameObject:SetActive(self._isSelf)
	self._txtTitle1.text = lang(self._isSelf and "自定义样板" or "推荐样板")
	self._preview:showHouse(self._sample, self:getFirstParam()[4])
	
	-- self._curDataList = {}
	-- self._curDataHash = {}

	-- local list = {}
	-- local totalCount = #self._sample
	-- local collectCount = 0
	-- local ownMap = {}
	-- local hasCollect, hasFree, furnitureId, w, h, furniture,replaceId,hasReplace,sortState
	-- local totalSize, totalComform = 0, 0
	-- for i, config in ipairs(self._sample) do
	-- 	furnitureId = config.furniture.id
	-- 	ownMap[furnitureId] =(ownMap[furnitureId] or 0) + 1
	-- 	hasCollect = ItemService.instance:getItemTotalNum(furnitureId) >= ownMap[furnitureId]
	-- 	hasFree = hasCollect and RoomEditModel.instance:getItemNum(furnitureId) >= ownMap[furnitureId]
	-- 	if hasCollect then
	-- 		collectCount = collectCount + 1
	-- 	end
	-- 	furniture = FurnitureConfig.getFurnitureDefine(furnitureId)
	-- 	w, h = RoomConfig.getFurnitureSize(furniture.id)
	-- 	totalSize = totalSize + (w * h)
	-- 	totalComform = totalComform + furniture.comfort
	-- 	replaceId = furnitureId
	-- 	hasReplace = false 
	-- 	if hasCollect then
	-- 		if hasFree then
	-- 			sortState = FurnitureSampleView.SortState.state3
	-- 		else
	-- 			sortState = FurnitureSampleView.SortState.state1
	-- 		end 
	-- 	else
	-- 		sortState = FurnitureSampleView.SortState.state0
	-- 	end
	-- 	local data = {id = i, itemId = furnitureId, hasCollect = hasCollect, hasFree = hasFree,hasReplace = hasReplace , replaceId = replaceId,sortState = sortState}
	-- 	table.insert(list, data)
	-- 	self._curDataHash[data.id] = data
	-- end
	-- self._txtComfort.text = totalComform
	-- self._txtSize.text = totalSize
	-- table.sort(list, function(a, b)
	-- 	if a.hasCollect and not b.hasCollect then
	-- 		return true
	-- 	elseif not a.hasCollect and b.hasCollect then
	-- 		return false
	-- 	elseif not a.hasFree and b.hasFree then
	-- 		return false
	-- 	elseif a.hasFree and not b.hasFree then
	-- 		return true
	-- 	else
	-- 		return a.itemId < b.itemId
	-- 	end
	-- end)
	-- self._listModel:setMoList(list)
	-- self._curDataList = list
	-- self:getText("txtProgress").text = lang("{1}/{2}", collectCount, totalCount)
	-- self:getGo("fullArea/imgFill"):GetComponent(goutil.Type_UIImage).fillAmount = collectCount / totalCount
	self:_initDataList()


	RoomController.instance:registerLocalNotify(RoomNotifyName.SearchFurnitureByIdSuc, self.close, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnSampleReplaceSingleFurniture, self.replaceSingleFurnitureInSample, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnSampleReplaceAllFurniture, self.replaceAllFurnitureIdInSample, self)

end

function FurnitureSampleView:_onClickModifyName()
	EditSampleName.show(self._sample[1].name, self._onInputName, self)
end

function FurnitureSampleView:_onInputName(name)
	self._changeName = name
	IslandAgent.instance:sendIslandModifyFurnitureTemplateNameRequest(self._templateId, name, handler(self._onNameChange, self))
end

function FurnitureSampleView:_onNameChange()
	self._txtName1.text = self._changeName
	self._txtName2.text = self._changeName
	RoomSampleModel.instance:changeName(self._templateId, self._changeName)
end

function FurnitureSampleView:_onClickShow()
	self:close()
	local furnitures = {}
	local furniture 
	for id, config in ipairs(self._sample) do
		local data = self._curDataHash[id]
		furniture = {id = data.replaceId,x = config.furniture.x,y = config.furniture.y,rotation = config.furniture. rotation,stackLayer = config.furniture.stackLayer }
		table.insert(furnitures, furniture)
	end
	RoomController.instance:localNotify(RoomNotifyName.AddNewMultiDragFurnitures, furnitures, self._templateId)
end

function FurnitureSampleView:_onClickShare()
	FurnitureTempalteSquareController.instance:openSelectLabelView(#self._sample,function(labelId)
		self._preview:getTexture(function(tex)
			FurnitureTempalteSquareController.instance:sendUploadShareFurnitureTemplateRequest(tex, self._templateId, self._sample[1].name, labelId,function(uniqueId)
				RoomSampleModel.instance:getSample(self._templateId).shareUniqueId = uniqueId
				FlyTextManager.instance:showFlyText("提交成功！岛务厅审核中...")
				RoomController.instance:localNotify(RoomNotifyName.OnSampleShareSucc,self._templateId)			
	
				self:close()
			end,UserInfo.userId)
		end)
	end)
end

function FurnitureSampleView:_onClickDel()
	local uniqueId = RoomSampleModel.instance:getSample(self._templateId).shareUniqueId
	FurnitureTempalteSquareController.instance:sendCancelShareFurnitureTemplateRequest(uniqueId,function()
			RoomSampleModel.instance:getSample(self._templateId).shareUniqueId = nil
			self._btnShare.gameObject:SetActive(true)
			self._btnDel.gameObject:SetActive(false)
			self._imgShare:SetActive(false)
			RoomController.instance:localNotify(RoomNotifyName.OnSampleShareSucc,self._templateId)
	end)
end

-- 替换指定索引家具,Id:家具索引，NewFurId：替换的家具配置Id
function FurnitureSampleView:replaceSingleFurnitureInSample(Id,NewFurId)
	local data = self._curDataHash[Id]
	if not data or data.replaceId == NewFurId then return end 
	if  NewFurId == data.itemId then
		self._replaceMap[data.itemId][data.replaceId] = self._replaceMap[data.itemId][data.replaceId]+1
	else
		if self:getCurItemReplaceCount(data.itemId)<=0 then return end 
		self._replaceMap[data.itemId][NewFurId] = self._replaceMap[data.itemId][NewFurId]-1
	end 
	data.replaceId = NewFurId

	self:_reloadCurDataList()
end

-- 指定种类家具全部替换,FurId:被替换的家具id，NewFurId：替换的家具配置Id
function FurnitureSampleView:replaceAllFurnitureIdInSample(FurId, NewFurId, isOccupyed)
	if FurId == NewFurId then return end 
	if self:getCurItemReplaceCount(FurId)<=0 then return end 
	local replaceAbleCount = self._replaceMap[FurId][NewFurId]
	
	if replaceAbleCount <= 0 then return end

	local matchDataList = {}
	for _ , data in ipairs (self._curDataList) do 
		--被占用的家具,全部替换也是被占用的家具
		if isOccupyed == data.isOccupyed and data.itemId == FurId and data.sortState == FurnitureSampleView.SortState.state2  then
			table.insert(matchDataList,data)
		end 
	end 
	replaceAbleCount = math.min(#matchDataList,replaceAbleCount)
	for i = 1, replaceAbleCount do 
		matchDataList[i].replaceId = NewFurId
	end 
	self._replaceMap[FurId][NewFurId] = self._replaceMap[FurId][NewFurId] - replaceAbleCount
	self:_reloadCurDataList()
end

-- 重新排序
function FurnitureSampleView:_reloadCurDataList()
	self._isExistReplace = false
	-- 计算当前缺失数量
	self.curMissCountHash = {}
	for _, v in ipairs(self._curDataList) do 
		if not ( v.sortState == FurnitureSampleView.SortState.state3) then
			if v.replaceId ~= v.itemId then
				v.sortState = FurnitureSampleView.SortState.state1
				self._isExistReplace = true
			else
				local curMissCount = self.curMissCountHash[v.itemId] or 0
				curMissCount = curMissCount + 1
				if  self:getCurItemReplaceCount(v.itemId)>0 then
					v.sortState = FurnitureSampleView.SortState.state2
				else
					v.sortState = FurnitureSampleView.SortState.state0
				end 
				self.curMissCountHash[v.itemId] = curMissCount
			end 
		end 
	end 

	table.sort(self._curDataList, function(a, b)
		local ownState1 = a.sortState == FurnitureSampleView.SortState.state3 and 1 or 0
		local ownState2 = b.sortState == FurnitureSampleView.SortState.state3 and 1 or 0
		--可直接摆放的优先级最高
		if ownState1 ~= ownState2 then
			return ownState1 > ownState2
		else
			--接着排被占用的
			local state1 = a.isOccupyed and 1 or 0
			local state2 = b.isOccupyed and 1 or 0
			if state1 ~= state2 then
				return state1 > state2
			else
				if a.sortState >b.sortState then
					return true
				elseif a.sortState <b.sortState then
					return false
				else
					local quality1 = ItemService.instance:getRank(a.itemId)
					local quality2 = ItemService.instance:getRank(b.itemId)
					if quality1 ~= quality2 then
						return quality1 > quality2
					else
						return a.itemId < b.itemId	
					end
				end	
			end
		end
	
	end)
	self._listModel:setMoList(self._curDataList)
end 

-- 初始化数据列表，计算并设置样板的舒适度
function FurnitureSampleView:_initDataList()
	-- 数值部分
	self._curDataList = {}
	self._curDataHash = {}
	self._replaceMap = {}

	local totalCount,collectCount= #self._sample,0 
	local ownMap = {}
	local totalSize, totalComform = 0, 0
	local hasCollect, hasFree, furnitureId, w, h, furniture,sortState,isOccupyed
	for i, config in ipairs(self._sample) do
		furnitureId = config.furniture.id
		furniture = FurnitureConfig.getFurnitureDefine(furnitureId)
		w, h = RoomConfig.getFurnitureSize(furniture.id)
		totalSize = totalSize + (w * h)
		totalComform = totalComform + furniture.comfort
		-- 设置数据状态，计数
		ownMap[furnitureId] =(ownMap[furnitureId] or 0) + 1
		hasCollect = ItemService.instance:getItemTotalNum(furnitureId) >= ownMap[furnitureId]
		hasFree = hasCollect and RoomEditModel.instance:getItemNum(furnitureId) >= ownMap[furnitureId]
		
		isOccupyed = false
		if hasCollect then
			collectCount = collectCount + 1 
			if hasFree then
				sortState = FurnitureSampleView.SortState.state3
			else
				isOccupyed = true
				self:_initRePlaceMap(furnitureId)
				sortState = FurnitureSampleView.SortState.state0
				if self:getCurItemReplaceCount(furnitureId) >0 then
					sortState = FurnitureSampleView.SortState.state2
				end
			end 
		else
			self:_initRePlaceMap(furnitureId)
			sortState = FurnitureSampleView.SortState.state0
			if self:getCurItemReplaceCount(furnitureId) >0 then
				sortState = FurnitureSampleView.SortState.state2
			end 
		end 
		local data = {id = i, itemId = furnitureId, replaceId = furnitureId, sortState = sortState, isOccupyed = isOccupyed}
		table.insert(self._curDataList, data)
		self._curDataHash[data.id] = data
	end 

	-- 替换数量还要排除掉可使用的干扰
	for _,data in ipairs(self._curDataList) do 
		if data.sortState == FurnitureSampleView.SortState.state3 then
			for __, map in pairs(self._replaceMap) do 
				local count = map[data.itemId]
				if count then
					count = count -1
					if count <= 0 then
						count = nil 
					end 
					map[data.itemId] = count 
				end 
			end 
		end 
	end 
	-- 排序设置
	self:_reloadCurDataList()
	-- UI部分
	self._txtComfort.text = totalComform
	self._txtSize.text = totalSize
	self:getText("txtProgress").text = lang("{1}/{2}", collectCount, totalCount)
	self:getGo("fullArea/imgFill"):GetComponent(goutil.Type_UIImage).fillAmount = collectCount / totalCount
end 

-- 刷新替换Map数据
function FurnitureSampleView:_initRePlaceMap(itemId)
	if not self._replaceMap[itemId] then
		local curItemReplaceMap =  {}
		local itemDefine =  FurnitureConfig.getFurnitureDefine(itemId)
		local w,h = RoomConfig.getFurnitureSize(itemId)
		local allSameTypeFurs = ItemService.instance:getItems(ItemType.FURNITURE,itemDefine.subType) or {}
		local _w,_h
		local matchIdHash = {}
		for _, v in ipairs(allSameTypeFurs) do
            local checkFurId =  v:getDefine().id
			if checkFurId ~= itemId then
				if not matchIdHash[checkFurId] then
					_w,_h = RoomConfig.getFurnitureSize(checkFurId)
					if w == _w and h == _h then
						matchIdHash[checkFurId] = true
					end 				
				end 
			end 
		end 
		for furId , _ in pairs(matchIdHash) do 
			local count = RoomEditModel.instance:getItemNum(furId)
			curItemReplaceMap[furId] = count
		end 
		self._replaceMap[itemId] = curItemReplaceMap
	end 
end

function FurnitureSampleView:getCurItemReplaceCount(itemId)
	local count = 0
	local map = self._replaceMap[itemId] or {}
	for _,num in pairs(map) do 
		count = count + num
	end 
	return count
end 

function FurnitureSampleView:onClickClose()
	if self._isExistReplace then
		local msg = lang("存在临时被替换的样板家具，关闭弹窗后将还原样板家具，确定关闭？")
		DialogHelper.showConfirmDlg(msg, function(isOK)
			if not isOK then return end
			self:close()
		end)
	else
		self:close()
	end 
	
end 

return FurnitureSampleView 