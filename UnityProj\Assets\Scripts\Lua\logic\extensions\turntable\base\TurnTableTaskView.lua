module("logic.extensions.turntable.base.TurnTableTaskView", package.seeall)

local TurnTableTaskView = class("TurnTableTaskView", ActivityDetailView)

-- info = {
--     rewards,
--     name,
--     taskIds,
--     introduction,
--     detailBgPath,
--     activityId
-- }

function TurnTableTaskView:onEnter()
    self.openParam = self:getOpenParam()[1]
    self.activityDefineId = self:getOpenParam()[2]
    self._txtActivityName.text = self.openParam.name
    self._txtActivityIntroduction.text = self.openParam.introduction
    IconLoader.setSpriteToImg(self._imgPicture, self.openParam.detailBgPath)

    self.isComplete = self:checkTaskIsComplete()
    self._btnGo:AddClickListener(self._onClickGo, self)
    self._btnGo.gameObject:SetActive(not self.isComplete)
    self:getGo("imgComplete"):SetActive(self.isComplete)

    self:setRewards()
    self:setTime()
end

function TurnTableTaskView:setTime()
    self._txtActivityTime.text = lang("剩余时间：{1}", ActivityFacade.instance:getActivityInfo(self.activityDefineId):getRemainingTimeText())
end

function TurnTableTaskView:setRewards()
    self:clearIcons()
    for i, v in ipairs(self.openParam.rewards) do
        -- local item = goutil.clone(self._awarditem, v.itemId)
        -- goutil.addChildToParent(item, self._awardListview)
        -- goutil.findChild(item, "imgIcon"):SetActive(false)
        -- goutil.findChild(item, "txtCount"):SetActive(false)
        local commonIcon = CommonIconMgr.instance:fetchCommonIcon()
        table.insert(self.icons, commonIcon)
        commonIcon:setWidthAndHeight(89):showFreeState(true):showRarenessGo(true):showSpecial(true):needClick(true)
        if v.count > 0 then
            commonIcon:showCount(true)
        else
            commonIcon:showCount(false)
        end 
        commonIcon:buildData({id = v.itemId, num = v.count})
        goutil.addChildToParent(commonIcon:getPrefab(), self._awardListview)
        -- commonIcon:getPrefab().transform:SetSiblingIndex(1)
        -- item:SetActive(true)
    end
    -- self._awardListview:SetActive(true)
end

function TurnTableTaskView:checkTaskIsComplete()
    local allComplete = true
    for i, taskId in ipairs(self.openParam.taskIds) do
        if TaskModel.instance:getTaskStatus(taskId) ~= TaskMO.Complete then
            allComplete = false
            break
        end
    end
    return allComplete
end

function TurnTableTaskView:_onClickGo()
    local currentId
    for i, taskId in ipairs(self.openParam.taskIds) do
        print("sdfsdf", taskId, TaskModel.instance:getTaskStatus(taskId))
        if TaskModel.instance:getTaskStatus(taskId) ~= TaskMO.Complete then
            currentId = taskId
            break
        end
    end
    print("sdfsdf", currentId)
    ViewMgr.instance:open("TaskObjectivePanel", TaskObjectivePanel.Tab_Business, currentId)
end

function TurnTableTaskView:clearIcons()
    if self.icons then
        for i, v in ipairs(self.icons) do
            CommonIconMgr.instance:returnCommonIcon(v)
        end
    end
    self.icons = {}
end

function TurnTableTaskView:onExit()
    self:clearIcons()
end

return TurnTableTaskView
