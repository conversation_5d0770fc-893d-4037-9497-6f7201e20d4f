module("logic.extensions.farm.core.component.FarmCompPlant", package.seeall)
local FarmCompPlant = class("FarmCompPlant", ECSComponent)

function FarmCompPlant:onInit()
	if not HouseModel.instance:getRoomParams(RoomParams.Plant_Enable) then return end
	self._camera = CameraTargetMgr.instance:getMainCameraTarget():getCamera()
	self._scene = SceneManager.instance:getCurScene()
	self._factory = self._scene.unitFactory
	self._unitDict = {}
	
	self._effectPool = {}
	self._effectList = {}
	self._tempHarvest = {}
	self._tempPlant = {}
	self._tempBonus = {}
	self._tempEndTime = {}
	self._lastHarvestSoundTime = 0
end

function FarmCompPlant:onDestroy()
	if not HouseModel.instance:getRoomParams(RoomParams.Plant_Enable) then return end
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.ClickEarth, self._onClickEarth, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.ClickPlant, self._onClickPlantOrAnimal, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.ClickAnimal, self._onClickPlantOrAnimal, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.FurnitureLoaded, self._handleUnitLoaded, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.OnDragHarvestTool, self._onDragHarvestTool, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.OnBeginDragHarvestTool, self._onBeginDragHarvestTool, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.EndDragHarvestTool, self._doHarvest, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnRecycleFurniture, self._onRecycleFurniture, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnDecorateSucc, self._setPlantUnit, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.OnSpeedUpPlant, self._onSpeedUpPlant, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnResetEditFurniture, self._onResetEditFur, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.SetBaseMode, self._setBaseMode, self)
	SceneTimer:removeTimer(self._onTick, self)
	self._camera = nil
	self._scene = nil
	self._factory = nil
	self._unitDict = nil
	self._effectPool = nil
	self._effectList = nil
	self._tempHarvest = nil
	self._tempPlant = nil
	self._tempBonus = nil
	self._compFur = nil
end

function FarmCompPlant:onEnter()
	if not HouseModel.instance:getRoomParams(RoomParams.Plant_Enable) then return end
	FarmController.instance:registerLocalNotify(FarmNotifyName.ClickEarth, self._onClickEarth, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.ClickPlant, self._onClickPlantOrAnimal, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.ClickAnimal, self._onClickPlantOrAnimal, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.FurnitureLoaded, self._handleUnitLoaded, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.OnDragHarvestTool, self._onDragHarvestTool, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.OnBeginDragHarvestTool, self._onBeginDragHarvestTool, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.EndDragHarvestTool, self._doHarvest, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnRecycleFurniture, self._onRecycleFurniture, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnDecorateSucc, self._setPlantUnit, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.OnSpeedUpPlant, self._onSpeedUpPlant, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnResetEditFurniture, self._onResetEditFur, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.SetBaseMode, self._setBaseMode, self)
	SceneTimer:setTimer(1, self._onTick, self, true)
	self:_setPlantUnit()
end

function FarmCompPlant:_setBaseMode(isBaseMode, baseModeType)
	local activeBaseModeType = FurnitureConfig.getPartDefine(FurnitureConfig.getFurnitureDefine(HarvestConfig.earthId()).subType).activeBaseModeType
	local activeInBaseMode = not isBaseMode or not bitutil.checkBitValue(activeBaseModeType, baseModeType - 1)
	local plantList = FarmModel.instance:getAllPlant()
	for _, plantMO in ipairs(plantList) do
		if plantMO:isCrop() then
			local cropsUnit = self._unitDict[plantMO.uniqueId]
			if cropsUnit then
				cropsUnit:setActiveMode(activeInBaseMode, RoomUnitFurnishing.ActiveMode.BaseMode)
			end
		end
	end
end

function FarmCompPlant:_onClickEarth(furnitureId, unit, preventGuide)
	local plantMO = FarmModel.instance:getPlantByFurUniqueId(unit.uniqueId)
	if plantMO then
		local earthMo = HouseModel.instance.houseMo:getFurnitureModel(plantMO.userId):getFurnitureInfoById(unit.uniqueId)
		local cropsUnit = self._unitDict[plantMO.uniqueId]
		self:_getCompCrops():_setCropsPosition(earthMo, unit, cropsUnit, true)
		self:_onClickPlantOrAnimal(furnitureId, unit)
	else
		SceneController.instance:stopWalk(true)
		local pos = Vector2.New(unit:getPos())
		RoomController.instance:focusCamera(pos)
		ViewMgr.instance:open("CropsListView")
		if not preventGuide then
			IslandPlantGuide.show(IslandPlantGuide.Plant, handlerWithParams(self._onClickEarth, self, {furnitureId, unit, true}))
		end
	end
	GlobalDispatcher:dispatch(GlobalNotify.OnClickEarth)
end

function FarmCompPlant:_onClickPlantOrAnimal(furnitureId, unit, preventGuide)
	local plantMO = FarmModel.instance:getPlantByFurUniqueId(unit.uniqueId)
	if not plantMO then
		plantMO = FarmModel.instance:getPlantByUniqueId(unit.uniqueId)
	end
	if not plantMO or plantMO.hasHarvest then return end
	GlobalDispatcher:dispatch(GlobalNotify.OnClickPlant)
	
	
	local plantUnit = self._unitDict[plantMO.uniqueId]
	if not plantUnit then return end
	local harvestConfig = HarvestConfig.getDefineBySeedId(plantMO.id)
	self.lastShowPos = plantUnit.icon:getPosition(RoomUnitIconComp.Align.Hor.Center, RoomUnitIconComp.Align.Ver.Top)
	self.targetUnit = plantUnit
	RoomController.instance:focusCamera(self.lastShowPos)
	if not plantMO:isMature() then
		if harvestConfig.clickSound > 0 then
			SoundManager.instance:playEffect(harvestConfig.clickSound)
		end
		self:_setPlayerAnimation(GameUtils.getPos(plantUnit.go), plantMO.cfg.touchAnimName, tonumber(GameUtils.getCommonConfig("PlayerEndTouchTime")))
		plantMO:setCanMove(false)
		plantUnit:playAnimOnec(HarvestConfig.getCommonConfig("touch"), function() plantMO:setCanMove(true) end)
		PlantProgressView.instance:show(plantMO, self.lastShowPos)
		IslandPropListView.tryShow(plantMO)
	else
		local cancelAction = SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.EditFurniture, nil, nil, true)
		if not cancelAction then return false end
		if plantMO:isAnimal() then
			SoundManager.instance:playEffect(130100)
		else
			if harvestConfig.clickSound > 0 then
				SoundManager.instance:playEffect(harvestConfig.clickSound)
			end
		end
		self.lastHarvestIcon = plantMO.cfg.harvestIcon
		HarvestToolView.instance:show(self.lastHarvestIcon, self.lastShowPos, self.targetUnit)
		if not preventGuide then
			IslandPlantGuide.show(IslandPlantGuide.Harvest, handlerWithParams(self._onClickPlantOrAnimal, self, {furnitureId, unit, true}))
		end
	end
end

function FarmCompPlant:_onTick()
	local plantList = FarmModel.instance:getAllPlant()
	local isMyHouse = HouseModel.instance:isMyHouse()
	local url
	for _, plantMO in ipairs(plantList) do
		local unit = self._unitDict[plantMO.uniqueId]
		if unit then
			if plantMO:isPhaseChanged() and not plantMO.isMoving then
				unit:resetState()
			end
			if plantMO:isHuge() then
				url = HarvestConfig.Effect_ChengShou_ChiXu_Big
			else
				url = HarvestConfig.Effect_ChengShou_ChiXu
			end
			if plantMO:isMature() and isMyHouse and not plantMO.hasHarvest then
				local config = HarvestConfig.getEffectOffset(plantMO.cfg.subType)
				self:_createEffect(url, unit, unit.uniqueId, config[1], config[2])
			else
				self:_removeEffect(url, unit.uniqueId)
			end
			if not plantMO.hasHarvest and not plantMO.isMoving then
				self:_playShake(plantMO, unit)
			end
			if unit.prop then
				unit.prop:tryAddProp()
			end
		end
	end
end

function FarmCompPlant:_playShake(plantMO, unit)
	local shakeConfigs = HarvestConfig.getShakeConfig(plantMO.cfg.subType, plantMO:isMature())
	if plantMO.countToShake == nil or plantMO.countToShake == 0 then
		plantMO.countToShake = shakeConfigs[1]
	elseif plantMO.countToShake > 0 then
		plantMO.countToShake = plantMO.countToShake - 1
		if unit.playAnimOnec and plantMO.countToShake == 0 and math.random() < shakeConfigs[2] then
			unit:playAnimOnec(HarvestConfig.getCommonConfig("idle2"))
			plantMO.countToShake = shakeConfigs[1]
		end
	end
end

function FarmCompPlant:_handleUnitLoaded(unit, factory)
	if self._factory ~= factory then return end
	self:_tryAddPlantUnit(unit)
end

function FarmCompPlant:_tryAddPlantUnit(unit)
	if not unit then return end
	local plantMO = FarmModel.instance:getPlantByUniqueId(unit.uniqueId) or FarmModel.instance:getPlantByFurUniqueId(unit.uniqueId)
	if plantMO and not HarvestConfig.isEarth(unit.itemId) then
		self._unitDict[plantMO.uniqueId] = unit
		if not unit.resetState then
			print("wrong ======", unit.serverId, unit.itemId, unit.userId, unit.uniqueId, unit.id)
			FarmModel.instance:removePlant(plantMO)
			return
		end
		unit:resetState()
		if unit.prop then
			unit.prop:tryAddProp()
		end
		FarmController.instance:localNotify(FarmNotifyName.OnAddPlantUnit, unit)
	end
end

function FarmCompPlant:_onBeginDragHarvestTool()
	self._tempBonus = {}
	-- SceneController.instance:stopWalk()
end

function FarmCompPlant:_onDragHarvestTool(pos)
	local plantList = RoomHitTest.getHitPlant(pos, self._camera)
	for _, target in pairs(plantList) do
		if not self:_addTempHarvest(target) then
			return
		end
	end
end

function FarmCompPlant:_addTempHarvest(unit)
	local plantMO = FarmModel.instance:getPlantByUniqueId(unit.uniqueId) or
		FarmModel.instance:getPlantByFurUniqueId(unit.uniqueId)--土地
	if not plantMO or not plantMO:isMature() or plantMO.hasHarvest then return true end
	if table.indexof(self._tempHarvest, plantMO.serverId) then return true end
	if self:_checkLimit(plantMO.id) or self:_checkStamina(plantMO.id) or self:_checkBonus(plantMO) then
		return false
	end
	if plantMO:isTree() and(ServerTime.now() - self._lastHarvestSoundTime <= 1) then
		SoundManager.instance:playEffect(130099)
	else
		local harvestConfig = HarvestConfig.getDefineBySeedId(plantMO.id)
		if harvestConfig.harvestSound > 0 then
			SoundManager.instance:playEffect(harvestConfig.harvestSound)
		end
	end
	self._lastHarvestSoundTime = ServerTime.now()
	table.insert(self._tempHarvest, plantMO.serverId)
	self._tempBonus[plantMO.serverId] = plantMO.bonus
	if self._tempPlant[plantMO.id] ~= nil then
		self._tempPlant[plantMO.id] = self._tempPlant[plantMO.id] + 1
	else
		self._tempPlant[plantMO.id] = 1
	end
	-- local sound = {261010110, 261010120}
	-- if plantMO:isHuge() then
	-- 	sound = {261010210, 261010220}
	-- end
	-- SoundManager.instance:playUserVoice(sound)
	local plantUnit = self._unitDict[plantMO.uniqueId]

	local offsetConfig = string.splitToNumber(GameUtils.getCommonConfig("HarvestPlayerOffset"), ",")
	local pos = GameUtils.getPos(plantUnit.go)
	local params = {
		targetPos = Vector2.New(pos.x + offsetConfig[1], pos.y + offsetConfig[2]),
		lastTime = tonumber(GameUtils.getCommonConfig("PlayerEndHarvestTime")),
		jumpBack = true,
		poseId = plantMO.cfg.poseId
	}
	SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Harvest, params)

	local url = HarvestConfig.Effect_ChengShou_DianJi
	self:_createEffect(url, plantUnit, plantUnit.uniqueId)
	SceneTimer:setTimer(1, handlerWithParams(self._removeEffect, self, {url, plantUnit.uniqueId}), nil, false)
	
	self:_playHarvest(plantMO)
	return true
end

function FarmCompPlant:_checkLimit(plantId)
	local define = HarvestConfig.getDefineBySeedId(plantId)
	if define:isCrop() then return false end
	local todayCount = LimitService.instance:getGainCount(plantId)
	local tempCount = 1
	if self._tempPlant[plantId] then
		tempCount = self._tempPlant[plantId] + 1
	end
	local limitCount = BuffService.instance:calculateResultValue(BuffType.AllType.ProduceLimitIncrease, define.limitTimes, define.subType, define.id)
	local overMax = todayCount ~= 0 and (todayCount + tempCount > limitCount)
	if overMax then
		FlyTextManager.instance:showFlyText(lang("今天的次数已经用完啦，明天再来吧"))
		FarmController.instance:localNotify(FarmNotifyName.SendEndDragHarvest)
	end
	return overMax
end

function FarmCompPlant:_checkStamina(plantId)
	local define = HarvestConfig.getDefineBySeedId(plantId)
	local tempAdd = define.tiredValue
	for id, count in pairs(self._tempPlant) do
		define = HarvestConfig.getDefineBySeedId(id)
		tempAdd = tempAdd + define.tiredValue * count
	end
	local canAdd = UserInfo.canAddStamina(tempAdd)
	if not canAdd then
		local needBuy = UserInfo.getStamina() + tempAdd - UserInfo.getMaxStamina()
		RechargeFacade.showCurrencyNotEnoughDialog(7, needBuy)
		FarmController.instance:localNotify(FarmNotifyName.SendEndDragHarvest)
	end
	return not canAdd
end

function FarmCompPlant:_checkBonus(plantMO)
	local bonus = {}
	table.insertto(bonus, plantMO.bonus or {})
	for serverId, b in pairs(self._tempBonus) do
		table.insertto(bonus, b or {})
	end
	local canAdd = ItemService.instance:canAdd(bonus)
	if not canAdd then
		DialogHelper.showFullDlg()
		FarmController.instance:localNotify(FarmNotifyName.SendEndDragHarvest)
	end
	return not canAdd
end

function FarmCompPlant:_setPlayerAnimation(pos, animName, lastTime, tempClothesId)
	local offsetConfig = string.splitToNumber(GameUtils.getCommonConfig("HarvestPlayerOffset"), ",")
	SceneController.instance:playTouchInIsland(Vector2.New(pos.x + offsetConfig[1], pos.y + offsetConfig[2]), animName, lastTime, true, false, nil, tempClothesId)
end

function FarmCompPlant:_doHarvest()
	if RoomTaskHelper.instance:needFadePlantCrop() then
		local count = RoomTaskHelper.instance.harvestCropsCount
		if not count then count = 0 end
		count = count + #self._tempHarvest
		if count == 4 then
			GlobalDispatcher:dispatch(GlobalNotify.OnHarvestCrops)
		else
			HarvestToolView.instance:show(self.lastHarvestIcon, self.lastShowPos, self.targetUnit)
		end
		RoomTaskHelper.instance.harvestCropsCount = count
		self:_onSendHarvest({})
		self._tempHarvest = {}
		self._tempPlant = {}
		return
	end
	if #self._tempHarvest == 0 then
		return
	end
	IslandAgent.instance:sendManorGatherCropRequest(self._tempHarvest, false, handler(self._onSendHarvest, self))
	self._tempHarvest = {}
	self._tempPlant = {}
end

function FarmCompPlant:_onSendHarvest()
end

function FarmCompPlant:_playHarvest(plantMO)
	plantMO.hasHarvest = true
	local plantUnit = self._unitDict[plantMO.uniqueId]
	local animName = HarvestConfig.getCommonConfig("touch")
	plantUnit:playAnimOnec(animName, handlerWithParams(self._onPlayHarvest, self, {plantUnit, plantMO}), true)
	FarmController.instance:localNotify(FarmNotifyName.OnBeginPlayHarvest, plantMO, plantUnit)
	
	if plantMO:isCrop() then
		plantUnit:addEventCallBack(animName, "yinxiao", function()
			SoundManager.instance:playEffect(130034)
		end, nil, true)
	end
	SceneTimer:setTimer(tonumber(GameUtils.getCommonConfig("HarvestGainItemDelay")), handlerWithParams(self._playGainItems, self, {plantUnit, plantMO}), nil, false)
	local url = HarvestConfig.Effect_ChengShou_ChiXu
	if not plantMO:harvestOnlyOneTime() then
		plantMO:resetPlantTime()
	end
	self:_removeEffect(url, plantUnit.uniqueId)
end

function FarmCompPlant:_onPlayHarvest(unit, plantMO)
	if self:_getFurComp() == nil then return end
	if plantMO:harvestOnlyOneTime() then
		FarmModel.instance:removePlant(plantMO)
		if plantMO:isCrop() then
			self._factory:removeUnit(unit.unitType, unit.id)
		elseif plantMO:isMagicPlant() then
			self:_getFurComp():returnUnitToFactory(unit.uniqueId)
		end
		self._unitDict[plantMO.uniqueId] = nil
		FarmController.instance:localNotify(FarmNotifyName.OnRemovePlantUnit, unit)
	end
	plantMO.hasHarvest = false
	FarmController.instance:localNotify(FarmNotifyName.OnHarvest, plantMO, unit)
end

function FarmCompPlant:_playGainItems(unit, plantMO)
	if not plantMO:isCrop() then
		SoundManager.instance:playEffect(130109)
	end
	if self._tempBonus[plantMO.serverId] then
		local pos = Vector2.New(unit:getPos())
		FlyItem.instance:addFlyItemsByWorldPos(self._tempBonus[plantMO.serverId], pos)
		self._tempBonus[plantMO.serverId] = nil
	end
end

function FarmCompPlant:_createEffect(url, unit, uniqueId, offsetX, offsetY)
	local dict = arrayutil.getOrCreateList(self._effectList, url)
	if dict[uniqueId] then return end
	local effect
	local pool = arrayutil.getOrCreateList(self._effectPool, url)
	if #pool > 0 then
		effect = table.remove(pool)
		goutil.setActive(effect, true)
	else
		effect = goutil.clone(self._scene.stage:getMainAsset(url))
	end
	local parentPos = Vector2.New(unit:getPos())
	goutil.addChildToParent(effect, unit.go)
	GameUtils.setPos(effect, parentPos.x + (offsetX or 0), parentPos.y + (offsetY or 0), 0)
	tfutil.SetLZ(effect, RoomConfig.FurnitureZInterval * - 0.01)
	dict[uniqueId] = effect
end

function FarmCompPlant:_removeEffect(url, uniqueId)
	if not self._effectList then return end
	local dict = arrayutil.getOrCreateList(self._effectList, url)
	local effect = dict[uniqueId]
	if not effect then return end
	goutil.setActive(effect, false)
	local pool = arrayutil.getOrCreateList(self._effectPool, url)
	table.insert(pool, effect)
	dict[uniqueId] = nil
end

function FarmCompPlant:_onRecycleFurniture(uniqueId, factory)
	if self._factory ~= factory then return end
	local plantMO = FarmModel.instance:getPlantByFurUniqueId(uniqueId)
	if plantMO and self._unitDict[plantMO.uniqueId] then
		if plantMO:isCrop() then
			local unit = self._unitDict[plantMO.uniqueId]
			self:_getCompCrops():_removeCropsSort(plantMO, unit)
			self._factory:removeUnit(unit.unitType, unit.id)
		end
		self:_removeEffect(HarvestConfig.Effect_ChengShou_ChiXu, plantMO.uniqueId)
		FarmController.instance:localNotify(FarmNotifyName.OnRemovePlantUnit, self._unitDict[plantMO.uniqueId])
		self._unitDict[plantMO.uniqueId] = nil
		-- table.insert(self._recyclePlantList, plantMO)
	end
end

function FarmCompPlant:_onResetEditFur()
	-- self._recyclePlantList = {}
end

function FarmCompPlant:_setPlantUnit()
	if self:_getFurComp() == nil then return end
	local allPlant = FarmModel.instance:getAllPlant()
	local furnitureMO
	local unit
	for _, plantMO in pairs(allPlant) do
		furnitureMO = self._factory:getFurnitureModel(plantMO.userId):getFurnitureByServerId(plantMO.serverId)
		if furnitureMO then
			unit = self:_getFurComp():getFurUnitByUniqueId(furnitureMO.uniqueId)
			self:_tryAddPlantUnit(unit)
		end
	end
	-- self._recyclePlantList = {}
	FarmController.instance:localNotify(FarmNotifyName.OnSetPlantUnit)
end

function FarmCompPlant:_getFurComp()
	if self._factory:getRoomSuite() == nil then return end
	if self._compFur == nil then
		self._compFur = self._factory:getRoomSuite():getComponent(RoomCompFurniture)
	end
	return self._compFur
end

function FarmCompPlant:_onSpeedUpPlant(plantMO)
	local unit = self._unitDict[plantMO.uniqueId]
	unit:setDefaultStateKey("idle1")
	unit:resetState()
	if RoomTaskHelper.instance:needFadePlantCrop() then
		for _, plant in pairs(FarmModel.instance:getAllPlant()) do
			if plant:isCrop() then
				plant:resetPlantTime()
			end
		end
		GlobalDispatcher:dispatch(GlobalNotify.OnSpeedUpCrops)
	end
end

function FarmCompPlant:getPlantUnit(uniqueId)
	return self._unitDict[uniqueId]
end

function FarmCompPlant:_getCompCrops()
	if not self._compCrops then
		self._compCrops = self.entity:getComponent(FarmCompCrops)
	end
	return self._compCrops
end

return FarmCompPlant 