-- {excel:B表情配置.xlsx, sheetName:export_表情配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_pose", package.seeall)

local title = {id=1,name=2,type=3,itemId=4,aniName=5,hasEffect=6,clothes=7,faceType=8,hideHandItem=9,sound=10,voice=11,evtSound=12,isRepeat=13,specialHandler=14}

local dataList = {
	{1, "飞吻", 1, 20000011, "Feiwen", true, 0, 0, 0, 130073, {262010410,262010420}, 0, false, 0},
	{2, "再见", 1, 0, "bye", true, 0, 0, 0, 130070, {262010210,262010220}, 0, false, 0},
	{3, "你好", 1, 0, "hello", true, 0, 0, 0, 130072, {262010110,262010120}, 0, false, 0},
	{5, "吓你一跳", 1, 20000001, "xian<PERSON><PERSON><PERSON>o", true, 0, 0, 0, 130071, {262010610,262010620}, 0, false, 0},
	{6, "谢谢", 1, 0, "xiexie", true, 0, 0, 0, 130075, {262010310,262010320}, 0, false, 0},
	{7, "你真好看", 1, 0, "you-beautiful", true, 0, 0, 0, 130069, {262010510,262010520}, 0, false, 0},
	{8, "扔雪球", 1, 0, "rengxueqiu", true, 0, 0, 0, 130074, nil, 0, false, 0},
	{10, "吹笛子(初级)", 2, 0, "cw_buzhuo2", true, 22, 1, 0, 0, nil, 0, false, 0},
	{11, "加好友", 2, 0, "hy_jiahaoyou", false, 21, 0, 0, 0, nil, 0, false, 0},
	{12, "炸弹传递", 2, 0, "hd_party_zhatou", true, 0, 1, 0, 0, nil, 0, false, 0},
	{14, "落地晕", 2, 0, "jingji_3", true, 0, 1, 0, 0, nil, 0, false, 0},
	{15, "摇汽水成功", 2, 0, "hd_qishui_1", true, 0, 1, 0, 0, nil, 0, false, 0},
	{16, "摇汽水失败", 2, 0, "hd_qishui_3", false, 0, 1, 0, 0, nil, 0, false, 0},
	{17, "拍照", 2, 0, "paizhao", false, 6, 0, 0, 0, nil, 0, false, 0},
	{18, "欢呼", 1, 20000002, "bq_huanhu", false, 0, 0, 0, 141099, {262010810,262010820}, 0, false, 0},
	{19, "大笑", 1, 20000003, "bq_daxiao", false, 0, 0, 0, 141100, {262010710,262010720}, 0, false, 0},
	{20, "点头", 1, 0, "bq_diantou", true, 0, 0, 0, 141101, {262011010,262011020}, 0, false, 0},
	{21, "大哭", 1, 20000004, "bq_daku", false, 0, 0, 0, 141102, {262011110,262011120}, 0, false, 0},
	{22, "小拳拳", 1, 20000005, "bq_xiaoquan", true, 0, 0, 0, 141103, {262011210,262011220}, 0, false, 0},
	{23, "求求", 1, 20000006, "bq_qiuqiu", false, 0, 0, 0, 141104, {262011310,262011320}, 0, false, 0},
	{24, "开心跳起", 1, 20000007, "bq_kaixin", false, 0, 0, 0, 141105, {262010910,262010920}, 0, false, 0},
	{25, "生气跺脚", 1, 20000008, "bq_duojiao", false, 0, 0, 0, 141106, {262011410,262011420}, 0, false, 0},
	{26, "敬礼", 1, 20000009, "bq_jingli", false, 0, 0, 0, 141107, nil, 0, false, 0},
	{27, "疑问", 1, 20000010, "bq_yiwen", true, 0, 0, 0, 141108, nil, 0, false, 0},
	{28, "做棉花糖", 2, 0, "mianhuatang", false, 0, 1, 0, 0, nil, 0, false, 0},
	{29, "看书", 2, 0, "idle_mangshi", false, 20, 0, 0, 0, nil, 0, false, 0},
	{30, "放礼炮", 2, 0, "hd_party_lipao", false, 18, 1, 0, 130079, nil, 0, false, 0},
	{31, "猜拳-1", 2, 0, "yxhz_bjc_chuzhao", true, 0, 1, 0, 0, nil, 0, false, 1},
	{32, "猜拳-2", 2, 0, "yxhz_bjc_chuzhao", true, 0, 1, 0, 0, nil, 0, false, 1},
	{33, "猜拳-3", 2, 0, "yxhz_bjc_chuzhao", true, 0, 1, 0, 0, nil, 0, false, 1},
	{34, "摇火把", 2, 0, "bf_jiuzhu", true, 0, 1, 0, 0, nil, 0, false, 0},
	{35, "砸冰雕", 2, 0, "wk_2", false, 17, 0, 0, 0, nil, 130088, false, 0},
	{36, "加亲友", 2, 0, "hy_jiahaoyou", false, 24, 0, 0, 0, nil, 0, false, 0},
	{37, "戳泡泡", 2, 0, "pp_jiuzhu", false, 0, 1, 0, 0, nil, 0, false, 0},
	{38, "挖捕兽坑", 2, 0, "bsk_jiuzhu", false, 0, 2, 0, 0, nil, 0, false, 0},
	{39, "玩泡泡机", 2, 0, "shoucheng", false, 0, 1, 0, 0, nil, 0, false, 0},
	{40, "扒广告贴", 2, 0, "ggt_jiuzhu", false, 0, 1, 0, 0, nil, 0, false, 0},
	{41, "吟唱", 1, 0, "cjtz_yongchang", true, 0, 1, 0, 0, nil, 0, true, 0},
	{42, "飞升", 2, 0, "cjtz_shengkong", true, 0, 1, 0, 0, nil, 0, false, 2},
	{43, "坐", 1, 0, "sit_yuandi", false, 0, 0, 0, 0, nil, 0, true, 0},
	{44, "吹笛子(中级)", 2, 0, "cw_buzhuo2", true, 22, 1, 0, 0, nil, 0, false, 0},
	{45, "吹笛子(高级)", 2, 0, "cw_buzhuo2", true, 22, 1, 0, 0, nil, 0, false, 0},
	{46, "玩家跳舞（被哈皮控制）", 2, 0, "2.2_dahapi_beigongji", true, 0, 1, 0, 0, nil, 0, true, 0},
	{47, "凿木雕", 2, 0, "cj_mudiao", true, 49, 1, 0, 0, nil, 130088, true, 0},
	{48, "玩家被花魇控制", 2, 0, "hm_longjuanfeng", false, 0, 1, 0, 0, nil, 0, true, 0},
	{49, "嫌弃", 1, 20000012, "bq_3_xianqi", true, 0, 0, 0, 141326, {262012010,262012020}, 0, false, 0},
	{50, "嘿嘿", 1, 20000013, "bq_3_xiexiao", true, 0, 0, 0, 141328, {262012110,262012120}, 0, false, 0},
	{51, "嘚瑟", 1, 20000014, "bq_3_dese", false, 0, 0, 0, 141322, {262011710,262011720}, 0, false, 0},
	{52, "加油", 1, 20000015, "bq_3_jiayou", false, 67, 0, 0, 141325, {262011810,262011820}, 0, false, 0},
	{53, "眨眼", 1, 20000016, "bq_3_zhayan", false, 0, 0, 0, 141323, nil, 0, false, 0},
	{54, "吃瓜", 1, 20000017, "bq_3_chigua", true, 69, 0, 0, 141329, nil, 0, false, 0},
	{55, "滑冰", 1, 20000018, "bq_4_liubing", true, 68, 0, 0, 141331, {262011910,262011920}, 0, false, 0},
	{56, "闪亮登场", 1, 20000019, "bq_4_denchang", true, 72, 0, 0, 141324, nil, 0, false, 0},
	{57, "魔术", 1, 20000020, "bq_4_moshu", true, 70, 0, 0, 141330, nil, 0, false, 0},
	{58, "许愿", 1, 20000021, "bq_4_zhanbu", true, 0, 0, 0, 141327, nil, 0, false, 0},
	{59, "玩家被花魇吸盾", 2, 0, "hm_shifa", false, 0, 1, 0, 0, nil, 0, false, 0},
	{60, "狼人杀_被宰", 999, 0, "lrs_dead", true, 77, 1, 0, 0, nil, 0, true, 0},
	{61, "狼人杀_开宰", 999, 0, "lrs_h_shifa", true, 0, 1, 0, 0, nil, 141383, false, 0},
	{62, "狼人杀_普通任务", 999, 0, "idle", false, 0, 1, -1, 0, nil, 0, false, 0},
	{63, "狼人杀_建筑交互", 999, 0, "shoucheng-guoshu", false, 0, 1, 0, 0, nil, 0, false, 0},
	{64, "狼人杀_精灵交互", 999, 0, "lrs_shifa", false, 130, 1, 0, 0, nil, 0, false, 0},
	{65, "挖稻草屋", 2, 0, "tb_fanlaji", false, 17, 0, 0, 0, nil, 130088, false, 0},
	{66, "派对赠礼-叮叮", 2, 0, "pd_andingding", true, 0, 1, 0, 141437, nil, 0, false, 0},
	{67, "派对赠礼-星星", 2, 0, "pd_rengxingxing", true, 0, 1, 0, 141438, nil, 0, false, 0},
	{68, "派对赠礼-气球", 2, 0, "pd_fangqiqiu", true, 0, 1, 0, 141434, nil, 0, false, 0},
	{69, "派对赠礼-彩虹", 2, 0, "pd_huacaihong", true, 0, 1, 0, 141436, nil, 0, false, 0},
	{70, "派对赠礼-狂热之花", 2, 0, "pd_huabanfukong", true, 0, 1, 0, 141435, nil, 0, false, 0},
	{71, "鼓掌", 1, 0, "bq_guzhang", false, 0, 1, 0, 141440, nil, 0, true, 0},
	{72, "AR", 2, 0, "idle_mangshi", false, 20, 0, 0, 0, nil, 0, false, 0},
	{73, "恭喜发财", 1, 20000035, "bq_facai", true, 114, 0, 0, 141610, {262013610,262013620}, 0, true, 0},
	{74, "谢谢老板", 1, 20000036, "bq_hongbao", true, 115, 0, 0, 141611, {262013710,262013720}, 0, true, 0},
	{75, "恭贺新禧", 1, 0, "bq_bainian", true, 0, 0, 0, 141609, {262013510,262013520}, 0, true, 0},
	{76, "扔骰子", 2, 0, "yxhz_rengtouzi", true, 0, 1, 0, 141779, nil, 0, false, 3},
	{77, "海底场景看书", 2, 0, "3.0_sea_idle_mangshi", false, 20, 0, 0, 0, nil, 0, false, 0},
	{100, "家族boss_玩家下坠", 2, 0, "lzdk_diaoluo_2", false, 0, 1, 0, 0, {263020410,263020420}, 0, false, 0},
	{101, "家族场景跳副本", 2, 0, "lzdk_tiaoru", false, 0, 1, 0, 141478, nil, 0, false, 0},
	{102, "家族boss_倒下", 2, 0, "lzdk_die", false, 0, 1, 0, 0, {263020510,263020520}, 0, false, 0},
	{103, "家族boss_修复道具", 2, 0, "gc_jiaohua", false, 82, 1, 0, 0, nil, 0, false, 0},
	{104, "家族boss_采集", 2, 0, "dg_chucao", false, 0, 1, 0, 141304, nil, 0, true, 0},
	{105, "跳一跳准备动作", 2, 0, "tyt_zhunbei", false, 0, 0, 0, 0, nil, 0, false, 0},
	{106, "跳一跳起跳动作", 2, 0, "tyt_qitiao", false, 0, 0, 0, 0, nil, 0, false, 0},
	{107, "跳一跳飞行动作", 2, 0, "tyt_feixing", false, 0, 0, 0, 0, nil, 0, true, 0},
	{108, "跳一跳落地动作", 2, 0, "tyt_luodi", false, 0, 0, 0, 0, nil, 0, false, 0},
	{109, "跳一跳熊被吃/咬动作", 2, 0, "tyt_beiyao", false, 0, 0, 0, 0, nil, 0, false, 0},
	{110, "跳一跳准备动作2", 2, 0, "tyt_zhunbei2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{111, "叉腰", 1, 20000022, "bq_5_chayao", true, 0, 0, 0, 141551, {262012210,262012220}, 0, false, 0},
	{112, "催眠", 1, 20000023, "bq_5_cuimian", true, 0, 0, 0, 141552, {262012310,262012320}, 0, true, 0},
	{113, "躺躺", 1, 20000024, "bq_5_geyoutang", true, 0, 0, 0, 141561, {262013210,262013220}, 0, false, 0},
	{114, "举手", 1, 20000025, "bq_5_jushou", true, 0, 0, 0, 141555, {262012610,262012620}, 0, false, 0},
	{115, "惊呆", 1, 20000026, "bq_5_jingdai", true, 0, 0, 0, 141554, {262012510,262012520}, 0, false, 0},
	{116, "扭扭", 1, 20000027, "bq_5_niuniu", false, 0, 0, 0, 141558, {262012910,262012920}, 0, true, 0},
	{117, "害羞", 1, 20000028, "bq_5_haixiu", true, 0, 0, 0, 141553, {262012410,262012420}, 0, false, 0},
	{118, "手舞足蹈", 1, 20000029, "bq_5_shouwuzudao", true, 0, 0, 0, 141559, {262013010,262013020}, 0, true, 0},
	{119, "摊手", 1, 20000030, "bq_5_tanshou", true, 0, 0, 0, 141560, {262013110,262013120}, 0, false, 0},
	{120, "晚安", 1, 20000031, "bq_5_wanan", true, 100, 0, 0, 141562, {262013310,262013320}, 0, false, 0},
	{121, "自闭", 1, 20000032, "bq_5_zibi", true, 99, 0, 0, 141563, {262013410,262013420}, 0, true, 0},
	{122, "拒绝", 1, 20000033, "bq_5_jujue", true, 0, 0, 0, 141556, {262012710,262012720}, 0, false, 0},
	{123, "码字", 1, 20000034, "bq_5_mazi", true, 98, 0, 0, 141557, {262012810,262012820}, 0, true, 0},
	{124, "年兽-眩晕", 2, 0, "dns_xuanyun", true, 0, 1, 0, 0, nil, 0, true, 0},
	{125, "灵动身姿", 1, 20000037, "xyk_1.10_yuanqi_01", false, 0, 1, 0, 141648, nil, 0, true, 0},
	{126, "万花筒舞步", 1, 20000038, "xyk_1.10_yuanqi_02", false, 0, 1, 0, 141649, nil, 0, true, 0},
	{127, "完美时光", 1, 20000039, "xyk_1.10_yuanqi_03", false, 0, 1, 0, 141650, nil, 0, true, 0},
	{128, "神秘缠绕", 1, 20000040, "xyk_1.10_yuanqi_04", false, 0, 1, 0, 141651, nil, 0, true, 0},
	{129, "飘逸身姿", 1, 20000041, "xyk_1.10_yuanqi_05", false, 0, 1, 0, 141652, nil, 0, true, 0},
	{130, "狂热跳跃", 1, 20000042, "xyk_1.10_yuanqi_06", false, 0, 1, 0, 141653, nil, 0, true, 0},
	{131, "踢踏舞步", 1, 20000043, "xyk_1.10_shuaiqi_01", false, 0, 1, 0, 141654, nil, 0, true, 0},
	{132, "飞翔舞步", 1, 20000044, "xyk_1.10_shuaiqi_02", false, 0, 1, 0, 141655, nil, 0, true, 0},
	{133, "落英舞步", 1, 20000045, "xyk_1.10_shuaiqi_03", false, 0, 1, 0, 141656, nil, 0, true, 0},
	{134, "街区舞步", 1, 20000046, "xyk_1.10_shuaiqi_04", false, 0, 1, 0, 141657, nil, 0, true, 0},
	{135, "激情狂舞", 1, 20000047, "xyk_1.10_shuaiqi_05", false, 0, 1, 0, 141658, nil, 0, true, 0},
	{136, "刺激浪漫", 1, 20000048, "xyk_1.10_shuaiqi_06", false, 0, 1, 0, 141659, nil, 0, true, 0},
	{137, "轻盈旋律", 1, 20000049, "xyk_1.10_yuanqi_07", false, 0, 1, 0, 0, nil, 0, true, 0},
	{138, "荧光挥舞", 1, 20000050, "pd_1.10_dacall", false, 122, 0, 0, 141660, nil, 0, true, 0},
	{139, "奥比木偶戏", 1, 20000051, "bq_1.11_qianxianmuou", true, 127, 0, 0, 141667, nil, 0, false, 0},
	{140, "熊娜丽莎的微笑", 1, 20000052, "bq_6_mengnalisha", true, 0, 1, 0, 141698, nil, 0, true, 0},
	{141, "呐喊", 1, 20000053, "bq_6_nahan", true, 0, 1, 0, 141699, nil, 0, true, 0},
	{142, "思考者", 1, 20000054, "bq_6_sikaozhe", true, 0, 1, 0, 141700, nil, 0, false, 0},
	{143, "观察", 1, 20000055, "bq_6_guancha", false, 133, 0, 0, 141701, nil, 0, false, 0},
	{144, "奥丁飞熊", 1, 20000056, "2.1_bq_niaoxiongaoding", true, 139, 1, 0, 141831, nil, 0, true, 0},
	{145, "月灵飘飘", 1, 20000057, "2.3_bq_bangui", true, 0, 1, 0, 141852, nil, 0, false, 0},
	{146, "撒糖", 1, 20000058, "2.3_bq_satang", true, 144, 1, 0, 141853, nil, 0, false, 0},
	{147, "拖鞋（你画我猜）", 2, 0, "2.5_nhwc_liwu_tuoxie", false, 0, 1, 0, 142054, nil, 0, false, 0},
	{148, "送花（你画我猜）", 2, 0, "2.5_nhwc_liwu_hua", false, 0, 1, 0, 130115, nil, 0, false, 0},
	{149, "奖杯（你画我猜）", 2, 0, "2.5_nhwc_liwu_jiangbei", false, 151, 1, 0, 130115, nil, 0, false, 0},
	{150, "许愿", 2, 0, "2.5.2_knwh_xuyuan", false, 0, 1, 0, 0, nil, 0, false, 0},
	{151, "本熊知道了", 1, 20000059, "2.7_bq_benxiongzhidaole", true, 167, 3, 0, 141988, nil, 0, false, 0},
	{152, "撒红包", 1, 20000060, "2.7_bq_fahongbao", true, 166, 1, 0, 141989, nil, 0, true, 0},
	{153, "得意（抽乌龟）", 2, 0, "2.7_ypnf_deyi", false, 155, 1, 0, 141940, nil, 0, false, 0},
	{154, "汗颜（抽乌龟）", 2, 0, "2.7_ypnf_hanyan", false, 155, 1, 0, 141941, nil, 0, false, 0},
	{155, "慌张（抽乌龟）", 2, 0, "2.7_ypnf_huangzhang", false, 155, 1, 0, 0, nil, 0, false, 0},
	{156, "惊恐（抽乌龟）", 2, 0, "2.7_ypnf_jingkong", false, 155, 1, 0, 141942, nil, 0, false, 0},
	{157, "开心（抽乌龟）", 2, 0, "2.7_ypnf_kaixin", false, 155, 1, 0, 141943, nil, 0, false, 0},
	{158, "拿牌1（抽乌龟）", 2, 0, "2.7_ypnf_napai1", false, 155, 1, 0, 0, nil, 0, false, 0},
	{159, "拿牌2（抽乌龟）", 2, 0, "2.7_ypnf_napai2", false, 155, 1, 0, 0, nil, 0, false, 0},
	{160, "思索（抽乌龟）", 2, 0, "2.7_ypnf_sisuo", false, 155, 1, 0, 0, nil, 0, false, 0},
	{161, "整理（抽乌龟）", 2, 0, "2.7_ypnf_zhengli", false, 155, 1, 0, 0, nil, 0, false, 0},
	{162, "完胜（抽乌龟）", 2, 0, "2.7_ypnf_wansheng", false, 0, 1, 0, 0, nil, 0, false, 0},
	{163, "优胜（抽乌龟）", 2, 0, "2.7_ypnf_yousheng", false, 0, 1, 0, 0, nil, 0, false, 0},
	{164, "险胜（抽乌龟）", 2, 0, "2.7_ypnf_xiansheng", false, 0, 1, 0, 0, nil, 0, false, 0},
	{165, "失败（抽乌龟）", 2, 0, "2.7_ypnf_shibai", false, 155, 1, 0, 0, nil, 0, false, 0},
	{166, "坐姿（抽乌龟）", 2, 0, "2.7_ypnf_zuozi", false, 0, 1, 0, 0, nil, 0, false, 0},
	{167, "胜利（花云挑战）", 2, 0, "bq_huanhu", false, 0, 0, 0, 0, nil, 0, false, 0},
	{168, "奥比摇", 1, 20000061, "2.8_bq_aobiyao", false, 0, 1, 0, 0, nil, 0, true, 0},
	{169, "叼玫瑰登场", 1, 20000062, "2.8_bq_shanliangdengchang", true, 169, 1, 0, 141994, nil, 0, false, 0},
	{170, "律动踢踏", 1, 20000063, "2.9_wxzy_tiaowu_01", false, 0, 1, 0, 0, nil, 0, true, 0},
	{171, "旋律扭动", 1, 20000064, "2.9_wxzy_tiaowu_02", false, 0, 1, 0, 0, nil, 0, true, 0},
	{172, "舞动全场", 1, 20000065, "2.9_wxzy_tiaowu_03", false, 0, 1, 0, 0, nil, 0, true, 0},
	{173, "飞扬元气", 1, 20000066, "2.9_wxzy_tiaowu_04", false, 0, 1, 0, 0, nil, 0, true, 0},
	{174, "热烈跳跃舞", 1, 20000067, "2.9_wxzy_tiaowu_05", false, 0, 1, 0, 0, nil, 0, true, 0},
	{175, "左右灵动步", 1, 20000068, "2.9_wxzy_tiaowu_06", false, 0, 1, 0, 0, nil, 0, true, 0},
	{176, "原地圆圈舞", 1, 20000069, "2.9_wxzy_tiaowu_07", false, 0, 1, 0, 0, nil, 0, true, 0},
	{177, "蝴蝶太空步", 1, 20000070, "2.9_wxzy_tiaowu_08", false, 0, 1, 0, 0, nil, 0, true, 0},
	{178, "飞舞纱巾", 1, 20000071, "2.9_bq_feiwushajin", true, 0, 1, 0, 142001, nil, 0, true, 0},
	{179, "打碟时刻", 1, 20000072, "2.9_bq_dadieshike", false, 174, 1, 0, 0, nil, 0, true, 0},
	{180, "方块顶顶乐", 1, 20000073, "2.10_bq_dingfangkuai", true, 0, 1, 0, 142013, nil, 0, true, 0},
	{181, "吹气球", 1, 20000074, "2.10_bq_chuiqiqiu", true, 0, 1, 0, 142012, nil, 0, false, 0},
	{182, "惊吓手电", 1, 20000075, "2.11_bq_jingxiashoudian", true, 178, 1, 0, 0, nil, 0, false, 0},
	{183, "智慧之光", 1, 20000076, "2.11_bq_zhihuizhiguang", true, 186, 1, 0, 142014, nil, 0, false, 0},
	{184, "小魔仙变身", 1, 20000077, "3.0_bq_xiaomoxianbianshen", true, 0, 1, 0, 142029, nil, 0, false, 0},
	{185, "海草海草", 1, 20000078, "3.0_bq_haicao", false, 0, 1, 0, 0, nil, 0, true, 0},
	{186, "宝石1偷宝石（奥比剧）", 2, 0, "2.11_abjc_baoshi1", false, 0, 1, 0, 0, nil, 0, false, 0},
	{187, "宝石2放回宝石（奥比剧）", 2, 0, "2.11_abjc_baoshi2", false, 0, 1, 0, 0, nil, 0, false, 0},
	{188, "宝石2扫地（奥比剧）", 2, 0, "2.11_abjc_saodi", false, 0, 1, 0, 0, nil, 0, false, 0},
	{189, "破案-逮捕错误（奥比剧）", 2, 0, "2.11_abjc_daibu1", false, 197, 1, 0, 0, nil, 0, false, 0},
	{190, "破案-逮捕正确（奥比剧）", 2, 0, "2.11_abjc_daibu2", false, 197, 1, 0, 0, nil, 0, false, 0},
	{191, "破案-疑惑（奥比剧）", 2, 0, "2.11_abjc_yihuo", true, 0, 1, 0, 0, nil, 0, false, 0},
	{192, "破案-侦探-脚印（奥比剧）", 2, 0, "2.11_abjc_zhentan1", false, 196, 1, 0, 0, nil, 0, false, 0},
	{193, "破案-侦探-问号（奥比剧）", 2, 0, "2.11_abjc_zhentan2", false, 195, 1, 0, 0, nil, 0, false, 0},
	{194, "破案-侦探-指认（奥比剧）", 2, 0, "2.11_abjc_zhiren", false, 0, 1, 0, 0, nil, 0, false, 0},
	{195, "飞机工坊-偷走齿轮（奥比剧）", 2, 0, "2.11_abjc_touzouchilun", false, 180, 1, 0, 0, nil, 0, false, 0},
	{196, "飞机工坊-修理机器（奥比剧）", 2, 0, "2.11_abjc_xiulijiqi", false, 181, 1, 0, 0, nil, 0, false, 0},
	{197, "飞机起飞-爆炸1（奥比剧）", 2, 0, "2.11_abjc_baozha1", false, 0, 1, 0, 0, nil, 0, false, 0},
	{198, "飞机起飞-爆炸2（奥比剧）", 2, 0, "2.11_abjc_baozha2", false, 0, 1, 0, 0, nil, 0, false, 0},
	{199, "飞机起飞-飞机飞走（奥比剧）", 2, 0, "2.11_abjc_feijiqifei", false, 0, 1, 0, 0, nil, 0, false, 0},
	{200, "基础表情（奥比剧）", 2, 0, "emotion-0", false, 0, 1, 0, 0, nil, 0, false, 0},
	{201, "高兴（奥比剧）", 2, 0, "emotion-1", false, 0, 1, 0, 0, nil, 0, false, 0},
	{202, "严肃（奥比剧）", 2, 0, "emotion-2", false, 0, 1, 0, 0, nil, 0, false, 0},
	{203, "慌张（奥比剧）", 2, 0, "emotion-3", false, 0, 1, 0, 0, nil, 0, false, 0},
	{204, "生气（奥比剧）", 2, 0, "emotion-4", false, 0, 1, 0, 0, nil, 0, false, 0},
	{205, "吃惊（奥比剧）", 2, 0, "emotion-5", false, 0, 1, 0, 0, nil, 0, false, 0},
	{206, "思考（奥比剧）", 2, 0, "emotion-6", false, 0, 1, 0, 0, nil, 0, false, 0},
	{207, "微笑（奥比剧）", 2, 0, "emotion-7", false, 0, 1, 0, 0, nil, 0, false, 0},
	{208, "沮丧（奥比剧）", 2, 0, "emotion-8", false, 0, 1, 0, 0, nil, 0, false, 0},
	{209, "兴奋（奥比剧）", 2, 0, "emotion-9", false, 0, 1, 0, 0, nil, 0, false, 0},
	{210, "尴尬（奥比剧）", 2, 0, "emotion-10", false, 0, 1, 0, 0, nil, 0, false, 0},
	{211, "赞同了解（奥比剧）", 2, 0, "emotion-11", false, 0, 1, 0, 0, nil, 0, false, 0},
	{212, "惊讶（奥比剧）", 2, 0, "emotion-12", false, 0, 1, 0, 0, nil, 0, false, 0},
	{213, "郁闷（奥比剧）", 2, 0, "emotion-13", false, 0, 1, 0, 0, nil, 0, false, 0},
	{214, "调皮（奥比剧）", 2, 0, "emotion-14", false, 0, 1, 0, 0, nil, 0, false, 0},
	{215, "害羞（奥比剧）", 2, 0, "emotion-15", false, 0, 1, 0, 0, nil, 0, false, 0},
	{216, "吓唬（奥比剧）", 2, 0, "emotion-16", false, 0, 1, 0, 0, nil, 0, false, 0},
	{217, "得意（奥比剧）", 2, 0, "emotion-17", false, 0, 1, 0, 0, nil, 0, false, 0},
	{218, "飞机爆炸-爆炸头（奥比剧）", 2, 0, "emotion-0", false, 185, 1, 0, 0, nil, 0, false, 0},
	{219, "坏笑（奥比剧）", 2, 0, "2.11_abjc_xiexiao", false, 0, 1, 0, 0, nil, 0, false, 0},
	{220, "胜利（魔仙版花云挑战）", 2, 0, "3.0_hytz_shengli", true, 0, 1, 0, 0, nil, 0, false, 0},
	{221, "失败（魔仙版花云挑战）", 2, 0, "3.0_hytz_shibai", false, 0, 1, 0, 0, nil, 0, false, 0},
	{222, "奋笔疾书", 1, 20000079, "3.1.2_bq_fenbijishu", true, 213, 1, 0, 142045, nil, 0, true, 0},
	{223, "登上颁奖台", 1, 20000080, "3.1_bq_aoyunbanjiang", true, 0, 1, 0, 0, nil, 0, false, 0},
	{224, "3.3快乐节放礼炮（复用）", 2, 0, "hd_party_lipao", true, 0, 1, 0, 0, nil, 0, false, 0},
	{225, "狼人杀_治疗", 999, 0, "3.4_lrs_zhiliao", false, 221, 1, 0, 0, nil, 0, false, 0},
	{226, "狼人杀_解救", 999, 0, "3.4_lrs_jiejiu", false, 0, 1, 0, 0, nil, 0, false, 0},
	{227, "狼人杀_修复", 999, 0, "3.4_lrs_xiufu", false, 181, 1, 0, 0, nil, 0, false, 0},
	{228, "狼人杀_解救被捕者", 999, 0, "3.4_lrs_boss_jiefang", true, 0, 1, 0, 0, nil, 0, false, 0},
	{229, "狼人杀_普攻", 999, 0, "3.4_lrs_boss_gongji", true, 0, 1, 1, 0, nil, 0, false, 0},
	{230, "狼人杀_捆绑", 999, 0, "3.4_lrs_boss_kunbang", true, 0, 1, 0, 0, nil, 0, false, 0},
	{231, "狼人杀_制作人偶", 999, 0, "3.4_lrs_boss_zhizuorenou", false, 0, 1, 0, 0, nil, 0, false, 0},
	{232, "狼人杀_挣扎", 999, 0, "3.4_lrs_kunbang_zhengtuo", false, 0, 1, 0, 0, nil, 0, false, 0},
	{233, "狼人杀_开门", 999, 0, "3.4_lrs_tuimen", false, 0, 1, 0, 0, nil, 0, false, 0},
	{234, "狼人杀_眩晕", 999, 0, "3.4_lrs_boss_xuanyun", true, 0, 1, 0, 0, nil, 0, false, 0},
	{235, "狼人杀_关门", 999, 0, "3.4_lrs_guanmen", false, 0, 1, 0, 0, nil, 0, false, 0},
	{236, "狼人杀_重伤治疗", 999, 0, "3.4_lrs_zhiliao2", false, 221, 1, 0, 0, nil, 0, false, 0},
	{237, "你敢应吗", 1, 20000082, "3.5_bq_niganyingma", true, 236, 1, 0, 0, nil, 0, true, 0},
	{238, "云里偷闲", 1, 20000081, "3.6_bq_yunlitouxian", true, 0, 1, 0, 0, nil, 0, true, 0},
	{239, "爱心发射", 1, 20000083, "3.7_bq_aixinfashe", false, 0, 1, 0, 0, nil, 0, true, 0},
	{240, "花式摔炮", 1, 20000084, "3.7_bq_shuaipao", true, 0, 1, 0, 142102, nil, 0, true, 0},
	{241, "火把舞打call", 2, 0, "3.7_ghwh_huobawu_5", true, 0, 1, 0, 0, nil, 0, true, 0},
	{242, "腰鼓舞打call", 2, 0, "3.7_ghwh_yaoguwu_5", true, 244, 1, 0, 0, nil, 0, true, 0},
	{243, "捧脸卖萌", 1, 20000085, "3.8_bq_penglianmaimeng", false, 0, 1, 0, 0, nil, 0, false, 0},
	{244, "食咗饭未", 1, 20000087, "hello", true, 0, 0, 0, 0, nil, 0, false, 0},
	{245, "我哭了，我装的", 1, 20000088, "3.10_bq_wozhuangde", true, 0, 1, 0, 142159, nil, 0, true, 0},
	{246, "钻石之心", 1, 20000086, "3.10_bq_zuanshizhixin", true, 0, 1, 0, 142158, nil, 0, false, 0},
	{247, "滑个步", 1, 20000089, "3.11_bq_taikongbu", false, 0, 1, 0, 0, nil, 0, true, 0},
	{248, "放纸鸢", 1, 20000090, "3.z12_bq_fangfengzheng", true, 0, 1, 0, 0, nil, 0, true, 0},
	{249, "气球趴趴乐", 1, 20000091, "4.0_znq_xunyou_qiqiu4", true, 0, 1, 0, 0, nil, 0, true, 0},
	{260, "梦幻国度扔炸弹", 2, 0, "mhgd_5_zhayaotong", false, 0, 0, 0, 0, nil, 0, false, 0},
	{261, "登岛表现1", 2, 0, "4.0_bq_shangxian", true, 0, 0, 0, 0, nil, 0, true, 0},
	{262, "登岛表现2", 2, 0, "4.0_bq_shangxian", true, 0, 0, 0, 0, nil, 0, true, 0},
	{263, "登岛表现3", 2, 0, "4.0_bq_shangxian", true, 0, 0, 0, 0, nil, 0, true, 0},
	{264, "登岛表现4", 2, 0, "4.0_bq_shangxian", true, 0, 0, 0, 0, nil, 0, true, 0},
	{265, "登岛表现5", 2, 0, "4.0_bq_shangxian", true, 0, 0, 0, 0, nil, 0, true, 0},
	{266, "4.0猫交互被攻击", 2, 0, "lzdk_diaoluo_1", false, 0, 0, 0, 0, nil, 0, false, 0},
	{12000104, "猫咪绅士手杖", 3, 0, "td_mmss_boy", true, 0, 1, -1, 0, nil, 0, false, 0},
	{12000229, "猫咪女士手持", 3, 0, "td_mmss_girl", true, 0, 1, -1, 0, nil, 0, false, 0},
	{12000210, "歌手领域话筒", 3, 0, "td_geshou_girl", false, 0, 1, -1, 0, nil, 0, false, 0},
	{12000199, "电子老贝斯", 3, 0, "td_geshou_boy", false, 1, 1, -1, 0, nil, 141168, false, 0},
	{12000323, "奇妙羽翼披风", 3, 0, "td_gd_boy", true, 0, 1, -1, 0, nil, 0, false, 0},
	{12000335, "安全员魔术手枪", 3, 0, "td_jc_girl", true, 0, 1, -1, 0, nil, 0, false, 0},
	{12000368, "月神云露长裙", 3, 0, "td_ys_girl", true, 0, 1, -1, 0, nil, 0, false, 0},
	{12000360, "日神和煦常服", 3, 0, "td_rs_boy", true, 0, 1, -1, 0, nil, 0, false, 0},
	{12000962, "小茶杯花茶手持", 3, 0, "td_xwc_girl", true, 0, 1, 1, 0, nil, 0, false, 0},
	{12000886, "小茶壶花茶手持", 3, 0, "td_xwc_boy", true, 0, 1, 2, 0, nil, 0, false, 0},
	{12000959, "领航员套装", 3, 0, "fk_jfxp_idle", false, 34, 1, 0, 0, nil, 0, false, 0},
	{12001025, "奥比敲蛋锤", 3, 0, "td_chuizi", false, 0, 1, 2, 0, nil, 0, false, 0},
	{12001027, "奥比魔法羽毛", 3, 0, "td_yumao", true, 0, 1, -1, 0, nil, 0, false, 0},
	{12001028, "热情夏威夷椰子", 3, 0, "td_yeziwu", false, 73, 1, 0, 0, nil, 0, false, 0},
	{12001371, "甜蜜石榴大勺子", 3, 0, "td_shiliu_girl", false, 58, 1, 0, 0, nil, 0, false, 0},
	{12001525, "晶莹石榴银质叉", 3, 0, "td_shiliu_boy", false, 59, 1, 0, 0, nil, 0, false, 0},
	{12001872, "白天鹅公主舞鞋", 3, 0, "td_baitiane_girl", false, 0, 1, 0, 0, nil, 0, false, 0},
	{12001844, "黑天鹅宫廷礼鞋", 3, 0, "td_baitiane_boy", false, 0, 1, 0, 0, nil, 0, false, 0},
	{12001740, "伯爵石榴奶茶棒", 3, 0, "td_shiliunaicha", true, 81, 1, 0, 0, nil, 0, false, 0},
	{12001946, "贤者的祝福", 3, 0, "td_gd_zhufupifeng", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12002166, "小丑面具", 3, 0, "td_zhaimianju", true, 85, 1, 0, 0, nil, 0, false, 0},
	{12002240, "蜜糖水晶搅拌棒", 3, 0, "td_huangbaoshi", true, 96, 1, 0, 0, nil, 0, false, 0},
	{12002258, "郁香之梦", 3, 0, "td_yuxiangzhimeng", true, 97, 1, 0, 0, nil, 0, false, 0},
	{12002455, "酷酷头巾", 3, 0, "td_1.5_kukutoujin", false, 0, 1, 0, 0, nil, 0, false, 0},
	{12002531, "夜宴蓝玫瑰花杖", 3, 0, "td_lanbaoshi_girl", true, 104, 1, 0, 0, nil, 0, false, 0},
	{12002558, "魔术蓝玫瑰礼杖", 3, 0, "td_lanbaoshi_boy", true, 105, 1, 0, 0, nil, 0, false, 0},
	{12002567, "温暖的星光", 3, 0, "td_wennuanxingguang", true, 106, 1, 0, 0, nil, 0, false, 0},
	{12002596, "翰墨风华龙纹杖", 3, 0, "td_longyinjiangshi", true, 111, 1, 0, 0, nil, 0, false, 0},
	{12002640, "镌刻丹青山河卷", 3, 0, "td_longxiaoshanhe", true, 112, 1, 0, 0, nil, 0, false, 0},
	{12002607, "财源滚滚来", 3, 0, "td_caishentutu", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12002823, "爱神魔法棒", 3, 0, "td_1.8_aishenzhufu", true, 0, 1, 2, 0, nil, 0, false, 0},
	{12002954, "深海晶莹水母帽", 3, 0, "td_shuimumaozi", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12003070, "星月云朵小号", 3, 0, "td_1.10_yunduoxiaohao", true, 123, 1, 0, 0, nil, 0, false, 0},
	{12003263, "调皮熊熊发饰", 3, 0, "td_1.11_wuyanxiaoxiong", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12003274, "你的礼物", 3, 0, "1.11.2_xinyuanliwuhe", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12003340, "曙光之纱", 3, 0, "td_1.12_shuguangzhisha", true, 134, 1, 0, 0, nil, 0, false, 0},
	{12003450, "懒羊羊麦芽糖杖", 3, 0, "td_lanyangyang_nv", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12003521, "炫彩心愿瓶", 3, 0, "td_2.0_xuancaixingyuanping", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12003510, "懒羊羊睡梦枕头", 3, 0, "td_lanyangyang_nan", true, 0, 1, 2, 0, nil, 0, false, 0},
	{12003598, "蝶之音指挥棒", 3, 0, "td_2.0_diezhiyinzhihuibang", true, 0, 1, 2, 0, nil, 0, false, 0},
	{12003648, "神之冠", 3, 0, "td_2.0_shenzhidie_nv", true, 0, 1, 2, 0, nil, 0, false, 0},
	{12003634, "蝶之乐", 3, 0, "td_2.0_shenzhidie_nan", true, 0, 1, 2, 0, nil, 0, false, 0},
	{12003647, "365签到玩偶服", 3, 0, "td_2.0_shilaimuliantiyi", true, 116, 3, 0, 0, nil, 0, false, 0},
	{12003664, "你的冰柠茶", 3, 0, "2.0.4_bingningcha", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12003728, "星空旅“兔”", 3, 0, "2.1_td_xingjizhaixingxing", true, 137, 1, 0, 0, nil, 0, false, 0},
	{12003782, "晴空一鹤羽翅服", 3, 0, "2.1_td_xianhe_nan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12003905, "秋叶印记相机", 3, 0, "2.2_td_qiuyexiangji", true, 142, 1, 0, 0, nil, 0, false, 0},
	{12003945, "月华盈辉", 3, 0, "2.2_td_yuehuayinghui", true, 134, 1, 0, 0, nil, 0, false, 0},
	{12004024, "月灵祈福扫帚", 3, 0, "2.3_td_yuelingsaozhou", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004026, "幽冥铃铛伞", 3, 0, "2.3.2_td_bianhuakai", true, 147, 1, 0, 0, nil, 0, false, 0},
	{12004096, "熊熊酥米玩偶服", 3, 0, "2.4.2_td_susumiliantiyi", true, 148, 1, 0, 0, nil, 0, false, 0},
	{12004173, "雾月之舞", 3, 0, "2.4_td_wuyuezhiwu", true, 134, 1, 0, 0, nil, 0, false, 0},
	{12004230, "冰霜王冠", 3, 0, "2.5_td_bingxuewangguan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004351, "小麋鹿玩偶服", 3, 0, "2.5.2_td_miluliantiyi", true, 154, 1, 0, 0, nil, 0, false, 0},
	{12004441, "沐光之环", 3, 0, "2.6_td_shenshengguanghuan", true, 134, 1, 0, 0, nil, 0, false, 0},
	{12004486, "紫罗兰之语设计", 3, 0, "2.6_td_huayubaoshi_nv", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004476, "绣球花之韵折绣", 3, 0, "2.6_td_huayubaoshi_nan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004533, "芋泥泡泡机", 3, 0, "2.6.2_td_yunipaopao", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004609, "镶金缀蓝古琴", 3, 0, "2.7_td_guqin", true, 163, 3, 0, 0, nil, 0, false, 0},
	{12004466, "红莲灼厄红莲扇", 3, 0, "2.7_td_huanxilong_nan", true, 165, 1, 0, 0, nil, 0, false, 0},
	{12004409, "苍翠润泽玉如意", 3, 0, "2.7_td_shengminglong_nv", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004648, "惊鸿飞天鼓", 3, 0, "2.7_td_dingdongshouyaogu", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004719, "望春归花花风车", 3, 0, "2.8_td_huahuafengche", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004713, "青春永驻连体衣", 3, 0, "2.8_td_qingchunyongzhu", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12004842, "不夜城之歌", 3, 0, "2.9_td_fugulimai", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12004926, "云端筑梦纸飞机", 3, 0, "2.9_td_yunduozhifeiji", true, 134, 1, 0, 142010, nil, 0, false, 0},
	{12004917, "酸甜葡萄果盘", 3, 0, "2.9.2_td_putaoguopan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005007, "太阳花花充能锤", 3, 0, "2.10_td_senzhichuichui", true, 176, 3, 0, 0, nil, 0, false, 0},
	{12005040, "童真鸭鸭雨衣", 3, 0, "2.10.2_td_xiaohuangyayuyi", true, 179, 1, 0, 0, nil, 0, false, 0},
	{12005221, "魔术助手小猫偶", 3, 0, "2.11_td_moshumaomao", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005295, "可爱熊熊圆珠笔", 3, 0, "2.11.2_td_biyeyuanzhubi", true, 201, 1, 0, 0, nil, 0, false, 0},
	{12005370, "神秘海螺杯", 3, 0, "3.0_td_shenhairenyumoyao", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005446, "璀璨星尘棒", 3, 0, "3.0_td_chuchendanzi", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005353, "群星盛宴花之铃", 3, 0, "3.0_td_hongbaoshihuiguniang_nv", true, 205, 1, 0, 0, nil, 0, false, 0},
	{12005441, "时沙掠影沙之漏", 3, 0, "3.0_td_hongbaoshihuiguniang_nan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005423, "脆脆甜筒服", 3, 0, "3.0.2_td_tiantongliantiyi", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12005174, "小黑魔仙黑魔琴", 3, 0, "3.0_td_heimotiqin", true, 204, 1, 0, 0, nil, 0, false, 0},
	{12005163, "魔仙女王蝴蝶杖", 3, 0, "3.0_td_moxiannvwang", true, 206, 1, 0, 0, nil, 0, false, 0},
	{12005532, "星羽表演棒", 3, 0, "3.1_td_ticaobang", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005616, "仲夏萤火捕虫网", 3, 0, "3.1.2_td_buchongwang", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005675, "奇妙魔药锅", 3, 0, "3.2_td_moyaoguo", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005753, "忘忧魔法糖", 3, 0, "3.3_td_bangbangtang", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005794, "充满爱意草莓壶", 3, 0, "3.3.2_td_caomeisashuihu", true, 219, 1, 0, 0, nil, 0, false, 0},
	{12005894, "背后灵玩偶", 3, 0, "3.4_td_beihoulingrenou", true, 134, 1, 0, 0, nil, 0, false, 0},
	{12005938, "曼舞折扇", 3, 0, "3.4.2_td_gudianleisishan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12006175, "妖灵召唤簿", 3, 0, "3.5_td_panguance", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12006099, "AI女孩LAURA", 3, 0, "3.5.2_td_laura", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12006205, "白日梦香水瓶", 3, 0, "3.6_td_bairimengxiangshuiping", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12006176, "甜蜜抱抱熊", 3, 0, "3.6.2_td_judaxiongwawa", true, 116, 1, 0, 0, nil, 0, false, 0},
	{12006370, "喜鹊登梅长箫", 3, 0, "3.7_td_xiquedongxiao", true, 0, 3, 0, 0, nil, 0, false, 0},
	{12006540, "煌宇莲华不染尘", 3, 0, "3.7_td_dunhuangfuxinanzhuang", true, 249, 1, 0, 0, nil, 0, false, 0},
	{12006510, "朔漠神女抚弦音", 3, 0, "3.7.2_td_dunhuangnvwanvzhuang", true, 252, 1, 0, 0, nil, 0, false, 0},
	{12006435, "一口团圆碗", 3, 0, "3.7.2_td_tangyuanwan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12006514, "花之时钟", 3, 0, "3.8_td_huazhishizhong", true, 134, 3, 0, 0, nil, 0, false, 0},
	{12006515, "SUSUMI之翼", 3, 0, "3.8.2_td_shigaoyi", true, 261, 1, 0, 0, nil, 0, false, 0},
	{12006707, "心之画笔", 3, 0, "3.9_td_huabizhixin", true, 259, 1, 0, 0, nil, 0, false, 0},
	{12006714, "Rabeea巨型铅笔", 3, 0, "3.9_td_qianbi", false, 260, 1, 0, 0, nil, 0, false, 0},
	{12006676, "Rabeea圆周裙摆", 3, 0, "3.9_td_chizi", false, 0, 1, 0, 0, nil, 0, false, 0},
	{12006641, "Rabeea闹钟衣裤", 3, 0, "3.9_td_rebeeanaozhonglianyiqun", false, 0, 1, 0, 0, nil, 0, false, 0},
	{12006766, "彩蛋兔兔抱偶", 3, 0, "3.9.2_td_tuzicaidan", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12006902, "港城来电", 3, 0, "3.z10_td_dianhuaheying", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007067, "幻彩涂鸦喷漆", 3, 0, "3.11_td_nonopenqiguan", true, 274, 3, 0, 0, nil, 0, false, 0},
	{12007173, "绘梦逐光纸飞机", 3, 0, "3.z12_td_zhifeiji", true, 281, 1, 0, 0, nil, 0, false, 0},
	{12007176, "异世界玻璃钥匙", 3, 0, "4.0_td_ciyuanyaoshi", true, 282, 1, 0, 0, nil, 0, false, 0},
	{12007408, "红色幸运礼炮筒", 3, 0, "4.0_td_lipao", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007409, "蓝色幸运礼炮筒", 3, 0, "4.0_td_lipao", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007410, "黄色幸运礼炮筒", 3, 0, "4.0_td_lipao", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007196, "爱之蜕变浪漫弓", 3, 0, "4.0_td_aishengongjian", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007431, "自由之风召风杖", 3, 0, "4.0_td_tianwangxing", true, 288, 1, 0, 0, nil, 0, false, 0},
	{12007452, "闪耀香槟瓶", 3, 0, "4.0_td_xiangbingta", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007206, "红粉都会好帮手", 3, 0, "4.0_td_jiweijiu", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007270, "玫瑰冰晶启瓶器", 3, 0, "4.0_td_bingmeigui", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12007472, "朝露花灯", 3, 0, "4.0_td_xiayezhimeng", true, 292, 1, 0, 0, nil, 0, false, 0},
	{10001, "采集动画-砍树", 4, 0, "caiji_kanshu", false, 11, 1, 0, 0, nil, 130007, true, 0},
	{10002, "采集动画-收成", 4, 0, "shoucheng-guoshu", false, 37, 1, 0, 0, nil, 130037, true, 0},
	{10003, "采集动画-采蜜", 4, 0, "caiji-caimi", false, 0, 1, 0, 0, nil, 130005, true, 0},
	{10004, "采集动画—挖矿", 4, 0, "wk_2", false, 17, 1, 0, 0, nil, 130088, true, 0},
	{10005, "采集动画-取水", 4, 0, "qs_qushui", false, 0, 2, 0, 0, nil, 130103, true, 0},
	{10006, "采集动画-扫地", 4, 0, "qingsao", false, 0, 1, 0, 0, nil, 0, true, 0},
	{10007, "农场动画-收成农作物", 4, 0, "shoucheng", false, 0, 1, 0, 0, nil, 0, false, 0},
	{10008, "农场动画-收成魔法作物", 4, 0, "shoucheng-guoshu", false, 0, 1, 0, 0, nil, 0, false, 0},
	{10009, "采集动画-采油", 4, 0, "caiji_1.9_caiyouji", false, 119, 1, 0, 141632, nil, 0, false, 0},
	{200001, "掏井盖", 4, 0, "jg_fan", false, 0, 1, 0, 0, nil, 0, false, 0},
	{200002, "掏井盖失败", 4, 0, "jg_diaoluo", false, 16, 1, 0, 0, nil, 0, false, 0},
	{300001, "星星坐垫", 5, 25000001, "sit_zuodian", false, 0, 0, -1, 0, nil, 0, true, 0},
	{300002, "叶子坐垫", 5, 25000002, "sit_yuandi", false, 0, 0, -1, 0, nil, 0, true, 0},
	{300003, "双人花坐垫-白", 5, 25000003, "sit_yuandi", false, 0, 0, -1, 0, nil, 0, true, 0},
	{300004, "双人花坐垫-粉", 5, 25000004, "sit_yuandi", false, 0, 0, -1, 0, nil, 0, true, 0},
	{300005, "飞云坐垫", 5, 25000005, "sit_zuodian", false, 0, 0, -1, 0, nil, 0, true, 0},
	{300006, "摇摇鸭", 5, 25000006, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300007, "爱心长椅", 5, 25000007, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300008, "梦幻摇摇椅", 5, 25000008, "cj_yaoyaole", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300009, "花朝秋千", 5, 25000009, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300010, "蜜糖果篮", 5, 25000010, "cj_guolan", false, 86, 0, 0, 0, nil, 0, true, 0},
	{300011, "奇异野餐垫", 5, 25000011, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300012, "麋鹿雪橇", 5, 25000012, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300013, "点墨江山", 5, 25000013, "sit_zuodian", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300014, "造梦童话", 5, 25000014, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300015, "霓虹音乐椅", 5, 25000015, "sit_zuodian_yinyueyi", false, 124, 0, 0, 0, nil, 0, true, 0},
	{300016, "趣味纸箱", 5, 25000016, "zd_1.11_emmazhixiangzuodian", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300017, "小黄鸭纸船", 5, 25000017, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300018, "羊羊椰树吊床", 5, 25000018, "sit_zuodian_diaoyi", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300019, "蝴蝶画框坐垫", 5, 25000019, "sit_zuodian", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300020, "盛夏柠檬", 5, 25000020, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300021, "游梦花枝", 5, 25000021, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300022, "小红摇摇鸭", 5, 25000022, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300023, "小橙摇摇鸭", 5, 25000023, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300024, "小黑摇摇鸭", 5, 25000024, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300025, "小紫摇摇鸭", 5, 25000025, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300026, "小蓝摇摇鸭", 5, 25000026, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300027, "小绿摇摇鸭", 5, 25000027, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300028, "小粉摇摇鸭", 5, 25000028, "cj_yazi_idle2", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300029, "请君入戏", 5, 25000029, "2.2_zd_zheshan", false, 143, 0, 0, 0, nil, 0, true, 0},
	{300030, "共赏月团", 5, 25000030, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300031, "幽冥竹灵", 5, 25000031, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300032, "萌趣爆米花", 5, 25000032, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300033, "幸运花环", 5, 25000033, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300034, "芋泥麻薯碗", 5, 25000034, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300035, "元夜鱼灯游", 5, 25000035, "2.7_zd_yuanyeyudeng", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300036, "灼灼花如绣", 5, 25000036, "2.7_zd_honglianmudan", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300037, "甜心草莓蛋糕", 5, 25000037, "2.8.2_zd_caomeidangao", false, 170, 0, 0, 0, nil, 0, true, 0},
	{300038, "向左熊转杯杯椅", 5, 25000038, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300039, "遗迹碎片", 5, 25000040, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300040, "熊熊芝士披萨", 5, 25000039, "2.9_zd_zhishipisa", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300041, "摇摇木马", 5, 25000041, "2.10_zd_yaoyaomuma", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300042, "趣味三角尺", 5, 25000042, "2.11.2_zd_quweisanjiaochi", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300043, "萌二夹心饼坐垫", 5, 25000043, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300044, "焦糖小布丁", 5, 25000044, "3.0.2_zd_jiaotangxiaobuding", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300045, "魔法项链坐垫", 5, 25000045, "3.0_zd_mofaxianglian", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300046, "璀璨星辰椅", 5, 25000046, "3.0_zd_hongbaoshi", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300047, "冰凉甜西瓜", 5, 25000047, "3.1.2_zd_bingliangtianxigua", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300048, "草莓牛奶", 5, 25000048, "3.3.2_zd_caomeiniunai", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300049, "梦镜桃心垫", 5, 25000049, "3.3_zd_aixinshuangrenzuodian", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300050, "小泡芙坐垫", 5, 25000050, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300051, "复古黄包车", 5, 25000051, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300052, "LAURA萌趣帽", 5, 25000052, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300053, "毛线团篮子", 5, 25000053, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300054, "灵蛇献瑞", 5, 25000054, "3.7_zd_lingshexianrui", false, 253, 0, 0, 0, nil, 0, true, 0},
	{300055, "桃花酥食盒", 5, 25000055, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300056, "桃花财运来坐垫", 5, 25000056, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300057, "SUSUMI宝贝软垫", 5, 25000057, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300058, "兔兔气球坐垫", 5, 25000058, "3.9.2_zd_tutuqiqiu", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300059, "玫瑰香雾", 5, 25000059, "3.9_zd_xiangshuiping", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300060, "猪米飞毯", 5, 25000060, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300061, "星愿卡坐垫", 5, 25000061, "4.0_zd_xingxuanliuhui", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300062, "彩虹坐垫", 5, 25000062, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300063, "摇曳醉意", 5, 25000063, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300064, "牵牛花秋千坐垫", 5, 25000064, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{300065, "魔法棒坐垫", 5, 25000065, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310001, "名侦探茶话桌", 5, 25010001, "2.11_zd_idle1", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310002, "萌宠营地", 5, 25010002, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310003, "熊熊快乐操", 5, 25010003, "3.2_zd_xiongxiongkuailecao_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310004, "生日蛋糕坐垫", 5, 25010004, "3.3_zd_shengribayinhe_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310005, "恐怖人偶故事会", 5, 25010005, "3.4_zd_renougushihui_idle", false, 218, 0, 0, 0, nil, 0, true, 0},
	{310006, "绮梦礼赠", 5, 25010006, "3.6_zd_qimenglizeng_idle", false, 237, 0, 0, 0, nil, 0, true, 0},
	{310007, "恭喜发财利是来", 5, 25010007, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310008, "莫奈画框坐垫", 5, 25010008, "sit", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310009, "怪可爱睡衣派对", 5, 25010009, "3.11_zd_shuiyipaidui_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{310010, "熊熊跳跳棋", 5, 25010010, "4.0_dgnd_xiongxiongtiaoqi_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{320001, "淡紫花梦伞", 2, 13003858, "2.11_ys_shuangrenyangsan_idle", true, 187, 0, 0, 0, nil, 0, true, 0},
	{320002, "熊耳晴雨伞", 2, 13003857, "2.11_ys_shuangrenyangsan_idle", true, 188, 0, 0, 0, nil, 0, true, 0},
	{320003, "童趣缤纷沙滩伞", 2, 13003933, "2.11_ys_shuangrenyangsan_idle", true, 5001, 0, 0, 0, nil, 0, true, 0},
	{320004, "海汐幽光水母伞", 2, 13004024, "2.11_ys_shuangrenyangsan_idle", true, 5002, 0, 0, 0, nil, 0, true, 0},
	{320005, "熊熊透明伞", 2, 13004046, "2.11_ys_shuangrenyangsan_idle", true, 5003, 0, 0, 0, nil, 0, true, 0},
	{320006, "纯白之梦羽伞", 2, 13004130, "2.11_ys_shuangrenyangsan_idle", true, 5004, 0, 0, 0, nil, 0, true, 0},
	{320007, "桂香思月伞", 2, 13004184, "2.11_ys_shuangrenyangsan_idle", true, 5005, 0, 0, 0, nil, 0, true, 0},
	{320008, "金秋枫叶伞", 2, 13004222, "2.11_ys_shuangrenyangsan_idle", true, 5007, 0, 0, 0, nil, 0, true, 0},
	{320009, "清新雏菊伞", 2, 13004223, "2.11_ys_shuangrenyangsan_idle", true, 5008, 0, 0, 0, nil, 0, true, 0},
	{320010, "蓬蓬蘑菇伞", 2, 13004224, "2.11_ys_shuangrenyangsan_idle", true, 5006, 0, 0, 0, nil, 0, true, 0},
	{320011, "浪漫樱花伞", 2, 13004637, "2.11_ys_shuangrenyangsan_idle", true, 5009, 0, 0, 0, nil, 0, true, 0},
	{320012, "星梦云朵伞", 2, 13004750, "2.11_ys_shuangrenyangsan_idle", true, 5010, 0, 0, 0, nil, 0, true, 0},
	{320013, "烟花之约伞", 2, 13005067, "2.11_ys_shuangrenyangsan_idle", true, 5011, 0, 0, 0, nil, 0, true, 0},
	{330001, "花海跳舞毯", 5, 13004454, "", false, 0, 0, 0, 0, nil, 0, true, 0},
	{330002, "凛冬美味", 5, 13004456, "", false, 0, 0, 0, 0, nil, 0, true, 0},
	{330003, "气球摊", 5, 13004752, "", false, 0, 0, 0, 0, nil, 0, true, 0},
	{330004, "娃娃机", 5, 13004751, "", false, 0, 0, 0, 0, nil, 0, true, 0},
	{330005, "奥记茶档", 5, 13004833, "", false, 0, 0, 0, 0, nil, 0, true, 0},
	{330006, "港食好味", 5, 13004834, "", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40001, "割麦子", 6, 0, "dg_gemaizi", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40002, "割麦子待机", 6, 0, "dg_gemaizi_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40003, "晒麦子", 6, 0, "dg_shaimaizi", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40004, "晒麦子待机", 6, 0, "dg_shaimaizi_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40005, "磨麦子", 6, 0, "dg_momaizi", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40006, "磨麦子待机", 6, 0, "dg_momaizi_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40007, "擀面粉", 6, 0, "dg_ganmianfen", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40008, "擀面粉待机", 6, 0, "dg_ganmianfen_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40009, "烤面包", 6, 0, "dg_kaomianbao", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40010, "烤面包待机", 6, 0, "dg_kaomianbao_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40011, "卖面包", 6, 0, "dg_maimianbao", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40012, "卖面包待机", 6, 0, "dg_maimianbao_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40013, "除草", 6, 0, "dg_chucao", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40014, "除草待机", 6, 0, "dg_chucao_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40015, "擦石碑", 6, 0, "dg_cashibei", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40016, "擦石碑待机", 6, 0, "dg_cashibei_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40017, "擦窗户", 6, 0, "dg_cachuang", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40018, "擦窗户待机", 6, 0, "dg_cachuang_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40019, "擦杯子", 6, 0, "dg_kf1_cabeizi", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40020, "擦杯子待机", 6, 0, "dg_kf1_cabeizi_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40021, "磨咖啡", 6, 0, "dg_kf2_mokafei", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40022, "磨咖啡待机", 6, 0, "dg_kf2_mokafei_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40023, "冲奶咖", 6, 0, "dg_kf3_lahua", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40024, "冲奶咖待机", 6, 0, "dg_kf3_lahua_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40025, "擦星星", 6, 0, "dg_hy1_xingxing", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40026, "擦星星待机", 6, 0, "dg_hy1_xingxing_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40027, "喷彩虹", 6, 0, "dg_hy2_shangse", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40028, "喷彩虹待机", 6, 0, "dg_hy2_shangse_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40029, "造云", 6, 0, "dg_hy3_yunduo", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40030, "造云待机", 6, 0, "dg_hy3_yunduo_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40031, "整理魔药", 6, 0, "dg_zhaoyaoji", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40032, "整理魔药待机", 6, 0, "dg_zhaoyaoji_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40033, "测量魔药", 6, 0, "dg_chenyaoji", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40034, "测量魔药待机", 6, 0, "dg_chenyaoji_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40035, "煮魔药", 6, 0, "dg_zhuyaoji", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40036, "煮魔药待机", 6, 0, "dg_zhuyaoji_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40037, "发传单", 6, 0, "dg_cx1_fcd", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40038, "发传单待机", 6, 0, "dg_cx1_fcd_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40039, "转招牌", 6, 0, "dg_cx2_zzp", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40040, "转招牌待机", 6, 0, "dg_cx2_zzp_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40041, "促销表演", 6, 0, "dg_cx3_cxby", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40042, "促销表演待机", 6, 0, "dg_cx3_cxby_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40043, "卖冰柜", 6, 0, "dg_xj1_maibinggun", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40044, "卖冰棍_待机", 6, 0, "dg_xj1_maibinggun_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40045, "反馈动作-耍棍", 6, 0, "dg_shuagun", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40046, "待机动作", 6, 0, "dg_shuagun_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40049, "书福迎春", 6, 0, "dg_2.7_shufuyingchun", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40050, "书福迎春待机", 6, 0, "dg_2.7_shufuyingchun_idle", false, 0, 0, 0, 0, nil, 0, true, 0},
	{12003362, "飞速赶到", 3, 0, "bq_xxk_chuchang", false, 0, 0, 0, 0, nil, 0, false, 0},
	{12003363, "探头探脑", 3, 0, "bq_xxk_tantou", false, 0, 0, 0, 0, nil, 0, false, 0},
	{12003364, "愉快蹦跳", 3, 0, "bq_xxk_bengbengtiao", false, 0, 0, 0, 0, nil, 0, false, 0},
	{12003365, "叉腰摇摆", 3, 0, "bq_xxk_chayaoniu", false, 0, 0, 0, 0, nil, 0, true, 0},
	{12003366, "热情飞吻", 3, 0, "bq_xxk_feiwen", true, 0, 0, 0, 0, nil, 0, true, 0},
	{12003367, "单腿平衡", 3, 0, "bq_xxk_tanwan", false, 0, 0, 0, 0, nil, 0, true, 0},
	{40047, "放孔明灯", 4, 0, "2.1_ymqq_kongmingdeng", false, 0, 1, 0, 0, nil, 0, false, 0},
	{12003994, "时空旅行", 3, 0, "2.3_bq_xxk_kongjiankaichang", true, 0, 0, 0, 0, nil, 0, false, 0},
	{40048, "幽冥望远镜", 4, 0, "2.3_cj_wangyuanjing", false, 0, 2, 0, 0, nil, 0, false, 0},
	{12004413, "空间开场(呆萌)", 3, 0, "2.5.2_bq_xxk_daimeng", false, 0, 0, 0, 0, nil, 0, false, 0},
	{12004522, "空间开场(贪吃)", 3, 0, "2.6.2_bq_xxk_tanchi", false, 0, 0, 0, 0, nil, 0, false, 0},
	{12004691, "空间开场(轻舞)", 3, 0, "2.7.2_bq_xxk_yuerwu", false, 0, 0, 0, 0, nil, 0, false, 0},
	{40051, "观察羊皮卷", 4, 0, "2.9.2_kldw_yangpijuan", false, 0, 1, 0, 0, nil, 0, false, 0},
	{12004927, "空间开场（召唤）", 3, 0, "2.9.2_xxk_kaichang", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12004925, "空间开场（破蛋）", 3, 0, "2.8.2_bq_xxk_pokeerchu", true, 0, 0, 0, 0, nil, 0, false, 0},
	{50001, "万象港传送门", 2, 0, "2.9_td_chuansongmen", true, 0, 1, 0, 0, nil, 0, false, 0},
	{12005241, "空间开场(匆忙)", 3, 0, "2.11_bq_xxk_congmang", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12005462, "空间开场(爱意)", 3, 0, "3.0.2_bq_xxk_aiyi", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12005603, "空间开场(甜蜜)", 3, 0, "3.1_xxk_tianmi", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12005830, "空间开场(草莓)", 3, 0, "3.3.2_xxk_caomei", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12005971, "空间开场(摇摆)", 3, 0, "3.4.2_xxk_manwu", false, 0, 0, 0, 0, nil, 0, false, 0},
	{12006229, "空间开场(下雪)", 3, 0, "3.6.2_xxk_bingxue", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12006491, "空间开场(放灯)", 3, 0, "3.7.2_xxk_fangdeng", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12006813, "空间开场(气球)", 3, 0, "3.9.2_xxk_qiqiu", true, 0, 0, 0, 0, nil, 0, false, 0},
	{12007471, "空间开场(喇叭)", 3, 0, "4.0.2_xxk_laba", true, 289, 0, 0, 0, nil, 0, false, 0},
}

local t_pose = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[5] = dataList[4],
	[6] = dataList[5],
	[7] = dataList[6],
	[8] = dataList[7],
	[10] = dataList[8],
	[11] = dataList[9],
	[12] = dataList[10],
	[14] = dataList[11],
	[15] = dataList[12],
	[16] = dataList[13],
	[17] = dataList[14],
	[18] = dataList[15],
	[19] = dataList[16],
	[20] = dataList[17],
	[21] = dataList[18],
	[22] = dataList[19],
	[23] = dataList[20],
	[24] = dataList[21],
	[25] = dataList[22],
	[26] = dataList[23],
	[27] = dataList[24],
	[28] = dataList[25],
	[29] = dataList[26],
	[30] = dataList[27],
	[31] = dataList[28],
	[32] = dataList[29],
	[33] = dataList[30],
	[34] = dataList[31],
	[35] = dataList[32],
	[36] = dataList[33],
	[37] = dataList[34],
	[38] = dataList[35],
	[39] = dataList[36],
	[40] = dataList[37],
	[41] = dataList[38],
	[42] = dataList[39],
	[43] = dataList[40],
	[44] = dataList[41],
	[45] = dataList[42],
	[46] = dataList[43],
	[47] = dataList[44],
	[48] = dataList[45],
	[49] = dataList[46],
	[50] = dataList[47],
	[51] = dataList[48],
	[52] = dataList[49],
	[53] = dataList[50],
	[54] = dataList[51],
	[55] = dataList[52],
	[56] = dataList[53],
	[57] = dataList[54],
	[58] = dataList[55],
	[59] = dataList[56],
	[60] = dataList[57],
	[61] = dataList[58],
	[62] = dataList[59],
	[63] = dataList[60],
	[64] = dataList[61],
	[65] = dataList[62],
	[66] = dataList[63],
	[67] = dataList[64],
	[68] = dataList[65],
	[69] = dataList[66],
	[70] = dataList[67],
	[71] = dataList[68],
	[72] = dataList[69],
	[73] = dataList[70],
	[74] = dataList[71],
	[75] = dataList[72],
	[76] = dataList[73],
	[77] = dataList[74],
	[100] = dataList[75],
	[101] = dataList[76],
	[102] = dataList[77],
	[103] = dataList[78],
	[104] = dataList[79],
	[105] = dataList[80],
	[106] = dataList[81],
	[107] = dataList[82],
	[108] = dataList[83],
	[109] = dataList[84],
	[110] = dataList[85],
	[111] = dataList[86],
	[112] = dataList[87],
	[113] = dataList[88],
	[114] = dataList[89],
	[115] = dataList[90],
	[116] = dataList[91],
	[117] = dataList[92],
	[118] = dataList[93],
	[119] = dataList[94],
	[120] = dataList[95],
	[121] = dataList[96],
	[122] = dataList[97],
	[123] = dataList[98],
	[124] = dataList[99],
	[125] = dataList[100],
	[126] = dataList[101],
	[127] = dataList[102],
	[128] = dataList[103],
	[129] = dataList[104],
	[130] = dataList[105],
	[131] = dataList[106],
	[132] = dataList[107],
	[133] = dataList[108],
	[134] = dataList[109],
	[135] = dataList[110],
	[136] = dataList[111],
	[137] = dataList[112],
	[138] = dataList[113],
	[139] = dataList[114],
	[140] = dataList[115],
	[141] = dataList[116],
	[142] = dataList[117],
	[143] = dataList[118],
	[144] = dataList[119],
	[145] = dataList[120],
	[146] = dataList[121],
	[147] = dataList[122],
	[148] = dataList[123],
	[149] = dataList[124],
	[150] = dataList[125],
	[151] = dataList[126],
	[152] = dataList[127],
	[153] = dataList[128],
	[154] = dataList[129],
	[155] = dataList[130],
	[156] = dataList[131],
	[157] = dataList[132],
	[158] = dataList[133],
	[159] = dataList[134],
	[160] = dataList[135],
	[161] = dataList[136],
	[162] = dataList[137],
	[163] = dataList[138],
	[164] = dataList[139],
	[165] = dataList[140],
	[166] = dataList[141],
	[167] = dataList[142],
	[168] = dataList[143],
	[169] = dataList[144],
	[170] = dataList[145],
	[171] = dataList[146],
	[172] = dataList[147],
	[173] = dataList[148],
	[174] = dataList[149],
	[175] = dataList[150],
	[176] = dataList[151],
	[177] = dataList[152],
	[178] = dataList[153],
	[179] = dataList[154],
	[180] = dataList[155],
	[181] = dataList[156],
	[182] = dataList[157],
	[183] = dataList[158],
	[184] = dataList[159],
	[185] = dataList[160],
	[186] = dataList[161],
	[187] = dataList[162],
	[188] = dataList[163],
	[189] = dataList[164],
	[190] = dataList[165],
	[191] = dataList[166],
	[192] = dataList[167],
	[193] = dataList[168],
	[194] = dataList[169],
	[195] = dataList[170],
	[196] = dataList[171],
	[197] = dataList[172],
	[198] = dataList[173],
	[199] = dataList[174],
	[200] = dataList[175],
	[201] = dataList[176],
	[202] = dataList[177],
	[203] = dataList[178],
	[204] = dataList[179],
	[205] = dataList[180],
	[206] = dataList[181],
	[207] = dataList[182],
	[208] = dataList[183],
	[209] = dataList[184],
	[210] = dataList[185],
	[211] = dataList[186],
	[212] = dataList[187],
	[213] = dataList[188],
	[214] = dataList[189],
	[215] = dataList[190],
	[216] = dataList[191],
	[217] = dataList[192],
	[218] = dataList[193],
	[219] = dataList[194],
	[220] = dataList[195],
	[221] = dataList[196],
	[222] = dataList[197],
	[223] = dataList[198],
	[224] = dataList[199],
	[225] = dataList[200],
	[226] = dataList[201],
	[227] = dataList[202],
	[228] = dataList[203],
	[229] = dataList[204],
	[230] = dataList[205],
	[231] = dataList[206],
	[232] = dataList[207],
	[233] = dataList[208],
	[234] = dataList[209],
	[235] = dataList[210],
	[236] = dataList[211],
	[237] = dataList[212],
	[238] = dataList[213],
	[239] = dataList[214],
	[240] = dataList[215],
	[241] = dataList[216],
	[242] = dataList[217],
	[243] = dataList[218],
	[244] = dataList[219],
	[245] = dataList[220],
	[246] = dataList[221],
	[247] = dataList[222],
	[248] = dataList[223],
	[249] = dataList[224],
	[260] = dataList[225],
	[261] = dataList[226],
	[262] = dataList[227],
	[263] = dataList[228],
	[264] = dataList[229],
	[265] = dataList[230],
	[266] = dataList[231],
	[12000104] = dataList[232],
	[12000229] = dataList[233],
	[12000210] = dataList[234],
	[12000199] = dataList[235],
	[12000323] = dataList[236],
	[12000335] = dataList[237],
	[12000368] = dataList[238],
	[12000360] = dataList[239],
	[12000962] = dataList[240],
	[12000886] = dataList[241],
	[12000959] = dataList[242],
	[12001025] = dataList[243],
	[12001027] = dataList[244],
	[12001028] = dataList[245],
	[12001371] = dataList[246],
	[12001525] = dataList[247],
	[12001872] = dataList[248],
	[12001844] = dataList[249],
	[12001740] = dataList[250],
	[12001946] = dataList[251],
	[12002166] = dataList[252],
	[12002240] = dataList[253],
	[12002258] = dataList[254],
	[12002455] = dataList[255],
	[12002531] = dataList[256],
	[12002558] = dataList[257],
	[12002567] = dataList[258],
	[12002596] = dataList[259],
	[12002640] = dataList[260],
	[12002607] = dataList[261],
	[12002823] = dataList[262],
	[12002954] = dataList[263],
	[12003070] = dataList[264],
	[12003263] = dataList[265],
	[12003274] = dataList[266],
	[12003340] = dataList[267],
	[12003450] = dataList[268],
	[12003521] = dataList[269],
	[12003510] = dataList[270],
	[12003598] = dataList[271],
	[12003648] = dataList[272],
	[12003634] = dataList[273],
	[12003647] = dataList[274],
	[12003664] = dataList[275],
	[12003728] = dataList[276],
	[12003782] = dataList[277],
	[12003905] = dataList[278],
	[12003945] = dataList[279],
	[12004024] = dataList[280],
	[12004026] = dataList[281],
	[12004096] = dataList[282],
	[12004173] = dataList[283],
	[12004230] = dataList[284],
	[12004351] = dataList[285],
	[12004441] = dataList[286],
	[12004486] = dataList[287],
	[12004476] = dataList[288],
	[12004533] = dataList[289],
	[12004609] = dataList[290],
	[12004466] = dataList[291],
	[12004409] = dataList[292],
	[12004648] = dataList[293],
	[12004719] = dataList[294],
	[12004713] = dataList[295],
	[12004842] = dataList[296],
	[12004926] = dataList[297],
	[12004917] = dataList[298],
	[12005007] = dataList[299],
	[12005040] = dataList[300],
	[12005221] = dataList[301],
	[12005295] = dataList[302],
	[12005370] = dataList[303],
	[12005446] = dataList[304],
	[12005353] = dataList[305],
	[12005441] = dataList[306],
	[12005423] = dataList[307],
	[12005174] = dataList[308],
	[12005163] = dataList[309],
	[12005532] = dataList[310],
	[12005616] = dataList[311],
	[12005675] = dataList[312],
	[12005753] = dataList[313],
	[12005794] = dataList[314],
	[12005894] = dataList[315],
	[12005938] = dataList[316],
	[12006175] = dataList[317],
	[12006099] = dataList[318],
	[12006205] = dataList[319],
	[12006176] = dataList[320],
	[12006370] = dataList[321],
	[12006540] = dataList[322],
	[12006510] = dataList[323],
	[12006435] = dataList[324],
	[12006514] = dataList[325],
	[12006515] = dataList[326],
	[12006707] = dataList[327],
	[12006714] = dataList[328],
	[12006676] = dataList[329],
	[12006641] = dataList[330],
	[12006766] = dataList[331],
	[12006902] = dataList[332],
	[12007067] = dataList[333],
	[12007173] = dataList[334],
	[12007176] = dataList[335],
	[12007408] = dataList[336],
	[12007409] = dataList[337],
	[12007410] = dataList[338],
	[12007196] = dataList[339],
	[12007431] = dataList[340],
	[12007452] = dataList[341],
	[12007206] = dataList[342],
	[12007270] = dataList[343],
	[12007472] = dataList[344],
	[10001] = dataList[345],
	[10002] = dataList[346],
	[10003] = dataList[347],
	[10004] = dataList[348],
	[10005] = dataList[349],
	[10006] = dataList[350],
	[10007] = dataList[351],
	[10008] = dataList[352],
	[10009] = dataList[353],
	[200001] = dataList[354],
	[200002] = dataList[355],
	[300001] = dataList[356],
	[300002] = dataList[357],
	[300003] = dataList[358],
	[300004] = dataList[359],
	[300005] = dataList[360],
	[300006] = dataList[361],
	[300007] = dataList[362],
	[300008] = dataList[363],
	[300009] = dataList[364],
	[300010] = dataList[365],
	[300011] = dataList[366],
	[300012] = dataList[367],
	[300013] = dataList[368],
	[300014] = dataList[369],
	[300015] = dataList[370],
	[300016] = dataList[371],
	[300017] = dataList[372],
	[300018] = dataList[373],
	[300019] = dataList[374],
	[300020] = dataList[375],
	[300021] = dataList[376],
	[300022] = dataList[377],
	[300023] = dataList[378],
	[300024] = dataList[379],
	[300025] = dataList[380],
	[300026] = dataList[381],
	[300027] = dataList[382],
	[300028] = dataList[383],
	[300029] = dataList[384],
	[300030] = dataList[385],
	[300031] = dataList[386],
	[300032] = dataList[387],
	[300033] = dataList[388],
	[300034] = dataList[389],
	[300035] = dataList[390],
	[300036] = dataList[391],
	[300037] = dataList[392],
	[300038] = dataList[393],
	[300039] = dataList[394],
	[300040] = dataList[395],
	[300041] = dataList[396],
	[300042] = dataList[397],
	[300043] = dataList[398],
	[300044] = dataList[399],
	[300045] = dataList[400],
	[300046] = dataList[401],
	[300047] = dataList[402],
	[300048] = dataList[403],
	[300049] = dataList[404],
	[300050] = dataList[405],
	[300051] = dataList[406],
	[300052] = dataList[407],
	[300053] = dataList[408],
	[300054] = dataList[409],
	[300055] = dataList[410],
	[300056] = dataList[411],
	[300057] = dataList[412],
	[300058] = dataList[413],
	[300059] = dataList[414],
	[300060] = dataList[415],
	[300061] = dataList[416],
	[300062] = dataList[417],
	[300063] = dataList[418],
	[300064] = dataList[419],
	[300065] = dataList[420],
	[310001] = dataList[421],
	[310002] = dataList[422],
	[310003] = dataList[423],
	[310004] = dataList[424],
	[310005] = dataList[425],
	[310006] = dataList[426],
	[310007] = dataList[427],
	[310008] = dataList[428],
	[310009] = dataList[429],
	[310010] = dataList[430],
	[320001] = dataList[431],
	[320002] = dataList[432],
	[320003] = dataList[433],
	[320004] = dataList[434],
	[320005] = dataList[435],
	[320006] = dataList[436],
	[320007] = dataList[437],
	[320008] = dataList[438],
	[320009] = dataList[439],
	[320010] = dataList[440],
	[320011] = dataList[441],
	[320012] = dataList[442],
	[320013] = dataList[443],
	[330001] = dataList[444],
	[330002] = dataList[445],
	[330003] = dataList[446],
	[330004] = dataList[447],
	[330005] = dataList[448],
	[330006] = dataList[449],
	[40001] = dataList[450],
	[40002] = dataList[451],
	[40003] = dataList[452],
	[40004] = dataList[453],
	[40005] = dataList[454],
	[40006] = dataList[455],
	[40007] = dataList[456],
	[40008] = dataList[457],
	[40009] = dataList[458],
	[40010] = dataList[459],
	[40011] = dataList[460],
	[40012] = dataList[461],
	[40013] = dataList[462],
	[40014] = dataList[463],
	[40015] = dataList[464],
	[40016] = dataList[465],
	[40017] = dataList[466],
	[40018] = dataList[467],
	[40019] = dataList[468],
	[40020] = dataList[469],
	[40021] = dataList[470],
	[40022] = dataList[471],
	[40023] = dataList[472],
	[40024] = dataList[473],
	[40025] = dataList[474],
	[40026] = dataList[475],
	[40027] = dataList[476],
	[40028] = dataList[477],
	[40029] = dataList[478],
	[40030] = dataList[479],
	[40031] = dataList[480],
	[40032] = dataList[481],
	[40033] = dataList[482],
	[40034] = dataList[483],
	[40035] = dataList[484],
	[40036] = dataList[485],
	[40037] = dataList[486],
	[40038] = dataList[487],
	[40039] = dataList[488],
	[40040] = dataList[489],
	[40041] = dataList[490],
	[40042] = dataList[491],
	[40043] = dataList[492],
	[40044] = dataList[493],
	[40045] = dataList[494],
	[40046] = dataList[495],
	[40049] = dataList[496],
	[40050] = dataList[497],
	[12003362] = dataList[498],
	[12003363] = dataList[499],
	[12003364] = dataList[500],
	[12003365] = dataList[501],
	[12003366] = dataList[502],
	[12003367] = dataList[503],
	[40047] = dataList[504],
	[12003994] = dataList[505],
	[40048] = dataList[506],
	[12004413] = dataList[507],
	[12004522] = dataList[508],
	[12004691] = dataList[509],
	[40051] = dataList[510],
	[12004927] = dataList[511],
	[12004925] = dataList[512],
	[50001] = dataList[513],
	[12005241] = dataList[514],
	[12005462] = dataList[515],
	[12005603] = dataList[516],
	[12005830] = dataList[517],
	[12005971] = dataList[518],
	[12006229] = dataList[519],
	[12006491] = dataList[520],
	[12006813] = dataList[521],
	[12007471] = dataList[522],
}

t_pose.dataList = dataList
local mt
if PoseDefine then
	mt = {
		__cname =  "PoseDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or PoseDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_pose