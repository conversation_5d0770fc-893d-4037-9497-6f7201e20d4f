-- {excel:261星际大赛2预热.xlsx, sheetName:export_星际大赛预热杂项}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_activity261_common", package.seeall)

local title = {activityId=1,key=2,value=3}

local dataList = {
	{1452, "ExternalShareId", "66"},
	{1452, "SelectTeamReward", "26010092:1"},
	{1452, "RandomTeamReward", "26010093:1"},
	{1452, "EndShareTime", "4"},
	{1452, "ExternalShareReward", "1#3:50;2#16000054:1;4#16000352:10;6#2:50"},
	{1452, "SignUpHelpTitle", "活动规则"},
	{1452, "SignUpHelpContent", "萤火之光，照亮宇宙！第109届星际大赛将于8月10日盛大开启！\r\n\r\n<color=#e38534>大赛宣传：</color>\r\n<color=#a5967c>1.8月3日-8月9日期间，小奥比们可以对外分享星际大赛宣传图。\r\n2.当分享次数达到指定的次数即可领取丰厚奖励。\r\n3.温馨提示：每日仅首次分享会记录次数，当天内重复分享不会再记录哦。</color>\r\n\r\n<color=#e38534>大赛报名：</color>\r\n<color=#a5967c>1.8月6日-8月9日期间，星际大赛将提前开启报名通道，小奥比们可通过“随机报名”或“自选报名”两种方式的任一方式进行报名。\r\n2.随机报名：\r\n　①报名通道开启期间，若无心仪的队伍，小奥比们可通过随机报名的方式随机加入一个队伍。\r\n　②通过随机报名的方式成功加入队伍的奥比可获得称号“天选我爱109”。\r\n3.自选报名：\r\n　①每日10点、20点，各个队伍均会放出一部分自选加入队伍的名额；\r\n　②当心仪的队伍有剩余名额时，奥比即可通过自选报名加入该队伍；\r\n　③温馨提示：名额有限，先到先得；\r\n　④通过自选报名的方式成功加入队伍的奥比可获得称号“我选我爱109”。\r\n4.8月10日，大赛正式开启后，“自选报名”方式将会关闭，但“随机报名”的方式将会保留，还未加入队伍的奥比仍可通过随机报名加入队伍并获得称号“天选我爱109”。</color>"},
	{1452, "LeftCountRate", "1.5"},
	{2006, "ExternalShareId", "122"},
	{2006, "SelectTeamReward", "26010177:1"},
	{2006, "RandomTeamReward", "26010178:1"},
	{2006, "EndShareTime", "4"},
	{2006, "ExternalShareReward", "1#16000598:4;2#16000599:3;4#16000600:2;6#2:50"},
	{2006, "SignUpHelpTitle", "活动规则"},
	{2006, "SignUpHelpContent", "灵魂共鸣，点亮世界！第110届星际大赛将于8月2日盛大开启！\r\n\r\n<color=#ad775d>【大赛宣传】</color>\r\n<color=#a89e8c>1.7月25日-8月1日期间，小奥比们可以对外分享星际大赛宣传图。\r\n2.当分享次数达到指定的次数即可领取丰厚奖励。\r\n3.温馨提示：每日仅首次分享会记录次数，当天内重复分享不会再记录哦。</color>\r\n\r\n<color=#ad775d>【大赛报名】</color>\r\n<color=#a89e8c>1.7月28日-8月1日期间，星际大赛将提前开启报名通道，小奥比们可通过“随机报名”或“自选报名”两种方式的任一方式进行报名。\r\n2.随机报名：\r\n　①报名通道开启期间，若无心仪的队伍，小奥比们可通过随机报名的方式随机加入一个队伍。\r\n　②通过随机报名的方式成功加入队伍的奥比可获得称号“天选之子来助力”。\r\n3.自选报名：\r\n　①每日10点、20点，各个队伍均会放出一部分自选加入队伍的名额；\r\n　②当心仪的队伍有剩余名额时，奥比即可通过自选报名加入该队伍；\r\n　③温馨提示：名额有限，先到先得；\r\n　④通过自选报名的方式成功加入队伍的奥比可获得称号“心仪队伍一起冲”。\r\n4.8月2日，大赛正式开启后，“自选报名”方式将会关闭，但“随机报名”的方式将会保留，还未加入队伍的奥比仍可通过随机报名加入队伍并获得称号“天选之子来助力”。</color>"},
	{2006, "LeftCountRate", "1.5"},
	{2498, "ExternalShareId", "147"},
	{2498, "SelectTeamReward", "26010267:1"},
	{2498, "RandomTeamReward", "26010259:1"},
	{2498, "EndShareTime", "3"},
	{2498, "ExternalShareReward", "1#16000787:4;2#16000788:3;4#16000789:2;6#2:50"},
	{2498, "SignUpHelpTitle", "活动规则"},
	{2498, "SignUpHelpContent", "乘风破浪，盗梦星海！第111届星际大赛将于7月31日盛大开启！\r\n\r\n<color=#ad775d>【大赛宣传】</color>\r\n<color=#a89e8c>1.7月24日-7月30日期间，小奥比们可以对外分享星际大赛宣传图。\r\n2.当分享次数达到指定的次数即可领取丰厚奖励。\r\n3.温馨提示：每日仅首次分享会记录次数，当天内重复分享不会再记录哦。</color>\r\n\r\n<color=#ad775d>【大赛报名】</color>\r\n<color=#a89e8c>1.7月26日-7月30日期间，星际大赛将提前开启报名通道，小奥比们可通过“随机报名”或“自选报名”两种方式的任一方式进行报名。\r\n2.随机报名：\r\n　①报名通道开启期间，若无心仪的队伍，小奥比们可通过随机报名的方式随机加入一个队伍。\r\n　②通过随机报名的方式成功加入队伍的奥比可获得称号“天选之队一定赢”。\r\n3.自选报名：\r\n　①每日10点、20点，各个队伍均会放出一部分自选加入队伍的名额；\r\n　②当心仪的队伍有剩余名额时，奥比即可通过自选报名加入该队伍；\r\n　③温馨提示：名额有限，先到先得；\r\n　④通过自选报名的方式成功加入队伍的奥比可获得称号“我队由我不由天”。\r\n4.8月2日，大赛正式开启后，“自选报名”方式将会关闭，但“随机报名”的方式将会保留，还未加入队伍的奥比仍可通过随机报名加入队伍并获得称号“天选之队一定赢”。</color>"},
	{2498, "LeftCountRate", "1.5"},
}

local t_activity261_common = {
	[1452] = {
		["ExternalShareId"] = dataList[1],
		["SelectTeamReward"] = dataList[2],
		["RandomTeamReward"] = dataList[3],
		["EndShareTime"] = dataList[4],
		["ExternalShareReward"] = dataList[5],
		["SignUpHelpTitle"] = dataList[6],
		["SignUpHelpContent"] = dataList[7],
		["LeftCountRate"] = dataList[8],
	},
	[2006] = {
		["ExternalShareId"] = dataList[9],
		["SelectTeamReward"] = dataList[10],
		["RandomTeamReward"] = dataList[11],
		["EndShareTime"] = dataList[12],
		["ExternalShareReward"] = dataList[13],
		["SignUpHelpTitle"] = dataList[14],
		["SignUpHelpContent"] = dataList[15],
		["LeftCountRate"] = dataList[16],
	},
	[2498] = {
		["ExternalShareId"] = dataList[17],
		["SelectTeamReward"] = dataList[18],
		["RandomTeamReward"] = dataList[19],
		["EndShareTime"] = dataList[20],
		["ExternalShareReward"] = dataList[21],
		["SignUpHelpTitle"] = dataList[22],
		["SignUpHelpContent"] = dataList[23],
		["LeftCountRate"] = dataList[24],
	},
}

t_activity261_common.dataList = dataList
local mt
if Act261CommonDefine then
	mt = {
		__cname =  "Act261CommonDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act261CommonDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_activity261_common