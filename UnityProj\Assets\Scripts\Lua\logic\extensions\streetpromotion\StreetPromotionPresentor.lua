module("logic.extensions.streetpromotion.StreetPromotionPresentor",package.seeall)

local StreetPromotionPresentor = class("StreetPromotionPresentor", ViewPresentor)

--- 配置view需要的资源列表
function StreetPromotionPresentor:dependWhatResources()
	return {"ui/anniversary/anniversaryact/streetproview.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function StreetPromotionPresentor:buildViews()
	return {
        StreetPromotionView.New(),
    }
end

--- 配置view所在的ui层
function StreetPromotionPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end

return StreetPromotionPresentor