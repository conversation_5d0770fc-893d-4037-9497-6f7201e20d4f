module("logic.extensions.activity.flyingchess.view.FlyingChessResultView", package.seeall)

local FlyingChessResultView = class("FlyingChessResultView", GameRoomResultPanel_base)

function FlyingChessResultView:ctor()
    FlyingChessResultView.super.ctor(self)
end

function FlyingChessResultView:buildUI()
    FlyingChessResultView.super.buildUI(self)
    self._txtQuickBtn = self:getText("btnList/btnQuick/txtBtn")
end

function FlyingChessResultView:onEnter()
    FlyingChessResultView.super.onEnter(self)
    local roomType = GameCentreModel.instance:getRoomCreateType()
    if roomType == GameEnum.GameRoomCreateType.CUSTOM then
        self._txtQuickBtn.text = lang("退出房间")
    else
        self._txtQuickBtn.text = lang("退出游戏")
    end
end

function FlyingChessResultView:refreshPlayerItems()
    if self._cellList ~= nil then
        for k,v in ipairs(self._cellList) do
            v:dispose()
        end
        self._cellList = nil
    end

    self._cellList = {}
    local playerDatalist = FlyingChessGameModel.instance.playerDataList
    local len = #playerDatalist
    for i=1, 4 do
        local go = self:getGo("playerlist/pumpturtleresultitem_"..i)
        goutil.setActive(go, i <= len)
        if i <= len then
            local cell = PjAobi.LuaComponent.GetOrAdd(go, FlyingChessResultCell)
            cell:initItemData(self, playerDatalist[i], i)
            table.insert(self._cellList, cell)
        end
    end
end

function FlyingChessResultView:onExit()
    FlyingChessResultView.super.onExit(self)
    if self._cellList ~= nil then
        for k,v in ipairs(self._cellList) do
            v:dispose()
        end
        self._cellList = nil
    end
end

function FlyingChessResultView:onClickBtnBackRoom()
    self:close()
	FlyingChessGameController.instance:backRoom()
end

function FlyingChessResultView:onClickBtnQuick()
    self:close()
    FlyingChessGameController.instance:tryExitGame()
end

function FlyingChessResultView:onClickBtnAgain()
    self:close()
    FlyingChessGameController.instance:tryExitGame(true)
end

return FlyingChessResultView