module("logic.extensions.scene.view.mountpanel.MountItemListCell", package.seeall)
local MountItemListCell = class("MountItemListCell", ListBinderCell)
function MountItemListCell:Awake()
	self.icon = goutil.findChild(self._go, "icon")
	goutil.findChild(self._go, "shareNum"):SetActive(false)
	goutil.findChild(self._go, "itemNum"):SetActive(false)
	self.txtName = goutil.findChildTextComponent(self._go, "txtName")	
	local btnIcon = Framework.ButtonAdapter.Get(self._go)
	btnIcon:AddClickListener(self.onClick, self)
	self.btnClose = goutil.findChild(self._go, "btnClose")
    Framework.ButtonAdapter.Get(self.btnClose):AddClickListener(self.onClick, self)
	self.imgUsing = goutil.findChild(self._go, "imgUsing")
	self.imgUsing:SetActive(false)
	self.redPoint = goutil.findChild(self._go, "redPoint")
end

function MountItemListCell:onSetMo(mo)
	self.mo = mo
    self.btnClose:SetActive(false)
	self.imgUsing:SetActive(false)
	self._isUsing = false
	if mo ~= nil then
		self.txtName.text = ItemService.instance:getName(mo.id)
		IconLoader.setIconForItem(self.icon, mo.id)
		if self._listView.isOnLand then
			local mountSaveId = SeaModel.instance:getLandMountSaveId()
			self._isUsing = mountSaveId == mo.id
		else
			local mountSaveId = SeaModel.instance:getMountSaveId()
			self._isUsing = mountSaveId == mo.id
		end
		self.imgUsing:SetActive(self._isUsing)
		local redpointKey
		if self._listView.isOnLand then
			redpointKey = "HudComp_Mount_ItemChange_Land"
		else
			redpointKey = "HudComp_Mount_ItemChange"
		end	
		local state = RedPointController.instance:getNodeByName(redpointKey):isExist()
		local hasRed = false
		if state == true then
			local intParams = RedPointController.instance:getNodeByName(redpointKey):getNumberParams()
			if intParams ~= nil then
				for i = 1, #intParams do
					if self.mo.id == intParams[i] then
						hasRed = true
						break
					end
				end
			end
		end
		self.redPoint:SetActive(hasRed)
	end
end
function MountItemListCell:onClick()
	self._listView:onClickIcon(self.mo,not self._isUsing)

		local saveKey, redpointKey
	if self._listView.isOnLand then
		saveKey = StorageKey.MountItemChange_Land
		redpointKey = "HudComp_Mount_ItemChange_Land"
	else
		saveKey = StorageKey.MountItemChange
		redpointKey = "HudComp_Mount_ItemChange"
	end
	local state = RedPointController.instance:getNodeByName(redpointKey):isExist()
	if state == true then
		goutil.setActive(self.redPoint, false)
		local itemIds = {}
		local intParams = RedPointController.instance:getNodeByName(redpointKey):getNumberParams()
		for i = 1, #intParams do
			if intParams[i] ~= self.mo.id then
				table.insert(itemIds,intParams[i])
			end
		end

		local str = table.concat(itemIds, ",")
		RedPointController.instance:getNodeByName(redpointKey):setNumberParams(itemIds)
		LocalStorage.instance:setValue(saveKey,str)
		if(#itemIds <= 0) then
			RedPointController.instance:setRedIsExist(redpointKey,false)
		end
	end
end

function MountItemListCell:OnDisable()
	IconLoader.clearImage(self.icon)
end
return MountItemListCell 