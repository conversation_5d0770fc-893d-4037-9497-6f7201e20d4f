-- {excel:J交互式物品配置.xlsx, sheetName:export_表情配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_expression", package.seeall)

local title = {id=1,subType=2,name=3,quality=4,desc=5}

local dataList = {
	{20000001, 1, "动作(吓你一跳)", 4, "获得道具后，可解锁动作。"},
	{20000002, 1, "动作(欢呼)", 4, "获得道具后，可解锁动作。"},
	{20000003, 1, "动作(大笑)", 4, "获得道具后，可解锁动作。"},
	{20000004, 1, "动作(大哭)", 4, "获得道具后，可解锁动作。"},
	{20000005, 1, "动作(小拳拳)", 4, "获得道具后，可解锁动作。"},
	{20000006, 1, "动作(求求)", 4, "获得道具后，可解锁动作。"},
	{20000007, 1, "动作(开心跳起)", 4, "获得道具后，可解锁动作。"},
	{20000008, 1, "动作(生气跺脚)", 4, "获得道具后，可解锁动作。"},
	{20000009, 1, "动作(敬礼)", 4, "获得道具后，可解锁动作。"},
	{20000010, 1, "动作(疑问)", 4, "获得道具后，可解锁动作。"},
	{20000011, 1, "动作(飞吻)", 4, "获得道具后，可解锁动作。"},
	{20000012, 1, "动作(嫌弃)", 4, "获得道具后，可解锁动作。"},
	{20000013, 1, "动作(嘿嘿)", 4, "获得道具后，可解锁动作。"},
	{20000014, 1, "动作(嘚瑟)", 4, "获得道具后，可解锁动作。"},
	{20000015, 1, "动作(加油)", 4, "获得道具后，可解锁动作。"},
	{20000016, 1, "动作(眨眼)", 4, "获得道具后，可解锁动作。"},
	{20000017, 1, "动作(吃瓜)", 4, "获得道具后，可解锁动作。"},
	{20000018, 1, "动作(滑冰)", 4, "获得道具后，可解锁动作。"},
	{20000019, 1, "动作(闪亮登场)", 4, "获得道具后，可解锁动作。"},
	{20000020, 1, "动作(魔术)", 4, "获得道具后，可解锁动作。"},
	{20000021, 1, "动作(许愿)", 4, "获得道具后，可解锁动作。"},
	{20000022, 1, "动作(叉腰)", 4, "获得道具后，可解锁动作。"},
	{20000023, 1, "动作(催眠)", 4, "获得道具后，可解锁动作。"},
	{20000024, 1, "动作(躺躺)", 4, "获得道具后，可解锁动作。"},
	{20000025, 1, "动作(举手)", 4, "获得道具后，可解锁动作。"},
	{20000026, 1, "动作(惊呆)", 4, "获得道具后，可解锁动作。"},
	{20000027, 1, "动作(扭扭)", 4, "获得道具后，可解锁动作。"},
	{20000028, 1, "动作(害羞)", 4, "获得道具后，可解锁动作。"},
	{20000029, 1, "动作(手舞足蹈)", 4, "获得道具后，可解锁动作。"},
	{20000030, 1, "动作(摊手)", 4, "获得道具后，可解锁动作。"},
	{20000031, 1, "动作(晚安)", 4, "获得道具后，可解锁动作。"},
	{20000032, 1, "动作(自闭)", 4, "获得道具后，可解锁动作。"},
	{20000033, 1, "动作(拒绝)", 4, "获得道具后，可解锁动作。"},
	{20000034, 1, "动作(码字)", 4, "获得道具后，可解锁动作。"},
	{20000035, 1, "动作(恭喜发财)", 4, "获得道具后，可解锁动作。"},
	{20000036, 1, "动作(谢谢老板)", 4, "获得道具后，可解锁动作。"},
	{20000037, 1, "动作(灵动身姿)", 4, "获得道具后，可解锁动作。"},
	{20000038, 1, "动作(万花筒舞步)", 4, "获得道具后，可解锁动作。"},
	{20000039, 1, "动作(完美时光)", 4, "获得道具后，可解锁动作。"},
	{20000040, 1, "动作(神秘缠绕)", 4, "获得道具后，可解锁动作。"},
	{20000041, 1, "动作(飘逸身姿)", 4, "获得道具后，可解锁动作。"},
	{20000042, 1, "动作(狂热跳跃)", 4, "获得道具后，可解锁动作。"},
	{20000043, 1, "动作(踢踏舞步)", 4, "获得道具后，可解锁动作。"},
	{20000044, 1, "动作(飞翔舞步)", 4, "获得道具后，可解锁动作。"},
	{20000045, 1, "动作(落英舞步)", 4, "获得道具后，可解锁动作。"},
	{20000046, 1, "动作(街区舞步)", 4, "获得道具后，可解锁动作。"},
	{20000047, 1, "动作(激情狂舞)", 4, "获得道具后，可解锁动作。"},
	{20000048, 1, "动作(刺激浪漫)", 4, "获得道具后，可解锁动作。"},
	{20000049, 1, "动作(轻盈旋律)", 4, "获得道具后，可解锁动作。"},
	{20000050, 1, "动作(荧光挥舞)", 4, "获得道具后，可解锁动作。"},
	{20000051, 1, "动作(牵线木偶)", 4, "获得道具后，可解锁动作。"},
	{20000052, 1, "动作(熊娜丽莎)", 4, "获得道具后，可解锁动作。"},
	{20000053, 1, "动作(呐喊)", 4, "获得道具后，可解锁动作。"},
	{20000054, 1, "动作(思考者)", 4, "获得道具后，可解锁动作。"},
	{20000055, 1, "动作(观察)", 4, "获得道具后，可解锁动作。"},
	{20000056, 1, "动作(奥丁飞熊)", 4, "获得道具后，可解锁动作。"},
	{20000057, 1, "动作(月灵飘飘)", 4, "获得道具后，可解锁动作。"},
	{20000058, 1, "动作(撒糖)", 4, "获得道具后，可解锁动作。"},
	{20000059, 1, "动作(本熊知道了)", 4, "获得道具后，可解锁动作。"},
	{20000060, 1, "动作(撒红包)", 4, "获得道具后，可解锁动作。"},
	{20000061, 1, "动作(奥比摇)", 4, "获得道具后，可解锁动作。"},
	{20000062, 1, "动作(叼玫瑰登场)", 4, "获得道具后，可解锁动作。"},
	{20000063, 1, "动作(律动踢踏)", 4, "获得道具后，可解锁动作。"},
	{20000064, 1, "动作(旋律扭动)", 4, "获得道具后，可解锁动作。"},
	{20000065, 1, "动作(舞动全场)", 4, "获得道具后，可解锁动作。"},
	{20000066, 1, "动作(飞扬元气)", 4, "获得道具后，可解锁动作。"},
	{20000067, 1, "动作(热烈跳跃舞)", 4, "获得道具后，可解锁动作。"},
	{20000068, 1, "动作(左右灵动步)", 4, "获得道具后，可解锁动作。"},
	{20000069, 1, "动作(原地圆圈舞)", 4, "获得道具后，可解锁动作。"},
	{20000070, 1, "动作(蝴蝶太空步)", 4, "获得道具后，可解锁动作。"},
	{20000071, 1, "动作(飞舞纱巾)", 4, "获得道具后，可解锁动作。"},
	{20000072, 1, "动作(打碟时刻)", 4, "获得道具后，可解锁动作。"},
	{20000073, 1, "动作(方块顶顶乐)", 4, "获得道具后，可解锁动作。"},
	{20000074, 1, "动作(吹气球)", 4, "获得道具后，可解锁动作。"},
	{20000075, 1, "动作(惊吓手电)", 4, "获得道具后，可解锁动作。"},
	{20000076, 1, "动作(智慧之光)", 4, "获得道具后，可解锁动作。"},
	{20000077, 1, "动作(小魔仙变身)", 4, "获得道具后，可解锁动作。"},
	{20000078, 1, "动作(海草海草)", 4, "获得道具后，可解锁动作。"},
	{20000079, 1, "动作(奋笔疾书)", 4, "获得道具后，可解锁动作。"},
	{20000080, 1, "动作(登上颁奖台)", 4, "获得道具后，可解锁动作。"},
	{20000081, 1, "动作(云里偷闲）", 4, "获得道具后，可解锁动作。"},
	{20000082, 1, "动作(你敢应吗）", 4, "获得道具后，可解锁动作。"},
	{20000083, 1, "动作(爱心发射)", 4, "获得道具后，可解锁动作。"},
	{20000084, 1, "动作(花式摔炮）", 4, "获得道具后，可解锁动作。"},
	{20000085, 1, "动作(捧脸卖萌）", 4, "获得道具后，可解锁动作。"},
	{20000086, 1, "动作(钻石之心）", 4, "获得道具后，可解锁动作。"},
	{20000087, 1, "动作(食咗饭未）", 4, "获得道具后，可解锁动作。"},
	{20000088, 1, "动作(我装的）", 4, "获得道具后，可解锁动作。"},
	{20000089, 1, "动作(滑个步)", 4, "获得道具后，可解锁动作。"},
	{20000090, 1, "动作(放纸鸢）", 4, "获得道具后，可解锁动作。"},
	{20000091, 1, "动作(气球趴趴乐）", 4, "获得道具后，可解锁动作。"},
	{20010001, 2, "双人（友情之力）", 4, "获得道具后，可解锁动作。"},
	{20010002, 2, "双人（比心）", 4, "获得道具后，可解锁动作。"},
	{20010003, 2, "双人（擦嘴）", 4, "获得道具后，可解锁动作。"},
	{20010004, 2, "双星拢月", 4, "获得道具后，可解锁动作。"},
	{20010005, 2, "一起来饮茶", 4, "获得道具后，可解锁动作。"},
	{20010006, 2, "手捧落雪", 4, "获得道具后，可解锁动作。"},
	{20010007, 2, "送你一朵大红花", 4, "获得道具后，可解锁动作。"},
	{20010008, 2, "咔嚓合照", 4, "获得道具后，可解锁动作。"},
	{20010009, 2, "惊喜而至", 4, "获得道具后，可解锁动作。"},
	{20010010, 2, "侧耳倾听", 4, "获得道具后，可解锁动作。"},
	{20010011, 2, "恶作剧蛋糕", 4, "获得道具后，可解锁动作。"},
	{20010012, 2, "冰柠茶时光", 4, "获得道具后，可解锁动作。"},
	{20010013, 2, "为你撑伞", 4, "获得道具后，可解锁动作。"},
	{20010014, 2, "治愈时刻", 4, "获得道具后，可解锁动作。"},
	{20010015, 2, "悄悄靠近", 4, "获得道具后，可解锁动作。"},
	{20010016, 2, "贴心玩偶", 4, "获得道具后，可解锁动作。"},
	{20010017, 2, "惊喜贴贴", 4, "获得道具后，可解锁动作。"},
	{20010018, 2, "雪中浪漫", 4, "获得道具后，可解锁动作。"},
	{20010019, 2, "甜甜糖葫芦", 4, "获得道具后，可解锁动作。"},
	{20010020, 2, "春日放风筝", 4, "获得道具后，可解锁动作。"},
	{20010021, 2, "双人（公主抱）", 4, "获得道具后，可解锁动作。"},
	{20010022, 2, "桂冠传递", 4, "获得道具后，可解锁动作。"},
	{20010023, 2, "童心折纸", 4, "获得道具后，可解锁动作。"},
	{20010024, 2, "毕业合影", 4, "获得道具后，可解锁动作。"},
	{20010025, 2, "生日心愿", 4, "获得道具后，可解锁动作。"},
	{20010026, 2, "爱之烟火", 4, "获得道具后，可解锁动作。"},
	{20010027, 2, "共享甜莓", 4, "获得道具后，可解锁动作。"},
	{20010028, 2, "与君共舞", 4, "获得道具后，可解锁动作。"},
	{20010029, 2, "精彩演出", 4, "获得道具后，可解锁动作。"},
	{20010030, 2, "冬日温暖", 4, "获得道具后，可解锁动作。"},
	{20010031, 2, "携手提灯", 4, "获得道具后，可解锁动作。"},
	{20010032, 2, "爱心亲签", 4, "获得道具后，可解锁动作。"},
	{20010033, 2, "惊喜彩蛋", 4, "获得道具后，可解锁动作。"},
	{20010035, 2, "小熊爱刷牙", 4, "获得道具后，可解锁动作。"},
	{20010036, 2, "牵手手", 4, "获得道具后，可解锁动作。"},
	{20010037, 2, "浇灌心花", 4, "获得道具后，可解锁动作。"},
}

local t_expression = {
	[20000001] = dataList[1],
	[20000002] = dataList[2],
	[20000003] = dataList[3],
	[20000004] = dataList[4],
	[20000005] = dataList[5],
	[20000006] = dataList[6],
	[20000007] = dataList[7],
	[20000008] = dataList[8],
	[20000009] = dataList[9],
	[20000010] = dataList[10],
	[20000011] = dataList[11],
	[20000012] = dataList[12],
	[20000013] = dataList[13],
	[20000014] = dataList[14],
	[20000015] = dataList[15],
	[20000016] = dataList[16],
	[20000017] = dataList[17],
	[20000018] = dataList[18],
	[20000019] = dataList[19],
	[20000020] = dataList[20],
	[20000021] = dataList[21],
	[20000022] = dataList[22],
	[20000023] = dataList[23],
	[20000024] = dataList[24],
	[20000025] = dataList[25],
	[20000026] = dataList[26],
	[20000027] = dataList[27],
	[20000028] = dataList[28],
	[20000029] = dataList[29],
	[20000030] = dataList[30],
	[20000031] = dataList[31],
	[20000032] = dataList[32],
	[20000033] = dataList[33],
	[20000034] = dataList[34],
	[20000035] = dataList[35],
	[20000036] = dataList[36],
	[20000037] = dataList[37],
	[20000038] = dataList[38],
	[20000039] = dataList[39],
	[20000040] = dataList[40],
	[20000041] = dataList[41],
	[20000042] = dataList[42],
	[20000043] = dataList[43],
	[20000044] = dataList[44],
	[20000045] = dataList[45],
	[20000046] = dataList[46],
	[20000047] = dataList[47],
	[20000048] = dataList[48],
	[20000049] = dataList[49],
	[20000050] = dataList[50],
	[20000051] = dataList[51],
	[20000052] = dataList[52],
	[20000053] = dataList[53],
	[20000054] = dataList[54],
	[20000055] = dataList[55],
	[20000056] = dataList[56],
	[20000057] = dataList[57],
	[20000058] = dataList[58],
	[20000059] = dataList[59],
	[20000060] = dataList[60],
	[20000061] = dataList[61],
	[20000062] = dataList[62],
	[20000063] = dataList[63],
	[20000064] = dataList[64],
	[20000065] = dataList[65],
	[20000066] = dataList[66],
	[20000067] = dataList[67],
	[20000068] = dataList[68],
	[20000069] = dataList[69],
	[20000070] = dataList[70],
	[20000071] = dataList[71],
	[20000072] = dataList[72],
	[20000073] = dataList[73],
	[20000074] = dataList[74],
	[20000075] = dataList[75],
	[20000076] = dataList[76],
	[20000077] = dataList[77],
	[20000078] = dataList[78],
	[20000079] = dataList[79],
	[20000080] = dataList[80],
	[20000081] = dataList[81],
	[20000082] = dataList[82],
	[20000083] = dataList[83],
	[20000084] = dataList[84],
	[20000085] = dataList[85],
	[20000086] = dataList[86],
	[20000087] = dataList[87],
	[20000088] = dataList[88],
	[20000089] = dataList[89],
	[20000090] = dataList[90],
	[20000091] = dataList[91],
	[20010001] = dataList[92],
	[20010002] = dataList[93],
	[20010003] = dataList[94],
	[20010004] = dataList[95],
	[20010005] = dataList[96],
	[20010006] = dataList[97],
	[20010007] = dataList[98],
	[20010008] = dataList[99],
	[20010009] = dataList[100],
	[20010010] = dataList[101],
	[20010011] = dataList[102],
	[20010012] = dataList[103],
	[20010013] = dataList[104],
	[20010014] = dataList[105],
	[20010015] = dataList[106],
	[20010016] = dataList[107],
	[20010017] = dataList[108],
	[20010018] = dataList[109],
	[20010019] = dataList[110],
	[20010020] = dataList[111],
	[20010021] = dataList[112],
	[20010022] = dataList[113],
	[20010023] = dataList[114],
	[20010024] = dataList[115],
	[20010025] = dataList[116],
	[20010026] = dataList[117],
	[20010027] = dataList[118],
	[20010028] = dataList[119],
	[20010029] = dataList[120],
	[20010030] = dataList[121],
	[20010031] = dataList[122],
	[20010032] = dataList[123],
	[20010033] = dataList[124],
	[20010035] = dataList[125],
	[20010036] = dataList[126],
	[20010037] = dataList[127],
}

t_expression.dataList = dataList
local mt
if ExpressionDefine then
	mt = {
		__cname =  "ExpressionDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or ExpressionDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_expression