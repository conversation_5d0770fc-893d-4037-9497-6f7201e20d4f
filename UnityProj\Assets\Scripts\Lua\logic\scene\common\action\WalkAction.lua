module("logic.scene.common.action.WalkAction",package.seeall)
local WalkAction = class("WalkAction", SceneActionBase)

function WalkAction:onStart()
	self.targetPos = self.params.targetPos
	if self.targetPos == nil then
		self:finish(true)
		return
	end
	local scene = SceneManager.instance:getCurScene()
	self.userPlayer = scene:getUserPlayer()
	local px, py = self.userPlayer:getPos()
	local targetRange = self.params.targetRange or 0
	if targetRange > 0 then
		local distance = math.sqrt((self.targetPos.posX - px)^2 + (self.targetPos.posY - py)^2)
		if distance < targetRange then
			self:finish(true)
			return 
		end
	end
	local wayPoints, isFind = nil,nil
	local sceneId = SceneManager.instance:getCurScene():getSceneId()
	if sceneId == 106 then
		wayPoints, isFind = scene.mapMgr:findPath_Vector3(self.userPlayer, self.targetPos.posX, self.targetPos.posY, self.targetPos.posZ,targetRange)
	else
		wayPoints, isFind = scene.mapMgr:findPath(self.userPlayer, self.targetPos.posX, self.targetPos.posY, self.targetPos.posZ,targetRange)
	end
	if isFind then
		self.targetPos = wayPoints[#wayPoints]		
		if #wayPoints > 1 then
			self:_walk(wayPoints)
		else
			--目标点不可走，最近可走的点就是起点的情况
			-- self:finish(true)
			self:_jump(self.targetPos)
		end
	elseif self.params.canJump then
		local walkable = scene.mapMgr:isWalkable(self.targetPos.posX, self.targetPos.posY)
		local checkJumpWalkable = self.params.checkJumpWalkable == nil and true or self.params.checkJumpWalkable
		if not checkJumpWalkable or walkable then
			self:_jump(self.targetPos)
		else
			self:finish(false)
		end
	else
		self:finish(false)
	end
end

function WalkAction:_jump(targetPos)
	SceneManager.instance:getCurScene():getUserPlayer():addListener(UnitNotify.Arrive, self.onArrive, self)	
	SceneAgent.instance:sendMoveRequest({targetPos}, 1)
	self.userPlayer:teleport(targetPos.posX, targetPos.posY)
	-- self:_walk(targetPos, targetPos, {})
	-- SceneController.instance:localNotify(SceneNotify.UserStopMove)
	-- self:finish(true)
end

function WalkAction:_walk(wayPoints)
	self.userPlayer:getComponent(NameBarComp):setFindWayTips(true)
	SceneManager.instance:getCurScene():getUserPlayer():addListener(UnitNotify.Arrive, self.onArrive, self)
	SceneController.instance:localNotify(SceneNotify.UserStartMove)
	SceneController.instance:localNotify(SceneNotify.ShowMoveTarget, self.targetPos)
	SceneAgent.instance:sendMoveRequest(wayPoints, 0)
	self.userPlayer:setPath(wayPoints)
end

function WalkAction:onArrive()
	local px, py = self.userPlayer:getPos()
	self.userPlayer:removeListener(UnitNotify.Arrive, self.onArrive, self)
	if math.abs(px - self.targetPos.posX) < 0.0001  and math.abs(py - self.targetPos.posY) < 0.0001 then
		SceneController.instance:localNotify(SceneNotify.UserStopMove)
		self.userPlayer:getComponent(NameBarComp):setFindWayTips(false)
		self:finish(true)
		-- printInfo("arrive")
	end
end

-- function WalkAction:canOverlap(type)
-- 	return false
-- end

-- function WalkAction:canInterrupt(type)
-- 	return true
-- end

function WalkAction:onStop()
	-- if nextActionType ~= SceneActionType.Walk and not isDispose then
	-- 	local pos = {}
	-- 	pos.x, pos.y = userPlayer:getPos()
	-- 	SceneAgent.instance:sendMoveRequest(pos, {})
	-- end
	self.userPlayer:getComponent(NameBarComp):setFindWayTips(false)
	SceneController.instance:localNotify(SceneNotify.UserStopMove)	
	self.userPlayer:removeListener(UnitNotify.Arrive, self.onArrive, self)	
	self.userPlayer = nil
	self:finish(false)
end

-- function WalkAction:onDispose()
-- 	self.userPlayer:removeListener(UnitNotify.Arrive, self.onArrive, self)	
-- 	self.userPlayer = nil
-- end

function WalkAction:getType()
	return SceneActionType.Walk
end

return WalkAction