module("logic.scene.unit.SeaMountUnit", package.seeall)

local SeaMountUnit = class("SeaMountUnit", SceneObjUnit)

function SeaMountUnit:Awake()
	SeaMountUnit.super.Awake(self)
	if VirtualCameraMgr.instance:is3DScene() then
		self.navAgent = PjAobi.CSGameUtils.AddNavMeshAgent(self.go, 0.1)
	end

end

function SeaMountUnit:_initComponents()
	SeaMountUnit.super._initComponents(self)
	self:_addComponent("illumComp", IlluminationComp)
end

function SeaMountUnit:onSetInfo(params)
	self.config = PoseConfig.getInteractiveProp(params.mountId)
	self._enableTime = params.enableTime and not HouseModel.instance:isInHouse()
	if self._enableTime then
		self.illumComp:setParams(0.6, true)
	end
	self:setMountId(params.mountId)
	self:setOwner(params.ownerUnit)
end

function SeaMountUnit:setOwner(unit)
	local x, y, z = unit.mover:getPos()
	y = y + unit.indexId * 0.00001
	self:setPos(x, y, z)
	self:setNavEnable(true)
	self.ownerUnit = unit
end

function SeaMountUnit:_bindOwner()
	if not self.ownerUnit then return end
	
	local boneName = "zuowei1"
	local spineRoot = self.view:getInst()
	
	local boneGo = goutil.findChild(spineRoot, boneName)
	if not boneGo then
		boneGo = goutil.create(boneName, false)
		goutil.addChildToParent(boneGo, spineRoot)
	end
	if VirtualCameraMgr.instance:is3DScene() then
		self.ownerUnit:setNavEnable(false)
	end
	if self.ownerUnit.go then
		goutil.addChildToParent(self.ownerUnit.go, boneGo)
		tfutil.SetLPos(self.ownerUnit.go, 0, 0, -0.0002)
		tfutil.SetRotationX(self.ownerUnit.go, 0)
		self:enableFocus(self.ownerUnit.go, false)
		if self.config.properties.offset then
			self.collider = self.ownerUnit.go:GetComponent(typeof(UnityEngine.BoxCollider))
			if self.collider then
				self.originCenter = self.collider.center
				self.collider.center = self.originCenter + Vector3.New(self.config.properties.offset[1], self.config.properties.offset[2], 0)
			end
		end
	end
	
	PjAobi.CSGameUtils.AddSpineBoneFollower(boneGo, spineRoot, boneName, false, false, false, false)
end

function SeaMountUnit:_unbindOwner()
	if not self.ownerUnit then
		return
	end
	if not self.scene or not self.scene.stage.overlap then
		self.ownerUnit = nil
		return
	end
	if VirtualCameraMgr.instance:is3DScene() then
		self.ownerUnit:setNavEnable(true)
	end
	goutil.addChildToParent(self.ownerUnit.go, self.scene.stage.overlap)
	self:enableFocus(self.ownerUnit.go, true)
	-- if self.ownerUnit.id == UserInfo.userId then
	-- 	print("_unbindOwner")
	-- 	SeaController.instance:localNotify(SeaNotify.onMountHandlerChange, true)
	-- end
	if self.collider then
		self.collider.center = self.originCenter
	end
	self.ownerUnit = nil
end

function SeaMountUnit:setJoiner(unit)
	if unit then
		self.joinerUnit = unit
		self:_bindJoiner()
	elseif self.joinerUnit then
		self:_unbindJoiner()
	end
end

function SeaMountUnit:_bindJoiner()
	if not self.joinerUnit then
		return
	end
	local spineRoot = self.view:getInst()
	if not spineRoot then
		return
	end
	local boneName = "zuowei2"
	local boneGo = goutil.findChild(spineRoot, boneName)
	if not boneGo then
		boneGo = goutil.create(boneName, false)
		-- local follow = goutil.findChild(spineRoot, "follow")
		goutil.addChildToParent(boneGo, spineRoot)
	end
	if VirtualCameraMgr.instance:is3DScene() then
		self.joinerUnit:setNavEnable(false)
	end
	goutil.addChildToParent(self.joinerUnit.go, boneGo)
	tfutil.SetLPos(self.joinerUnit.go, 0, 0, -0.0001)
	tfutil.SetRotationX(self.joinerUnit.go, 0)
	self:enableFocus(self.joinerUnit.go, false)
	PjAobi.CSGameUtils.AddSpineBoneFollower(boneGo, spineRoot, boneName, false, false, false, false)
end

function SeaMountUnit:_unbindJoiner()
	if not self.joinerUnit then
		return
	end
	if not self.scene or not self.scene.stage.overlap then
		self.joinerUnit = nil
		return
	end
	if VirtualCameraMgr.instance:is3DScene() then
		self.joinerUnit:setNavEnable(true)
	end
	goutil.addChildToParent(self.joinerUnit.go, self.scene.stage.overlap)
	self:enableFocus(self.joinerUnit.go, true)
	self.joinerUnit = nil
end

function SeaMountUnit:setPos(x, y, z)
	print("SeaMountUnit:setPos", x, y, z)

	self:setNavEnable(false)
	-- self.posCtrl:setPos(x, y)
	local hasZ = z ~= nil
	if VirtualCameraMgr.instance:is3DScene() then
		if not hasZ then
			z = GameUtils.GetPosYOnGround(x, y)
		end
		-- tfutil.SetY(self.go, z)
		self.posCtrl:setPos(x, y, z)
		self:setNavEnable(not hasZ)
	else
		self.posCtrl:setPos(x, y, z)
	end
end

function SeaMountUnit:changePos(x, y, z)
	self._x = x
	self._y = y
	self._z = z
	if z then
		self:setNavEnable(false)	
	else
		self:setNavEnable(true)
	end
	GameUtils.setPosInScene(self.go, x, y, z)
end

function SeaMountUnit:setNavEnable(value)
	if self.navAgent then
		if self._navEnable ~= value then
			self._navEnable = value
			self.navAgent.enabled = value
		end
	end
end

function SeaMountUnit:_doSetDir()
	self.view:setDirection(self.dir)
	if self._eff and self.coon then
		tfutil.SetScaleX(self._eff, self.dir == UnitSpineDir.Left and -1 or 1)
	end
	if self._frontSkeletonAnimation then
		if not self._frontSkeletonAnimation.enabled then self._frontSkeletonAnimation.enabled = true end
		self._frontSkeletonAnimation.Skeleton.ScaleX = self.view._isFlipX and -1 or 1
		self._frontSkeletonAnimation:Update(0)
	end
end

function SeaMountUnit:setMountId(mountId)
	self.mountId = mountId
end

function SeaMountUnit:setDir(playerDir)
	local dir = playerDir % 2 == 0 and UnitSpineDir.Left or UnitSpineDir.Right
	if self.dir == dir then return end
	self.dir = dir
	self:_doSetDir()

	local cfg = PoseConfig.getInteractiveProp(self.mountId)
	local hasFlipSkin = cfg.properties.hasFlipSkin
	local hasFrontSkin = cfg.properties.hasFrontSkin
	if hasFlipSkin then
		if self.dir == UnitDirection.LeftDown  then
			skinName = SceneManager.instance:isInSea() and "sea" or "land"
			skinName = "L-" .. skinName
			if self._frontSkeletonAnimation and hasFrontSkin then
				self._frontSkeletonAnimation.initialSkinName = skinName
				self._frontSkeletonAnimation.Skeleton:SetSkin(skinName)
				self._frontSkeletonAnimation.Skeleton:SetSlotsToSetupPose()
			end
		elseif self.dir == UnitDirection.RightDown  then
			skinName = SceneManager.instance:isInSea() and "sea" or "land"
			if self._frontSkeletonAnimation and hasFrontSkin then
				self._frontSkeletonAnimation.initialSkinName = skinName
				self._frontSkeletonAnimation.Skeleton:SetSkin(skinName)
				self._frontSkeletonAnimation.Skeleton:SetSlotsToSetupPose()
			end
		end
		if self.view then
			local skeleton = self.view:getSkeletonAnimation()
			skeleton.initialSkinName = skinName
			skeleton.Skeleton:SetSkin(skinName)
			skeleton.Skeleton:SetSlotsToSetupPose()
		end
	end
end

function SeaMountUnit:onReset()
	SceneTimer:removeTimer(self.resetPos, self)
	if self._triggerGo then
		self.scene.triggerController:removeClickTrigger(self._triggerGo)
	end

	self._eff = nil

	self:_unbindOwner()
	self:_unbindJoiner()
	self.ownerUnit = nil
	self.joinerUnit = nil
	self._dispose = true
	self._moving = false
	self.frontAnim = nil
	self._frontSkeletonAnimation = nil

	local cfg = PoseConfig.getInteractiveProp(self.mountId)
	local soundId = cfg.properties.soundId
	if soundId then
		SoundManager.instance:stopEffect(tonumber(self.soundId),self.view:getInst())
	end
	SeaMountUnit.super.onReset(self)
end

function SeaMountUnit:setView(mountId, dir, onLoad, onLoadTarget)
	self._dispose = false
	
	self.dir = dir
	self.scene = SceneManager.instance:getCurScene()
	local skinName = SceneManager.instance:isInSea() and "sea" or "land"
	SeaMountUnit.super.setView(self, GameUrl.getSeaMountUrl(mountId), onLoad, onLoadTarget, skinName)
end

function SeaMountUnit:onLoaded()
	if self._dispose then return end
	-- PjAobi.CSGameUtils.SetMaterialBlock(self:getView(), "_NightFactor", self:_getNightFactor())
	self._triggerGo = goutil.findChild(self:getView(), "trigger")
	if self._triggerGo then
		-- Framework.GameObjectUtil.SetTagRecursively(self._triggerGo, self:getTag())
		if self.ownerUnit.isUser then
			self._triggerGo:SetActive(false)
		else
			self._triggerGo:SetActive(true)
			self.scene.triggerController:addClickTrigger(self._triggerGo, 1, self._onClickJoin, self)
		end
	end
	self._eff = goutil.findChild(self:getView(), "effect")
	if self._enableTime then
		self.illumComp:clearGO()
		self.illumComp:addLitGO(self.view:getInst())
	end		
	local spineRoot = self.view:getInst()
	local front = goutil.findChild(spineRoot, "front")
	if front then
		self.frontAnim = SpineAnimationHelper.New(front)
		self._frontSkeletonAnimation = front:GetComponent(SpineUnitComp.SkeletonAnimationType)

		local cfg = PoseConfig.getInteractiveProp(self.mountId)
		local hasFrontSkin = cfg.properties.hasFrontSkin
		if hasFrontSkin then
			local skinName = SceneManager.instance:isInSea() and "sea" or "land"
			self._frontSkeletonAnimation.initialSkinName = skinName
			self._frontSkeletonAnimation.Skeleton:SetSkin(skinName)
			self._frontSkeletonAnimation.Skeleton:SetSlotsToSetupPose()
		end
	else
		self.frontAnim = nil
	end

	self:_bindOwner()
	self:_bindJoiner(self.joinerUnit)
	self:_doSetDir()
	
	if self._moving then
		self:onStartMove()
	else
		self:onStopMove()
	end
end

function SeaMountUnit:onStartMove()
	self._moving = true
	self.view:play("move", true)
	if self.frontAnim then
		self.frontAnim:setAnimation("move", true, 1)
	end

	local cfg = PoseConfig.getInteractiveProp(self.mountId)
	local soundId = cfg.properties.soundId
	if soundId then
		self.soundId = tonumber(soundId)
		SoundManager.instance:playEffect(self.soundId,self.view:getInst())
	end
end

function SeaMountUnit:onStopMove()
	self._moving = false
	self.view:play("idle", true)
	if self.frontAnim then
		self.frontAnim:setAnimation("idle", true, 1)
	end

	local cfg = PoseConfig.getInteractiveProp(self.mountId)
	local soundId = cfg.properties.soundId
	if soundId then
		SoundManager.instance:stopEffect(tonumber(self.soundId),self.view:getInst())
	end
end

function SeaMountUnit:getTag()
    return UnitTag.Player
end

function SeaMountUnit:addClickPosListener(clickListener, clickTarget)
	self.clickListener = clickListener
	self.clickTarget = clickTarget
end

function SeaMountUnit:_onClickJoin()
	if self.joinerUnit then
		return
	end
	if self.clickListener then
		self.clickListener(self.clickTarget, x, y, sitPos)
	end
end

function SeaMountUnit:isMeRiding()
	if self.ownerUnit and self.ownerUnit.isUser then
		return true
	end
	if self.joinerUnit and self.joinerUnit.isUser then
		return true
	end
	return false
end

function SeaMountUnit:isMeAndFriendRiding()
	local isMeRiding = self:isMeRiding()
	if isMeRiding then
		return true
	end
	if self.ownerUnit then
		if FriendService.instance:queryIsFriend(self.ownerUnit.id) then
			return true
		end
	end
	if self.joinerUnit then
		if FriendService.instance:queryIsFriend(self.joinerUnit.id) then
			return true
		end
	end
	return false
end
function SeaMountUnit:getPos()
	if not self.ownerUnit then
		return 0, 0, 0
	end
	return self.ownerUnit:getPos()
end

function SeaMountUnit:enableFocus(go, enable)
	local comp = go:GetComponent("KeepRotationWithFocus")
	if comp then
		comp.enabled = enable
	end
	
end

function SeaMountUnit:onReconect()
	self:_bindOwner()
	self:_bindJoiner()
end

function SeaMountUnit:isMoving()
	return self.ownerUnit and self.ownerUnit:isMoving()
end

function SeaMountUnit:getOwner()
	return self.ownerUnit
end


return SeaMountUnit 