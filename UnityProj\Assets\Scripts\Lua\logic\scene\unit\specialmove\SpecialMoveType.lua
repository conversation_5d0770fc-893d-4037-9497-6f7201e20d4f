module("logic.scene.unit.specialmove.SpecialMoveType", package.seeall)
local SpecialMoveType = {}

SpecialMoveType.Jump = 2
SpecialMoveType.Trap = 3
SpecialMoveType.SnowBallHit = 4
SpecialMoveType.FlyAway = 5
SpecialMoveType.HuaYunAway = 6
SpecialMoveType.PushedHandler = 7
SpecialMoveType.PianoJumpHandler = 8
SpecialMoveType.ClimbHandler = 9
SpecialMoveType.DreamRoomCatThrow = 10
SpecialMoveType.PaperPlaneHandler = 11

local handlerMap = {
	[SpecialMoveType.Jump] = PlayerJumpHandler,
	[SpecialMoveType.Trap] = PlayerTrapHandler,
	[SpecialMoveType.SnowBallHit] = PlayerSnowBallHitHandler,
	[SpecialMoveType.FlyAway] = PlayerFlyAwayHandler,
	[SpecialMoveType.HuaYunAway] = FlowerCloudFlyHandler,
	[SpecialMoveType.PushedHandler] = PlayerPushedHandler,
	[SpecialMoveType.PianoJumpHandler] = PianoJumpHandler,
	[SpecialMoveType.ClimbHandler] = <PERSON>limb<PERSON>and<PERSON>,
	[SpecialMoveType.DreamRoomCatThrow] = DreamRoomCatThrowHandler,
	[SpecialMoveType.PaperPlaneHandler] = PaperPlaneHandler,
}

function SpecialMoveType.getHandler(type, unit)
	return handlerMap[type].New(type, unit)
end

return SpecialMoveType
