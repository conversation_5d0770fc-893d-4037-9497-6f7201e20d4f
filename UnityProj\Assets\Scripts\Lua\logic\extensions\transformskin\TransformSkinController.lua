module("logic.extensions.transformskin.TransformSkinController", package.seeall)
---@class TransformSkinController
local TransformSkinController = class("TransformSkinController", BaseController)

function TransformSkinController:onInit()
	self:registerNotify(GlobalNotify.OpenScene, self._openScene, self)
	self:registerNotify(GlobalNotify.LeaveScene, self._leaveScene, self)
	--self:registerLocalNotify(TransformSkinNotify.OnCancelOp, self.onCancelOperate, self)
	
end

function TransformSkinController:onReset()
	self:unregisterNotify(GlobalNotify.OpenScene, self._openScene, self)
	self:unregisterNotify(GlobalNotify.LeaveScene, self._leaveScene, self)
	--self:unregisterLocalNotify(TransformSkinNotify.OnCancelOp, self.onCancelOperate, self)
	
	self._itemId = nil
	self:transformSkinEnd()
end

function TransformSkinController:_openScene()
	self:_initSceneInteractAnim()
end

function TransformSkinController:_leaveScene()
	removetimer(self._loopCheckUserRemainTime,self)
	self:_disposeSceneInteractAnim()
end

------------------------------ common ------------------------------
--放置场景物
--function TransformSkinController:onOkOperate(params)
	--if params == nil then
		--return
	--end
	
--end
--取消放置场景物
--function TransformSkinController:onCancelOperate(define)

--end

------------------------------ 变身道具 ------------------------------
--开始变身
function TransformSkinController:beginTransformSkin(info)
	SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.TransformSkinPlayerAction,info,self.transformSkinEnd,true)
end
--变身成功
--propUnitId：场景变身道具的unitId
function TransformSkinController:transformSkinSuccess(propUnitId,itemId)
	local config = TransformSkinConfig.getPlacingItemByItemId(itemId)
	local unitType = TransformSkinSettingView.getUnitInfo(config.type).unitType
	local unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
	local unit = unitMgr:getUnit(unitType, propUnitId)
	if unit then
		unit:transformFinish()
	end
	self._itemId = itemId
	self:setTransformSkinState(true)
end
--变身结束
function TransformSkinController:transformSkinEnd(isEnd)
	printInfo("变身道具结束")
	local itemId = TransformSkinController.instance._itemId
	TransformSkinController.instance:setUserCD(itemId)
	TransformSkinController.instance:setTransformSkinState(false)
end
--取消变身
function TransformSkinController:cancelTransformSkin()
	SceneController.instance:stopAction(SceneActionType.TransformSkinPlayerAction,nil)
end

--设置变身状态
function TransformSkinController:setTransformSkinState(state)
	self._atTransformSkinState = state
	if state then
		self:setUserRemainTime(self._itemId)
	else
		removetimer(self._loopCheckUserRemainTime,self)
	end
	TransformSkinController.instance:localNotify(TransformSkinNotify.transformSkinStateChange,state)
end
--获取变身状态
function TransformSkinController:getTransformSkinState()
	return self._atTransformSkinState
end

--随机一个皮肤名字
function TransformSkinController:returnRandomSkinName(itemId)
	local config = TransformSkinConfig.getTransformSkinByItemId(itemId)
	local skinName = config.playerSkinName
	if skinName ~= nil and skinName ~= "" then
		return skinName
	end
	local prefix = config.playerSkinPrefix
	if prefix ~= nil and prefix ~= "" then
		local count = config.playerSkinsCount
		local index = math.random(1,count)
		skinName = string.format(prefix,index)
		return skinName
	end
	return nil
end

function TransformSkinController:returnRandomSkinIndex(itemId)
	local config = TransformSkinConfig.getTransformSkinByItemId(itemId)
	local playerSkinUrl = config.playerSkinUrl
	if playerSkinUrl and #playerSkinUrl > 0 then 
		return math.random(1,#playerSkinUrl)
	end
	return nil
end

--设置变身剩余时间
function TransformSkinController:setUserRemainTime(itemId)
	if itemId == nil then
		return
	end
	local config = TransformSkinConfig.getTransformSkinByItemId(itemId)
	self._totalRemainTime = config.tContinueTime + 200
	self._curRemainTime = config.tContinueTime + 200
	removetimer(self._loopCheckUserRemainTime,self)
	settimer(0,self._loopCheckUserRemainTime,self,true)
end
--检测变身持续时间
function TransformSkinController:_loopCheckUserRemainTime()
	if self._longActDelay then 
		self._longActDelay = self._longActDelay - Time.deltaTime
		if self._longActDelay <= 0 then
			self._longActDelay = nil
			TransformSkinController.instance:localNotify(TransformSkinNotify.playLongStateAnim,false)
		end
	end

	if self._curRemainTime == nil or self._curRemainTime <= 0 then
		removetimer(self._loopCheckUserRemainTime,self)
		self:cancelTransformSkin()
		return
	end
	self._curRemainTime = self._curRemainTime - Time.deltaTime
end
--获取变身剩余时间比例
function TransformSkinController:getTransformSkinRemainTimeRadio()
	if not self._atTransformSkinState then
		return 0
	end
	if self._curRemainTime == nil or self._curRemainTime <= 0 then
		return 0
	end
	return self._curRemainTime/self._totalRemainTime
end
--获取变身剩余时间
function TransformSkinController:getTransformSkinRemainTime()
	return self._curRemainTime
end

--设置变身CD
function TransformSkinController:setUserCD(itemId)
	if itemId == nil then
		removetimer(self.loopUserCD,self)
		return
	end
	local config = TransformSkinConfig.getTransformSkinByItemId(itemId)
	self._cdTime = config.tCDTime
	removetimer(self.loopUserCD,self)
	settimer(1,self.loopUserCD,self,true)
end
--获取变身cd
function TransformSkinController:getUserCD()
	return self._cdTime
end
--检测变身cd
function TransformSkinController:loopUserCD()
	if self._cdTime == nil or self._cdTime <= 0 then
		removetimer(self.loopUserCD,self)
		return
	end
	self._cdTime = self._cdTime - 1
end

------------------------------ 变身道具场景互动动画 ------------------------------
--itemIds：只有以下变身道具才具有互动动画
--isNeedIdle：unit不动的情况下
--range,count：每两个unit之间距离小于一定范围且超过一定数量，即一堆unit站在一起或者相连
--delay：播放互动动画后多久回到idle状态
--TransformSkinController.interactionDic = 
--{
	--[1] = {itemIds = {[16000579]=true,[16000580]=true,[16000581]=true},range = 2,count = 3,delay = 1,isNeedIdle = true,animName = "zhakai"}
--}
function TransformSkinController:_initSceneInteractAnim()
	if not self:_isNeedCheckInteraction() then
		return
	end
	
	self._conditionCompList = {}
	self._interactionList = TransformSkinConfig.getAllTransformSkinInteract()
	for i = 1, #self._interactionList do
		local info = self._interactionList[i]
		self._unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
		self._conditionCompList[i] = TransformSkinConditionComp.New(self._interactionList[i])
	end
	
	settimer(1,self._lookCheckInteraction,self,true)
	
end
function TransformSkinController:_disposeSceneInteractAnim()
	if self._conditionCompList then
		for i = 1, #self._conditionCompList do
			self._conditionCompList[i]:onDispose()
		end
		self._conditionCompList = nil
	end
	self._transformUnitInfos = nil
	removetimer(self._lookCheckInteraction,self)
end

--是否需要检测互动动画
function TransformSkinController:_isNeedCheckInteraction()
	local configs = TransformSkinConfig.getPlacingItemByType(1)
	if #configs == 0 then
		return false
	end
	local curSceneId = SceneManager.instance:getCurSceneId()
	local sceneIdsStr = TransformSkinConfig.getCommonCfgByKey("InteractionSceneIds")
	local isNeedInteraction = sceneIdsStr == nil or sceneIdsStr == ""
	if sceneIdsStr and sceneIdsStr ~= "" then
		local sceneIds = string.split(sceneIdsStr,',')
		for i = 1, #sceneIds do
			if tonumber(sceneIds[i]) == curSceneId then
				isNeedInteraction = true
				break
			end
		end
	end
	return isNeedInteraction
end

--检测，1s一次
function TransformSkinController:_lookCheckInteraction()
	if not self._conditionCompList or #self._conditionCompList == 0 then
		return
	end
	local units = self._unitMgr:getUnits(SceneUnitType.Player)
	if #units <= 1 then
		return
	end
	local tempUnits = arrayutil.copy(units)
	for i = 1, #self._conditionCompList do
		local getSufficeUnitsDic = self._conditionCompList[i]:getSufficeUnitsDic(tempUnits)
		self:_executeInteraction(getSufficeUnitsDic,i)
	end
	self:_loopInteractionAnimCD()
end
--执行互动
function TransformSkinController:_executeInteraction(getSufficeUnitsDic,index)
	if getSufficeUnitsDic == nil then
		return
	end
	local animCd = tonumber(TransformSkinConfig.getCommonCfgByKey("InteractionAnimCDTime"))
	local delay = self._interactionList[index].delay
	local animName = self._interactionList[index].animName
	local isLoop = self._interactionList[index].isLoop
	local soundId = self._interactionList[index].soundId
	local isLongState = self._interactionList[index].isLongState

	if isLongState then
		for k, unit in pairs(getSufficeUnitsDic) do
			if unit and unit.isUser then
				self._longActDelay = delay
				TransformSkinController.instance:localNotify(TransformSkinNotify.playLongStateAnim,true)
				self:setTransformSkinUnitInfo(unit.id,nil,nil,animCd)
			end
		end
	else 
		for k, unit in pairs(getSufficeUnitsDic) do
			if unit then
				unit.customSkin:playAnimation(animName,true,true,delay)
				self:setTransformSkinUnitInfo(unit.id,nil,nil,animCd)
				if unit.isUser and soundId and soundId ~= 0 then 
					SoundManager.instance:playEffect(soundId)
				end
			end
		end
	end
end

--自己缓存一份变身玩家的数据
function TransformSkinController:getTransformSkinUnitInfo(userId)
	if self._transformUnitInfos == nil then
		return nil
	end
	return self._transformUnitInfos[userId]
end
--unit呆着不动cd
function TransformSkinController:setTransformSkinUnitInfo(userId,itemId,isInIdleCd,animCd)
	--itemId：变身道具id
	if self._transformUnitInfos == nil then
		self._transformUnitInfos = {}
	end
	if self._transformUnitInfos[userId] == nil then
		self._transformUnitInfos[userId] = {}
	end
	if itemId ~= nil then
		self._transformUnitInfos[userId].itemId = itemId
	end
	if isInIdleCd ~= nil then
		self._transformUnitInfos[userId].isInIdleCd = isInIdleCd
	end
	if animCd ~= nil then
		self._transformUnitInfos[userId].animCd = animCd
	end
end
--互动cd
function TransformSkinController:_loopInteractionAnimCD()
	if self._transformUnitInfos == nil then
		return
	end
	for userId, info in pairs(self._transformUnitInfos) do
		if info and info.animCd and info.animCd > 0 then
			info.animCd = info.animCd - 1
		end
	end
end

function TransformSkinController:removeTransformSkinUnitInfo(userId)
	if self._transformUnitInfos == nil then
		return
	end
	self._transformUnitInfos[userId] = nil
end

--移除数据
function TransformSkinController:removeTransformSkinUnitInfo(userId)
	if self._transformUnitInfos == nil then
		return
	end
	self._transformUnitInfos[userId] = nil
end

TransformSkinController.instance = TransformSkinController.New()
return TransformSkinController
