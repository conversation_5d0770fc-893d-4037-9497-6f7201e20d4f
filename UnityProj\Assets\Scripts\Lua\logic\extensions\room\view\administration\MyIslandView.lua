module("logic.extensions.room.view.administration.MyIslandView", package.seeall)

local MyIslandView = class("MyIslandView", ViewComponent)

--- view初始化时会执行，在buildUI之后
function MyIslandView:bindEvents()
	self:getBtn("btnManagement"):AddClickListener(self._onClickManagement, self)
	self:getBtn("btnclose/btnClose"):AddClickListener(self.close, self)
end

function MyIslandView:onExit()
	self:unregisterLocalNotify(RoomNotifyName.OnHouseUpgradeSuc, self._onHouseUpgrade, self)
	CommonHUDFacade.instance:showHUD("MyIsland")
	local index = self._toggle:getIndex()
	local scene = 0
	if index == 2 then
		scene = -2
	elseif index == 3 then
		scene = -3
	end
	if self.houseData:isMyHouse() and self.houseData.defaultVisitScene ~= scene then
		HouseAgent.instance:sendChangeDefaultVisitAreaRequest(scene, function()
			self.houseData.defaultVisitScene = scene
		end)
	end
	RedPointController.instance:unregisterRedPoint(self.mgrRedPoint, {"IslandManager_Upgrade"})
	RedPointController.instance:unregisterRedPoint(self.islandDiaryRedPoint, {"IslandDiaryNewPhoto"})
	RedPointController.instance:unregisterRedPoint(self.newHouseRedPoint, {"IslandManager_NewHouse"})
end

--- view初始化时会执行
function MyIslandView:buildUI()
	self.houseMoList = {}
	self.posGoList = {}
	self.uiCanvas = ViewMgr.instance:getUICanvas()
	self.uiCamera = CameraTargetMgr.instance:getUICameraTarget():getCamera()
	self.mgrRedPoint = self:getGo("btnManagement/redPoint")
	self.newHouseRedPoint = self:getGo("btnPut/redPoint")
	local posGoComp, dragTrigger, params, clickTrigger, btnGroupTrigger, reportTrigger
	
	for i = 1, 3 do
		params = {pos = i}
		posGoComp = BaseLuaComponent.New(self:getGo("houseGroup/house" .. i))
		posGoComp:getGo("normal/imgMain"):SetActive(i == 1)
		posGoComp:getGo("normal/btnGroup"):SetActive(false)
		posGoComp:getGo("normal/edit/btnDelete"):SetActive(false)
		-- posGoComp:getGo("normal/imgCanPlace"):SetActive(false)
		posGoComp:getGo("lock"):SetActive(false)
		posGoComp:getBtn("normal/btnGroup/btnUpgrade"):AddClickListener(self._onClickUpgrade, self, params)
		posGoComp:getBtn("normal/btnGroup/btnEdit"):AddClickListener(self._onClickEdit, self, params)
		posGoComp:getBtn("normal/btnGroup/btnEnter"):AddClickListener(self._onClickEnter, self, params)
		posGoComp:getBtn("normal/edit/btnModifyName"):AddClickListener(self._onClickModifyName, self, params)
		posGoComp:getBtn("normal/edit/btnDelete"):AddClickListener(self._onClickDelete, self, params)
		posGoComp:getBtn("lock"):AddClickListener(self._onClickLock, self, params)
		
		self["txtName" .. i] = posGoComp:getText("normal/name/imgMine/txtName")
		self["otherTxtName" .. i] = posGoComp:getText("normal/name/imgOther/txtName")
		
		
		dragTrigger = Framework.UIDragTrigger.Get(posGoComp._go)
		dragTrigger:AddBeginDragListener(self._onBeginDragPosGo, self, params)
		dragTrigger:AddDragListener(self._onDragPosGo, self, params)
		dragTrigger:AddEndDragListener(self._onEndDragPosGo, self, params)
		
		clickTrigger = Framework.UIClickTrigger.Get(posGoComp._go)
		clickTrigger:AddClickListener(self._onClickHouseGo, self, params)
		
		reportTrigger = Framework.UIClickTrigger.Get(goutil.findChild(posGoComp._go, "normal/name/imgOther/btnReport"))
		reportTrigger:AddClickListener(self._onReportHouse, self, params)
		
		btnGroupTrigger = Framework.UIGlobalTouchTrigger.Get(posGoComp:getGo("normal/btnGroup"))
		btnGroupTrigger:AddIgnoreTargetListener(self._hideAllHouseBtnGroup, self)
		
		table.insert(self.posGoList, posGoComp)
	end
	self:getBtn("btnIslandDiary"):AddClickListener(self._onClickIslandDiary, self)
	self.islandDiaryRedPoint = self:getGo("btnIslandDiary/redPoint")
	-- 海域
	self._txtLocation = self:getText("txtLocation")
	
	self._rightTop = self:getGo("imgGroup/rightTop")
	self._toggle = ToggleGroup.New(nil, nil, nil, handler(self._canChangeTab, self))
	self._toggle:addView(self:getGo("imgGroup/rightTop/land"))
	self._toggle:addView(self:getGo("imgGroup/rightTop/sea"))
	self._toggle:addView(self:getGo("imgGroup/rightTop/street"))
end

function MyIslandView:_canChangeTab(index, isSelect)
	if index == 2 and MyHouseData.instance:isLock(4) then
		FlyTextManager.instance:showFlyText(lang("请先解锁海底小屋"))
		return false
	end
	if index == 3 and not FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.ShareStreet, true) then
		return false
	end
	return true
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function MyIslandView:onEnter()
	CommonHUDFacade.instance:hideHUD("MyIsland")	
	self:getGo("houseGroup"):SetActive(false)
	self.userId = self:getFirstParam().userId
	self:getText("imgGroup/imgName/txt").text = HouseModel.instance:getRoleInfo().roleSimpleInfo.nickname
	self._txtLocation.text = UserConfig.getIslandRacial(HouseModel.instance:getRoleInfo().roleSimpleInfo.islandRacial).name
	if self.userId == nil then self.userId = UserInfo.userId end
	if self.userId == UserInfo.userId then
		self.houseData = MyHouseData.instance
	else
		self.houseData = MyHouseData.New(self.userId)
	end
	self:getGo("imgGroup/imgName"):SetActive(not self.houseData:isMyHouse())
	LoadingMask.instance:show()
	self.houseData:getDataFromServer(handler(self._onGetData, self), true)
	local isMyHouse = self.houseData:isMyHouse()
	self:getText("btnclose/Text").text = isMyHouse and lang("我的小岛") or(lang("TA的小岛"))
	self:getGo("btnManagement"):SetActive(isMyHouse)
	self:getGo("btnPut"):SetActive(isMyHouse)
	self:getGo("imgGroup/comfortbar"):SetActive(isMyHouse)
	if not string.nilorempty(self:getFirstParam().pointBtn) then
		GuideArrowManager.instance:addArrowAtGO(self:getGo("btnPut"))
	end
	self._rightTop:SetActive(isMyHouse)
	local select = 1
	if self.houseData.defaultVisitScene == -2 then
		select = 2
	elseif self.houseData.defaultVisitScene == -3 then
		select = 3
	end
	self._toggle:clickViews(true, select)
	self:registerLocalNotify(RoomNotifyName.OnHouseUpgradeSuc, self._onHouseUpgrade, self)
	RedPointController.instance:registerRedPoint(self.mgrRedPoint, {"IslandManager_Upgrade"})
	RedPointController.instance:registerRedPoint(self.islandDiaryRedPoint, {"IslandDiaryNewPhoto"})
	RedPointController.instance:registerRedPoint(self.newHouseRedPoint, {"IslandManager_NewHouse"})
end

function MyIslandView:_onGetData()
	LoadingMask.instance:close()
	self:getGo("houseGroup"):SetActive(true)
	self._viewPresentor.editView:setEdit(self:getFirstParam().enterEdit)
	local params = self:getFirstParam()
	params.enterEdit = false
	params.pointBtn = nil
	self._viewPresentor._openParam[1] = params
	if self.houseData:isMyHouse() and not self:_isInSeabed() then
		FuncUnlockFacade.instance:checkAndShowGuide(13)
	end
end

function MyIslandView:_isInSeabed()
	if self:getFirstParam() then
		return self:getFirstParam().seabed
	else
		local islandType = HouseModel.instance:getUserProperty(HouseUserProperty.IslandType, 0)
		if HouseModel.instance:isInHouse() then
		    islandType = RoomConfig.getShowAreaConfig(HouseModel.instance:getUserProperty(HouseUserProperty.HouseAreaId, 1)).islandType
		end
		return islandType == 1
	end
end

function MyIslandView:_resetAllHouseView()
	self.houseMoList = {}
	local posGoComp, hasHouse
	for i = 1, #self.posGoList do
		posGoComp = self.posGoList[i]
		self:_setHouseGoEnable(posGoComp, false)
		local isLock = self.houseData:isLock(self:_getAreaIndex(i))
		local hosueId = 0
		if not isLock then
			hosueId = self.houseData:getArea(self:_getAreaIndex(i)).houseId
		end
		posGoComp:getGo("normal/edit/btnModifyName"):SetActive(self.houseData:isMyHouse() and not self._isEdit and not isLock and hosueId > 0)
		posGoComp:getGo("normal/edit/btnDelete"):SetActive(self.houseData:isMyHouse() and self._isEdit and i ~= 1 and not isLock and hosueId > 0)
		posGoComp:getGo("normal/btnGroup"):SetActive(false)
		posGoComp:getGo("normal/name/imgOther"):SetActive(not self.houseData:isMyHouse() or(self._isEdit and i == 1))
		posGoComp:getGo("normal/name/imgMine"):SetActive(self.houseData:isMyHouse() and(i ~= 1 or not self._isEdit))
		posGoComp:getGo("normal/name"):SetActive(not isLock and hosueId > 0)
		posGoComp:getGo("normal/imgFood"):SetActive(false)
		local preAreaId = RoomConfig.getShowAreaConfig(self:_getAreaIndex(i)).preAreaId
		local canUnlock = preAreaId == 0 or HouseModel.instance:isAreaUnlock(preAreaId)
		posGoComp:getGo("lock"):SetActive(isLock and self.houseData:isMyHouse() and canUnlock)
		if isLock then
			self:_setHouseGo(nil, i, true)
		elseif hosueId > 0 then
			posGoComp = self.posGoList[i]
			local houseMo = self.houseData:getHouseData(hosueId)
			self:_setHouseGo(houseMo, i)
			local btnUpgrade = posGoComp:getGo("normal/btnGroup/btnUpgrade")
			GameUtils.setImgGray(btnUpgrade, houseMo:isMaxLevel(), true)
			PjAobi.CSGameUtils.SetImageAlpha(goutil.findChildImageComponent(posGoComp._go, "normal/btnGroup/btnUpgrade/Image"), houseMo:isMaxLevel() and 0.6 or 1)
			posGoComp:getGo("normal/imgFood"):SetActive(self.houseData.isAllowToEat and houseMo.hasFood and not HouseModel.instance:isMyHouse())
		else
			posGoComp:getGo("normal/imgIcon"):SetActive(false)
		end
	end
end

function MyIslandView:_getAreaIndex(index)
	if self:_isInSeabed() then
		return index + 3
	else
		return index
	end
end

function MyIslandView:_getPosIndex(areaId)
	if self:_isInSeabed() then
		return areaId - 3
	else
		return areaId
	end
end

function MyIslandView:onBeginDragHouseGo()
	for i = 1, #self.posGoList do
		if not self.houseData:isLock(i) then
			-- self.posGoList[i]:getGo("normal/imgCanPlace"):SetActive(true)
		end
	end
end

function MyIslandView:checkHitPos(pos)
	for i = 1, #self.posGoList do
		if Framework.GeometryUtil.RectContainsScreenPoint(pos, self.uiCanvas, self.posGoList[i]._go.transform, self.uiCamera) then
			return i, self.houseData:isLock(self:_getAreaIndex(i))
		end
	end
	return 0
end

function MyIslandView:onEndDragHouseGo(houseMo, position)
	local hitPos = self:checkHitPos(position)
	local hitAreaId = self:_getAreaIndex(hitPos)
	local useingAreaId = self.houseData:isUse(houseMo.houseId)
	if hitPos == 0 or hitPos == useingAreaId or self.houseData:isLock(hitAreaId) then
		if useingAreaId then
			self:_setHouseGo(houseMo, self:_getPosIndex(useingAreaId))
		end
		return
	end
	self:_replaceHouse(hitPos, houseMo.id)
end

function MyIslandView:_replaceHouse(hitPos, houseId)
	if self._lastReplaceTime ~= nil and ServerTime.now() - self._lastReplaceTime < 1 then
		FlyTextManager.instance:showFlyText(lang("你操作得太频繁了，休息一下吧~"))
		self:_resetAllHouseView()
		return false
	end
	self._lastReplaceTime = ServerTime.now()
	local areaId = self:_getAreaIndex(hitPos)
	local useingAreaId = self.houseData:isUse(houseId)
	local houseIdInArea = self.houseData:getArea(areaId).houseId
	if(useingAreaId == 1 and houseIdInArea == 0) or(hitPos == 1 and houseId == 0) then
		FlyTextManager.instance:showFlyText(lang("主岛不能卸下!"))
		self:_resetAllHouseView()
		return false
	end
	if self:_checkIsInHouse(self.houseMoList[hitPos]) and houseId == 0 then
		DialogHelper.showMsg(lang("你当前正在该小屋内，无法撤下哦~"))
	else
		if houseId == 0 or houseIdInArea == 0 then
			self:_sureToReplace(areaId, houseId)
			return
		end
		self._showTips = true
		ViewFacade.showTgNotice(5, function(isOk)
			if isOk then
				self:_sureToReplace(areaId, houseId)
			else
				self:_resetAllHouseView()
			end
			self._showTips = false
		end)
	end
	return true
end

function MyIslandView:_checkIsInHouse(houseMo)
	return houseMo and houseMo.houseId == HouseModel.instance:getUserProperty(HouseUserProperty.HouseId)
end

function MyIslandView:_sureToReplace(areaId, houseId)
	HouseAgent.instance:sendChangeHouseRequest(areaId, houseId, function(suc)
		if suc then
			self.houseData:onReplaceHouse(areaId, houseId)
			self._viewPresentor.comfortBar:_updateComfort()
			self:_resetAllHouseView()
			self._viewPresentor.editView:resetData()
		else
			self:_resetAllHouseView()
		end
	end)
end


function MyIslandView:setEdit(isEdit)
	self._isEdit = isEdit
	self:_resetAllHouseView()
end

function MyIslandView:_setHouseGo(houseMo, pos, isLock)
	self.houseMoList[pos] = houseMo
	local posGoComp = self.posGoList[pos]
	self:_setHouseGoEnable(posGoComp, not isLock)
	local icon = posGoComp:getGo("normal/imgIcon")
	posGoComp:getGo("normal/imgMain"):SetActive(pos == 1)
	icon:SetActive(true)
	if isLock then
		local houseType = RoomConfig.getHouseConfigByFurnitureId(RoomConfig.getShowAreaConfig(self:_getAreaIndex(pos)).defaultHouse).houseType
		IconLoader.setSpriteToImg(icon, GameUrl.getRoomOutsideUrl(houseType, 1))
		Framework.ColorUtil.SetImageColor(icon:GetComponent("Image"), "#9A9A9A")
	else
		IconLoader.setSpriteToImg(icon, GameUrl.getRoomOutsideUrl(houseMo.houseType, houseMo.level))
		
		SDKLeitingWordFilter.instance:wordFilter(self.houseData:getArea(self:_getAreaIndex(pos)).name, 2, self["wordFilterHandler" .. pos], self)
		Framework.ColorUtil.SetImageColor(icon:GetComponent("Image"), "#FFFFFF")
	end
end

function MyIslandView:wordFilterHandler1(filtWord)
	self.txtName1.text = filtWord
	self.otherTxtName1.text = filtWord
end

function MyIslandView:wordFilterHandler2(filtWord)
	self.txtName2.text = filtWord
	self.otherTxtName2.text = filtWord
end

function MyIslandView:wordFilterHandler3(filtWord)
	self.txtName3.text = filtWord
	self.otherTxtName3.text = filtWord
end

function MyIslandView:_setHouseGoEnable(posGoComp, enable)
	posGoComp:getGo("normal/imgMain"):SetActive(enable)
end

function MyIslandView:_onBeginDragPosGo(evt, params)
	if not self._isEdit or not self.houseMoList[params.pos] then return end
	self._viewPresentor.editView:preventHide()
	-- --主岛不能拖动
	-- if params.pos == 1 then return end
	self.dragMo = self.houseMoList[params.pos]
	self._viewPresentor.editView:_handleBeginDrag(self.dragMo, evt.position)
	self:_setHouseGoEnable(self.posGoList[params.pos], false)
	self.posGoList[params.pos]:getGo("normal/imgIcon"):SetActive(false)
	self.houseMoList[params.pos] = nil
end

function MyIslandView:_onDragPosGo(evt, params)
	if not self.dragMo then return end
	self._viewPresentor.editView:_handleDrag(self.dragMo, evt.position)
end

function MyIslandView:_onEndDragPosGo(evt, params)
	if not self.dragMo then return end
	self._viewPresentor.editView:_handleEndDrag(self.dragMo, evt.position)
	self.dragMo = nil
end

function MyIslandView:_onClickHouseGo(evt, params)
	if not self.houseData:isMyHouse() then
		self:_enterRoom(params.pos)
		return
	end
	self._viewPresentor.editView:preventHide()
	if self.houseData:isLock(self:_getAreaIndex(params.pos)) then
		FlyTextManager.instance:showFlyText(lang("解锁小岛区域可以获得更多小屋摆放位置哦~"))
	end
	if self._isEdit or not self.houseMoList[params.pos] then return end
	self:_hideAllHouseBtnGroup()
	local posGoComp = self.posGoList[params.pos]
	posGoComp:getGo("normal/btnGroup"):SetActive(true)
end

function MyIslandView:_onReportHouse(evt, params)
	local text = self["otherTxtName" .. params.pos].text or ""
	local houseMo = self.houseMoList[params.pos]
	print("MyIslandView:_onReportHouse", params.pos, text, houseMo.houseId)
	ReportFacade.instance:showReport(self.userId, HouseModel.instance:getRoleInfo().roleSimpleInfo.nickname, {30}, {text}, {{houseId = houseMo.houseId}})
end

function MyIslandView:_hideAllHouseBtnGroup()
	for i = 1, #self.posGoList do
		self.posGoList[i]:getGo("normal/btnGroup"):SetActive(false)
	end
end

function MyIslandView:_onClickUpgrade(params)
	local pos = params.pos
	local houseMo = self.houseMoList[params.pos]
	RoomFacade.instance:showHouseUpgrade(houseMo.houseId, true)
end

function MyIslandView:_onHouseUpgrade()
	self:_resetAllHouseView()
end

function MyIslandView:_onClickEdit(params)
	local houseId = self.houseMoList[params.pos].houseId
	if houseId == HouseModel.instance:getUserProperty(HouseUserProperty.HouseId) then
		RoomEditModel.instance:tryEnterEdit(true)
	else
		RoomFacade.instance:enterVirtualRoom(houseId, true)
	end
end

function MyIslandView:_onClickEnter(params)
	self:_enterRoom(params.pos)
end

function MyIslandView:_enterRoom(pos)
	local areaId = self:_getAreaIndex(pos)
	if self.houseData:isLock(areaId) or self.houseData:getArea(areaId).houseId == 0 then return end
	self:close()
	RoomFacade.instance:enterRoomScene(self.userId, self.houseData:getArea(areaId).houseId, nil, nil, nil, nil, areaId)
end

function MyIslandView:_onClickModifyName(params)
	FuncHelper.btnHandlerLock(FuncIds.HouseName, function()
		local pos = params.pos
		MyIslandEditNameView.show(self:_getAreaIndex(pos), function()
			SDKLeitingWordFilter.instance:wordFilter(self.houseData:getArea(self:_getAreaIndex(pos)).name, 2, self["wordFilterHandler" .. pos], self)
		end)
	end)
end

function MyIslandView:_onClickDelete(params)
	self._viewPresentor.editView:preventHide()
	local pos = params.pos
	self:_replaceHouse(pos, 0)
end

function MyIslandView:_onClickLock(params)
	local pos = params.pos
	local preAreaId = RoomConfig.getShowAreaConfig(self:_getAreaIndex(pos)).preAreaId
	if preAreaId ~= 0 and not HouseModel.instance:isAreaUnlock(preAreaId) then
		FlyTextManager.instance:showFlyText(lang("解锁小岛区域可以获得更多小屋摆放位置哦~"))
		return
	end
	HouseAreaUnlockView.show(self:_getAreaIndex(pos), handler(self._resetAllHouseView, self))
end

function MyIslandView:_onClickManagement()
	RoomFacade.instance:showHouseList()
end

function MyIslandView:_onClickIslandDiary()
	IslandAgent.instance:sendGetAllIslandDiaryRequest(function()
		self:close()
		ViewMgr.instance:open("IslandDiaryPanel")
	end)
end

return MyIslandView 