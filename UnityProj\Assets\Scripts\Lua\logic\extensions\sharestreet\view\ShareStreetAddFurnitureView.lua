module("logic.extensions.sharestreet.view.ShareStreetAddFurnitureView",package.seeall)

local ShareStreetAddFurnitureView = class("ShareStreetAddFurnitureView", ViewComponent)

ShareStreetAddFurnitureView.ADD_SHARE = 1
ShareStreetAddFurnitureView.VIEW_SHARE = 2

function ShareStreetAddFurnitureView:buildUI()
    self.viewShareGO = self:getGo("rightGo/btnStreetGo")
    self.btnShare = self:getBtn("rightGo/btnOk")
    self.btnShare:AddClickListener(self.onClickShare, self)
    self.closeBtn = self:getBtn("btnclose/btnClose")
    self.closeBtn:AddClickListener(self.onClickClose, self)
    self.txtShareCount = self:getText("countGo/countTxt")
    self.txtTitle = self:getText("btnclose/Text")

end

function ShareStreetAddFurnitureView:destroyUI()
end

function ShareStreetAddFurnitureView:onEnter()
    self.totalCount = tonumber(ShareStreetCfg.instance:getCommonConfigValue("maxShareFurnitureCount"))
    local type = self:getOpenParam()[1]
    if type == ShareStreetAddFurnitureView.ADD_SHARE then
        self.viewShareGO:SetActive(false)
        self.btnShare.gameObject:SetActive(true)
        self._viewPresentor.listComp:setCanShare(true)
        local items = ShareStreetFacade.instance:getCanShareFurnitures()
        self._viewPresentor.listComp:setItems(items)
        -- if #items > 0 then
        --     self._viewPresentor.listComp:selectCell(1, true)
        -- end
        self.txtTitle.text = lang("添加共享")
        self.shareCount =self:getOpenParam()[2] or 0
        self:updateShareCount()
    elseif type == ShareStreetAddFurnitureView.VIEW_SHARE then
        self.viewShareGO:SetActive(true)
        self.btnShare.gameObject:SetActive(false)
        self._viewPresentor.listComp:setCanShare(false)
        self.txtTitle.text = lang("街区共享")
        ShareStreetAgent.instance:sendGetPersonShareFurnitureCountRequest(function(list)
            self._viewPresentor.listComp:setItems(list)
            self.shareCount = 0
            for i=1, #list do
                self.shareCount = self.shareCount + list[i].num    
            end
            self:updateShareCount()
        end, self)
    end
end

function ShareStreetAddFurnitureView:onClickShare()
    local selectItems = self._viewPresentor.listComp:getShareItems()

    if #selectItems > 0 then
        local addCount = 0
        for i = 1, #selectItems do
            addCount = addCount + selectItems[i].num
        end
        if addCount + self.shareCount > self.totalCount then
            FlyTextManager.instance:showFlyText(lang("共享家具数量超出上限"))
        else

            ShareStreetAgent.instance:sendStreetShareFurnitureRequest(selectItems, function()
                self._viewPresentor.listComp:onMultiSelectChange(self._viewPresentor.listComp.isMultiSelect)
                self._viewPresentor.listComp:setItems(ShareStreetFacade.instance:getCanShareFurnitures())
                self.shareCount = self.shareCount + addCount
                self:updateShareCount()
                FlyTextManager.instance:showFlyText(lang("已共享到街区"))
            end, self)
        end
    end
end

function ShareStreetAddFurnitureView:updateShareCount()
    self.txtShareCount.text = string.format("%d/%d", self.shareCount,  self.totalCount)
end

function ShareStreetAddFurnitureView:onClickClose()
    self:close()
end


function ShareStreetAddFurnitureView:onExit()
end

return ShareStreetAddFurnitureView


