module("logic.extensions.sharestreet.view.ShareStreetInfoViewPresentor",package.seeall)
---@class ShareStreetInfoViewPresentor
local ShareStreetInfoViewPresentor = class("ShareStreetInfoViewPresentor",ViewPresentor)

function ShareStreetInfoViewPresentor:ctor()
	ShareStreetInfoViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetInfoViewPresentor:dependWhatResources()
	return {"ui/street/streetsetview.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetInfoViewPresentor:buildViews()
	return {ShareStreetInfoView.New()}
end

--- 配置view所在的ui层
function ShareStreetInfoViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetInfoViewPresentor