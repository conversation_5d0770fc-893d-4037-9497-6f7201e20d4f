module("logic.extensions.room.view.sampledetail.SamplePreview",package.seeall)

local SamplePreview = class("SamplePreview")

function SamplePreview:ctor(container)
	self._preview = RoomPreView.New(container)
	self._rect = container:GetComponent("RectTransform").rect
	print(self._rect.width, self._rect.height)
end

function SamplePreview:destroy()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnLoadHouseMesh, self._onLoadHouseMesh, self)
	self._preview:destroy()
	self._preview = nil
end

function SamplePreview:showHouse(furnitures, islandType)
    local infos = {furniture = {}, rooms = {}}
	for i, config in ipairs(furnitures) do
		table.insert(infos.furniture, {
            id = i, 
            furnitureId = config.furniture.id, 
            posX = config.furniture.x, 
            posY = config.furniture.y, 
            rotation = config.furniture.rotation, 
            floor = config.furniture.stackLayer
        })
	end
	self._loadFurniture = false
	self._loadHouse = false
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnLoadHouseMesh, self._onLoadHouseMesh, self)
	self._preview:showHouse(self:_createHouseMo(infos, islandType), self._onLoadHouse, self)
end

function SamplePreview:_onLoadHouse()
	self._loadFurniture = true
	-- self._preview:viewAllFurniture(self._rect.width, self._rect.height)
	self:_viewAll()
end

function SamplePreview:_onLoadHouseMesh(factory)
	if factory ~= self._preview.unitFactory then return end
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnLoadHouseMesh, self._onLoadHouseMesh, self)
	self._loadHouse = true
	self:_viewAll()
end

function SamplePreview:_viewAll()
	if self._loadHouse and self._loadFurniture then
		SceneTimer:setTimer(0, function()
			self._preview:viewAllFurniture(self._rect.width, self._rect.height)
		end, nil, false)
	end
end

function SamplePreview:getTexture(callBack, target)
	self._preview:getTexture(callBack, target, self._rect.width, self._rect.height)
end

function SamplePreview:_createHouseMo(info, islandType)
	local houseMo = HouseMo.New()
	info.houseType = 0
	--背景用
	info.level = 42
	if islandType then
		info.level = 42 + islandType
	end
	houseMo:setHouse(UserInfo.userId, info, RoomScene.Type.Preview)
	houseMo:getAreaModel():setHouseArea(4, 1, UserInfo.userId)
	return houseMo
end

function SamplePreview:_getFurnitureRect(furniture)
	local w, h
	if furniture.rotation == 1 then
		h, w = RoomConfig.getFurnitureSize(furniture.id)
    else
        w, h = RoomConfig.getFurnitureSize(furniture.id)
	end
	return furniture.x, furniture.y, furniture.x - w + 1, furniture.y - h + 1
end

return SamplePreview