module("logic.scene.common.interaction.dreamroom.DreamRoomInteraction",package.seeall)

local DreamRoomInteraction = class("DreamRoomInteraction",SceneInteractionBase)

function DreamRoomInteraction:begin(info, isEnterScene)
	printInfo("DreamRoomInteraction",isEnterScene)

    self._exploreGameTrigger = {}
    self._treasureGos = {}
    self._dreamRoomTriggerController = SceneManager.instance:getCurScene().triggerController
    ExploreGameController.instance:registerLocalNotify(ExploreGameNotifyName.GetActInfo, self.onGetExploreActInfo, self)
    ExploreGameController.instance:registerLocalNotify(ExploreGameNotifyName.OpenTreasure, self.onOpenTreasure, self)
    ExploreGameController.instance:registerLocalNotify(ExploreGameNotifyName.GainTreasure, self.onGainTreasure, self)

    ExploreGameController.instance:registerNotify(GlobalNotify.getActivityInfoFinish, self.setCarnivalPartyCakeState, self)
	ExploreGameController.instance:registerNotify(GlobalNotify.ActivityUpdate, self.setCarnivalPartyCakeState, self)
    
    ExploreGameController.instance:registerNotify(GlobalNotify.getActivityInfoFinish, self.setSigninBoardState, self)
	ExploreGameController.instance:registerNotify(GlobalNotify.ActivityUpdate, self.setSigninBoardState, self)
    GlobalDispatcher:addListener(GlobalNotify.OnServerRefresh, self.onServerRefresh, self)
    self:_invisableAllExploreGameTrigger()
    Act467Agent.instance:sendGetAct467InfoRequest()

    self.catHasSelf = false
    self:_setCatState()
    SceneTimer:setTimer(1, self._update, self, true)
    self:setCarnivalPartyCakeState()
    self:setSigninBoardState()
end

function DreamRoomInteraction:onStop(isLeaveScene)
    printInfo("DreamRoomInteraction",isLeaveScene)

	ExploreGameController.instance:unregisterLocalNotify(ExploreGameNotifyName.GetActInfo, self.onGetExploreActInfo, self)
    ExploreGameController.instance:unregisterLocalNotify(ExploreGameNotifyName.OpenTreasure, self.onOpenTreasure, self)
    ExploreGameController.instance:unregisterLocalNotify(ExploreGameNotifyName.GainTreasure, self.onGainTreasure, self)

    ExploreGameController.instance:unregisterNotify(GlobalNotify.getActivityInfoFinish, self.setCarnivalPartyCakeState, self)
	ExploreGameController.instance:unregisterNotify(GlobalNotify.ActivityUpdate, self.setCarnivalPartyCakeState, self)

    ExploreGameController.instance:unregisterNotify(GlobalNotify.getActivityInfoFinish, self.setSigninBoardState, self)
	ExploreGameController.instance:unregisterNotify(GlobalNotify.ActivityUpdate, self.setSigninBoardState, self)
    GlobalDispatcher:removeListener(GlobalNotify.OnServerRefresh, self.onServerRefresh, self)

    SceneTimer:removeTimer(self._update, self)
    SceneTimer:removeTimer(self._playerJump, self)
    SceneTimer:removeTimer(self._playCatBlinkAnim, self)
    SceneTimer:removeTimer(self._catBlink, self)
    SceneTimer:removeTimer(self._catAngry, self)
    if self._catAnimation then
        self._catAnimation:RemoveFinishListener()
    end
    self._catAnimation = nil
    self._catPos = nil
    self._catTriggers = nil
    self._cakeNotFinishObj = nil
    self._catEyes = nil
    self.catHasSelf = false

    self._signinBoardGos = nil
end

function DreamRoomInteraction:onGetExploreActInfo()
    printWarn("onGetExploreActInfo")

    local activeTreasure = Act467Model.instance:getActiveTreasureInfo()
    for k, v in ipairs(activeTreasure) do
        self:_showExploreGameTrigger(v.id)
    end
end

function DreamRoomInteraction:onOpenTreasure(id)
    printInfo("onOpenTreasure",id)
    if self._treasureGos[id] then 
        self._treasureGos[id].transform:GetChild(0):GetComponent("Animation"):Play()
        --克隆一个临时特效
        local effect = goutil.findChild(self._treasureGos[id],"effect") 
        local tempEffect = goutil.clone(effect, "tempEffect")
        tempEffect:SetActive(false)
        tempEffect:SetActive(true)
        goutil.addChildToParent(tempEffect,self._treasureGos[id].transform.parent.gameObject)
        -- effect.gameObject:SetActive(false)
        -- effect.gameObject:SetActive(true)

        settimer(1, function()
            if not goutil.isNil(tempEffect) then 
                tempEffect:SetActive(false)
                goutil.destroy(tempEffect)
            end
        end, nil, false)

    end
    settimer(0.5, function()
        self:_showExploreGameTrigger(id)
    end, nil, false)
end

function DreamRoomInteraction:onGainTreasure(id)
    self:_hideExploreGameTrigger(id)
end

function DreamRoomInteraction:_invisableAllExploreGameTrigger()
    printInfo("_invisableAllTrigger")

    for i=1,30 do 
        --triggerid从15开始
        local triggerId = 15 + i
        local trigger = self._dreamRoomTriggerController:getTrigger(tostring(triggerId))
        trigger.sceneIcon:setVisible(false)
        trigger.triggerGO:SetActive(false)
    end
end

function DreamRoomInteraction:_showExploreGameTrigger(id)
    printInfo("_showExploreGameTrigger",id)

    local treasureConfig = Act467Config.getTreasureConfigById(id)
    local trigger = self._dreamRoomTriggerController:getTrigger(tostring(treasureConfig.triggerId))
    if not trigger then 
        return
    end
    trigger.sceneIcon:setVisible(true)
    trigger.triggerGO:SetActive(true)

    if self._treasureGos[id] then 
        self._treasureGos[id]:SetActive(false)
        goutil.destroy(self._treasureGos[id])
        self._treasureGos[id] = nil
    end
    local goType = Act467Model.instance:getTreasureState(id) == Act467Model.TreasureState.Opened and 0 or treasureConfig.shellType
    local treasurePath = string.format("trigger/exploregameprefab/exploregametrigger%d",goType)
    local modelGo = goutil.findChild(SceneManager.instance:getCurScene().stage.stageGO, treasurePath)
    local treasureGo = goutil.clone(modelGo, "treasureGo" .. id)
    treasureGo:SetActive(true)
    self._treasureGos[id] = treasureGo
    local triggerPos = GameUtils.getPos(trigger.triggerGO)

    --printWarn(triggerPos.x, triggerPos.z,CurveSurface.Instance:GetPosYOnGround(triggerPos.x,triggerPos.z),SceneConfig.GroundOffset)

   -- local y = GameUtils.GetPosYOnGround(triggerPos.x, triggerPos.z)
    local curScene = SceneManager.instance:getCurScene()
	local sceneRoot = curScene.stage.stageGO
    --goutil.addChildToParent(sceneRoot, trigger.triggerGO)
    goutil.addChildToParent(treasureGo, trigger.triggerGO)
    GameUtils.setPos(treasureGo, triggerPos.x, triggerPos.y -0.75 + 0.5, triggerPos.z)

    --tfutil.SetY(treasureGo, y - (triggerPos.y / 2))
    
end

function DreamRoomInteraction:_hideExploreGameTrigger(id)
    local treasureConfig = Act467Config.getTreasureConfigById(id)
    local trigger = self._dreamRoomTriggerController:getTrigger(tostring(treasureConfig.triggerId))
    trigger.sceneIcon:setVisible(false)
    trigger.triggerGO:SetActive(false)
    if self._treasureGos[id] then 
        self._treasureGos[id]:SetActive(false)
        goutil.destroy(self._treasureGos[id])
        self._treasureGos[id] = nil
    end
end

function DreamRoomInteraction:onServerRefresh()
    printInfo("onServerRefresh")
    self:_invisableAllExploreGameTrigger()

    for k,v in pairs(self._treasureGos) do
        v:SetActive(false)
        goutil.destroy(v)
    end
    self._treasureGos = {}

    Act467Agent.instance:sendGetAct467InfoRequest()
end

------------------------------- 猫交互 --------------------------------
-- 获取猫交互的配置（写死在代码里）
function DreamRoomInteraction:_getCatStateConfig()
    local sleepTimeSlot = { -- 10:00~14:59,18:00~01:59
        {10,14},
        {18,24},
        {0,1},
    }
    local awakeTimeSlot = { -- 02:00~09:59,15:00~17:59
        {2,9},
        {15,17},
    }
    local cfg = {
        sleepTimeSlot = sleepTimeSlot,
        awakeTimeSlot = awakeTimeSlot,
    }
    return cfg
end
-- 猫身上最多能座几个人，达到此值猫会震开全部在身上的玩家
function DreamRoomInteraction:_getCatCanSitMaxCount()
    return 4
end
-- 获取猫跳跃的时间点（分钟）
function DreamRoomInteraction:_getCatJumpTime()
    return {1, 16, 31, 46}
end
-- 当猫跳跃时，距离猫多远的玩家会被攻击到
function DreamRoomInteraction:_getAttackRange()
    return 15
end
-- 玩家被猫弹开后的落点
function DreamRoomInteraction:_getPlayerThrowPos()
    return {
        {7.15,-16.64},
        {4.98,-15.83},
        {2.93,-16.23},
        {1.24,-16.28},
    }
end
-- 猫眨眼间隔(秒)
function DreamRoomInteraction:_getCatBlinkInterval()
    return 5
end

function DreamRoomInteraction:_init()
    if not self._catAnimation then
        local catGo = goutil.findChild(SceneManager.instance:getCurScene().stage.stageGO, "standstill/wujian/mesh_anim_mao_skin")
        self._catAnimation = Framework.AnimationAdapter.Get(catGo)
        self._catPos = GameUtils.getLocalPos(catGo)
        self._catEyes = {} -- 保存猫眼睛GameObject的table
        self._catEyes.idle = goutil.findChild(catGo, "mesh_anim_maoyan/mesh_anim_maoyan_idle")
        self._catEyes.sleep = goutil.findChild(catGo, "mesh_anim_maoyan/mesh_anim_maoyan_sleep")
        self._catEyes.awake = goutil.findChild(catGo, "mesh_anim_maoyan/mesh_anim_maoyan_awake")
        self._catEyes.miyan = goutil.findChild(catGo, "mesh_anim_maoyan/mesh_anim_maoyan_miyan") -- 眯眼
        self._catEyes.idle:SetActive(false)
        self._catEyes.sleep:SetActive(false)
        self._catEyes.awake:SetActive(false)
        self._catEyes.miyan:SetActive(false)
    end
end

function DreamRoomInteraction:_getCatAnimState()
    local cfg = self:_getCatStateConfig()
    local curHour = ServerTime.nowDateServerLook().hour
    curHour = curHour == 0 and 24 or curHour
    local curAnim = DreamRoomInteraction.catAnim.idle
    for k,v in ipairs(cfg.sleepTimeSlot) do
        if curHour >= v[1] and curHour <= v[2] then
            curAnim = DreamRoomInteraction.catAnim.sleep
        end
    end
    for k,v in ipairs(cfg.awakeTimeSlot) do
        if curHour >= v[1] and curHour <= v[2] then
            curAnim = DreamRoomInteraction.catAnim.idle
        end
    end
    return curAnim
end

DreamRoomInteraction.catAnim = {
    idle = "mesh_anim_mao_idle", -- 猫清醒状态播放的动画
    awake = "mesh_anim_mao_awake", -- 猫睡觉时的攻击动画
    sleep = "mesh_anim_mao_sleep", -- 猫睡觉状态的动画
    attack = "mesh_anim_mao_attack", -- 猫清醒时的攻击动画
}

function DreamRoomInteraction:_setCatState()
    local blinkInterval = self:_getCatBlinkInterval()
    self:_init()
    local curAnim = self:_getCatAnimState()
    if not self._lastCatAnim then
        self:_showTriggers(curAnim == DreamRoomInteraction.catAnim.sleep)
        self._catEyes.sleep:SetActive(curAnim == DreamRoomInteraction.catAnim.sleep)
        self._catEyes.awake:SetActive(curAnim ~= DreamRoomInteraction.catAnim.sleep)
        if curAnim == DreamRoomInteraction.catAnim.idle then
            SceneTimer:setTimer(blinkInterval, self._catBlink, self, true)
        else
            SoundManager.instance:playEffect(142180, self._catAnimation.gameObject)
        end
        self._catAnimation:Play(curAnim, true)
        printInfo("_setCatAnim: "..tostring(curAnim))
    elseif self._lastCatAnim ~= curAnim then
        self._catAnimation:Play(curAnim, true)
        if self._lastCatAnim == DreamRoomInteraction.catAnim.sleep and curAnim == DreamRoomInteraction.catAnim.idle then -- 睡觉转清醒时
            printInfo("_setCatState 睡觉转清醒")
            self:_clearSittingPlayer() -- 清一下坐在上面的玩家
            SceneTimer:removeTimer(self._catAngry, self) -- 如果猫准备炸毛，先清掉timer
            self._catAnimation:RemoveFinishListener() -- 如果猫正在炸毛，清掉播完动画的回调
            self:_showTriggers(false)
            self._catEyes.sleep:SetActive(curAnim == DreamRoomInteraction.catAnim.sleep)
            self._catEyes.awake:SetActive(curAnim ~= DreamRoomInteraction.catAnim.sleep)
            SceneTimer:setTimer(blinkInterval, self._catBlink, self, true)
            SoundManager.instance:stopEffect(142180, self._catAnimation.gameObject)
        elseif self._lastCatAnim == DreamRoomInteraction.catAnim.idle and curAnim == DreamRoomInteraction.catAnim.sleep then -- 清醒转睡觉
            printInfo("_setCatState 清醒转睡觉")
            self:_showTriggers(true)
            self._catEyes.sleep:SetActive(curAnim == DreamRoomInteraction.catAnim.sleep)
            self._catEyes.awake:SetActive(curAnim ~= DreamRoomInteraction.catAnim.sleep)
            SceneTimer:removeTimer(self._catBlink, self)
            SoundManager.instance:playEffect(142180, self._catAnimation.gameObject)
        end
    end
    self._lastCatAnim = curAnim
end

function DreamRoomInteraction:_playCatSleep()
    self._catAnimation:Play(DreamRoomInteraction.catAnim.sleep, true)
    self._catAnimation:RemoveFinishListener()
    self._catEyes.sleep:SetActive(true)
    self._catEyes.idle:SetActive(false)
    self:_showTriggers(true)
end

function DreamRoomInteraction:_playCatIdle()
    self._catAnimation:Play(DreamRoomInteraction.catAnim.idle, true)
    self._catAnimation:RemoveFinishListener()
end

function DreamRoomInteraction:_showTriggers(isShow)
    printInfo("DreamRoomInteraction _showTriggers: "..tostring(isShow))
    if not self._catTriggers then
        self._catTriggers = {}
        local count = self:_getCatCanSitMaxCount()
        local beginIndex = 65
        for i=1,count do
            local trigger = goutil.findChild(SceneManager.instance:getCurScene().stage.stageGO, "trigger/trigger"..tostring(beginIndex-1+i))
            table.insert(self._catTriggers, trigger)
        end
    end
    for k,v in ipairs(self._catTriggers) do
        v:SetActive(isShow)
    end
end

function DreamRoomInteraction:_update()
    self:_checkIsJump()
    self:_setCatState()
    self:_checkIsAngry()
end

-- 炸毛弹开
function DreamRoomInteraction:_checkIsAngry()
    if self._lastCatAnim ~= DreamRoomInteraction.catAnim.sleep then -- 只有在睡觉时才会炸毛
        return
    end
    local allPlayers = SceneManager.instance:getCurScene():getAllPlayers()
    local sitOnCatCount = 0
    if allPlayers then
        for k,playerUnit in ipairs(allPlayers) do
            local variable = playerUnit.userVar:getVariable(UserVarKey.SceneRoleState)
            if variable and variable.params and variable.params[1] then
                local triggerId = tonumber(variable.params[1])
                if triggerId == 65 or triggerId == 66 or triggerId == 67 or triggerId == 68 then
                    sitOnCatCount = sitOnCatCount + 1
                    if playerUnit.isUser then
                        self.catHasSelf = true
                    end
                end
            end
        end
    end
    if sitOnCatCount >= self:_getCatCanSitMaxCount() and not self._hasSetCatAngryTimer then
        SceneTimer:setTimer(3, self._catAngry, self, false)
        self._hasSetCatAngryTimer = true
        printInfo("self.catHasSelf: "..tostring(self.catHasSelf))
    end
end

function DreamRoomInteraction:_catAngry()
    printInfo("cat angry")
    if self.catHasSelf then
        self.catHasSelf = false
        self:_clearSittingPlayer()
    end
    self._hasSetCatAngryTimer = false
    self._catAnimation:Play(DreamRoomInteraction.catAnim.awake, true)
    self._catAnimation:AddFinishListener(self._playCatSleep, self)
    self._catEyes.sleep:SetActive(false)
    self._catEyes.idle:SetActive(true)
    self:_showTriggers(false)
    SoundManager.instance:playEffect(142181)
end

function DreamRoomInteraction:_clearSittingPlayer()
    local playerUnit = SceneManager.instance:getCurScene():getUserPlayer()
    local action = playerUnit.actionComp:getCurAction()
    local variable = playerUnit.userVar:getVariable(UserVarKey.SceneRoleState)
    if variable and variable.params and variable.params[1] then
        local triggerId = tonumber(variable.params[1])
        if triggerId == 65 or triggerId == 66 or triggerId == 67 or triggerId == 68 then
            printInfo("_clearSittingPlayer")
            SceneController.instance:stopAction(SceneActionType.UseObj, handler(self.throw, self))
        end
    end
end

function DreamRoomInteraction:throw()
    printInfo("cat throw")
    local throwPos = self:_getPlayerThrowPos()
    local pos = arrayutil.randomOne(throwPos)
    SceneController.instance:dreamRoomCatThrow(pos[1], pos[2])
end

-- 到点了猫会跳一下
function DreamRoomInteraction:_checkIsJump()
    if self._lastCatAnim ~= DreamRoomInteraction.catAnim.idle then -- 只有在清醒时才会跳
        return
    end
    local curTime = ServerTime.now()
    local day, hour, min, sec = TimeUtil.getDay_Hour_Min_Sec(curTime)
    local jumpTimeCfg = self:_getCatJumpTime()
    for k,v in ipairs(jumpTimeCfg) do
        if sec == 0 and v == min then
            self._catAnimation:Play(DreamRoomInteraction.catAnim.attack, true)
            self._catAnimation:AddFinishListener(self._playCatIdle, self)
            SceneTimer:setTimer(2.5, self._playerJump, self, false)
            SoundManager.instance:playEffect(142182)
        end
    end
end

-- 播放猫眨眼动画
function DreamRoomInteraction:_catBlink()
    if self._lastCatAnim ~= DreamRoomInteraction.catAnim.idle then -- 只有在清醒时才会眨眼
        return
    end
    printInfo("_catBlink")
    self:_playCatBlinkAnim()
end

function DreamRoomInteraction:_playCatBlinkAnim()
    if not self._blinkIndex then
        self._blinkIndex = 0
        SceneTimer:setTimer(0.1, self._playCatBlinkAnim, self, true)
    end
    self._blinkIndex = self._blinkIndex + 1
    if self._blinkIndex == 1 then -- 先半眯眼
        self._catEyes.awake:SetActive(false)
        self._catEyes.idle:SetActive(true)
    elseif self._blinkIndex == 2 then -- 闭眼
        self._catEyes.idle:SetActive(false)
        self._catEyes.sleep:SetActive(true)
    else
        self._catEyes.sleep:SetActive(false)
        self._catEyes.awake:SetActive(true)
        self._blinkIndex = nil
        SceneTimer:removeTimer(self._playCatBlinkAnim, self)
    end
end

function DreamRoomInteraction:_playerJump()
    local isIdle = SceneManager.instance:getCurScene().actionMgr:isIdle()
    local playerUnit = SceneManager.instance:getCurScene():getUserPlayer()
    printInfo("DreamRoomInteraction _playerJump isIdle: "..tostring(isIdle))
    local catPos = GameUtils.getPos(self._catAnimation.gameObject)
    -- printError("DreamRoomInteraction _playerJump catPos: "..tostring(catPos.x)..", "..tostring(catPos.y)..", "..tostring(catPos.z))
    local maxDis = self:_getAttackRange()
    local maxDis_square = maxDis * maxDis
    local x,y,z = playerUnit:getPos()
    -- printError("DreamRoomInteraction _playerJump playerPos: "..tostring(x)..", "..tostring(y)..", "..tostring(z))
    local dis_square = (catPos.x - x) * (catPos.x - x) + (catPos.z - y) * (catPos.z - y)
    -- printError("DreamRoomInteraction _playerJump dis_square: "..tostring(dis_square))
    if isIdle and dis_square <= maxDis_square then
        SceneController.instance:showPose(266)
    end
end
-----------------------------------------------------------------------

----------------------------------周年蛋糕----------------------------
function DreamRoomInteraction:setCarnivalPartyCakeState()
    if not self._cakeNotFinishObj then
        self._cakeNotFinishObj = goutil.findChild(SceneManager.instance:getCurScene().stage.stageGO, "standstill/wujian/ab-s158_obj_dangaohe")
    end
    local isShow = ActivityModel.instance:getActivityIsShow(ToyHouseGotoUtil.carnivalPartyCake, true)
    if isShow then
        self:_getCakeActivityInfo()
    else
        self._cakeNotFinishObj:SetActive(false)
    end
end
function DreamRoomInteraction:_getCakeActivityInfo()
    Activity251Agent.instance:sendGetAct251InfoRequest(ToyHouseGotoUtil.carnivalPartyCake,
        handler(self._onGetCakeActivitySucc, self))
end
function DreamRoomInteraction:_onGetCakeActivitySucc(curday, globalprocess, processrewardinfos, todaysubmititem,
    completerewardinfos)
    self._finishDay = tonumber(CarnivalPartyCakeConfig.getCommonConfig(ToyHouseGotoUtil.carnivalPartyCake,
        "finishDay"))
    self._cakeisComplete = curday >= self._finishDay
    self._cakeNotFinishObj:SetActive(not self._cakeisComplete)
end
-----------------------------------------------------------------------

----------------------------------签到板----------------------------
function DreamRoomInteraction:setSigninBoardState()
    if not self._signinBoardGos then
        self._signinBoardGos = {}
        local container = SceneManager.instance:getCurScene().stage.stageGO
        self._signinBoardGos.imgGo = goutil.findChild(container, "standstill/wujian/ab-s158_obj_pingban/imgGo")
        self._signinBoardGos.imgNotFull = goutil.findChild(container, "standstill/wujian/ab-s158_obj_pingban/imgGo/imgProgressNotFull")
        self._signinBoardGos.imgFull = goutil.findChild(container, "standstill/wujian/ab-s158_obj_pingban/imgGo/imgProgressFull")
        local txtGo = goutil.findChild(container, "standstill/wujian/ab-s158_obj_pingban/imgGo/txtProgress")
        self._signinBoardGos.txtProgress = txtGo:GetComponent(typeof(UnityEngine.TextMesh))
    end
    self._maxProgress = tonumber(Activity466Config.getCommonConfig("maxProgressValue"))
    local isShow = ActivityModel.instance:getActivityIsShow(ToyHouseGotoUtil.signinBoard, true)
    if isShow then
        self._signinBoardGos.imgNotFull:SetActive(false)
        self._signinBoardGos.imgFull:SetActive(false)
        self._signinBoardGos.txtProgress.gameObject:SetActive(false)
        self:_getSigninBoardActInfo()
    else
        self._signinBoardGos.imgNotFull:SetActive(true)
        self._signinBoardGos.imgFull:SetActive(false)
        self._signinBoardGos.txtProgress.gameObject:SetActive(false)
    end
end
function DreamRoomInteraction:_getSigninBoardActInfo()
    Activity466Agent.instance:sendGetAct466InfoRequest(handler(self._onGetSigninBoardActInfo, self))
end
function DreamRoomInteraction:_onGetSigninBoardActInfo(curglobalprogress, selfmessage, backgroundindex, gainedglobalprogressrewardids)
    local progress = curglobalprogress/self._maxProgress*100
    progress = progress > 100 and 100 or progress
    self._signinBoardGos.txtProgress.text = tostring(math.floor(progress)).."%"
    self._signinBoardGos.imgNotFull:SetActive(progress < 100)
    self._signinBoardGos.imgFull:SetActive(progress >= 100)
    self._signinBoardGos.txtProgress.gameObject:SetActive(true)
end
-----------------------------------------------------------------------

return DreamRoomInteraction