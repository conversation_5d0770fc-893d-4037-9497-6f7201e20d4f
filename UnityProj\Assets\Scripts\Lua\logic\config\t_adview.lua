-- {excel:P拍脸图配置表.xlsx, sheetName:export_活动广告图}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_adview", package.seeall)

local title = {id=1,type=2,sortId=3,activityDefineId=4,functionId=5,channel=6,startTime=7,endTime=8,clickFun=9,clickParams=10}

local dataList = {
	{1, 1, 3, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-23T23:59:59", "gotoActivityById", "2487"},
	{2, 1, 4, 111, 0, nil, "2025-07-03T05:00:00", "2025-07-30T23:59:59", "openView", "LifeGuideView"},
	{3, 1, 5, 0, 0, nil, "2025-07-17T05:00:00", "2025-07-30T23:59:59", "gotoActivityById", "2496"},
	{4, 1, 6, 0, 0, nil, "2025-06-28T05:00:00", "2025-07-02T23:59:59", "gotoActivityById", "2371"},
	{5, 1, 7, 0, 0, {110001,210009,610001,610002}, "2025-01-22T10:00:00", "2025-02-18T23:59:59", "activity", "32"},
	{6, 1, 8, 0, 0, {110001,210009,610001,610002}, "2024-05-11T00:00:00", "2024-06-06T23:59:59", "activity", "64"},
	{7, 1, 9, 0, 0, {110001,210009,610001,610002}, "2025-07-03T14:00:00", "2025-07-22T23:59:59", "activity", "31"},
	{8, 1, 10, 0, 0, {110006,130005}, "2025-07-03T14:00:00", "2025-07-22T23:59:59", "", ""},
	{9, 1, 1, 0, 0, nil, "2025-07-12T05:00:00", "2025-08-10T23:59:59", "gotoActivityById", "2461"},
	{10, 1, 2, 0, 0, nil, "2025-06-26T05:00:00", "2025-07-02T23:59:59", "gotoActivityById", "1562"},
	{11, 2, 1, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-23T23:59:59", "gotoActivityById", "2430"},
	{12, 2, 3, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-30T23:59:59", "gotoCatalog", "11"},
	{13, 2, 4, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-30T23:59:59", "gotoCatalog", "11"},
	{14, 2, 6, 0, 0, {110001,210009,610001,610002}, "2025-07-03T05:00:00", "2025-07-23T23:59:59", "activity", "32"},
	{15, 2, 2, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-23T23:59:59", "gotoActivityById", "2448"},
	{16, 2, 7, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-30T23:59:59", "gotoCatalog", "11"},
	{17, 2, 5, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-30T23:59:59", "gotoCatalog", "11"},
	{18, 2, 8, 0, 0, {110001,210009,610001,610002}, "2025-05-01T12:00:00", "2025-07-16T23:59:59", "activity", "73"},
	{19, 2, 9, 0, 0, {110001,210009,610001,610002}, "2024-06-29T10:00:00", "2024-07-08T23:59:59", "activity", "63"},
	{20, 2, 10, 0, 0, {110001,210009,610001,610002}, "2025-06-20T10:00:00", "2025-07-18T23:59:59", "activity", "50"},
}

local t_adview = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
	[9] = dataList[9],
	[10] = dataList[10],
	[11] = dataList[11],
	[12] = dataList[12],
	[13] = dataList[13],
	[14] = dataList[14],
	[15] = dataList[15],
	[16] = dataList[16],
	[17] = dataList[17],
	[18] = dataList[18],
	[19] = dataList[19],
	[20] = dataList[20],
}

t_adview.dataList = dataList
local mt
mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_adview