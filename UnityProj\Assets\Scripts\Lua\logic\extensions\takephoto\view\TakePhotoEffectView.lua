module("logic.extensions.takephoto.view.TakePhotoEffectView",package.seeall)
---@class TakePhotoEffectView
local TakePhotoEffectView = class("TakePhotoEffectView",ViewComponent)

-- function TakePhotoEffectView:ctor()
-- 	self.model = BaseListModel.New()
--     TakePhotoEffectView.super.ctor(self, self.model, "beautifyGo/view/chartletGo/chartletList", TakePhotoPresentor.Img_Item, TakePhotoImgListCell, {
--         kScrollDirV, 100, 100, 14.5, 14.5, 2
--     })
-- end

--- view初始化时会执行
function TakePhotoEffectView:buildUI()
	TakePhotoEffectView.super.buildUI(self)

	self._goViews = self:getGo("beautifyGo/view")
	self._btnBg = self:getBtn("beautifyGo/bg")

	--右侧列表
	self._effctButtonGroup = self:getGo("beautifyGo/view/beautifyTab")
	local trans = self._effctButtonGroup.transform
	self._btnGroup = {}

	for i = 1,trans.childCount do 
		local go = trans:GetChild(i-1).gameObject
		local btn = Framework.ButtonAdapter.Get(go)
		table.insert(self._btnGroup,btn)
	end

	self._goStrickList = self:getGo("beautifyGo/view/chartletGo/chartletList")
	self._goWordList = self:getGo("beautifyGo/view/chartletGo/wordList")

	self._goStrickList:SetActive(false)
	self._goWordList:SetActive(false)

	--左侧图层列表
	self._goLayer = self:getGo("beautifyGo/view/layerGo")
	self._layerListGo = self:getGo("beautifyGo/view/layerGo/layerList")
	self._layerContent = self:getGo("beautifyGo/view/layerGo/layerList/content")
	self._btnLayer = self:getBtn("beautifyGo/view/btnLayer")
	self._goLayerItem = self:getGo("beautifyGo/view/layerGo/layerList/content/takephotolayeritem")
	--中间画布
	self._goCanvas = self:getGo("beautifyGo/CanvasGO")
	self._goEffectItem = self:getGo("beautifyGo/CanvasGO/adjustGo")
	--左下按钮
	self._btnClear = self:getBtn("beautifyGo/view/btnClear")

	--如你所见，集成把这两个按钮放错了
	self._btnRevert = self:getBtn("beautifyGo/view/btnForward")
	self._btnForward = self:getBtn("beautifyGo/view/btnRetreat")

	self._btnBack = self:getBtn("beautifyGo/view/btnClose")
	self._btnBeautify = self:getBtn("commonGo/btnBeautify")

	self._goJoyStick = self:getGo("beautifyGo/joystickGo")
	self._goJoyStick:SetActive(false)

	self._goRedPoint = self:getGo("commonGo/btnBeautify/redPoint")

	self:getGo("dressGo"):SetActive(false)

	self._commandReceiver = PhotoEffectCommandReceiver.New(self._goCanvas,self._layerContent )
	self._commandReceiver:setExcuteCallBack(handler(self._onCommandExcute,self))
	self._commandReceiver:setRevertCallBack(handler(self._onCommandRevert,self))
end

--- view初始化时会执行，在buildUI之后
function TakePhotoEffectView:bindEvents()
	self._btnClear:AddClickListener(self._onBtnClickClear,self)
	self._btnRevert:AddClickListener(self._onBtnClickRevert,self)
	self._btnForward:AddClickListener(self._onBtnClickForward,self)
	self._btnBack:AddClickListener(self._onBtnClickBack,self)
	self._btnBeautify:AddClickListener(self._onBtnClickBeautify,self)
	self._btnLayer:AddClickListener(self._onBtnClickLayer,self)
	self._btnBg:AddClickListener(self._onBtnClickBg,self)

	local types = PhotoStickerConfig.getAllStrickerType()
	self._typeToIndex = {}
	for i=1,#self._btnGroup do 

		self._btnGroup[i]:AddClickListener(self._onBtnClickTab,self,{types[i]})
		
		self._typeToIndex[types[i]] = i
	end
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function TakePhotoEffectView:onEnter()
	TakePhotoEffectView.super.onEnter(self)

	GlobalDispatcher:addListener(GlobalNotify.OnSelectPhotoEffect, self._onSelectPhotoEffect, self)
	GlobalDispatcher:addListener(GlobalNotify.OnClickPhotoEffectItem, self._onClickPhotoEffectItem, self)
	GlobalDispatcher:addListener(GlobalNotify.OnClickTakePhoto, self._onClickTakePhoto, self)

	for i=1,#self._btnGroup do 
		local go = self._btnGroup[i].gameObject
		local select = goutil.findChild(go,"imgSelect")
		local unSelect = goutil.findChild(go,"imgUnSelect")
		select:SetActive(false)
		unSelect:SetActive(true)
	end

	self:hideList()
	self:hideLayer()
	self:_onBtnClickTab({1})
	self:_checkRedpoint()
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function TakePhotoEffectView:onExit()
	TakePhotoEffectView.super.onExit(self)
	self._commandReceiver:deleteAll()
	GlobalDispatcher:removeListener(GlobalNotify.OnSelectPhotoEffect, self._onSelectPhotoEffect, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnClickPhotoEffectItem, self._onClickPhotoEffectItem, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnClickTakePhoto, self._onClickTakePhoto, self)
end

--- view销毁时会执行，在destroyUI之前
function TakePhotoEffectView:unbindEvents()
	TakePhotoEffectView.super.unbindEvents(self)

	self._btnClear:RemoveClickListener()
	self._btnRevert:RemoveClickListener()
	self._btnForward:RemoveClickListener()
	self._btnBack:RemoveClickListener()
	self._btnBeautify:RemoveClickListener()
	self._btnLayer:RemoveClickListener()
	self._btnBg:RemoveClickListener()

	for i=1,#self._btnGroup do 
		self._btnGroup[i]:RemoveClickListener()
	end
end

--- view销毁时会执行
function TakePhotoEffectView:destroyUI()
	TakePhotoEffectView.super.destroyUI(self)
end

function TakePhotoEffectView:_checkRedpoint()
	local storageKey = LocalStorage.instance:getValue({name = PhotoStickerConfig.RedPointKey,type = 3},false)
	if storageKey then 
		self._goRedPoint:SetActive(true)
	else 
		local list = PhotoStickerConfig.getAllStrickerList()
		local isRedpoint = false
		for i=1,#list do 
			local item = list[i]
			if item.id ~= 0 then 
				local isFirst = not LocalStorage.instance:getValue({name = PhotoStickerConfig.ItemStorageKey .. item.id,type = 3},false)
				if isFirst then 
					isRedpoint = true
					printInfo("TakePhotoEffectView isFirst ItemStorageKey",item.id)
					break
				end

				if item.itemId > 0 then 
					local isEnable = ItemService.instance:getItemNum(item.itemId) > 0
					if isEnable then 
						local isFirstEnable = not LocalStorage.instance:getValue({name = PhotoStickerConfig.FirstEnableKey .. item.id,type = 3},false)
						if isFirstEnable then
							isRedpoint = true
							printInfo("TakePhotoEffectView FirstEnableKey",item.id,item.itemId)
							break
						end
					end
				end
				
				if item.openTime and item.closeTime then
					local isEnable = TimeUtil.isNowBetween(item.openTime, item.closeTime)
					if isEnable then 
						local isFirstEnable = not LocalStorage.instance:getValue({name = PhotoStickerConfig.FirstEnableKey .. item.id,type = 3},false)
						if isFirstEnable then
							isRedpoint = true
							printInfo("TakePhotoEffectView FirstEnableKey",item.id,item.openTime,item.closeTime)
							break
						end
					end
				end
			end	
		end
		if isRedpoint then 
			LocalStorage.instance:setValue({name = PhotoStickerConfig.RedPointKey,type = 3},true)
		end
		self._goRedPoint:SetActive(isRedpoint)
	end
end

function TakePhotoEffectView:_setRedpoint()
	local storageKey = LocalStorage.instance:getValue({name = PhotoStickerConfig.RedPointKey,type = 3},false)
	if not storageKey then 
		return
	end

	local list = PhotoStickerConfig.getAllStrickerList()
	for i=1,#list do 
		local item = list[i]
		if item.id ~= 0 then 
			LocalStorage.instance:setValue({name = PhotoStickerConfig.ItemStorageKey .. item.id,type = 3},true)
			
			if item.itemId > 0 then 
				local isEnable = ItemService.instance:getItemNum(item.itemId) > 0
				if isEnable then 
					local isFirstEnable = not LocalStorage.instance:getValue({name = PhotoStickerConfig.FirstEnableKey .. item.id,type = 3},false)
					if isFirstEnable then
						LocalStorage.instance:setValue({name = PhotoStickerConfig.FirstEnableKey .. item.id,type = 3},true)
					end
				end
			end

			if item.openTime and item.closeTime then
				local isEnable = TimeUtil.isNowBetween(item.openTime, item.closeTime)
				if isEnable then 
					local isFirstEnable = not LocalStorage.instance:getValue({name = PhotoStickerConfig.FirstEnableKey .. item.id,type = 3},false)
					if isFirstEnable then
						LocalStorage.instance:setValue({name = PhotoStickerConfig.FirstEnableKey .. item.id,type = 3},true)
					end
				end
			end
		end
	end

	LocalStorage.instance:setValue({name = PhotoStickerConfig.RedPointKey,type = 3},false)
	self._goRedPoint:SetActive(false)
end

function TakePhotoEffectView:showList()
	if not self._activeDic then
		self._activeDic = {}
	end

	local go = self:getGo("right")
	self._activeDic["right"] = go.activeSelf
	go:SetActive(false)

	go = self:getGo("arGo")
	self._activeDic["arGo"] = go.activeSelf
	go:SetActive(false)


	self._btnBeautify.gameObject:SetActive(false)

	--self._goViews:SetActive(true)


	self:getGo("beautifyGo/view/chartletGo"):SetActive(true)
	self:getGo("beautifyGo/view/beautifyTab"):SetActive(true)
	self:getGo("commonGo/btnAlbum"):SetActive(false)
	self._btnBg.gameObject:SetActive(true)
	self:refreshRevertBtn()

	self._isListHide = false
end

function TakePhotoEffectView:hideList()
	if self._activeDic then 
		self:getGo("right"):SetActive(self._activeDic["right"])
		self:getGo("arGo"):SetActive(self._activeDic["arGo"])
		self:getGo("arGo"):SetActive(self._activeDic["arGo"])
	end
	self:getGo("commonGo/btnAlbum"):SetActive(true)
	self._btnBeautify.gameObject:SetActive(true)

	self:getGo("beautifyGo/view/chartletGo"):SetActive(false)
	self:getGo("beautifyGo/view/beautifyTab"):SetActive(false)
	--self._commandReceiver:unselectNow()
	self._isListHide = true
end

function TakePhotoEffectView:showLayer()
	if not self._activeDic then
		self._activeDic = {}
	end

	local go = self:getGo("arGo")
	self._activeDic["arGo"] = go.activeSelf
	go:SetActive(false)

	self:getGo("commonGo/btnPanorama"):SetActive(false)
	self:getGo("btnAR"):SetActive(false)
	self._btnBg.gameObject:SetActive(true)

	self:_setLayerActive(true)
	self._btnLayer.gameObject:SetActive(true)

	local activeNum = self._commandReceiver:getActiveEffectNum()
	local commandNum,revertNum = self._commandReceiver:getCommandNum()
	self._btnClear.gameObject:SetActive(activeNum > 0 or commandNum > 0 or revertNum > 0) 
	self._btnRevert.gameObject:SetActive(activeNum > 0 or commandNum > 0 or revertNum > 0) 
	self._btnForward.gameObject:SetActive(activeNum > 0 or commandNum > 0 or revertNum > 0)

	self._isLayerHide = false
	--self:refreshRevertBtn()
end

function TakePhotoEffectView:hideLayer()

	if self._activeDic then 
		self:getGo("arGo"):SetActive(self._activeDic["arGo"])
	end

	local isInMyOwn = HouseModel.instance:isMyHouse()
	local isInHouseOrIsland = HouseModel.instance:isInHouseOrIsland()
	self:getGo("commonGo/btnPanorama"):SetActive(isInHouseOrIsland and isInMyOwn)

	local hideARBtn = self:getOpenParam()[3] or false
	self:getGo("btnAR"):SetActive(not hideARBtn)

	self:_setLayerActive(false)
	self._btnLayer.gameObject:SetActive(false)
	self._commandReceiver:unselectNow()

	self._btnClear.gameObject:SetActive(false) 
	self._btnRevert.gameObject:SetActive(false) 
	self._btnForward.gameObject:SetActive(false)
	
	self._isLayerHide = true
end

function TakePhotoEffectView:isItemEnable(info)
	local isTime = true
	local isItem = true

	if info.openTime and info.closeTime then
		isTime = TimeUtil.isNowBetween(info.openTime, info.closeTime)
	end 

	if info.itemId ~= 0 then 
		isItem = ItemService.instance:getItemNum(info.itemId) > 0
	end

	return isTime and isItem
end

function TakePhotoEffectView:_onBtnClickTab(params)
	local typeId = params[1]
	local btnIndex = self._typeToIndex[tonumber(typeId)]
	if self._preSelect then 
		local select = goutil.findChild(self._preSelect,"imgSelect")
		local unSelect = goutil.findChild(self._preSelect,"imgUnSelect")
		select:SetActive(false)
		unSelect:SetActive(true)
	end

	local go = self._btnGroup[btnIndex].gameObject
	local select = goutil.findChild(go,"imgSelect")
	local unSelect = goutil.findChild(go,"imgUnSelect")
	unSelect:SetActive(false)
	select:SetActive(true)

	self._nowType = typeId
	self._preSelect = go
	
	local list = PhotoStickerConfig.getStrickerListByType(typeId)


	table.sort(list,function(a, b)
		local isEnable_a = self:isItemEnable(a)
		local isEnable_b = self:isItemEnable(b)
		if a.openTime and a.closeTime then 
			isEnable_a = TimeUtil.isNowBetween(a.openTime, a.closeTime)
		end
		
		if b.openTime and b.closeTime then 
			isEnable_b = TimeUtil.isNowBetween(b.openTime, b.closeTime)
		end

		if isEnable_a and not isEnable_b then
			return true
		elseif not isEnable_a and isEnable_b then
			return false
		else
			if a.priority or b.priority then 
				if a.priority and b.priority then 
					return a.id < b.id
				else
					return a.priority
				end
			else 
				return a.id < b.id
			end
		end
	end)

	
	
	if typeId == 1 then
		self._goStrickList:SetActive(true)
		self._goWordList:SetActive(false)
		self._viewPresentor.strickModel:setMoList(list)
	elseif typeId == 2 then
		self._goStrickList:SetActive(false)
		self._goWordList:SetActive(true)
		self._viewPresentor.wordAndFrameModel:setMoList(list)
		--清空相框的选中表现
		self._viewPresentor.takePhotoEffectWordListView:setSelectItems({},true)
	elseif typeId == 3 then 
		self._goStrickList:SetActive(false)
		self._goWordList:SetActive(true)
		table.insert(list,1,{id = 0,type = 3})
		self._viewPresentor.wordAndFrameModel:setMoList(list)
		local nowFrame = self._commandReceiver:getNowFrame()
		if not nowFrame then 
			self._viewPresentor.takePhotoEffectWordListView:selectCell(1,true)
		else 
			local index = table.indexof(list,nowFrame) or 1
			self._viewPresentor.takePhotoEffectWordListView:selectCell(index,true)
		end
	end
end

function TakePhotoEffectView:refreshRevertBtn()
	local commandNum,revertNum = self._commandReceiver:getCommandNum()
	self._btnRevert.btn.interactable = commandNum > 0
	self._btnForward.btn.interactable = revertNum > 0
	local activeNum = self._commandReceiver:getActiveEffectNum()
	self._btnClear.btn.interactable = activeNum > 0 or commandNum > 0 or revertNum > 0

	if activeNum > 0 or commandNum > 0 or revertNum > 0 then 
		self:showLayer()
	elseif (activeNum <= 0 and commandNum <=0 and revertNum <= 0) and not self._isLayerHide then 
		self:hideLayer()
	end
end

function TakePhotoEffectView:_onBtnClickClear()
	DialogHelper.showConfirmDlg(lang("是否确定清除全部贴纸，清除后无法撤销"), function (isOk)
		if isOk then
			self._commandReceiver:deleteAll()
			self:refreshRevertBtn()
			self:hideList()
			if self._nowType == 3 then 
				self._viewPresentor.takePhotoEffectWordListView:selectCell(1,true)
			end
		end
	end)
end

function TakePhotoEffectView:_onBtnClickRevert()
	self._commandReceiver:revertLastCommand()
	self:refreshRevertBtn()
end

function TakePhotoEffectView:_onBtnClickForward()
	self._commandReceiver:cancelRevert()
	self:refreshRevertBtn()
end

function TakePhotoEffectView:_onBtnClickBack()
	self:close()
end

function TakePhotoEffectView:_onBtnClickBeautify()
	LogService.instance:logCustom("photo_beautification")
	self:_setRedpoint()
	self:showList()
end

function TakePhotoEffectView:_onSelectPhotoEffect(cell) 
	local type = cell.cellInfo.type
	local photoFrame = 3

	if type ~= photoFrame then 
		local command = PhotoCommand.New()
    	command:setCommand(PhotoCommand.CommandType.create,_,{cell.cellInfo.type,cell.cellInfo})
		command.callback = handler(self._onCreateItemSucc,self)
    	self._commandReceiver:excuteCommand(command)
	else
		local command = PhotoCommand.New()
    	command:setCommand(PhotoCommand.CommandType.setFrame,_,{cell.cellInfo})
    	command.callback = handler(self._selectFrameCallBack,self) 
		self._commandReceiver:excuteCommand(command)
		--直接拿别的viewComponent不太规范，但是baseListModel里面好像没法通过DataProvider去选中，所以只能这样搞
		local index = self._viewPresentor.wordAndFrameModel:getMoIndex(cell.cellInfo)
		self._viewPresentor.takePhotoEffectWordListView:selectCell(index,true)
	end

	self:refreshRevertBtn()
end

function TakePhotoEffectView:_onCreateItemSucc()
	self:hideList()
end

function TakePhotoEffectView:_selectFrameCallBack(command,isRevert)
	if self._nowType ~= 3 then return end
	local info
	if isRevert then 
		info = command.preFrame
	else
		info = command.param[1]
	end
	if info then
		local index = self._viewPresentor.wordAndFrameModel:getMoIndex(info)
		self._viewPresentor.takePhotoEffectWordListView:selectCell(index or 1,true)
	else
		self._viewPresentor.takePhotoEffectWordListView:selectCell(1,true)
	end
end

function TakePhotoEffectView:_onBtnClickLayer()
	local activeSelf = self._goLayer.activeSelf
	self:_setLayerActive(not activeSelf)
end

function TakePhotoEffectView:_onBtnClickBg()
	if not self._isLayerHide then 
		self:_setLayerActive(false)
		self._commandReceiver:unselectNow()
	end
	self:hideList()

	self._btnBg.gameObject:SetActive(false)
end

function TakePhotoEffectView:_setLayerActive(active)
	self:getGo("commonGo/left"):SetActive(not active)
	self:getGo("beautifyGo/view/btnLayer/imgSelect"):SetActive(active)
	self:getGo("beautifyGo/view/btnLayer/imgUnSelect"):SetActive(not active)
	self._goLayer:SetActive(active)
end

function TakePhotoEffectView:_onClickPhotoEffectItem()
	if self._isLayerHide then 
		self:showLayer()
	end
end

function TakePhotoEffectView:_onCommandExcute()
	self._btnBg.gameObject:SetActive(true)
	self:refreshRevertBtn()
end

function TakePhotoEffectView:_onCommandRevert()
	self:refreshRevertBtn()
end

function TakePhotoEffectView:_onClickTakePhoto()
	self._commandReceiver:unselectNow()
end

return TakePhotoEffectView