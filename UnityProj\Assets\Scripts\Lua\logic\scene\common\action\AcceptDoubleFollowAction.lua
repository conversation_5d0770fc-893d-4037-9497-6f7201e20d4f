module("logic.scene.common.action.AcceptDoubleFollowAction",package.seeall)
local AcceptDoubleFollowAction = class("AcceptDoubleFollowAction", SceneActionBase)

function AcceptDoubleFollowAction:onStart()
	local player = SceneManager.instance:getCurScene():getPlayer(self.params.targetId)
	if player then
		local userPlayer = SceneManager.instance:getCurScene():getUserPlayer()
		local x1 ,y1 = userPlayer:getPos()
		local x2, y2 = player:getPos()
		if (x1-x2)^2 + (y1-y2)^2 < 16 then
			SceneAgent.instance:sendSceneShareRequest(PlayerActionType.AcceptDoubleFollow.typeId, {self.params.targetId}, handler(self.onShareResponse, self))
			return
		else 
			FlyTextManager.instance:showFlyText(lang("距离太远了~")) 
		end
	end
	self:finish(false)
end

function AcceptDoubleFollowAction:onShareResponse(status)
	if status == 0 then
		TaskCamCmdHelper.focusCharacter(0)
		SceneController.instance:registerLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		ViewMgr.instance:open("JoinUmbrellaPanel")
		SceneManager.instance:getCurScene():getUserPlayer():setTriggerEnable(false)
	else
		DialogHelper.showErrorMsg(status)
		self:finish(false)
	end
end

function AcceptDoubleFollowAction:onStop()
	self:finish(true)
	SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.AcceptDoubleFollow.typeId, handler(self.onCancelRequest, self))
	SceneManager.instance:getCurScene():getUserPlayer():setTriggerEnable(true)
	ViewMgr.instance:close("JoinUmbrellaPanel")
end

function AcceptDoubleFollowAction:onCancelRequest(status)
	SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
end

function AcceptDoubleFollowAction:onActionChange(nowState, oldState)
	if oldState == PlayerActionType.AcceptDoubleFollow and nowState ~= PlayerActionType.AcceptDoubleFollow then
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		SceneManager.instance:getCurScene():getUserPlayer():setTriggerEnable(true)		
		ViewMgr.instance:close("JoinUmbrellaPanel")
		self:finish(true)
	end
end

function AcceptDoubleFollowAction:getType()
	return SceneActionType.AcceptDoubleFollow
end
return AcceptDoubleFollowAction