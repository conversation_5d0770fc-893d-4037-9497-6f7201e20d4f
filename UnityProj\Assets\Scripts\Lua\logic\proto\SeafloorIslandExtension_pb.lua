-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
local HOUSEEXTENSION_PB = require("logic.proto.HouseExtension_pb")
local ISLANDEXTENSION_PB = require("logic.proto.IslandExtension_pb")
local BACKPACKEXTENSION_PB = require("logic.proto.BackpackExtension_pb")
local USEREXTENSION_PB = require("logic.proto.UserExtension_pb")
local TRAVELLEREXTENSION_PB = require("logic.proto.TravellerExtension_pb")
local FOODEXTENSION_PB = require("logic.proto.FoodExtension_pb")
local SEABEDORDEREXTENSION_PB = require("logic.proto.SeabedOrderExtension_pb")
module("logic.proto.SeafloorIslandExtension_pb", package.seeall)


local tb = {}
GETSEAFLOORISLANDINFOREQUEST_MSG = protobuf.Descriptor()
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
SEAFLOORSECTORNO_MSG = protobuf.Descriptor()
tb.SEAFLOORSECTORNO_SECTORS_FIELD = protobuf.FieldDescriptor()
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG = protobuf.Descriptor()
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD = protobuf.FieldDescriptor()
DECORATESEAFLOORISLANDREQUEST_MSG = protobuf.Descriptor()
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD = protobuf.FieldDescriptor()
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG = protobuf.Descriptor()
SEAFLOORORDERNO_MSG = protobuf.Descriptor()
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD = protobuf.FieldDescriptor()
tb.SEAFLOORORDERNO_ORDERS_FIELD = protobuf.FieldDescriptor()
GETSEAFLOORISLANDINFOREPLY_MSG = protobuf.Descriptor()
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD = protobuf.FieldDescriptor()
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD = protobuf.FieldDescriptor()
DECORATESEAFLOORISLANDREPLY_MSG = protobuf.Descriptor()
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD = protobuf.FieldDescriptor()

tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.name = "userId"
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.full_name = ".GetSeafloorIslandInfoRequest.userId"
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.number = 1
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.index = 0
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.default_value = ""
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.type = 9
tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD.cpp_type = 9

GETSEAFLOORISLANDINFOREQUEST_MSG.name = "GetSeafloorIslandInfoRequest"
GETSEAFLOORISLANDINFOREQUEST_MSG.full_name = ".GetSeafloorIslandInfoRequest"
GETSEAFLOORISLANDINFOREQUEST_MSG.filename = "SeafloorIslandExtension"
GETSEAFLOORISLANDINFOREQUEST_MSG.nested_types = {}
GETSEAFLOORISLANDINFOREQUEST_MSG.enum_types = {}
GETSEAFLOORISLANDINFOREQUEST_MSG.fields = {tb.GETSEAFLOORISLANDINFOREQUEST_USERID_FIELD}
GETSEAFLOORISLANDINFOREQUEST_MSG.is_extendable = false
GETSEAFLOORISLANDINFOREQUEST_MSG.extensions = {}
tb.SEAFLOORSECTORNO_SECTORS_FIELD.name = "sectors"
tb.SEAFLOORSECTORNO_SECTORS_FIELD.full_name = ".SeafloorSectorNO.sectors"
tb.SEAFLOORSECTORNO_SECTORS_FIELD.number = 1
tb.SEAFLOORSECTORNO_SECTORS_FIELD.index = 0
tb.SEAFLOORSECTORNO_SECTORS_FIELD.label = 3
tb.SEAFLOORSECTORNO_SECTORS_FIELD.has_default_value = false
tb.SEAFLOORSECTORNO_SECTORS_FIELD.default_value = {}
tb.SEAFLOORSECTORNO_SECTORS_FIELD.message_type = ISLANDEXTENSION_PB.UNLOCKINFONO_MSG
tb.SEAFLOORSECTORNO_SECTORS_FIELD.type = 11
tb.SEAFLOORSECTORNO_SECTORS_FIELD.cpp_type = 10

SEAFLOORSECTORNO_MSG.name = "SeafloorSectorNO"
SEAFLOORSECTORNO_MSG.full_name = ".SeafloorSectorNO"
SEAFLOORSECTORNO_MSG.filename = "SeafloorIslandExtension"
SEAFLOORSECTORNO_MSG.nested_types = {}
SEAFLOORSECTORNO_MSG.enum_types = {}
SEAFLOORSECTORNO_MSG.fields = {tb.SEAFLOORSECTORNO_SECTORS_FIELD}
SEAFLOORSECTORNO_MSG.is_extendable = false
SEAFLOORSECTORNO_MSG.extensions = {}
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.name = "seafloorOrderInfo"
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.full_name = ".GetSeafloorIslandBuildingInfoReply.seafloorOrderInfo"
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.number = 1
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.index = 0
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.label = 1
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.default_value = nil
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.message_type = SEAFLOORORDERNO_MSG
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.type = 11
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.name = "seafloorSectorInfo"
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.full_name = ".GetSeafloorIslandBuildingInfoReply.seafloorSectorInfo"
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.number = 2
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.index = 1
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.label = 1
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.default_value = nil
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.message_type = SEAFLOORSECTORNO_MSG
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.type = 11
tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD.cpp_type = 10

GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.name = "GetSeafloorIslandBuildingInfoReply"
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.full_name = ".GetSeafloorIslandBuildingInfoReply"
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.filename = "SeafloorIslandExtension"
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.nested_types = {}
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.enum_types = {}
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.fields = {tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORORDERINFO_FIELD, tb.GETSEAFLOORISLANDBUILDINGINFOREPLY_SEAFLOORSECTORINFO_FIELD}
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.is_extendable = false
GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG.extensions = {}
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.name = "furnitureInfos"
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.full_name = ".DecorateSeafloorIslandRequest.furnitureInfos"
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.number = 1
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.index = 0
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.type = 11
tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD.cpp_type = 10

tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.name = "sellingItem"
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.full_name = ".DecorateSeafloorIslandRequest.sellingItem"
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.number = 2
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.index = 1
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.type = 5
tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD.cpp_type = 1

tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.name = "buyingItem"
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.full_name = ".DecorateSeafloorIslandRequest.buyingItem"
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.number = 3
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.index = 2
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.type = 5
tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD.cpp_type = 1

tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.name = "takeBackItem"
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.full_name = ".DecorateSeafloorIslandRequest.takeBackItem"
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.number = 4
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.index = 3
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.type = 5
tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD.cpp_type = 1

tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.name = "terrainInfo"
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.full_name = ".DecorateSeafloorIslandRequest.terrainInfo"
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.number = 5
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.index = 4
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.message_type = ISLANDEXTENSION_PB.TERRAININFONO_MSG
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.type = 11
tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD.cpp_type = 10

tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.name = "decorateType"
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.full_name = ".DecorateSeafloorIslandRequest.decorateType"
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.number = 6
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.index = 5
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.label = 1
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.default_value = 0
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.type = 5
tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD.cpp_type = 1

tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.name = "itemLockPassWord"
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.full_name = ".DecorateSeafloorIslandRequest.itemLockPassWord"
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.number = 7
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.index = 6
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.label = 1
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.default_value = ""
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.type = 9
tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.cpp_type = 9

tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.name = "islandOutsideInfo"
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.full_name = ".DecorateSeafloorIslandRequest.islandOutsideInfo"
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.number = 8
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.index = 7
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.type = 5
tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.cpp_type = 1

tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.name = "weatherId"
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.full_name = ".DecorateSeafloorIslandRequest.weatherId"
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.number = 9
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.index = 8
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.label = 1
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.default_value = 0
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.type = 5
tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD.cpp_type = 1

DECORATESEAFLOORISLANDREQUEST_MSG.name = "DecorateSeafloorIslandRequest"
DECORATESEAFLOORISLANDREQUEST_MSG.full_name = ".DecorateSeafloorIslandRequest"
DECORATESEAFLOORISLANDREQUEST_MSG.filename = "SeafloorIslandExtension"
DECORATESEAFLOORISLANDREQUEST_MSG.nested_types = {}
DECORATESEAFLOORISLANDREQUEST_MSG.enum_types = {}
DECORATESEAFLOORISLANDREQUEST_MSG.fields = {tb.DECORATESEAFLOORISLANDREQUEST_FURNITUREINFOS_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_SELLINGITEM_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_BUYINGITEM_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_TAKEBACKITEM_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_TERRAININFO_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_DECORATETYPE_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_ITEMLOCKPASSWORD_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD, tb.DECORATESEAFLOORISLANDREQUEST_WEATHERID_FIELD}
DECORATESEAFLOORISLANDREQUEST_MSG.is_extendable = false
DECORATESEAFLOORISLANDREQUEST_MSG.extensions = {}
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.name = "GetSeafloorIslandBuildingInfoRequest"
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.full_name = ".GetSeafloorIslandBuildingInfoRequest"
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.filename = "SeafloorIslandExtension"
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.nested_types = {}
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.enum_types = {}
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.fields = {}
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.is_extendable = false
GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG.extensions = {}
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.name = "commitTimes"
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.full_name = ".SeafloorOrderNO.commitTimes"
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.number = 1
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.index = 0
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.label = 1
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.has_default_value = false
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.default_value = 0
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.type = 5
tb.SEAFLOORORDERNO_COMMITTIMES_FIELD.cpp_type = 1

tb.SEAFLOORORDERNO_ORDERS_FIELD.name = "orders"
tb.SEAFLOORORDERNO_ORDERS_FIELD.full_name = ".SeafloorOrderNO.orders"
tb.SEAFLOORORDERNO_ORDERS_FIELD.number = 2
tb.SEAFLOORORDERNO_ORDERS_FIELD.index = 1
tb.SEAFLOORORDERNO_ORDERS_FIELD.label = 3
tb.SEAFLOORORDERNO_ORDERS_FIELD.has_default_value = false
tb.SEAFLOORORDERNO_ORDERS_FIELD.default_value = {}
tb.SEAFLOORORDERNO_ORDERS_FIELD.message_type = SEABEDORDEREXTENSION_PB.SEABEDORDERNO_MSG
tb.SEAFLOORORDERNO_ORDERS_FIELD.type = 11
tb.SEAFLOORORDERNO_ORDERS_FIELD.cpp_type = 10

SEAFLOORORDERNO_MSG.name = "SeafloorOrderNO"
SEAFLOORORDERNO_MSG.full_name = ".SeafloorOrderNO"
SEAFLOORORDERNO_MSG.filename = "SeafloorIslandExtension"
SEAFLOORORDERNO_MSG.nested_types = {}
SEAFLOORORDERNO_MSG.enum_types = {}
SEAFLOORORDERNO_MSG.fields = {tb.SEAFLOORORDERNO_COMMITTIMES_FIELD, tb.SEAFLOORORDERNO_ORDERS_FIELD}
SEAFLOORORDERNO_MSG.is_extendable = false
SEAFLOORORDERNO_MSG.extensions = {}
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.name = "sector"
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.full_name = ".GetSeafloorIslandInfoReply.sector"
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.number = 1
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.index = 0
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.label = 3
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.default_value = {}
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.message_type = ISLANDEXTENSION_PB.UNLOCKINFONO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.full_name = ".GetSeafloorIslandInfoReply.decoratedItems"
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.number = 2
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.index = 1
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.label = 3
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.default_value = {}
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.name = "isAllowToEat"
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.full_name = ".GetSeafloorIslandInfoReply.isAllowToEat"
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.number = 3
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.index = 2
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.type = 8
tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD.cpp_type = 7

tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.full_name = ".GetSeafloorIslandInfoReply.foodItemInfos"
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.number = 4
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.index = 3
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.label = 3
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.default_value = {}
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.name = "houseShowAreaInfos"
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.full_name = ".GetSeafloorIslandInfoReply.houseShowAreaInfos"
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.number = 5
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.index = 4
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.label = 3
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.default_value = {}
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.message_type = HOUSEEXTENSION_PB.HOUSESHOWAREAINFO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.name = "comfortValue"
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.full_name = ".GetSeafloorIslandInfoReply.comfortValue"
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.number = 6
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.index = 5
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.default_value = nil
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.message_type = HOUSEEXTENSION_PB.COMFORTVALUENO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.name = "terrainInfo"
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.full_name = ".GetSeafloorIslandInfoReply.terrainInfo"
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.number = 7
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.index = 6
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.label = 3
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.default_value = {}
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.message_type = ISLANDEXTENSION_PB.TERRAININFONO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.name = "building"
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.full_name = ".GetSeafloorIslandInfoReply.building"
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.number = 8
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.index = 7
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.label = 3
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.default_value = {}
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.message_type = ISLANDEXTENSION_PB.UNLOCKINFONO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.name = "roleInfo"
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.full_name = ".GetSeafloorIslandInfoReply.roleInfo"
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.number = 9
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.index = 8
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.default_value = nil
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLEINFONO_MSG
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.type = 11
tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD.cpp_type = 10

tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.name = "takePhoto"
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.full_name = ".GetSeafloorIslandInfoReply.takePhoto"
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.number = 10
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.index = 9
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.type = 8
tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD.cpp_type = 7

tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.name = "orderBuildingLevel"
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.full_name = ".GetSeafloorIslandInfoReply.orderBuildingLevel"
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.number = 11
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.index = 10
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.default_value = 0
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.type = 5
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD.cpp_type = 1

tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.name = "orderBuildingTime"
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.full_name = ".GetSeafloorIslandInfoReply.orderBuildingTime"
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.number = 12
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.index = 11
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.default_value = 0
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.type = 5
tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD.cpp_type = 1

tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.name = "streetId"
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.full_name = ".GetSeafloorIslandInfoReply.streetId"
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.number = 13
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.index = 12
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.label = 1
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.has_default_value = false
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.default_value = ""
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.type = 9
tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD.cpp_type = 9

GETSEAFLOORISLANDINFOREPLY_MSG.name = "GetSeafloorIslandInfoReply"
GETSEAFLOORISLANDINFOREPLY_MSG.full_name = ".GetSeafloorIslandInfoReply"
GETSEAFLOORISLANDINFOREPLY_MSG.filename = "SeafloorIslandExtension"
GETSEAFLOORISLANDINFOREPLY_MSG.nested_types = {}
GETSEAFLOORISLANDINFOREPLY_MSG.enum_types = {}
GETSEAFLOORISLANDINFOREPLY_MSG.fields = {tb.GETSEAFLOORISLANDINFOREPLY_SECTOR_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_DECORATEDITEMS_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_ISALLOWTOEAT_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_FOODITEMINFOS_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_HOUSESHOWAREAINFOS_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_COMFORTVALUE_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_TERRAININFO_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_BUILDING_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_ROLEINFO_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_TAKEPHOTO_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGLEVEL_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_ORDERBUILDINGTIME_FIELD, tb.GETSEAFLOORISLANDINFOREPLY_STREETID_FIELD}
GETSEAFLOORISLANDINFOREPLY_MSG.is_extendable = false
GETSEAFLOORISLANDINFOREPLY_MSG.extensions = {}
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.full_name = ".DecorateSeafloorIslandReply.decoratedItems"
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.number = 1
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.index = 0
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.type = 11
tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD.cpp_type = 10

tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.full_name = ".DecorateSeafloorIslandReply.foodItemInfos"
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.number = 2
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.index = 1
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.type = 11
tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD.cpp_type = 10

tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.name = "terrains"
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.full_name = ".DecorateSeafloorIslandReply.terrains"
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.number = 3
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.index = 2
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.message_type = ISLANDEXTENSION_PB.TERRAININFONO_MSG
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.type = 11
tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD.cpp_type = 10

tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.name = "islandOutsideInfo"
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.full_name = ".DecorateSeafloorIslandReply.islandOutsideInfo"
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.number = 4
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.index = 3
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.label = 3
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.has_default_value = false
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.default_value = {}
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.type = 5
tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.cpp_type = 1

DECORATESEAFLOORISLANDREPLY_MSG.name = "DecorateSeafloorIslandReply"
DECORATESEAFLOORISLANDREPLY_MSG.full_name = ".DecorateSeafloorIslandReply"
DECORATESEAFLOORISLANDREPLY_MSG.filename = "SeafloorIslandExtension"
DECORATESEAFLOORISLANDREPLY_MSG.nested_types = {}
DECORATESEAFLOORISLANDREPLY_MSG.enum_types = {}
DECORATESEAFLOORISLANDREPLY_MSG.fields = {tb.DECORATESEAFLOORISLANDREPLY_DECORATEDITEMS_FIELD, tb.DECORATESEAFLOORISLANDREPLY_FOODITEMINFOS_FIELD, tb.DECORATESEAFLOORISLANDREPLY_TERRAINS_FIELD, tb.DECORATESEAFLOORISLANDREPLY_ISLANDOUTSIDEINFO_FIELD}
DECORATESEAFLOORISLANDREPLY_MSG.is_extendable = false
DECORATESEAFLOORISLANDREPLY_MSG.extensions = {}

DecorateSeafloorIslandReply = protobuf.Message(DECORATESEAFLOORISLANDREPLY_MSG)
DecorateSeafloorIslandRequest = protobuf.Message(DECORATESEAFLOORISLANDREQUEST_MSG)
GetSeafloorIslandBuildingInfoReply = protobuf.Message(GETSEAFLOORISLANDBUILDINGINFOREPLY_MSG)
GetSeafloorIslandBuildingInfoRequest = protobuf.Message(GETSEAFLOORISLANDBUILDINGINFOREQUEST_MSG)
GetSeafloorIslandInfoReply = protobuf.Message(GETSEAFLOORISLANDINFOREPLY_MSG)
GetSeafloorIslandInfoRequest = protobuf.Message(GETSEAFLOORISLANDINFOREQUEST_MSG)
SeafloorOrderNO = protobuf.Message(SEAFLOORORDERNO_MSG)
SeafloorSectorNO = protobuf.Message(SEAFLOORSECTORNO_MSG)

return _G["logic.proto.SeafloorIslandExtension_pb"]
