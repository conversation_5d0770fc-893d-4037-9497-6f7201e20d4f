module("logic.extensions.farm.core.component.FarmCompCrops", package.seeall)
local FarmCompCrops = class("FarmCompCrops", ECSComponent)

function FarmCompCrops:onInit()
	if not HouseModel.instance:getRoomParams(RoomParams.Plant_Enable) then return end
	self._operatingFurAndCops = {}
	self._mainCamera = CameraTargetMgr.instance:getMainCameraTarget():getCamera()
	self._curScene = SceneManager.instance:getCurScene()
	self._factory = self._curScene.unitFactory
	FarmController.instance:registerLocalNotify(FarmNotifyName.BeginDragCrops, self._onBeginDragCrops, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.OnDragCrops, self._onDragCropsTool, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.EndDragCrops, self._onEndDragCrops, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.OnHarvest, self._onHarvest, self)
	
	RoomController.instance:registerLocalNotify(RoomNotifyName.ClickFurniture, self._onStartOpFur, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnDragFurniture, self._onDragFur, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.CancelClickFurniture, self._onEndOpFur, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnResetEditFurniture, self._onResetEditFur, self)
	
	RoomController.instance:registerLocalNotify(RoomNotifyName.ChangeFurnitureToMultiList, self._selectMultiFurniture, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.ClearFurnitureInMultiDrag, self._onEndOpFur, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnMultiDrag, self._onDragFur, self)
	
	self:resetEarthList()
	CropsInfoView.instance:init()
	self._tempCrops = nil
end

function FarmCompCrops:onDestroy()
	if not HouseModel.instance:getRoomParams(RoomParams.Plant_Enable) then return end
	SceneTimer:removeTimer(self._onResetEditFur, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.BeginDragCrops, self._onBeginDragCrops, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.OnDragCrops, self._onDragCropsTool, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.EndDragCrops, self._onEndDragCrops, self)
	FarmController.instance:unregisterLocalNotify(FarmNotifyName.OnHarvest, self._onHarvest, self)
	
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.ClickFurniture, self._onStartOpFur, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnDragFurniture, self._onDragFur, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.CancelClickFurniture, self._onEndOpFur, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnResetEditFurniture, self._onResetEditFur, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.FurnitureLoaded, self._handleUnitLoaded, self)
	
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.ChangeFurnitureToMultiList, self._selectMultiFurniture, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.ClearFurnitureInMultiDrag, self._onEndOpFur, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnMultiDrag, self._onDragFur, self)
	
	CropsInfoView.instance:destroy()
	PlantProgressView.instance:destroy()
	HarvestToolView.instance:destroy()
	UprootToolView.instance:destroy()
	self._mainCamera = nil
	self._curScene = nil
	self._factory = nil
	self._furComp = nil
	self._operatingFurAndCops = nil
end

function FarmCompCrops:onEnter()
	if not HouseModel.instance:getRoomParams(RoomParams.Plant_Enable) then return end
	self:_onLoadAllFur()
end

function FarmCompCrops:_onLoadAllFur()
	RoomController.instance:registerLocalNotify(RoomNotifyName.FurnitureLoaded, self._handleUnitLoaded, self)
	local allPlants = FarmModel.instance:getAllPlant()
	local unit
	for _, plantMO in ipairs(allPlants) do
		local furnitureMO = self._factory:getFurnitureModel(plantMO.userId):getFurnitureByServerId(plantMO.serverId)
		if furnitureMO == nil then
			print("找不到农作物对应土地：", plantMO.cfg.name, plantMO.serverId)
			FarmModel.instance:removePlant(plantMO)
		else
			unit = self:_getFurComp():getFurUnitByUniqueId(furnitureMO.uniqueId)
		end 
		if plantMO:isCrop() then
			if unit then
				self:_buildCrops(unit, plantMO, false)
			else
				FarmModel.instance:removePlant(plantMO)
				print("找不到农作物对应土地：", plantMO.cfg.name, plantMO.serverId)
			end
		end
	end
	SceneTimer:setTimer(5, self._onResetEditFur, self, true)
end

function FarmCompCrops:_handleUnitLoaded(unit, factory)
	if factory ~= SceneManager.instance:getCurScene().unitFactory then return end
	local plantMO = FarmModel.instance:getPlantByUniqueId(unit.uniqueId) or FarmModel.instance:getPlantByFurUniqueId(unit.uniqueId)
	if plantMO and HarvestConfig.isEarth(unit.itemId) then
		self:_buildCrops(unit, plantMO, true)
	end
end

function FarmCompCrops:_onBeginDragCrops(cropsId, screenPos)
	self._cropsId = cropsId
	self._tempCrops = {}
end

function FarmCompCrops:_onDragCropsTool(screenPos)
	if not self._cropsId then return end
	local furList = RoomHitTest.getHitFurnitures(screenPos, self._mainCamera)
	local furnitureMO, uniqueId, info, plantMO
	local furnitureModel = self._factory:getFurnitureModel(UserInfo.userId)
	for _, furUnit in ipairs(furList) do
		furnitureMO = furnitureModel:getFurnitureInfoById(furUnit.uniqueId)
		if furnitureMO and HarvestConfig.isEarth(furnitureMO.id) and not furnitureMO:getPlant() then
			if self:_checkEnoughCurrency() and self:_checkTodayLimit() then
				info = {serverId = furnitureMO.serverId, id = self._cropsId, userId = furnitureMO.userId}
				if RoomTaskHelper.instance:needFadePlantCrop() then
					plantMO = TaskPlantMO.New()
					plantMO:copyToSelf(info)
					if #FarmModel.instance:getAllPlant() + 1 == 4 then
						GlobalDispatcher:dispatch(GlobalNotify.OnPlantCrops)
					end
				else
					plantMO = PlantMO.New()
					table.insert(self._tempCrops, plantMO)
					plantMO:copyToSelf(info)
					plantMO:resetPlantTime()
					self:_notifyCurrencyChange()
				end
				FarmModel.instance:addPlant(plantMO)
				SoundManager.instance:playEffect(130052)
				self:_buildCrops(furUnit, plantMO, true)
			else
				FarmController.instance:localNotify(FarmNotifyName.SendEndDragCrops)
			end
		end
	end
end

function FarmCompCrops:_notifyCurrencyChange()
	if not self._tempCrops or #self._tempCrops == 0 then
		GlobalDispatcher:dispatch(GlobalNotify.HideHUDCoinChange)
		return
	end
	local changeNum = 0
	for i = 1, #self._tempCrops do
		changeNum = changeNum - self._tempCrops[i].cfg.consumeCurrency.count
	end
	GlobalDispatcher:dispatch(GlobalNotify.ShowHUDCoinChange, changeNum)
end

function FarmCompCrops:_checkEnoughCurrency()
	local define = HarvestConfig.getDefineBySeedId(self._cropsId)
	local needCurrency = define.consumeCurrency.count *(#self._tempCrops + 1)
	local enough = ItemService.instance:getItemNum(define.consumeCurrency.id) >= needCurrency
	if not enough then
		RechargeFacade.showCurrencyNotEnoughDialog(define.consumeCurrency.id, define.consumeCurrency.count)
	end
	return enough
end

function FarmCompCrops:_checkTodayLimit()
	local define = HarvestConfig.getDefineBySeedId(self._cropsId)
	local todayCount = LimitService.instance:getGainCount(define.id)
	local limitCount = BuffService.instance:calculateResultValue(BuffType.AllType.ProduceLimitIncrease, define.limitTimes, define.subType, define.id)
	local overMax = todayCount + #self._tempCrops + 1 > limitCount
	if overMax then
		FlyTextManager.instance:showFlyText(lang("今天的次数已经用完啦，明天再来吧"))
	end
	return not overMax
end

function FarmCompCrops:_onEndDragCrops()
	if not self._tempCrops then return end
	local noCropsCount = 0
	for _, furnitureMO in pairs(self.earthList) do
		if not FarmModel.instance:getPlantByServerId(furnitureMO.serverId, furnitureMO.userId) then
			noCropsCount = noCropsCount + 1
		end
	end
	if noCropsCount == 0 then
		ViewMgr.instance:close("CropsListView")
	end
	if #self._tempCrops == 0 or RoomTaskHelper.instance:needFadePlantCrop() then return end
	IslandAgent.instance:sendManorPlantRequest(self._tempCrops, handler(self._onSendCrops, self))
	self._tempCrops = {}
end

function FarmCompCrops:_onSendCrops(plants)
	for i = 1, #plants do
		local plantMo = FarmModel.instance:getPlantByServerId(plants[i].id)
		if not plantMo then
			plantMo = PlantMO.New()
			FarmModel.instance:addPlant(plantMo)
		end
		plantMo:setDataFromServer(plants[i])
	end
	self:_notifyCurrencyChange()
end

function FarmCompCrops:resetEarthList()
	self.earthList = {}
	if self._factory:getHouseMo() == nil then return end
	local allFurnitures = self._factory:getHouseMo():getRoomFurnitureList()
	for _, furnitureMO in pairs(allFurnitures) do
		if HarvestConfig.isEarth(furnitureMO.id) then
			table.insert(self.earthList, furnitureMO)
		end
	end
end

function FarmCompCrops:_buildCrops(earthUnit, plantMO, newPlant, sortImmediate)
	if self._factory == nil or not HarvestConfig.isEarth(earthUnit.itemId) or not plantMO or not plantMO:isCrop() then return end
	local cropsUnit = self._factory:buildCrops(plantMO.uniqueId, plantMO.id, plantMO.userId)
	if newPlant then
		DOTweenHelper.ZoomScale(cropsUnit.go, 0.1, 1)
		cropsUnit:playAnimOnec(HarvestConfig.getCommonConfig("grow"))
	end
	local furnitureMO = self._factory:getHouseMo():getFurnitureInfoById(earthUnit.uniqueId)
	self:_setCropsPosition(furnitureMO, earthUnit, cropsUnit, true)
	self:_insertCropsSort(cropsUnit, sortImmediate)
end

function FarmCompCrops:_onHarvest(plantMO, unit)
	self:_removeCropsSort(plantMO, unit)
end

function FarmCompCrops:_onStartOpFur(furnitureMO, furnitureUnit)
	if not HarvestConfig.isEarth(furnitureMO.id) then return end
	local plantMO = furnitureMO:getPlant()
	if not plantMO then return end
	local info = {furMO = furnitureMO, furUnit = furnitureUnit, plantMO = plantMO, plantUnit = self:_getPlantUnit(plantMO.uniqueId)}
	self._operatingFurAndCops[furnitureMO.uniqueId] = info
	self:_setCropsPosition(furnitureMO, furnitureUnit, info.plantUnit, false, true)
	self:_removeCropsSort(plantMO, info.plantUnit)
end

function FarmCompCrops:_onDragFur(canPlace)
	for uniqueId, info in pairs(self._operatingFurAndCops) do
		self:_setCropsPosition(info.furMO, info.furUnit, info.plantUnit, false, true)
	end
end

function FarmCompCrops:_onEndOpFur()
	for uniqueId, info in pairs(self._operatingFurAndCops) do
		self:_setCropsPosition(info.furMO, info.furUnit, info.plantUnit, false, true)
		self:_insertCropsSort(info.plantUnit, true)
	end
	self._operatingFurAndCops = {}
end

function FarmCompCrops:_selectMultiFurniture(furnitureMoList, isAdd)
	for _, furnitureMO in ipairs(furnitureMoList) do
		if isAdd then
			for uniqueId, info in pairs(self._operatingFurAndCops) do
				self:_setCropsPosition(info.furMO, info.furUnit, info.plantUnit, false, true)
			end
			self:_onStartOpFur(furnitureMO, furnitureMO:getUnit())
		else
			local info = self._operatingFurAndCops[furnitureMO.uniqueId]
			if info then
				self:_setCropsPosition(info.furMO, info.furUnit, info.plantUnit, false, true)
				self:_insertCropsSort(info.plantUnit, true)
			end
			self._operatingFurAndCops[furnitureMO.uniqueId] = nil
		end
	end
end

function FarmCompCrops:_removeCropsSort(cropsMO, unit)
	if not cropsMO or not cropsMO:isCrop() then return end
	self:_getMaper():removeSceneObjectSortMap(unit.go)
end

function FarmCompCrops:_insertCropsSort(cropsUnit, sortImmediate)
	local pos = GameUtils.getPos(cropsUnit.go)
	self:_getMaper():insertSceneObjectToSortMap(cropsUnit, pos)
	if sortImmediate then
		self._maper:sortSceneObjectImmediate()
	end
end

function FarmCompCrops:_setCropsPosition(furnitureMO, earthUnit, cropsUnit, userCoord, setZ)
	if cropsUnit == nil then
		print(debug.traceback())
	end
	if self:_getMaper() == nil then return end
	local pos
	if userCoord then
		pos = self:_getMaper():getFurniturePosByCoordPos(furnitureMO)
	else
		pos = GameUtils.getPos(earthUnit.go)
	end
	if setZ then
		GameUtils.setPos(cropsUnit.go, pos.x, pos.y - 0.001, pos.z - 0.001)
	else
		tfutil.SetXY(cropsUnit.go, pos.x, pos.y - 0.001)
	end
	cropsUnit.view:mirror(furnitureMO:getMirror())
end

function FarmCompCrops:_onResetEditFur()
	if self._factory == nil or self:_getCompPlant() == nil or self:_getCompPlant()._unitDict == nil then return end
	local plantMO, furnitureMO, earthUnit
	for _, cropsUnit in pairs(self:_getCompPlant()._unitDict) do
		if cropsUnit then
			plantMO = FarmModel.instance:getPlantByUniqueId(cropsUnit.uniqueId)
			if plantMO and plantMO:isCrop() then
				furnitureMO = self._factory:getFurnitureModel(plantMO.userId):getFurnitureByServerId(plantMO.serverId)
				earthUnit = self:_getFurComp():getFurUnitByUniqueId(furnitureMO.uniqueId)
				self:_setCropsPosition(furnitureMO, earthUnit, cropsUnit)
			end
		end
	end
end

function FarmCompCrops:_getPlantUnit(uniqueId)
	return self:_getCompPlant()._unitDict[uniqueId]
end

function FarmCompCrops:_getCompPlant()
	if not self._compPlant then
		self._compPlant = self.entity:getComponent(FarmCompPlant)
	end
	return self._compPlant
end

function FarmCompCrops:_getMaper()
	if self._maper then return self._maper end
	self._maper = self._factory:getRoomSuite():getComponent(RoomCompMaper)
	return self._maper
end

function FarmCompCrops:_getFurComp()
	if self._furComp then return self._furComp end
	self._furComp = self._factory:getRoomSuite():getComponent(RoomCompFurniture)
	return self._furComp
end

return FarmCompCrops 