module("logic.extensions.recycle.view.ShreFurnitureItemCell",package.seeall)

local ShreFurnitureItemCell = class("ShreFurnitureItemCell",ListBinderCell)

-------to be overrided
function ShreFurnitureItemCell:Awake()
	self.container = goutil.findChild(self._go, "container")
	-- self.clickTrigger = Framework.UIClickTrigger.Get(self.container)	
	-- self.clickTrigger:AddClickListener(self.onClick, self, nil)
	-- self.iconRank = goutil.findChild(self.container, "iconrank")
	self.imgSelect = goutil.findChild(self.container, "imgSelect")	
	-- self.hasCapGO = goutil.findChild(self.container, "hasCapacity")
	-- self.hasActionGO = goutil.findChild(self.container, "hasAction")
	self.newGO = goutil.findChild(self.container, "new")
	self.newGO:SetActive(false)
	-- self.txtNum = goutil.findChildTextComponent(self.container, "txtNum")
	-- self.icon = goutil.findChild(self.container, "icon")
	self.emptyGO = goutil.findChild(self._go, "empty")
	self.commonIcon = CommonIconMgr.instance:fetchCommonIcon()
	self.commonIcon:setWidthAndHeight(112):showFreeState(true):showRarenessGo(true):showSpecial(true)
	goutil.addChildToParent(self.commonIcon:getPrefab(), self.container)
	self.commonIcon:getPrefab().transform:SetSiblingIndex(0)
	self.commonIcon:addIconClickListener(self.onClick, self)
	self.goMultiSelect = goutil.findChild(self._go, "container/selectTog")
	self.imgMultiSelect = goutil.findChild(self.goMultiSelect, "Background/Checkmark")
	self.occupyGO = goutil.findChild(self._go, "container/occupyGo")
end

function ShreFurnitureItemCell:onSetMo(mo)
	self.mo = mo
	if mo.id > 0 then
		self.container:SetActive(true)
		self.emptyGO:SetActive(false)		
		self.commonIcon:buildData(mo)
		-- self.txtNum.text = mo.num
		-- IconLoader.setIconForItem(self.icon, mo.id)
		-- local rank = mo:getDefine().quality		
		-- IconLoader.setAtlasToImg(self.iconRank, AtlasUrl.ItemRank, "rank" .. rank)
		-- goutil.setActive(self.hasCapGO, checkbool(mo:getDefine().hasCapacity))
		-- goutil.setActive(self.hasActionGO, checkbool(mo:getDefine().hasAction))
		-- goutil.setActive(self.newGO, checkbool(mo:getNewState()))
		self.imgSelect:SetActive(self.isSelected)
		if mo.useCount then
			self.occupyGO:SetActive(mo.useCount > 0)
		else
			self.occupyGO:SetActive(false)
		end
	else
		-- IconLoader.clearImage(self.iconRank)
		-- IconLoader.clearImage(self.icon)
		self.container:SetActive(false)
		self.emptyGO:SetActive(true)
	end
	self.goMultiSelect:SetActive(self._listView.isMultiSelect)
	if self._listView.isMultiSelect then	
		self.imgMultiSelect:SetActive(self.isSelected)
		self.imgSelect:SetActive(false)
	end
end

function ShreFurnitureItemCell:onClick()
	self:localNotify(ItemNotify.SelectItem, self.mo)		
end	

function ShreFurnitureItemCell:onSelect(isSelected)
	if self._listView.isMultiSelect then
		self.imgMultiSelect:SetActive(isSelected)
	else
		self.imgSelect:SetActive(isSelected)
	end
end

function ShreFurnitureItemCell:OnDestroy()
	CommonIconMgr.instance:returnCommonIcon(self.commonIcon)
	self.commonIcon = nil
	-- self.clickTrigger:RemoveClickListener()
end

return ShreFurnitureItemCell