module("logic.scene.common.action.UseObjectAction",package.seeall)
local UseObjectAction = class("UseObjectAction", SceneActionBase)

UseObjectAction.hasShow = false

function UseObjectAction:onStart()
	-- printInfo("start sit", self.params.sitPos.x, self.params.sitPos.y)
	self.userPlayer = SceneManager.instance:getCurScene():getUserPlayer()
	self.isWorking = true
	local triggerId = self:getTriggerId()
	printInfo("UseObjectAction:onStart", triggerId)
	if triggerId then
		SceneAgent.instance:sendSceneShareRequest(PlayerActionType.UseObject.typeId, {triggerId, self.params.type, self.params.param}, handler(self.onSitResponse, self))
	else
		self.isWorking = false
		self:finish(false)
	end
end

function UseObjectAction:onSitResponse(status)
	self.isWorking = false
	if status == 0 then
		-- self.userPlayer:setPos(self.params.sitPos.x, self.params.sitPos.y)
		-- SceneAgent.instance:sendMoveRequest(self.sitPos, {})
		self.isRest = ItemService.instance:startRest()
	else
		DialogHelper.showErrorMsg(status)
		self:finish(false)
	end
end

function UseObjectAction:getTriggerId()
	if self.params.triggerId then
		return self.params.triggerId
	elseif self.params.getTriggerId then
		return self.params.getTriggerId()
	end
end

function UseObjectAction:onStop()
	if self.isRest then
		ItemService.instance:stopRest()
	end

	self.isWorking = true
	SceneController.instance:registerLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
	SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.UseObject.typeId, handler(self.onCancelRequest, self))
end

function UseObjectAction:onCancelRequest(status)
	-- if self.isDisposed then
	-- 	return
	-- end
	if status == 0 then
	else
		DialogHelper.showErrorMsg(status)
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		self:finish(false)
	end
end

function UseObjectAction:onActionChange(nowState, oldState)
	if oldState == PlayerActionType.UseObject and nowState ~= PlayerActionType.UseObject then
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		self:finish(true)
	end
end

function UseObjectAction:getType()
	return SceneActionType.UseObj
end

-- function UseObjectAction:getActionId()
-- 	return PlayerActionType.UseObjectAction.typeId
-- end

-- function UseObjectAction:onDispose()
-- 	SceneAgent.instance:sendCancelSceneShareRequest(self:getActionId())
-- 	self.isDisposed = true
-- 	self.userPlayer = nil
-- end


return UseObjectAction