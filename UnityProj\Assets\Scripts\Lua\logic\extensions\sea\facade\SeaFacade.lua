module("logic.extensions.sea.facade.SeaFacade", package.seeall)

local SeaFacade = class("SeaFacade", BaseFacade)

-- 进入海底世界
function SeaFacade.gotoSea(depth, force, handler)
    local suc = FuncUnlockFacade:checkIsUnlocked(FuncIds.Sea, true)
    if suc then
        if not depth then
            depth = 1
        end
        if SceneManager.instance:isInSea() then
            SeaFacade.gotoDepth(depth, force, handler)
        else
            SeaController.instance:getSeaSceneInfoRequest(function()
                SeaFacade.gotoDepth(depth, force, handler)
            end)
        end
    end
    return suc
end

-- 进入海底世界
function SeaFacade.gotoScene(sceneId, channelId, force, handler)
    local suc = FuncUnlockFacade:checkIsUnlocked(FuncIds.Sea, true)
    if suc then
        if SceneManager.instance:isInSea() then
            SeaFacade.loadScene(sceneId, channelId, force, handler)
        else
            SeaController.instance:getSeaSceneInfoRequest(function()
                SeaFacade.loadScene(sceneId, channelId, force, handler)
            end)
        end
    end
    return suc
end

-- 在海底世界中，切换深度
function SeaFacade.gotoDepth(depth, force, handler)
    if not SeaFacade.getDepthIsOpen(depth, true) then
        return
    end
    local terrainInfo = SeaMap.instance:getMainTerrainInfo(depth)
    if not terrainInfo then
        printError("isn't have this terrain.")
        return
    end
    SeaFacade.loadScene(terrainInfo.sceneId, nil, force, handler)
end

-- 在海底世界中，切换地形
function SeaFacade.gotoTerrain(sceneId, dir, id)
    if dir == SeaEnum.Dir.Down then
        if not SeaFacade.getDepthIsOpen(SeaFacade.getCurDepth() + 1, true) then
            return
        end
    end
    local terrainInfo = SeaMap.instance:getNeighbor(sceneId, dir)
    if not terrainInfo then
        printError("isn't have this terrain.")
        return
    end
    local reefDefine = SeaConfig.getReef(terrainInfo.reefId)
    local bornPos = reefDefine["bornPos" .. dir][id]
    local x = bornPos[1] or 0
    local y = bornPos[2] or 0
    local sceneConfig = SceneConfig.getSceneConfig(terrainInfo.sceneId)
    sceneConfig.bornPointMap[0] = {x, y}
    SceneManager.instance:loadScene(terrainInfo.sceneId)
end

-- 在海底世界中，切换频道
function SeaFacade.changeChannel(channelId, handler)
    local sceneId = SceneManager.instance:getCurSceneId()
    SeaFacade.loadScene(sceneId, channelId, true, handler)
end

-- 跳转到指定场景
function SeaFacade.loadScene(sceneId, channelId, force, handler)
    local terrainInfo = SeaMap.instance:getTerrainInfo(sceneId)
    local x, y = SeaFacade.getBornPos(terrainInfo.sceneId)
    local sceneConfig = SceneConfig.getSceneConfig(sceneId)
    sceneConfig.bornPointMap[0] = {x, y}
    local params = {}
    params.sceneId = sceneId
    params.channelId = channelId
    params.handler = handler
    params.ignoreSame = force
    if force == true then
        SceneManager.instance:loadSceneRaw(params)
    else
        SceneManager.instance:loadSceneWithParams(params)
    end
end

-- 获取该场景出生点
function SeaFacade.getBornPos(sceneId)
    local suc = SeaMap.instance:isInited()
    local x, y
    if suc then
        local terrainInfo = SeaMap.instance:getTerrainInfo(sceneId)
        if terrainInfo then
            local terrainDefine = SeaConfig.getTerrain(terrainInfo.terrainId)
            x = terrainDefine.bornPos[1]
            y = terrainDefine.bornPos[2]
            local radius = terrainDefine.bornPos[3] * 1e2
            local nx = math.random(-radius, radius) / 1e2
            local ny = math.random(-radius, radius) / 1e2
            x = x + nx
            y = y + ny
        else
            suc = false
            printError("scene isn't in sea!")
        end
    else
        printError("SeaMap isn't inited!")
    end
    return x, y, suc
end

-- 获取当前所在深度
function SeaFacade.getCurDepth()
    if SceneManager.instance:isInSea() then
        local sceneId = SceneManager.instance:getCurSceneId()
        local terrainInfo = SeaMap.instance:getTerrainInfo(sceneId)
        local terrainDefine = SeaConfig.getTerrain(terrainInfo.terrainId)
        return terrainDefine.depth
    end
    return 0
end

-- 获取深度是否已开启
function SeaFacade.getDepthIsOpen(depth, tips)
    if depth == 0 then
        return true
    end
    local define = SeaConfig.getDepth(depth)
    local isOpen = false
    if define then
        if define.unlockTime ~= nil then
            isOpen = TimeUtil.isNowAfter(define.unlockTime)
        else
            isOpen = true
        end
        if tips and not isOpen then
            FlyTextManager.instance:showFlyText(define.unlockTips)
        end
    end
    return isOpen
end

-- 获取场景是否已开启
function SeaFacade.getSceneIsOpen(sceneId, tips)
    local terrainInfo = SeaMap.instance:getTerrainInfo(sceneId)
    local terrainDefine = SeaConfig.getTerrain(terrainInfo.terrainId)
    return SeaFacade.getDepthIsOpen(terrainDefine.depth, tips)
end

-- 获得重量评级
function SeaFacade.getFishGrading(fishId, weight)
    local fishDefine = SeaConfig.getFish(fishId)
    local allFishWeightGradingDefines = SeaConfig.getAllFishWeightGrading()
    local n = weight / fishDefine.weight[1]
    local grading = 1
    for i = #allFishWeightGradingDefines, 1, -1 do
        local factor = allFishWeightGradingDefines[i].weightFactor
        if n >= factor then
            grading = i
            break
        end
    end
    return grading
end

-- 调试场景指定刷鱼
function SeaFacade.testFish(fishIds, num)
    if not num then
        num = 1
    end
    local scene = SceneManager.instance:getCurScene()
    for _, fishId in ipairs(fishIds) do
        for i = 1, num do
            scene.nestMgr:spawnFish(SeaConfig.getFish(fishId))
        end
    end
end

-- 调试炫耀指定鱼
function SeaFacade.testShowFish(fishId)
    SceneController.instance:seaShowFish(SeaEnum.Ani.Suc, fishId, 0)
end

-- 是否显示坐骑
function SeaFacade.getMountType()
    print("SeaFacade.getMountType", SceneManager.instance:getCurSceneId())
    local mountType = SceneConfig.getSceneConfig(SceneManager.instance:getCurSceneId()).useMountType
    if mountType then
        if table.indexof(mountType, SeaEnum.MountType.Land) then
            return SeaEnum.MountType.Land
        end
        return SeaEnum.MountType.Sea
    end
    return 0
end

-- 打开炫耀界面
function SeaFacade.openFishShowView()
    SeaAgent.instance:sendSeaSceneGetAllFishDetailRequest(function(info)
        ViewMgr.instance:open("SeaFishShowView", info)
    end)
end

-- 打开海底图鉴
function SeaFacade.openArchiveView()
    ArchiveFacade.instance:open(GameEnum.ArchiveFieldType.SEA_SCENE_FISH)
end

--海底壁画交互
function SeaFacade.seaMuralInteraction()
    SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.SeaMural)
end

function SeaFacade.playSite(params)
    local isIdle = false
    local mgr = SceneManager.instance:getCurScene():getComponent(SceneActionMgr)
    if mgr:isIdle() then
        isIdle = true
    end
    if mgr:hasAction(SceneActionType.StartRideMount) and not mgr:hasAction(SceneActionType.Joystick) then
        isIdle = true
    end
    if isIdle then
        Activity433Agent.instance:sendAct433GetInfoRequest(function(msg)
            SeaSpaCarController.instance:SaveDailyAwardTimesLimit(msg.todayAwardTimes)
            SeaSpaCarController.instance:saveDailyAwardDepth(msg.nowDepth)
            ViewMgr.instance:open("SeaBusRuleView",params)
            --ViewMgr.instance:open("ActivitySeaBusView")
        end)
    end
end

function SeaFacade.showFishTask()
    Activity432Agent.instance:sendGetAct432InfoRequest(function(targets, todayHasGainedReward)
        ViewMgr.instance:open("ActivitySeaFishTaskRewardView", {
            targets = targets,
            todayHasGainedReward = todayHasGainedReward
        })
    end)
end

return SeaFacade
