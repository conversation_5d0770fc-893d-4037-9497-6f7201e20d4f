module("logic.scene.scene3D.free3D.SceneFree3DCamera", package.seeall)

local SceneFree3DCamera = class("SceneFree3DCamera", SceneComponentBase)

local R = {
    camCollisionRadius = 0.4 -- 相机碰撞半径
}

function SceneFree3DCamera:onInit()
    self.mainCamera = CameraTargetMgr.instance:getMainCameraTarget():getCamera()
    self.sightComp = PjAobi.CameraSightComponent.Get(self.mainCamera)
    self.audioListener = goutil.find("SoundEventInsContain")
end

function SceneFree3DCamera:onEnterScene(sceneId, bornX, bornZ)
    VirtualCameraMgr.instance:setCamera3D(true)
    VirtualJoystickFacade.instance:setJoystickModel(false)

    self.camPivot = goutil.create("camPivot", false)
    goutil.addChildToParent(self.camPivot)

    self.camPivotTrs = self.camPivot.transform
    self.camTrs = self.mainCamera.transform
    goutil.addChildToParent(self.camTrs, self.camPivot)

    self.audioListener.transform.position = Vector3.zero
    goutil.addChildToParent(self.audioListener, self.camPivot)

    self._hits = System.Array.CreateInstance(typeof("UnityEngine.RaycastHit", true), 10)
    self._overlaps = System.Array.CreateInstance(typeof("UnityEngine.Collider", true), 10)
    self._triggerLayerMask = LayerMask.GetMask(SceneLayer.SceneTrigger, SceneLayer.Default)

    self.__curFar = self.__camFar
    self.__curHig = self.__camHig

    self:setCamRotate(self.__camRotaH, self.__camRotaV)

    self:resetCam()

    SceneController.instance:registerLocalNotify(SceneNotify.PreloadFinished, self.onLoadedScene, self, NotifyPriority.High)
end

function SceneFree3DCamera:onLoadedScene()
    SceneController.instance:unregisterLocalNotify(SceneNotify.PreloadFinished, self.onLoadedScene, self, NotifyPriority.High)

    local setting = self._scene.stage.stageGO:GetComponent(typeof(PjAobi.SceneSetting))
    CurveSurface.Instance:SetCurveInfo(setting.radius, -setting.radius, 0, setting.heightData)
end

function SceneFree3DCamera:onEnterSceneFinished(sceneId, bornX, bornZ)
    self.sceneConfig = SceneConfig.getSceneConfig(sceneId)
    self.zoomConfig = self.sceneConfig.zoomRange
    self:setViewSize(self.zoomConfig.default)

    self.mainCamera:ResetProjectionMatrix()
    self.mainCamera.nearClipPlane = 1
    self.mainCamera.farClipPlane = 300

    local player = self._scene:getUserPlayer()
    self:setIsFree(false)
    self:setFollower(player.go)
    UpdateBeat:Add(self.onUpdate, self)
    LateUpdateBeat:Add(self.onLateUpdate, self)
end

function SceneFree3DCamera:onExitScene()
    goutil.addChildToParent(self.audioListener, nil)
    goutil.addChildToParent(self.camTrs)
    goutil.destroy(self.camPivot)

    self:setIsFree(false)
    self:setFollower(nil)
    UpdateBeat:Remove(self.onUpdate, self)
    LateUpdateBeat:Remove(self.onLateUpdate, self)
    SceneTimer:removeTimer(self.setFirstRotate, self)
end

function SceneFree3DCamera:onUpdate()
    local players = self._scene:getAllPlayers()
    local unitMgr = self._scene:getUnitMgr()
    local camPosX, camPosY = self:getPos()
    local min = self.__showPlayerMin
    local max = self.__showPlayerMax
    for _, unit in ipairs(players) do
        local mount = unit:getMount()
        local x, y = unit:getPos()
        x = x - camPosX
        y = y - camPosY
        local dist = Vector2.Magnitude(gvec2(x, y))
        if dist > min and dist < max then
            unitMgr:setUnitVisible(unit, true)
            if mount then
                unitMgr:setUnitVisible(mount, true)
            end
        else
            unitMgr:setUnitVisible(unit, false)
            if mount then
                unitMgr:setUnitVisible(mount, false)
            end
        end
    end
end

function SceneFree3DCamera:onLateUpdate()
    if not self._scene.controller.isLockCamera then
        if self.isFree then
            local h, v, magnitude = VirtualJoystickFacade.instance:getVector(VirtualJoystickFacade.FreeCameraMode)
            if magnitude > 0.1 then
                self:move(h, v)
            end
        else
            if self.follower then
                self:follow()
            end
        end
        self:collision()
        self:updateCam()
    end
    self:unitsFaceTo()
end

function SceneFree3DCamera:unitsFaceTo()
    local unitMgr = self._scene:getUnitMgr()
    if unitMgr.unitsFaceTo then
        unitMgr:unitsFaceTo()
    end
end

function SceneFree3DCamera:setParams(params)
    self.__camFar = params.camFar
    self.__camFarMin = params.camFarMin
    self.__camFarMax = params.camFarMax

    self.__camHig = params.camHig
    self.__camHigMin = params.camHigMin
    self.__camHigMax = params.camHigMax

    self.__camFocus = params.camFocus
    self.__camRotaH = params.camRotaH
    self.__camRotaV = params.camRotaV
    self.__camRotaVMin = params.camRotaVMin
    self.__camRotaVMax = params.camRotaVMax

    self.__camMoveSpeedH = params.camMoveSpeedH
    self.__camMoveSpeedV = params.camMoveSpeedV
    self.__camRotaSpeedH = params.camRotaSpeedH
    self.__camRotaSpeedV = params.camRotaSpeedV

    self.__showPlayerMin = params.showPlayerMin
    self.__showPlayerMax = params.showPlayerMax
end

function SceneFree3DCamera:setIsFree(isFree)
    self.isFree = isFree
    if isFree then
        VirtualJoystickFacade.instance:setMode(VirtualJoystickFacade.FreeCameraMode)
        self._scene.joystick:setBanJoystickListenerMove(true)
    else
        VirtualJoystickFacade.instance:setMode(VirtualJoystickFacade.CommonMoveMode)
        self._scene.joystick:setBanJoystickListenerMove(false)
    end
    GlobalDispatcher:dispatch(GlobalNotify.OnSceneCameraBeFree, isFree)
end

function SceneFree3DCamera:setFollower(follower)
    if follower ~= self.follower then
        self.follower = follower
        if follower then
            self.followerTrs = follower.transform
        else
            self.followerTrs = nil
        end
    end
end

function SceneFree3DCamera:follow()
    local position = self.follower.transform.position
    if self._lastFollowY then
        position.y = Mathf.Lerp(self._lastFollowY, position.y + self.__camFocus, 0.2)
    else
        position.y = position.y + self.__camFocus
    end
    self._lastFollowY = position.y
    self.camPivotTrs.position = position
end

function SceneFree3DCamera:move(x, z)
    local dx = x * self.__camMoveSpeedH * Time.deltaTime
    local dz = z * self.__camMoveSpeedV * Time.deltaTime
    self.camPivotTrs:Translate(dx, 0, dz)
end

function SceneFree3DCamera:offset(x, z)
    local dh = -x * self.__camRotaSpeedH * Time.deltaTime
    local dv = z * self.__camRotaSpeedV * Time.deltaTime
    self.__curRotaH = self.__curRotaH + dh
    self.__curRotaV = self.__curRotaV + dv
    self.__curRotaV = math.max(self.__curRotaV, self.__camRotaVMin)
    self.__curRotaV = math.min(self.__curRotaV, self.__camRotaVMax)
    self.camPivotTrs.eulerAngles = gvec3(self.__curRotaV, self.__curRotaH, 0)
end

function SceneFree3DCamera:collision()
    local radius = R.camCollisionRadius
    local camPivotPos = self.camPivotTrs.position
    local camPos = self.camTrs.position
    local dir = camPos - camPivotPos
    dir:SetNormalize()

    local overlapCount = UnityEngine.Physics.OverlapSphereNonAlloc(camPivotPos, radius, self._overlaps, self._triggerLayerMask)
    local isOverlap = false
    for i = 0, overlapCount - 1 do
        local collider = self._overlaps:GetValue(i)
        if collider.gameObject:CompareTag("Ground") or collider.transform.parent.gameObject:CompareTag("Ground") then
            isOverlap = true
            break
        end
    end

    local hitCounts = UnityEngine.Physics.SphereCastNonAlloc(camPivotPos, radius, dir, self._hits, self.__camFarMax, self._triggerLayerMask)
    local hits = {}
    local hit = nil
    for i = 0, hitCounts - 1 do
        hit = self._hits:GetValue(i)
        if hit.collider.gameObject:CompareTag("Ground") or hit.collider.transform.parent.gameObject:CompareTag("Ground") then
            table.insert(hits, hit)
        end
    end
    table.sort(
        hits,
        function(a, b)
            return a.distance < b.distance
        end
    )

    hit = hits[1]
    if hit then
        local p1 = camPivotPos
        local p2 = camPos
        local p3 = hit.point
        local p4 = nil
        if isOverlap then
            p4 = camPivotPos + dir * 0.1
        else
            p4 = p3 + hit.normal * radius
        end
        self:drawGizmos(p1, p2, p3, p4)
        local p = self.camPivotTrs:InverseTransformPoint(p4.x, p4.y, p4.z)
        local far = math.abs(p.z)
        local hig = math.abs(p.y)
        if far < self.__showPlayerMin then
            hig = self.__camHigMin
        end
        self:setCam(far, hig)
    else
        self:setCam(Mathf.Lerp(self.__curFar, self.__camFar, 0.04), Mathf.Lerp(self.__curHig, self.__camHig, 0.04))
    end
end

function SceneFree3DCamera:setCam(far, hig)
    if far then
        far = math.max(far, self.__camFarMin)
        far = math.min(far, self.__camFarMax)
        if far ~= self.__curFar then
            self.__newFar = far
        end
    end
    if hig then
        hig = math.max(hig, self.__camHigMin)
        hig = math.min(hig, self.__camHigMax)
        if hig ~= self.__curHig then
            self.__newHig = hig
        end
    end
end

function SceneFree3DCamera:updateCam()
    local far = self.__newFar
    local hig = self.__newHig
    self.__newFar = nil
    self.__newHig = nil
    if far then
        self.__curFar = far
    end
    if hig then
        self.__curHig = hig
    end
    if far or hig then
        local p = gvec3(0, self.__curHig, -self.__curFar)
        self.camTrs.localPosition = p
        if self.__curFar > self.__showPlayerMin then
            self.camTrs:LookAt(self.camPivotTrs)
        end
    end
end

function SceneFree3DCamera:resetCam()
    self.__newFar = self.__curFar
    self.__newHig = self.__curHig
    self:onLateUpdate()
end

function SceneFree3DCamera:drawGizmos(p1, p2, hitPos, newPos)
    if enableDebug then
        DebugDrawer.Line(p1.x, p1.y, p1.z, p2.x, p2.y, p2.z, 0, 0, 1)
        DebugDrawer.Cross(hitPos.x, hitPos.y, hitPos.z, 1, 0, 0)
        DebugDrawer.Cross(newPos.x, newPos.y, newPos.z, 0, 1, 0)
        DebugDrawer.Circle(hitPos.x, hitPos.y, hitPos.z, R.camCollisionRadius, 1, 0, 0)
        DebugDrawer.Circle(newPos.x, newPos.y, newPos.z, R.camCollisionRadius, 0, 1, 0)
    end
end

function SceneFree3DCamera:getPos()
    local camPos = self.camTrs.position
    return camPos.x, camPos.z
end

function SceneFree3DCamera:isPosInSight(pos)
    return self.sightComp:IsPosInSight(pos)
end

function SceneFree3DCamera:isInSight(unit)
    return self.sightComp:IsInSight(unit.go)
end

function SceneFree3DCamera:setViewSize(size)
    size = math.max(size, self.zoomConfig.zoomMin)
    size = math.min(size, self.zoomConfig.zoomMax)
    self.viewSize = size
    self.mainCamera.fieldOfView = size * ScreenAdaption.Instance.CamScale
    SceneController.instance:localNotify(SceneNotify.CameraZoom, size / 3.6)
end

function SceneFree3DCamera:getViewSize()
    return self.viewSize
end

function SceneFree3DCamera:getDefaultViewSize()
    return self.zoomConfig.default
end

function SceneFree3DCamera:moveTo(x, z, duration, easeType)
    -- nothing todo
end

function SceneFree3DCamera:stopMove()
    -- nothing todo
end

function SceneFree3DCamera:setCamRotate(h, v)
    local e = self.camPivotTrs.eulerAngles
    if h then
        e.y = h
        self.__curRotaH = h
    end
    if v then
        e.x = v
        self.__curRotaV = v
    end
    self.camPivotTrs.eulerAngles = e
end

return SceneFree3DCamera
