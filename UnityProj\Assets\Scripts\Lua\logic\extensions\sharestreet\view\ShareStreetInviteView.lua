module("logic.extensions.sharestreet.view.ShareStreetInviteView",package.seeall)
---@class ShareStreetInviteView
local ShareStreetInviteView = class("ShareStreetInviteView",ListBinderView)

function ShareStreetInviteView:ctor()
	self._inviteModel = BaseListModel.New()
	ShareStreetInviteView.super.ctor(
		self,
		self._inviteModel,
		"listView",
		ShareStreetInviteViewPresentor.Url_Item,
		ShareStreetInviteItem,
		{kScrollDirV, 264, 273, 10, 10, 4},
		false
	)
end

--- view初始化时会执行
function ShareStreetInviteView:buildUI()
	ShareStreetInviteView.super.buildUI(self)

	self._btnClose = self:getBtn("btnclose/btnClose")
	self._txtClose = self:getText("btnclose/Text")
	self._goTopBar = self:getGo("topBar")
	self._iptSearch = self:getInput("topBar/searchIpt")
	self._iptSearch.gameObject:SetActive(false)
	self._btnSearch = self:getBtn("topBar/searchBtn")
	self._btnSearch.gameObject:SetActive(false)
	self._goToggle = self:getGo("topBar/tag_1")
	self._toggle = Framework.ToggleAdapter.Get(self._goToggle)
	self._txtToggle = self:getText("topBar/tag_1/Label")
	self._goNull = self:getGo("listView/goNull")
	self._txtNull = self:getText("listView/goNull/commonnullview/txt")
	self._txtMemberCount = self:getText("countGo/countTxt")
end

--- view初始化时会执行，在buildUI之后
function ShareStreetInviteView:bindEvents()
	ShareStreetInviteView.super.bindEvents(self)

	self._btnClose:AddClickListener(self.close, self)
	self._toggle:AddOnValueChanged(self._onClickToggle, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetInviteView:onEnter()
	ShareStreetInviteView.super.onEnter(self)

	self._isInviteFriend = self:getFirstParam()
	self._isInvitedFriendMap = {}
	self._streetInfo = ShareStreetModel.instance:getUserInfo()
	self._inviteModel:setMoList({})
	if self._isInviteFriend then
		self._txtClose.text = lang("邀请加入")
		ShareStreetAgent.instance:sendGetFriendStreetListRequest(handler(self._onGetFriendStreetList, self))
		self._txtMemberCount.text = ""
		self._txtToggle.text = "只显示创建街区的好友"
	else
		local memberCount = #self._streetInfo:getMemberList()
		local unlockedAreaIdCount = self._streetInfo:getUnlockedAreaIdCount()
		self._txtClose.text = lang("加入申请")
		self._txtMemberCount.text = lang("成员数量 {1}/{2}", memberCount, unlockedAreaIdCount)
		self._txtToggle.text = "不再接受申请"
		if memberCount >= unlockedAreaIdCount then
			self._goNull:SetActive(true)
			self._txtNull.text = lang("当前无可开放区域，无法接受邀请")
		else
			self._goNull:SetActive(false)
			self._txtNull.text = lang("暂未收到加入街区的申请")
			ShareStreetAgent.instance:sendGetStreetReceiveAskListRequest(handler(self._onGetApplyList, self))
		end
	end

	self:registerLocalNotify(ShareStreetLocalNotify.OnAcceptJoinRequestSucc, self._onAcceptJoinRequestSucc, self)
	self:registerLocalNotify(ShareStreetLocalNotify.OnRefuseJoinRequestSucc, self._onRefuseJoinRequestSucc, self)
	self:registerLocalNotify(ShareStreetLocalNotify.OnInviteFriend, self._onInviteFriendSucc, self)

	self:registerNotify(GlobalNotify.StartLoadScene, self.close, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetInviteView:onEnterFinished()
	ShareStreetInviteView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetInviteView:onExit()
	ShareStreetInviteView.super.onExit(self)

	self:unregisterLocalNotify(ShareStreetLocalNotify.OnAcceptJoinRequestSucc, self._onAcceptJoinRequestSucc, self)
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnRefuseJoinRequestSucc, self._onRefuseJoinRequestSucc, self)
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnInviteFriend, self._onInviteFriendSucc, self)

	self:unregisterNotify(GlobalNotify.StartLoadScene, self.close, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetInviteView:onExitFinished()
	ShareStreetInviteView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetInviteView:unbindEvents()
	ShareStreetInviteView.super.unbindEvents(self)

	self._btnClose:RemoveClickListener()
	self._toggle:RemoveOnValueChanged()
end

--- view销毁时会执行
function ShareStreetInviteView:destroyUI()
	ShareStreetInviteView.super.destroyUI(self)
end

-- 更新邀请状态的辅助函数
function ShareStreetInviteView:_updateHasInvitedStatus(hasInvited)
	print("ShareStreetInviteView:_updateHasInvitedStatus", hasInvited, debug.traceback())
	local oldHasInvited = ShareStreetModel.instance:getHasInvited()
	ShareStreetModel.instance:setHasInvited(hasInvited)
	-- 如果状态发生变更，发送通知
	if oldHasInvited ~= hasInvited then
		ShareStreetController.instance:localNotify(ShareStreetLocalNotify.OnHasInvitedStatusChanged, hasInvited)
	end
end

-- 更新申请状态的辅助函数
function ShareStreetInviteView:_updateHasApplyStatus(hasApply)
	local oldHasApply = ShareStreetModel.instance:getHasApply()
	ShareStreetModel.instance:setHasApply(hasApply)
	-- 如果状态发生变更，发送通知
	if oldHasApply ~= hasApply then
		ShareStreetController.instance:localNotify(ShareStreetLocalNotify.OnHasApplyStatusChanged, hasApply)
	end
end

function ShareStreetInviteView:_onClickToggle(go, isOn)
	if self._isInviteFriend then
		LocalStorage.instance:setValue(StorageKey.ShareStreet_IsShowStreetOnly, isOn)
		self:_updateFriendList()
	else
		if self._isAcceptAsk == isOn then
			self._isAcceptAsk = not isOn
			ShareStreetAgent.instance:sendModifyStreetAcceptAskRequest(self._isAcceptAsk)
		end
	end
end

function ShareStreetInviteView:_onGetFriendStreetList(streetlist)
	self._friendStreetList = streetlist
	self:_updateFriendList()
end

function ShareStreetInviteView:_updateFriendList()
	local streetlist = self._friendStreetList
	self:_updateToggle()
	local hasStreetOnly = self._toggle.isOn
	print("hasStreetOnly", hasStreetOnly)
	self._inviteFriendCount = 0
	local moList = {}
	for _, streetNo in ipairs(streetlist) do
		repeat
			if hasStreetOnly and not streetNo.hasStreet then 
				break
			end
			if streetNo.isInvited then 
				self._inviteFriendCount = self._inviteFriendCount + 1
			end
			local mo = {}
			mo.isInviteFriend = true
			mo.no = streetNo
			mo.roleInfo = streetNo.friendInfo
			mo.isInvited = self._isInvitedFriendMap[mo.roleInfo.id] or streetNo.isInvited
			table.insert(moList, mo)
		until true
	end
	self._inviteModel:setMoList(moList)
	self._goNull:SetActive(#moList == 0)

	self:_updateInviteFriendCount()
end

function ShareStreetInviteView:_updateInviteFriendCount()
	self._txtMemberCount.text = lang("邀约人数 {1}/{2}", self._inviteFriendCount, ShareStreetCfg.instance:getCommonConfigValue("maxInviteCount"))
end

function ShareStreetInviteView:_onGetApplyList(applyList, isAccpetAsk)
	self._applyList = applyList
	self._isAcceptAsk = isAccpetAsk
	self:_updateToggle()
	self:_updateApplyList()
	-- 记录是否存在申请状态
	self:_updateHasApplyStatus(#applyList > 0)
end

function ShareStreetInviteView:_updateToggle()
	if self._isInviteFriend then
		self._toggle.isOn = LocalStorage.instance:getValue(StorageKey.ShareStreet_IsShowStreetOnly, false)
	else
		self._toggle.isOn = not self._isAcceptAsk
	end
end

function ShareStreetInviteView:_updateApplyList()
	local moList = {}
	for _, streetNo in ipairs(self._applyList) do
		local mo = {}
		mo.isJoinRequest = true
		mo.no = streetNo
		mo.roleInfo = streetNo.streetOwner
		table.insert(moList, mo)
	end
	self._inviteModel:setMoList(moList)
	self._goNull:SetActive(#moList == 0)
end

function ShareStreetInviteView:_onAcceptJoinRequestSucc(ownerId, askList)
	self._applyList = askList
	self:_updateApplyList()
	-- 更新申请状态
	self:_updateHasApplyStatus(#askList > 0)
end

function ShareStreetInviteView:_onRefuseJoinRequestSucc(ownerId, askList)
	self._applyList = askList
	self:_updateApplyList()
	-- 更新申请状态
	self:_updateHasApplyStatus(#askList > 0)
end

function ShareStreetInviteView:_onInviteFriendSucc(playerId)
	self._inviteFriendCount = self._inviteFriendCount + 1
	self._isInvitedFriendMap[playerId] = true
	self:_updateInviteFriendCount()
	-- 更新邀请状态
	self:_updateHasInvitedStatus(self._inviteFriendCount > 0)
end

return ShareStreetInviteView