-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
local USEREXTENSION_PB = require("logic.proto.UserExtension_pb")
module("logic.proto.Activity161Extension_pb", package.seeall)


local tb = {}
tb.ACT161REDENVELOPETYPE_ENUM = protobuf.EnumDescriptor()
tb.ACT161REDENVELOPETYPE_NORMAL_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT161REDENVELOPETYPE_EXQUISITE_ENUMITEM = protobuf.EnumValueDescriptor()
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG = protobuf.Descriptor()
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT161DRAWREDENVELOPEREPLY_MSG = protobuf.Descriptor()
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG = protobuf.Descriptor()
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD = protobuf.FieldDescriptor()
GETACT161INFOREQUEST_MSG = protobuf.Descriptor()
ACT161REDENVELOPENO_MSG = protobuf.Descriptor()
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD = protobuf.FieldDescriptor()
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD = protobuf.FieldDescriptor()
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD = protobuf.FieldDescriptor()
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD = protobuf.FieldDescriptor()
ACT161REDENVELOPEDRAWRECORDNO_MSG = protobuf.Descriptor()
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD = protobuf.FieldDescriptor()
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD = protobuf.FieldDescriptor()
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD = protobuf.FieldDescriptor()
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD = protobuf.FieldDescriptor()
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG = protobuf.Descriptor()
ACT161LIKEREDENVELOPEREPLY_MSG = protobuf.Descriptor()
ACT161LOTTERYREPLY_MSG = protobuf.Descriptor()
tb.ACT161LOTTERYREPLY_IDS_FIELD = protobuf.FieldDescriptor()
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD = protobuf.FieldDescriptor()
ACT161DRAWFATIGUEREWARDREPLY_MSG = protobuf.Descriptor()
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT161LIKEREDENVELOPEREQUEST_MSG = protobuf.Descriptor()
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD = protobuf.FieldDescriptor()
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD = protobuf.FieldDescriptor()
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD = protobuf.FieldDescriptor()
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG = protobuf.Descriptor()
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD = protobuf.FieldDescriptor()
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD = protobuf.FieldDescriptor()
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD = protobuf.FieldDescriptor()
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD = protobuf.FieldDescriptor()
ACT161LOTTERYRECORDNO_MSG = protobuf.Descriptor()
tb.ACT161LOTTERYRECORDNO_ID_FIELD = protobuf.FieldDescriptor()
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD = protobuf.FieldDescriptor()
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD = protobuf.FieldDescriptor()
GETACT161INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT161INFOREPLY_IDS_FIELD = protobuf.FieldDescriptor()
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD = protobuf.FieldDescriptor()
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD = protobuf.FieldDescriptor()
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD = protobuf.FieldDescriptor()
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD = protobuf.FieldDescriptor()
ACT161DRAWFATIGUEREWARDREQUEST_MSG = protobuf.Descriptor()
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD = protobuf.FieldDescriptor()
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG = protobuf.Descriptor()
ACT161DRAWPROCESSREPLY_MSG = protobuf.Descriptor()
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT161GETREDENVELOPEINFOREQUEST_MSG = protobuf.Descriptor()
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG = protobuf.Descriptor()
ACT161GLOBALLOTTERYRECORDNO_MSG = protobuf.Descriptor()
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD = protobuf.FieldDescriptor()
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD = protobuf.FieldDescriptor()
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD = protobuf.FieldDescriptor()
ACT161DRAWREDENVELOPEREQUEST_MSG = protobuf.Descriptor()
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD = protobuf.FieldDescriptor()
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD = protobuf.FieldDescriptor()
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD = protobuf.FieldDescriptor()
ACT161GETREDENVELOPEINFOREPLY_MSG = protobuf.Descriptor()
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD = protobuf.FieldDescriptor()
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD = protobuf.FieldDescriptor()
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD = protobuf.FieldDescriptor()
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD = protobuf.FieldDescriptor()
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD = protobuf.FieldDescriptor()
ACT161LOTTERYREQUEST_MSG = protobuf.Descriptor()
tb.ACT161LOTTERYREQUEST_TEN_FIELD = protobuf.FieldDescriptor()
ACT161CONVERTLOTTERYITEMREPLY_MSG = protobuf.Descriptor()
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT161CONVERTLOTTERYITEMREQUEST_MSG = protobuf.Descriptor()
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD = protobuf.FieldDescriptor()
ACT161QUERYLOTTERYRECORDSREPLY_MSG = protobuf.Descriptor()
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD = protobuf.FieldDescriptor()
ACT161QUERYLOTTERYRECORDSREQUEST_MSG = protobuf.Descriptor()
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG = protobuf.Descriptor()
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD = protobuf.FieldDescriptor()
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG = protobuf.Descriptor()
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD = protobuf.FieldDescriptor()
ACT161DRAWPROCESSREQUEST_MSG = protobuf.Descriptor()
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD = protobuf.FieldDescriptor()
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD = protobuf.FieldDescriptor()

tb.ACT161REDENVELOPETYPE_NORMAL_ENUMITEM.name = "NORMAL"
tb.ACT161REDENVELOPETYPE_NORMAL_ENUMITEM.index = 0
tb.ACT161REDENVELOPETYPE_NORMAL_ENUMITEM.number = 1
tb.ACT161REDENVELOPETYPE_EXQUISITE_ENUMITEM.name = "EXQUISITE"
tb.ACT161REDENVELOPETYPE_EXQUISITE_ENUMITEM.index = 1
tb.ACT161REDENVELOPETYPE_EXQUISITE_ENUMITEM.number = 2
tb.ACT161REDENVELOPETYPE_ENUM.name = "Act161RedEnvelopeType"
tb.ACT161REDENVELOPETYPE_ENUM.full_name = ".Act161RedEnvelopeType"
tb.ACT161REDENVELOPETYPE_ENUM.values = {tb.ACT161REDENVELOPETYPE_NORMAL_ENUMITEM,tb.ACT161REDENVELOPETYPE_EXQUISITE_ENUMITEM}
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.full_name = ".Act161DrawLotteryScoreRewardReply.changeSetId"
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.name = "Act161DrawLotteryScoreRewardReply"
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.full_name = ".Act161DrawLotteryScoreRewardReply"
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.filename = "Activity161Extension"
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.nested_types = {}
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.enum_types = {}
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.fields = {tb.ACT161DRAWLOTTERYSCOREREWARDREPLY_CHANGESETID_FIELD}
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.is_extendable = false
ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG.extensions = {}
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.full_name = ".Act161DrawRedEnvelopeReply.changeSetId"
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.number = 1
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.index = 0
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.label = 1
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.type = 5
tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT161DRAWREDENVELOPEREPLY_MSG.name = "Act161DrawRedEnvelopeReply"
ACT161DRAWREDENVELOPEREPLY_MSG.full_name = ".Act161DrawRedEnvelopeReply"
ACT161DRAWREDENVELOPEREPLY_MSG.filename = "Activity161Extension"
ACT161DRAWREDENVELOPEREPLY_MSG.nested_types = {}
ACT161DRAWREDENVELOPEREPLY_MSG.enum_types = {}
ACT161DRAWREDENVELOPEREPLY_MSG.fields = {tb.ACT161DRAWREDENVELOPEREPLY_CHANGESETID_FIELD}
ACT161DRAWREDENVELOPEREPLY_MSG.is_extendable = false
ACT161DRAWREDENVELOPEREPLY_MSG.extensions = {}
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.name = "drawRecords"
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.full_name = ".Act161GetRedEnvelopeDrawRecordsReply.drawRecords"
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.number = 1
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.index = 0
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.label = 3
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.default_value = {}
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.message_type = ACT161REDENVELOPEDRAWRECORDNO_MSG
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.type = 11
tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD.cpp_type = 10

ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.name = "Act161GetRedEnvelopeDrawRecordsReply"
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.full_name = ".Act161GetRedEnvelopeDrawRecordsReply"
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.filename = "Activity161Extension"
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.nested_types = {}
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.enum_types = {}
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.fields = {tb.ACT161GETREDENVELOPEDRAWRECORDSREPLY_DRAWRECORDS_FIELD}
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.is_extendable = false
ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG.extensions = {}
GETACT161INFOREQUEST_MSG.name = "GetAct161InfoRequest"
GETACT161INFOREQUEST_MSG.full_name = ".GetAct161InfoRequest"
GETACT161INFOREQUEST_MSG.filename = "Activity161Extension"
GETACT161INFOREQUEST_MSG.nested_types = {}
GETACT161INFOREQUEST_MSG.enum_types = {}
GETACT161INFOREQUEST_MSG.fields = {}
GETACT161INFOREQUEST_MSG.is_extendable = false
GETACT161INFOREQUEST_MSG.extensions = {}
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.name = "sendUserUid"
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.full_name = ".Act161RedEnvelopeNO.sendUserUid"
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.number = 1
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.index = 0
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.label = 1
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.has_default_value = false
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.default_value = ""
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.type = 9
tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD.cpp_type = 9

tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.name = "roleSimpleInfo"
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.full_name = ".Act161RedEnvelopeNO.roleSimpleInfo"
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.number = 2
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.index = 1
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.label = 1
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.has_default_value = false
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.default_value = nil
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.type = 11
tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD.cpp_type = 10

tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.name = "redEnvelopeId"
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.full_name = ".Act161RedEnvelopeNO.redEnvelopeId"
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.number = 3
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.index = 2
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.label = 1
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.has_default_value = false
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.default_value = 0
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.type = 5
tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD.cpp_type = 1

tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.name = "redEnvelopeDefineId"
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.full_name = ".Act161RedEnvelopeNO.redEnvelopeDefineId"
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.number = 4
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.index = 3
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.label = 1
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.has_default_value = false
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.default_value = 0
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.type = 5
tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD.cpp_type = 1

ACT161REDENVELOPENO_MSG.name = "Act161RedEnvelopeNO"
ACT161REDENVELOPENO_MSG.full_name = ".Act161RedEnvelopeNO"
ACT161REDENVELOPENO_MSG.filename = "Activity161Extension"
ACT161REDENVELOPENO_MSG.nested_types = {}
ACT161REDENVELOPENO_MSG.enum_types = {}
ACT161REDENVELOPENO_MSG.fields = {tb.ACT161REDENVELOPENO_SENDUSERUID_FIELD, tb.ACT161REDENVELOPENO_ROLESIMPLEINFO_FIELD, tb.ACT161REDENVELOPENO_REDENVELOPEID_FIELD, tb.ACT161REDENVELOPENO_REDENVELOPEDEFINEID_FIELD}
ACT161REDENVELOPENO_MSG.is_extendable = false
ACT161REDENVELOPENO_MSG.extensions = {}
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.name = "senderRoleSimpleInfo"
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.full_name = ".Act161RedEnvelopeDrawRecordNO.senderRoleSimpleInfo"
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.number = 1
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.index = 0
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.label = 1
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.has_default_value = false
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.default_value = nil
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.type = 11
tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD.cpp_type = 10

tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.name = "redEnvelopeId"
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.full_name = ".Act161RedEnvelopeDrawRecordNO.redEnvelopeId"
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.number = 2
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.index = 1
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.label = 1
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.has_default_value = false
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.default_value = 0
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.type = 5
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD.cpp_type = 1

tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.name = "redEnvelopeType"
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.full_name = ".Act161RedEnvelopeDrawRecordNO.redEnvelopeType"
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.number = 3
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.index = 2
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.label = 1
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.has_default_value = false
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.default_value = 0
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.type = 5
tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD.cpp_type = 1

tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.name = "drawTime"
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.full_name = ".Act161RedEnvelopeDrawRecordNO.drawTime"
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.number = 4
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.index = 3
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.label = 1
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.has_default_value = false
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.default_value = 0
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.type = 5
tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD.cpp_type = 1

ACT161REDENVELOPEDRAWRECORDNO_MSG.name = "Act161RedEnvelopeDrawRecordNO"
ACT161REDENVELOPEDRAWRECORDNO_MSG.full_name = ".Act161RedEnvelopeDrawRecordNO"
ACT161REDENVELOPEDRAWRECORDNO_MSG.filename = "Activity161Extension"
ACT161REDENVELOPEDRAWRECORDNO_MSG.nested_types = {}
ACT161REDENVELOPEDRAWRECORDNO_MSG.enum_types = {}
ACT161REDENVELOPEDRAWRECORDNO_MSG.fields = {tb.ACT161REDENVELOPEDRAWRECORDNO_SENDERROLESIMPLEINFO_FIELD, tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPEID_FIELD, tb.ACT161REDENVELOPEDRAWRECORDNO_REDENVELOPETYPE_FIELD, tb.ACT161REDENVELOPEDRAWRECORDNO_DRAWTIME_FIELD}
ACT161REDENVELOPEDRAWRECORDNO_MSG.is_extendable = false
ACT161REDENVELOPEDRAWRECORDNO_MSG.extensions = {}
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.name = "Act161GetGlobalLotteryRecordsRequest"
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.full_name = ".Act161GetGlobalLotteryRecordsRequest"
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.filename = "Activity161Extension"
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.nested_types = {}
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.enum_types = {}
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.fields = {}
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.is_extendable = false
ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG.extensions = {}
ACT161LIKEREDENVELOPEREPLY_MSG.name = "Act161LikeRedEnvelopeReply"
ACT161LIKEREDENVELOPEREPLY_MSG.full_name = ".Act161LikeRedEnvelopeReply"
ACT161LIKEREDENVELOPEREPLY_MSG.filename = "Activity161Extension"
ACT161LIKEREDENVELOPEREPLY_MSG.nested_types = {}
ACT161LIKEREDENVELOPEREPLY_MSG.enum_types = {}
ACT161LIKEREDENVELOPEREPLY_MSG.fields = {}
ACT161LIKEREDENVELOPEREPLY_MSG.is_extendable = false
ACT161LIKEREDENVELOPEREPLY_MSG.extensions = {}
tb.ACT161LOTTERYREPLY_IDS_FIELD.name = "ids"
tb.ACT161LOTTERYREPLY_IDS_FIELD.full_name = ".Act161LotteryReply.ids"
tb.ACT161LOTTERYREPLY_IDS_FIELD.number = 1
tb.ACT161LOTTERYREPLY_IDS_FIELD.index = 0
tb.ACT161LOTTERYREPLY_IDS_FIELD.label = 3
tb.ACT161LOTTERYREPLY_IDS_FIELD.has_default_value = false
tb.ACT161LOTTERYREPLY_IDS_FIELD.default_value = {}
tb.ACT161LOTTERYREPLY_IDS_FIELD.type = 5
tb.ACT161LOTTERYREPLY_IDS_FIELD.cpp_type = 1

tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.name = "processIds"
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.full_name = ".Act161LotteryReply.processIds"
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.number = 2
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.index = 1
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.label = 3
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.has_default_value = false
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.default_value = {}
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.type = 5
tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD.cpp_type = 1

ACT161LOTTERYREPLY_MSG.name = "Act161LotteryReply"
ACT161LOTTERYREPLY_MSG.full_name = ".Act161LotteryReply"
ACT161LOTTERYREPLY_MSG.filename = "Activity161Extension"
ACT161LOTTERYREPLY_MSG.nested_types = {}
ACT161LOTTERYREPLY_MSG.enum_types = {}
ACT161LOTTERYREPLY_MSG.fields = {tb.ACT161LOTTERYREPLY_IDS_FIELD, tb.ACT161LOTTERYREPLY_PROCESSIDS_FIELD}
ACT161LOTTERYREPLY_MSG.is_extendable = false
ACT161LOTTERYREPLY_MSG.extensions = {}
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.full_name = ".Act161DrawFatigueRewardReply.changeSetId"
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT161DRAWFATIGUEREWARDREPLY_MSG.name = "Act161DrawFatigueRewardReply"
ACT161DRAWFATIGUEREWARDREPLY_MSG.full_name = ".Act161DrawFatigueRewardReply"
ACT161DRAWFATIGUEREWARDREPLY_MSG.filename = "Activity161Extension"
ACT161DRAWFATIGUEREWARDREPLY_MSG.nested_types = {}
ACT161DRAWFATIGUEREWARDREPLY_MSG.enum_types = {}
ACT161DRAWFATIGUEREWARDREPLY_MSG.fields = {tb.ACT161DRAWFATIGUEREWARDREPLY_CHANGESETID_FIELD}
ACT161DRAWFATIGUEREWARDREPLY_MSG.is_extendable = false
ACT161DRAWFATIGUEREWARDREPLY_MSG.extensions = {}
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.name = "sendUserUId"
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.full_name = ".Act161LikeRedEnvelopeRequest.sendUserUId"
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.number = 1
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.index = 0
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.label = 1
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.has_default_value = false
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.default_value = ""
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.type = 9
tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD.cpp_type = 9

tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.name = "redEnvelopeId"
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.full_name = ".Act161LikeRedEnvelopeRequest.redEnvelopeId"
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.number = 2
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.index = 1
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.label = 1
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.has_default_value = false
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.default_value = 0
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.type = 5
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD.cpp_type = 1

tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.name = "redEnvelopeType"
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.full_name = ".Act161LikeRedEnvelopeRequest.redEnvelopeType"
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.number = 3
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.index = 2
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.label = 1
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.has_default_value = false
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.default_value = 0
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.type = 5
tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.cpp_type = 1

ACT161LIKEREDENVELOPEREQUEST_MSG.name = "Act161LikeRedEnvelopeRequest"
ACT161LIKEREDENVELOPEREQUEST_MSG.full_name = ".Act161LikeRedEnvelopeRequest"
ACT161LIKEREDENVELOPEREQUEST_MSG.filename = "Activity161Extension"
ACT161LIKEREDENVELOPEREQUEST_MSG.nested_types = {}
ACT161LIKEREDENVELOPEREQUEST_MSG.enum_types = {}
ACT161LIKEREDENVELOPEREQUEST_MSG.fields = {tb.ACT161LIKEREDENVELOPEREQUEST_SENDUSERUID_FIELD, tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPEID_FIELD, tb.ACT161LIKEREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD}
ACT161LIKEREDENVELOPEREQUEST_MSG.is_extendable = false
ACT161LIKEREDENVELOPEREQUEST_MSG.extensions = {}
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.name = "meRoleSimpleInfo"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.full_name = ".Act161GetRedEnvelopeSendStatisticsReply.meRoleSimpleInfo"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.number = 1
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.index = 0
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.label = 1
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.default_value = nil
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.type = 11
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD.cpp_type = 10

tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.name = "sendNormalCount"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.full_name = ".Act161GetRedEnvelopeSendStatisticsReply.sendNormalCount"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.number = 2
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.index = 1
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.label = 1
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.default_value = 0
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.type = 5
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD.cpp_type = 1

tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.name = "sendExquisiteCount"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.full_name = ".Act161GetRedEnvelopeSendStatisticsReply.sendExquisiteCount"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.number = 3
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.index = 2
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.label = 1
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.default_value = 0
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.type = 5
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD.cpp_type = 1

tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.name = "likesCount"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.full_name = ".Act161GetRedEnvelopeSendStatisticsReply.likesCount"
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.number = 4
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.index = 3
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.label = 1
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.default_value = 0
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.type = 5
tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD.cpp_type = 1

ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.name = "Act161GetRedEnvelopeSendStatisticsReply"
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.full_name = ".Act161GetRedEnvelopeSendStatisticsReply"
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.filename = "Activity161Extension"
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.nested_types = {}
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.enum_types = {}
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.fields = {tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_MEROLESIMPLEINFO_FIELD, tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDNORMALCOUNT_FIELD, tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_SENDEXQUISITECOUNT_FIELD, tb.ACT161GETREDENVELOPESENDSTATISTICSREPLY_LIKESCOUNT_FIELD}
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.is_extendable = false
ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG.extensions = {}
tb.ACT161LOTTERYRECORDNO_ID_FIELD.name = "id"
tb.ACT161LOTTERYRECORDNO_ID_FIELD.full_name = ".Act161LotteryRecordNO.id"
tb.ACT161LOTTERYRECORDNO_ID_FIELD.number = 1
tb.ACT161LOTTERYRECORDNO_ID_FIELD.index = 0
tb.ACT161LOTTERYRECORDNO_ID_FIELD.label = 1
tb.ACT161LOTTERYRECORDNO_ID_FIELD.has_default_value = false
tb.ACT161LOTTERYRECORDNO_ID_FIELD.default_value = 0
tb.ACT161LOTTERYRECORDNO_ID_FIELD.type = 5
tb.ACT161LOTTERYRECORDNO_ID_FIELD.cpp_type = 1

tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.name = "rewards"
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.full_name = ".Act161LotteryRecordNO.rewards"
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.number = 2
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.index = 1
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.label = 1
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.has_default_value = false
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.default_value = ""
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.type = 9
tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD.cpp_type = 9

tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.name = "lotteryTime"
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.full_name = ".Act161LotteryRecordNO.lotteryTime"
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.number = 3
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.index = 2
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.label = 1
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.has_default_value = false
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.default_value = 0
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.type = 5
tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD.cpp_type = 1

ACT161LOTTERYRECORDNO_MSG.name = "Act161LotteryRecordNO"
ACT161LOTTERYRECORDNO_MSG.full_name = ".Act161LotteryRecordNO"
ACT161LOTTERYRECORDNO_MSG.filename = "Activity161Extension"
ACT161LOTTERYRECORDNO_MSG.nested_types = {}
ACT161LOTTERYRECORDNO_MSG.enum_types = {}
ACT161LOTTERYRECORDNO_MSG.fields = {tb.ACT161LOTTERYRECORDNO_ID_FIELD, tb.ACT161LOTTERYRECORDNO_REWARDS_FIELD, tb.ACT161LOTTERYRECORDNO_LOTTERYTIME_FIELD}
ACT161LOTTERYRECORDNO_MSG.is_extendable = false
ACT161LOTTERYRECORDNO_MSG.extensions = {}
tb.GETACT161INFOREPLY_IDS_FIELD.name = "ids"
tb.GETACT161INFOREPLY_IDS_FIELD.full_name = ".GetAct161InfoReply.ids"
tb.GETACT161INFOREPLY_IDS_FIELD.number = 1
tb.GETACT161INFOREPLY_IDS_FIELD.index = 0
tb.GETACT161INFOREPLY_IDS_FIELD.label = 3
tb.GETACT161INFOREPLY_IDS_FIELD.has_default_value = false
tb.GETACT161INFOREPLY_IDS_FIELD.default_value = {}
tb.GETACT161INFOREPLY_IDS_FIELD.type = 5
tb.GETACT161INFOREPLY_IDS_FIELD.cpp_type = 1

tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.name = "drawProcess"
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.full_name = ".GetAct161InfoReply.drawProcess"
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.number = 2
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.index = 1
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.label = 1
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.has_default_value = false
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.default_value = 0
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.type = 5
tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD.cpp_type = 1

tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.name = "totalLotteryCount"
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.full_name = ".GetAct161InfoReply.totalLotteryCount"
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.number = 3
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.index = 2
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.label = 1
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.has_default_value = false
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.default_value = 0
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.type = 5
tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD.cpp_type = 1

tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.name = "tiredValue"
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.full_name = ".GetAct161InfoReply.tiredValue"
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.number = 4
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.index = 3
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.label = 1
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.has_default_value = false
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.default_value = 0
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.type = 5
tb.GETACT161INFOREPLY_TIREDVALUE_FIELD.cpp_type = 1

tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.name = "drawTiredRewardProcess"
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.full_name = ".GetAct161InfoReply.drawTiredRewardProcess"
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.number = 5
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.index = 4
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.label = 1
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.has_default_value = false
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.default_value = 0
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.type = 5
tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD.cpp_type = 1

tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.name = "drawScoreRewardsProcess"
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.full_name = ".GetAct161InfoReply.drawScoreRewardsProcess"
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.number = 6
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.index = 5
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.label = 1
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.has_default_value = false
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.default_value = 0
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.type = 5
tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD.cpp_type = 1

GETACT161INFOREPLY_MSG.name = "GetAct161InfoReply"
GETACT161INFOREPLY_MSG.full_name = ".GetAct161InfoReply"
GETACT161INFOREPLY_MSG.filename = "Activity161Extension"
GETACT161INFOREPLY_MSG.nested_types = {}
GETACT161INFOREPLY_MSG.enum_types = {}
GETACT161INFOREPLY_MSG.fields = {tb.GETACT161INFOREPLY_IDS_FIELD, tb.GETACT161INFOREPLY_DRAWPROCESS_FIELD, tb.GETACT161INFOREPLY_TOTALLOTTERYCOUNT_FIELD, tb.GETACT161INFOREPLY_TIREDVALUE_FIELD, tb.GETACT161INFOREPLY_DRAWTIREDREWARDPROCESS_FIELD, tb.GETACT161INFOREPLY_DRAWSCOREREWARDSPROCESS_FIELD}
GETACT161INFOREPLY_MSG.is_extendable = false
GETACT161INFOREPLY_MSG.extensions = {}
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.name = "id"
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.full_name = ".Act161DrawFatigueRewardRequest.id"
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.number = 1
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.index = 0
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.label = 2
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.has_default_value = false
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.default_value = 0
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.type = 5
tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD.cpp_type = 1

ACT161DRAWFATIGUEREWARDREQUEST_MSG.name = "Act161DrawFatigueRewardRequest"
ACT161DRAWFATIGUEREWARDREQUEST_MSG.full_name = ".Act161DrawFatigueRewardRequest"
ACT161DRAWFATIGUEREWARDREQUEST_MSG.filename = "Activity161Extension"
ACT161DRAWFATIGUEREWARDREQUEST_MSG.nested_types = {}
ACT161DRAWFATIGUEREWARDREQUEST_MSG.enum_types = {}
ACT161DRAWFATIGUEREWARDREQUEST_MSG.fields = {tb.ACT161DRAWFATIGUEREWARDREQUEST_ID_FIELD}
ACT161DRAWFATIGUEREWARDREQUEST_MSG.is_extendable = false
ACT161DRAWFATIGUEREWARDREQUEST_MSG.extensions = {}
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.name = "Act161GetRedEnvelopeSendStatisticsRequest"
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.full_name = ".Act161GetRedEnvelopeSendStatisticsRequest"
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.filename = "Activity161Extension"
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.nested_types = {}
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.enum_types = {}
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.fields = {}
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.is_extendable = false
ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG.extensions = {}
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.full_name = ".Act161DrawProcessReply.changeSetId"
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.number = 1
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.index = 0
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.label = 1
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.type = 5
tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT161DRAWPROCESSREPLY_MSG.name = "Act161DrawProcessReply"
ACT161DRAWPROCESSREPLY_MSG.full_name = ".Act161DrawProcessReply"
ACT161DRAWPROCESSREPLY_MSG.filename = "Activity161Extension"
ACT161DRAWPROCESSREPLY_MSG.nested_types = {}
ACT161DRAWPROCESSREPLY_MSG.enum_types = {}
ACT161DRAWPROCESSREPLY_MSG.fields = {tb.ACT161DRAWPROCESSREPLY_CHANGESETID_FIELD}
ACT161DRAWPROCESSREPLY_MSG.is_extendable = false
ACT161DRAWPROCESSREPLY_MSG.extensions = {}
ACT161GETREDENVELOPEINFOREQUEST_MSG.name = "Act161GetRedEnvelopeInfoRequest"
ACT161GETREDENVELOPEINFOREQUEST_MSG.full_name = ".Act161GetRedEnvelopeInfoRequest"
ACT161GETREDENVELOPEINFOREQUEST_MSG.filename = "Activity161Extension"
ACT161GETREDENVELOPEINFOREQUEST_MSG.nested_types = {}
ACT161GETREDENVELOPEINFOREQUEST_MSG.enum_types = {}
ACT161GETREDENVELOPEINFOREQUEST_MSG.fields = {}
ACT161GETREDENVELOPEINFOREQUEST_MSG.is_extendable = false
ACT161GETREDENVELOPEINFOREQUEST_MSG.extensions = {}
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.name = "Act161GetRedEnvelopeDrawRecordsRequest"
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.full_name = ".Act161GetRedEnvelopeDrawRecordsRequest"
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.filename = "Activity161Extension"
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.nested_types = {}
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.enum_types = {}
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.fields = {}
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.is_extendable = false
ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG.extensions = {}
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.name = "roleSimpleInfo"
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.full_name = ".Act161GlobalLotteryRecordNO.roleSimpleInfo"
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.number = 1
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.index = 0
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.label = 1
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.has_default_value = false
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.default_value = nil
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.type = 11
tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD.cpp_type = 10

tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.name = "rewards"
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.full_name = ".Act161GlobalLotteryRecordNO.rewards"
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.number = 2
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.index = 1
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.label = 1
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.has_default_value = false
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.default_value = ""
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.type = 9
tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD.cpp_type = 9

tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.name = "lotteryTime"
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.full_name = ".Act161GlobalLotteryRecordNO.lotteryTime"
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.number = 3
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.index = 2
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.label = 1
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.has_default_value = false
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.default_value = 0
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.type = 5
tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD.cpp_type = 1

ACT161GLOBALLOTTERYRECORDNO_MSG.name = "Act161GlobalLotteryRecordNO"
ACT161GLOBALLOTTERYRECORDNO_MSG.full_name = ".Act161GlobalLotteryRecordNO"
ACT161GLOBALLOTTERYRECORDNO_MSG.filename = "Activity161Extension"
ACT161GLOBALLOTTERYRECORDNO_MSG.nested_types = {}
ACT161GLOBALLOTTERYRECORDNO_MSG.enum_types = {}
ACT161GLOBALLOTTERYRECORDNO_MSG.fields = {tb.ACT161GLOBALLOTTERYRECORDNO_ROLESIMPLEINFO_FIELD, tb.ACT161GLOBALLOTTERYRECORDNO_REWARDS_FIELD, tb.ACT161GLOBALLOTTERYRECORDNO_LOTTERYTIME_FIELD}
ACT161GLOBALLOTTERYRECORDNO_MSG.is_extendable = false
ACT161GLOBALLOTTERYRECORDNO_MSG.extensions = {}
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.name = "sendUserUId"
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.full_name = ".Act161DrawRedEnvelopeRequest.sendUserUId"
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.number = 1
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.index = 0
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.label = 1
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.has_default_value = false
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.default_value = ""
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.type = 9
tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD.cpp_type = 9

tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.name = "redEnvelopeId"
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.full_name = ".Act161DrawRedEnvelopeRequest.redEnvelopeId"
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.number = 2
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.index = 1
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.label = 1
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.has_default_value = false
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.default_value = 0
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.type = 5
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD.cpp_type = 1

tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.name = "redEnvelopeType"
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.full_name = ".Act161DrawRedEnvelopeRequest.redEnvelopeType"
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.number = 3
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.index = 2
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.label = 1
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.has_default_value = false
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.default_value = 0
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.type = 5
tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD.cpp_type = 1

ACT161DRAWREDENVELOPEREQUEST_MSG.name = "Act161DrawRedEnvelopeRequest"
ACT161DRAWREDENVELOPEREQUEST_MSG.full_name = ".Act161DrawRedEnvelopeRequest"
ACT161DRAWREDENVELOPEREQUEST_MSG.filename = "Activity161Extension"
ACT161DRAWREDENVELOPEREQUEST_MSG.nested_types = {}
ACT161DRAWREDENVELOPEREQUEST_MSG.enum_types = {}
ACT161DRAWREDENVELOPEREQUEST_MSG.fields = {tb.ACT161DRAWREDENVELOPEREQUEST_SENDUSERUID_FIELD, tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPEID_FIELD, tb.ACT161DRAWREDENVELOPEREQUEST_REDENVELOPETYPE_FIELD}
ACT161DRAWREDENVELOPEREQUEST_MSG.is_extendable = false
ACT161DRAWREDENVELOPEREQUEST_MSG.extensions = {}
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.name = "normalStorageInfos"
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.full_name = ".Act161GetRedEnvelopeInfoReply.normalStorageInfos"
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.number = 1
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.index = 0
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.label = 3
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.default_value = {}
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.message_type = ACT161REDENVELOPENO_MSG
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.type = 11
tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD.cpp_type = 10

tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.name = "exquisiteStorageInfos"
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.full_name = ".Act161GetRedEnvelopeInfoReply.exquisiteStorageInfos"
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.number = 2
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.index = 1
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.label = 3
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.default_value = {}
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.message_type = ACT161REDENVELOPENO_MSG
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.type = 11
tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD.cpp_type = 10

tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.name = "thanksRedEnvelopeInfos"
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.full_name = ".Act161GetRedEnvelopeInfoReply.thanksRedEnvelopeInfos"
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.number = 3
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.index = 2
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.label = 3
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.default_value = {}
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.message_type = ACT161REDENVELOPENO_MSG
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.type = 11
tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD.cpp_type = 10

tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.name = "drawNormalCountToday"
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.full_name = ".Act161GetRedEnvelopeInfoReply.drawNormalCountToday"
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.number = 4
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.index = 3
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.label = 1
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.default_value = 0
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.type = 5
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD.cpp_type = 1

tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.name = "drawExquisiteCountToday"
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.full_name = ".Act161GetRedEnvelopeInfoReply.drawExquisiteCountToday"
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.number = 5
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.index = 4
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.label = 1
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.has_default_value = false
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.default_value = 0
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.type = 5
tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD.cpp_type = 1

ACT161GETREDENVELOPEINFOREPLY_MSG.name = "Act161GetRedEnvelopeInfoReply"
ACT161GETREDENVELOPEINFOREPLY_MSG.full_name = ".Act161GetRedEnvelopeInfoReply"
ACT161GETREDENVELOPEINFOREPLY_MSG.filename = "Activity161Extension"
ACT161GETREDENVELOPEINFOREPLY_MSG.nested_types = {}
ACT161GETREDENVELOPEINFOREPLY_MSG.enum_types = {}
ACT161GETREDENVELOPEINFOREPLY_MSG.fields = {tb.ACT161GETREDENVELOPEINFOREPLY_NORMALSTORAGEINFOS_FIELD, tb.ACT161GETREDENVELOPEINFOREPLY_EXQUISITESTORAGEINFOS_FIELD, tb.ACT161GETREDENVELOPEINFOREPLY_THANKSREDENVELOPEINFOS_FIELD, tb.ACT161GETREDENVELOPEINFOREPLY_DRAWNORMALCOUNTTODAY_FIELD, tb.ACT161GETREDENVELOPEINFOREPLY_DRAWEXQUISITECOUNTTODAY_FIELD}
ACT161GETREDENVELOPEINFOREPLY_MSG.is_extendable = false
ACT161GETREDENVELOPEINFOREPLY_MSG.extensions = {}
tb.ACT161LOTTERYREQUEST_TEN_FIELD.name = "ten"
tb.ACT161LOTTERYREQUEST_TEN_FIELD.full_name = ".Act161LotteryRequest.ten"
tb.ACT161LOTTERYREQUEST_TEN_FIELD.number = 1
tb.ACT161LOTTERYREQUEST_TEN_FIELD.index = 0
tb.ACT161LOTTERYREQUEST_TEN_FIELD.label = 2
tb.ACT161LOTTERYREQUEST_TEN_FIELD.has_default_value = false
tb.ACT161LOTTERYREQUEST_TEN_FIELD.default_value = false
tb.ACT161LOTTERYREQUEST_TEN_FIELD.type = 8
tb.ACT161LOTTERYREQUEST_TEN_FIELD.cpp_type = 7

ACT161LOTTERYREQUEST_MSG.name = "Act161LotteryRequest"
ACT161LOTTERYREQUEST_MSG.full_name = ".Act161LotteryRequest"
ACT161LOTTERYREQUEST_MSG.filename = "Activity161Extension"
ACT161LOTTERYREQUEST_MSG.nested_types = {}
ACT161LOTTERYREQUEST_MSG.enum_types = {}
ACT161LOTTERYREQUEST_MSG.fields = {tb.ACT161LOTTERYREQUEST_TEN_FIELD}
ACT161LOTTERYREQUEST_MSG.is_extendable = false
ACT161LOTTERYREQUEST_MSG.extensions = {}
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.full_name = ".Act161ConvertLotteryItemReply.changeSetId"
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.number = 1
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.index = 0
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.label = 1
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.type = 5
tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT161CONVERTLOTTERYITEMREPLY_MSG.name = "Act161ConvertLotteryItemReply"
ACT161CONVERTLOTTERYITEMREPLY_MSG.full_name = ".Act161ConvertLotteryItemReply"
ACT161CONVERTLOTTERYITEMREPLY_MSG.filename = "Activity161Extension"
ACT161CONVERTLOTTERYITEMREPLY_MSG.nested_types = {}
ACT161CONVERTLOTTERYITEMREPLY_MSG.enum_types = {}
ACT161CONVERTLOTTERYITEMREPLY_MSG.fields = {tb.ACT161CONVERTLOTTERYITEMREPLY_CHANGESETID_FIELD}
ACT161CONVERTLOTTERYITEMREPLY_MSG.is_extendable = false
ACT161CONVERTLOTTERYITEMREPLY_MSG.extensions = {}
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.name = "count"
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.full_name = ".Act161ConvertLotteryItemRequest.count"
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.number = 1
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.index = 0
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.label = 2
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.has_default_value = false
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.default_value = 0
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.type = 5
tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD.cpp_type = 1

ACT161CONVERTLOTTERYITEMREQUEST_MSG.name = "Act161ConvertLotteryItemRequest"
ACT161CONVERTLOTTERYITEMREQUEST_MSG.full_name = ".Act161ConvertLotteryItemRequest"
ACT161CONVERTLOTTERYITEMREQUEST_MSG.filename = "Activity161Extension"
ACT161CONVERTLOTTERYITEMREQUEST_MSG.nested_types = {}
ACT161CONVERTLOTTERYITEMREQUEST_MSG.enum_types = {}
ACT161CONVERTLOTTERYITEMREQUEST_MSG.fields = {tb.ACT161CONVERTLOTTERYITEMREQUEST_COUNT_FIELD}
ACT161CONVERTLOTTERYITEMREQUEST_MSG.is_extendable = false
ACT161CONVERTLOTTERYITEMREQUEST_MSG.extensions = {}
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.name = "lotteryRecords"
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.full_name = ".Act161QueryLotteryRecordsReply.lotteryRecords"
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.number = 1
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.index = 0
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.label = 3
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.has_default_value = false
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.default_value = {}
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.message_type = ACT161LOTTERYRECORDNO_MSG
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.type = 11
tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD.cpp_type = 10

ACT161QUERYLOTTERYRECORDSREPLY_MSG.name = "Act161QueryLotteryRecordsReply"
ACT161QUERYLOTTERYRECORDSREPLY_MSG.full_name = ".Act161QueryLotteryRecordsReply"
ACT161QUERYLOTTERYRECORDSREPLY_MSG.filename = "Activity161Extension"
ACT161QUERYLOTTERYRECORDSREPLY_MSG.nested_types = {}
ACT161QUERYLOTTERYRECORDSREPLY_MSG.enum_types = {}
ACT161QUERYLOTTERYRECORDSREPLY_MSG.fields = {tb.ACT161QUERYLOTTERYRECORDSREPLY_LOTTERYRECORDS_FIELD}
ACT161QUERYLOTTERYRECORDSREPLY_MSG.is_extendable = false
ACT161QUERYLOTTERYRECORDSREPLY_MSG.extensions = {}
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.name = "Act161QueryLotteryRecordsRequest"
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.full_name = ".Act161QueryLotteryRecordsRequest"
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.filename = "Activity161Extension"
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.nested_types = {}
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.enum_types = {}
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.fields = {}
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.is_extendable = false
ACT161QUERYLOTTERYRECORDSREQUEST_MSG.extensions = {}
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.name = "id"
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.full_name = ".Act161DrawLotteryScoreRewardRequest.id"
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.number = 1
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.index = 0
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.label = 2
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.has_default_value = false
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.default_value = 0
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.type = 5
tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD.cpp_type = 1

ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.name = "Act161DrawLotteryScoreRewardRequest"
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.full_name = ".Act161DrawLotteryScoreRewardRequest"
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.filename = "Activity161Extension"
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.nested_types = {}
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.enum_types = {}
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.fields = {tb.ACT161DRAWLOTTERYSCOREREWARDREQUEST_ID_FIELD}
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.is_extendable = false
ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG.extensions = {}
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.name = "globalLotteryRecords"
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.full_name = ".Act161GetGlobalLotteryRecordsReply.globalLotteryRecords"
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.number = 1
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.index = 0
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.label = 3
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.has_default_value = false
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.default_value = {}
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.message_type = ACT161GLOBALLOTTERYRECORDNO_MSG
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.type = 11
tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD.cpp_type = 10

ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.name = "Act161GetGlobalLotteryRecordsReply"
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.full_name = ".Act161GetGlobalLotteryRecordsReply"
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.filename = "Activity161Extension"
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.nested_types = {}
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.enum_types = {}
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.fields = {tb.ACT161GETGLOBALLOTTERYRECORDSREPLY_GLOBALLOTTERYRECORDS_FIELD}
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.is_extendable = false
ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG.extensions = {}
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.name = "process"
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.full_name = ".Act161DrawProcessRequest.process"
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.number = 1
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.index = 0
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.label = 2
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.has_default_value = false
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.default_value = 0
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.type = 5
tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD.cpp_type = 1

tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.name = "index"
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.full_name = ".Act161DrawProcessRequest.index"
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.number = 2
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.index = 1
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.label = 2
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.has_default_value = false
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.default_value = 0
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.type = 5
tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD.cpp_type = 1

ACT161DRAWPROCESSREQUEST_MSG.name = "Act161DrawProcessRequest"
ACT161DRAWPROCESSREQUEST_MSG.full_name = ".Act161DrawProcessRequest"
ACT161DRAWPROCESSREQUEST_MSG.filename = "Activity161Extension"
ACT161DRAWPROCESSREQUEST_MSG.nested_types = {}
ACT161DRAWPROCESSREQUEST_MSG.enum_types = {}
ACT161DRAWPROCESSREQUEST_MSG.fields = {tb.ACT161DRAWPROCESSREQUEST_PROCESS_FIELD, tb.ACT161DRAWPROCESSREQUEST_INDEX_FIELD}
ACT161DRAWPROCESSREQUEST_MSG.is_extendable = false
ACT161DRAWPROCESSREQUEST_MSG.extensions = {}

Act161ConvertLotteryItemReply = protobuf.Message(ACT161CONVERTLOTTERYITEMREPLY_MSG)
Act161ConvertLotteryItemRequest = protobuf.Message(ACT161CONVERTLOTTERYITEMREQUEST_MSG)
Act161DrawFatigueRewardReply = protobuf.Message(ACT161DRAWFATIGUEREWARDREPLY_MSG)
Act161DrawFatigueRewardRequest = protobuf.Message(ACT161DRAWFATIGUEREWARDREQUEST_MSG)
Act161DrawLotteryScoreRewardReply = protobuf.Message(ACT161DRAWLOTTERYSCOREREWARDREPLY_MSG)
Act161DrawLotteryScoreRewardRequest = protobuf.Message(ACT161DRAWLOTTERYSCOREREWARDREQUEST_MSG)
Act161DrawProcessReply = protobuf.Message(ACT161DRAWPROCESSREPLY_MSG)
Act161DrawProcessRequest = protobuf.Message(ACT161DRAWPROCESSREQUEST_MSG)
Act161DrawRedEnvelopeReply = protobuf.Message(ACT161DRAWREDENVELOPEREPLY_MSG)
Act161DrawRedEnvelopeRequest = protobuf.Message(ACT161DRAWREDENVELOPEREQUEST_MSG)
Act161GetGlobalLotteryRecordsReply = protobuf.Message(ACT161GETGLOBALLOTTERYRECORDSREPLY_MSG)
Act161GetGlobalLotteryRecordsRequest = protobuf.Message(ACT161GETGLOBALLOTTERYRECORDSREQUEST_MSG)
Act161GetRedEnvelopeDrawRecordsReply = protobuf.Message(ACT161GETREDENVELOPEDRAWRECORDSREPLY_MSG)
Act161GetRedEnvelopeDrawRecordsRequest = protobuf.Message(ACT161GETREDENVELOPEDRAWRECORDSREQUEST_MSG)
Act161GetRedEnvelopeInfoReply = protobuf.Message(ACT161GETREDENVELOPEINFOREPLY_MSG)
Act161GetRedEnvelopeInfoRequest = protobuf.Message(ACT161GETREDENVELOPEINFOREQUEST_MSG)
Act161GetRedEnvelopeSendStatisticsReply = protobuf.Message(ACT161GETREDENVELOPESENDSTATISTICSREPLY_MSG)
Act161GetRedEnvelopeSendStatisticsRequest = protobuf.Message(ACT161GETREDENVELOPESENDSTATISTICSREQUEST_MSG)
Act161GlobalLotteryRecordNO = protobuf.Message(ACT161GLOBALLOTTERYRECORDNO_MSG)
Act161LikeRedEnvelopeReply = protobuf.Message(ACT161LIKEREDENVELOPEREPLY_MSG)
Act161LikeRedEnvelopeRequest = protobuf.Message(ACT161LIKEREDENVELOPEREQUEST_MSG)
Act161LotteryRecordNO = protobuf.Message(ACT161LOTTERYRECORDNO_MSG)
Act161LotteryReply = protobuf.Message(ACT161LOTTERYREPLY_MSG)
Act161LotteryRequest = protobuf.Message(ACT161LOTTERYREQUEST_MSG)
Act161QueryLotteryRecordsReply = protobuf.Message(ACT161QUERYLOTTERYRECORDSREPLY_MSG)
Act161QueryLotteryRecordsRequest = protobuf.Message(ACT161QUERYLOTTERYRECORDSREQUEST_MSG)
Act161RedEnvelopeDrawRecordNO = protobuf.Message(ACT161REDENVELOPEDRAWRECORDNO_MSG)
Act161RedEnvelopeNO = protobuf.Message(ACT161REDENVELOPENO_MSG)
EXQUISITE = 2
GetAct161InfoReply = protobuf.Message(GETACT161INFOREPLY_MSG)
GetAct161InfoRequest = protobuf.Message(GETACT161INFOREQUEST_MSG)
NORMAL = 1

return _G["logic.proto.Activity161Extension_pb"]
