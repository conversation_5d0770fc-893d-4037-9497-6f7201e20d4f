module("logic.extensions.activity.celebparty.view.CelebPartyActView", package.seeall)

local CelebPartyActView = class("CelebPartyActView", ViewComponent)

function CelebPartyActView:ctor()
    CelebPartyActView.super.ctor(self)
    self._commonIconsDaily = {}
    self._commonIconsJoin = {}
    self._items = {}
end

function CelebPartyActView:buildUI()
    self._dailyRewardList = self:getGo("awardView1/list")
    self._joinRewardList = self:getGo("awardView2/list")
    self._joinRewardItem = self:getGo("awardView2/list/carnivalitem")
    self._txtTotalJoinTimes = self:getText("txt2")
    self._txtTimeStart = self:getText("txtTimeStart")
    self._imgPrg = self:getGo("imgPrg"):GetComponent("Image")
    self:getBtn("btnGo"):AddClickListener(self._onBtnGo, self)
    self:getBtn("btnclose/btnClose"):AddClickListener(self.close, self)
    self:getText("txtActivityDescription").text = lang("周年巡游活动描述")
    self:_initRewards()
end

function CelebPartyActView:destroyUI()
    for _, commonIcon in ipairs(self._commonIconsDaily) do
        CommonIconMgr.instance:returnCommonIcon(commonIcon)
    end
    for _, commonIcon in ipairs(self._commonIconsJoin) do
        CommonIconMgr.instance:returnCommonIcon(commonIcon)
    end
    self._commonIconsDaily = nil
    self._commonIconsJoin = nil
    self._items = nil
end

function CelebPartyActView:onEnter()
    Activity471Controller.instance:getAct471InfoRequest(
        function(dailyDrawn, totalJoinCount, dailyJoinCount, drawJoinTimesRewards)
            self._dailyDrawn = dailyDrawn
            self._totalJoinCount = totalJoinCount
            self._dailyJoinCount = dailyJoinCount
            self._drawJoinTimesRewards = drawJoinTimesRewards
            SceneTimer:setTimer(1, self._updateTime, self, true)
            self.mainGO:SetActive(true)
            self:_updateView()
            self:_updateTime()
        end
    )
    self.mainGO:SetActive(false)
    if RedPointController.instance:getRedIsExist("CelebParty") then
        RedPointController.instance:setLastClick("CelebParty")
        RedPointController.instance:setRedIsExist("CelebParty", false)
    end
end

function CelebPartyActView:onExit()
    SceneTimer:removeTimer(self._updateTime, self)
end

function CelebPartyActView:_initRewards()
    local dailyCallRewards = string.split(Activity471Config.getCommonValue("DailyCallRewards"), ",")
    for _, str in ipairs(dailyCallRewards) do
        local define = string.split(str, ":")
        local id = tonumber(define[1])
        local count = tonumber(define[2])
        local params = {}
        params.id = id
        params.count = count
        params.rareness = true
        params.widthAndHeght = 57
        params.fontSize = 18
        local commonIcon = CommonIconMgr.instance:fetchCommonIconWithParams(params)
        goutil.addChildToParent(commonIcon:getPrefab(), self._dailyRewardList)
        table.insert(self._commonIconsDaily, commonIcon)
    end
    local joinTimeRewards = {}
    for _, define in pairs(Activity471Config.getJoinTimeRewards()) do
        table.insert(joinTimeRewards, define)
    end
    table.sort(
        joinTimeRewards,
        function(a, b)
            return a.id < b.id
        end
    )
    for _, v in ipairs(joinTimeRewards) do
        local item = goutil.clone(self._joinRewardItem)
        goutil.addChildToParent(item, self._joinRewardList)
        local commonItemGo = goutil.findChild(item, "commonItemGo")
        local txtItemCont = goutil.findChildTextComponent(item, "txtBg/txtItemCont")
        txtItemCont.text = v.id
        self._items[v.id] = item
        local id = v.rewards[1].id
        local count = v.rewards[1].count
        local params = {}
        params.id = id
        params.count = count
        params.rareness = true
        params.widthAndHeght = 66
        params.fontSize = 18
        local commonIcon = CommonIconMgr.instance:fetchCommonIconWithParams(params)
        goutil.addChildToParent(commonIcon:getPrefab(), commonItemGo)
        table.insert(self._commonIconsJoin, commonIcon)
    end
    self._joinRewardItem:SetActive(false)
    self._joinTimeRewards = joinTimeRewards
end

function CelebPartyActView:_updateView()
    if self._dailyJoinCount > 0 and self._dailyDrawn then
        for _, commonIcon in ipairs(self._commonIconsDaily) do
            commonIcon:showAwardState(CommonIcon.State_Received)
            commonIcon:removeIconLickListener()
        end
    elseif self._dailyJoinCount > 0 and not self._dailyDrawn then
        for _, commonIcon in ipairs(self._commonIconsDaily) do
            commonIcon:showAwardState(CommonIcon.State_CanGet)
            commonIcon:addIconClickListener(self._clickDailyRewardIcon, self)
        end
    else
        for _, commonIcon in ipairs(self._commonIconsDaily) do
            commonIcon:showAwardState(CommonIcon.State_None)
            commonIcon:removeIconLickListener()
        end
    end
    for i, v in ipairs(self._joinTimeRewards) do
        local hasGet = table.indexof(self._drawJoinTimesRewards, v.id)
        if self._totalJoinCount >= v.id and hasGet then
            self._commonIconsJoin[i]:showAwardState(CommonIcon.State_Received)
            self._commonIconsJoin[i]:removeIconLickListener()
        elseif self._totalJoinCount >= v.id and not hasGet then
            self._commonIconsJoin[i]:showAwardState(CommonIcon.State_CanGet)
            self._commonIconsJoin[i]:addIconClickListener(self._clickJoinRewardIcon, self, v.id)
        else
            self._commonIconsJoin[i]:showAwardState(CommonIcon.State_None)
            self._commonIconsJoin[i]:removeIconLickListener()
        end
    end
    self._imgPrg.fillAmount = self._totalJoinCount / self._joinTimeRewards[#self._joinTimeRewards].id
    self._txtTotalJoinTimes.text = lang("累计参与次数：<color=#c76728>{1}</color>", self._totalJoinCount)
end

function CelebPartyActView:_updateTime()
    local isExistShow, define = Activity471Controller.instance:isExistShow()
    self._txtTimeStart.gameObject:SetActive(isExistShow)
    if isExistShow then
        local closeTime = TimeUtil.dateStr2TimeStamp(define.closeTime, "T")
        self._txtTimeStart.text = lang("开启中 {1}", TimeUtil.second2TimeString(closeTime - ServerTime.now()))
    end
end

function CelebPartyActView:_clickDailyRewardIcon()
    Activity471Controller.instance:act471DrawDailyRewardRequest(
        function(changeSetId)
            self._dailyDrawn = true
            DialogHelper.showRewardsDlgByCI(changeSetId, nil, self._updateView, self)
        end
    )
end

function CelebPartyActView:_clickJoinRewardIcon(itemId, joinTimes)
    Activity471Controller.instance:act471DrawTotalJoinRewardRequest(
        joinTimes,
        function(changeSetId)
            table.insert(self._drawJoinTimesRewards, joinTimes)
            DialogHelper.showRewardsDlgByCI(changeSetId, nil, self._updateView, self)
        end
    )
end

function CelebPartyActView:_onBtnGo()
    if not Activity471Controller.instance:isExistShow() then
        FlyTextManager.instance:showFlyText(lang("周年巡游活动时间未到提示"))
        return
    end
    self:close()
    ToyHouseGotoUtil.closeMap()
    if SceneManager.instance:getCurSceneId() ~= 106 then
        SceneManager.instance:loadScene(106)
    end
end

return CelebPartyActView
