module("logic.extensions.traveller.controller.TravellerController", package.seeall)
local TravellerController = class("TravellerController", BaseController)

function TravellerController:onInit()
	-- GlobalDispatcher:addListener(GlobalNotify.EnterScene, self._onEnterScene, self)
	GlobalDispatcher:addListener(GlobalNotify.ItemChange, self.onItemChange, self)
	GlobalDispatcher:addListener(GlobalNotify.OnFuncUnlock, self._onFuncUnlock, self)
	FavorabilityController.instance:registerLocalNotify(FavorabilityNotify.OnFavorabilityUnlock, self.refreshFavorRedDot, self)
	FavorabilityController.instance:registerLocalNotify(FavorabilityNotify.OnFavorabilityAdd, self.refreshFavorRedDot, self)
	FavorabilityController.instance:registerLocalNotify(FavorabilityNotify.OnGetAward, self.refreshFavorRedDot, self)
	FavorabilityController.instance:registerLocalNotify(FavorabilityNotify.OnChatFavorabilityAdd, self.onChatFavorabilityAdd, self)
	GlobalDispatcher:addListener(GlobalNotify.LeaveScene, self._onExitScene, self)
end

function TravellerController:onReset()
	self._isShopOpen = false
	-- GlobalDispatcher:removeListener(GlobalNotify.EnterScene, self._onEnterScene, self)
	GlobalDispatcher:removeListener(GlobalNotify.ItemChange, self.onItemChange, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnFuncUnlock, self._onFuncUnlock, self)
	FavorabilityController.instance:unregisterLocalNotify(FavorabilityNotify.OnFavorabilityUnlock, self.refreshFavorRedDot, self)
	FavorabilityController.instance:unregisterLocalNotify(FavorabilityNotify.OnFavorabilityAdd, self.refreshFavorRedDot, self)
	FavorabilityController.instance:unregisterLocalNotify(FavorabilityNotify.OnGetAward, self.refreshFavorRedDot, self)
	FavorabilityController.instance:unregisterLocalNotify(FavorabilityNotify.OnChatFavorabilityAdd, self.onChatFavorabilityAdd, self)
	GlobalDispatcher:removeListener(GlobalNotify.LeaveScene, self._onExitScene, self)
end

function TravellerController:_onExitScene()
	self._travellerAnimationList = nil
end

function TravellerController:_onEnterScene(userId)
	-- printError("!!!!!", tostring("_onEnterScene"))
	-- if not HouseModel.instance:isInIsland() then
	-- 	return
	-- end
	if userId ~= UserInfo.userId then
		return
	end
	if FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.Traveller, false) then
		self:_requireData()
	end
end

function TravellerController:_requireData()
	TravellerTreasureAgent.instance:sendGetTravellerTreasureInfoRequest()
	TravellerAgent.instance:sendGetTravellerInfoRequest()
end

function TravellerController:_onFuncUnlock(funcId)
	if funcId == FuncIds.Traveller then
		self:_requireData()
	end
end

function TravellerController:onItemChange(itemIdList)
	for i = 1, #itemIdList do
		local item = ItemService.instance:getItem(itemIdList[i])
		if item then
			local subType = item:getDefine().subType
			if subType == PropType.TravellerFragment or subType == PropType.TravellerCommonFragment then
				self:refreshRedDot()
				self:refreshUpgradeRedDot()
				self:localNotify(TravellerNotify.OnFragmentChange)
				break
			end
		end
	end
end

function TravellerController:refreshRedDot()
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if not travellerMO:isUnlock() and travellerMO:getFragmentCount() >= travellerMO:getFragmentNeed() then
			RedPointController.instance:setRedIsExist("TravellerUnlock", true)
			return
		end
	end
	RedPointController.instance:setRedIsExist("TravellerUnlock", false)
end

function TravellerController:refreshFavorRedDot()
	local redDot = false
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if self:checkTravellerFavorRedDot(travellerMO) then
			RedPointController.instance:setRedIsExist("TravellerFavor", true)
			return
		end
	end
	RedPointController.instance:setRedIsExist("TravellerFavor", false)
end

function TravellerController:refreshUpgradeRedDot()
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if self:checkTravellerCanUpgrade(travellerMO) then
			RedPointController.instance:setRedIsExist("TravellerUpgrade", true)
			return
		end
	end
	RedPointController.instance:setRedIsExist("TravellerUpgrade", false)
end

function TravellerController:checkTravellerCanUpgrade(travellerMO)
	if travellerMO:isUnlock() and not travellerMO:isTalentLevelMax() then
		local cnt = travellerMO:getFragmentCount()
		local need = TravellerConfig.getTalentUpgradeDefine(travellerMO.talentLevel).fragmentCost
		-- printError(travellerMO:getName(), cnt, need)
		if cnt >= need then
			return true
		end
	end
	return false
end

function TravellerController:checkTravellerFavorRedDot(travellerMO)
	local npcMo = FavorabilityModel.instance:getNpcItemMoById(travellerMO.config.npcId)
	if not npcMo then
		return false
	end
	local awards = npcMo:getShowAwards()
	for i = 1, #awards do
		local award = awards[i]
		local isUnlock = award.isUnlock()
		local canGet = isUnlock and not award.isGeted
		if canGet then
			return true
		end
	end
	return false
end

function TravellerController:checkTravellerClothesRedDot(travellerMO)
	local p = RedPointController.instance:getNodeByKey(GameEnum.RedPointTypeEnum.TRAVELLER_RED_POINT):getNumberParams()
	if not p then
		return false
	end
	local lst = TravellerConfig.getTravellerAllDress(travellerMO.id)
	for i = 1, #lst do
		local clothesId = lst[i].dressId
		for i = 1, #p do
			if p[i] == clothesId then
				return true
			end
		end
	end
	return false
end

function TravellerController:refreshHasUpgradeRewardRedDot()
	local redDot = false
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if travellerMO:isUnlock() and self:checkTravellerHasUpgradeRewardRedDot(travellerMO) then
			RedPointController.instance:setRedIsExist("TravellerHasUpgradeReward", true)
			return
		end
	end
	RedPointController.instance:setRedIsExist("TravellerHasUpgradeReward", false)
end

function TravellerController:checkTravellerHasUpgradeRewardRedDot(travellerMO)
	local canGet = travellerMO:hasStarAwardToGet()
	return canGet
end

function TravellerController:getTravellerMO(travellerId)
	return TravellerModel.instance:getTravellerMO(travellerId)
end

function TravellerController:getSortedTravellerIdList()
	return TravellerModel.instance:getSortedTravellerIdList()
end

--获取所有生效的buff
function TravellerController:getAllEfficientBuff()
	local combineBuffs = {}
	local treasureBuffs = {}
	local travellerBuffs = {}
	
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if travellerMO:isUnlock() then
			table.insert(travellerBuffs, {id = travellerMO.config.buffId, value = travellerMO.config.buffValue, npcId = travellerMO.config.npcId})
			if travellerMO:isOnIsland() then			
				local talentBuffDefine = TravellerConfig.getTalentBuffDefine(travellerMO.id, travellerMO.talentLevel)
				table.insert(travellerBuffs, {id = talentBuffDefine.buffId, value = talentBuffDefine.buffValue, npcId = travellerMO.config.npcId, isTalent = true})
				
				local treasureMO = TravellerTreasureController.instance:getTreasureMO(travellerMO.treasure)
				if treasureMO then
					local treasureBuffDefine = treasureMO:getBuffDefine()
					table.insert(treasureBuffs, {id = treasureBuffDefine.buffId, value = treasureBuffDefine.buffValue, treasureId = treasureMO.itemId})
				end
			end
		end
	end
	
	local combineDefines = TravellerConfig.getAllCombineDefines()
	for i = 1, #combineDefines do
		local define = combineDefines[i]
		local ids = string.splitToNumber(define.combine, ",")
		local allOnIsland = true
		for i = 1, #ids do
			local id = ids[i]
			local travellerMO = TravellerController.instance:getTravellerMO(id)
			if travellerMO:isOnIsland() == false then
				allOnIsland = false
				break
			end
		end
		if allOnIsland then
			table.insert(combineBuffs, {id = define.buffId, value = define.buffValue, traverllerCombineDefine = define})
		end
	end
	return combineBuffs, treasureBuffs, travellerBuffs
end

--获取可以上岛的旅行者数量
function TravellerController:getOnIslandCount()
	local count = tonumber(GameUtils.getCommonConfig("TravellerInitIslandNum"))
	local userLv = UserInfo.getUserLevel()
	local lvs = string.splitToNumber(GameUtils.getCommonConfig("TravellerExtraIslandNeedLevels"), ",")
	for i = 1, #lvs do
		local lv = lvs[i]
		if userLv >= lv then
			count = count + 1
		end
	end
	return count
end

function TravellerController:openMainPanelWithItemId(type, itemId)
	local travellerId
	if type == PropType.TravellerFragment then
		travellerId = TravellerConfig.getTravellerIdWithUnlockFragmentId(itemId)
	elseif type == PropType.TravellerDress then
		travellerId = TravellerConfig.getTravellerIdByDressId(itemId)
	end
	
	ViewMgr.instance:open("TravellerMainPanel", travellerId)
end

--接收协议
--所有解锁的旅行者信息
function TravellerController:onReceiveTravellers(infos)
	for i = 1, #infos do
		local info = infos[i]
		local travellerMO = self:getTravellerMO(info.travellerId)
		travellerMO:setDataFromServer(info)
	end
	TravellerModel.instance:sortRoleList()
	self:refreshRedDot()
	self:refreshUpgradeRedDot()
	self:refreshFavorRedDot()
	self:refreshHasUpgradeRewardRedDot()
	self:checkTravellerShopOpen()
	self:localNotify(TravellerNotify.OnBuffChange)
end

--解锁旅行者
function TravellerController:onReceiveUnlockTraveller(travellerServerDatas)
	for i = 1, #travellerServerDatas do
		local serverData = travellerServerDatas[i]
		local travellerMO = self:getTravellerMO(serverData.travellerId)
		travellerMO:setDataFromServer(serverData)
	end
	TravellerModel.instance:sortRoleList()
	
	--顺序问题，这条推送比扣除碎片早到，导致红点刷新不正确，强行延时一下
	settimer(0.1, function()
		self:refreshRedDot()
		self:refreshUpgradeRedDot()
		self:localNotify(TravellerNotify.SortChange)
		self:localNotify(TravellerNotify.Unlock)
		self:localNotify(TravellerNotify.OnBuffChange)	
	end, nil, false)
end

--旅行者置顶更改
function TravellerController:onReceiveChangeTop(travellerId)
	local travellerMO = self:getTravellerMO(travellerId)
	travellerMO:onChangeTop()
	TravellerModel.instance:sortRoleList()
	self:localNotify(TravellerNotify.SortChange)
	self:localNotify(TravellerNotify.TopChange)
end

--旅行者升级
function TravellerController:onReceiveTravellerTalentUpgrade(travellerServerData)
	local travellerMO = self:getTravellerMO(travellerServerData.travellerId)
	local oldLevel = travellerMO.talentLevel
	travellerMO:onUpgrade(travellerServerData)
	
	self:checkTravellerShopOpen()
	self:localNotify(TravellerNotify.TalentUpgrade)
	
	
	FlyTextManager.instance:showFlyText(lang("升级天赋成功"))
	if oldLevel ~= travellerMO.talentLevel then
		self:localNotify(TravellerNotify.OnBuffChange)
	end
	self:refreshUpgradeRedDot()
	self:refreshHasUpgradeRewardRedDot()
end

--旅行者的传闻
function TravellerController:onReceiveHearsay(travellerId, heatinfos, latestinfos)
	local travellerMO = self:getTravellerMO(travellerId)
	travellerMO:setHearsayFromServer(heatinfos, latestinfos)
	ViewMgr.instance:open("TravellerHearsayPanel", travellerMO)
end

--传闻操作
function TravellerController:onReceiveHearsayStateChange(travellerId, messageId, state)
	local travellerMO = self:getTravellerMO(travellerId)
	travellerMO:onChangeHearsayState(messageId, state)
	self:localNotify(TravellerNotify.HearsayStateChange, travellerId, messageId)
end

function TravellerController:onReceivePublishHearsay(travellerId, hearsay)
	FlyTextManager.instance:showFlyText(lang("发布传闻成功"))
	local travellerMO = self:getTravellerMO(travellerId)
	travellerMO:onSendHearsay(hearsay)
	self:localNotify(TravellerNotify.OnSendHearsay)
end

--换装
function TravellerController:onReceiveChangeDress(travellerId, dressId)
	local travellerMO = self:getTravellerMO(travellerId)
	travellerMO:onChangeDressId(dressId)
end

--更换宝物
--travellerId 当前变更的旅行者
--treasureId 当前变更的旅行者新装备上的宝物or脱下
--oldTravellerId 因变更而被脱掉宝物的旅行者（可能没有,视乎treasureId原来有没有被装备）
function TravellerController:onReceiveChangeTreasure(travellerId, treasureId, oldTravellerId)
	local travellerMO = self:getTravellerMO(travellerId)
	local oldTreasureId = travellerMO.treasure
	travellerMO:setTreasureId(treasureId)
	
	local oldTravellerMO = self:getTravellerMO(oldTravellerId)	
	if oldTravellerMO then
		oldTravellerMO:setTreasureId(0)
	end
	
	self:localNotify(TravellerNotify.ChangeTreasure, treasureId, oldTreasureId, travellerId)
	self:localNotify(TravellerNotify.OnBuffChange)
end

--尝试上/下岛
function TravellerController:tryLanding(travellerId)
	ViewMgr.instance:open("TravellerLibrary", travellerId)
end

--上下岛
function TravellerController:onReceiveChangeScene(infos)
	local lst = {}
	local curSceneId = RoomConfig.getShowAreaIdBySceneId(SceneManager.instance:getCurSceneId())
	for travellerId, sceneId in pairs(infos) do
		local travellerMO = self:getTravellerMO(travellerId)
		if not travellerMO:isOnIsland() and sceneId == curSceneId then
			table.insert(lst, travellerMO)
		end
		travellerMO:onChangeSceneId(sceneId)
	end
	self:localNotify(TravellerNotify.OnIslandChange)
	self:localNotify(TravellerNotify.OnGoIslandChange)
	
	if #lst > 0 then
		ViewMgr.instance:open("TravellerTipsPanel", 2, lst)
	end
end

--成功领取旅行者星图奖励
function TravellerController:onReceiveTravellerStarMapAward(travellerId, drawnReward, changeSetId)
	local travellerMO = self:getTravellerMO(travellerId)
	travellerMO:onGetStarMapAward(drawnReward)
	DialogHelper.showRewardsDlgByCI(changeSetId)
	self:localNotify(TravellerNotify.OnGetStarMapAward)
	self:refreshHasUpgradeRewardRedDot()
end

function TravellerController:onTravellerScoreChange(msg)
	for i = 1, #msg.scoreChangeInfos do
		local travellerMO = self:getTravellerMO(msg.scoreChangeInfos[i].travellerId)
		travellerMO:refreshScore(msg.scoreChangeInfos[i].score)
	end
	self:localNotify(TravellerNotify.OnScoreUpdate)
end

function TravellerController:onTravellerGoIsland(ids)
	for i = 1, #ids do
		local travellerMO = self:getTravellerMO(ids[i])
		travellerMO:switchGoIsland()
	end
	self:localNotify(TravellerNotify.OnGoIslandChange)
end

function TravellerController:canCelTravellerGoIsland(ids)
	for i = 1, #ids do
		local travellerMO = self:getTravellerMO(ids[i])
		if travellerMO:isGoIsland() then
			travellerMO:switchGoIsland()
		end
	end
	self:localNotify(TravellerNotify.OnGoIslandChange)
end

--协议-end
function TravellerController:setTravellerTips(randomTravellers)
	self.randomTravellers = randomTravellers
end

function TravellerController:checkTravellerTips(finishCb)
	if self.randomTravellers then
		ViewMgr.instance:open("TravellerTipsPanel", 1, self.randomTravellers, finishCb)
	else
		finishCb()
	end
	
end

function TravellerController:checkTravellerShopOpen()
	if not self._isShopOpen then
		for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
			if travellerMO:isUnlock() and travellerMO:isTalentLevelMax() then
				self._isShopOpen = true
				break
			end
		end
	end
	
	if not LocalStorage.instance:getValue(StorageKey.TravellerOpenUnlock, false) and self._isShopOpen then
		RedPointController.instance:setRedIsExist("TravellerShopUnlock", true)
	end
end

function TravellerController:isTravellerShopOpen()
	return self._isShopOpen
end

function TravellerController:gotoTraveller(travellerId)
	local mo = TravellerController.instance:getTravellerMO(travellerId)
	local lst = self:getOnIslandAndGoIsland()
	if not table.indexof(lst, travellerId) then
		local msg = lang("{1}未上岛，要邀请上岛吗？", mo:getName())
		DialogHelper.showConfirmDlg(msg, function(bOK)
			if bOK then
				self:tryLanding(travellerId)
			end
		end)
		return false
	end
	--闲时上岛
	if not mo:isOnIsland() then
		local u = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SceneUnitType.Npc, mo.config.npcId)
		if u then
			u.travellerBehavior:startBehavior(TravellerBehaviorType.Click)
			return true
		end
		return false
	end
	
	local curSceneId = RoomConfig.getShowAreaIdBySceneId(SceneManager.instance:getCurSceneId())
	if not HouseModel.instance:isInHouseOrIsland(HouseModel.Either, HouseModel.Mine, true, HouseModel.NormalIsland) or curSceneId ~= mo.sceneId then
		self._gotoTargetTravellerId = travellerId
		self:registerLocalNotify(TravellerNotify.OnCreateTravellerUnit, self._onCreateTravellerUnit, self)
		local sceneId
		if mo.sceneId == 0 then
			FarmFacade.instance:enterIsland(nil, nil, nil, 0)
		else
			local houseId = MyHouseData.instance:getArea(mo.sceneId).houseId
			RoomFacade.instance:enterRoomScene(UserInfo.userId, houseId, nil, nil, nil, nil, mo.sceneId)
		end
	else
		local u = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SceneUnitType.Npc, mo.config.npcId)
		if u then
			u.travellerBehavior:startBehavior(TravellerBehaviorType.Click)
		end
	end
	return true
end

function TravellerController:_onCreateTravellerUnit()
	self:unregisterLocalNotify(TravellerNotify.OnCreateTravellerUnit, self._onCreateTravellerUnit, self)
	local mo = TravellerController.instance:getTravellerMO(self._gotoTargetTravellerId)
	local u = SceneManager.instance:getCurScene():getUnitMgr():getUnit(SceneUnitType.Npc, mo.config.npcId)
	if u then
		u.travellerBehavior:startBehavior(TravellerBehaviorType.Click)
	end
	self._gotoTargetTravellerId = nil
end

--获取上了岛的，闲时上岛也随到了的旅行者id列表
function TravellerController:getOnIslandAndGoIsland()
	local lst = {}
	local tempMap = {}
	--上了岛的
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if travellerMO:isOnIsland() then
			table.insert(lst, travellerMO.id)
			tempMap[travellerMO.id] = true
		end
	end
	if not HouseModel.instance:_isMine() then
		return lst
	end
	--闲时上岛
	local travellerComp = SceneManager.instance:getCurScene().traveller
	if travellerComp and travellerComp._npcList then
		for travellerId, unit in pairs(travellerComp._npcList) do
			if not tempMap[travellerId] then
				local _curB = unit.travellerBehavior._curBehavior
				if not(_curB and _curB.type == TravellerBehaviorType.LeaveIsland) then
					table.insert(lst, travellerId)
				end
			end
		end
	end
	return lst
end

function TravellerController:getOnIslandTravellers()
	local lst = {}
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if travellerMO:isOnIsland() then
			table.insert(lst, travellerMO)
		end
	end
	return lst
end

function TravellerController:onChatFavorabilityAdd(npcId)
	for _, travellerMO in pairs(TravellerModel.instance:getAllTravellers()) do
		if travellerMO.config.npcId == npcId then
			travellerMO:onChatFavorabilityAdd()
		end
	end
end

function TravellerController:randomAninationList()
	if self._travellerAnimationList == nil or #self._travellerAnimationList == 0 then
		local allTravellers = HouseModel.instance:getUserProperty(HouseUserProperty.Travellers, {})
		local travellerModel = SceneManager.instance:getCurScene().traveller:getTravellerModel()
		self._travellerAnimationList = {}
		for i, info in ipairs(allTravellers) do
			table.insert(self._travellerAnimationList, travellerModel:getTravellerMO(info.travellerId))
		end
		arrayutil.randomAry(self._travellerAnimationList)
		table.sort(self._travellerAnimationList, function(a, b)
			local tA = TravellerConfig.getTraveller(a.id)
			local tB = TravellerConfig.getTraveller(b.id)
			return tA.actionPriority > tB.actionPriority
		end)
	end
end

function TravellerController:getNextAnimationTraveller()
	if self._travellerAnimationList == nil or #self._travellerAnimationList == 0 then
		self:randomAninationList()
	end
	return self._travellerAnimationList[1]
end

function TravellerController:isEnableScene()
	local enableIsland = HouseModel.instance:isInIsland(HouseModel.NormalIsland)
	local enableHouse = HouseModel.instance:isInHouse(true, HouseModel.NormalIsland)
	local enableFunc = FuncUnlockModel.instance:isUnlocked(FuncIds.Traveller)
	return enableFunc and (enableIsland or enableHouse)
end

function TravellerController:popAnimationTraveller()
	table.remove(self._travellerAnimationList, 1)
end

TravellerController.instance = TravellerController.New()
return TravellerController 