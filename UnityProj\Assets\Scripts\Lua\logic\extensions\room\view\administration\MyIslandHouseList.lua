module("logic.extensions.room.view.administration.MyIslandHouseList",package.seeall)

local MyIslandHouseList = class("MyIslandHouseList", ListBinderView)

MyIslandHouseList.DragHouseGoUrl = "ui/scene/room/myisland_draghouse.prefab"

function MyIslandHouseList:ctor()
	MyIslandHouseList.super.ctor(self, BaseListModel.New(), "edit/itemList", MyIslandHouseCell.ItemUrl, MyIslandHouseCell, 
		{kScrollDirH, 216, 155, 0, 0, 1})
end

--- view初始化时会执行，在buildUI之后
function MyIslandHouseList:bindEvents()
	MyIslandHouseList.super.bindEvents(self)
	self:getBtn("btnPut"):AddClickListener(self._onClickPut, self)
	self.globalTrigger = Framework.UIGlobalTouchTrigger.Get(self:getGo("edit/itemList"))
	self.globalTrigger:AddIgnoreTargetListener(self._onGlobalClick, self)
end

--- view销毁时会执行，在destroyUI之前
function MyIslandHouseList:unbindEvents()
	MyIslandHouseList.super.unbindEvents(self)
end

--- view初始化时会执行
function MyIslandHouseList:buildUI()
    self.uiRectTransform = ViewMgr.instance._uiRoot.transform
	self._mainCamera = CameraTargetMgr.instance:getUICameraTarget():getCamera()
	MyIslandHouseList.super.buildUI(self)
	self.hideGoList = {
		self:getGo("btnManagement"), self:getGo("btnPut"), self:getGo("btnEffects")
	}
	self.showGoList = {
		self:getGo("edit")
	}
end

function MyIslandHouseList:onExit()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.AllNotInScreenFurnitureLoaded, self._onLoadAllFurniture, self)
	MyIslandHouseList.super.onExit(self)
	if self.dragHouseGo then
		IconLoader.clearImage(goutil.findChild(self.dragHouseGo, "imgIcon"))
	end
end

function MyIslandHouseList:_onClickPut()
	local allHouse = RoomConfig.getAllHousueConfig()
	for i = 1, #allHouse do
		if ItemService.instance:getNewState(allHouse[i].houseFurnitureId) then
			ItemService.instance:setNewState(allHouse[i].houseFurnitureId, false)
		end
	end
	RedPointController.instance:setRedIsExist("IslandManager_NewHouse", false)
	if RoomEditModel.instance._loadAllFurniture then
		self:setEdit(true)
	else
		LoadingMask.instance:show()
		RoomController.instance:registerLocalNotify(RoomNotifyName.AllNotInScreenFurnitureLoaded, self._onLoadAllFurniture, self)
	end
end

function MyIslandHouseList:_onLoadAllFurniture()
	LoadingMask.instance:close()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.AllNotInScreenFurnitureLoaded, self._onLoadAllFurniture, self)
	self:setEdit(true)
end

function MyIslandHouseList:setEdit(isEdit)
	local isMyHouse = self._viewPresentor:isMyHouse()
	if not isMyHouse then
		isEdit = false
	end
	for i=1, #self.hideGoList do
		goutil.setActive(self.hideGoList[i], not isEdit and isMyHouse)
	end
	for i=1, #self.showGoList do
		goutil.setActive(self.showGoList[i], isEdit and isMyHouse)
	end
	if isEdit then
		self:resetData()
	end
	self._viewPresentor.mainView:setEdit(isEdit)
end

function MyIslandHouseList:resetData()
	local result = {}
	local houseData = MyHouseData.instance
	local allHouse = houseData:getMyHouseList()
	for i=1, #allHouse do
		if not houseData:isUse(allHouse[i].id) and not allHouse[i].isEmpty then
			table.insert(result, allHouse[i])
		end
	end
	self._listModel:setMoList(result)
	goutil.setActive(self:getGo("edit/txtNull"), #result == 0)
end

function MyIslandHouseList:_handleBeginDrag(houseMo, pos)
	self:_createDragHouseGo()
	self.dragHouseMo = houseMo
	goutil.setActive(self.dragHouseGo, true)
	IconLoader.setSpriteToImg(goutil.findChild(self.dragHouseGo, "imgIcon"), GameUrl.getRoomOutsideUrl(houseMo.houseType, houseMo.level))
	self:_setHouseGoPos(pos)
	self._viewPresentor.mainView:onBeginDragHouseGo()
end

function MyIslandHouseList:_handleDrag(houseMo, pos)
	self:_setHouseGoPos(pos)
end

function MyIslandHouseList:_handleEndDrag(houseMo, pos)
	goutil.setActive(self.dragHouseGo, false)
	self._viewPresentor.mainView:onEndDragHouseGo(houseMo, pos)
	self.dragHouseMo = nil
end

function MyIslandHouseList:_setHouseGoPos(pos)
    local b, result = UnityEngine.RectTransformUtility.ScreenPointToWorldPointInRectangle(self.uiRectTransform, pos, self._mainCamera, Vector3.New(0, 0, 0))
	GameUtils.setPos(self.dragHouseGo, result.x, result.y, 0)
end

function MyIslandHouseList:_createDragHouseGo()
	if self.dragHouseGo ~= nil then return end
	self.dragHouseGo = self:getResInstance(MyIslandHouseList.DragHouseGoUrl)
	goutil.addChildToParent(self.dragHouseGo, ViewMgr.instance:getRoot(ViewRootType.Top))
end

function MyIslandHouseList:preventHide()
	self._preventHide = true
end

function MyIslandHouseList:_onGlobalClick()
	if self._preventHide then
		self._preventHide = false
		return
	end
	if self._viewPresentor.mainView._showTips then return end
	self:setEdit(false)
end

return MyIslandHouseList