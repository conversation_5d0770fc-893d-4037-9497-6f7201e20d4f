using System;
using UnityEngine;
using LuaInterface;
using FMODUnity;
using FMOD.Studio;
using FMOD;


[Serializable]
public struct SoundEventParam
{
    public string Name;
    public float Value;
}

public class SoundEventInstance : MonoBehaviour
{
    public string Event;
    private EventDescription _description;
    private EventInstance _instance;
    private Vector3 _lastPosition;
    private bool _isPlaying;
    private bool _is3D;
    private bool _isOneShot;
    private Action _soundCB;

    public static SoundEventInstance Add(string evt, GameObject go)
    {
        var com = go.AddComponent<SoundEventInstance>();
        if (!com.Init(evt))
        {
            GameObject.Destroy(com);
            return null;
        } 
        return com;
    }

    public bool Init(string evt)
    {
        EventDescription desc = RuntimeManager.GetEventDescription(evt);
        if (!desc.isValid()) return false;
        _description = desc;
        _description.is3D(out _is3D);
        _description.isOneshot(out _isOneShot);
        Event = evt;
        return true;
    }

    public PLAYBACK_STATE State
    {
        get
        {
            PLAYBACK_STATE state = PLAYBACK_STATE.STOPPED;
            if (_instance.isValid())
            {
                _instance.getPlaybackState(out state);
            }
            return state;
        }
    }

    public void Play(SoundEventParam[] param, Action soundLoadCallback)
    {
        enabled = true;

        if (!_instance.isValid())
        {
            _description.createInstance(out _instance);
        }
        if (param != null)
        {
            foreach (SoundEventParam p in param)
            {
                _instance.setParameterByName(p.Name, p.Value);
            }
        }

        if (_is3D)
        {
            _lastPosition = transform.position;
            _instance.set3DAttributes(RuntimeUtils.To3DAttributes(_lastPosition));
        }

        if (soundLoadCallback != null) _soundCB = soundLoadCallback;

        _instance.start();
        _isPlaying = true;
    }

    public void SetEventParams(LuaTable param)
	{
        var dict = param.ToDictTable();
        foreach (var p in dict)
        {
            _instance.setParameterByName((string)p.Key, Convert.ToSingle(p.Value));
        }
    }

    public void Stop(FMOD.Studio.STOP_MODE stopMode)
    {
        if (_instance.isValid())
        {
            _instance.stop(stopMode);
        }
        _isPlaying = false;
    }

    public void JumpToPos(int pos)
    {
        if (_instance.isValid())
        {
            _instance.setTimelinePosition(pos);
        }
    }

    public int getTime()
    {
        int res = 0;
        if (_instance.isValid())
        {
            _instance.getTimelinePosition(out res);
        }
        return res;
    }

    private static RESULT OnOneShotFinished(EVENT_CALLBACK_TYPE type, EventInstance _event, IntPtr parameters)
    {
        if (_event.isValid())
        {
            _event.release();
            _event.clearHandle();
        }

        return RESULT.OK;
    }

    private void LateUpdate()
    {
        var s = State;
        if (s == PLAYBACK_STATE.STOPPED || s == PLAYBACK_STATE.STOPPING)
        {
            enabled = false;
            return;
        }
        {
            if (_is3D && transform.position != _lastPosition)
            {
                _lastPosition = transform.position;
                _instance.set3DAttributes(RuntimeUtils.To3DAttributes(_lastPosition));
            }
            if (_soundCB != null && s == PLAYBACK_STATE.PLAYING)
            {
                _soundCB.Invoke();
                _soundCB = null;
            }
        }
    }

    private void OnDestroy()
    {
        if (_instance.isValid())
        {
            _instance.stop(FMOD.Studio.STOP_MODE.ALLOWFADEOUT);
            _instance.release();
            _instance.clearHandle();
        }
        _isPlaying = false;
    }
}
