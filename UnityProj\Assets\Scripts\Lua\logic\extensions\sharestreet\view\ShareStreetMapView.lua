module("logic.extensions.sharestreet.view.ShareStreetMapView",package.seeall)
---@class ShareStreetMapView
local ShareStreetMapView = class("ShareStreetMapView",ViewComponent)

function ShareStreetMapView:ctor()
	ShareStreetMapView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetMapView:buildUI()
	ShareStreetMapView.super.buildUI(self)

	self._btnClose = self:getBtn("btnclose/btnClose")
	self._btnEdit = self:getBtn("btnEdit")
	self._btnGo = self:getBtn("btnGo")
	self._txtDes = self:getText("titleGo/txtDes")
	self._btnInfo = self:getBtn("btnMess")
	self._txtName = self:getText("titleGo/txtName")
	self._btnApply = self:getBtn("btnCheck")
	self._btnOther = self:getBtn("btnOther")
	self._goJoinOtherTips = self:getGo("btnOther/bubble")
	self._btnHelp = self:getBtn("btnhelp")

	self._txtLikeNum = self:getText("top_1/txtCount")
	self._txtVisitNum = self:getText("top_2/txtCount")
	self._txtAreaNum = self:getText("top_3/txtCount")

	self._btnMsg = self:getBtn("btnMsg")
	self._goMap = self:getGo("mapGo")

	self._goMsgRed = self:getGo("btnMsg/redPoint")
	self._goInfoRed = self:getGo("btnMess/redPoint")
	if self._goInfoRed then
		self._goInfoRed:SetActive(false)
	end
	self._goApplyRed = self:getGo("btnCheck/redPoint")

	self._mapItemList = {}

	for i = 1, ShareStreetCfg.instance:getAreaCount() do
		local areaCfg = ShareStreetCfg.instance:getAreaConfigByIndex(i)
		print("areaCfg.id", areaCfg.id)
		local playerGo = self:getGo("mapGo/player_" .. i)
		table.insert(self._mapItemList, ShareStreetMapItem.New(playerGo, i, areaCfg.id))
	end
end

--- view初始化时会执行，在buildUI之后
function ShareStreetMapView:bindEvents()
	ShareStreetMapView.super.bindEvents(self)
	
	self._btnClose:AddClickListener(self.close, self)
	self._btnHelp:AddClickListener(self._onClickHelp, self)

	self._btnEdit:AddClickListener(self._onClickEdit, self)
	self._btnGo:AddClickListener(self._onClickGo, self)
	self._btnOther:AddClickListener(self._onClickOther, self)
	self._btnMsg:AddClickListener(self._onClickMsg, self)
	self._btnApply:AddClickListener(self._onClickApply, self)
	self._btnInfo:AddClickListener(self._onClickInfo, self)
	
	for _, mapItem in ipairs(self._mapItemList) do
		mapItem:addAddHandler(handler(self._onClickAddPlayer, self))
		mapItem:addDeleteHandler(handler(self._onClickDelPlayer, self))
		mapItem:addUnlockHandler(handler(self._onClickUnlockArea, self))
		mapItem:addQuitHandler(handler(self._onClickQuit, self))
	end

	self:registerLocalNotify(ShareStreetLocalNotify.OnJoinOtherStreetSucc, self._onJoinSucc, self)
	self:registerLocalNotify(ShareStreetLocalNotify.OnAcceptJoinRequestSucc, self._onAcceptJoinSucc, self)

	self:registerLocalNotify(ShareStreetLocalNotify.OnChangeNameSucc, self._onChangeNameSucc, self)
	self:registerLocalNotify(ShareStreetLocalNotify.OnChangeDescSucc, self._onChangeDescSucc, self)
	
	-- 注册邀请和申请状态变更通知
	self:registerLocalNotify(ShareStreetLocalNotify.OnHasInvitedStatusChanged, self._onHasInvitedStatusChanged, self)
	self:registerLocalNotify(ShareStreetLocalNotify.OnHasApplyStatusChanged, self._onHasApplyStatusChanged, self)
	
	-- 注册退出街区成功通知
	self:registerLocalNotify(ShareStreetLocalNotify.OnQuitSucc, self._onQuitSucc, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetMapView:onEnter()
	ShareStreetMapView.super.onEnter(self)

	self._info = ShareStreetModel.instance:getUserInfo(self:getOpenParam()[1])
	self._ownerId = self._info:getOwnerId()

	print("ownerId", self._ownerId)
	print("userId", UserInfo.userId)

	RedPointController.instance:registerRedPoint(self._goMsgRed, {"ShareStreet_InviteRed"})
	RedPointController.instance:registerRedPoint(self._goApplyRed, {"ShareStreet_ApplyRed"})

	self:_updateInfo()

	self:registerNotify(GlobalNotify.StartLoadScene, self.close, self)

	if RedPointFacade.getIsExistByName("ShareStreet_FirstClick") then
		RedPointController.instance:setLastClick("ShareStreet_FirstClick", ServerTime.now())
		self:_onClickHelp()
	end
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetMapView:onEnterFinished()
	ShareStreetMapView.super.onEnterFinished(self)

	GuideArrowManager.instance:regKeyGO(GuideArrowKey.ShareStreet_Other, self._btnOther.gameObject)
	GuideArrowManager.instance:regKeyGO(GuideArrowKey.ShareStreet_Edit, self._btnEdit.gameObject)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetMapView:onExit()
	ShareStreetMapView.super.onExit(self)

	RedPointController.instance:unregisterRedPoint(self._goMsgRed)
	RedPointController.instance:unregisterRedPoint(self._goApplyRed)

	self:unregisterNotify(GlobalNotify.StartLoadScene, self.close, self)

	GuideArrowManager.instance:unregKeyGO(GuideArrowKey.ShareStreet_Other)
	GuideArrowManager.instance:unregKeyGO(GuideArrowKey.ShareStreet_Edit)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetMapView:onExitFinished()
	ShareStreetMapView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetMapView:unbindEvents()
	ShareStreetMapView.super.unbindEvents(self)

	self._btnClose:RemoveClickListener()
	self._btnHelp:RemoveClickListener()
	self._btnEdit:RemoveClickListener()
	self._btnGo:RemoveClickListener()
	self._btnOther:RemoveClickListener()
	self._btnMsg:RemoveClickListener()
	self._btnApply:RemoveClickListener()

	self:unregisterLocalNotify(ShareStreetLocalNotify.OnJoinOtherStreetSucc, self._onJoinSucc, self)
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnAcceptJoinRequestSucc, self._onAcceptJoinSucc, self)

	self:unregisterLocalNotify(ShareStreetLocalNotify.OnChangeNameSucc, self._onChangeNameSucc, self)
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnChangeDescSucc, self._onChangeDescSucc, self)
	
	-- 取消注册邀请和申请状态变更通知
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnHasInvitedStatusChanged, self._onHasInvitedStatusChanged, self)
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnHasApplyStatusChanged, self._onHasApplyStatusChanged, self)
	
	-- 取消注册退出街区成功通知
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnQuitSucc, self._onQuitSucc, self)
end

--- view销毁时会执行
function ShareStreetMapView:destroyUI()
	for _, mapItem in ipairs(self._mapItemList) do
		mapItem:destroy()
	end
	self._mapItemList = nil

	ShareStreetMapView.super.destroyUI(self)
end

function ShareStreetMapView:_updateInfo()
	self._txtName.text = self._info:getName()
	self._txtDes.text = self._info:getDesc()
	self._txtLikeNum.text = self._info:getLikeCount()
	self._txtVisitNum.text = self._info:getVisitCount()
	self._txtAreaNum.text = string.format("%s/%s", self._info:getMemberCount(), self._info:getUnlockedAreaIdCount())

	local memberList = self._info:getMemberList()
	local unlockAreaIdList = self._info:getUnlockedAreaIdList()
	local isReadOnly = self._info:getOwnerId() ~= UserInfo.userId

	local upgradeAreaId = self._info:getLastCanUpgradeAreaId()
	local canUnlockAreaIdList = {}
	if upgradeAreaId then
		local upgradeConfig = ShareStreetCfg.instance:getUpgradeConfigById(upgradeAreaId)
		local areaIds = upgradeConfig.areaIds
		for i, areaId in ipairs(areaIds) do
			table.insert(canUnlockAreaIdList, areaId)
		end
	end
	for i, mapItem in ipairs(self._mapItemList) do
		local areaId = mapItem:getAreaId()
		local isUnlock = table.indexof(unlockAreaIdList, areaId) ~= false
		local memberInfo = self._info:getMemberByAreaId(areaId)
		local isOwner = false
		if memberInfo then
			isOwner = memberInfo.roleInfo.id == self._info:getOwnerId()
		end
		if enableLog then
			print("index", i, "isOwner", isOwner, "memberId",memberInfo and memberInfo.roleInfo.id, "ownerId", self._info:getOwnerId(), "isUnlock", isUnlock, "areaId", areaId)
		end
		mapItem:setInfo(isOwner, isReadOnly, isUnlock, memberInfo and memberInfo.roleInfo)
		if not isUnlock then
			if table.indexof(canUnlockAreaIdList, areaId) then
				mapItem:setNullImageAlpha(1)
			else
				mapItem:setNullImageAlpha(0.5)
			end
		end
	end
	self._goJoinOtherTips:SetActive(#memberList <= 1 and self._info:getOwnerId() == UserInfo.userId)
	-- 只有当用户是房主且成员数小于等于1且存在申请或邀请时才显示消息按钮
	local hasInvited = ShareStreetModel.instance:getHasInvited()
	local hasApply = ShareStreetModel.instance:getHasApply()
	local hasRed = RedPointFacade.getIsExistByName("ShareStreet_InviteRed")
	print("hasRed", hasRed, "hasInvited", hasInvited, "hasApply", hasApply)
	self._btnMsg.gameObject:SetActive(self._info:getOwnerId() == UserInfo.userId and #memberList <= 1 and (hasRed or hasInvited or hasApply))
	self._btnApply.gameObject:SetActive(self._ownerId == UserInfo.userId)
end

function ShareStreetMapView:_onClickHelp()
	print("help")
	ViewMgr.instance:open("HelpView", {"yindao_jiequ_01", "yindao_jiequ_02", "yindao_jiequ_03", "yindao_jiequ_04"})
end

function ShareStreetMapView:_onClickEdit()
	print("edit")
	ViewMgr.instance:open("ShareStreetFurnitureView")
end

function ShareStreetMapView:_onClickGo()	
	print("go")
	self:close()
	ShareStreetFacade.instance:enterMyStreetScene()
end

function ShareStreetMapView:_onClickOther()
	print("other")
	ShareStreetFacade.instance:showRankView()
end

function ShareStreetMapView:_onClickMsg()
	print("msg")
	ShareStreetFacade.instance:showAcceptJoinRequestView()
end

function ShareStreetMapView:_onJoinSucc(ownerId)
	print("ShareStreetMapView:_onJoinSucc", ownerId)
	self._info = ShareStreetModel.instance:getUserInfo(ownerId)
	self._ownerId = self._info:getOwnerId()
	self:_updateInfo()
end

function ShareStreetMapView:_onAcceptJoinSucc(playerId)
	print("ShareStreetMapView:_onAcceptJoinSucc", playerId)
	self:_updateInfo()
end

function ShareStreetMapView:_onClickApply()
	print("apply")
	ShareStreetFacade.instance:showAcceptInviteView()
end

function ShareStreetMapView:_onClickInfo()
	print("info")
	local isReadOnly = self._info:getOwnerId() ~= UserInfo.userId
	ViewMgr.instance:open("ShareStreetInfoView", isReadOnly, self._info:getOwnerId())
end

function ShareStreetMapView:onClickHelp()
	ViewMgr.instance:open("ActivityRuleCommon", lang("嘉年华规则标题"), lang("嘉年华规则标题描述"))
end

function ShareStreetMapView:_onClickAddPlayer()
	print("add")
	ShareStreetFacade.instance:showInviteFriendView()
end

function ShareStreetMapView:_onClickDelPlayer(playerId)
	print("del", playerId)
	DialogHelper.showConfirmDlg(lang("是否要踢除此成员？"), handlerWithParams(self._onConfirmDelPlayer, self, {playerId}))
end

function ShareStreetMapView:_onConfirmDelPlayer(playerId, bool)
	print("_onConfirmDelPlayer", bool, playerId)
	if bool then
		ShareStreetController.instance:kick(playerId, handler(self._updateInfo, self))
	end
end

function ShareStreetMapView:_onClickUnlockArea(index, areaId)
    print("unlock", index)
    local upgradeAreaId = self._info:getLastCanUpgradeAreaId()
    if upgradeAreaId then
        local upgradeConfig = ShareStreetCfg.instance:getUpgradeConfigById(upgradeAreaId)
        local areaIds = upgradeConfig.areaIds
        local cfg1 = ShareStreetCfg.instance:getAreaConfigById(areaIds[1])
        local cfg2 = ShareStreetCfg.instance:getAreaConfigById(areaIds[2])
        if table.indexof(areaIds, areaId) == false then
            FlyTextManager.instance:showFlyText(lang("请先解锁{1}和{2}", cfg1.name, cfg2.name))
            return
        end
        DialogHelper.showConfirmDlg(lang(
            "是否花费<color=#e38534>{1}晶钻</color>，新增<color=#e38534>{2}</color>和<color=#e38534>{3}</color>两个街区区域？",
            upgradeConfig.consumeCurrency.count, cfg1.name, cfg2.name), handler(self._onUnlockArea, self))
    end
end

function ShareStreetMapView:_onClickQuit()
	print("quit")
	DialogHelper.showConfirmDlg(lang("退出后将恢复独立的街区状态,但保留当前的街区摆设,确定退出街区?"), handler(self._onConfirmQuit, self))
end

function ShareStreetMapView:_onConfirmQuit(bool)
	if bool then
		ShareStreetController.instance:quit(handler(function()
			-- 退出成功后，重新获取用户自己的街区信息
			self._info = ShareStreetModel.instance:getUserInfo(UserInfo.userId)
			self._ownerId = self._info:getOwnerId()
			self:_updateInfo()
		end, self))
	end	
end

function ShareStreetMapView:_onUnlockArea(isConfirm)
	if isConfirm then
		local upgradeAreaId = self._info:getLastCanUpgradeAreaId()
		if upgradeAreaId then
			ShareStreetController.instance:unlockArea(upgradeAreaId, handler(self._onUnlockAreaSucc, self))
		end
	end
end

function ShareStreetMapView:_onUnlockAreaSucc(upgradeAreaId)
	local areaIdList = {}
	local upgradeCfg = ShareStreetCfg.instance:getUpgradeConfigById(upgradeAreaId)
    if upgradeCfg then
		for __, areaId in ipairs(upgradeCfg.areaIds) do
            table.insert(areaIdList, areaId)
        end
	end
	for i, mapItem in ipairs(self._mapItemList) do
		if table.indexof(areaIdList, mapItem:getAreaId()) then
			if not self._unlockAniCount then
				self._unlockAniCount = 0
			end
			self._unlockAniCount = self._unlockAniCount + 1
			mapItem:playUnlockAni(handler(self._onUnlockAniEnd, self))
		end
	end
end

function ShareStreetMapView:_onUnlockAniEnd()
	self._unlockAniCount = self._unlockAniCount - 1
	if self._unlockAniCount == 0 then
		self:_updateInfo()
	end
end

function ShareStreetMapView:_onChangeNameSucc()
	self._txtName.text = self._info:getName()
end

function ShareStreetMapView:_onChangeDescSucc()
	self._txtDes.text = self._info:getDesc()
end

-- 处理邀请状态变更通知
function ShareStreetMapView:_onHasInvitedStatusChanged(hasInvited)
	print("ShareStreetMapView:_onHasInvitedStatusChanged", hasInvited)
	self:_updateInfo()
end

-- 处理申请状态变更通知
function ShareStreetMapView:_onHasApplyStatusChanged(hasApply)
	print("ShareStreetMapView:_onHasApplyStatusChanged", hasApply)
	self:_updateInfo()
end

function ShareStreetMapView:_onQuitSucc()
	print("ShareStreetMapView:_onQuitSucc")
	-- 退出街区后，重新获取用户自己的街区信息
	self._info = ShareStreetModel.instance:getUserInfo(UserInfo.userId)
	self._ownerId = self._info:getOwnerId()
	self:_updateInfo()
end

return ShareStreetMapView