module("logic.extensions.tetris.game.view.TetrisGameResultView",package.seeall)
---@class TetrisGameResultView
local TetrisGameResultView = class("TetrisGameResultView",ViewComponent)

--- view初始化时会执行
function TetrisGameResultView:buildUI()
	self._btnClose = self:getBtn("imgGroup/btnClose")
	self._btnOK = self:getBtn("btnOK")
	self._btnAgain = self:getBtn("btnAgain")
	self._btnBack = self:getBtn("btnBack")
	self._btnQuit = self:getBtn("btnQuit")
	self._headBgGo = self:getGo("imgGroup/imgBg")
	self._headGo = self:getGo("imgGroup/imgHead")
	self._nameText = self:getText("txtFriend")
	self._scoreText = self:getText("txtMsg")
	self._txtHint = self:getText("txtHint")
	self._teamText = self:getText("txtTeam")
	self._txtReward = self:getGo("txtReward")
	self._titleNameGo = self:getGo("txtTitle")
	self._loseTitleGo = self:getGo("imgGroup/imgLoseTitle")
	self._winTitleGo = self:getGo("imgGroup/imgWinTitle")
	self._equalTitleGo = self:getGo("imgGroup/imgEqualTitle")
	self._newScoreGo = self:getGo("newScore")
	
	self._customHideGo1 = self:getGo("imgGroup/frame")
	self._customHideGo2 = self:getGo("imgGroup/imgStar_1")
	self._customHideGo3 = self:getGo("imgGroup/imgStar_2")

	self._rewardContent = self:getGo("rewards")

	goutil.setActive(self._btnAgain.gameObject,false)
	goutil.setActive(self._txtHint.gameObject,true)
end

--- view初始化时会执行，在buildUI之后
function TetrisGameResultView:bindEvents()
	self._btnClose:AddClickListener(self._onCloseBtnClick,self)
	self._btnOK:AddClickListener(self._onCloseBtnClick,self)
	self._btnAgain:AddClickListener(self._onAgainBtnClick,self)
	self._btnBack:AddClickListener(self._onBackBtnClick,self)
	self._btnQuit:AddClickListener(self._onQuitBtnClick,self)
end
--- view销毁时会执行，在destroyUI之前
function TetrisGameResultView:unbindEvents()
	self._btnClose:RemoveClickListener()
	self._btnOK:RemoveClickListener()
	self._btnAgain:RemoveClickListener()
	self._btnBack:RemoveClickListener()
	self._btnQuit:RemoveClickListener()
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function TetrisGameResultView:onEnter()
	local params = self:getFirstParam()
	local isStandalone = params.isStandalone
	local winType = params.winType
	local roomType = GameCentreModel.instance:getRoomCreateType()
	
	HeadPortraitHelper.instance:setHeadPortraitWithUserId(self._headGo,UserInfo.userId)
	self._nameText.text = UserInfo.nickname
	if isStandalone or roomType == GameEnum.GameRoomCreateType.CUSTOM then
		self._scoreText.text = lang("我的得分：<color=#e38534>{1}</color>",params.score)
		self._txtHint.text = ""
	else
		--self._scoreText.text = lang("<color=#e38534>匹配积分：{1} {2}{3}</color>",params.beforeMatchScore,"+",params.changeMatchScore) --lang("参与次数：<color=#e38534>{1}</color>",params.winCount)
		if params.changeMatchScore > 0 then
			self._scoreText.text = lang("匹配积分：<color=#e38534>{1} {2}{3}</color>",params.beforeMatchScore,"+",params.changeMatchScore)
			self._txtHint.text = "" --lang("<color=#e38534>匹配积分：{1} {2}{3}</color>",params.beforeMatchScore,"+",params.changeMatchScore)
		elseif params.changeMatchScore < 0 then
			self._scoreText.text = lang("匹配积分：<color=#e38534>{1} {2}{3}</color>",params.beforeMatchScore,"-",math.abs(params.changeMatchScore))
			self._txtHint.text = "" --lang("<color=#e38534>匹配积分：{1} {2}{3}</color>",params.beforeMatchScore,"-",math.abs(params.changeMatchScore))
		else
			self._scoreText.text = lang("匹配积分：<color=#e38534>{1}</color>",params.beforeMatchScore)
			self._txtHint.text = "" --lang("<color=#e38534>匹配积分：{1}</color>",params.beforeMatchScore)
		end
	end

	local hasReward = false
	local items = ItemService.instance:popChangeSet(params.changeId)
	self.itemsData = {}
	if items then
		self._itemsList = {}
		for i = 1, #items do
			local id = items[i].id
			local num = items[i].num
			if num > 0 then
				local item = CommonIconMgr.instance:fetchCommonIcon()
				item:setWidthAndHeight(73):showRarenessGo(true)
				goutil.addChildToParent(item:getPrefab(), self._rewardContent)
				local data = {id = id, num = num}
				table.insert(self.itemsData,data)
				item:buildData(data)
				table.insert(self._itemsList,item)
				hasReward = true
			end
		end
	end
	goutil.setActive(self._loseTitleGo,not isStandalone and winType == 3)
	goutil.setActive(self._winTitleGo,not isStandalone and winType == 1)
	goutil.setActive(self._equalTitleGo,not isStandalone and winType == 2)
	goutil.setActive(self._txtReward,hasReward)
	goutil.setActive(self._titleNameGo,isStandalone)
	goutil.setActive(self._newScoreGo,isStandalone and params.newScore)
	
	
	self._btnBack.gameObject:SetActive(roomType == GameEnum.GameRoomCreateType.CUSTOM)
	self._btnQuit.gameObject:SetActive(roomType == GameEnum.GameRoomCreateType.CUSTOM)
	self._btnOK.gameObject:SetActive(roomType ~= GameEnum.GameRoomCreateType.CUSTOM)
	self._scoreText.gameObject:SetActive(roomType ~= GameEnum.GameRoomCreateType.CUSTOM)
	self._customHideGo1:SetActive(roomType ~= GameEnum.GameRoomCreateType.CUSTOM)
	self._customHideGo2:SetActive(roomType ~= GameEnum.GameRoomCreateType.CUSTOM)
	self._customHideGo3:SetActive(roomType ~= GameEnum.GameRoomCreateType.CUSTOM)
	if roomType == GameEnum.GameRoomCreateType.CUSTOM then
		GameUtils.setLocalPos(self._nameText.gameObject,44.9,0,0)
		GameUtils.setLocalPos(self._headBgGo,-88.1,0,0)
		GameUtils.setLocalPos(self._headGo,-89.1,0,0)
	else
		GameUtils.setLocalPos(self._nameText.gameObject,44.9,63.7,0)
		GameUtils.setLocalPos(self._headBgGo,-88.1,65,0)
		GameUtils.setLocalPos(self._headGo,-89.1,65.2,0)
	end
	--self._btnAgain.gameObject:SetActive(roomType == GameEnum.GameRoomCreateType.MATCH)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function TetrisGameResultView:onExit()
	if self._itemsList then
		for i = 1, #self._itemsList do
			CommonIconMgr.instance:returnCommonIcon(self._itemsList[i])
		end
		self._itemsList = nil
	end
end

--- view销毁时会执行
function TetrisGameResultView:destroyUI()

end

--退出
function TetrisGameResultView:_onCloseBtnClick()
	self:close()
	if self.itemsData and #self.itemsData > 0 then
		DialogHelper.showRewardsDlg(self.itemsData, nil, TetrisController.quickGame, TetrisController.instance)
	else
		TetrisController.instance:quickGame()
	end
end

--再来一局
function TetrisGameResultView:_onAgainBtnClick()
	if not TetrisController.instance:checkActivityIsOpen(true) then
		self:_onCloseBtnClick()
		return
	end
	--Activity204Agent.instance:sendStartAct204JumpRequest(self._tabIndex, function(friendgameinfo)
	--if self._againHandler then
	--self._againHandler(true)
	--end
	--if self._icon ~= nil then
	--CommonIconMgr.instance:returnCommonIcon(self._icon)
	--self._icon = nil
	--end
	--self:close()
	--end)
end

--返回房间
function TetrisGameResultView:_onBackBtnClick()
	GameRoomController.instance:backToWaitingRoom(9)
end

--退出房间
function TetrisGameResultView:_onQuitBtnClick()
	GameRoomController.instance:tryQuitGame(GameEnum.GameRoomType.TETRIS, false, false, TetrisModel.instance:getLastSceneId())
	self:close()
end

return TetrisGameResultView