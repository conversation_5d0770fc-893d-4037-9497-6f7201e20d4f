module("logic.extensions.sharestreet.view.ShareStreetMapViewPresentor",package.seeall)
---@class ShareStreetMapViewPresentor
local ShareStreetMapViewPresentor = class("ShareStreetMapViewPresentor",ViewPresentor)

function ShareStreetMapViewPresentor:ctor()
	ShareStreetMapViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetMapViewPresentor:dependWhatResources()
	return {"ui/street/streetmapview.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetMapViewPresentor:buildViews()
	return {ShareStreetMapView.New()}
end

--- 配置view所在的ui层
function ShareStreetMapViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetMapViewPresentor