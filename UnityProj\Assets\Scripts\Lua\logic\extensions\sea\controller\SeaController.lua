module("logic.extensions.sea.controller.SeaController", package.seeall)

local SeaController = class("SeaController", BaseController)

function SeaController:onInit()
    self:registerNotify(GlobalNotify.EnterScene, self._onEnterScene, self)
end

function SeaController:onReset()
    self:unregisterNotify(GlobalNotify.EnterScene, self._onEnterScene, self)
end

function SeaController:_onEnterScene()
    self:checkMount()
end

-- 初始化坐骑信息
function SeaController:checkMount()
    local mountType = SeaFacade.getMountType()
    if mountType <= 0 then
        return
    end
    local mountId = (mountType == SeaEnum.MountType.Land) and SeaModel.instance:getLandMountState() or SeaModel.instance:getMountState()
    print("mountId", mountId)
    if mountId == 0 then
        return
    end
    -- local lastSceneType = SceneManager.instance._lastSceneType
    -- local _lastSceneId = SceneManager.instance._lastSceneId
    -- local isSeaHouse = (_lastSceneId >= 79 and _lastSceneId <= 82)
    -- if lastSceneType ~= SceneType.Sea and not isSeaHouse then
    --     return
    -- end
    SceneController.instance:startRideMountAction(mountId)
end

-- 获取海底坐骑增益
function SeaController:getAllSeaMountBuff()
    local buffs = {}
    local mountId = SeaModel.instance:getMountState()
    if mountId ~= 0 and SeaFacade.getMountType() == SeaEnum.MountType.Sea then
        local define = PoseConfig.getInteractiveProp(mountId)
        if define.buffs then
            for _, v in ipairs(define.buffs) do
                table.insert(buffs, {
                    id = v.id,
                    value = v.count
                })
            end
        end
    end
    return buffs
end

-- 获取海底场景总数据
function SeaController:getSeaSceneInfoRequest(callback)
    SeaAgent.instance:sendGetSeaSceneInfoRequest(function(depthInfos, fishInfos, playerInfo)
        SeaMap.instance:structure(depthInfos)
        SeaModel.instance:setFishInfos(fishInfos)
        SeaModel.instance:setPlayerInfo(playerInfo)
        SeaTaskModel.instance:initTaskInfo(depthInfos)
        if callback then
            callback()
        end
    end)
end

-- 请求发射
function SeaController:seaSceneFireRequest(callback)
    SeaAgent.instance:sendSeaSceneFireRequest(self:getBulletId(), function()
        if callback then
            callback()
        end
    end)
end

-- 请求捕鱼
function SeaController:seaSceneFishRequest(fishId, bulletId, specialActId, callback)
    if specialActId then
        local activityInfo = ActivityModel.instance:getActivityInfo(specialActId)
        if activityInfo and activityInfo:getIsOpen() then
            specialActId = activityInfo.simpleInfo.activityId
        end
    end
    if not specialActId then
        specialActId = 0
    end
    SeaAgent.instance:sendSeaSceneFishRequest(fishId, bulletId, specialActId, function(success)
        if callback then
            callback(success)
        end
    end)
end

-- 请求捕鱼奖励
function SeaController:seaSceneFishAwardRequest(fishId, reward, callback)
    SeaAgent.instance:sendSeaSceneFishAwardRequest(fishId, reward, function(first, weight, historyWeight, changeSetId)
        if reward then
            ViewMgr.instance:open("SeaFishSucView", {
                fishId = fishId,
                weight = weight,
                historyWeight = historyWeight,
                changeSetId = changeSetId
            })
            local fishInfo = SeaModel.instance:getFishInfo(fishId)
            if fishInfo == nil then
                SeaModel.instance:setFishInfo(fishId, {
                    fishId = fishId,
                    todayFishNum = 1
                })
            else
                fishInfo.todayFishNum = fishInfo.todayFishNum + 1
            end
        end
        if callback then
            callback()
        end
    end)
end

-- 请求开宝箱
function SeaController:seaSceneOpenBoxRequest(boxId, callback)
    SeaAgent.instance:sendSeaSceneOpenBoxRequest(boxId, function(changeSetId)
        if callback then
            callback(changeSetId)
        end
    end)
end

-- 修改正在使用的子弹
function SeaController:setBulletId(bulletId)
    LocalStorage.instance:setValue(StorageKey.SeaSceneBulletId, bulletId)
end

-- 获取正在使用的子弹
function SeaController:getBulletId()
    return LocalStorage.instance:getValue(StorageKey.SeaSceneBulletId, 0)
end

-- 获取有效子弹列表
function SeaController:getBulletList()
    local list = {}
    local bullets = SeaConfig.getAllBullet()
    local bulletId = self:getBulletId()
    local index = 1
    if bulletId > 0 then
        for i, v in ipairs(bullets) do
            if v.id == bulletId then
                index = i
                break
            end
        end
    end
    for i = index + 1, #bullets do
        if ItemService.instance:hasItem(bullets[i].id) then
            table.insert(list, bullets[i])
        end
    end
    for i = 1, index do
        if ItemService.instance:hasItem(bullets[i].id) then
            table.insert(list, bullets[i])
        end
    end
    return list
end

-- 获取背包列表
function SeaController:getBagList()
    local list = {}
    local items = SeaConfig.getAllItem()
    for _, item in ipairs(items) do
        if item.isShow and ItemService.instance:hasItem(item.id) then
            table.insert(list, item)
        end
    end
    table.sort(list, function(a, b)
        return a.sort < b.sort
    end)
    return list
end

SeaController.instance = SeaController.New()

return SeaController
