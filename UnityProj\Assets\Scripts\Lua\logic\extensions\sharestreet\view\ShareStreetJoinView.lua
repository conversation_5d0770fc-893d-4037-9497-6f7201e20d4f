module("logic.extensions.sharestreet.view.ShareStreetJoinView", package.seeall)
---@class ShareStreetJoinView
local ShareStreetJoinView = class("ShareStreetJoinView", ListBinderView)

function ShareStreetJoinView:ctor()
    self._inviteModel = BaseListModel.New()
    ShareStreetJoinView.super.ctor(self, self._inviteModel, "listView", ShareStreetJoinViewPresentor.Url_Item,
        ShareStreetInviteItem, {kScrollDirV, 264, 273, 10, 10, 4}, false)
end

--- view初始化时会执行
function ShareStreetJoinView:buildUI()
    ShareStreetJoinView.super.buildUI(self)

	self._btnClose = self:getBtn("btnclose/btnClose")
	self._txtClose = self:getText("btnclose/Text")
	self._goNull = self:getGo("goNull")
	self._txtNull = self:getText("goNull/commonnullview/txt")
	self._goBottomTips = self:getGo("txtTips")

	self._txtTips = self:getText("countGo/tipTxt")
	self._txtCount = self:getText("countGo/countTxt")

	self._tgTab = ToggleGroup.New(handler(self._onClickTab, self))
	self._tgTab:addView(self:getGo("tabGo/tab_1"))
	self._tgTab:addView(self:getGo("tabGo/tab_2"))
end

--- view初始化时会执行，在buildUI之后
function ShareStreetJoinView:bindEvents()
    ShareStreetJoinView.super.bindEvents(self)

	self._btnClose:AddClickListener(self.close, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetJoinView:onEnter()
    ShareStreetJoinView.super.onEnter(self)

	self:registerLocalNotify(ShareStreetLocalNotify.OnJoinOtherStreetSucc, self._onJoinSucc, self)
	self:registerLocalNotify(ShareStreetLocalNotify.OnCancelJoinApplySucc, self._onCancelSucc, self)

	self._isAcceptInvite = self:getFirstParam()
	if self._isAcceptInvite then
		self._tgTab:clickViews(true, 1)
	else
		self._tgTab:clickViews(true, 2)
	end

	self:registerNotify(GlobalNotify.StartLoadScene, self.close, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetJoinView:onEnterFinished()
    ShareStreetJoinView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetJoinView:onExit()
    ShareStreetJoinView.super.onExit(self)

	self:unregisterLocalNotify(ShareStreetLocalNotify.OnJoinOtherStreetSucc, self._onJoinSucc, self)
	self:unregisterLocalNotify(ShareStreetLocalNotify.OnCancelJoinApplySucc, self._onCancelSucc, self)
	
	self:unregisterNotify(GlobalNotify.StartLoadScene, self.close, self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetJoinView:onExitFinished()
    ShareStreetJoinView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetJoinView:unbindEvents()
    ShareStreetJoinView.super.unbindEvents(self)

	self._btnClose:RemoveClickListener()
end

--- view销毁时会执行
function ShareStreetJoinView:destroyUI()
    ShareStreetJoinView.super.destroyUI(self)
end

function ShareStreetJoinView:_onClickTab(index, isSelected)
	if not isSelected then return end
	self._selectedIndex = index
	self:_updateView()
end

function ShareStreetJoinView:_updateView()
	if self._selectedIndex == 1 then
		self._txtTips.text = "受邀加入街区"
		self._txtCount.text = ""
		ShareStreetAgent.instance:sendGetStreetReceiveInviteListRequest(handler(function(_, inviteList)
			local moList = {}
			for _, streetNo in ipairs(inviteList) do
				local mo = {}
				mo.isAcceptInvite = true
				mo.no = streetNo
				mo.roleInfo = streetNo.streetOwner
				table.insert(moList, mo)
			end
			self._txtCount.text = string.format("%s/%s", #moList, ShareStreetCfg.instance:getCommonConfigValue("maxReceiveInviteCount"))
			self._inviteModel:setMoList(moList)
			self._goNull:SetActive(#moList == 0)
			self._goBottomTips:SetActive(#moList > 0)
			if #moList == 0 then
				self._txtNull.text = lang("受邀加入街区空状态提示")
			end
		end, self))
	else
		self._txtTips.text = "申请加入街区"
		self._txtCount.text = ""
		ShareStreetAgent.instance:sendGetStreetApplyListRequest(handler(function(_, applyList)
			local moList = {}
			for _, streetNo in ipairs(applyList) do
				local mo = {}
				mo.isGetApplyList = true
				mo.no = streetNo
				mo.roleInfo = streetNo.streetOwner
				table.insert(moList, mo)
			end
			self._txtCount.text = string.format("%s/%s", #moList, ShareStreetCfg.instance:getCommonConfigValue("maxApplyCount"))
			self._inviteModel:setMoList(moList)
			self._goNull:SetActive(#moList == 0)
			self._goBottomTips:SetActive(#moList > 0)
			if #moList == 0 then
				self._txtNull.text = lang("申请加入街区空状态提示")
			end
		end, self))
	end
end

function ShareStreetJoinView:_onJoinSucc(ownerId)
	self:close()
end

function ShareStreetJoinView:_onCancelSucc(ownerId)
	self:_updateView()
end

return ShareStreetJoinView
