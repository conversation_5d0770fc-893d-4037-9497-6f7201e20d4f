module("logic.extensions.sharestreet.view.ShareStreetInviteViewPresentor",package.seeall)
---@class ShareStreetInviteViewPresentor
local ShareStreetInviteViewPresentor = class("ShareStreetInviteViewPresentor",ViewPresentor)

ShareStreetInviteViewPresentor.Url_Main = "ui/street/streetmasterinvitepanel.prefab"
ShareStreetInviteViewPresentor.Url_Item = "ui/street/streetinvititem.prefab"

function ShareStreetInviteViewPresentor:ctor()
	ShareStreetInviteViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetInviteViewPresentor:dependWhatResources()
	return {ShareStreetInviteViewPresentor.Url_Main, ShareStreetInviteViewPresentor.Url_Item}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetInviteViewPresentor:buildViews()
	return {ShareStreetInviteView.New()}
end

--- 配置view所在的ui层
function ShareStreetInviteViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetInviteViewPresentor