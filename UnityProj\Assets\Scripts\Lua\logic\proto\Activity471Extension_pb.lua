-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity471Extension_pb", package.seeall)


local tb = {}
CELEBPARTYQUEUENO_MSG = protobuf.Descriptor()
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD = protobuf.FieldDescriptor()
CHANGECELEBPARTYSTATEREQUEST_MSG = protobuf.Descriptor()
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD = protobuf.FieldDescriptor()
tb.CH<PERSON><PERSON><PERSON>LEBPARTYSTATEREQUEST_MODE_FIELD = protobuf.FieldDescriptor()
CELEBPARTYROLENO_MSG = protobuf.Descriptor()
tb.CELEBPARTYROLENO_USERID_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYROLENO_CALLVALUE_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYROLENO_SKIN_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYROLENO_MODE_FIELD = protobuf.FieldDescriptor()
ACT471DRAWTOTALJOINREWARDREPLY_MSG = protobuf.Descriptor()
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
LEAVECELEBPARTYQUEUEREPLY_MSG = protobuf.Descriptor()
ACT471DRAWTOTALJOINREWARDREQUEST_MSG = protobuf.Descriptor()
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD = protobuf.FieldDescriptor()
JOINCELEBPARTYQUEUEREPLY_MSG = protobuf.Descriptor()
ACT471DRAWDAILYREWARDREPLY_MSG = protobuf.Descriptor()
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT471DRAWDAILYREWARDREQUEST_MSG = protobuf.Descriptor()
LEAVECELEBPARTYQUEUEREQUEST_MSG = protobuf.Descriptor()
JOINCELEBPARTYQUEUEREQUEST_MSG = protobuf.Descriptor()
GETACT471INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD = protobuf.FieldDescriptor()
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD = protobuf.FieldDescriptor()
GETACT471INFOREQUEST_MSG = protobuf.Descriptor()
CHANGECELEBPARTYSTATEREPLY_MSG = protobuf.Descriptor()
CELEBPARTYINFONO_MSG = protobuf.Descriptor()
tb.CELEBPARTYINFONO_SHOWID_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYINFONO_PATH_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYINFONO_STEP_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD = protobuf.FieldDescriptor()
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD = protobuf.FieldDescriptor()

tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.name = "queueId"
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.full_name = ".CelebPartyQueueNO.queueId"
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.number = 1
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.index = 0
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.label = 1
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.has_default_value = false
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.default_value = 0
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.type = 5
tb.CELEBPARTYQUEUENO_QUEUEID_FIELD.cpp_type = 1

tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.name = "roleInfos"
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.full_name = ".CelebPartyQueueNO.roleInfos"
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.number = 2
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.index = 1
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.label = 3
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.has_default_value = false
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.default_value = {}
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.message_type = CELEBPARTYROLENO_MSG
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.type = 11
tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD.cpp_type = 10

CELEBPARTYQUEUENO_MSG.name = "CelebPartyQueueNO"
CELEBPARTYQUEUENO_MSG.full_name = ".CelebPartyQueueNO"
CELEBPARTYQUEUENO_MSG.filename = "Activity471Extension"
CELEBPARTYQUEUENO_MSG.nested_types = {}
CELEBPARTYQUEUENO_MSG.enum_types = {}
CELEBPARTYQUEUENO_MSG.fields = {tb.CELEBPARTYQUEUENO_QUEUEID_FIELD, tb.CELEBPARTYQUEUENO_ROLEINFOS_FIELD}
CELEBPARTYQUEUENO_MSG.is_extendable = false
CELEBPARTYQUEUENO_MSG.extensions = {}
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.name = "skin"
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.full_name = ".ChangeCelebPartyStateRequest.skin"
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.number = 1
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.index = 0
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.label = 1
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.has_default_value = false
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.default_value = 0
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.type = 5
tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD.cpp_type = 1

tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.name = "mode"
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.full_name = ".ChangeCelebPartyStateRequest.mode"
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.number = 2
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.index = 1
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.label = 1
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.has_default_value = false
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.default_value = 0
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.type = 5
tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD.cpp_type = 1

CHANGECELEBPARTYSTATEREQUEST_MSG.name = "ChangeCelebPartyStateRequest"
CHANGECELEBPARTYSTATEREQUEST_MSG.full_name = ".ChangeCelebPartyStateRequest"
CHANGECELEBPARTYSTATEREQUEST_MSG.filename = "Activity471Extension"
CHANGECELEBPARTYSTATEREQUEST_MSG.nested_types = {}
CHANGECELEBPARTYSTATEREQUEST_MSG.enum_types = {}
CHANGECELEBPARTYSTATEREQUEST_MSG.fields = {tb.CHANGECELEBPARTYSTATEREQUEST_SKIN_FIELD, tb.CHANGECELEBPARTYSTATEREQUEST_MODE_FIELD}
CHANGECELEBPARTYSTATEREQUEST_MSG.is_extendable = false
CHANGECELEBPARTYSTATEREQUEST_MSG.extensions = {}
tb.CELEBPARTYROLENO_USERID_FIELD.name = "userId"
tb.CELEBPARTYROLENO_USERID_FIELD.full_name = ".CelebPartyRoleNO.userId"
tb.CELEBPARTYROLENO_USERID_FIELD.number = 1
tb.CELEBPARTYROLENO_USERID_FIELD.index = 0
tb.CELEBPARTYROLENO_USERID_FIELD.label = 1
tb.CELEBPARTYROLENO_USERID_FIELD.has_default_value = false
tb.CELEBPARTYROLENO_USERID_FIELD.default_value = ""
tb.CELEBPARTYROLENO_USERID_FIELD.type = 9
tb.CELEBPARTYROLENO_USERID_FIELD.cpp_type = 9

tb.CELEBPARTYROLENO_CALLVALUE_FIELD.name = "callValue"
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.full_name = ".CelebPartyRoleNO.callValue"
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.number = 2
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.index = 1
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.label = 1
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.has_default_value = false
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.default_value = 0
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.type = 5
tb.CELEBPARTYROLENO_CALLVALUE_FIELD.cpp_type = 1

tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.name = "joinQueueTime"
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.full_name = ".CelebPartyRoleNO.joinQueueTime"
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.number = 3
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.index = 2
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.label = 1
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.has_default_value = false
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.default_value = 0
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.type = 5
tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD.cpp_type = 1

tb.CELEBPARTYROLENO_SKIN_FIELD.name = "skin"
tb.CELEBPARTYROLENO_SKIN_FIELD.full_name = ".CelebPartyRoleNO.skin"
tb.CELEBPARTYROLENO_SKIN_FIELD.number = 4
tb.CELEBPARTYROLENO_SKIN_FIELD.index = 3
tb.CELEBPARTYROLENO_SKIN_FIELD.label = 1
tb.CELEBPARTYROLENO_SKIN_FIELD.has_default_value = false
tb.CELEBPARTYROLENO_SKIN_FIELD.default_value = 0
tb.CELEBPARTYROLENO_SKIN_FIELD.type = 5
tb.CELEBPARTYROLENO_SKIN_FIELD.cpp_type = 1

tb.CELEBPARTYROLENO_MODE_FIELD.name = "mode"
tb.CELEBPARTYROLENO_MODE_FIELD.full_name = ".CelebPartyRoleNO.mode"
tb.CELEBPARTYROLENO_MODE_FIELD.number = 5
tb.CELEBPARTYROLENO_MODE_FIELD.index = 4
tb.CELEBPARTYROLENO_MODE_FIELD.label = 1
tb.CELEBPARTYROLENO_MODE_FIELD.has_default_value = false
tb.CELEBPARTYROLENO_MODE_FIELD.default_value = 0
tb.CELEBPARTYROLENO_MODE_FIELD.type = 5
tb.CELEBPARTYROLENO_MODE_FIELD.cpp_type = 1

CELEBPARTYROLENO_MSG.name = "CelebPartyRoleNO"
CELEBPARTYROLENO_MSG.full_name = ".CelebPartyRoleNO"
CELEBPARTYROLENO_MSG.filename = "Activity471Extension"
CELEBPARTYROLENO_MSG.nested_types = {}
CELEBPARTYROLENO_MSG.enum_types = {}
CELEBPARTYROLENO_MSG.fields = {tb.CELEBPARTYROLENO_USERID_FIELD, tb.CELEBPARTYROLENO_CALLVALUE_FIELD, tb.CELEBPARTYROLENO_JOINQUEUETIME_FIELD, tb.CELEBPARTYROLENO_SKIN_FIELD, tb.CELEBPARTYROLENO_MODE_FIELD}
CELEBPARTYROLENO_MSG.is_extendable = false
CELEBPARTYROLENO_MSG.extensions = {}
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.full_name = ".Act471DrawTotalJoinRewardReply.changeSetId"
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT471DRAWTOTALJOINREWARDREPLY_MSG.name = "Act471DrawTotalJoinRewardReply"
ACT471DRAWTOTALJOINREWARDREPLY_MSG.full_name = ".Act471DrawTotalJoinRewardReply"
ACT471DRAWTOTALJOINREWARDREPLY_MSG.filename = "Activity471Extension"
ACT471DRAWTOTALJOINREWARDREPLY_MSG.nested_types = {}
ACT471DRAWTOTALJOINREWARDREPLY_MSG.enum_types = {}
ACT471DRAWTOTALJOINREWARDREPLY_MSG.fields = {tb.ACT471DRAWTOTALJOINREWARDREPLY_CHANGESETID_FIELD}
ACT471DRAWTOTALJOINREWARDREPLY_MSG.is_extendable = false
ACT471DRAWTOTALJOINREWARDREPLY_MSG.extensions = {}
LEAVECELEBPARTYQUEUEREPLY_MSG.name = "LeaveCelebPartyQueueReply"
LEAVECELEBPARTYQUEUEREPLY_MSG.full_name = ".LeaveCelebPartyQueueReply"
LEAVECELEBPARTYQUEUEREPLY_MSG.filename = "Activity471Extension"
LEAVECELEBPARTYQUEUEREPLY_MSG.nested_types = {}
LEAVECELEBPARTYQUEUEREPLY_MSG.enum_types = {}
LEAVECELEBPARTYQUEUEREPLY_MSG.fields = {}
LEAVECELEBPARTYQUEUEREPLY_MSG.is_extendable = false
LEAVECELEBPARTYQUEUEREPLY_MSG.extensions = {}
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.name = "joinTimes"
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.full_name = ".Act471DrawTotalJoinRewardRequest.joinTimes"
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.number = 1
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.index = 0
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.label = 1
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.has_default_value = false
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.default_value = 0
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.type = 5
tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD.cpp_type = 1

ACT471DRAWTOTALJOINREWARDREQUEST_MSG.name = "Act471DrawTotalJoinRewardRequest"
ACT471DRAWTOTALJOINREWARDREQUEST_MSG.full_name = ".Act471DrawTotalJoinRewardRequest"
ACT471DRAWTOTALJOINREWARDREQUEST_MSG.filename = "Activity471Extension"
ACT471DRAWTOTALJOINREWARDREQUEST_MSG.nested_types = {}
ACT471DRAWTOTALJOINREWARDREQUEST_MSG.enum_types = {}
ACT471DRAWTOTALJOINREWARDREQUEST_MSG.fields = {tb.ACT471DRAWTOTALJOINREWARDREQUEST_JOINTIMES_FIELD}
ACT471DRAWTOTALJOINREWARDREQUEST_MSG.is_extendable = false
ACT471DRAWTOTALJOINREWARDREQUEST_MSG.extensions = {}
JOINCELEBPARTYQUEUEREPLY_MSG.name = "JoinCelebPartyQueueReply"
JOINCELEBPARTYQUEUEREPLY_MSG.full_name = ".JoinCelebPartyQueueReply"
JOINCELEBPARTYQUEUEREPLY_MSG.filename = "Activity471Extension"
JOINCELEBPARTYQUEUEREPLY_MSG.nested_types = {}
JOINCELEBPARTYQUEUEREPLY_MSG.enum_types = {}
JOINCELEBPARTYQUEUEREPLY_MSG.fields = {}
JOINCELEBPARTYQUEUEREPLY_MSG.is_extendable = false
JOINCELEBPARTYQUEUEREPLY_MSG.extensions = {}
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.full_name = ".Act471DrawDailyRewardReply.changeSetId"
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

ACT471DRAWDAILYREWARDREPLY_MSG.name = "Act471DrawDailyRewardReply"
ACT471DRAWDAILYREWARDREPLY_MSG.full_name = ".Act471DrawDailyRewardReply"
ACT471DRAWDAILYREWARDREPLY_MSG.filename = "Activity471Extension"
ACT471DRAWDAILYREWARDREPLY_MSG.nested_types = {}
ACT471DRAWDAILYREWARDREPLY_MSG.enum_types = {}
ACT471DRAWDAILYREWARDREPLY_MSG.fields = {tb.ACT471DRAWDAILYREWARDREPLY_CHANGESETID_FIELD}
ACT471DRAWDAILYREWARDREPLY_MSG.is_extendable = false
ACT471DRAWDAILYREWARDREPLY_MSG.extensions = {}
ACT471DRAWDAILYREWARDREQUEST_MSG.name = "Act471DrawDailyRewardRequest"
ACT471DRAWDAILYREWARDREQUEST_MSG.full_name = ".Act471DrawDailyRewardRequest"
ACT471DRAWDAILYREWARDREQUEST_MSG.filename = "Activity471Extension"
ACT471DRAWDAILYREWARDREQUEST_MSG.nested_types = {}
ACT471DRAWDAILYREWARDREQUEST_MSG.enum_types = {}
ACT471DRAWDAILYREWARDREQUEST_MSG.fields = {}
ACT471DRAWDAILYREWARDREQUEST_MSG.is_extendable = false
ACT471DRAWDAILYREWARDREQUEST_MSG.extensions = {}
LEAVECELEBPARTYQUEUEREQUEST_MSG.name = "LeaveCelebPartyQueueRequest"
LEAVECELEBPARTYQUEUEREQUEST_MSG.full_name = ".LeaveCelebPartyQueueRequest"
LEAVECELEBPARTYQUEUEREQUEST_MSG.filename = "Activity471Extension"
LEAVECELEBPARTYQUEUEREQUEST_MSG.nested_types = {}
LEAVECELEBPARTYQUEUEREQUEST_MSG.enum_types = {}
LEAVECELEBPARTYQUEUEREQUEST_MSG.fields = {}
LEAVECELEBPARTYQUEUEREQUEST_MSG.is_extendable = false
LEAVECELEBPARTYQUEUEREQUEST_MSG.extensions = {}
JOINCELEBPARTYQUEUEREQUEST_MSG.name = "JoinCelebPartyQueueRequest"
JOINCELEBPARTYQUEUEREQUEST_MSG.full_name = ".JoinCelebPartyQueueRequest"
JOINCELEBPARTYQUEUEREQUEST_MSG.filename = "Activity471Extension"
JOINCELEBPARTYQUEUEREQUEST_MSG.nested_types = {}
JOINCELEBPARTYQUEUEREQUEST_MSG.enum_types = {}
JOINCELEBPARTYQUEUEREQUEST_MSG.fields = {}
JOINCELEBPARTYQUEUEREQUEST_MSG.is_extendable = false
JOINCELEBPARTYQUEUEREQUEST_MSG.extensions = {}
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.name = "dailyDrawn"
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.full_name = ".GetAct471InfoReply.dailyDrawn"
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.number = 1
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.index = 0
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.label = 1
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.has_default_value = false
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.default_value = false
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.type = 8
tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD.cpp_type = 7

tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.name = "totalJoinCount"
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.full_name = ".GetAct471InfoReply.totalJoinCount"
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.number = 2
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.index = 1
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.label = 1
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.has_default_value = false
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.default_value = 0
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.type = 5
tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD.cpp_type = 1

tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.name = "dailyJoinCount"
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.full_name = ".GetAct471InfoReply.dailyJoinCount"
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.number = 3
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.index = 2
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.label = 1
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.has_default_value = false
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.default_value = 0
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.type = 5
tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD.cpp_type = 1

tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.name = "drawJoinTimesRewards"
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.full_name = ".GetAct471InfoReply.drawJoinTimesRewards"
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.number = 4
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.index = 3
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.label = 3
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.has_default_value = false
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.default_value = {}
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.type = 5
tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD.cpp_type = 1

GETACT471INFOREPLY_MSG.name = "GetAct471InfoReply"
GETACT471INFOREPLY_MSG.full_name = ".GetAct471InfoReply"
GETACT471INFOREPLY_MSG.filename = "Activity471Extension"
GETACT471INFOREPLY_MSG.nested_types = {}
GETACT471INFOREPLY_MSG.enum_types = {}
GETACT471INFOREPLY_MSG.fields = {tb.GETACT471INFOREPLY_DAILYDRAWN_FIELD, tb.GETACT471INFOREPLY_TOTALJOINCOUNT_FIELD, tb.GETACT471INFOREPLY_DAILYJOINCOUNT_FIELD, tb.GETACT471INFOREPLY_DRAWJOINTIMESREWARDS_FIELD}
GETACT471INFOREPLY_MSG.is_extendable = false
GETACT471INFOREPLY_MSG.extensions = {}
GETACT471INFOREQUEST_MSG.name = "GetAct471InfoRequest"
GETACT471INFOREQUEST_MSG.full_name = ".GetAct471InfoRequest"
GETACT471INFOREQUEST_MSG.filename = "Activity471Extension"
GETACT471INFOREQUEST_MSG.nested_types = {}
GETACT471INFOREQUEST_MSG.enum_types = {}
GETACT471INFOREQUEST_MSG.fields = {}
GETACT471INFOREQUEST_MSG.is_extendable = false
GETACT471INFOREQUEST_MSG.extensions = {}
CHANGECELEBPARTYSTATEREPLY_MSG.name = "ChangeCelebPartyStateReply"
CHANGECELEBPARTYSTATEREPLY_MSG.full_name = ".ChangeCelebPartyStateReply"
CHANGECELEBPARTYSTATEREPLY_MSG.filename = "Activity471Extension"
CHANGECELEBPARTYSTATEREPLY_MSG.nested_types = {}
CHANGECELEBPARTYSTATEREPLY_MSG.enum_types = {}
CHANGECELEBPARTYSTATEREPLY_MSG.fields = {}
CHANGECELEBPARTYSTATEREPLY_MSG.is_extendable = false
CHANGECELEBPARTYSTATEREPLY_MSG.extensions = {}
tb.CELEBPARTYINFONO_SHOWID_FIELD.name = "showId"
tb.CELEBPARTYINFONO_SHOWID_FIELD.full_name = ".CelebPartyInfoNO.showId"
tb.CELEBPARTYINFONO_SHOWID_FIELD.number = 1
tb.CELEBPARTYINFONO_SHOWID_FIELD.index = 0
tb.CELEBPARTYINFONO_SHOWID_FIELD.label = 1
tb.CELEBPARTYINFONO_SHOWID_FIELD.has_default_value = false
tb.CELEBPARTYINFONO_SHOWID_FIELD.default_value = 0
tb.CELEBPARTYINFONO_SHOWID_FIELD.type = 5
tb.CELEBPARTYINFONO_SHOWID_FIELD.cpp_type = 1

tb.CELEBPARTYINFONO_PATH_FIELD.name = "path"
tb.CELEBPARTYINFONO_PATH_FIELD.full_name = ".CelebPartyInfoNO.path"
tb.CELEBPARTYINFONO_PATH_FIELD.number = 2
tb.CELEBPARTYINFONO_PATH_FIELD.index = 1
tb.CELEBPARTYINFONO_PATH_FIELD.label = 1
tb.CELEBPARTYINFONO_PATH_FIELD.has_default_value = false
tb.CELEBPARTYINFONO_PATH_FIELD.default_value = 0
tb.CELEBPARTYINFONO_PATH_FIELD.type = 5
tb.CELEBPARTYINFONO_PATH_FIELD.cpp_type = 1

tb.CELEBPARTYINFONO_STEP_FIELD.name = "step"
tb.CELEBPARTYINFONO_STEP_FIELD.full_name = ".CelebPartyInfoNO.step"
tb.CELEBPARTYINFONO_STEP_FIELD.number = 3
tb.CELEBPARTYINFONO_STEP_FIELD.index = 2
tb.CELEBPARTYINFONO_STEP_FIELD.label = 1
tb.CELEBPARTYINFONO_STEP_FIELD.has_default_value = false
tb.CELEBPARTYINFONO_STEP_FIELD.default_value = 0
tb.CELEBPARTYINFONO_STEP_FIELD.type = 5
tb.CELEBPARTYINFONO_STEP_FIELD.cpp_type = 1

tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.name = "stepLeftMillTime"
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.full_name = ".CelebPartyInfoNO.stepLeftMillTime"
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.number = 4
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.index = 3
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.label = 1
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.has_default_value = false
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.default_value = 0
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.type = 5
tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD.cpp_type = 1

tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.name = "queueInfos"
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.full_name = ".CelebPartyInfoNO.queueInfos"
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.number = 5
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.index = 4
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.label = 3
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.has_default_value = false
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.default_value = {}
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.message_type = CELEBPARTYQUEUENO_MSG
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.type = 11
tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD.cpp_type = 10

CELEBPARTYINFONO_MSG.name = "CelebPartyInfoNO"
CELEBPARTYINFONO_MSG.full_name = ".CelebPartyInfoNO"
CELEBPARTYINFONO_MSG.filename = "Activity471Extension"
CELEBPARTYINFONO_MSG.nested_types = {}
CELEBPARTYINFONO_MSG.enum_types = {}
CELEBPARTYINFONO_MSG.fields = {tb.CELEBPARTYINFONO_SHOWID_FIELD, tb.CELEBPARTYINFONO_PATH_FIELD, tb.CELEBPARTYINFONO_STEP_FIELD, tb.CELEBPARTYINFONO_STEPLEFTMILLTIME_FIELD, tb.CELEBPARTYINFONO_QUEUEINFOS_FIELD}
CELEBPARTYINFONO_MSG.is_extendable = false
CELEBPARTYINFONO_MSG.extensions = {}

Act471DrawDailyRewardReply = protobuf.Message(ACT471DRAWDAILYREWARDREPLY_MSG)
Act471DrawDailyRewardRequest = protobuf.Message(ACT471DRAWDAILYREWARDREQUEST_MSG)
Act471DrawTotalJoinRewardReply = protobuf.Message(ACT471DRAWTOTALJOINREWARDREPLY_MSG)
Act471DrawTotalJoinRewardRequest = protobuf.Message(ACT471DRAWTOTALJOINREWARDREQUEST_MSG)
CelebPartyInfoNO = protobuf.Message(CELEBPARTYINFONO_MSG)
CelebPartyQueueNO = protobuf.Message(CELEBPARTYQUEUENO_MSG)
CelebPartyRoleNO = protobuf.Message(CELEBPARTYROLENO_MSG)
ChangeCelebPartyStateReply = protobuf.Message(CHANGECELEBPARTYSTATEREPLY_MSG)
ChangeCelebPartyStateRequest = protobuf.Message(CHANGECELEBPARTYSTATEREQUEST_MSG)
GetAct471InfoReply = protobuf.Message(GETACT471INFOREPLY_MSG)
GetAct471InfoRequest = protobuf.Message(GETACT471INFOREQUEST_MSG)
JoinCelebPartyQueueReply = protobuf.Message(JOINCELEBPARTYQUEUEREPLY_MSG)
JoinCelebPartyQueueRequest = protobuf.Message(JOINCELEBPARTYQUEUEREQUEST_MSG)
LeaveCelebPartyQueueReply = protobuf.Message(LEAVECELEBPARTYQUEUEREPLY_MSG)
LeaveCelebPartyQueueRequest = protobuf.Message(LEAVECELEBPARTYQUEUEREQUEST_MSG)

return _G["logic.proto.Activity471Extension_pb"]
