module("logic.extensions.activity.main.Controller.ActivityController", package.seeall)

local ActivityController = class("ActivityController", BaseController)

function ActivityController:ctor()
	self._currentPoster = 0
	--这个标记是用来标记正在自动流程的，如果进入了七天或者签到都证明要走这个流程(主要是海报类型的界面检测是在关闭的地方，与正常关闭流程重合)
	self._isAuto = false
	--这个标记是给新手查询是否正在展示活动
	self._isShow = false
end

function ActivityController:onInit()
	-- self.popUpMgr = ActivityPopUpMgr.New()
	self.activityMgr = {}
	self:createAndInitActivityMgr()
	self:registerNotify(GlobalNotify.ExitGame, self._onExitGame, self)

	self:init0OClock()
	self:registerNotify(GlobalNotify.ItemChange, self._onItemChange, self)
end

function ActivityController:_onItemChange()
	RedPointController.instance:setRedIsExist("activity429Item", ItemService.instance:hasItem(Activity429Model.getItemId()))
end

function ActivityController:onReset()
	removetimer(self.serverPushMgr, self)
	self:unregisterNotify(GlobalNotify.ItemChange, self._onItemChange, self)
	self:unregisterNotify(GlobalNotify.ExitGame, self._onExitGame, self)
	-- self:unregisterNotify(GlobalNotify.ActivityUpdate, self._onActivityUpdate, self)
	self:unregisterNotify(GlobalNotify.ActivityUpdate, self._openRecharge, self)
	for k, mgrList in pairs(self.activityMgr) do
		trycall(
			function()
				for i, mgr in ipairs(mgrList) do
					mgr:destroy()
				end
			end
		)
	end
end

function ActivityController:onFunctionChange(funcId)
	local isEnable = FuncModel.instance:getEnable(funcId)
	--按道理来说不会存在开上加开
	if funcId == FuncIds.Activity then
		if isEnable then
			self:openActivitySystem()
		else
			self:closeActivitySystem()
		end
	else
		--活动功能只支持强关，强开关只能走活动底层开关
		if isEnable == false then
			self:deleteActiveFromFuncLock(FuncIds.getActivityId(funcId))
		end
	end
end

function ActivityController:openActivitySystem()
	ActivityModel.instance:initCalenderInfo()
	self:getOpenActivityInfo()
end

function ActivityController:closeActivitySystem()
	if self.isInit == true then
		ActivityModel.instance:deleteAllActivity()
		for k, mgrList in pairs(self.activityMgr) do
			trycall(
				function()
					for i, mgr in ipairs(mgrList) do
						mgr:destroy()
					end
				end
			)
		end
		self:notify(GlobalNotify.ActivityUpdate)
	end
	self.isInit = false
end

function ActivityController:deleteActiveFromFuncLock(activityId)
	ActivityModel.instance:deleteActivityInfo({{activityDefineId = activityId}})
	self:notify(GlobalNotify.ActivityUpdate)
end

function ActivityController:_onExitGame()
	if self.isInit == true then
		for k, mgrList in pairs(self.activityMgr) do
			trycall(
				function()
					for i, mgr in ipairs(mgrList) do
						mgr:destroy()
					end
				end
			)
		end
	end
end

function ActivityController:initActivityData(finishedCb)
	self._initActInfoFinishedCallBack = finishedCb
	ActivityModel.instance:initCalenderInfo()
	self:getOpenActivityInfo()
	self:getActivityData()
end

function ActivityController:onFunctionUnlock()
	-- self:getOpenActivityInfo()
end

--服务器5点更新活动推送
function ActivityController:onActivityInfoRefreshPush()
	if self.isInit ~= true then
		return
	end
	--5点服务器推送准备好活动数据通知
	self:notify(GlobalNotify.onServerPushActivity)
	-- self:getOpenActivityInfo()
	settimer(3, self.serverPushMgr, self, false)
end

function ActivityController:serverPushMgr()
	for k, mgrList in pairs(self.activityMgr) do
		trycall(
			function()
				for i, mgr in ipairs(mgrList) do
					mgr:serverPushActivity()
				end
			end
		)
	end
end

function ActivityController:_onActivityUpdate()
	self:doCloseHandler()
end

function ActivityController:notifyActivityUpdate(activityId)
	printWarn("GlobalNotify.ActivityUpdate")
	--activityId ：目前只返回一个，后面看看要返回一个列表还是不返回了
	for k, mgrList in pairs(self.activityMgr) do
		trycall(
			function()
				for i, mgr in ipairs(mgrList) do
					mgr:activityUpdate()
				end
			end
		)
	end
	self:notify(GlobalNotify.ActivityUpdate, activityId)
end

--零点通知（用于部分12点UI界面会刷新的面板，不作为活动刷新的通知，活动通知走五点时候的后端推送）
function ActivityController:init0OClock()
	removetimer(self.notify0OClock, self)
end

--0点刷新，能不用，不要用这个，除非很明确。用的时候自己ActivityController.instance:set0OClockNotify()
--默认不派发ActivityNotify.on0OClock
function ActivityController:set0OClockNotify()
	removetimer(self.notify0OClock, self)
	local t = ServerTime.now()
	local nextDayStamp = TimeUtil.getDay1StampOfDay2Stamp(t, 1)
	local interval = nextDayStamp - t
	settimer(interval, self.notify0OClock, self, false)
end

function ActivityController:notify0OClock()
	printWarn("0点了")
	self:notify(ActivityNotify.on0OClock)
	self:set0OClockNotify()
end

function ActivityController:getOpenActivityInfo()
	ActivityAgent.instance:sendGetOpenActivityInfoRequest(
		function(activitySimpleInfos, registerTime, activityGainLimit)
			self.isInit = true
			ActivityModel.instance:setRegisterTime(registerTime)
			ActivityModel.instance:refreshOpenActivityInfo(activitySimpleInfos)
			ActivityGainLimitModel.instance:setGainLimitData(activityGainLimit)
			for k, mgrList in pairs(self.activityMgr) do
				trycall(
					function()
						for i, mgr in ipairs(mgrList) do
							mgr:init()
							mgr:activityUpdate()
						end
					end
				)
			end
			self:notify(GlobalNotify.getActivityInfoAdvance)
			self:notify(GlobalNotify.getActivityInfoFinish)
			self:checkRecharge()
			if self._initActInfoFinishedCallBack then
				self._initActInfoFinishedCallBack()
				self._initActInfoFinishedCallBack = nil
			end
		end
	)
end

function ActivityController:createAndInitActivityMgr()
	self.activityMgrMap = {}
	for k, mgrList in pairs(ActivityMgrType.activityMgrClass) do
		trycall(
			function()
				for i, mgr in ipairs(mgrList) do
					if self.activityMgr[k] == nil then
						self.activityMgr[k] = {}
					end
					self.activityMgr[k][i] = mgr.New(k)
					self.activityMgrMap[mgr.__cname] = self.activityMgr[k][i]
				end
			end
		)
	end
end

function ActivityController:getMgr(mgrName)
	return self.activityMgrMap[mgrName]
end

function ActivityController:doCloseHandler()
	for actId, handler in pairs(ActivityModel.instance.closeMap) do
		if handler and not ActivityModel.instance:getActivityIsShow(actId) then
			handler()
		end
	end
end

function ActivityController:activityChangeStatePush(msg)
	if self.isInit ~= true then
		return
	end
	ActivityModel.instance:refreshOpenActivityInfo(msg.startInfos)
	ActivityModel.instance:deleteActivityInfo(msg.endInfos)
	ActivityModel.instance:refreshOpenActivityInfo(msg.modifyInfos)
	for k, mgrList in pairs(self.activityMgr) do
		trycall(
			function()
				for i, mgr in ipairs(mgrList) do
					mgr:activityUpdate()
				end
			end
		)
	end
	self:notify(GlobalNotify.ActivityUpdate)
end

function ActivityController:getActivityData()
	self:setIsShow(true)
end

function ActivityController:onPopupViewFinish(finishCb)
	print("ActivityController onPopupViewFinish ")
	self:setIsShow(false)
	self:setIsAuto(false)

	self:notify(GlobalNotify.OnFinishActivityShow)
	finishCb()
end

function ActivityController:setIsShow(flag)
	self._isShow = flag
end

function ActivityController:getIsShow()
	return self._isShow
end

function ActivityController:setIsAuto(state)
	self._isAuto = state
end
--是否正在自动流程
function ActivityController:getIsAuto()
	return self._isAuto
end

function ActivityController:showExchange(exchangeId)
	local exchange = ExchangeGroupConfig.getSpecialDefine(exchangeId)
	ViewMgr.instance:open(
		"ExchangePanel",
		exchange.sources[1],
		exchange.exchanges[1],
		99,
		function(count)
			ActivityController.instance:handleExchange(
				exchangeId,
				count,
				function(cId)
					DialogHelper.showRewardsDlgByCI(cId)
				end
			)
		end
	)
end

function ActivityController:handleExchange(exchangeId, count, callBack)
	local exchange = ExchangeGroupConfig.getSpecialDefine(exchangeId)
	local itemInfo = BackpackExtension_pb.ExchangeReqNo()
	itemInfo.exchangeId = exchange.id
	itemInfo.count = count
	BackpackAgent.instance:sendExchangeItemRequest(
		{itemInfo},
		false,
		function(changeSetId)
			callBack(changeSetId)
		end
	)
end

--推送数据
function ActivityController:onActivityGainLimitInfoPush(activityGainLimit)
	if self.isInit then
		--数据更新，刷新本地数据
		ActivityGainLimitModel.instance:setGainLimitData(activityGainLimit)
		self:notify(GlobalNotify.ActivityGainLimitChange)
	end
end

---------------------首充---------------------
function ActivityController:checkRecharge()
	--没有开启的活动的时候注册
	local isClose = ActivityFacade.instance:getActivityInfo(GameEnum.ActivityEnum.ACTIVITY_464_FIRST_RECHARGE_V4) == nil
	print("checkRecharge", isClose)
	if isClose then
		self:registerNotify(GlobalNotify.ActivityUpdate, self._openRecharge, self)
	end
end

function ActivityController:_openRecharge()
	if ActivityFacade.instance:getActivityInfo(GameEnum.ActivityEnum.ACTIVITY_464_FIRST_RECHARGE_V4) then
		self:unregisterNotify(GlobalNotify.ActivityUpdate, self._openRecharge, self)
		ViewFacade.addCallBackWhenIdle(
			function()
				ViewMgr.instance:open("FirstRecharge2")
			end
		)
	end
end

ActivityController.instance = ActivityController.New()
return ActivityController
