module("logic.extensions.room.view.clothesstage.ClothesStageView", package.seeall)

local ClothesStageView = class("ClothesStageView", ListBinderView)

function ClothesStageView:ctor()
	ClothesStageView.super.ctor(self, BaseListModel.New(), "listview", ClothesStageCell.url, ClothesStageCell, {kScrollDirV, 106, 106, 19, 18, 3})
	self._avatarInfo = AvatarInfo.New()
end

function ClothesStageView:buildUI()
	ClothesStageView.super.buildUI(self)
	self._imgModel = self:getGo("imgModel")
	self:getBtn("btnClose"):AddClickListener(self.close, self)
    self:getBtn("btnEdit"):AddClickListener(self._onClickEdit, self)
	self._nameTxt = self:getText("nameTxt")
end

function ClothesStageView:_onClickEdit()
	local params = {}
	params.defaultClothes = {ClothesStageHandler.DefaultClothesId}
	params.idleAni = self:getFirstParam().animation
	params.furnitureId = self._serverId
	params.houseId = self._houseId
	ViewFacade.openSpecialDress(self._avatarInfo:getIdList(), 3, handler(self._onChangeClothes, self), params)
end

function ClothesStageView:onExit()
	if self._model then
		self._photoBase:TurnOff()
		self._model:dispose(false)
		self._model = nil
	end
end

function ClothesStageView:onEnter()
	ClothesStageView.super.onEnter(self)
	if not self:getIsBack() then
		self._isMyHouse = self:getFirstParam().isMyHouse
		self._serverId = self:getFirstParam().serverId
		self._houseId = self:getFirstParam().houseId
		--小岛和海底小岛的self:getFirstParam().houseId都是0，这里海底小岛强制改为-2
		if HouseModel.instance:isInIsland() then
			local islandType = HouseModel.instance:getUserProperty(HouseUserProperty.IslandType)
			self._houseId = RoomConfig.getDecorationSceneIdByIslandType(islandType)
		end
		self:getGo("btnEdit"):SetActive(self._isMyHouse)
		GameUtils.setUIHeight(self:getGo("listview").transform, self._isMyHouse and 191 or 264)
		local idList = self:getFirstParam().clothes or {}
		self._avatarInfo:setAvatar(idList)
		
		self._oldUIds = DressController.getClothesStageUIds(self._avatarInfo, self._serverId, self._houseId)
	end
	
	if self._isMyHouse then
		self:_setClothes(self._oldUIds)
	else
		self:_setOtherClothes()
	end
	
	self._nameTxt.text = ItemService.instance:getName(self:getFirstParam().itemId)
end
--其他玩家的熊台
function ClothesStageView:_setOtherClothes()
	local list = {}
	local items = self._avatarInfo:getAllItems()
	
	for i=#items,1,-1 do
		local define = items[i]
		if not ClothesConfig.isDefaultClotehs(define.id, ModelType.Bear) and define.id ~= ClothesStageHandler.DefaultClothesId then
			table.insert(list, {id = define.id})
		else
			self._avatarInfo:removeItem(define)
		end
	end

	table.sort(list, function(a, b)
			return ItemService.instance:getRank(a.id) > ItemService.instance:getRank(b.id)
		end)
	self._listModel:setMoList(list)
	
	
	self._avatarInfo:addItem(ClothesConfig.getClothesDefine(ClothesStageHandler.DefaultClothesId),true)
	if self._model == nil then
		self:_createModel()
	end
	self._furniture:setItem(self:getFirstParam().itemId, nil, true)
	self._model:setClothes(ModelType.Bear, self._avatarInfo)
	self._model._avatarView:playAnimation(self:getFirstParam().animation, true)
	self._model._avatarView.skinView:stopAdditionalAni()
	self:getGo("txtNoEdit"):SetActive(#list == 0)
end

function ClothesStageView:_setClothes(uIds)
	local list = {}
	if uIds ~= nil and #uIds > 0 then
		for i=#uIds,1,-1  do
			local cId = uIds[i]
			if ClothesConfig.isDefineID(cId) and ClothesConfig.isDefaultClotehs(cId, ModelType.Bear) then
				--table.remove(uIds,i)
			else
				if cId ~= ClothesStageHandler.DefaultClothesId then
					
					table.insert(list, DressController.instance:getItem(cId))
				end
			end
		end
	end
	table.sort(list, function(a, b)
		return ItemService.instance:getRank(a.id) > ItemService.instance:getRank(b.id)
	end)
	self._listModel:setMoList(list)
	
	DressController.setUIds2Avatar(self._avatarInfo,uIds)
	
	self._avatarInfo:addItem(ClothesConfig.getClothesDefine(ClothesStageHandler.DefaultClothesId),true)
	
	if self._model == nil then
		self:_createModel()
	end
	self._furniture:setItem(self:getFirstParam().itemId, nil, true)
	self._model:setClothes(ModelType.Bear, self._avatarInfo)
    self._model._avatarView:playAnimation(self:getFirstParam().animation, true)
	self._model._avatarView.skinView:stopAdditionalAni()
	self:getGo("txtNoEdit"):SetActive(#list == 0)
end

function ClothesStageView:_createModel()
	self._model = ModelPhotoBase.New(self._imgModel, 1, ModelPhotoBase.Type_Player)
	self._model._avatarView.idleComp:setEnable(false)
	
	self._furniture = GameUtils.createUIFurnitureView()
	self._photoBase = Framework.PhotoBase.Add(self:getGo("imgStage"))
	self._photoBase:TurnOn()
	self._photoBase:ShowTarget(self._furniture.go, true)
	self._photoBase.producer.rtCamera.cullingMask = 33
	self._photoBase.producer.rtCamera.orthographic = true
	self._photoBase.producer.rtCamera.orthographicSize = 1
	self._photoBase:SetCameraPosition(0, 0, -5)
end

function ClothesStageView:_onChangeClothes(uIds)
	
	for i=#uIds,1,-1  do
		local cId = uIds[i]
		if ClothesConfig.isDefineID(cId) and ClothesConfig.isDefaultClotehs(cId, ModelType.Bear) then
			--table.remove(uIds,i)
		end
	end
	
	DressController.setUIds2Avatar(self._avatarInfo,uIds)

	local changeIds = DressController.getClothesStageUIds(self._avatarInfo,self._serverId, self._houseId)
	
	for i=#changeIds,1,-1  do
		if changeIds[i] == ClothesStageHandler.DefaultClothesId  or (ClothesConfig.isDefineID(changeIds[i]) and ClothesConfig.isDefaultClotehs(changeIds[i], ModelType.Bear)) then
			table.remove(changeIds,i)
		end
	end
	
	local hasChange = not self._avatarInfo:equals(self._oldUIds)
	
    if not hasChange then return end
    if HouseModel.instance:isInIsland() then
        IslandAgent.instance:sendIsLandFurnitureChangeClothesRequest(self._serverId, changeIds, handler(self._onSave, self))
    else
        local houseId = HouseModel.instance:getUserProperty(HouseUserProperty.HouseId, 1)
        HouseAgent.instance:sendHouseFurnitureChangeClothesRequest(houseId, self._serverId, changeIds, handler(self._onSave, self))
    end
end

function ClothesStageView:_onSave()
	self._oldUIds = DressController.getClothesStageUIds(self._avatarInfo,self._serverId, self._houseId)
    self:localNotify(RoomNotifyName.ClothesStageChange, self._serverId, self._avatarInfo:getIdList())
    self:_setClothes(self._oldUIds)
end

return ClothesStageView 