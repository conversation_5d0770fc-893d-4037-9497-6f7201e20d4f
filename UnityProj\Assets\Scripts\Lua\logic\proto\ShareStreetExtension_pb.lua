-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
local USEREXTENSION_PB = require("logic.proto.UserExtension_pb")
local FOODEXTENSION_PB = require("logic.proto.FoodExtension_pb")
local HOUSEEXTENSION_PB = require("logic.proto.HouseExtension_pb")
local BACKPACKEXTENSION_PB = require("logic.proto.BackpackExtension_pb")
local ISLANDEXTENSION_PB = require("logic.proto.IslandExtension_pb")
module("logic.proto.ShareStreetExtension_pb", package.seeall)


local tb = {}
tb.SHARESTREETSEARCHTYPE_ENUM = protobuf.EnumDescriptor()
tb.SHARESTREETSEARCHTYPE_SEARCH_USER_ID_ENUMITEM = protobuf.EnumValueDescriptor()
tb.SHARESTREETSEARCHTYPE_SEARCH_USER_NAME_ENUMITEM = protobuf.EnumValueDescriptor()
tb.SHARESTREETSEARCHTYPE_SEARCH_STREET_ID_ENUMITEM = protobuf.EnumValueDescriptor()
STREETCANCELSHAREFURNITUREREQUEST_MSG = protobuf.Descriptor()
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD = protobuf.FieldDescriptor()
CANCELAPPLYJOINSHARESTREETREPLY_MSG = protobuf.Descriptor()
STREETUPDATEINFOREQUEST_MSG = protobuf.Descriptor()
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD = protobuf.FieldDescriptor()
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD = protobuf.FieldDescriptor()
VISITSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.VISITSHARESTREETREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
SHARESTREETDECORATIONNO_MSG = protobuf.Descriptor()
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_CROPS_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_AREAID_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD = protobuf.FieldDescriptor()
INVITEJOINSHARESTREETREPLY_MSG = protobuf.Descriptor()
AGREEJOINSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD = protobuf.FieldDescriptor()
AGREEJOINSHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD = protobuf.FieldDescriptor()
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD = protobuf.FieldDescriptor()
MODIFYSTREETACCEPTASKREPLY_MSG = protobuf.Descriptor()
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD = protobuf.FieldDescriptor()
STREETUNLOCKAREAREPLY_MSG = protobuf.Descriptor()
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD = protobuf.FieldDescriptor()
CANCELAPPLYJOINSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD = protobuf.FieldDescriptor()
LIKESHARESTREETREPLY_MSG = protobuf.Descriptor()
AGREEINVITEJOINSHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD = protobuf.FieldDescriptor()
GETSTREETRECOMMENDLISTREQUEST_MSG = protobuf.Descriptor()
SEARCHSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD = protobuf.FieldDescriptor()
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD = protobuf.FieldDescriptor()
STREETCANCELSHAREFURNITUREREPLY_MSG = protobuf.Descriptor()
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD = protobuf.FieldDescriptor()
MODIFYSTREETACCEPTASKREQUEST_MSG = protobuf.Descriptor()
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD = protobuf.FieldDescriptor()
GETSTREETPOPULARITYRANKREQUEST_MSG = protobuf.Descriptor()
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD = protobuf.FieldDescriptor()
REFUSEJOINSHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD = protobuf.FieldDescriptor()
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG = protobuf.Descriptor()
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD = protobuf.FieldDescriptor()
GETSTREETRECOMMENDLISTREPLY_MSG = protobuf.Descriptor()
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD = protobuf.FieldDescriptor()
REFUSEJOINSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD = protobuf.FieldDescriptor()
GETFRIENDSTREETLISTREPLY_MSG = protobuf.Descriptor()
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD = protobuf.FieldDescriptor()
SHARESTREETSCENEITEMNO_MSG = protobuf.Descriptor()
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD = protobuf.FieldDescriptor()
INVITEJOINSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD = protobuf.FieldDescriptor()
GETFRIENDSTREETLISTREQUEST_MSG = protobuf.Descriptor()
VISITSHARESTREETREPLY_MSG = protobuf.Descriptor()
AGREEINVITEJOINSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD = protobuf.FieldDescriptor()
STREETUNLOCKAREAREQUEST_MSG = protobuf.Descriptor()
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD = protobuf.FieldDescriptor()
GETSHARESTREETFURNITUREINFOREPLY_MSG = protobuf.Descriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD = protobuf.FieldDescriptor()
GETSTREETAPPLYLISTREQUEST_MSG = protobuf.Descriptor()
CHANGESHARESTREETHOUSEREPLY_MSG = protobuf.Descriptor()
GETSHARESTREETINFOREQUEST_MSG = protobuf.Descriptor()
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD = protobuf.FieldDescriptor()
FURNITURECOUNTNO_MSG = protobuf.Descriptor()
tb.FURNITURECOUNTNO_FURNITUREID_FIELD = protobuf.FieldDescriptor()
tb.FURNITURECOUNTNO_COUNT_FIELD = protobuf.FieldDescriptor()
tb.FURNITURECOUNTNO_USECOUNT_FIELD = protobuf.FieldDescriptor()
GETSTREETRECEIVEASKLISTREPLY_MSG = protobuf.Descriptor()
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD = protobuf.FieldDescriptor()
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD = protobuf.FieldDescriptor()
GETSTREETPOPULARITYRANKREPLY_MSG = protobuf.Descriptor()
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD = protobuf.FieldDescriptor()
GETSHARESTREETFURNITUREINFOREQUEST_MSG = protobuf.Descriptor()
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
GETUSERJOINEDSTREETIDREPLY_MSG = protobuf.Descriptor()
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD = protobuf.FieldDescriptor()
GETSHARESTREETINFOREPLY_MSG = protobuf.Descriptor()
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD = protobuf.FieldDescriptor()
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD = protobuf.FieldDescriptor()
SHARESTREETMEMBERNO_MSG = protobuf.Descriptor()
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETMEMBERNO_AREAID_FIELD = protobuf.FieldDescriptor()
APPLYJOINSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD = protobuf.FieldDescriptor()
CREATESHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.CREATESHARESTREETREQUEST_NAME_FIELD = protobuf.FieldDescriptor()
tb.CREATESHARESTREETREQUEST_DESC_FIELD = protobuf.FieldDescriptor()
SEARCHSHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD = protobuf.FieldDescriptor()
CREATESHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD = protobuf.FieldDescriptor()
STREETFURNITUREINFOCHANGEPUSH_MSG = protobuf.Descriptor()
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD = protobuf.FieldDescriptor()
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD = protobuf.FieldDescriptor()
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD = protobuf.FieldDescriptor()
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD = protobuf.FieldDescriptor()
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD = protobuf.FieldDescriptor()
QUITSHARESTREETREQUEST_MSG = protobuf.Descriptor()
GETSTREETRECEIVEINVITELISTREQUEST_MSG = protobuf.Descriptor()
GETSTREETRECEIVEINVITELISTREPLY_MSG = protobuf.Descriptor()
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD = protobuf.FieldDescriptor()
STREETSHAREFURNITUREREQUEST_MSG = protobuf.Descriptor()
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD = protobuf.FieldDescriptor()
CHANGESHARESTREETHOUSEREQUEST_MSG = protobuf.Descriptor()
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
GETUSERJOINEDSTREETIDREQUEST_MSG = protobuf.Descriptor()
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
QUITSHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD = protobuf.FieldDescriptor()
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG = protobuf.Descriptor()
GETPERSONSHAREFURNITURECOUNTREPLY_MSG = protobuf.Descriptor()
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD = protobuf.FieldDescriptor()
STREETUPDATEINFOREPLY_MSG = protobuf.Descriptor()
FRIENDSTREETINFONO_MSG = protobuf.Descriptor()
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_HASSTREET_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_NAME_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_ISINVITED_FIELD = protobuf.FieldDescriptor()
tb.FRIENDSTREETINFONO_PICTURE_FIELD = protobuf.FieldDescriptor()
DECORATESHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD = protobuf.FieldDescriptor()
SHARESTREETINFONO_MSG = protobuf.Descriptor()
tb.SHARESTREETINFONO_OWNERID_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_NAME_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_DESC_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_MEMBERS_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_AREAID_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_PICTURE_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_LIKECOUNT_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_VISITCOUNT_FIELD = protobuf.FieldDescriptor()
tb.SHARESTREETINFONO_DISPLAYID_FIELD = protobuf.FieldDescriptor()
GETSTREETAPPLYLISTREPLY_MSG = protobuf.Descriptor()
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD = protobuf.FieldDescriptor()
STREETSIMPLEINFONO_MSG = protobuf.Descriptor()
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD = protobuf.FieldDescriptor()
tb.STREETSIMPLEINFONO_NAME_FIELD = protobuf.FieldDescriptor()
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD = protobuf.FieldDescriptor()
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD = protobuf.FieldDescriptor()
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD = protobuf.FieldDescriptor()
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD = protobuf.FieldDescriptor()
tb.STREETSIMPLEINFONO_PICTURE_FIELD = protobuf.FieldDescriptor()
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD = protobuf.FieldDescriptor()
GETSTREETRECEIVEASKLISTREQUEST_MSG = protobuf.Descriptor()
KICKOUTSHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD = protobuf.FieldDescriptor()
KICKOUTSHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD = protobuf.FieldDescriptor()
LIKESHARESTREETREQUEST_MSG = protobuf.Descriptor()
tb.LIKESHARESTREETREQUEST_STREETID_FIELD = protobuf.FieldDescriptor()
APPLYJOINSHARESTREETREPLY_MSG = protobuf.Descriptor()
DECORATESHARESTREETREPLY_MSG = protobuf.Descriptor()
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_CROPS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD = protobuf.FieldDescriptor()
GETSTREETSHAREFURNITURECOUNTREPLY_MSG = protobuf.Descriptor()
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD = protobuf.FieldDescriptor()
STREETSHAREFURNITUREREPLY_MSG = protobuf.Descriptor()
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD = protobuf.FieldDescriptor()
STREETUSERFURNITURECOUNTNO_MSG = protobuf.Descriptor()
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD = protobuf.FieldDescriptor()
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD = protobuf.FieldDescriptor()

tb.SHARESTREETSEARCHTYPE_SEARCH_USER_ID_ENUMITEM.name = "SEARCH_USER_ID"
tb.SHARESTREETSEARCHTYPE_SEARCH_USER_ID_ENUMITEM.index = 0
tb.SHARESTREETSEARCHTYPE_SEARCH_USER_ID_ENUMITEM.number = 0
tb.SHARESTREETSEARCHTYPE_SEARCH_USER_NAME_ENUMITEM.name = "SEARCH_USER_NAME"
tb.SHARESTREETSEARCHTYPE_SEARCH_USER_NAME_ENUMITEM.index = 1
tb.SHARESTREETSEARCHTYPE_SEARCH_USER_NAME_ENUMITEM.number = 1
tb.SHARESTREETSEARCHTYPE_SEARCH_STREET_ID_ENUMITEM.name = "SEARCH_STREET_ID"
tb.SHARESTREETSEARCHTYPE_SEARCH_STREET_ID_ENUMITEM.index = 2
tb.SHARESTREETSEARCHTYPE_SEARCH_STREET_ID_ENUMITEM.number = 2
tb.SHARESTREETSEARCHTYPE_ENUM.name = "ShareStreetSearchType"
tb.SHARESTREETSEARCHTYPE_ENUM.full_name = ".ShareStreetSearchType"
tb.SHARESTREETSEARCHTYPE_ENUM.values = {tb.SHARESTREETSEARCHTYPE_SEARCH_USER_ID_ENUMITEM,tb.SHARESTREETSEARCHTYPE_SEARCH_USER_NAME_ENUMITEM,tb.SHARESTREETSEARCHTYPE_SEARCH_STREET_ID_ENUMITEM}
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.name = "furnitureCount"
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.full_name = ".StreetCancelShareFurnitureRequest.furnitureCount"
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.number = 1
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.index = 0
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.label = 3
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.has_default_value = false
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.default_value = {}
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.message_type = FURNITURECOUNTNO_MSG
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.type = 11
tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.cpp_type = 10

STREETCANCELSHAREFURNITUREREQUEST_MSG.name = "StreetCancelShareFurnitureRequest"
STREETCANCELSHAREFURNITUREREQUEST_MSG.full_name = ".StreetCancelShareFurnitureRequest"
STREETCANCELSHAREFURNITUREREQUEST_MSG.filename = "ShareStreetExtension"
STREETCANCELSHAREFURNITUREREQUEST_MSG.nested_types = {}
STREETCANCELSHAREFURNITUREREQUEST_MSG.enum_types = {}
STREETCANCELSHAREFURNITUREREQUEST_MSG.fields = {tb.STREETCANCELSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD}
STREETCANCELSHAREFURNITUREREQUEST_MSG.is_extendable = false
STREETCANCELSHAREFURNITUREREQUEST_MSG.extensions = {}
CANCELAPPLYJOINSHARESTREETREPLY_MSG.name = "CancelApplyJoinShareStreetReply"
CANCELAPPLYJOINSHARESTREETREPLY_MSG.full_name = ".CancelApplyJoinShareStreetReply"
CANCELAPPLYJOINSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
CANCELAPPLYJOINSHARESTREETREPLY_MSG.nested_types = {}
CANCELAPPLYJOINSHARESTREETREPLY_MSG.enum_types = {}
CANCELAPPLYJOINSHARESTREETREPLY_MSG.fields = {}
CANCELAPPLYJOINSHARESTREETREPLY_MSG.is_extendable = false
CANCELAPPLYJOINSHARESTREETREPLY_MSG.extensions = {}
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.name = "text"
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.full_name = ".StreetUpdateInfoRequest.text"
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.number = 1
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.index = 0
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.label = 1
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.has_default_value = false
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.default_value = ""
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.type = 9
tb.STREETUPDATEINFOREQUEST_TEXT_FIELD.cpp_type = 9

tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.name = "type"
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.full_name = ".StreetUpdateInfoRequest.type"
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.number = 2
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.index = 1
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.label = 1
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.has_default_value = false
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.default_value = 0
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.type = 5
tb.STREETUPDATEINFOREQUEST_TYPE_FIELD.cpp_type = 1

STREETUPDATEINFOREQUEST_MSG.name = "StreetUpdateInfoRequest"
STREETUPDATEINFOREQUEST_MSG.full_name = ".StreetUpdateInfoRequest"
STREETUPDATEINFOREQUEST_MSG.filename = "ShareStreetExtension"
STREETUPDATEINFOREQUEST_MSG.nested_types = {}
STREETUPDATEINFOREQUEST_MSG.enum_types = {}
STREETUPDATEINFOREQUEST_MSG.fields = {tb.STREETUPDATEINFOREQUEST_TEXT_FIELD, tb.STREETUPDATEINFOREQUEST_TYPE_FIELD}
STREETUPDATEINFOREQUEST_MSG.is_extendable = false
STREETUPDATEINFOREQUEST_MSG.extensions = {}
tb.VISITSHARESTREETREQUEST_USERID_FIELD.name = "userId"
tb.VISITSHARESTREETREQUEST_USERID_FIELD.full_name = ".VisitShareStreetRequest.userId"
tb.VISITSHARESTREETREQUEST_USERID_FIELD.number = 1
tb.VISITSHARESTREETREQUEST_USERID_FIELD.index = 0
tb.VISITSHARESTREETREQUEST_USERID_FIELD.label = 1
tb.VISITSHARESTREETREQUEST_USERID_FIELD.has_default_value = false
tb.VISITSHARESTREETREQUEST_USERID_FIELD.default_value = ""
tb.VISITSHARESTREETREQUEST_USERID_FIELD.type = 9
tb.VISITSHARESTREETREQUEST_USERID_FIELD.cpp_type = 9

VISITSHARESTREETREQUEST_MSG.name = "VisitShareStreetRequest"
VISITSHARESTREETREQUEST_MSG.full_name = ".VisitShareStreetRequest"
VISITSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
VISITSHARESTREETREQUEST_MSG.nested_types = {}
VISITSHARESTREETREQUEST_MSG.enum_types = {}
VISITSHARESTREETREQUEST_MSG.fields = {tb.VISITSHARESTREETREQUEST_USERID_FIELD}
VISITSHARESTREETREQUEST_MSG.is_extendable = false
VISITSHARESTREETREQUEST_MSG.extensions = {}
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.full_name = ".ShareStreetDecorationNO.decoratedItems"
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.number = 1
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.index = 0
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.label = 3
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.default_value = {}
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD.cpp_type = 10

tb.SHARESTREETDECORATIONNO_CROPS_FIELD.name = "crops"
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.full_name = ".ShareStreetDecorationNO.crops"
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.number = 2
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.index = 1
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.label = 3
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.default_value = {}
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.message_type = ISLANDEXTENSION_PB.MANORCROPNO_MSG
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_CROPS_FIELD.cpp_type = 10

tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.name = "houseInfo"
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.full_name = ".ShareStreetDecorationNO.houseInfo"
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.number = 3
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.index = 2
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.label = 3
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.default_value = {}
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.message_type = HOUSEEXTENSION_PB.HOUSESHOWAREAINFO_MSG
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD.cpp_type = 10

tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.name = "friendTrees"
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.full_name = ".ShareStreetDecorationNO.friendTrees"
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.number = 4
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.index = 3
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.label = 3
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.default_value = {}
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.message_type = ISLANDEXTENSION_PB.FRIENDTREENO_MSG
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD.cpp_type = 10

tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.name = "catchButterfly"
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.full_name = ".ShareStreetDecorationNO.catchButterfly"
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.number = 5
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.index = 4
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.label = 1
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.default_value = false
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.type = 8
tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD.cpp_type = 7

tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.full_name = ".ShareStreetDecorationNO.foodItemInfos"
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.number = 6
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.index = 5
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.label = 3
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.default_value = {}
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD.cpp_type = 10

tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.name = "terrains"
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.full_name = ".ShareStreetDecorationNO.terrains"
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.number = 7
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.index = 6
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.label = 3
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.default_value = {}
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.message_type = ISLANDEXTENSION_PB.TERRAININFONO_MSG
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD.cpp_type = 10

tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.name = "areaOwner"
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.full_name = ".ShareStreetDecorationNO.areaOwner"
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.number = 9
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.index = 7
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.label = 1
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.default_value = nil
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD.cpp_type = 10

tb.SHARESTREETDECORATIONNO_AREAID_FIELD.name = "areaId"
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.full_name = ".ShareStreetDecorationNO.areaId"
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.number = 10
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.index = 8
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.label = 1
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.default_value = 0
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.type = 5
tb.SHARESTREETDECORATIONNO_AREAID_FIELD.cpp_type = 1

tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.name = "isAllowToEat"
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.full_name = ".ShareStreetDecorationNO.isAllowToEat"
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.number = 11
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.index = 9
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.label = 1
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.default_value = false
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.type = 8
tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD.cpp_type = 7

tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.name = "sceneItems"
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.full_name = ".ShareStreetDecorationNO.sceneItems"
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.number = 12
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.index = 10
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.label = 3
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.has_default_value = false
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.default_value = {}
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.message_type = SHARESTREETSCENEITEMNO_MSG
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.type = 11
tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD.cpp_type = 10

SHARESTREETDECORATIONNO_MSG.name = "ShareStreetDecorationNO"
SHARESTREETDECORATIONNO_MSG.full_name = ".ShareStreetDecorationNO"
SHARESTREETDECORATIONNO_MSG.filename = "ShareStreetExtension"
SHARESTREETDECORATIONNO_MSG.nested_types = {}
SHARESTREETDECORATIONNO_MSG.enum_types = {}
SHARESTREETDECORATIONNO_MSG.fields = {tb.SHARESTREETDECORATIONNO_DECORATEDITEMS_FIELD, tb.SHARESTREETDECORATIONNO_CROPS_FIELD, tb.SHARESTREETDECORATIONNO_HOUSEINFO_FIELD, tb.SHARESTREETDECORATIONNO_FRIENDTREES_FIELD, tb.SHARESTREETDECORATIONNO_CATCHBUTTERFLY_FIELD, tb.SHARESTREETDECORATIONNO_FOODITEMINFOS_FIELD, tb.SHARESTREETDECORATIONNO_TERRAINS_FIELD, tb.SHARESTREETDECORATIONNO_AREAOWNER_FIELD, tb.SHARESTREETDECORATIONNO_AREAID_FIELD, tb.SHARESTREETDECORATIONNO_ISALLOWTOEAT_FIELD, tb.SHARESTREETDECORATIONNO_SCENEITEMS_FIELD}
SHARESTREETDECORATIONNO_MSG.is_extendable = false
SHARESTREETDECORATIONNO_MSG.extensions = {}
INVITEJOINSHARESTREETREPLY_MSG.name = "InviteJoinShareStreetReply"
INVITEJOINSHARESTREETREPLY_MSG.full_name = ".InviteJoinShareStreetReply"
INVITEJOINSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
INVITEJOINSHARESTREETREPLY_MSG.nested_types = {}
INVITEJOINSHARESTREETREPLY_MSG.enum_types = {}
INVITEJOINSHARESTREETREPLY_MSG.fields = {}
INVITEJOINSHARESTREETREPLY_MSG.is_extendable = false
INVITEJOINSHARESTREETREPLY_MSG.extensions = {}
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.name = "applyUserId"
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.full_name = ".AgreeJoinShareStreetRequest.applyUserId"
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.number = 1
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.index = 0
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.label = 1
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.has_default_value = false
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.default_value = ""
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.type = 9
tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.cpp_type = 9

AGREEJOINSHARESTREETREQUEST_MSG.name = "AgreeJoinShareStreetRequest"
AGREEJOINSHARESTREETREQUEST_MSG.full_name = ".AgreeJoinShareStreetRequest"
AGREEJOINSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
AGREEJOINSHARESTREETREQUEST_MSG.nested_types = {}
AGREEJOINSHARESTREETREQUEST_MSG.enum_types = {}
AGREEJOINSHARESTREETREQUEST_MSG.fields = {tb.AGREEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD}
AGREEJOINSHARESTREETREQUEST_MSG.is_extendable = false
AGREEJOINSHARESTREETREQUEST_MSG.extensions = {}
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.name = "askList"
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.full_name = ".AgreeJoinShareStreetReply.askList"
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.number = 1
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.index = 0
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.label = 3
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.has_default_value = false
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.default_value = {}
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.type = 11
tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD.cpp_type = 10

tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.name = "shareStreetInfo"
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.full_name = ".AgreeJoinShareStreetReply.shareStreetInfo"
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.number = 2
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.index = 1
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.label = 1
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.has_default_value = false
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.default_value = nil
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.message_type = SHARESTREETINFONO_MSG
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.type = 11
tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.cpp_type = 10

AGREEJOINSHARESTREETREPLY_MSG.name = "AgreeJoinShareStreetReply"
AGREEJOINSHARESTREETREPLY_MSG.full_name = ".AgreeJoinShareStreetReply"
AGREEJOINSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
AGREEJOINSHARESTREETREPLY_MSG.nested_types = {}
AGREEJOINSHARESTREETREPLY_MSG.enum_types = {}
AGREEJOINSHARESTREETREPLY_MSG.fields = {tb.AGREEJOINSHARESTREETREPLY_ASKLIST_FIELD, tb.AGREEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD}
AGREEJOINSHARESTREETREPLY_MSG.is_extendable = false
AGREEJOINSHARESTREETREPLY_MSG.extensions = {}
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.name = "isAccept"
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.full_name = ".ModifyStreetAcceptAskReply.isAccept"
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.number = 1
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.index = 0
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.label = 1
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.has_default_value = false
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.default_value = false
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.type = 8
tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD.cpp_type = 7

MODIFYSTREETACCEPTASKREPLY_MSG.name = "ModifyStreetAcceptAskReply"
MODIFYSTREETACCEPTASKREPLY_MSG.full_name = ".ModifyStreetAcceptAskReply"
MODIFYSTREETACCEPTASKREPLY_MSG.filename = "ShareStreetExtension"
MODIFYSTREETACCEPTASKREPLY_MSG.nested_types = {}
MODIFYSTREETACCEPTASKREPLY_MSG.enum_types = {}
MODIFYSTREETACCEPTASKREPLY_MSG.fields = {tb.MODIFYSTREETACCEPTASKREPLY_ISACCEPT_FIELD}
MODIFYSTREETACCEPTASKREPLY_MSG.is_extendable = false
MODIFYSTREETACCEPTASKREPLY_MSG.extensions = {}
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.name = "changeId"
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.full_name = ".StreetUnlockAreaReply.changeId"
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.number = 1
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.index = 0
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.label = 1
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.has_default_value = false
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.default_value = 0
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.type = 5
tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD.cpp_type = 1

STREETUNLOCKAREAREPLY_MSG.name = "StreetUnlockAreaReply"
STREETUNLOCKAREAREPLY_MSG.full_name = ".StreetUnlockAreaReply"
STREETUNLOCKAREAREPLY_MSG.filename = "ShareStreetExtension"
STREETUNLOCKAREAREPLY_MSG.nested_types = {}
STREETUNLOCKAREAREPLY_MSG.enum_types = {}
STREETUNLOCKAREAREPLY_MSG.fields = {tb.STREETUNLOCKAREAREPLY_CHANGEID_FIELD}
STREETUNLOCKAREAREPLY_MSG.is_extendable = false
STREETUNLOCKAREAREPLY_MSG.extensions = {}
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.name = "streetId"
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.full_name = ".CancelApplyJoinShareStreetRequest.streetId"
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.number = 1
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.index = 0
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.label = 1
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.has_default_value = false
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.default_value = ""
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.type = 9
tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD.cpp_type = 9

CANCELAPPLYJOINSHARESTREETREQUEST_MSG.name = "CancelApplyJoinShareStreetRequest"
CANCELAPPLYJOINSHARESTREETREQUEST_MSG.full_name = ".CancelApplyJoinShareStreetRequest"
CANCELAPPLYJOINSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
CANCELAPPLYJOINSHARESTREETREQUEST_MSG.nested_types = {}
CANCELAPPLYJOINSHARESTREETREQUEST_MSG.enum_types = {}
CANCELAPPLYJOINSHARESTREETREQUEST_MSG.fields = {tb.CANCELAPPLYJOINSHARESTREETREQUEST_STREETID_FIELD}
CANCELAPPLYJOINSHARESTREETREQUEST_MSG.is_extendable = false
CANCELAPPLYJOINSHARESTREETREQUEST_MSG.extensions = {}
LIKESHARESTREETREPLY_MSG.name = "LikeShareStreetReply"
LIKESHARESTREETREPLY_MSG.full_name = ".LikeShareStreetReply"
LIKESHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
LIKESHARESTREETREPLY_MSG.nested_types = {}
LIKESHARESTREETREPLY_MSG.enum_types = {}
LIKESHARESTREETREPLY_MSG.fields = {}
LIKESHARESTREETREPLY_MSG.is_extendable = false
LIKESHARESTREETREPLY_MSG.extensions = {}
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.name = "shareStreetInfo"
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.full_name = ".AgreeInviteJoinShareStreetReply.shareStreetInfo"
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.number = 1
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.index = 0
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.label = 1
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.has_default_value = false
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.default_value = nil
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.message_type = SHARESTREETINFONO_MSG
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.type = 11
tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD.cpp_type = 10

AGREEINVITEJOINSHARESTREETREPLY_MSG.name = "AgreeInviteJoinShareStreetReply"
AGREEINVITEJOINSHARESTREETREPLY_MSG.full_name = ".AgreeInviteJoinShareStreetReply"
AGREEINVITEJOINSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
AGREEINVITEJOINSHARESTREETREPLY_MSG.nested_types = {}
AGREEINVITEJOINSHARESTREETREPLY_MSG.enum_types = {}
AGREEINVITEJOINSHARESTREETREPLY_MSG.fields = {tb.AGREEINVITEJOINSHARESTREETREPLY_SHARESTREETINFO_FIELD}
AGREEINVITEJOINSHARESTREETREPLY_MSG.is_extendable = false
AGREEINVITEJOINSHARESTREETREPLY_MSG.extensions = {}
GETSTREETRECOMMENDLISTREQUEST_MSG.name = "GetStreetRecommendListRequest"
GETSTREETRECOMMENDLISTREQUEST_MSG.full_name = ".GetStreetRecommendListRequest"
GETSTREETRECOMMENDLISTREQUEST_MSG.filename = "ShareStreetExtension"
GETSTREETRECOMMENDLISTREQUEST_MSG.nested_types = {}
GETSTREETRECOMMENDLISTREQUEST_MSG.enum_types = {}
GETSTREETRECOMMENDLISTREQUEST_MSG.fields = {}
GETSTREETRECOMMENDLISTREQUEST_MSG.is_extendable = false
GETSTREETRECOMMENDLISTREQUEST_MSG.extensions = {}
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.name = "searchKey"
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.full_name = ".SearchShareStreetRequest.searchKey"
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.number = 1
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.index = 0
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.label = 1
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.has_default_value = false
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.default_value = ""
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.type = 9
tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD.cpp_type = 9

tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.name = "searchType"
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.full_name = ".SearchShareStreetRequest.searchType"
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.number = 2
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.index = 1
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.label = 1
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.has_default_value = false
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.default_value = nil
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.enum_type = SHARESTREETSEARCHTYPE_ENUM
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.type = 14
tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD.cpp_type = 8

SEARCHSHARESTREETREQUEST_MSG.name = "SearchShareStreetRequest"
SEARCHSHARESTREETREQUEST_MSG.full_name = ".SearchShareStreetRequest"
SEARCHSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
SEARCHSHARESTREETREQUEST_MSG.nested_types = {}
SEARCHSHARESTREETREQUEST_MSG.enum_types = {}
SEARCHSHARESTREETREQUEST_MSG.fields = {tb.SEARCHSHARESTREETREQUEST_SEARCHKEY_FIELD, tb.SEARCHSHARESTREETREQUEST_SEARCHTYPE_FIELD}
SEARCHSHARESTREETREQUEST_MSG.is_extendable = false
SEARCHSHARESTREETREQUEST_MSG.extensions = {}
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.name = "changeId"
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.full_name = ".StreetCancelShareFurnitureReply.changeId"
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.number = 1
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.index = 0
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.label = 1
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.has_default_value = false
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.default_value = 0
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.type = 5
tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD.cpp_type = 1

STREETCANCELSHAREFURNITUREREPLY_MSG.name = "StreetCancelShareFurnitureReply"
STREETCANCELSHAREFURNITUREREPLY_MSG.full_name = ".StreetCancelShareFurnitureReply"
STREETCANCELSHAREFURNITUREREPLY_MSG.filename = "ShareStreetExtension"
STREETCANCELSHAREFURNITUREREPLY_MSG.nested_types = {}
STREETCANCELSHAREFURNITUREREPLY_MSG.enum_types = {}
STREETCANCELSHAREFURNITUREREPLY_MSG.fields = {tb.STREETCANCELSHAREFURNITUREREPLY_CHANGEID_FIELD}
STREETCANCELSHAREFURNITUREREPLY_MSG.is_extendable = false
STREETCANCELSHAREFURNITUREREPLY_MSG.extensions = {}
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.name = "isAccept"
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.full_name = ".ModifyStreetAcceptAskRequest.isAccept"
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.number = 1
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.index = 0
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.label = 1
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.has_default_value = false
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.default_value = false
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.type = 8
tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD.cpp_type = 7

MODIFYSTREETACCEPTASKREQUEST_MSG.name = "ModifyStreetAcceptAskRequest"
MODIFYSTREETACCEPTASKREQUEST_MSG.full_name = ".ModifyStreetAcceptAskRequest"
MODIFYSTREETACCEPTASKREQUEST_MSG.filename = "ShareStreetExtension"
MODIFYSTREETACCEPTASKREQUEST_MSG.nested_types = {}
MODIFYSTREETACCEPTASKREQUEST_MSG.enum_types = {}
MODIFYSTREETACCEPTASKREQUEST_MSG.fields = {tb.MODIFYSTREETACCEPTASKREQUEST_ISACCEPT_FIELD}
MODIFYSTREETACCEPTASKREQUEST_MSG.is_extendable = false
MODIFYSTREETACCEPTASKREQUEST_MSG.extensions = {}
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.name = "friend"
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.full_name = ".GetStreetPopularityRankRequest.friend"
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.number = 1
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.index = 0
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.label = 1
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.has_default_value = false
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.default_value = false
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.type = 8
tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD.cpp_type = 7

GETSTREETPOPULARITYRANKREQUEST_MSG.name = "GetStreetPopularityRankRequest"
GETSTREETPOPULARITYRANKREQUEST_MSG.full_name = ".GetStreetPopularityRankRequest"
GETSTREETPOPULARITYRANKREQUEST_MSG.filename = "ShareStreetExtension"
GETSTREETPOPULARITYRANKREQUEST_MSG.nested_types = {}
GETSTREETPOPULARITYRANKREQUEST_MSG.enum_types = {}
GETSTREETPOPULARITYRANKREQUEST_MSG.fields = {tb.GETSTREETPOPULARITYRANKREQUEST_FRIEND_FIELD}
GETSTREETPOPULARITYRANKREQUEST_MSG.is_extendable = false
GETSTREETPOPULARITYRANKREQUEST_MSG.extensions = {}
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.name = "askList"
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.full_name = ".RefuseJoinShareStreetReply.askList"
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.number = 1
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.index = 0
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.label = 3
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.has_default_value = false
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.default_value = {}
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.type = 11
tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD.cpp_type = 10

REFUSEJOINSHARESTREETREPLY_MSG.name = "RefuseJoinShareStreetReply"
REFUSEJOINSHARESTREETREPLY_MSG.full_name = ".RefuseJoinShareStreetReply"
REFUSEJOINSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
REFUSEJOINSHARESTREETREPLY_MSG.nested_types = {}
REFUSEJOINSHARESTREETREPLY_MSG.enum_types = {}
REFUSEJOINSHARESTREETREPLY_MSG.fields = {tb.REFUSEJOINSHARESTREETREPLY_ASKLIST_FIELD}
REFUSEJOINSHARESTREETREPLY_MSG.is_extendable = false
REFUSEJOINSHARESTREETREPLY_MSG.extensions = {}
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.name = "isTotal"
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.full_name = ".GetStreetShareFurnitureCountRequest.isTotal"
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.number = 1
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.index = 0
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.label = 1
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.has_default_value = false
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.default_value = false
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.type = 8
tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD.cpp_type = 7

GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.name = "GetStreetShareFurnitureCountRequest"
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.full_name = ".GetStreetShareFurnitureCountRequest"
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.filename = "ShareStreetExtension"
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.nested_types = {}
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.enum_types = {}
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.fields = {tb.GETSTREETSHAREFURNITURECOUNTREQUEST_ISTOTAL_FIELD}
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.is_extendable = false
GETSTREETSHAREFURNITURECOUNTREQUEST_MSG.extensions = {}
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.name = "recommendList"
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.full_name = ".GetStreetRecommendListReply.recommendList"
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.number = 1
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.index = 0
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.label = 3
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.has_default_value = false
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.default_value = {}
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.type = 11
tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD.cpp_type = 10

GETSTREETRECOMMENDLISTREPLY_MSG.name = "GetStreetRecommendListReply"
GETSTREETRECOMMENDLISTREPLY_MSG.full_name = ".GetStreetRecommendListReply"
GETSTREETRECOMMENDLISTREPLY_MSG.filename = "ShareStreetExtension"
GETSTREETRECOMMENDLISTREPLY_MSG.nested_types = {}
GETSTREETRECOMMENDLISTREPLY_MSG.enum_types = {}
GETSTREETRECOMMENDLISTREPLY_MSG.fields = {tb.GETSTREETRECOMMENDLISTREPLY_RECOMMENDLIST_FIELD}
GETSTREETRECOMMENDLISTREPLY_MSG.is_extendable = false
GETSTREETRECOMMENDLISTREPLY_MSG.extensions = {}
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.name = "applyUserId"
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.full_name = ".RefuseJoinShareStreetRequest.applyUserId"
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.number = 1
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.index = 0
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.label = 1
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.has_default_value = false
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.default_value = ""
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.type = 9
tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD.cpp_type = 9

REFUSEJOINSHARESTREETREQUEST_MSG.name = "RefuseJoinShareStreetRequest"
REFUSEJOINSHARESTREETREQUEST_MSG.full_name = ".RefuseJoinShareStreetRequest"
REFUSEJOINSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
REFUSEJOINSHARESTREETREQUEST_MSG.nested_types = {}
REFUSEJOINSHARESTREETREQUEST_MSG.enum_types = {}
REFUSEJOINSHARESTREETREQUEST_MSG.fields = {tb.REFUSEJOINSHARESTREETREQUEST_APPLYUSERID_FIELD}
REFUSEJOINSHARESTREETREQUEST_MSG.is_extendable = false
REFUSEJOINSHARESTREETREQUEST_MSG.extensions = {}
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.name = "streetList"
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.full_name = ".GetFriendStreetListReply.streetList"
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.number = 1
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.index = 0
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.label = 3
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.has_default_value = false
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.default_value = {}
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.message_type = FRIENDSTREETINFONO_MSG
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.type = 11
tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD.cpp_type = 10

GETFRIENDSTREETLISTREPLY_MSG.name = "GetFriendStreetListReply"
GETFRIENDSTREETLISTREPLY_MSG.full_name = ".GetFriendStreetListReply"
GETFRIENDSTREETLISTREPLY_MSG.filename = "ShareStreetExtension"
GETFRIENDSTREETLISTREPLY_MSG.nested_types = {}
GETFRIENDSTREETLISTREPLY_MSG.enum_types = {}
GETFRIENDSTREETLISTREPLY_MSG.fields = {tb.GETFRIENDSTREETLISTREPLY_STREETLIST_FIELD}
GETFRIENDSTREETLISTREPLY_MSG.is_extendable = false
GETFRIENDSTREETLISTREPLY_MSG.extensions = {}
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.name = "positionId"
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.full_name = ".ShareStreetSceneItemNO.positionId"
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.number = 1
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.index = 0
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.label = 1
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.has_default_value = false
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.default_value = 0
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.type = 5
tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD.cpp_type = 1

tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.name = "itemId"
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.full_name = ".ShareStreetSceneItemNO.itemId"
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.number = 2
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.index = 1
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.label = 1
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.has_default_value = false
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.default_value = 0
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.type = 5
tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD.cpp_type = 1

SHARESTREETSCENEITEMNO_MSG.name = "ShareStreetSceneItemNO"
SHARESTREETSCENEITEMNO_MSG.full_name = ".ShareStreetSceneItemNO"
SHARESTREETSCENEITEMNO_MSG.filename = "ShareStreetExtension"
SHARESTREETSCENEITEMNO_MSG.nested_types = {}
SHARESTREETSCENEITEMNO_MSG.enum_types = {}
SHARESTREETSCENEITEMNO_MSG.fields = {tb.SHARESTREETSCENEITEMNO_POSITIONID_FIELD, tb.SHARESTREETSCENEITEMNO_ITEMID_FIELD}
SHARESTREETSCENEITEMNO_MSG.is_extendable = false
SHARESTREETSCENEITEMNO_MSG.extensions = {}
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.name = "inviteUserId"
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.full_name = ".InviteJoinShareStreetRequest.inviteUserId"
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.number = 1
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.index = 0
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.label = 1
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.has_default_value = false
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.default_value = ""
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.type = 9
tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD.cpp_type = 9

INVITEJOINSHARESTREETREQUEST_MSG.name = "InviteJoinShareStreetRequest"
INVITEJOINSHARESTREETREQUEST_MSG.full_name = ".InviteJoinShareStreetRequest"
INVITEJOINSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
INVITEJOINSHARESTREETREQUEST_MSG.nested_types = {}
INVITEJOINSHARESTREETREQUEST_MSG.enum_types = {}
INVITEJOINSHARESTREETREQUEST_MSG.fields = {tb.INVITEJOINSHARESTREETREQUEST_INVITEUSERID_FIELD}
INVITEJOINSHARESTREETREQUEST_MSG.is_extendable = false
INVITEJOINSHARESTREETREQUEST_MSG.extensions = {}
GETFRIENDSTREETLISTREQUEST_MSG.name = "GetFriendStreetListRequest"
GETFRIENDSTREETLISTREQUEST_MSG.full_name = ".GetFriendStreetListRequest"
GETFRIENDSTREETLISTREQUEST_MSG.filename = "ShareStreetExtension"
GETFRIENDSTREETLISTREQUEST_MSG.nested_types = {}
GETFRIENDSTREETLISTREQUEST_MSG.enum_types = {}
GETFRIENDSTREETLISTREQUEST_MSG.fields = {}
GETFRIENDSTREETLISTREQUEST_MSG.is_extendable = false
GETFRIENDSTREETLISTREQUEST_MSG.extensions = {}
VISITSHARESTREETREPLY_MSG.name = "VisitShareStreetReply"
VISITSHARESTREETREPLY_MSG.full_name = ".VisitShareStreetReply"
VISITSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
VISITSHARESTREETREPLY_MSG.nested_types = {}
VISITSHARESTREETREPLY_MSG.enum_types = {}
VISITSHARESTREETREPLY_MSG.fields = {}
VISITSHARESTREETREPLY_MSG.is_extendable = false
VISITSHARESTREETREPLY_MSG.extensions = {}
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.name = "streetUserId"
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.full_name = ".AgreeInviteJoinShareStreetRequest.streetUserId"
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.number = 1
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.index = 0
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.label = 1
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.has_default_value = false
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.default_value = ""
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.type = 9
tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD.cpp_type = 9

AGREEINVITEJOINSHARESTREETREQUEST_MSG.name = "AgreeInviteJoinShareStreetRequest"
AGREEINVITEJOINSHARESTREETREQUEST_MSG.full_name = ".AgreeInviteJoinShareStreetRequest"
AGREEINVITEJOINSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
AGREEINVITEJOINSHARESTREETREQUEST_MSG.nested_types = {}
AGREEINVITEJOINSHARESTREETREQUEST_MSG.enum_types = {}
AGREEINVITEJOINSHARESTREETREQUEST_MSG.fields = {tb.AGREEINVITEJOINSHARESTREETREQUEST_STREETUSERID_FIELD}
AGREEINVITEJOINSHARESTREETREQUEST_MSG.is_extendable = false
AGREEINVITEJOINSHARESTREETREQUEST_MSG.extensions = {}
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.name = "areaId"
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.full_name = ".StreetUnlockAreaRequest.areaId"
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.number = 1
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.index = 0
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.label = 1
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.has_default_value = false
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.default_value = 0
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.type = 5
tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD.cpp_type = 1

STREETUNLOCKAREAREQUEST_MSG.name = "StreetUnlockAreaRequest"
STREETUNLOCKAREAREQUEST_MSG.full_name = ".StreetUnlockAreaRequest"
STREETUNLOCKAREAREQUEST_MSG.filename = "ShareStreetExtension"
STREETUNLOCKAREAREQUEST_MSG.nested_types = {}
STREETUNLOCKAREAREQUEST_MSG.enum_types = {}
STREETUNLOCKAREAREQUEST_MSG.fields = {tb.STREETUNLOCKAREAREQUEST_AREAID_FIELD}
STREETUNLOCKAREAREQUEST_MSG.is_extendable = false
STREETUNLOCKAREAREQUEST_MSG.extensions = {}
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.name = "decorationInfo"
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.decorationInfo"
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.number = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.index = 0
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.label = 3
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.default_value = {}
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.message_type = SHARESTREETDECORATIONNO_MSG
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.type = 11
tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD.cpp_type = 10

tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.name = "needHappyBirthday"
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.needHappyBirthday"
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.number = 2
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.index = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.type = 8
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.cpp_type = 7

tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.name = "needBackFlow2Story"
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.needBackFlow2Story"
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.number = 3
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.index = 2
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.type = 8
tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD.cpp_type = 7

tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.name = "sceneItems"
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.sceneItems"
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.number = 4
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.index = 3
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.label = 3
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.default_value = {}
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.message_type = SHARESTREETSCENEITEMNO_MSG
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.type = 11
tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD.cpp_type = 10

tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.name = "like"
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.like"
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.number = 5
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.index = 4
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.type = 8
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD.cpp_type = 7

tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.name = "likeCount"
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.likeCount"
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.number = 6
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.index = 5
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.default_value = 0
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.type = 5
tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD.cpp_type = 1

tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.name = "visitCount"
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.visitCount"
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.number = 7
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.index = 6
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.default_value = 0
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.type = 5
tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD.cpp_type = 1

tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.name = "areaId"
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.areaId"
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.number = 8
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.index = 7
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.label = 3
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.default_value = {}
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.type = 5
tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD.cpp_type = 1

tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.name = "takePhoto"
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.takePhoto"
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.number = 9
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.index = 8
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.type = 8
tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD.cpp_type = 7

tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.name = "streetName"
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.full_name = ".GetShareStreetFurnitureInfoReply.streetName"
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.number = 10
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.index = 9
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.default_value = ""
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.type = 9
tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD.cpp_type = 9

GETSHARESTREETFURNITUREINFOREPLY_MSG.name = "GetShareStreetFurnitureInfoReply"
GETSHARESTREETFURNITUREINFOREPLY_MSG.full_name = ".GetShareStreetFurnitureInfoReply"
GETSHARESTREETFURNITUREINFOREPLY_MSG.filename = "ShareStreetExtension"
GETSHARESTREETFURNITUREINFOREPLY_MSG.nested_types = {}
GETSHARESTREETFURNITUREINFOREPLY_MSG.enum_types = {}
GETSHARESTREETFURNITUREINFOREPLY_MSG.fields = {tb.GETSHARESTREETFURNITUREINFOREPLY_DECORATIONINFO_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_NEEDBACKFLOW2STORY_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_SCENEITEMS_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_LIKE_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_LIKECOUNT_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_VISITCOUNT_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_AREAID_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_TAKEPHOTO_FIELD, tb.GETSHARESTREETFURNITUREINFOREPLY_STREETNAME_FIELD}
GETSHARESTREETFURNITUREINFOREPLY_MSG.is_extendable = false
GETSHARESTREETFURNITUREINFOREPLY_MSG.extensions = {}
GETSTREETAPPLYLISTREQUEST_MSG.name = "GetStreetApplyListRequest"
GETSTREETAPPLYLISTREQUEST_MSG.full_name = ".GetStreetApplyListRequest"
GETSTREETAPPLYLISTREQUEST_MSG.filename = "ShareStreetExtension"
GETSTREETAPPLYLISTREQUEST_MSG.nested_types = {}
GETSTREETAPPLYLISTREQUEST_MSG.enum_types = {}
GETSTREETAPPLYLISTREQUEST_MSG.fields = {}
GETSTREETAPPLYLISTREQUEST_MSG.is_extendable = false
GETSTREETAPPLYLISTREQUEST_MSG.extensions = {}
CHANGESHARESTREETHOUSEREPLY_MSG.name = "ChangeShareStreetHouseReply"
CHANGESHARESTREETHOUSEREPLY_MSG.full_name = ".ChangeShareStreetHouseReply"
CHANGESHARESTREETHOUSEREPLY_MSG.filename = "ShareStreetExtension"
CHANGESHARESTREETHOUSEREPLY_MSG.nested_types = {}
CHANGESHARESTREETHOUSEREPLY_MSG.enum_types = {}
CHANGESHARESTREETHOUSEREPLY_MSG.fields = {}
CHANGESHARESTREETHOUSEREPLY_MSG.is_extendable = false
CHANGESHARESTREETHOUSEREPLY_MSG.extensions = {}
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.name = "userId"
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.full_name = ".GetShareStreetInfoRequest.userId"
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.number = 1
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.index = 0
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.label = 1
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.default_value = ""
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.type = 9
tb.GETSHARESTREETINFOREQUEST_USERID_FIELD.cpp_type = 9

tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.name = "isSelf"
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.full_name = ".GetShareStreetInfoRequest.isSelf"
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.number = 2
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.index = 1
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.label = 1
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.default_value = false
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.type = 8
tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD.cpp_type = 7

GETSHARESTREETINFOREQUEST_MSG.name = "GetShareStreetInfoRequest"
GETSHARESTREETINFOREQUEST_MSG.full_name = ".GetShareStreetInfoRequest"
GETSHARESTREETINFOREQUEST_MSG.filename = "ShareStreetExtension"
GETSHARESTREETINFOREQUEST_MSG.nested_types = {}
GETSHARESTREETINFOREQUEST_MSG.enum_types = {}
GETSHARESTREETINFOREQUEST_MSG.fields = {tb.GETSHARESTREETINFOREQUEST_USERID_FIELD, tb.GETSHARESTREETINFOREQUEST_ISSELF_FIELD}
GETSHARESTREETINFOREQUEST_MSG.is_extendable = false
GETSHARESTREETINFOREQUEST_MSG.extensions = {}
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.name = "furnitureId"
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.full_name = ".FurnitureCountNO.furnitureId"
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.number = 1
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.index = 0
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.label = 1
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.has_default_value = false
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.default_value = 0
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.type = 5
tb.FURNITURECOUNTNO_FURNITUREID_FIELD.cpp_type = 1

tb.FURNITURECOUNTNO_COUNT_FIELD.name = "count"
tb.FURNITURECOUNTNO_COUNT_FIELD.full_name = ".FurnitureCountNO.count"
tb.FURNITURECOUNTNO_COUNT_FIELD.number = 2
tb.FURNITURECOUNTNO_COUNT_FIELD.index = 1
tb.FURNITURECOUNTNO_COUNT_FIELD.label = 1
tb.FURNITURECOUNTNO_COUNT_FIELD.has_default_value = false
tb.FURNITURECOUNTNO_COUNT_FIELD.default_value = 0
tb.FURNITURECOUNTNO_COUNT_FIELD.type = 5
tb.FURNITURECOUNTNO_COUNT_FIELD.cpp_type = 1

tb.FURNITURECOUNTNO_USECOUNT_FIELD.name = "useCount"
tb.FURNITURECOUNTNO_USECOUNT_FIELD.full_name = ".FurnitureCountNO.useCount"
tb.FURNITURECOUNTNO_USECOUNT_FIELD.number = 3
tb.FURNITURECOUNTNO_USECOUNT_FIELD.index = 2
tb.FURNITURECOUNTNO_USECOUNT_FIELD.label = 1
tb.FURNITURECOUNTNO_USECOUNT_FIELD.has_default_value = false
tb.FURNITURECOUNTNO_USECOUNT_FIELD.default_value = 0
tb.FURNITURECOUNTNO_USECOUNT_FIELD.type = 5
tb.FURNITURECOUNTNO_USECOUNT_FIELD.cpp_type = 1

FURNITURECOUNTNO_MSG.name = "FurnitureCountNO"
FURNITURECOUNTNO_MSG.full_name = ".FurnitureCountNO"
FURNITURECOUNTNO_MSG.filename = "ShareStreetExtension"
FURNITURECOUNTNO_MSG.nested_types = {}
FURNITURECOUNTNO_MSG.enum_types = {}
FURNITURECOUNTNO_MSG.fields = {tb.FURNITURECOUNTNO_FURNITUREID_FIELD, tb.FURNITURECOUNTNO_COUNT_FIELD, tb.FURNITURECOUNTNO_USECOUNT_FIELD}
FURNITURECOUNTNO_MSG.is_extendable = false
FURNITURECOUNTNO_MSG.extensions = {}
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.name = "askList"
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.full_name = ".GetStreetReceiveAskListReply.askList"
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.number = 1
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.index = 0
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.label = 3
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.has_default_value = false
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.default_value = {}
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.type = 11
tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD.cpp_type = 10

tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.name = "acceptAsk"
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.full_name = ".GetStreetReceiveAskListReply.acceptAsk"
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.number = 2
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.index = 1
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.label = 1
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.has_default_value = false
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.default_value = false
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.type = 8
tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD.cpp_type = 7

GETSTREETRECEIVEASKLISTREPLY_MSG.name = "GetStreetReceiveAskListReply"
GETSTREETRECEIVEASKLISTREPLY_MSG.full_name = ".GetStreetReceiveAskListReply"
GETSTREETRECEIVEASKLISTREPLY_MSG.filename = "ShareStreetExtension"
GETSTREETRECEIVEASKLISTREPLY_MSG.nested_types = {}
GETSTREETRECEIVEASKLISTREPLY_MSG.enum_types = {}
GETSTREETRECEIVEASKLISTREPLY_MSG.fields = {tb.GETSTREETRECEIVEASKLISTREPLY_ASKLIST_FIELD, tb.GETSTREETRECEIVEASKLISTREPLY_ACCEPTASK_FIELD}
GETSTREETRECEIVEASKLISTREPLY_MSG.is_extendable = false
GETSTREETRECEIVEASKLISTREPLY_MSG.extensions = {}
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.name = "rankList"
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.full_name = ".GetStreetPopularityRankReply.rankList"
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.number = 1
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.index = 0
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.label = 3
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.has_default_value = false
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.default_value = {}
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.type = 11
tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD.cpp_type = 10

GETSTREETPOPULARITYRANKREPLY_MSG.name = "GetStreetPopularityRankReply"
GETSTREETPOPULARITYRANKREPLY_MSG.full_name = ".GetStreetPopularityRankReply"
GETSTREETPOPULARITYRANKREPLY_MSG.filename = "ShareStreetExtension"
GETSTREETPOPULARITYRANKREPLY_MSG.nested_types = {}
GETSTREETPOPULARITYRANKREPLY_MSG.enum_types = {}
GETSTREETPOPULARITYRANKREPLY_MSG.fields = {tb.GETSTREETPOPULARITYRANKREPLY_RANKLIST_FIELD}
GETSTREETPOPULARITYRANKREPLY_MSG.is_extendable = false
GETSTREETPOPULARITYRANKREPLY_MSG.extensions = {}
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.name = "userId"
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.full_name = ".GetShareStreetFurnitureInfoRequest.userId"
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.number = 1
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.index = 0
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.label = 1
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.has_default_value = false
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.default_value = ""
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.type = 9
tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD.cpp_type = 9

GETSHARESTREETFURNITUREINFOREQUEST_MSG.name = "GetShareStreetFurnitureInfoRequest"
GETSHARESTREETFURNITUREINFOREQUEST_MSG.full_name = ".GetShareStreetFurnitureInfoRequest"
GETSHARESTREETFURNITUREINFOREQUEST_MSG.filename = "ShareStreetExtension"
GETSHARESTREETFURNITUREINFOREQUEST_MSG.nested_types = {}
GETSHARESTREETFURNITUREINFOREQUEST_MSG.enum_types = {}
GETSHARESTREETFURNITUREINFOREQUEST_MSG.fields = {tb.GETSHARESTREETFURNITUREINFOREQUEST_USERID_FIELD}
GETSHARESTREETFURNITUREINFOREQUEST_MSG.is_extendable = false
GETSHARESTREETFURNITUREINFOREQUEST_MSG.extensions = {}
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.name = "streetId"
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.full_name = ".GetUserJoinedStreetIdReply.streetId"
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.number = 1
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.index = 0
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.label = 1
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.has_default_value = false
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.default_value = ""
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.type = 9
tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD.cpp_type = 9

GETUSERJOINEDSTREETIDREPLY_MSG.name = "GetUserJoinedStreetIdReply"
GETUSERJOINEDSTREETIDREPLY_MSG.full_name = ".GetUserJoinedStreetIdReply"
GETUSERJOINEDSTREETIDREPLY_MSG.filename = "ShareStreetExtension"
GETUSERJOINEDSTREETIDREPLY_MSG.nested_types = {}
GETUSERJOINEDSTREETIDREPLY_MSG.enum_types = {}
GETUSERJOINEDSTREETIDREPLY_MSG.fields = {tb.GETUSERJOINEDSTREETIDREPLY_STREETID_FIELD}
GETUSERJOINEDSTREETIDREPLY_MSG.is_extendable = false
GETUSERJOINEDSTREETIDREPLY_MSG.extensions = {}
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.name = "shareStreetInfo"
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.full_name = ".GetShareStreetInfoReply.shareStreetInfo"
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.number = 1
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.index = 0
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.label = 1
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.default_value = nil
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.message_type = SHARESTREETINFONO_MSG
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.type = 11
tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD.cpp_type = 10

tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.name = "like"
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.full_name = ".GetShareStreetInfoReply.like"
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.number = 2
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.index = 1
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.label = 1
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.default_value = false
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.type = 8
tb.GETSHARESTREETINFOREPLY_LIKE_FIELD.cpp_type = 7

tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.name = "hasInvited"
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.full_name = ".GetShareStreetInfoReply.hasInvited"
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.number = 3
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.index = 2
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.label = 1
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.default_value = false
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.type = 8
tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD.cpp_type = 7

tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.name = "hasApply"
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.full_name = ".GetShareStreetInfoReply.hasApply"
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.number = 4
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.index = 3
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.label = 1
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.default_value = false
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.type = 8
tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD.cpp_type = 7

tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.name = "mineStreetId"
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.full_name = ".GetShareStreetInfoReply.mineStreetId"
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.number = 5
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.index = 4
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.label = 1
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.default_value = ""
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.type = 9
tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD.cpp_type = 9

tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.name = "modifyNameCount"
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.full_name = ".GetShareStreetInfoReply.modifyNameCount"
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.number = 6
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.index = 5
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.label = 1
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.has_default_value = false
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.default_value = 0
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.type = 5
tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD.cpp_type = 1

GETSHARESTREETINFOREPLY_MSG.name = "GetShareStreetInfoReply"
GETSHARESTREETINFOREPLY_MSG.full_name = ".GetShareStreetInfoReply"
GETSHARESTREETINFOREPLY_MSG.filename = "ShareStreetExtension"
GETSHARESTREETINFOREPLY_MSG.nested_types = {}
GETSHARESTREETINFOREPLY_MSG.enum_types = {}
GETSHARESTREETINFOREPLY_MSG.fields = {tb.GETSHARESTREETINFOREPLY_SHARESTREETINFO_FIELD, tb.GETSHARESTREETINFOREPLY_LIKE_FIELD, tb.GETSHARESTREETINFOREPLY_HASINVITED_FIELD, tb.GETSHARESTREETINFOREPLY_HASAPPLY_FIELD, tb.GETSHARESTREETINFOREPLY_MINESTREETID_FIELD, tb.GETSHARESTREETINFOREPLY_MODIFYNAMECOUNT_FIELD}
GETSHARESTREETINFOREPLY_MSG.is_extendable = false
GETSHARESTREETINFOREPLY_MSG.extensions = {}
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.name = "roleInfo"
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.full_name = ".ShareStreetMemberNO.roleInfo"
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.number = 1
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.index = 0
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.label = 1
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.has_default_value = false
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.default_value = nil
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.type = 11
tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD.cpp_type = 10

tb.SHARESTREETMEMBERNO_AREAID_FIELD.name = "areaId"
tb.SHARESTREETMEMBERNO_AREAID_FIELD.full_name = ".ShareStreetMemberNO.areaId"
tb.SHARESTREETMEMBERNO_AREAID_FIELD.number = 2
tb.SHARESTREETMEMBERNO_AREAID_FIELD.index = 1
tb.SHARESTREETMEMBERNO_AREAID_FIELD.label = 1
tb.SHARESTREETMEMBERNO_AREAID_FIELD.has_default_value = false
tb.SHARESTREETMEMBERNO_AREAID_FIELD.default_value = 0
tb.SHARESTREETMEMBERNO_AREAID_FIELD.type = 5
tb.SHARESTREETMEMBERNO_AREAID_FIELD.cpp_type = 1

SHARESTREETMEMBERNO_MSG.name = "ShareStreetMemberNO"
SHARESTREETMEMBERNO_MSG.full_name = ".ShareStreetMemberNO"
SHARESTREETMEMBERNO_MSG.filename = "ShareStreetExtension"
SHARESTREETMEMBERNO_MSG.nested_types = {}
SHARESTREETMEMBERNO_MSG.enum_types = {}
SHARESTREETMEMBERNO_MSG.fields = {tb.SHARESTREETMEMBERNO_ROLEINFO_FIELD, tb.SHARESTREETMEMBERNO_AREAID_FIELD}
SHARESTREETMEMBERNO_MSG.is_extendable = false
SHARESTREETMEMBERNO_MSG.extensions = {}
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.name = "ownerId"
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.full_name = ".ApplyJoinShareStreetRequest.ownerId"
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.number = 1
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.index = 0
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.label = 1
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.has_default_value = false
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.default_value = ""
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.type = 9
tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD.cpp_type = 9

APPLYJOINSHARESTREETREQUEST_MSG.name = "ApplyJoinShareStreetRequest"
APPLYJOINSHARESTREETREQUEST_MSG.full_name = ".ApplyJoinShareStreetRequest"
APPLYJOINSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
APPLYJOINSHARESTREETREQUEST_MSG.nested_types = {}
APPLYJOINSHARESTREETREQUEST_MSG.enum_types = {}
APPLYJOINSHARESTREETREQUEST_MSG.fields = {tb.APPLYJOINSHARESTREETREQUEST_OWNERID_FIELD}
APPLYJOINSHARESTREETREQUEST_MSG.is_extendable = false
APPLYJOINSHARESTREETREQUEST_MSG.extensions = {}
tb.CREATESHARESTREETREQUEST_NAME_FIELD.name = "name"
tb.CREATESHARESTREETREQUEST_NAME_FIELD.full_name = ".CreateShareStreetRequest.name"
tb.CREATESHARESTREETREQUEST_NAME_FIELD.number = 1
tb.CREATESHARESTREETREQUEST_NAME_FIELD.index = 0
tb.CREATESHARESTREETREQUEST_NAME_FIELD.label = 1
tb.CREATESHARESTREETREQUEST_NAME_FIELD.has_default_value = false
tb.CREATESHARESTREETREQUEST_NAME_FIELD.default_value = ""
tb.CREATESHARESTREETREQUEST_NAME_FIELD.type = 9
tb.CREATESHARESTREETREQUEST_NAME_FIELD.cpp_type = 9

tb.CREATESHARESTREETREQUEST_DESC_FIELD.name = "desc"
tb.CREATESHARESTREETREQUEST_DESC_FIELD.full_name = ".CreateShareStreetRequest.desc"
tb.CREATESHARESTREETREQUEST_DESC_FIELD.number = 2
tb.CREATESHARESTREETREQUEST_DESC_FIELD.index = 1
tb.CREATESHARESTREETREQUEST_DESC_FIELD.label = 1
tb.CREATESHARESTREETREQUEST_DESC_FIELD.has_default_value = false
tb.CREATESHARESTREETREQUEST_DESC_FIELD.default_value = ""
tb.CREATESHARESTREETREQUEST_DESC_FIELD.type = 9
tb.CREATESHARESTREETREQUEST_DESC_FIELD.cpp_type = 9

CREATESHARESTREETREQUEST_MSG.name = "CreateShareStreetRequest"
CREATESHARESTREETREQUEST_MSG.full_name = ".CreateShareStreetRequest"
CREATESHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
CREATESHARESTREETREQUEST_MSG.nested_types = {}
CREATESHARESTREETREQUEST_MSG.enum_types = {}
CREATESHARESTREETREQUEST_MSG.fields = {tb.CREATESHARESTREETREQUEST_NAME_FIELD, tb.CREATESHARESTREETREQUEST_DESC_FIELD}
CREATESHARESTREETREQUEST_MSG.is_extendable = false
CREATESHARESTREETREQUEST_MSG.extensions = {}
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.name = "streetInfo"
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.full_name = ".SearchShareStreetReply.streetInfo"
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.number = 1
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.index = 0
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.label = 3
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.has_default_value = false
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.default_value = {}
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.type = 11
tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD.cpp_type = 10

SEARCHSHARESTREETREPLY_MSG.name = "SearchShareStreetReply"
SEARCHSHARESTREETREPLY_MSG.full_name = ".SearchShareStreetReply"
SEARCHSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
SEARCHSHARESTREETREPLY_MSG.nested_types = {}
SEARCHSHARESTREETREPLY_MSG.enum_types = {}
SEARCHSHARESTREETREPLY_MSG.fields = {tb.SEARCHSHARESTREETREPLY_STREETINFO_FIELD}
SEARCHSHARESTREETREPLY_MSG.is_extendable = false
SEARCHSHARESTREETREPLY_MSG.extensions = {}
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.name = "shareStreetInfo"
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.full_name = ".CreateShareStreetReply.shareStreetInfo"
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.number = 1
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.index = 0
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.label = 1
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.has_default_value = false
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.default_value = nil
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.message_type = SHARESTREETINFONO_MSG
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.type = 11
tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD.cpp_type = 10

CREATESHARESTREETREPLY_MSG.name = "CreateShareStreetReply"
CREATESHARESTREETREPLY_MSG.full_name = ".CreateShareStreetReply"
CREATESHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
CREATESHARESTREETREPLY_MSG.nested_types = {}
CREATESHARESTREETREPLY_MSG.enum_types = {}
CREATESHARESTREETREPLY_MSG.fields = {tb.CREATESHARESTREETREPLY_SHARESTREETINFO_FIELD}
CREATESHARESTREETREPLY_MSG.is_extendable = false
CREATESHARESTREETREPLY_MSG.extensions = {}
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.name = "areaId"
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.full_name = ".StreetFurnitureInfoChangePush.areaId"
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.number = 1
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.index = 0
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.label = 1
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.has_default_value = false
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.default_value = 0
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.type = 5
tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD.cpp_type = 1

tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.name = "userId"
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.full_name = ".StreetFurnitureInfoChangePush.userId"
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.number = 2
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.index = 1
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.label = 1
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.has_default_value = false
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.default_value = ""
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.type = 9
tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD.cpp_type = 9

tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.name = "changeItems"
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.full_name = ".StreetFurnitureInfoChangePush.changeItems"
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.number = 3
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.index = 2
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.label = 3
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.has_default_value = false
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.default_value = {}
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.type = 11
tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.cpp_type = 10

tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.name = "removeItems"
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.full_name = ".StreetFurnitureInfoChangePush.removeItems"
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.number = 4
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.index = 3
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.label = 3
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.has_default_value = false
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.default_value = {}
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.type = 5
tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD.cpp_type = 1

tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.name = "houseInfo"
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.full_name = ".StreetFurnitureInfoChangePush.houseInfo"
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.number = 5
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.index = 4
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.label = 3
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.has_default_value = false
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.default_value = {}
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.message_type = HOUSEEXTENSION_PB.HOUSESHOWAREAINFO_MSG
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.type = 11
tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.cpp_type = 10

tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.name = "sceneItems"
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.full_name = ".StreetFurnitureInfoChangePush.sceneItems"
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.number = 6
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.index = 5
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.label = 3
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.has_default_value = false
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.default_value = {}
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.message_type = SHARESTREETSCENEITEMNO_MSG
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.type = 11
tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD.cpp_type = 10

STREETFURNITUREINFOCHANGEPUSH_MSG.name = "StreetFurnitureInfoChangePush"
STREETFURNITUREINFOCHANGEPUSH_MSG.full_name = ".StreetFurnitureInfoChangePush"
STREETFURNITUREINFOCHANGEPUSH_MSG.filename = "ShareStreetExtension"
STREETFURNITUREINFOCHANGEPUSH_MSG.nested_types = {}
STREETFURNITUREINFOCHANGEPUSH_MSG.enum_types = {}
STREETFURNITUREINFOCHANGEPUSH_MSG.fields = {tb.STREETFURNITUREINFOCHANGEPUSH_AREAID_FIELD, tb.STREETFURNITUREINFOCHANGEPUSH_USERID_FIELD, tb.STREETFURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD, tb.STREETFURNITUREINFOCHANGEPUSH_REMOVEITEMS_FIELD, tb.STREETFURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD, tb.STREETFURNITUREINFOCHANGEPUSH_SCENEITEMS_FIELD}
STREETFURNITUREINFOCHANGEPUSH_MSG.is_extendable = false
STREETFURNITUREINFOCHANGEPUSH_MSG.extensions = {}
QUITSHARESTREETREQUEST_MSG.name = "QuitShareStreetRequest"
QUITSHARESTREETREQUEST_MSG.full_name = ".QuitShareStreetRequest"
QUITSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
QUITSHARESTREETREQUEST_MSG.nested_types = {}
QUITSHARESTREETREQUEST_MSG.enum_types = {}
QUITSHARESTREETREQUEST_MSG.fields = {}
QUITSHARESTREETREQUEST_MSG.is_extendable = false
QUITSHARESTREETREQUEST_MSG.extensions = {}
GETSTREETRECEIVEINVITELISTREQUEST_MSG.name = "GetStreetReceiveInviteListRequest"
GETSTREETRECEIVEINVITELISTREQUEST_MSG.full_name = ".GetStreetReceiveInviteListRequest"
GETSTREETRECEIVEINVITELISTREQUEST_MSG.filename = "ShareStreetExtension"
GETSTREETRECEIVEINVITELISTREQUEST_MSG.nested_types = {}
GETSTREETRECEIVEINVITELISTREQUEST_MSG.enum_types = {}
GETSTREETRECEIVEINVITELISTREQUEST_MSG.fields = {}
GETSTREETRECEIVEINVITELISTREQUEST_MSG.is_extendable = false
GETSTREETRECEIVEINVITELISTREQUEST_MSG.extensions = {}
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.name = "inviteList"
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.full_name = ".GetStreetReceiveInviteListReply.inviteList"
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.number = 1
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.index = 0
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.label = 3
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.has_default_value = false
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.default_value = {}
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.type = 11
tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD.cpp_type = 10

GETSTREETRECEIVEINVITELISTREPLY_MSG.name = "GetStreetReceiveInviteListReply"
GETSTREETRECEIVEINVITELISTREPLY_MSG.full_name = ".GetStreetReceiveInviteListReply"
GETSTREETRECEIVEINVITELISTREPLY_MSG.filename = "ShareStreetExtension"
GETSTREETRECEIVEINVITELISTREPLY_MSG.nested_types = {}
GETSTREETRECEIVEINVITELISTREPLY_MSG.enum_types = {}
GETSTREETRECEIVEINVITELISTREPLY_MSG.fields = {tb.GETSTREETRECEIVEINVITELISTREPLY_INVITELIST_FIELD}
GETSTREETRECEIVEINVITELISTREPLY_MSG.is_extendable = false
GETSTREETRECEIVEINVITELISTREPLY_MSG.extensions = {}
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.name = "furnitureCount"
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.full_name = ".StreetShareFurnitureRequest.furnitureCount"
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.number = 1
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.index = 0
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.label = 3
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.has_default_value = false
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.default_value = {}
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.message_type = FURNITURECOUNTNO_MSG
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.type = 11
tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD.cpp_type = 10

STREETSHAREFURNITUREREQUEST_MSG.name = "StreetShareFurnitureRequest"
STREETSHAREFURNITUREREQUEST_MSG.full_name = ".StreetShareFurnitureRequest"
STREETSHAREFURNITUREREQUEST_MSG.filename = "ShareStreetExtension"
STREETSHAREFURNITUREREQUEST_MSG.nested_types = {}
STREETSHAREFURNITUREREQUEST_MSG.enum_types = {}
STREETSHAREFURNITUREREQUEST_MSG.fields = {tb.STREETSHAREFURNITUREREQUEST_FURNITURECOUNT_FIELD}
STREETSHAREFURNITUREREQUEST_MSG.is_extendable = false
STREETSHAREFURNITUREREQUEST_MSG.extensions = {}
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.name = "houseId"
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.full_name = ".ChangeShareStreetHouseRequest.houseId"
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.number = 1
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.index = 0
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.label = 1
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.has_default_value = false
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.default_value = 0
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.type = 5
tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD.cpp_type = 1

CHANGESHARESTREETHOUSEREQUEST_MSG.name = "ChangeShareStreetHouseRequest"
CHANGESHARESTREETHOUSEREQUEST_MSG.full_name = ".ChangeShareStreetHouseRequest"
CHANGESHARESTREETHOUSEREQUEST_MSG.filename = "ShareStreetExtension"
CHANGESHARESTREETHOUSEREQUEST_MSG.nested_types = {}
CHANGESHARESTREETHOUSEREQUEST_MSG.enum_types = {}
CHANGESHARESTREETHOUSEREQUEST_MSG.fields = {tb.CHANGESHARESTREETHOUSEREQUEST_HOUSEID_FIELD}
CHANGESHARESTREETHOUSEREQUEST_MSG.is_extendable = false
CHANGESHARESTREETHOUSEREQUEST_MSG.extensions = {}
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.name = "userId"
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.full_name = ".GetUserJoinedStreetIdRequest.userId"
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.number = 1
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.index = 0
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.label = 1
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.has_default_value = false
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.default_value = ""
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.type = 9
tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD.cpp_type = 9

GETUSERJOINEDSTREETIDREQUEST_MSG.name = "GetUserJoinedStreetIdRequest"
GETUSERJOINEDSTREETIDREQUEST_MSG.full_name = ".GetUserJoinedStreetIdRequest"
GETUSERJOINEDSTREETIDREQUEST_MSG.filename = "ShareStreetExtension"
GETUSERJOINEDSTREETIDREQUEST_MSG.nested_types = {}
GETUSERJOINEDSTREETIDREQUEST_MSG.enum_types = {}
GETUSERJOINEDSTREETIDREQUEST_MSG.fields = {tb.GETUSERJOINEDSTREETIDREQUEST_USERID_FIELD}
GETUSERJOINEDSTREETIDREQUEST_MSG.is_extendable = false
GETUSERJOINEDSTREETIDREQUEST_MSG.extensions = {}
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.name = "shareStreetInfo"
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.full_name = ".QuitShareStreetReply.shareStreetInfo"
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.number = 1
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.index = 0
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.label = 1
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.has_default_value = false
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.default_value = nil
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.message_type = SHARESTREETINFONO_MSG
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.type = 11
tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD.cpp_type = 10

QUITSHARESTREETREPLY_MSG.name = "QuitShareStreetReply"
QUITSHARESTREETREPLY_MSG.full_name = ".QuitShareStreetReply"
QUITSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
QUITSHARESTREETREPLY_MSG.nested_types = {}
QUITSHARESTREETREPLY_MSG.enum_types = {}
QUITSHARESTREETREPLY_MSG.fields = {tb.QUITSHARESTREETREPLY_SHARESTREETINFO_FIELD}
QUITSHARESTREETREPLY_MSG.is_extendable = false
QUITSHARESTREETREPLY_MSG.extensions = {}
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.name = "GetPersonShareFurnitureCountRequest"
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.full_name = ".GetPersonShareFurnitureCountRequest"
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.filename = "ShareStreetExtension"
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.nested_types = {}
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.enum_types = {}
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.fields = {}
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.is_extendable = false
GETPERSONSHAREFURNITURECOUNTREQUEST_MSG.extensions = {}
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.name = "furnitureCount"
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.full_name = ".GetPersonShareFurnitureCountReply.furnitureCount"
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.number = 1
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.index = 0
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.label = 3
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.has_default_value = false
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.default_value = {}
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.message_type = FURNITURECOUNTNO_MSG
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.type = 11
tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD.cpp_type = 10

GETPERSONSHAREFURNITURECOUNTREPLY_MSG.name = "GetPersonShareFurnitureCountReply"
GETPERSONSHAREFURNITURECOUNTREPLY_MSG.full_name = ".GetPersonShareFurnitureCountReply"
GETPERSONSHAREFURNITURECOUNTREPLY_MSG.filename = "ShareStreetExtension"
GETPERSONSHAREFURNITURECOUNTREPLY_MSG.nested_types = {}
GETPERSONSHAREFURNITURECOUNTREPLY_MSG.enum_types = {}
GETPERSONSHAREFURNITURECOUNTREPLY_MSG.fields = {tb.GETPERSONSHAREFURNITURECOUNTREPLY_FURNITURECOUNT_FIELD}
GETPERSONSHAREFURNITURECOUNTREPLY_MSG.is_extendable = false
GETPERSONSHAREFURNITURECOUNTREPLY_MSG.extensions = {}
STREETUPDATEINFOREPLY_MSG.name = "StreetUpdateInfoReply"
STREETUPDATEINFOREPLY_MSG.full_name = ".StreetUpdateInfoReply"
STREETUPDATEINFOREPLY_MSG.filename = "ShareStreetExtension"
STREETUPDATEINFOREPLY_MSG.nested_types = {}
STREETUPDATEINFOREPLY_MSG.enum_types = {}
STREETUPDATEINFOREPLY_MSG.fields = {}
STREETUPDATEINFOREPLY_MSG.is_extendable = false
STREETUPDATEINFOREPLY_MSG.extensions = {}
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.name = "friendInfo"
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.full_name = ".FriendStreetInfoNO.friendInfo"
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.number = 1
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.index = 0
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.label = 1
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.default_value = nil
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.type = 11
tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD.cpp_type = 10

tb.FRIENDSTREETINFONO_HASSTREET_FIELD.name = "hasStreet"
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.full_name = ".FriendStreetInfoNO.hasStreet"
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.number = 2
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.index = 1
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.label = 1
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.default_value = false
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.type = 8
tb.FRIENDSTREETINFONO_HASSTREET_FIELD.cpp_type = 7

tb.FRIENDSTREETINFONO_NAME_FIELD.name = "name"
tb.FRIENDSTREETINFONO_NAME_FIELD.full_name = ".FriendStreetInfoNO.name"
tb.FRIENDSTREETINFONO_NAME_FIELD.number = 3
tb.FRIENDSTREETINFONO_NAME_FIELD.index = 2
tb.FRIENDSTREETINFONO_NAME_FIELD.label = 1
tb.FRIENDSTREETINFONO_NAME_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_NAME_FIELD.default_value = ""
tb.FRIENDSTREETINFONO_NAME_FIELD.type = 9
tb.FRIENDSTREETINFONO_NAME_FIELD.cpp_type = 9

tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.name = "areaCount"
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.full_name = ".FriendStreetInfoNO.areaCount"
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.number = 4
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.index = 3
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.label = 1
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.default_value = 0
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.type = 5
tb.FRIENDSTREETINFONO_AREACOUNT_FIELD.cpp_type = 1

tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.name = "memberCount"
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.full_name = ".FriendStreetInfoNO.memberCount"
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.number = 5
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.index = 4
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.label = 1
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.default_value = 0
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.type = 5
tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD.cpp_type = 1

tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.name = "likeCount"
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.full_name = ".FriendStreetInfoNO.likeCount"
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.number = 6
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.index = 5
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.label = 1
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.default_value = 0
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.type = 5
tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD.cpp_type = 1

tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.name = "visitCount"
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.full_name = ".FriendStreetInfoNO.visitCount"
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.number = 7
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.index = 6
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.label = 1
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.default_value = 0
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.type = 5
tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD.cpp_type = 1

tb.FRIENDSTREETINFONO_ISINVITED_FIELD.name = "isInvited"
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.full_name = ".FriendStreetInfoNO.isInvited"
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.number = 8
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.index = 7
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.label = 1
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.default_value = false
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.type = 8
tb.FRIENDSTREETINFONO_ISINVITED_FIELD.cpp_type = 7

tb.FRIENDSTREETINFONO_PICTURE_FIELD.name = "picture"
tb.FRIENDSTREETINFONO_PICTURE_FIELD.full_name = ".FriendStreetInfoNO.picture"
tb.FRIENDSTREETINFONO_PICTURE_FIELD.number = 9
tb.FRIENDSTREETINFONO_PICTURE_FIELD.index = 8
tb.FRIENDSTREETINFONO_PICTURE_FIELD.label = 1
tb.FRIENDSTREETINFONO_PICTURE_FIELD.has_default_value = false
tb.FRIENDSTREETINFONO_PICTURE_FIELD.default_value = ""
tb.FRIENDSTREETINFONO_PICTURE_FIELD.type = 9
tb.FRIENDSTREETINFONO_PICTURE_FIELD.cpp_type = 9

FRIENDSTREETINFONO_MSG.name = "FriendStreetInfoNO"
FRIENDSTREETINFONO_MSG.full_name = ".FriendStreetInfoNO"
FRIENDSTREETINFONO_MSG.filename = "ShareStreetExtension"
FRIENDSTREETINFONO_MSG.nested_types = {}
FRIENDSTREETINFONO_MSG.enum_types = {}
FRIENDSTREETINFONO_MSG.fields = {tb.FRIENDSTREETINFONO_FRIENDINFO_FIELD, tb.FRIENDSTREETINFONO_HASSTREET_FIELD, tb.FRIENDSTREETINFONO_NAME_FIELD, tb.FRIENDSTREETINFONO_AREACOUNT_FIELD, tb.FRIENDSTREETINFONO_MEMBERCOUNT_FIELD, tb.FRIENDSTREETINFONO_LIKECOUNT_FIELD, tb.FRIENDSTREETINFONO_VISITCOUNT_FIELD, tb.FRIENDSTREETINFONO_ISINVITED_FIELD, tb.FRIENDSTREETINFONO_PICTURE_FIELD}
FRIENDSTREETINFONO_MSG.is_extendable = false
FRIENDSTREETINFONO_MSG.extensions = {}
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.full_name = ".DecorateShareStreetRequest.decoratedItems"
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.number = 1
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.index = 0
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.type = 11
tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.name = "sellingItems"
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.full_name = ".DecorateShareStreetRequest.sellingItems"
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.number = 2
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.index = 1
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.type = 5
tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD.cpp_type = 1

tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.name = "buyingItems"
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.full_name = ".DecorateShareStreetRequest.buyingItems"
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.number = 3
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.index = 2
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.type = 5
tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD.cpp_type = 1

tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.name = "takeBackFurniture"
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.full_name = ".DecorateShareStreetRequest.takeBackFurniture"
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.number = 4
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.index = 3
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.label = 3
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.default_value = {}
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.type = 5
tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD.cpp_type = 1

tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.name = "terrains"
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.full_name = ".DecorateShareStreetRequest.terrains"
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.number = 5
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.index = 4
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.label = 3
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.default_value = {}
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.message_type = ISLANDEXTENSION_PB.TERRAININFONO_MSG
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.type = 11
tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.name = "decorateType"
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.full_name = ".DecorateShareStreetRequest.decorateType"
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.number = 7
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.index = 5
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.label = 1
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.default_value = 0
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.type = 5
tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD.cpp_type = 1

tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.name = "itemLockPassword"
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.full_name = ".DecorateShareStreetRequest.itemLockPassword"
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.number = 8
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.index = 6
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.label = 1
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.default_value = ""
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.type = 9
tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD.cpp_type = 9

tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.name = "publicItems"
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.full_name = ".DecorateShareStreetRequest.publicItems"
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.number = 9
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.index = 7
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.message_type = SHARESTREETSCENEITEMNO_MSG
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.type = 11
tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.name = "personalItems"
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.full_name = ".DecorateShareStreetRequest.personalItems"
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.number = 10
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.index = 8
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.message_type = SHARESTREETSCENEITEMNO_MSG
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.type = 11
tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD.cpp_type = 10

DECORATESHARESTREETREQUEST_MSG.name = "DecorateShareStreetRequest"
DECORATESHARESTREETREQUEST_MSG.full_name = ".DecorateShareStreetRequest"
DECORATESHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
DECORATESHARESTREETREQUEST_MSG.nested_types = {}
DECORATESHARESTREETREQUEST_MSG.enum_types = {}
DECORATESHARESTREETREQUEST_MSG.fields = {tb.DECORATESHARESTREETREQUEST_DECORATEDITEMS_FIELD, tb.DECORATESHARESTREETREQUEST_SELLINGITEMS_FIELD, tb.DECORATESHARESTREETREQUEST_BUYINGITEMS_FIELD, tb.DECORATESHARESTREETREQUEST_TAKEBACKFURNITURE_FIELD, tb.DECORATESHARESTREETREQUEST_TERRAINS_FIELD, tb.DECORATESHARESTREETREQUEST_DECORATETYPE_FIELD, tb.DECORATESHARESTREETREQUEST_ITEMLOCKPASSWORD_FIELD, tb.DECORATESHARESTREETREQUEST_PUBLICITEMS_FIELD, tb.DECORATESHARESTREETREQUEST_PERSONALITEMS_FIELD}
DECORATESHARESTREETREQUEST_MSG.is_extendable = false
DECORATESHARESTREETREQUEST_MSG.extensions = {}
tb.SHARESTREETINFONO_OWNERID_FIELD.name = "ownerId"
tb.SHARESTREETINFONO_OWNERID_FIELD.full_name = ".ShareStreetInfoNO.ownerId"
tb.SHARESTREETINFONO_OWNERID_FIELD.number = 1
tb.SHARESTREETINFONO_OWNERID_FIELD.index = 0
tb.SHARESTREETINFONO_OWNERID_FIELD.label = 1
tb.SHARESTREETINFONO_OWNERID_FIELD.has_default_value = false
tb.SHARESTREETINFONO_OWNERID_FIELD.default_value = ""
tb.SHARESTREETINFONO_OWNERID_FIELD.type = 9
tb.SHARESTREETINFONO_OWNERID_FIELD.cpp_type = 9

tb.SHARESTREETINFONO_NAME_FIELD.name = "name"
tb.SHARESTREETINFONO_NAME_FIELD.full_name = ".ShareStreetInfoNO.name"
tb.SHARESTREETINFONO_NAME_FIELD.number = 2
tb.SHARESTREETINFONO_NAME_FIELD.index = 1
tb.SHARESTREETINFONO_NAME_FIELD.label = 1
tb.SHARESTREETINFONO_NAME_FIELD.has_default_value = false
tb.SHARESTREETINFONO_NAME_FIELD.default_value = ""
tb.SHARESTREETINFONO_NAME_FIELD.type = 9
tb.SHARESTREETINFONO_NAME_FIELD.cpp_type = 9

tb.SHARESTREETINFONO_DESC_FIELD.name = "desc"
tb.SHARESTREETINFONO_DESC_FIELD.full_name = ".ShareStreetInfoNO.desc"
tb.SHARESTREETINFONO_DESC_FIELD.number = 3
tb.SHARESTREETINFONO_DESC_FIELD.index = 2
tb.SHARESTREETINFONO_DESC_FIELD.label = 1
tb.SHARESTREETINFONO_DESC_FIELD.has_default_value = false
tb.SHARESTREETINFONO_DESC_FIELD.default_value = ""
tb.SHARESTREETINFONO_DESC_FIELD.type = 9
tb.SHARESTREETINFONO_DESC_FIELD.cpp_type = 9

tb.SHARESTREETINFONO_MEMBERS_FIELD.name = "members"
tb.SHARESTREETINFONO_MEMBERS_FIELD.full_name = ".ShareStreetInfoNO.members"
tb.SHARESTREETINFONO_MEMBERS_FIELD.number = 4
tb.SHARESTREETINFONO_MEMBERS_FIELD.index = 3
tb.SHARESTREETINFONO_MEMBERS_FIELD.label = 3
tb.SHARESTREETINFONO_MEMBERS_FIELD.has_default_value = false
tb.SHARESTREETINFONO_MEMBERS_FIELD.default_value = {}
tb.SHARESTREETINFONO_MEMBERS_FIELD.message_type = SHARESTREETMEMBERNO_MSG
tb.SHARESTREETINFONO_MEMBERS_FIELD.type = 11
tb.SHARESTREETINFONO_MEMBERS_FIELD.cpp_type = 10

tb.SHARESTREETINFONO_AREAID_FIELD.name = "areaId"
tb.SHARESTREETINFONO_AREAID_FIELD.full_name = ".ShareStreetInfoNO.areaId"
tb.SHARESTREETINFONO_AREAID_FIELD.number = 5
tb.SHARESTREETINFONO_AREAID_FIELD.index = 4
tb.SHARESTREETINFONO_AREAID_FIELD.label = 3
tb.SHARESTREETINFONO_AREAID_FIELD.has_default_value = false
tb.SHARESTREETINFONO_AREAID_FIELD.default_value = {}
tb.SHARESTREETINFONO_AREAID_FIELD.type = 5
tb.SHARESTREETINFONO_AREAID_FIELD.cpp_type = 1

tb.SHARESTREETINFONO_PICTURE_FIELD.name = "picture"
tb.SHARESTREETINFONO_PICTURE_FIELD.full_name = ".ShareStreetInfoNO.picture"
tb.SHARESTREETINFONO_PICTURE_FIELD.number = 6
tb.SHARESTREETINFONO_PICTURE_FIELD.index = 5
tb.SHARESTREETINFONO_PICTURE_FIELD.label = 1
tb.SHARESTREETINFONO_PICTURE_FIELD.has_default_value = false
tb.SHARESTREETINFONO_PICTURE_FIELD.default_value = ""
tb.SHARESTREETINFONO_PICTURE_FIELD.type = 9
tb.SHARESTREETINFONO_PICTURE_FIELD.cpp_type = 9

tb.SHARESTREETINFONO_LIKECOUNT_FIELD.name = "likeCount"
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.full_name = ".ShareStreetInfoNO.likeCount"
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.number = 7
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.index = 6
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.label = 1
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.has_default_value = false
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.default_value = 0
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.type = 5
tb.SHARESTREETINFONO_LIKECOUNT_FIELD.cpp_type = 1

tb.SHARESTREETINFONO_VISITCOUNT_FIELD.name = "visitCount"
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.full_name = ".ShareStreetInfoNO.visitCount"
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.number = 8
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.index = 7
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.label = 1
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.has_default_value = false
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.default_value = 0
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.type = 5
tb.SHARESTREETINFONO_VISITCOUNT_FIELD.cpp_type = 1

tb.SHARESTREETINFONO_DISPLAYID_FIELD.name = "displayId"
tb.SHARESTREETINFONO_DISPLAYID_FIELD.full_name = ".ShareStreetInfoNO.displayId"
tb.SHARESTREETINFONO_DISPLAYID_FIELD.number = 9
tb.SHARESTREETINFONO_DISPLAYID_FIELD.index = 8
tb.SHARESTREETINFONO_DISPLAYID_FIELD.label = 1
tb.SHARESTREETINFONO_DISPLAYID_FIELD.has_default_value = false
tb.SHARESTREETINFONO_DISPLAYID_FIELD.default_value = ""
tb.SHARESTREETINFONO_DISPLAYID_FIELD.type = 9
tb.SHARESTREETINFONO_DISPLAYID_FIELD.cpp_type = 9

SHARESTREETINFONO_MSG.name = "ShareStreetInfoNO"
SHARESTREETINFONO_MSG.full_name = ".ShareStreetInfoNO"
SHARESTREETINFONO_MSG.filename = "ShareStreetExtension"
SHARESTREETINFONO_MSG.nested_types = {}
SHARESTREETINFONO_MSG.enum_types = {}
SHARESTREETINFONO_MSG.fields = {tb.SHARESTREETINFONO_OWNERID_FIELD, tb.SHARESTREETINFONO_NAME_FIELD, tb.SHARESTREETINFONO_DESC_FIELD, tb.SHARESTREETINFONO_MEMBERS_FIELD, tb.SHARESTREETINFONO_AREAID_FIELD, tb.SHARESTREETINFONO_PICTURE_FIELD, tb.SHARESTREETINFONO_LIKECOUNT_FIELD, tb.SHARESTREETINFONO_VISITCOUNT_FIELD, tb.SHARESTREETINFONO_DISPLAYID_FIELD}
SHARESTREETINFONO_MSG.is_extendable = false
SHARESTREETINFONO_MSG.extensions = {}
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.name = "applyList"
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.full_name = ".GetStreetApplyListReply.applyList"
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.number = 1
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.index = 0
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.label = 3
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.has_default_value = false
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.default_value = {}
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.message_type = STREETSIMPLEINFONO_MSG
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.type = 11
tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD.cpp_type = 10

GETSTREETAPPLYLISTREPLY_MSG.name = "GetStreetApplyListReply"
GETSTREETAPPLYLISTREPLY_MSG.full_name = ".GetStreetApplyListReply"
GETSTREETAPPLYLISTREPLY_MSG.filename = "ShareStreetExtension"
GETSTREETAPPLYLISTREPLY_MSG.nested_types = {}
GETSTREETAPPLYLISTREPLY_MSG.enum_types = {}
GETSTREETAPPLYLISTREPLY_MSG.fields = {tb.GETSTREETAPPLYLISTREPLY_APPLYLIST_FIELD}
GETSTREETAPPLYLISTREPLY_MSG.is_extendable = false
GETSTREETAPPLYLISTREPLY_MSG.extensions = {}
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.name = "streetOwner"
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.full_name = ".StreetSimpleInfoNO.streetOwner"
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.number = 1
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.index = 0
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.label = 1
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.default_value = nil
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.type = 11
tb.STREETSIMPLEINFONO_STREETOWNER_FIELD.cpp_type = 10

tb.STREETSIMPLEINFONO_NAME_FIELD.name = "name"
tb.STREETSIMPLEINFONO_NAME_FIELD.full_name = ".StreetSimpleInfoNO.name"
tb.STREETSIMPLEINFONO_NAME_FIELD.number = 2
tb.STREETSIMPLEINFONO_NAME_FIELD.index = 1
tb.STREETSIMPLEINFONO_NAME_FIELD.label = 1
tb.STREETSIMPLEINFONO_NAME_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_NAME_FIELD.default_value = ""
tb.STREETSIMPLEINFONO_NAME_FIELD.type = 9
tb.STREETSIMPLEINFONO_NAME_FIELD.cpp_type = 9

tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.name = "areaCount"
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.full_name = ".StreetSimpleInfoNO.areaCount"
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.number = 3
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.index = 2
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.label = 1
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.default_value = 0
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.type = 5
tb.STREETSIMPLEINFONO_AREACOUNT_FIELD.cpp_type = 1

tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.name = "memberCount"
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.full_name = ".StreetSimpleInfoNO.memberCount"
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.number = 4
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.index = 3
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.label = 1
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.default_value = 0
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.type = 5
tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD.cpp_type = 1

tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.name = "likeCount"
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.full_name = ".StreetSimpleInfoNO.likeCount"
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.number = 5
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.index = 4
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.label = 1
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.default_value = 0
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.type = 5
tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD.cpp_type = 1

tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.name = "visitCount"
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.full_name = ".StreetSimpleInfoNO.visitCount"
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.number = 6
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.index = 5
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.label = 1
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.default_value = 0
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.type = 5
tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD.cpp_type = 1

tb.STREETSIMPLEINFONO_PICTURE_FIELD.name = "picture"
tb.STREETSIMPLEINFONO_PICTURE_FIELD.full_name = ".StreetSimpleInfoNO.picture"
tb.STREETSIMPLEINFONO_PICTURE_FIELD.number = 7
tb.STREETSIMPLEINFONO_PICTURE_FIELD.index = 6
tb.STREETSIMPLEINFONO_PICTURE_FIELD.label = 1
tb.STREETSIMPLEINFONO_PICTURE_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_PICTURE_FIELD.default_value = ""
tb.STREETSIMPLEINFONO_PICTURE_FIELD.type = 9
tb.STREETSIMPLEINFONO_PICTURE_FIELD.cpp_type = 9

tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.name = "displayId"
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.full_name = ".StreetSimpleInfoNO.displayId"
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.number = 8
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.index = 7
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.label = 1
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.has_default_value = false
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.default_value = ""
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.type = 9
tb.STREETSIMPLEINFONO_DISPLAYID_FIELD.cpp_type = 9

STREETSIMPLEINFONO_MSG.name = "StreetSimpleInfoNO"
STREETSIMPLEINFONO_MSG.full_name = ".StreetSimpleInfoNO"
STREETSIMPLEINFONO_MSG.filename = "ShareStreetExtension"
STREETSIMPLEINFONO_MSG.nested_types = {}
STREETSIMPLEINFONO_MSG.enum_types = {}
STREETSIMPLEINFONO_MSG.fields = {tb.STREETSIMPLEINFONO_STREETOWNER_FIELD, tb.STREETSIMPLEINFONO_NAME_FIELD, tb.STREETSIMPLEINFONO_AREACOUNT_FIELD, tb.STREETSIMPLEINFONO_MEMBERCOUNT_FIELD, tb.STREETSIMPLEINFONO_LIKECOUNT_FIELD, tb.STREETSIMPLEINFONO_VISITCOUNT_FIELD, tb.STREETSIMPLEINFONO_PICTURE_FIELD, tb.STREETSIMPLEINFONO_DISPLAYID_FIELD}
STREETSIMPLEINFONO_MSG.is_extendable = false
STREETSIMPLEINFONO_MSG.extensions = {}
GETSTREETRECEIVEASKLISTREQUEST_MSG.name = "GetStreetReceiveAskListRequest"
GETSTREETRECEIVEASKLISTREQUEST_MSG.full_name = ".GetStreetReceiveAskListRequest"
GETSTREETRECEIVEASKLISTREQUEST_MSG.filename = "ShareStreetExtension"
GETSTREETRECEIVEASKLISTREQUEST_MSG.nested_types = {}
GETSTREETRECEIVEASKLISTREQUEST_MSG.enum_types = {}
GETSTREETRECEIVEASKLISTREQUEST_MSG.fields = {}
GETSTREETRECEIVEASKLISTREQUEST_MSG.is_extendable = false
GETSTREETRECEIVEASKLISTREQUEST_MSG.extensions = {}
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.name = "shareStreetInfo"
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.full_name = ".KickOutShareStreetReply.shareStreetInfo"
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.number = 1
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.index = 0
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.label = 1
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.has_default_value = false
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.default_value = nil
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.message_type = SHARESTREETINFONO_MSG
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.type = 11
tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD.cpp_type = 10

KICKOUTSHARESTREETREPLY_MSG.name = "KickOutShareStreetReply"
KICKOUTSHARESTREETREPLY_MSG.full_name = ".KickOutShareStreetReply"
KICKOUTSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
KICKOUTSHARESTREETREPLY_MSG.nested_types = {}
KICKOUTSHARESTREETREPLY_MSG.enum_types = {}
KICKOUTSHARESTREETREPLY_MSG.fields = {tb.KICKOUTSHARESTREETREPLY_SHARESTREETINFO_FIELD}
KICKOUTSHARESTREETREPLY_MSG.is_extendable = false
KICKOUTSHARESTREETREPLY_MSG.extensions = {}
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.name = "kickUserId"
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.full_name = ".KickOutShareStreetRequest.kickUserId"
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.number = 1
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.index = 0
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.label = 1
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.has_default_value = false
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.default_value = ""
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.type = 9
tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD.cpp_type = 9

KICKOUTSHARESTREETREQUEST_MSG.name = "KickOutShareStreetRequest"
KICKOUTSHARESTREETREQUEST_MSG.full_name = ".KickOutShareStreetRequest"
KICKOUTSHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
KICKOUTSHARESTREETREQUEST_MSG.nested_types = {}
KICKOUTSHARESTREETREQUEST_MSG.enum_types = {}
KICKOUTSHARESTREETREQUEST_MSG.fields = {tb.KICKOUTSHARESTREETREQUEST_KICKUSERID_FIELD}
KICKOUTSHARESTREETREQUEST_MSG.is_extendable = false
KICKOUTSHARESTREETREQUEST_MSG.extensions = {}
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.name = "streetId"
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.full_name = ".LikeShareStreetRequest.streetId"
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.number = 1
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.index = 0
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.label = 1
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.has_default_value = false
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.default_value = ""
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.type = 9
tb.LIKESHARESTREETREQUEST_STREETID_FIELD.cpp_type = 9

LIKESHARESTREETREQUEST_MSG.name = "LikeShareStreetRequest"
LIKESHARESTREETREQUEST_MSG.full_name = ".LikeShareStreetRequest"
LIKESHARESTREETREQUEST_MSG.filename = "ShareStreetExtension"
LIKESHARESTREETREQUEST_MSG.nested_types = {}
LIKESHARESTREETREQUEST_MSG.enum_types = {}
LIKESHARESTREETREQUEST_MSG.fields = {tb.LIKESHARESTREETREQUEST_STREETID_FIELD}
LIKESHARESTREETREQUEST_MSG.is_extendable = false
LIKESHARESTREETREQUEST_MSG.extensions = {}
APPLYJOINSHARESTREETREPLY_MSG.name = "ApplyJoinShareStreetReply"
APPLYJOINSHARESTREETREPLY_MSG.full_name = ".ApplyJoinShareStreetReply"
APPLYJOINSHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
APPLYJOINSHARESTREETREPLY_MSG.nested_types = {}
APPLYJOINSHARESTREETREPLY_MSG.enum_types = {}
APPLYJOINSHARESTREETREPLY_MSG.fields = {}
APPLYJOINSHARESTREETREPLY_MSG.is_extendable = false
APPLYJOINSHARESTREETREPLY_MSG.extensions = {}
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.full_name = ".DecorateShareStreetReply.decoratedItems"
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.number = 1
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.index = 0
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.type = 11
tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREPLY_CROPS_FIELD.name = "crops"
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.full_name = ".DecorateShareStreetReply.crops"
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.number = 2
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.index = 1
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.label = 3
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.default_value = {}
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.message_type = ISLANDEXTENSION_PB.MANORCROPNO_MSG
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.type = 11
tb.DECORATESHARESTREETREPLY_CROPS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.name = "friendTrees"
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.full_name = ".DecorateShareStreetReply.friendTrees"
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.number = 3
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.index = 2
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.label = 3
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.default_value = {}
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.message_type = ISLANDEXTENSION_PB.FRIENDTREENO_MSG
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.type = 11
tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.full_name = ".DecorateShareStreetReply.foodItemInfos"
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.number = 4
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.index = 3
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.label = 3
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.default_value = {}
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.type = 11
tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.name = "terrains"
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.full_name = ".DecorateShareStreetReply.terrains"
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.number = 5
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.index = 4
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.label = 3
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.default_value = {}
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.message_type = ISLANDEXTENSION_PB.TERRAININFONO_MSG
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.type = 11
tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.name = "hasDeleteShare"
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.full_name = ".DecorateShareStreetReply.hasDeleteShare"
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.number = 6
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.index = 5
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.label = 1
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.default_value = false
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.type = 8
tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD.cpp_type = 7

tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.name = "addShareState"
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.full_name = ".DecorateShareStreetReply.addShareState"
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.number = 7
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.index = 6
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.label = 1
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.default_value = 0
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.type = 5
tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD.cpp_type = 1

tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.name = "publicItems"
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.full_name = ".DecorateShareStreetReply.publicItems"
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.number = 8
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.index = 7
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.message_type = SHARESTREETSCENEITEMNO_MSG
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.type = 11
tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD.cpp_type = 10

tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.name = "personalItems"
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.full_name = ".DecorateShareStreetReply.personalItems"
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.number = 9
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.index = 8
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.label = 3
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.has_default_value = false
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.default_value = {}
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.message_type = SHARESTREETSCENEITEMNO_MSG
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.type = 11
tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD.cpp_type = 10

DECORATESHARESTREETREPLY_MSG.name = "DecorateShareStreetReply"
DECORATESHARESTREETREPLY_MSG.full_name = ".DecorateShareStreetReply"
DECORATESHARESTREETREPLY_MSG.filename = "ShareStreetExtension"
DECORATESHARESTREETREPLY_MSG.nested_types = {}
DECORATESHARESTREETREPLY_MSG.enum_types = {}
DECORATESHARESTREETREPLY_MSG.fields = {tb.DECORATESHARESTREETREPLY_DECORATEDITEMS_FIELD, tb.DECORATESHARESTREETREPLY_CROPS_FIELD, tb.DECORATESHARESTREETREPLY_FRIENDTREES_FIELD, tb.DECORATESHARESTREETREPLY_FOODITEMINFOS_FIELD, tb.DECORATESHARESTREETREPLY_TERRAINS_FIELD, tb.DECORATESHARESTREETREPLY_HASDELETESHARE_FIELD, tb.DECORATESHARESTREETREPLY_ADDSHARESTATE_FIELD, tb.DECORATESHARESTREETREPLY_PUBLICITEMS_FIELD, tb.DECORATESHARESTREETREPLY_PERSONALITEMS_FIELD}
DECORATESHARESTREETREPLY_MSG.is_extendable = false
DECORATESHARESTREETREPLY_MSG.extensions = {}
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.name = "totalCount"
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.full_name = ".GetStreetShareFurnitureCountReply.totalCount"
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.number = 1
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.index = 0
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.label = 3
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.has_default_value = false
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.default_value = {}
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.message_type = FURNITURECOUNTNO_MSG
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.type = 11
tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD.cpp_type = 10

tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.name = "userFurnitureCount"
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.full_name = ".GetStreetShareFurnitureCountReply.userFurnitureCount"
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.number = 2
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.index = 1
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.label = 3
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.has_default_value = false
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.default_value = {}
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.message_type = STREETUSERFURNITURECOUNTNO_MSG
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.type = 11
tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD.cpp_type = 10

GETSTREETSHAREFURNITURECOUNTREPLY_MSG.name = "GetStreetShareFurnitureCountReply"
GETSTREETSHAREFURNITURECOUNTREPLY_MSG.full_name = ".GetStreetShareFurnitureCountReply"
GETSTREETSHAREFURNITURECOUNTREPLY_MSG.filename = "ShareStreetExtension"
GETSTREETSHAREFURNITURECOUNTREPLY_MSG.nested_types = {}
GETSTREETSHAREFURNITURECOUNTREPLY_MSG.enum_types = {}
GETSTREETSHAREFURNITURECOUNTREPLY_MSG.fields = {tb.GETSTREETSHAREFURNITURECOUNTREPLY_TOTALCOUNT_FIELD, tb.GETSTREETSHAREFURNITURECOUNTREPLY_USERFURNITURECOUNT_FIELD}
GETSTREETSHAREFURNITURECOUNTREPLY_MSG.is_extendable = false
GETSTREETSHAREFURNITURECOUNTREPLY_MSG.extensions = {}
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.name = "changeId"
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.full_name = ".StreetShareFurnitureReply.changeId"
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.number = 1
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.index = 0
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.label = 1
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.has_default_value = false
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.default_value = 0
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.type = 5
tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD.cpp_type = 1

STREETSHAREFURNITUREREPLY_MSG.name = "StreetShareFurnitureReply"
STREETSHAREFURNITUREREPLY_MSG.full_name = ".StreetShareFurnitureReply"
STREETSHAREFURNITUREREPLY_MSG.filename = "ShareStreetExtension"
STREETSHAREFURNITUREREPLY_MSG.nested_types = {}
STREETSHAREFURNITUREREPLY_MSG.enum_types = {}
STREETSHAREFURNITUREREPLY_MSG.fields = {tb.STREETSHAREFURNITUREREPLY_CHANGEID_FIELD}
STREETSHAREFURNITUREREPLY_MSG.is_extendable = false
STREETSHAREFURNITUREREPLY_MSG.extensions = {}
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.name = "userId"
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.full_name = ".StreetUserFurnitureCountNO.userId"
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.number = 1
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.index = 0
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.label = 1
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.has_default_value = false
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.default_value = ""
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.type = 9
tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD.cpp_type = 9

tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.name = "furnitureCount"
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.full_name = ".StreetUserFurnitureCountNO.furnitureCount"
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.number = 2
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.index = 1
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.label = 3
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.has_default_value = false
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.default_value = {}
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.message_type = FURNITURECOUNTNO_MSG
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.type = 11
tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD.cpp_type = 10

STREETUSERFURNITURECOUNTNO_MSG.name = "StreetUserFurnitureCountNO"
STREETUSERFURNITURECOUNTNO_MSG.full_name = ".StreetUserFurnitureCountNO"
STREETUSERFURNITURECOUNTNO_MSG.filename = "ShareStreetExtension"
STREETUSERFURNITURECOUNTNO_MSG.nested_types = {}
STREETUSERFURNITURECOUNTNO_MSG.enum_types = {}
STREETUSERFURNITURECOUNTNO_MSG.fields = {tb.STREETUSERFURNITURECOUNTNO_USERID_FIELD, tb.STREETUSERFURNITURECOUNTNO_FURNITURECOUNT_FIELD}
STREETUSERFURNITURECOUNTNO_MSG.is_extendable = false
STREETUSERFURNITURECOUNTNO_MSG.extensions = {}

AgreeInviteJoinShareStreetReply = protobuf.Message(AGREEINVITEJOINSHARESTREETREPLY_MSG)
AgreeInviteJoinShareStreetRequest = protobuf.Message(AGREEINVITEJOINSHARESTREETREQUEST_MSG)
AgreeJoinShareStreetReply = protobuf.Message(AGREEJOINSHARESTREETREPLY_MSG)
AgreeJoinShareStreetRequest = protobuf.Message(AGREEJOINSHARESTREETREQUEST_MSG)
ApplyJoinShareStreetReply = protobuf.Message(APPLYJOINSHARESTREETREPLY_MSG)
ApplyJoinShareStreetRequest = protobuf.Message(APPLYJOINSHARESTREETREQUEST_MSG)
CancelApplyJoinShareStreetReply = protobuf.Message(CANCELAPPLYJOINSHARESTREETREPLY_MSG)
CancelApplyJoinShareStreetRequest = protobuf.Message(CANCELAPPLYJOINSHARESTREETREQUEST_MSG)
ChangeShareStreetHouseReply = protobuf.Message(CHANGESHARESTREETHOUSEREPLY_MSG)
ChangeShareStreetHouseRequest = protobuf.Message(CHANGESHARESTREETHOUSEREQUEST_MSG)
CreateShareStreetReply = protobuf.Message(CREATESHARESTREETREPLY_MSG)
CreateShareStreetRequest = protobuf.Message(CREATESHARESTREETREQUEST_MSG)
DecorateShareStreetReply = protobuf.Message(DECORATESHARESTREETREPLY_MSG)
DecorateShareStreetRequest = protobuf.Message(DECORATESHARESTREETREQUEST_MSG)
FriendStreetInfoNO = protobuf.Message(FRIENDSTREETINFONO_MSG)
FurnitureCountNO = protobuf.Message(FURNITURECOUNTNO_MSG)
GetFriendStreetListReply = protobuf.Message(GETFRIENDSTREETLISTREPLY_MSG)
GetFriendStreetListRequest = protobuf.Message(GETFRIENDSTREETLISTREQUEST_MSG)
GetPersonShareFurnitureCountReply = protobuf.Message(GETPERSONSHAREFURNITURECOUNTREPLY_MSG)
GetPersonShareFurnitureCountRequest = protobuf.Message(GETPERSONSHAREFURNITURECOUNTREQUEST_MSG)
GetShareStreetFurnitureInfoReply = protobuf.Message(GETSHARESTREETFURNITUREINFOREPLY_MSG)
GetShareStreetFurnitureInfoRequest = protobuf.Message(GETSHARESTREETFURNITUREINFOREQUEST_MSG)
GetShareStreetInfoReply = protobuf.Message(GETSHARESTREETINFOREPLY_MSG)
GetShareStreetInfoRequest = protobuf.Message(GETSHARESTREETINFOREQUEST_MSG)
GetStreetApplyListReply = protobuf.Message(GETSTREETAPPLYLISTREPLY_MSG)
GetStreetApplyListRequest = protobuf.Message(GETSTREETAPPLYLISTREQUEST_MSG)
GetStreetPopularityRankReply = protobuf.Message(GETSTREETPOPULARITYRANKREPLY_MSG)
GetStreetPopularityRankRequest = protobuf.Message(GETSTREETPOPULARITYRANKREQUEST_MSG)
GetStreetReceiveAskListReply = protobuf.Message(GETSTREETRECEIVEASKLISTREPLY_MSG)
GetStreetReceiveAskListRequest = protobuf.Message(GETSTREETRECEIVEASKLISTREQUEST_MSG)
GetStreetReceiveInviteListReply = protobuf.Message(GETSTREETRECEIVEINVITELISTREPLY_MSG)
GetStreetReceiveInviteListRequest = protobuf.Message(GETSTREETRECEIVEINVITELISTREQUEST_MSG)
GetStreetRecommendListReply = protobuf.Message(GETSTREETRECOMMENDLISTREPLY_MSG)
GetStreetRecommendListRequest = protobuf.Message(GETSTREETRECOMMENDLISTREQUEST_MSG)
GetStreetShareFurnitureCountReply = protobuf.Message(GETSTREETSHAREFURNITURECOUNTREPLY_MSG)
GetStreetShareFurnitureCountRequest = protobuf.Message(GETSTREETSHAREFURNITURECOUNTREQUEST_MSG)
GetUserJoinedStreetIdReply = protobuf.Message(GETUSERJOINEDSTREETIDREPLY_MSG)
GetUserJoinedStreetIdRequest = protobuf.Message(GETUSERJOINEDSTREETIDREQUEST_MSG)
InviteJoinShareStreetReply = protobuf.Message(INVITEJOINSHARESTREETREPLY_MSG)
InviteJoinShareStreetRequest = protobuf.Message(INVITEJOINSHARESTREETREQUEST_MSG)
KickOutShareStreetReply = protobuf.Message(KICKOUTSHARESTREETREPLY_MSG)
KickOutShareStreetRequest = protobuf.Message(KICKOUTSHARESTREETREQUEST_MSG)
LikeShareStreetReply = protobuf.Message(LIKESHARESTREETREPLY_MSG)
LikeShareStreetRequest = protobuf.Message(LIKESHARESTREETREQUEST_MSG)
ModifyStreetAcceptAskReply = protobuf.Message(MODIFYSTREETACCEPTASKREPLY_MSG)
ModifyStreetAcceptAskRequest = protobuf.Message(MODIFYSTREETACCEPTASKREQUEST_MSG)
QuitShareStreetReply = protobuf.Message(QUITSHARESTREETREPLY_MSG)
QuitShareStreetRequest = protobuf.Message(QUITSHARESTREETREQUEST_MSG)
RefuseJoinShareStreetReply = protobuf.Message(REFUSEJOINSHARESTREETREPLY_MSG)
RefuseJoinShareStreetRequest = protobuf.Message(REFUSEJOINSHARESTREETREQUEST_MSG)
SEARCH_STREET_ID = 2
SEARCH_USER_ID = 0
SEARCH_USER_NAME = 1
SearchShareStreetReply = protobuf.Message(SEARCHSHARESTREETREPLY_MSG)
SearchShareStreetRequest = protobuf.Message(SEARCHSHARESTREETREQUEST_MSG)
ShareStreetDecorationNO = protobuf.Message(SHARESTREETDECORATIONNO_MSG)
ShareStreetInfoNO = protobuf.Message(SHARESTREETINFONO_MSG)
ShareStreetMemberNO = protobuf.Message(SHARESTREETMEMBERNO_MSG)
ShareStreetSceneItemNO = protobuf.Message(SHARESTREETSCENEITEMNO_MSG)
StreetCancelShareFurnitureReply = protobuf.Message(STREETCANCELSHAREFURNITUREREPLY_MSG)
StreetCancelShareFurnitureRequest = protobuf.Message(STREETCANCELSHAREFURNITUREREQUEST_MSG)
StreetFurnitureInfoChangePush = protobuf.Message(STREETFURNITUREINFOCHANGEPUSH_MSG)
StreetShareFurnitureReply = protobuf.Message(STREETSHAREFURNITUREREPLY_MSG)
StreetShareFurnitureRequest = protobuf.Message(STREETSHAREFURNITUREREQUEST_MSG)
StreetSimpleInfoNO = protobuf.Message(STREETSIMPLEINFONO_MSG)
StreetUnlockAreaReply = protobuf.Message(STREETUNLOCKAREAREPLY_MSG)
StreetUnlockAreaRequest = protobuf.Message(STREETUNLOCKAREAREQUEST_MSG)
StreetUpdateInfoReply = protobuf.Message(STREETUPDATEINFOREPLY_MSG)
StreetUpdateInfoRequest = protobuf.Message(STREETUPDATEINFOREQUEST_MSG)
StreetUserFurnitureCountNO = protobuf.Message(STREETUSERFURNITURECOUNTNO_MSG)
VisitShareStreetReply = protobuf.Message(VISITSHARESTREETREPLY_MSG)
VisitShareStreetRequest = protobuf.Message(VISITSHARESTREETREQUEST_MSG)

return _G["logic.proto.ShareStreetExtension_pb"]
