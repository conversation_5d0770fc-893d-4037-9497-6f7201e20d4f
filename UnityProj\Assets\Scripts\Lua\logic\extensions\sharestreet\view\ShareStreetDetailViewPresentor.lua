module("logic.extensions.sharestreet.view.ShareStreetDetailViewPresentor",package.seeall)
---@class ShareStreetDetailViewPresentor
local ShareStreetDetailViewPresentor = class("ShareStreetDetailViewPresentor",ViewPresentor)

function ShareStreetDetailViewPresentor:ctor()
	ShareStreetDetailViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetDetailViewPresentor:dependWhatResources()
	return {"ui/street/streetdetailsview.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetDetailViewPresentor:buildViews()
	return {ShareStreetDetailView.New()}
end

--- 配置view所在的ui层
function ShareStreetDetailViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetDetailViewPresentor