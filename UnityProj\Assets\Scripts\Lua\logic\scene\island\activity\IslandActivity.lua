module("logic.scene.island.activity.IslandActivity", package.seeall)

local IslandActivity = class("IslandActivity", SceneComponentBase)

local SpecWorkShopUrl = "prefabs/island/specworkshop.prefab"
local SpecWorkShopPos = {- 36.5, - 24.6}
local SpecWorkShopActId = 198

function IslandActivity:onEnterSceneFinished(sceneId, bornX, bornZ)
	self._isInit = false
	self._pointActIds = {}
	ActivityFacade.instance:getActivityInfoASync(SpecWorkShopActId, handler(self._onGetActivityInfo, self))
	self._btnEcoPark = goutil.findChild(self._scene.stage.background, "bg1/ecoPark")
	if HouseModel.instance:getRoomParams(RoomParams.Params_Mode) == RoomScene.Type.Normal and self._btnEcoPark then
		FuncUnlockController.instance:regFuncUnlockGO(56, self._btnEcoPark)
		self._btnEcoPark:SetActive(FuncUnlockFacade.instance:checkIsUnlocked(56, false) and SceneManager.instance:getCurSceneId() ~= 71)
		self._scene.triggerController:addClickTrigger(self._btnEcoPark, nil, self._openEcoPark, self)
	end
	self._btnDoor = goutil.findChild(self._scene.stage.background, "bg1/door")
	if HouseModel.instance:getRoomParams(RoomParams.Params_Mode) == RoomScene.Type.Normal and self._btnDoor then
		self._scene.triggerController:addClickTrigger(self._btnDoor, nil, self._onClickDoor, self)
	end
end

function IslandActivity:_onClickDoor()
	if HouseModel.instance.isNpc then
		FlyTextManager.instance:showFlyText(lang("暂未开放海底小岛"))
		return
	end
	local curSceneId = SceneManager.instance:getCurSceneId()
	--0 和 1
	local islandType = HouseModel.instance:getUserProperty(HouseUserProperty.IslandType)
	FarmFacade.instance:enterOthersIsland(HouseModel.instance.userId, false, nil, nil, nil, nil, nil, nil, 1 - islandType)
end

function IslandActivity:_openEcoPark()
	EcoParkFacade.gotoEntranceEcoPark()
end

function IslandActivity:onExitScene()
	FuncUnlockController.instance:unregFuncUnlockGO(56, self._btnEcoPark)
	SceneController.instance:unregisterLocalNotify(SceneNotify.DaytimeChange, self._resetSpecWorkShopEffect, self)
	GlobalDispatcher:removeListener(GlobalNotify.ActivityUpdate, self._addSpecWorkShop, self)
	self._specWorkShop = nil
	self._pointActIds = nil
end

function IslandActivity:pointToActivity(activityId)
	if not self._isInit then
		table.insert(self._pointActIds, activityId)
		return
	end
	if activityId == SpecWorkShopActId then
		if self._specWorkShop then
			SceneManager.instance:getCurScene().camera:moveTo(SpecWorkShopPos[1], SpecWorkShopPos[2] + 4, 0)
			SceneTimer:setTimer(0.01, function()
				GuideArrowManager.instance:addArrowAtGO(self._specWorkShop.go, Vector2.New(0, 300))
			end, nil, false)
		end
	end
end

function IslandActivity:_onGetActivityInfo()
	if self._isInit then return end
	self._isInit = true
	self:_addSpecWorkShop()
	for _, id in ipairs(self._pointActIds) do
		self:pointToActivity(id)
	end
	self._pointActIds = {}
end

function IslandActivity:_addSpecWorkShop()
	-- 现在不要这个挫工坊了
	-- GlobalDispatcher:removeListener(GlobalNotify.ActivityUpdate, self._addSpecWorkShop, self)
	-- if ActivityFacade.instance:getActivityIsOpen(SpecWorkShopActId) then
	-- 	self._specWorkShop = self._scene.unitFactory:addUnit(SceneUnitType.SceneObj, {id = "SpecWorkShop"})
	-- 	self._specWorkShop:setPos(SpecWorkShopPos[1], SpecWorkShopPos[2])
	-- 	self._specWorkShop:addClickListener(self._onClickSpecWorkShop, self)
	-- 	self._specWorkShop:setView(SpecWorkShopUrl, self._resetSpecWorkShopEffect, self, nil, true)
	-- 	GlobalDispatcher:addListener(GlobalNotify.ActivityUpdate, self._addSpecWorkShop, self)
	-- 	SceneController.instance:registerLocalNotify(SceneNotify.DaytimeChange, self._resetSpecWorkShopEffect, self)
	-- else
	-- 	self._scene.unitFactory:removeUnit(SceneUnitType.SceneObj, "SpecWorkShop")
	-- 	SceneController.instance:unregisterLocalNotify(SceneNotify.DaytimeChange, self._resetSpecWorkShopEffect, self)
	-- end
end

function IslandActivity:_onClickSpecWorkShop()
	WorkshopService.instance:openAppointWorkshop(2000)
end

function IslandActivity:_resetSpecWorkShopEffect()
	local effect = goutil.findChild(self._specWorkShop:getView(), "nightEffect")
	goutil.setActive(effect, not SceneTime.isDaytime())
end

return IslandActivity 