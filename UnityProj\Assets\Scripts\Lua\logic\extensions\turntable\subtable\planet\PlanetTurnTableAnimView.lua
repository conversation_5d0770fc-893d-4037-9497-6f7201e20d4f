module("logic.extensions.turntable.subtable.perfume.PlanetTurnTableAnimView", package.seeall)
local PlanetTurnTableAnimView = class("PlanetTurnTableAnimView", ViewComponent)

--抽奖动画时间
local BaseAnimTime = 1.0
--抽中道具时间
local ShowInterval = 0.5
--结束等待时间
local EndShowTime = 1
--- view初始化时会执行
function PlanetTurnTableAnimView:buildUI()
    PlanetTurnTableAnimView.super.buildUI(self)
    self._txtTips = self:getGo("txtTips")
    local imgGoTrs = self:getGo("imgGo").transform
    self.rewardGos = {}

    for i = 1, imgGoTrs.childCount do
        local trans = imgGoTrs:Find("item" .. i)
        if trans then
            table.insert(self.rewardGos, trans.gameObject)
            self.rewardGos[i]:SetActive(false)
        end
    end
    self.needInit = true
    self.rewardCells = {}
    --抽奖动画GO，更新替换这个为新的动画
    self.effectGos = {self:getGo("bgGo/ui_planetturntableview_layer_01"),self:getGo("bgGo/ui_planetturntableview_trail_00_comp"),self:getGo("bgGo/ui_planetturntableview_target_001"),self:getGo("ui_planetturntableview_mask_04_front")}
    --抽奖的时候需要隐藏的GO，根据实际需求增删
    self.needHideGos = {self:getGo("btnclose"), self:getGo("btnhelp"), self:getGo("btnGo"), self:getGo("btnGife"),
                        self:getGo("btnAward"), self:getGo("btnBuy"), self:getGo("timeGo"), self:getGo("barDiamond"),
                        self:getGo("barActive"), self:getGo("barActive2"), self:getGo("awardPlanGo"), self:getGo("awardList"),self:getGo("extraAwardGo"),
                        self:getGo("btnRecord"), self:getGo("tipsGo"),self:getGo("btnRedbag") 
                    }
    self.specialNeedHideGos = {self:getGo("lotteryBtns"), self:getGo("toggle"),
                               self:getGo("starTimesGo")}

    self._animationAdapter = Framework.AnimationAdapter.Get(self.mainGO)
    self.imgGo = self:getGo("imgGo")
end

function PlanetTurnTableAnimView:setAnimState(value)
    for k, v in pairs(self.needHideGos) do
        goutil.setActive(v, not value)
    end
    local specialValue = not value and FengHuaTurntableController.instance:getRemainCount() > 0
    for k, v in pairs(self.specialNeedHideGos) do
        goutil.setActive(v, specialValue)
    end
end

function PlanetTurnTableAnimView:setEffect(value)
    print("setEffectcanSee", value)
    for k, v in pairs(self.effectGos) do
        goutil.setActive(v, value)
        -- if value then
        --     GameUtils.setPos(v, 0, 0, 0)
        -- else
        --     GameUtils.setPos(v, 10000, 10000, 0)
        -- end
    end
end

function PlanetTurnTableAnimView:destroyUI()
    self:resetRewards()
end

function PlanetTurnTableAnimView:resetRewards()
    for i, v in ipairs(self.rewardCells) do
        v:onExit()
    end
    self.rewardCells = {}
end

function PlanetTurnTableAnimView:bindEvents()
    self:registerLocalNotify(TurnTableNotify.ReSetTurnTable, self.updateView, self)
    self:registerLocalNotify(TurnTableNotify.PlayTurnAnim, self.startAnim, self)
end

function PlanetTurnTableAnimView:unbindEvents()
    self:unregisterLocalNotify(TurnTableNotify.ReSetTurnTable, self.updateView, self)
    self:unregisterLocalNotify(TurnTableNotify.PlayTurnAnim, self.startAnim, self)
end

function PlanetTurnTableAnimView:onEnter()
    self:setAnimState(false)
    self:setEffect(false)
    self._txtTips:SetActive(false)
    SoundManager.instance:playBGM(120097)
end

function PlanetTurnTableAnimView:onExit()
    removetimer(self.onAnimEnd, self)
    TaskUtil.BlockClick(false, "TurnTableBaseController:playAnim")
    SoundManager.instance:replayBGM()
end

function PlanetTurnTableAnimView:updateView()
    self.imgGo:SetActive(true)
    self:setAnimState(false)
    self:setEffect(false)
    if self.needInit then
        self:resetRewards()
        local result = FengHuaTurntableController.instance:getTurnTable()
        for i, go in ipairs(self.rewardGos) do
            local typeId = result[i]
            if typeId then
                local needHide = TurnTableConfig.getTableNeedHide(typeId, FengHuaTurntableController.instance.activityId)
                if not needHide then
                    local rewardCell = DragonTurnTableCell.New(typeId, go, BaseAnimTime, ShowInterval)
                    table.insert(self.rewardCells, rewardCell)
                end
            end
        end
        self.needInit = false
    else
        for i, v in ipairs(self.rewardCells) do
            v:update()
        end
    end
end

function PlanetTurnTableAnimView:startAnim(isSkip)
    self:setAnimState(true)
    if isSkip then
        self:onAnimEnd()
    else
        -- self._animationAdapter:Play("ani_ui_planetturntableview_01_start", true)
        self:setEffect(true)
        SoundManager.instance:playEffect(141990)
        local lotteryResult = FengHuaTurntableController.instance.lotteryResult
        local index = 1
        for i, v in ipairs(lotteryResult) do
            local typeId = TurnTableConfig.getBonus(FengHuaTurntableController.instance.activityId)[v].rewardType
            local needHide = TurnTableConfig.getTableNeedHide(typeId, FengHuaTurntableController.instance.activityId)
            if not needHide then
                for _, cell in pairs(self.rewardCells) do
                    if cell.typeId == typeId then
                        if not cell.isChoosed then
                            index = index + 1
                        end
                        cell:playChooseAnim(index)
                        break
                    end
                end
            end
        end
        settimer(BaseAnimTime + index * ShowInterval + EndShowTime, self.onAnimEnd, self, false)
    end
end

function PlanetTurnTableAnimView:onAnimEnd()
    SoundManager.instance:playEffect(141048)
    self.imgGo:SetActive(false)
    FengHuaTurntableController.instance:endAnim()
end

return PlanetTurnTableAnimView
