-- {excel:L聊天配置.xlsx, sheetName:export_喇叭样式配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_worldhorn_style", package.seeall)

local title = {id=1,isTimeLimit=2,isAvailable=3,url_effect_preview=4,url_effect_panel=5,url_effect_marquee=6}

local dataList = {
	{1, false, true, "effect_trumpetpanel_star_01", "effect_chatview_star_01", "effect_chat_star_01"},
	{2, false, false, "effect_trumpetpanel_2024_02", "effect_chatview_2024_02", "effect_chat_2024_02"},
	{3, false, true, "effect_trumpetpanel_flower_03", "effect_chatview_flower_03", "effect_chat_flower_03"},
	{4, false, false, "effect_trumpetpanel_elf_02", "effect_chatview_elf_02", "effect_chat_elf_02"},
}

local t_worldhorn_style = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
}

t_worldhorn_style.dataList = dataList
local mt
if WorldHornStyleDefine then
	mt = {
		__cname =  "WorldHornStyleDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or WorldHornStyleDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_worldhorn_style