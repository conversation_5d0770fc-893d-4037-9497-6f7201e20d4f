module("logic.scene.scene3D.free3D.SceneFree3DUnitMgr", package.seeall)

local SceneFree3DUnitMgr = class("SceneFree3DUnitMgr", SceneUnitManager)

local zeroRotation = Vector3.zero

function SceneFree3DUnitMgr:unitsFaceTo()
    local camera = self._scene.camera
    local forward = camera.camTrs.forward
    local rotation = Quaternion.LookRotation(forward)
    local list = SceneUnitType.FaceToFreeCamera
    local unitMgr = self._scene:getUnitMgr()
    for _, unitType in ipairs(list) do
        local units = unitMgr:getUnits(unitType)
        for _, unit in ipairs(units) do
            if unit:getCanSee() then
                if not unit.noRotation then
                    unit.go.transform.rotation = rotation
                else
                    unit.go.transform.rotation = zeroRotation
                end
            end
        end
    end
    local targetArrow = self._scene.tipsMgr.targetArrow
    if targetArrow then
        targetArrow.transform.rotation = rotation
    end
end

return SceneFree3DUnitMgr
