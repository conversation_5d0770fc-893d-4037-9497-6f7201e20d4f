module("logic.scene.unit.playeraction.TransformSkinPlayerHandler",package.seeall)

local TransformSkinPlayerHandler = class("TransformSkinPlayerHandler",PlayerActionHandlerBase)

function TransformSkinPlayerHandler:onStart()
	--播放跳舞动画，需要判断是自动播放舞蹈还是玩家控制
	self._originScale = GameUtils.getLocalScale(self.unit.go)
	self.unit:setAlwaysFront(true)
	local dir = self.unit:getDirection()
	self.unit.skinView:setFace(true)
	self.unit:setDirection(dir)
	self.unit.mover:clearWayPoints()
	self.unit:stop()
	self.unit:lockMove(true)
	self.unit.skinView:setAnimation(self.unit:getIdleAniName(), true)
	self.unit.walkEffect:setSpecialMove(true)
	TaskUtil.BlockClick(true,"TransformSkin")
	
	self._idleCdTime = tonumber(TransformSkinConfig.getCommonCfgByKey("InteractionIdleCDTime"))
	self._itemId = tonumber(self.info.params[3]) 
	local config = TransformSkinConfig.getTransformSkinByItemId(self._itemId)

	self.startSpeed = self.unit:getSpeed()
	local newSpeed = self.startSpeed * config.speedBoost
	self.unit:setSpeed(newSpeed)

	self:_setInteractCDTime()
	self:_showEffect()	
end

function TransformSkinPlayerHandler:onStop()
	TransformSkinController.instance:removeTransformSkinUnitInfo(self.unit.id)
	--防止状态结束后音效没停
	local config = TransformSkinConfig.getTransformSkinInteractByItemId(self._itemId)
	local soundId = config.soundId
	if soundId and soundId ~= 0 then 
		SoundManager.instance:stopEffect(soundId,self.unit.go)
	end

	if self._bornEffect then
		self._bornEffect:Play()
	end
	self._isCustomSkin = false
	self:_transformSkin(false)
	self.unit:setAlwaysFront(false)
	self.unit:lockMove(false)
	self.unit.walkEffect:setSpecialMove(false)
	self.unit:setSpeed(self.startSpeed)
	TaskUtil.BlockClick(false,"TransformSkin")
	removetimer(self._showEffectFinish,self)
	removetimer(self._transformUnitSkin,self)
	removetimer(self._checkInteractCDTime,self)
end

function TransformSkinPlayerHandler:onUpdate()
	if self._actState == tonumber(self.info.params[2]) then
		return
	end

	if tonumber(self.info.params[2]) == 2 then
		local config = TransformSkinConfig.getTransformSkinInteractByItemId(self._itemId)
		local animName = config.animName
		local delay = config.delay
		local soundId = config.soundId
		self.unit.customSkin:playAnimation(animName,true)
		if soundId and soundId ~= 0 then 
			SoundManager.instance:stopEffect(soundId,self.unit.go)
			printWarn(self.unit.go.name)
			SoundManager.instance:playEffect(soundId,self.unit.go)
		end
	else
		self.unit:playAnimation("idle", true)
		self.unit.customSkin:playAnimation("idle",true)	
		local config = TransformSkinConfig.getTransformSkinInteractByItemId(self._itemId)
		local soundId = config.soundId
		if soundId and soundId ~= 0 then 
			SoundManager.instance:stopEffect(soundId,self.unit.go)
		end
	end

	self._actState = tonumber(self.info.params[2])
end

--防止进场景加载延迟 被推送到onUpdate时customSkin.aniHelper == nil，setSkin后强制更新一次动画
function TransformSkinPlayerHandler:forceUpdate()
	if tonumber(self.info.params[2]) == 2 then
		local config = TransformSkinConfig.getTransformSkinInteractByItemId(self._itemId)
		local animName = config.animName
		local delay = config.delay
		local soundId = config.soundId
		self.unit.customSkin:playAnimation(animName,true)
		if soundId and soundId ~= 0 then 
			
			SoundManager.instance:stopEffect(soundId,self.unit.go)
			printWarn(self.unit.go.name)
			SoundManager.instance:playEffect(soundId,self.unit.go)
		end
	end
end

function TransformSkinPlayerHandler:onStartMove()
	removetimer(self._checkInteractCDTime,self)
	self:_setInteractCDTime()
	if not self._isCustomSkin then
		self.unit:playAnimation("walk", true)
	end

	if self.unit.isUser then 
		if self._actState == 2 then 
			TransformSkinController.instance:localNotify(TransformSkinNotify.playLongStateAnim,false)
		end
	end
end

function TransformSkinPlayerHandler:onStopMove()
	removetimer(self._checkInteractCDTime,self)
	settimer(1,self._checkInteractCDTime,self,true)
	if not self._isCustomSkin then
		self.unit:playAnimation("idle", true)	
	end
end

function TransformSkinPlayerHandler:_getSkinUrl()
	local itemId = self._itemId
	local config = TransformSkinConfig.getTransformSkinByItemId(itemId)
	return config.playerSkinUrl and config.playerSkinUrl[tonumber(self.info.params[7])] and config.playerSkinUrl[tonumber(self.info.params[7])]
end

--播放特效
function TransformSkinPlayerHandler:_showEffect()
	local time = 0.8
	local url = "prefabs/transformskin/transformbom_bearsmoke_effect_01.prefab"
	self.unit.effectLoader:getSlot(EffectLoader.OnEffectLoaded):load(url,self._loadEffectSuccess,self)
	removetimer(self._transformUnitSkin,self)
	settimer(0.3,self._transformUnitSkin,self,false)
	removetimer(self._showEffectFinish,self)
	settimer(time,self._showEffectFinish,self,false)
end
function TransformSkinPlayerHandler:_loadEffectSuccess()
	local go = self.unit.effectLoader:getSlot(EffectLoader.OnEffectLoaded):getInst()
	self._bornEffect = goutil.findChild(go,"Effect_01"):GetComponent("ParticleSystem")
	self._bornEffect:Play()
end
function TransformSkinPlayerHandler:_showEffectFinish()
	local url = self:_getSkinUrl()
	local isCustomSkin = url and url ~= ""
	if not isCustomSkin then
		self.unit:setAlwaysFront(false)
		self.unit.walkEffect:setSpecialMove(false)
	end
	self.unit:lockMove(false)
	TaskUtil.BlockClick(false,"TransformSkin")
end

--转换皮肤
function TransformSkinPlayerHandler:_transformUnitSkin()
	self:_transformSkin(true)
end

--变身
function TransformSkinPlayerHandler:_transformSkin(isTransform)
	local itemId = self._itemId
	local config = TransformSkinConfig.getTransformSkinByItemId(itemId)
	local url = self:_getSkinUrl()
	self._isCustomSkin = url and url ~= ""
	if isTransform then
		local scale = config.playerScale == nil and 1 or config.playerScale
		if self._isCustomSkin then
			self.unit:setCustomSkin(url,self._loadCustomSkinFinish,self,0)
			if itemId == 16000583 then
				self.unit.customSkin:setAniName("idle1","move")
			else
				self.unit.customSkin:setAniName("idle","walk")
			end
		end
		
		if config.nameOffsetY and config.nameOffsetY ~= 0 then
			--1.4是PlayerUnit脚本那边写死的一个值，代表初始的Y偏移
			--local uiOffsetY = scale / self._originScale.y * 1.4
			self.unit.followUIComp:setOffset(UnitFollowUIType.Top, 0, config.nameOffsetY)
		end
		
		GameUtils.setLocalScale(self.unit.go,scale,scale,scale)
	else
		if self._isCustomSkin then
			if self.isTransform then 
				self.unit:removeCustomSkin()
			end
			--self.unit:removeCustomSkin()
		end
		if config.nameOffsetY and config.nameOffsetY ~= 0 then
			self.unit.followUIComp:setOffset(UnitFollowUIType.Top, 0, 1.4)
		end
		GameUtils.setLocalScale(self.unit.go,self._originScale.x,self._originScale.y,self._originScale.z)
	end

	self.isTransform = isTransform
end

--加载变身皮肤完成
function TransformSkinPlayerHandler:_loadCustomSkinFinish()
	local skinName = self.info.params[6]
	if skinName and skinName ~= "" then
		local spineAnimation = self.unit.customSkin:getSkin().animComp
		spineAnimation.skeleton:SetSkin(skinName)
		spineAnimation.skeleton:SetSlotsToSetupPose()
	end
	self:forceUpdate()
end

function TransformSkinPlayerHandler:_setInteractCDTime()
	self._cdTime = self._idleCdTime
	self.isInIdleCd = true
	TransformSkinController.instance:setTransformSkinUnitInfo(self.unit.id,self._itemId,self.isInIdleCd)
end
--检测交互cd
function TransformSkinPlayerHandler:_checkInteractCDTime()
	if self._cdTime <= 0 then
		self.isInIdleCd = false
		removetimer(self._checkInteractCDTime,self)
		TransformSkinController.instance:setTransformSkinUnitInfo(self.unit.id,self._itemId,self.isInIdleCd)
		return
	end
	self._cdTime = self._cdTime - 1
end

return TransformSkinPlayerHandler