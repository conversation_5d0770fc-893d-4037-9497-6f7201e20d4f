module("logic.extensions.transformskin.TransformSkinConditionComp",package.seeall)
local TransformSkinConditionComp = class("TransformSkinConditionComp")

--1.typeId：unit处于某种handler状态（PlayerActionType，可为空
--2.isNeedIdle：unit不动的情况下
--3.range,count：每两个unit之间距离小于一定范围且超过一定数量，即一堆unit站在一起或者相连
function TransformSkinConditionComp:ctor(info)
	self._info = info
	self._isNeedIdle = info.isNeedIdle
	self._range = info.range * info.range
	self._count = info.count
	--self._allocCount = allocCount
end
function TransformSkinConditionComp:onDispose()
	self._finalUnitsDic = nil
end

--获得符合条件的unit
function TransformSkinConditionComp:getSufficeUnitsDic(tempUnits)
	local newTempUnits = self:_filterUnit(tempUnits)
	local sufficeGroups = self:_filterByRangeAndCount(newTempUnits)
	local sufficeGroups_1 = self:_filterByCDAndItemId(sufficeGroups)
	self._finalUnitsDic = self:_filterSameUnit(sufficeGroups_1)
	
	newTempUnits = nil
	sufficeGroups = nil
	sufficeGroups_1 = nil
	finalGroups = nil
	return self._finalUnitsDic
end

--先剔除不在变身状态的unit
--是否在变身状态；是否不在cd；是否属于可以互动的道具（itemId）;是否处于idle;是否在长状态
function TransformSkinConditionComp:_filterUnit(tempUnits)
	local newUnits = {}
	for i = 1, #tempUnits do
		if tempUnits[i] then
			local info = TransformSkinController.instance:getTransformSkinUnitInfo(tempUnits[i].id)
			
			if info then
				local isInAnimCD = info.animCd or 0
				local isInIdleCD = info.isInIdleCd
				local isNeedItemId = self:_chechHasNeedItemId(info.itemId)
				if not isInIdleCD and isInAnimCD <= 0 and isNeedItemId then
					local isMoving = self._isNeedIdle and tempUnits[i]:isMoving() or false
					if not isMoving then
						table.insert(newUnits,tempUnits[i])
					end
				end
			end
		end
	end
	return newUnits
end

function TransformSkinConditionComp:_filterByRangeAndCount(tempUnits)
	local sufficeGroups = {}
	local calCount = #tempUnits
	for i = 1, calCount do
		local group = nil
		local pos1 = tempUnits[i].go.transform.position
		for j = 1, calCount do
			if i ~= j then
				local pos2 = tempUnits[j].go.transform.position
				local sqrDis = (pos2 - pos1):SqrMagnitude()
				if sqrDis <= self._range then
					if group == nil then group = {} end
					table.insert(group,tempUnits[j])
				end
			end
		end
		if group and #group >= (self._count - 1) then
			table.insert(group,tempUnits[i])
			table.insert(sufficeGroups,group)
		else
			group = nil
		end
	end
	return sufficeGroups
end

--过滤掉同组不同道具id的unit
function TransformSkinConditionComp:_filterByCDAndItemId(sufficeGroups)
	if sufficeGroups == nil or #sufficeGroups == 0 then
		return
	end
	local finalGroups = nil
	for i = 1, #sufficeGroups do
		local group = sufficeGroups[i]
		local isCanInteract = true
		local preItemId = nil
		for j = 1, #group do
			local unit = group[j]
			if unit then
				local info = TransformSkinController.instance:getTransformSkinUnitInfo(unit.id)
				if info and info.itemId ~= nil then
					if preItemId ~= nil then
						if preItemId ~= info.itemId then
							isCanInteract = false
							break
						end
					end
					preItemId = info.itemId
				else
					isCanInteract = false
					break
				end
			else
				isCanInteract = false
				break
			end
		end
		if isCanInteract then
			if finalGroups == nil then
				finalGroups = {}
			end
			table.insert(finalGroups,group)
		end
	end
	return finalGroups
end

--剔除可交互列表里相同的unit  字典
function TransformSkinConditionComp:_filterSameUnit(finalGroups)
	if finalGroups == nil or #finalGroups == 0 then
		return nil
	end
	local finalUnits = {}
	for i = 1, #finalGroups do
		local group = finalGroups[i]
		for j = 1, #group do
			finalUnits[group[j].id] = group[j]
		end
	end

	return finalUnits
end

--检测是否是需要判断的itemId
function TransformSkinConditionComp:_chechHasNeedItemId(itemId)
	if itemId == nil then
		return false		
	end
	local itemIds = self._info.itemIds
	for i = 1, #itemIds do
		if itemId == itemIds[i] then
			return true
		end
	end
	return false
end


return TransformSkinConditionComp