-- {excel:284周末与周一福利.xlsx, sheetName:export_act284周一奖励表（累计）}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act284_accumulate", package.seeall)

local title = {activityId=1,signTimes=2,accumulateRewards=3,accumulateRewards2=4}

local dataList = {
	{1550, 1, {{count=20,id=2},{count=1,id=16000054}}, {{count=20,id=2},{count=1,id=16000054}}},
	{1550, 3, {{count=100,id=2},{count=1,id=16000054},{count=1,id=16000212}}, {{count=100,id=2},{count=1,id=16000789},{count=1,id=16000212}}},
}

local t_act284_accumulate = {
	[1550] = {
		[1] = dataList[1],
		[3] = dataList[2],
	},
}

t_act284_accumulate.dataList = dataList
local mt
if Activity284AccumulateDefine then
	mt = {
		__cname =  "Activity284AccumulateDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Activity284AccumulateDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act284_accumulate