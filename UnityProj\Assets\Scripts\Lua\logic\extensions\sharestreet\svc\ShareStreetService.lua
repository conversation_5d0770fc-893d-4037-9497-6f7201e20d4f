module("logic.extensions.sharestreet.svc.ShareStreetService",package.seeall)

local ShareStreetService = class("ShareStreetService")

function ShareStreetService:init()
    -- if FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.ShareStreet) then
    --     ShareStreetController.instance:getMyInfo(handler(self._onGetMyInfo, self))
    -- else
        GlobalDispatcher:dispatch(GlobalNotify.InitFinish)
    -- end
end

function ShareStreetService:_onGetMyInfo()
    GlobalDispatcher:dispatch(GlobalNotify.InitFinish)
end

ShareStreetService.instance = ShareStreetService.New()
return ShareStreetService