module("logic.extensions.throwcake.ThrowCakeController", package.seeall)

local ThrowCakeController = class("ThrowCakeController", BaseController)

ThrowCakeController.LimitId = 13

function ThrowCakeController:onInit()
    self.activityId = ThrowCakeConfig.getActivityId()
    self:registerNotify(GlobalNotify.getActivityInfoFinish, self._lateCheckLevelIsOpen, self)
    self:registerNotify(GlobalNotify.onServerPushActivity, self._lateCheckLevelIsOpen, self)
    self:registerNotify(GlobalNotify.LeaveScene, self.stopGame, self)
end

function ThrowCakeController:onReset()
    self:unregisterNotify(GlobalNotify.getActivityInfoFinish, self._lateCheckLevelIsOpen, self)
    self:unregisterNotify(GlobalNotify.onServerPushActivity, self._lateCheckLevelIsOpen, self)
    self:unregisterNotify(GlobalNotify.LeaveScene, self.stopGame, self)
end

function ThrowCakeController:_lateCheckLevelIsOpen()
    -- settimer(1, self.updateClickRedPoint, self, false)
end

function ThrowCakeController:getGameInfo()
    ThrowCakeAgent.instance:sendGetAct252RewardInfoRequest(function(gameinfos, hasgetrewards)
        self.maxScoreMap = {}
        -- 关卡最高分
        for i = 1, 4 do
            -- 防止传空
            self.maxScoreMap[i] = 0
        end
        for i, v in ipairs(gameinfos) do
            if v.maxScore ~= nil then
                self.maxScoreMap[v.modeId] = v.maxScore
            end
        end
        -- 领取过的奖励id
        self.rewardIdHasGet = hasgetrewards
        self:localNotify(ThrowCakeNotify.UpdateView)
    end)
end

function ThrowCakeController:setGameLevel(level)
    self.gameLevel = level
    if level < 5 then
        self:localNotify(ThrowCakeNotify.SetGameLevel)
    end
end

function ThrowCakeController:getGameLevel()
    return self.gameLevel or 4
end

function ThrowCakeController:activityIsOpen()
    local isOpen = ActivityFacade.instance:getActivityIsOpen(GameEnum.ActivityEnum.ACTIVITY_252)
    if not isOpen then
        FlyTextManager.instance:showFlyText(lang("活动已结束。"))
        return false
    end
    return true
end

function ThrowCakeController:getOpenTime(level)
    local openTime = ThrowCakeConfig.getLevelCfg(level).openTime
    local time = TimeUtil.dateStr2TimeStamp(openTime, "T")
    return time
end

function ThrowCakeController:updateClickRedPoint()
    print("ThrowCakeController:updateClickRedPoint")
    self.tabHasClick = {}
    -- for i = 1, 4, 1 do
    --     local hasClick = false
    --     local storageActId = LocalStorage.instance:getValue(StorageKey.LastThrowCakeId, 0)
    --     if storageActId == self.activityId then
    --         local lastTime = LocalStorage.instance:getValue(StorageKey["ThrowCake_LastClick_Tab" .. i], 0)
    --         -- print("lastTime", lastTime, ServerTime.now())
    --         hasClick = lastTime ~= 0 and ServerTime.now() >= lastTime
    --     else
    --         LocalStorage.instance:setValue(StorageKey.LastThrowCakeId, self.activityId)
    --         for j = 1, 4, 1 do
    --             LocalStorage.instance:setValue(StorageKey["ThrowCake_LastClick_Tab" .. j], 0)
    --         end
    --     end
    --     self.tabHasClick[i] = hasClick
    -- end
    -- RedPointController.instance:setRedIsExist("ThrowCakeGameTab", false)
    -- for i, v in ipairs(self.tabHasClick) do
    --     local time = ThrowCakeController.instance:getOpenTime(i)
    --     local isopen = ServerTime.now() > time
    --     if isopen and v == false then
    --         print(i, "已解锁但未点击，设置红点")
    --         RedPointController.instance:setRedIsExist("ThrowCakeGameTab", true)
    --         break
    --     end
    -- end
end

function ThrowCakeController:hasRewardRedPoint(level)
    print("check reward redpoint", level)
    local rewardsCfg = ThrowCakeConfig.getRewardByLevel(level)
    for i, v in ipairs(rewardsCfg) do
        local canGet = v.needScore <= self.maxScoreMap[level]
        local hasGet = self:getRewardHasGet(v.rewardId)
        if canGet and not hasGet then
            return true
        end
    end
    return false
end

function ThrowCakeController:getTabHasClick(index)
    return self.tabHasClick[index]
end

function ThrowCakeController:getMaxScore(level)
    if level == nil then
        level = self.gameLevel
    end
    local result = self.maxScoreMap[level]
    -- printError("ThrowCakeController:getMaxScore", level, result)
    return result
end

function ThrowCakeController:getRewardHasGet(rewardId)
    for i, v in ipairs(self.rewardIdHasGet) do
        if v == rewardId then
            return true
        end
    end
    return false
end

function ThrowCakeController:receiveReward(rewardId)
    ThrowCakeAgent.instance:sendGetAct252RewardRequest(rewardId, function(changeid)
        DialogHelper.showRewardsDlgByCI(changeid)
        table.insert(self.rewardIdHasGet, rewardId)
        self:localNotify(ThrowCakeNotify.UpdateView)
    end)
end

function ThrowCakeController:openPrepareView()
    -- printError("打开准备界面")
    ViewMgr.instance:open("ThrowCakeGamePrepareView")
    -- self:openSingleGameView()
end

function ThrowCakeController:openMutiGameView(isFirst, partnerId, partnerName, avatarInfo)
    self.partnerInfo = {
        id = partnerId,
        name = partnerName,
        avatarInfo = avatarInfo
    }
    self:setGameLevel(5)
    self:openGameView(isFirst, self.partnerInfo)
end

function ThrowCakeController:openSingleGameView()
    ThrowCakeModel.instance:clearData()
    local mode = self:getGameLevel()
    ThrowCakeAgent.instance:sendStartAct252GameRequest(mode, handler(self._onStartGameReply, self))
end

function ThrowCakeController:_onStartGameReply()
    self:openGameView()
end

function ThrowCakeController:openGameView(isFirst, partnerPlayerId)
    local mode = self:getGameLevel()
    self.gameCfgInfo = ThrowCakeConfig.getGameCfgInfo(mode)
    self.gameCfgInfo.isFirst = isFirst
    ViewMgr.instance:open("ThrowCakeGameView", self.gameCfgInfo.isSingle, partnerPlayerId)
    local limitProp = ActivityFacade.instance:getGainLimitModel({ThrowCakeController.LimitId})
    -- 游戏开始之前就要记录此数量，否则结束了算不对
    -- 已领取的数量
    local receivedCount = 0
    -- 如果后端存在已领取的限制道具
    if limitProp then
        receivedCount = limitProp[1].dailyLimit
        -- printError("0.已领取了！",receivedCount)
    end
    self.receivedCount = receivedCount
end

function ThrowCakeController:getGameCfgInfo()
    return self.gameCfgInfo
end

function ThrowCakeController:quitGame()
    if self:getGameLevel() <= 4 then
        local gameInfo = ThrowCakeModel.instance:getGameOverInfo()
        print(gameInfo.hp,gameInfo.score, gameInfo.isPass)
        if self:getGameLevel() == 4 then
            -- 无尽模式中途退出要发送协议
            if gameInfo.hp > 0 and not gameInfo.isPass then
                -- printError("中途退出发结束")
                ThrowCakeAgent.instance:sendFinishAct252GameRequest(gameInfo.score, gameInfo.isPass,
                    handler(self._onFinishGameReply, self))
            else
                self:_onFinishGameReply()
            end
        else
            -- printError("前三关发结束")
            ThrowCakeAgent.instance:sendFinishAct252GameRequest(gameInfo.score, gameInfo.isPass,
                handler(self._onFinishGameReply, self))
        end
    else
        -- 无尽模式正常结束不用发送协议,结束推送早就到了
        self:_onFinishGameReply()
    end
end

function ThrowCakeController:endMutiGame()
    print("多人游戏结束")
end

function ThrowCakeController:onGetGameEndPush(msg)
    -- printError("游戏结束推送")
    -- local modeid = msg.modeId
    -- local step = msg.step
    -- local score = msg.score
    -- local changeid = msg.changeId
    -- local disconnectend = msg.disConnectEnd
    -- local resultcode = msg.resultCode
    self.gameOverPushInfo = msg
    if self.gameOverPushInfo.resultCode == nil then
        self.gameOverPushInfo.resultCode = 0
    end
    if msg.disConnectEnd then
        FlyTextManager.instance:showFlyText(lang("天才蛋糕对方掉线提示"))
        self:_onFinishGameReply()
    end
end

function ThrowCakeController:openResultPanel()
    -- printError("打开结算界面")
    local totalScore = self.gameOverPushInfo.score
    local gameLevel = self:getGameLevel()
    local levelCfg = ThrowCakeConfig.getLevelCfg(gameLevel)
    local id = levelCfg.levelAward[1].id
    local count = levelCfg.levelAward[1].count
    -- printError("1.得分",totalScore)
    -- printError("2.原始奖励",count)
    if totalScore < levelCfg.dailyTargetScore then
        -- 需要超过一定分数才能领
        count = 0
        -- printError("3.最低分数",levelCfg.dailyTargetScore,"未达到！0分")
    elseif self.gameOverPushInfo.resultCode ~= 0 then
        count = 0
    else
        -- 剩余可领取的数量
        local surplusCanAddCount = ActivityConfig.getGainLimitTotalDailyLimitById(ThrowCakeController.LimitId) -
                                       self.receivedCount
        -- printError("4.获取上限~",ActivityConfig.getGainLimitTotalDailyLimitById(ThrowCakeController.LimitId))
        -- 计算本次游戏能获得的奖励数量(若关卡奖励数量小于剩余可领取数量，则为关卡奖励数量，否则为剩余可领取数量)
        if count > surplusCanAddCount then
            -- printError("5.剩余可领取的~",surplusCanAddCount)
            count = surplusCanAddCount
        end
    end
    -- printError("6.最终奖励数量~",count)
    local reward = {id, count}
    local restartHandler = function()
        ThrowCakeController.instance:openSingleGameView()
    end
    local partnerInfo = deepcopy(self.partnerInfo)
    self.partnerInfo = nil
    ViewMgr.instance:open("ThrowCakeGameResultView", totalScore, reward, restartHandler, partnerInfo)
    if self.gameOverPushInfo.resultCode ~= 0 then
        print(self.gameOverPushInfo.resultCode)
        DialogHelper.showErrorMsg(self.gameOverPushInfo.resultCode)
    end
    self.gameOverPushInfo = nil
    if self:getGameLevel() < 5 then
        ThrowCakeController.instance:getGameInfo()
    end
end

function ThrowCakeController:_onFinishGameReply()
    ViewMgr.instance:close("ThrowCakeGameView")
    self:stopGame()
    self:openResultPanel()
end

-- 游戏部分

function ThrowCakeController:tryStartGame(viewGo)
    self._context = ThrowCakeContext.New(self.gameCfgInfo, viewGo)
    -- --- 首次进行单人玩法就展示指引
    -- if isSingle then
    --     local isGuided = LocalStorage.instance:getValue(StorageKey.LumberJackNewbieGuideFinished, false)
    --     if not isGuided then
    --         print("指引~")
    --         self:startGame()
    --     else
    --         self:startGame()
    --     end
    -- else
    --     self:startGame()
    -- end
    self:startGame()
end

function ThrowCakeController:startGame()
    ViewMgr.instance:open("ColaHelpPanel", handler(self.onCountDownFinished, self), 3)
    self._context:startGame()
    -- self:onCountDownFinished()
end

function ThrowCakeController:onCountDownFinished()
    print("游戏正式开始")
    UpdateBeat:Add(self._updateContext, self)
end

function ThrowCakeController:_updateContext()
    if self._context then
        self._context:update()
    end

end

function ThrowCakeController:stopGame()
    ThrowCakeModel.instance:clearData()
    ViewMgr.instance:close("ColaHelpPanel")
    UpdateBeat:Remove(self._updateContext, self)
    if self._context then
        self._context:stopGame()
        self._context = nil
    end
end

-- 同步结果
function ThrowCakeController:sendThrowResult(throwResult)
    -- Act252EndlessThrowCakeRequest
    if self.gameCfgInfo.isSingle then
        ThrowCakeAgent.instance:sendAct252EndlessThrowCakeRequest(throwResult.round, throwResult.finalX,
            handler(self._onSendEndlessReply, self))
    else
        TinyGameAgent.instance:sendStackCakeThrowRequest(throwResult.round, throwResult.finalX)
    end
end

function ThrowCakeController:_onSendEndlessReply(msg)
    print("push-------------")
    -- local heart = msg.heart
    local score = msg.score
    local nextappearanceid = msg.nextAppearanceId
    -- local nextisnewlevel = msg.nextIsnewLevel
    local issuccess = msg.isSuccess
    local sonstep = msg.sonStep
    -- local sonlayer = msg.sonLayer
    local wind = msg.wind
    local centerpos = msg.centerPos or msg.cakeCenterPos

    local throwResult = {
        score = msg.score,
        isSuccess = msg.isSuccess,
        -- heart = msg.heart,
        finalX = centerpos,
        round = sonstep
    }
    local newLevelInfo = {
        round = sonstep + 1,
        cakeType = msg.nextAppearanceId,
        wind = wind
    }
    ThrowCakeModel.instance:addThrowResult(throwResult)
    ThrowCakeModel.instance:addRoundInfo(newLevelInfo)
end

function ThrowCakeController.openDoubleRankView()
    ViewMgr.instance:open("ThrowCakeDoubleRankView")
end

ThrowCakeController.instance = ThrowCakeController.New()
return ThrowCakeController
