module("logic.scene.unit.PlayerUnit", package.seeall)
local PlayerUnit = class("PlayerUnit", SceneUnitBase)

-- function PlayerUnit:ctor(componetContainer)
-- 	PlayerUnit.super.ctor(self, componetContainer)
-- end

--dir:
-- 2   3
--- ↖↗
--  ↙↘
-- 0   1

PlayerUnit.IdleAniList_boy = {"nanlook", "daheqian_boy"}
PlayerUnit.IdleAniList_girl = {"nvlook", "daheqian_girl"}
PlayerUnit.IdleAniList_None = {"nvlook", "daheqian_girl","nanlook", "daheqian_boy"}
local WalkFastSoundId = 130089
local WalkSlowSoundId = 130102

function PlayerUnit:getTag()
	return UnitTag.Player
end

function PlayerUnit:getLayer()
	return Framework.LayerUtil.NameToLayer(SceneLayer.MainPlayer)
end

function PlayerUnit:Awake()
	NotifyDispatcher.extend(self)
	PlayerUnit.super.Awake(self)
	self.outdateMap = {}
	self.dir = 1
	self.avatar = AvatarInfo.New()
	self.clickSignal = SignalAmount.New()
	self:setSpeed(SceneConfig.GetPlayerSpeed())

	self:_addListeners()
	if VirtualCameraMgr.instance:is3DScene() then
		self.navAgent = PjAobi.CSGameUtils.AddNavMeshAgent(self.go, 0.1)
	end

end

function PlayerUnit:_initComponents()
	self:_addComponent("userVar", UserVarComp)
	self:_addComponent("mover", UnitCompMoverLerp)
	self:_addComponent("followUIComp", UnitCompFollowUI)
	self:_addComponent("skinView", PlayerSkinView)
	self:_addComponent("effectLoader", EffectLoader)
	self:_addComponent("nameBarContainer", NameBarContainerComp)
	self:_addComponent("nameBar", NameBarComp)
	self:_addComponent("shareItem", ShareItemCom)
	self:_addComponent("poseComp", PoseComp)
	self:_addComponent("posCtrl", ScenePosComp)
	self:_addComponent("talkComp", TalkComp)
	self:_addComponent("treasureHunt", TreasureHuntComp)
	-- self:_addComponent("taskComp", TaskComp)
	self:_addComponent("playChatComp", PlayerChatComp)
	self:_addComponent("actionComp", PlayerActionComp)
	self:_addComponent("idleComp", IdlePoseComp)
	-- self:_addComponent("unitFollow", UnitCompRoleFollow)
	self:_addComponent("illumComp", IlluminationComp)
	self:_addComponent("shadow", ShadowComp)
	self:_addComponent("playingMusicComp", PlayingMusicComp)
	self:_addComponent("walkSoundComp", WalkSoundComp)
	self:_addComponent("customSkin", CustomSkinComp)
	self:_addComponent("handThrowItem", HandThrowItemComp)
	self:_addComponent("walkEffect", WalkEffectComp)
	self:_addComponent("headTips", HeadTipsComp)
	self:_addComponent("followComp", FollowUnitComp)
end

function PlayerUnit:OnDestroy()
	self:_removeListeners()
	PlayerUnit.super.OnDestroy(self)
end

function PlayerUnit:_addListeners()
	self.userVar:addListener(UserVarKey.NickName, self.updateNickName, self)
	self.userVar:addListener(UserVarKey.Title, self.updateTitle, self)
	self.userVar:addListener(UserVarKey.Clothes, self.setClothes, self)
	self.userVar:addListener(UserVarKey.SceneRoleState, self.setAction, self)
	self.userVar:addListener(UserVarKey.Pos, self.handleMove, self)
	self.userVar:addListener(UserVarKey.SceneRolePose, self._setShortAction, self)
	self.userVar:addListener(UserVarKey.ModelType, self.setModelType, self)
	self.userVar:addListener(UserVarKey.HeadTips, self.setHeadTips, self)
	self.userVar:addListener(UserVarKey.VipCardType, self.updateMember, self)
	self.userVar:addListener(UserVarKey.StartMatchTeam, self.updateStartMatchTeam, self)

end

function PlayerUnit:_removeListeners()
	self.userVar:removeListener(UserVarKey.NickName, self.updateNickName, self)
	self.userVar:removeListener(UserVarKey.Title, self.updateTitle, self)
	self.userVar:removeListener(UserVarKey.Clothes, self.setClothes, self)
	self.userVar:removeListener(UserVarKey.SceneRoleState, self.setAction, self)
	self.userVar:removeListener(UserVarKey.Pos, self.handleMove, self)
	self.userVar:removeListener(UserVarKey.SceneRolePose, self._setShortAction, self)
	self.userVar:removeListener(UserVarKey.ModelType, self.setModelType, self)
	self.userVar:removeListener(UserVarKey.HeadTips, self.setHeadTips, self)
	self.userVar:removeListener(UserVarKey.VipCardType, self.updateMember, self)
	self.userVar:removeListener(UserVarKey.StartMatchTeam, self.updateStartMatchTeam, self)
end

function PlayerUnit:setFace(isFront)
	self:setDirection(UnitDirection.setFrontBack(self.dir, isFront))
end

function PlayerUnit:lockDirection(value)
	self._lockDirection = value
end

function PlayerUnit:lockMove(value)
	self._lockMove = value
end

-- -- return 是否向右，是否向前
-- function PlayerUnit:getFace()
-- 	return PlayerUnit.DirectionSetting[self.dir][1], PlayerUnit.DirectionSetting[self.dir][2]
-- end

function PlayerUnit:setDirection(dir)
	if self._lockDirection then
		return
	end
	if self._alwaysFront then
		dir = dir > 1 and dir - 2 or dir
	end
	self.dir = dir
	self:_refreshViewDir()
	self.walkEffect:onSetDirection()
	self.playingMusicComp:onSetDirection()
	self:dispatch(UnitNotify.DirectionChange)
end

function PlayerUnit:lookTo(tx, ty)
	self:setFace(true)
	self.posCtrl:setNewDir(tx, ty)
end

function PlayerUnit:getDirection()
	return self.dir
end

function PlayerUnit:onStartMove()
	self.actionComp:startMove()
	self.walkSoundComp:startMove(self:getSpeed())
	self.customSkin:startMove()
	self.walkEffect:startMove()
	self:dispatch(UnitNotify.StartMove, self)
end

function PlayerUnit:onStopMove()
	self.actionComp:stopMove()
	self.walkSoundComp:stopMove()
	self.customSkin:stopMove()
	self.walkEffect:stopMove()
end

function PlayerUnit:isMoving()
	return self.mover:getCurWayPoint() ~= nil
end

--param isManual:bool 是否是手动调用，一些需要本地调用又不需要网络回调的方法使用，在ignoreList里面声明需要忽略自身的网络调用
function PlayerUnit:setAction(info, isInit, isManual)
	if not isManual then
		if info ~= nil then
			local infoId = info.id
			if (infoId == UserInfo.userId) and self:_isIgnoreAction(info.state) then
				return
			end
		end
	end

	self.actionComp:setAction(info, isInit)
	-- if self.isUser then
	-- local type = info and PlayerActionType.getTypeById(info.state) or PlayerActionType.Idle
	-- printInfo("GlobalNotify.PlayerActionChange", type.id)
	-- GlobalDispatcher:dispatch(GlobalNotify.PlayerActionChange, type)
	-- end
end

PlayerUnit.ignoreList = {16,59,61,62}

function PlayerUnit:_isIgnoreAction(id)
	--在此处声明
	if not self._ignoreMap then
		self._ignoreMap = {}
		for i, v in ipairs(PlayerUnit.ignoreList) do
			self._ignoreMap[v] = true
		end
	end

	return self._ignoreMap[id]
end

function PlayerUnit:_setShortAction(info, isInit)
	if info then
		self:setAction(info, isInit)
	end
end

function PlayerUnit:addAnimation(aniName, isLoop, backToIdle, isForceRestart)
	self.skinView:addAnimation(aniName, isLoop, isForceRestart)
	if not isLoop and backToIdle then
		self.skinView:addAnimation(self:getIdleAniName(), true)
	end
end

function PlayerUnit:playAnimation(aniName, isLoop, backToIdle, isForceRestart, isSkipWhenAniNotFound)
	self.skinView:setAnimation(aniName, isLoop, isForceRestart, isSkipWhenAniNotFound)
	if not isLoop and backToIdle then
		self.skinView:addAnimation(self:getIdleAniName(), true)
	end
end

function PlayerUnit:getIdleAniName()
	if self.specifiedIdleAni then
		return self.specifiedIdleAni
	end
	return self._walkAniConfig.idleName
end

function PlayerUnit:getWalkAniName()
	if self.specifiedWalkAni then
		return self.specifiedWalkAni
	end
	if self._speed > 3 then
		return self._walkAniConfig.runName
	else
		return self._walkAniConfig.walkName
	end
end

function PlayerUnit:specificWalkAni(aniName)
	self.specifiedWalkAni = aniName
end

function PlayerUnit:specificIdleAni(aniName)
	self.specifiedIdleAni = aniName
end

function PlayerUnit:playSpeedUpEffect()
	if not EffectLoader or not EffectLoader.slots then
		return
	end
	if not self.speedUpEffect then
		self.speedUpEffect = self:getComponent(EffectLoader):getSlot(EffectLoader.SpeedUpEffect)
		self.speedUpEffect:load("prefabs/tinygame/snowball/model_run_effect_01.prefab")
	end
	goutil.setActive(self.speedUpEffect:getGO(), false)
	goutil.setActive(self.speedUpEffect:getGO(), true)
end

function PlayerUnit:onArrive()
	self:onStopMove()
	self:dispatch(UnitNotify.Arrive, self)
end

function PlayerUnit:onInterrupt()
	-- self:onStopMove()
	self:dispatch(UnitNotify.Interrupt, self)
	-- printInfo("onInterrupt")
end

function PlayerUnit:onSetInfo(info, isReload)
	self.isUser = self.id == UserInfo.userId
	if self.isUser then
		self.indexId = 0
	end
	if not isReload then
		self:lockDirection(false)
		self:lockMove(false)
		self:specificIdleAni()
		self:specificWalkAni()
		GlobalDispatcher:addListener(GlobalNotify.ShowNameSettingChange, self.updateShowNameStatus, self)
		FriendController.instance:registerLocalNotify(FriendNotify.OnAddBlack, self._onAddBlack, self)
	end
	if not HouseModel.instance:isInHouse() then
		local timeFactor
		if SceneManager.instance:getCurSceneId() == 95 then
			timeFactor = 0.4
		else
			timeFactor = 0.6
		end		
		self.illumComp:setParams(timeFactor, true)
	else
		self.effectLoader:setNightFactor(0)
	end
	self.userVar:initVar(info.roleInfoField)
	self:_refreshViewDir()
	-- self.skinView:hideShadow()
	if not HouseModel.instance:isInHouse() then
		self.illumComp:addLitGO(self.skinView.skinFrontGO)
		self.illumComp:addLitGO(self.skinView.skinBackGO)
	end
	self.skinView:playAdditionalAni()
	self.shadow:setVisible(true)
	local aniList
	if self:getSex() == 0 then
		aniList = PlayerUnit.IdleAniList_boy
	elseif self:getSex() == 1 then
		aniList = PlayerUnit.IdleAniList_girl
	else
		aniList = PlayerUnit.IdleAniList_None
	end
	self.idleComp:setInfo(aniList)
	self:updateShowNameStatus()
	-- self.nameBar:showNameGO(self.userVar:getVariable(UserVarKey.TalentGuideIdendity) == GameEnum.GuideTalentType.NEW_PLAYER)
	self.nameBar:showNameGO("imgReturn", self.userVar:getVariable(UserVarKey.ReturnSign) == 1)
	self.nameBar:showNameGO("imgHelp", self.userVar:getVariable(UserVarKey.TalentGuideIdendity) == GameEnum.GuideTalentType.NEW_PLAYER)
	self.isBlackUser = FriendService.instance:queryIsInBlack(self.id)
end

function PlayerUnit:updateShowNameStatus()
	local isShowName 
	if self.isUser then
		isShowName = SettingService.instance:getShowNameSetting(SettingService.ShowSelfName)
	else
		isShowName = SettingService.instance:getShowNameSetting(SettingService.ShowOtherName)
	end
	self:getComponent(NameBarComp):setNameVisible(isShowName)
	local isShowTitle
	if self.isUser then
		isShowTitle = SettingService.instance:getShowNameSetting(SettingService.ShowSelfTitle)
	else
		isShowTitle = SettingService.instance:getShowNameSetting(SettingService.ShowOtherTitle)
	end
	self:getComponent(NameBarComp):setTitleVisible(isShowTitle)
end

function PlayerUnit:updateNickName(nickName)
	local color
	if self.isUser then
		color = parsecolor("#ffd71d")
	else
		if FriendService.instance:queryIsFriend(self.id) then
			color = parsecolor("#8fccff")
		else
			color = parsecolor("#f7f6f1")
		end
	end
	self.nameBar:setName(nickName, color)
end

function PlayerUnit:updateMember()
	self.nameBar:setIsMember(self:getIsRubyMember())
end

function PlayerUnit:updateTitle(titleId)
	self.nameBar:setTitle(titleId)
end

--星际大赛队伍icon
function PlayerUnit:updateStartMatchTeam(teamId)
	self.nameBar:setStartMatchTeamIcon(teamId)
end

function PlayerUnit:onReset()
	self:dispatch(UnitNotify.Remove, self)
	GlobalDispatcher:removeListener(GlobalNotify.ShowNameSettingChange, self.updateShowNameStatus, self)
	FriendController.instance:unregisterLocalNotify(FriendNotify.OnAddBlack, self._onAddBlack, self)	
	self._petUnit = nil
	self:setMount(nil)
	self.walkSoundComp:stopMove()
	--if self.walkSound then
	--		SoundManager.instance:stopEffect(self.walkSound)
	--		self.walkSound = false
	--end
end

function PlayerUnit:getSpeed()
	if not self._speed then
		self:setSpeed(SceneConfig.GetPlayerSpeed())
	end
	return self._speed
end

function PlayerUnit:setSpeed(speed)
	self._speed = speed
	self.mover:setSpeed(speed)
	-- self.walkEffect:setSpeed(speed)
end

function PlayerUnit:handleMove(moveInfo, isInit)
	-- printInfo("handleMove", moveInfo.moveType)
	if self._lockMove or not moveInfo then
		return
	end
	local moveType = moveInfo.moveType
	if moveType == 0 then
		if #moveInfo.targetPos > 1 then
			--点击场景移动
			self:setPath(moveInfo.targetPos,moveInfo.moveStartMillis, isInit)
		else
			--摇杆移动
			local targetPos = moveInfo.targetPos[1]
			local elapsedTime = GameUtils.getServerTimeStamp() - moveInfo.moveStartMillis
			if isInit or elapsedTime > 1000 then
				self:setPos(targetPos.posX, targetPos.posY)
			else
				self:moveTo(targetPos.posX, targetPos.posY)
			end
		end
	elseif moveType == 1 then
		--瞬移
		local targetPos = moveInfo.targetPos[1]
		local z
		--若targetPos是protobuf对象，字段有默认值则，要判断字段是否存在，不能直接取
		if not targetPos.HasField or targetPos:HasField("posZ") then
			z = targetPos.posZ
		end
		self:teleport(targetPos.posX, targetPos.posY, z)
	elseif moveType == 3 then
		--飞行
		local targetPos = moveInfo.targetPos[1]
		if #moveInfo.targetPos > 1 then
			targetPos = moveInfo.targetPos[2]
		end
		local elapsedTime = GameUtils.getServerTimeStamp() - moveInfo.moveStartMillis
		if isInit or elapsedTime > 1000 then
			self:setPos(targetPos.posX, targetPos.posY,targetPos.posZ)
		else
			local z
			--若targetPos是protobuf对象，字段有默认值则，要判断字段是否存在，不能直接取
			if not targetPos.HasField or targetPos:HasField("posZ") then
				z = targetPos.posZ
			end
			--self:setNavEnable(false)
			self.mover:setWayPoint(targetPos.posX,targetPos.posY,z)
		end
	else
		--特殊动作，例如跳
		local targetPos = moveInfo.targetPos[1]
		if isInit then
			self:setPos(targetPos.posX, targetPos.posY)
		else
			local z
			if not targetPos.HasField or targetPos:HasField("posZ") then
				z = targetPos.posZ
			end
			self:specialMove(targetPos.posX, targetPos.posY, moveType, z)
		end
	end
end

function PlayerUnit:setPath(path, beginTime, isInit)
	self.mover:setSpeed(self._speed)
	self.posCtrl:setPath(path, beginTime, isInit)
end

function PlayerUnit:moveTo(x, y, speed)
	if speed then
		self.mover:setSpeed(speed)
	else
		self.mover:setSpeed(self._speed)
	end
	self.posCtrl:moveTo(x, y)
end

function PlayerUnit:walkTo(x, y, speed, range, canJump)
	self:setNavEnable(true)
	if speed then
		self.mover:setSpeed(speed)
	else
		self.mover:setSpeed(self._speed)
	end
	return self.posCtrl:walkTo(x, y, range, canJump)
end

function PlayerUnit:specialMove(x, y, type, z)
	self.posCtrl:stop()
	self.actionComp:setAction({state = PlayerActionType.SpecialMove.typeId, x = x, y = y, z = z, type = type}, false)
end

function PlayerUnit:throw(itemId, worldPos)
	self.posCtrl:stop()
	local throwType = 0 --无后端功能
	local isThrowSucc = 1 --是否投掷成功（无后端功能可不管）
	self.actionComp:setAction(
		{
			state = PlayerActionType.Throw.typeId,
			params = {itemId, throwType, isThrowSucc, worldPos.x, worldPos.y, worldPos.z}
		},
		false
	)
end

function PlayerUnit:finishMove()
	self.posCtrl:finishMove()
end

function PlayerUnit:stop()
	self.posCtrl:stop()
end

function PlayerUnit:setPos(x, y, z)
	self:setNavEnable(false)
	-- self.posCtrl:setPos(x, y)
	local hasZ = z ~= nil
	if VirtualCameraMgr.instance:is3DScene() then
		if not hasZ then
			z = GameUtils.GetPosYOnGround(x, y)
		end
		-- tfutil.SetY(self.go, z)
		self.posCtrl:setPos(x, y, z)
		if not self:getMount() then
			self:setNavEnable(not hasZ)
		end
	else
		self.posCtrl:setPos(x, y, z)
	end
end

function PlayerUnit:setNavEnable(value)
	if self.navAgent then
		if self._navEnable ~= value then
			self._navEnable = value
			self.navAgent.enabled = value
		end
	end
end

function PlayerUnit:teleport(x, y, z)
	print("teleport", x, y, z)
	self:setPos(x, y, z)
	self:dispatch(UnitNotify.Arrive, self)
end

function PlayerUnit:getPos()
	return self.mover:getPos()
end

function PlayerUnit:setClothes(ids)
	if ids then
		self.avatar:setAvatar(ids)
		if self:getCanSee() then
			self:_refreshViewClothes()
		else
			self.outdateMap[1] = true
		end
		self:dispatch(UnitNotify.ClothesChange, self)
	end
	self._walkAniConfig = ClothesConfig.getWalkAniByAvatar(self.avatar)
	self.walkSoundComp:setWalkSound(self._walkAniConfig.walkSound)

end

function PlayerUnit:setModelType(type)
	type = type or ModelType.Bear
	self.skinView:setModelType(type, true)
	self.skinView:setShowLargeEffect(true)
	if type == ModelType.Bear then
		self.followUIComp:setOffset(UnitFollowUIType.Top, 0, 1.4)
		self.skinView:setScale(0.39)
	else
		self.followUIComp:setOffset(UnitFollowUIType.Top, 0, 1.5)
		self.skinView:setScale(0.4)
	end
end

function PlayerUnit:setHeadTips(info)
	self:getComponent(HeadTipsComp):setHeadTips(info)
end

function PlayerUnit:showTalk(msg)
	self.talkComp:showTalk(msg)
end

function PlayerUnit:hideTalk()
	self.talkComp:hideTalk()
end

function PlayerUnit:getSex()
	return self.userVar:getVariable(UserVarKey.Sex)
end

function PlayerUnit:getNickname()
	return self.userVar:getVariable(UserVarKey.NickName)
end

function PlayerUnit:getIsRubyMember()
	local vipLv = self.userVar:getVariable(UserVarKey.VipCardType)
	return vipLv ~= nil and vipLv > -1
end

function PlayerUnit:setCustomSkin(url, callback, target, offsetNameY)
	self.skinView:setModelVisible("customSkin", false)
	self.customSkin:setSkin(url, callback, target)
	if offsetNameY ~= nil then
		self.followUIComp:setOffset(UnitFollowUIType.Top, 0, offsetNameY)
	end
end

function PlayerUnit:removeCustomSkin()
	--这里可能设置了名字的高度，需要还原一下
	if self.skinView.modelType == ModelType.Bear then
		self.followUIComp:setOffset(UnitFollowUIType.Top, 0, 1.4)
	else
		self.followUIComp:setOffset(UnitFollowUIType.Top, 0, 1.5)
	end
	self.skinView:setModelVisible("customSkin", true)
	self.customSkin:clear()
end
-- function PlayerUnit:setSortingIndex(index)
-- 	self.skinView:setSortingIndex(index)
-- end

-- function PlayerUnit:update()
-- 	self.mover:update()
-- 	-- self.unitFollow:update()
-- end

function PlayerUnit:onSetCanSee(value)
	self.nameBarContainer:setEnable(value)
	self.followUIComp:setEnbale(value)
	if value then
		self:_refreshView()
	end
end

-- function PlayerUnit:setFollowUnit(unit)
-- 	self.unitFollow:setFollowUnit(unit)
-- end

function PlayerUnit:_refreshView()
	if self.outdateMap[1] then
		self:_refreshViewClothes()
		self.outdateMap[1] = false
	end
	-- if self.outdateMap[2] then
	-- 	self:_refreshViewDir()
	-- 	self.outdateMap[2] = false
	-- end
end

function PlayerUnit:_refreshViewClothes()
	self.skinView:setClothes(self.avatar, self.isUser)
end

function PlayerUnit:_refreshViewDir()
	local setting = UnitDirection.getFaceSetting(self.dir)
	self.skinView:setFilpX(setting[1])
	-- tfutil.SetScaleX(self.effectGO, setting[1] and 1 or -1)
	self.skinView:setFace(setting[2])
	self.customSkin:setDirection(self.dir)
end

function PlayerUnit:OnTriggerEnter(collider)
	if self._triggerEnable ~= false then
		SceneTriggerMgr.instance:onTriggerEnter(collider, self)
	else
		GlobalDispatcher:dispatch(GlobalNotify.PlayerColliderTriggerBan, true, collider, self)
	end
end

function PlayerUnit:OnTriggerExit(collider)
	if self._triggerEnable ~= false then
		SceneTriggerMgr.instance:onTriggerExit(collider, self)
	else
		GlobalDispatcher:dispatch(GlobalNotify.PlayerColliderTriggerBan, false, collider, self)
	end
end

--设置是否可点击
function PlayerUnit:setClickable(key, canClick)
	if canClick then
		self.clickSignal:remove(key)
	else
		self.clickSignal:add(key)
	end
	local visible = self.clickSignal:isUnactive()
	if visible then
		self.go.layer = self:getLayer()
	else
		self.go.layer = Framework.LayerUtil.NameToLayer(SceneLayer.Water)
	end
end

--设置角色的碰撞点击
function PlayerUnit:setTriggerEnable(value)
	if goutil.isNil(self.go) == false then
		--为了脚步声，这里不可以设置enable，用逻辑来代替
		self._triggerEnable = value
		-- self.go:GetComponent(typeof(UnityEngine.Collider)).enabled = value
	end
end

function PlayerUnit:_onAddBlack()
	self.isBlackUser = FriendService.instance:queryIsInBlack(self.id)
end

--设置强行正面
function PlayerUnit:setAlwaysFront(value)
	self._alwaysFront = value
end

function PlayerUnit:setMount(mount)
	self.noRotation = mount ~= nil
	self.posCtrl:setMount(mount)
end

function PlayerUnit:getMount()
	return self.posCtrl:getMount()
end

return PlayerUnit
