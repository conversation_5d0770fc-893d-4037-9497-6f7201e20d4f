-- {excel:R每日任务表.xlsx, sheetName:export_每日任务步骤}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_daily_task_step", package.seeall)

local title = {id=1,taskId=2,desc=3,npc=4,objectives=5,objectiveDescriptions=6,triggerType=7,triggerParams=8,dialog=9,hideNpc=10,showNpc=11,npcByTargets=12,transferZone=13}

local dataList = {
	{410001001, 410001, "没看到需要显示，不配了", 1, {{type=17,target={num=3}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{410002001, 410002, "没看到需要显示，不配了", 1, {{type=84,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{410003001, 410003, "没看到需要显示，不配了", 1, {{type=119,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{410004001, 410004, "没看到需要显示，不配了", 1, {{type=120,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{410005001, 410005, "没看到需要显示，不配了", 1, {{type=121,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420001001, 420001, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000001}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420002001, 420002, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000002}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420003001, 420003, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000003}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420004001, 420004, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000004}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420005001, 420005, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000005}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420006001, 420006, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000006}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420007001, 420007, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000007}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420008001, 420008, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000008}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420009001, 420009, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000009}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420010001, 420010, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000010}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420011001, 420011, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000011}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420012001, 420012, "没看到需要显示，不配了", 1, {{type=5,target={num=50,id=14000012}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420013001, 420013, "没看到需要显示，不配了", 1, {{type=18,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420014001, 420014, "没看到需要显示，不配了", 1, {{type=13,target={num=3}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420015001, 420015, "没看到需要显示，不配了", 1, {{type=14,target={num=3}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420016001, 420016, "没看到需要显示，不配了", 1, {{type=19,target={num=3}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420017001, 420017, "没看到需要显示，不配了", 1, {{type=9,target={num=20,id=14000503}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420018001, 420018, "没看到需要显示，不配了", 1, {{type=9,target={num=20,id=14000507}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420019001, 420019, "没看到需要显示，不配了", 1, {{type=38,target={num=2}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420020001, 420020, "没看到需要显示，不配了", 1, {{type=70,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{420021001, 420021, "没看到需要显示，不配了", 1, {{type=15,target={num=5}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430001001, 430001, "没看到需要显示，不配了", 1, {{type=1,target={num=10,id=3}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430002001, 430002, "没看到需要显示，不配了", 1, {{type=21,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430003001, 430003, "没看到需要显示，不配了", 1, {{type=22,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430004001, 430004, "没看到需要显示，不配了", 1, {{type=54,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430005001, 430005, "没看到需要显示，不配了", 1, {{type=23,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430006001, 430006, "没看到需要显示，不配了", 1, {{type=45,target={num=2}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430007001, 430007, "没看到需要显示，不配了", 1, {{type=41,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430008001, 430008, "没看到需要显示，不配了", 1, {{type=43,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430009001, 430009, "没看到需要显示，不配了", 1, {{type=157,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430010001, 430010, "没看到需要显示，不配了", 1, {{type=75,target={num=2}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430011001, 430011, "没看到需要显示，不配了", 1, {{type=77,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430012001, 430012, "没看到需要显示，不配了", 1, {{type=78,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430013001, 430013, "没看到需要显示，不配了", 1, {{type=87,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430014001, 430014, "没看到需要显示，不配了", 1, {{type=71,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430015001, 430015, "没看到需要显示，不配了", 1, {{type=80,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{430016001, 430016, "没看到需要显示，不配了", 1, {{type=192,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{440001001, 440001, "没看到需要显示，不配了", 1, {{type=169,target={num=1,type=2}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{440002001, 440002, "没看到需要显示，不配了", 1, {{type=169,target={num=1,type=8}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{450001001, 450001, "没看到需要显示，不配了", 1, {{type=46,target={num=2}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{450002001, 450002, "没看到需要显示，不配了", 1, {{type=10029}}, nil, 0, 0, "", nil, nil, nil, 0},
	{460001001, 460001, "没看到需要显示，不配了", 1, {{type=140,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{460002001, 460002, "没看到需要显示，不配了", 1, {{type=53,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{460003001, 460003, "没看到需要显示，不配了", 1, {{type=60,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{460004001, 460004, "没看到需要显示，不配了", 1, {{type=61,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{460005001, 460005, "没看到需要显示，不配了", 1, {{type=2021,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{460006001, 460006, "没看到需要显示，不配了", 1, {{type=200,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{460007001, 460007, "没看到需要显示，不配了", 1, {{type=205,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470001001, 470001, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470002001, 470002, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470003001, 470003, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470004001, 470004, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470005001, 470005, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470006001, 470006, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470007001, 470007, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470008001, 470008, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470009001, 470009, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470010001, 470010, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470011001, 470011, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470012001, 470012, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470013001, 470013, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470014001, 470014, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470015001, 470015, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470016001, 470016, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470017001, 470017, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470018001, 470018, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470019001, 470019, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470020001, 470020, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470021001, 470021, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470022001, 470022, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470023001, 470023, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470024001, 470024, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470025001, 470025, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470026001, 470026, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470027001, 470027, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470028001, 470028, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470029001, 470029, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470030001, 470030, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470031001, 470031, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470032001, 470032, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470033001, 470033, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470034001, 470034, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470035001, 470035, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470036001, 470036, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470037001, 470037, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470038001, 470038, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470039001, 470039, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470040001, 470040, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470041001, 470041, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470042001, 470042, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470043001, 470043, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470044001, 470044, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470045001, 470045, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470046001, 470046, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470047001, 470047, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470048001, 470048, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470049001, 470049, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470050001, 470050, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470051001, 470051, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470052001, 470052, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470053001, 470053, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470054001, 470054, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470055001, 470055, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470056001, 470056, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470057001, 470057, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470058001, 470058, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470059001, 470059, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470060001, 470060, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470061001, 470061, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470062001, 470062, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470063001, 470063, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470064001, 470064, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470065001, 470065, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470066001, 470066, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470067001, 470067, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470068001, 470068, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470069001, 470069, "没看到需要显示，不配了", 1, {{type=10034}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470070001, 470070, "没看到需要显示，不配了", 1, {{type=2041,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470071001, 470071, "没看到需要显示，不配了", 1, {{type=10030}}, nil, 0, 0, "", nil, nil, nil, 0},
	{470072001, 470072, "没看到需要显示，不配了", 1, {{type=2034,target={num=1}}}, nil, 0, 0, "", nil, nil, nil, 0},
}

local t_daily_task_step = {
	[410001001] = dataList[1],
	[410002001] = dataList[2],
	[410003001] = dataList[3],
	[410004001] = dataList[4],
	[410005001] = dataList[5],
	[420001001] = dataList[6],
	[420002001] = dataList[7],
	[420003001] = dataList[8],
	[420004001] = dataList[9],
	[420005001] = dataList[10],
	[420006001] = dataList[11],
	[420007001] = dataList[12],
	[420008001] = dataList[13],
	[420009001] = dataList[14],
	[420010001] = dataList[15],
	[420011001] = dataList[16],
	[420012001] = dataList[17],
	[420013001] = dataList[18],
	[420014001] = dataList[19],
	[420015001] = dataList[20],
	[420016001] = dataList[21],
	[420017001] = dataList[22],
	[420018001] = dataList[23],
	[420019001] = dataList[24],
	[420020001] = dataList[25],
	[420021001] = dataList[26],
	[430001001] = dataList[27],
	[430002001] = dataList[28],
	[430003001] = dataList[29],
	[430004001] = dataList[30],
	[430005001] = dataList[31],
	[430006001] = dataList[32],
	[430007001] = dataList[33],
	[430008001] = dataList[34],
	[430009001] = dataList[35],
	[430010001] = dataList[36],
	[430011001] = dataList[37],
	[430012001] = dataList[38],
	[430013001] = dataList[39],
	[430014001] = dataList[40],
	[430015001] = dataList[41],
	[430016001] = dataList[42],
	[440001001] = dataList[43],
	[440002001] = dataList[44],
	[450001001] = dataList[45],
	[450002001] = dataList[46],
	[460001001] = dataList[47],
	[460002001] = dataList[48],
	[460003001] = dataList[49],
	[460004001] = dataList[50],
	[460005001] = dataList[51],
	[460006001] = dataList[52],
	[460007001] = dataList[53],
	[470001001] = dataList[54],
	[470002001] = dataList[55],
	[470003001] = dataList[56],
	[470004001] = dataList[57],
	[470005001] = dataList[58],
	[470006001] = dataList[59],
	[470007001] = dataList[60],
	[470008001] = dataList[61],
	[470009001] = dataList[62],
	[470010001] = dataList[63],
	[470011001] = dataList[64],
	[470012001] = dataList[65],
	[470013001] = dataList[66],
	[470014001] = dataList[67],
	[470015001] = dataList[68],
	[470016001] = dataList[69],
	[470017001] = dataList[70],
	[470018001] = dataList[71],
	[470019001] = dataList[72],
	[470020001] = dataList[73],
	[470021001] = dataList[74],
	[470022001] = dataList[75],
	[470023001] = dataList[76],
	[470024001] = dataList[77],
	[470025001] = dataList[78],
	[470026001] = dataList[79],
	[470027001] = dataList[80],
	[470028001] = dataList[81],
	[470029001] = dataList[82],
	[470030001] = dataList[83],
	[470031001] = dataList[84],
	[470032001] = dataList[85],
	[470033001] = dataList[86],
	[470034001] = dataList[87],
	[470035001] = dataList[88],
	[470036001] = dataList[89],
	[470037001] = dataList[90],
	[470038001] = dataList[91],
	[470039001] = dataList[92],
	[470040001] = dataList[93],
	[470041001] = dataList[94],
	[470042001] = dataList[95],
	[470043001] = dataList[96],
	[470044001] = dataList[97],
	[470045001] = dataList[98],
	[470046001] = dataList[99],
	[470047001] = dataList[100],
	[470048001] = dataList[101],
	[470049001] = dataList[102],
	[470050001] = dataList[103],
	[470051001] = dataList[104],
	[470052001] = dataList[105],
	[470053001] = dataList[106],
	[470054001] = dataList[107],
	[470055001] = dataList[108],
	[470056001] = dataList[109],
	[470057001] = dataList[110],
	[470058001] = dataList[111],
	[470059001] = dataList[112],
	[470060001] = dataList[113],
	[470061001] = dataList[114],
	[470062001] = dataList[115],
	[470063001] = dataList[116],
	[470064001] = dataList[117],
	[470065001] = dataList[118],
	[470066001] = dataList[119],
	[470067001] = dataList[120],
	[470068001] = dataList[121],
	[470069001] = dataList[122],
	[470070001] = dataList[123],
	[470071001] = dataList[124],
	[470072001] = dataList[125],
}

t_daily_task_step.dataList = dataList
local mt
if DailyTaskStepDefine then
	mt = {
		__cname =  "DailyTaskStepDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or DailyTaskStepDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_daily_task_step