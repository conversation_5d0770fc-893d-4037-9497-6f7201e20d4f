--auto-generated
usingnow("logic.proto.AchievementExtension_pb", "AchievementExtension_pb")
usingnow("logic.proto.Activity101Extension_pb", "Activity101Extension_pb")
usingnow("logic.proto.Activity102Extension_pb", "Activity102Extension_pb")
usingnow("logic.proto.Activity103Extension_pb", "Activity103Extension_pb")
usingnow("logic.proto.Activity107Extension_pb", "Activity107Extension_pb")
usingnow("logic.proto.Activity109Extension_pb", "Activity109Extension_pb")
usingnow("logic.proto.Activity110Extension_pb", "Activity110Extension_pb")
usingnow("logic.proto.Activity111Extension_pb", "Activity111Extension_pb")
usingnow("logic.proto.Activity114Extension_pb", "Activity114Extension_pb")
usingnow("logic.proto.Activity115Extension_pb", "Activity115Extension_pb")
usingnow("logic.proto.Activity116Extension_pb", "Activity116Extension_pb")
usingnow("logic.proto.Activity117Extension_pb", "Activity117Extension_pb")
usingnow("logic.proto.Activity122Extension_pb", "Activity122Extension_pb")
usingnow("logic.proto.Activity124Extension_pb", "Activity124Extension_pb")
usingnow("logic.proto.Activity125Extension_pb", "Activity125Extension_pb")
usingnow("logic.proto.Activity126Extension_pb", "Activity126Extension_pb")
usingnow("logic.proto.Activity128Etrension_pb", "Activity128Etrension_pb")
usingnow("logic.proto.Activity128Extension_pb", "Activity128Extension_pb")
usingnow("logic.proto.Activity130Extension_pb", "Activity130Extension_pb")
usingnow("logic.proto.Activity131Extension_pb", "Activity131Extension_pb")
usingnow("logic.proto.Activity132Extension_pb", "Activity132Extension_pb")
usingnow("logic.proto.Activity133Extension_pb", "Activity133Extension_pb")
usingnow("logic.proto.Activity135Extension_pb", "Activity135Extension_pb")
usingnow("logic.proto.Activity138Extension_pb", "Activity138Extension_pb")
usingnow("logic.proto.Activity140Extension_pb", "Activity140Extension_pb")
usingnow("logic.proto.Activity142Extension_pb", "Activity142Extension_pb")
usingnow("logic.proto.Activity143Extension_pb", "Activity143Extension_pb")
usingnow("logic.proto.Activity144Extension_pb", "Activity144Extension_pb")
usingnow("logic.proto.Activity145Extension_pb", "Activity145Extension_pb")
usingnow("logic.proto.Activity146Extension_pb", "Activity146Extension_pb")
usingnow("logic.proto.Activity147Extension_pb", "Activity147Extension_pb")
usingnow("logic.proto.Activity148Extension_pb", "Activity148Extension_pb")
usingnow("logic.proto.Activity150Extension_pb", "Activity150Extension_pb")
usingnow("logic.proto.Activity151Extension_pb", "Activity151Extension_pb")
usingnow("logic.proto.Activity152Extension_pb", "Activity152Extension_pb")
usingnow("logic.proto.Activity153Extension_pb", "Activity153Extension_pb")
usingnow("logic.proto.Activity156Extension_pb", "Activity156Extension_pb")
usingnow("logic.proto.Activity157Extension_pb", "Activity157Extension_pb")
usingnow("logic.proto.Activity158Extension_pb", "Activity158Extension_pb")
usingnow("logic.proto.Activity159Extension_pb", "Activity159Extension_pb")
usingnow("logic.proto.Activity160Extension_pb", "Activity160Extension_pb")
usingnow("logic.proto.Activity161Extension_pb", "Activity161Extension_pb")
usingnow("logic.proto.Activity163Extension_pb", "Activity163Extension_pb")
usingnow("logic.proto.Activity166Extension_pb", "Activity166Extension_pb")
usingnow("logic.proto.Activity170Extension_pb", "Activity170Extension_pb")
usingnow("logic.proto.Activity173Extension_pb", "Activity173Extension_pb")
usingnow("logic.proto.Activity175Extension_pb", "Activity175Extension_pb")
usingnow("logic.proto.Activity178Extension_pb", "Activity178Extension_pb")
usingnow("logic.proto.Activity180Extension_pb", "Activity180Extension_pb")
usingnow("logic.proto.Activity183Extension_pb", "Activity183Extension_pb")
usingnow("logic.proto.Activity188Extension_pb", "Activity188Extension_pb")
usingnow("logic.proto.Activity190Extension_pb", "Activity190Extension_pb")
usingnow("logic.proto.Activity191Extension_pb", "Activity191Extension_pb")
usingnow("logic.proto.Activity192Extension_pb", "Activity192Extension_pb")
usingnow("logic.proto.Activity193Extension_pb", "Activity193Extension_pb")
usingnow("logic.proto.Activity194Extension_pb", "Activity194Extension_pb")
usingnow("logic.proto.Activity195Extension_pb", "Activity195Extension_pb")
usingnow("logic.proto.Activity196Extension_pb", "Activity196Extension_pb")
usingnow("logic.proto.Activity198Extension_pb", "Activity198Extension_pb")
usingnow("logic.proto.Activity199Extension_pb", "Activity199Extension_pb")
usingnow("logic.proto.Activity200Extension_pb", "Activity200Extension_pb")
usingnow("logic.proto.Activity202Extension_pb", "Activity202Extension_pb")
usingnow("logic.proto.Activity204Extension_pb", "Activity204Extension_pb")
usingnow("logic.proto.Activity205Extension_pb", "Activity205Extension_pb")
usingnow("logic.proto.Activity207Extension_pb", "Activity207Extension_pb")
usingnow("logic.proto.Activity209Extension_pb", "Activity209Extension_pb")
usingnow("logic.proto.Activity211Extension_pb", "Activity211Extension_pb")
usingnow("logic.proto.Activity213Extension_pb", "Activity213Extension_pb")
usingnow("logic.proto.Activity214Extension_pb", "Activity214Extension_pb")
usingnow("logic.proto.Activity215Extension_pb", "Activity215Extension_pb")
usingnow("logic.proto.Activity217Extension_pb", "Activity217Extension_pb")
usingnow("logic.proto.Activity218Extension_pb", "Activity218Extension_pb")
usingnow("logic.proto.Activity219Extension_pb", "Activity219Extension_pb")
usingnow("logic.proto.Activity220Extension_pb", "Activity220Extension_pb")
usingnow("logic.proto.Activity221Extension_pb", "Activity221Extension_pb")
usingnow("logic.proto.Activity222Extension_pb", "Activity222Extension_pb")
usingnow("logic.proto.Activity227Extension_pb", "Activity227Extension_pb")
usingnow("logic.proto.Activity228Extension_pb", "Activity228Extension_pb")
usingnow("logic.proto.Activity229Extension_pb", "Activity229Extension_pb")
usingnow("logic.proto.Activity231Extension_pb", "Activity231Extension_pb")
usingnow("logic.proto.Activity232Extension_pb", "Activity232Extension_pb")
usingnow("logic.proto.Activity233Extension_pb", "Activity233Extension_pb")
usingnow("logic.proto.Activity234Extension_pb", "Activity234Extension_pb")
usingnow("logic.proto.Activity235Extension_pb", "Activity235Extension_pb")
usingnow("logic.proto.Activity236Extension_pb", "Activity236Extension_pb")
usingnow("logic.proto.Activity237Extension_pb", "Activity237Extension_pb")
usingnow("logic.proto.Activity238Extension_pb", "Activity238Extension_pb")
usingnow("logic.proto.Activity241Extension_pb", "Activity241Extension_pb")
usingnow("logic.proto.Activity242Extension_pb", "Activity242Extension_pb")
usingnow("logic.proto.Activity243Extension_pb", "Activity243Extension_pb")
usingnow("logic.proto.Activity244Extension_pb", "Activity244Extension_pb")
usingnow("logic.proto.Activity245Extension_pb", "Activity245Extension_pb")
usingnow("logic.proto.Activity246Extension_pb", "Activity246Extension_pb")
usingnow("logic.proto.Activity247Extension_pb", "Activity247Extension_pb")
usingnow("logic.proto.Activity249Extension_pb", "Activity249Extension_pb")
usingnow("logic.proto.Activity250Extension_pb", "Activity250Extension_pb")
usingnow("logic.proto.Activity251Extension_pb", "Activity251Extension_pb")
usingnow("logic.proto.Activity252Extension_pb", "Activity252Extension_pb")
usingnow("logic.proto.Activity254Extension_pb", "Activity254Extension_pb")
usingnow("logic.proto.Activity255Extension_pb", "Activity255Extension_pb")
usingnow("logic.proto.Activity256Extension_pb", "Activity256Extension_pb")
usingnow("logic.proto.Activity257Extension_pb", "Activity257Extension_pb")
usingnow("logic.proto.Activity258Extension_pb", "Activity258Extension_pb")
usingnow("logic.proto.Activity259Extension_pb", "Activity259Extension_pb")
usingnow("logic.proto.Activity260Extension_pb", "Activity260Extension_pb")
usingnow("logic.proto.Activity261Extension_pb", "Activity261Extension_pb")
usingnow("logic.proto.Activity262Extension_pb", "Activity262Extension_pb")
usingnow("logic.proto.Activity263Extension_pb", "Activity263Extension_pb")
usingnow("logic.proto.Activity264Extension_pb", "Activity264Extension_pb")
usingnow("logic.proto.Activity265Extension_pb", "Activity265Extension_pb")
usingnow("logic.proto.Activity266Extension_pb", "Activity266Extension_pb")
usingnow("logic.proto.Activity267Extension_pb", "Activity267Extension_pb")
usingnow("logic.proto.Activity268Extension_pb", "Activity268Extension_pb")
usingnow("logic.proto.Activity272Extension_pb", "Activity272Extension_pb")
usingnow("logic.proto.Activity276Extension_pb", "Activity276Extension_pb")
usingnow("logic.proto.Activity277Extension_pb", "Activity277Extension_pb")
usingnow("logic.proto.Activity278Extension_pb", "Activity278Extension_pb")
usingnow("logic.proto.Activity279Extension_pb", "Activity279Extension_pb")
usingnow("logic.proto.Activity280Extension_pb", "Activity280Extension_pb")
usingnow("logic.proto.Activity281Extension_pb", "Activity281Extension_pb")
usingnow("logic.proto.Activity282Extension_pb", "Activity282Extension_pb")
usingnow("logic.proto.Activity283Extension_pb", "Activity283Extension_pb")
usingnow("logic.proto.Activity284Extension_pb", "Activity284Extension_pb")
usingnow("logic.proto.Activity285Extension_pb", "Activity285Extension_pb")
usingnow("logic.proto.Activity286Extension_pb", "Activity286Extension_pb")
usingnow("logic.proto.Activity287Extension_pb", "Activity287Extension_pb")
usingnow("logic.proto.Activity288Extension_pb", "Activity288Extension_pb")
usingnow("logic.proto.Activity289Extension_pb", "Activity289Extension_pb")
usingnow("logic.proto.Activity290Extension_pb", "Activity290Extension_pb")
usingnow("logic.proto.Activity291Extension_pb", "Activity291Extension_pb")
usingnow("logic.proto.Activity292Extension_pb", "Activity292Extension_pb")
usingnow("logic.proto.Activity293Extension_pb", "Activity293Extension_pb")
usingnow("logic.proto.Activity294Extension_pb", "Activity294Extension_pb")
usingnow("logic.proto.Activity295Extension_pb", "Activity295Extension_pb")
usingnow("logic.proto.Activity296Extension_pb", "Activity296Extension_pb")
usingnow("logic.proto.Activity297Extension_pb", "Activity297Extension_pb")
usingnow("logic.proto.Activity298Extension_pb", "Activity298Extension_pb")
usingnow("logic.proto.Activity299Extension_pb", "Activity299Extension_pb")
usingnow("logic.proto.Activity401Extension_pb", "Activity401Extension_pb")
usingnow("logic.proto.Activity403Extension_pb", "Activity403Extension_pb")
usingnow("logic.proto.Activity404Extension_pb", "Activity404Extension_pb")
usingnow("logic.proto.Activity405Extension_pb", "Activity405Extension_pb")
usingnow("logic.proto.Activity406Extension_pb", "Activity406Extension_pb")
usingnow("logic.proto.Activity407Extension_pb", "Activity407Extension_pb")
usingnow("logic.proto.Activity408Extension_pb", "Activity408Extension_pb")
usingnow("logic.proto.Activity409Extension_pb", "Activity409Extension_pb")
usingnow("logic.proto.Activity410Extension_pb", "Activity410Extension_pb")
usingnow("logic.proto.Activity411Extension_pb", "Activity411Extension_pb")
usingnow("logic.proto.Activity412Extension_pb", "Activity412Extension_pb")
usingnow("logic.proto.Activity414Extension_pb", "Activity414Extension_pb")
usingnow("logic.proto.Activity415Extension_pb", "Activity415Extension_pb")
usingnow("logic.proto.Activity416Extension_pb", "Activity416Extension_pb")
usingnow("logic.proto.Activity417Extension_pb", "Activity417Extension_pb")
usingnow("logic.proto.Activity418Extension_pb", "Activity418Extension_pb")
usingnow("logic.proto.Activity419Extension_pb", "Activity419Extension_pb")
usingnow("logic.proto.Activity420Extension_pb", "Activity420Extension_pb")
usingnow("logic.proto.Activity421Extension_pb", "Activity421Extension_pb")
usingnow("logic.proto.Activity423Extension_pb", "Activity423Extension_pb")
usingnow("logic.proto.Activity424Extension_pb", "Activity424Extension_pb")
usingnow("logic.proto.Activity425Extension_pb", "Activity425Extension_pb")
usingnow("logic.proto.Activity426Extension_pb", "Activity426Extension_pb")
usingnow("logic.proto.Activity427Extension_pb", "Activity427Extension_pb")
usingnow("logic.proto.Activity428Extension_pb", "Activity428Extension_pb")
usingnow("logic.proto.Activity429Extension_pb", "Activity429Extension_pb")
usingnow("logic.proto.Activity430Extension_pb", "Activity430Extension_pb")
usingnow("logic.proto.Activity431Extension_pb", "Activity431Extension_pb")
usingnow("logic.proto.Activity432Extension_pb", "Activity432Extension_pb")
usingnow("logic.proto.Activity433Extension_pb", "Activity433Extension_pb")
usingnow("logic.proto.Activity434Extension_pb", "Activity434Extension_pb")
usingnow("logic.proto.Activity435Extension_pb", "Activity435Extension_pb")
usingnow("logic.proto.Activity436Extension_pb", "Activity436Extension_pb")
usingnow("logic.proto.Activity437Extension_pb", "Activity437Extension_pb")
usingnow("logic.proto.Activity438Extension_pb", "Activity438Extension_pb")
usingnow("logic.proto.Activity439Extension_pb", "Activity439Extension_pb")
usingnow("logic.proto.Activity440Extension_pb", "Activity440Extension_pb")
usingnow("logic.proto.Activity441Extension_pb", "Activity441Extension_pb")
usingnow("logic.proto.Activity442Extension_pb", "Activity442Extension_pb")
usingnow("logic.proto.Activity444Extension_pb", "Activity444Extension_pb")
usingnow("logic.proto.Activity445Extension_pb", "Activity445Extension_pb")
usingnow("logic.proto.Activity446Extension_pb", "Activity446Extension_pb")
usingnow("logic.proto.Activity447Extension_pb", "Activity447Extension_pb")
usingnow("logic.proto.Activity448Extension_pb", "Activity448Extension_pb")
usingnow("logic.proto.Activity449Extension_pb", "Activity449Extension_pb")
usingnow("logic.proto.Activity451Extension_pb", "Activity451Extension_pb")
usingnow("logic.proto.Activity452Extension_pb", "Activity452Extension_pb")
usingnow("logic.proto.Activity453Extension_pb", "Activity453Extension_pb")
usingnow("logic.proto.Activity454Extension_pb", "Activity454Extension_pb")
usingnow("logic.proto.Activity455Extension_pb", "Activity455Extension_pb")
usingnow("logic.proto.Activity456Extension_pb", "Activity456Extension_pb")
usingnow("logic.proto.Activity457Extension_pb", "Activity457Extension_pb")
usingnow("logic.proto.Activity458Extension_pb", "Activity458Extension_pb")
usingnow("logic.proto.Activity461Extension_pb", "Activity461Extension_pb")
usingnow("logic.proto.Activity462Extension_pb", "Activity462Extension_pb")
usingnow("logic.proto.Activity463Extension_pb", "Activity463Extension_pb")
usingnow("logic.proto.Activity464Extension_pb", "Activity464Extension_pb")
usingnow("logic.proto.Activity465Extension_pb", "Activity465Extension_pb")
usingnow("logic.proto.Activity466Extension_pb", "Activity466Extension_pb")
usingnow("logic.proto.Activity467Extension_pb", "Activity467Extension_pb")
usingnow("logic.proto.Activity468Extension_pb", "Activity468Extension_pb")
usingnow("logic.proto.Activity469Extension_pb", "Activity469Extension_pb")
usingnow("logic.proto.Activity471Extension_pb", "Activity471Extension_pb")
usingnow("logic.proto.ActivityExtension1_pb", "ActivityExtension1_pb")
usingnow("logic.proto.ArchiveExtension_pb", "ArchiveExtension_pb")
usingnow("logic.proto.ATMExtension_pb", "ATMExtension_pb")
usingnow("logic.proto.Backflow2Extension_pb", "Backflow2Extension_pb")
usingnow("logic.proto.Backflow3Extension_pb", "Backflow3Extension_pb")
usingnow("logic.proto.BackflowExtension_pb", "BackflowExtension_pb")
usingnow("logic.proto.BackpackExtension_pb", "BackpackExtension_pb")
usingnow("logic.proto.BuffExtension_pb", "BuffExtension_pb")
usingnow("logic.proto.CatchStarExtension_pb", "CatchStarExtension_pb")
usingnow("logic.proto.ChatExtension_pb", "ChatExtension_pb")
usingnow("logic.proto.ClothesCodeExtension_pb", "ClothesCodeExtension_pb")
usingnow("logic.proto.ClothesExtension_pb", "ClothesExtension_pb")
usingnow("logic.proto.CollectionExtension_pb", "CollectionExtension_pb")
usingnow("logic.proto.CouncilDefendExtension_pb", "CouncilDefendExtension_pb")
usingnow("logic.proto.CouncilExtension_pb", "CouncilExtension_pb")
usingnow("logic.proto.DecorationMatchExtension_pb", "DecorationMatchExtension_pb")
usingnow("logic.proto.DollHouseExtension_pb", "DollHouseExtension_pb")
usingnow("logic.proto.DragonChildExtension_pb", "DragonChildExtension_pb")
usingnow("logic.proto.DrawAndGuessExtension_pb", "DrawAndGuessExtension_pb")
usingnow("logic.proto.DrawTortoiseGameExtension_pb", "DrawTortoiseGameExtension_pb")
usingnow("logic.proto.DreamPalaceExtension_pb", "DreamPalaceExtension_pb")
usingnow("logic.proto.DuoMMExtension_pb", "DuoMMExtension_pb")
usingnow("logic.proto.EcoparkExtension_pb", "EcoparkExtension_pb")
usingnow("logic.proto.ElfExtension_pb", "ElfExtension_pb")
usingnow("logic.proto.ElfMagicExtension_pb", "ElfMagicExtension_pb")
usingnow("logic.proto.ExternalShareExtension_pb", "ExternalShareExtension_pb")
usingnow("logic.proto.FavorabilityExtension_pb", "FavorabilityExtension_pb")
usingnow("logic.proto.FifthPalaceExtension_pb", "FifthPalaceExtension_pb")
usingnow("logic.proto.FirstPalaceExtension_pb", "FirstPalaceExtension_pb")
usingnow("logic.proto.FlowerGardenExtension_pb", "FlowerGardenExtension_pb")
usingnow("logic.proto.FoggyForestExtension_pb", "FoggyForestExtension_pb")
usingnow("logic.proto.FoodExtension_pb", "FoodExtension_pb")
usingnow("logic.proto.FourthPalaceExtension_pb", "FourthPalaceExtension_pb")
usingnow("logic.proto.FrameExtension_pb", "FrameExtension_pb")
usingnow("logic.proto.FreeRiceExtension_pb", "FreeRiceExtension_pb")
usingnow("logic.proto.FriendExtension_pb", "FriendExtension_pb")
usingnow("logic.proto.FriendshipCheckExtension_pb", "FriendshipCheckExtension_pb")
usingnow("logic.proto.FriendTaskExtension_pb", "FriendTaskExtension_pb")
usingnow("logic.proto.FuncUnlockExtension_pb", "FuncUnlockExtension_pb")
usingnow("logic.proto.FurnitureTemplateShareSquareExtension_pb", "FurnitureTemplateShareSquareExtension_pb")
usingnow("logic.proto.GameRoomExtension_pb", "GameRoomExtension_pb")
usingnow("logic.proto.GMExtension_pb", "GMExtension_pb")
usingnow("logic.proto.GobangExtension_pb", "GobangExtension_pb")
usingnow("logic.proto.GoFishingExtension_pb", "GoFishingExtension_pb")
usingnow("logic.proto.GroceryExtension_pb", "GroceryExtension_pb")
usingnow("logic.proto.HappyBreadExtension_pb", "HappyBreadExtension_pb")
usingnow("logic.proto.HouseExtension_pb", "HouseExtension_pb")
usingnow("logic.proto.ImageExtension_pb", "ImageExtension_pb")
usingnow("logic.proto.IslandBuildExtension_pb", "IslandBuildExtension_pb")
usingnow("logic.proto.IslandExtension_pb", "IslandExtension_pb")
usingnow("logic.proto.IslandLevelExtension_pb", "IslandLevelExtension_pb")
usingnow("logic.proto.ItemLockExtension_pb", "ItemLockExtension_pb")
usingnow("logic.proto.JukeboxExtension_pb", "JukeboxExtension_pb")
usingnow("logic.proto.LotteryEggExtension_pb", "LotteryEggExtension_pb")
usingnow("logic.proto.MailExtension_pb", "MailExtension_pb")
usingnow("logic.proto.MiaoHouseExtension_pb", "MiaoHouseExtension_pb")
usingnow("logic.proto.MomentExtension_pb", "MomentExtension_pb")
usingnow("logic.proto.MoveHouseExtension_pb", "MoveHouseExtension_pb")
usingnow("logic.proto.MoveOrDieExtension_pb", "MoveOrDieExtension_pb")
usingnow("logic.proto.MultiRoomListExtension_pb", "MultiRoomListExtension_pb")
usingnow("logic.proto.MusicGameExtension_pb", "MusicGameExtension_pb")
usingnow("logic.proto.OrderExtension_pb", "OrderExtension_pb")
usingnow("logic.proto.ParttimeExtension_pb", "ParttimeExtension_pb")
usingnow("logic.proto.PartyExtension_pb", "PartyExtension_pb")
usingnow("logic.proto.PetExtension_pb", "PetExtension_pb")
usingnow("logic.proto.PotionsRefineExtension_pb", "PotionsRefineExtension_pb")
usingnow("logic.proto.RankExtension_pb", "RankExtension_pb")
usingnow("logic.proto.RechargeExtension_pb", "RechargeExtension_pb")
usingnow("logic.proto.RecycleExtension_pb", "RecycleExtension_pb")
usingnow("logic.proto.RedEnvelopeExtension_pb", "RedEnvelopeExtension_pb")
usingnow("logic.proto.RedEnvelopExtension_pb", "RedEnvelopExtension_pb")
usingnow("logic.proto.RedPointExtension_pb", "RedPointExtension_pb")
usingnow("logic.proto.RhythmBattlesExtension_pb", "RhythmBattlesExtension_pb")
usingnow("logic.proto.SceneExtension_pb", "SceneExtension_pb")
usingnow("logic.proto.SceneGameExtension_pb", "SceneGameExtension_pb")
usingnow("logic.proto.SceneInteractiveExtension_pb", "SceneInteractiveExtension_pb")
usingnow("logic.proto.SeabedOrderExtension_pb", "SeabedOrderExtension_pb")
usingnow("logic.proto.SeafloorIslandExtension_pb", "SeafloorIslandExtension_pb")
usingnow("logic.proto.SeafloorTechExtension_pb", "SeafloorTechExtension_pb")
usingnow("logic.proto.SeaSceneExtension_pb", "SeaSceneExtension_pb")
usingnow("logic.proto.SecondPalaceExtension_pb", "SecondPalaceExtension_pb")
usingnow("logic.proto.ShareStreetExtension_pb", "ShareStreetExtension_pb")
usingnow("logic.proto.SnakeExtension_pb", "SnakeExtension_pb")
usingnow("logic.proto.StoreExtension_pb", "StoreExtension_pb")
usingnow("logic.proto.StoryTellerExtension_pb", "StoryTellerExtension_pb")
usingnow("logic.proto.SurpriseSignExtension_pb", "SurpriseSignExtension_pb")
usingnow("logic.proto.TalentExtension_pb", "TalentExtension_pb")
usingnow("logic.proto.TaskExtension_pb", "TaskExtension_pb")
usingnow("logic.proto.ThirdPalaceExtension_pb", "ThirdPalaceExtension_pb")
usingnow("logic.proto.TinyGameExtension_pb", "TinyGameExtension_pb")
usingnow("logic.proto.TinyModuleExtension_pb", "TinyModuleExtension_pb")
usingnow("logic.proto.TradingExtension_pb", "TradingExtension_pb")
usingnow("logic.proto.TravellerExtension_pb", "TravellerExtension_pb")
usingnow("logic.proto.TravellerTreasureExtension_pb", "TravellerTreasureExtension_pb")
usingnow("logic.proto.UserExtension_pb", "UserExtension_pb")
usingnow("logic.proto.VarietyStoreExtension_pb", "VarietyStoreExtension_pb")
usingnow("logic.proto.VipExtension_pb", "VipExtension_pb")
usingnow("logic.proto.WerewolfExtension_pb", "WerewolfExtension_pb")
usingnow("logic.proto.WishCloverExtension_pb", "WishCloverExtension_pb")
usingnow("logic.proto.WishExtension_pb", "WishExtension_pb")
usingnow("logic.proto.WorkExtension_pb", "WorkExtension_pb")
usingnow("logic.proto.WorkShopExtension_pb", "WorkShopExtension_pb")
