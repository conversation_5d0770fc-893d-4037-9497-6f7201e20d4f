module("logic.extensions.activity.commonview.MiddleActView", package.seeall)

local MiddleActView = class("MiddleActView", ViewComponent)--ActivityEndTimeUltimateComp)

MiddleActView.Url = "ui/middleact/middleactview.prefab"

local HelpDescKey = {
	[425] = {title = "主题大富翁规则标题", content = "主题大富翁规则内容"},
	[429] = {title = "合成玩法活动规则标题", content = "合成玩法活动规则内容"},
	[435] = {title = "Activity435RuleTitle", content = "Activity435RuleContent"},
}

local redIndex2RedPointName = {
	[1] = AutoGetActInfo.getMiddleSigninActRedName(), -- 中主题签到
	[2] = AutoGetActInfo.getMiddleShopActRedName(), -- 中主题商店
	[3] = AutoGetActInfo.getMiddleStoryActRedName(), -- 中主题剧情
	[4] = "act426",--萌粒购
	[5] = "Activity2285",
}
-- 配置活动id
local tabId2ActivityId = {
	[1] = {AutoGetActInfo.getMiddleSigninActId()}, -- 中主题签到
	[2] = {AutoGetActInfo.getMiddleShopActId()}, -- 中主题商店
	[3] = {AutoGetActInfo.getMiddleStoryActId()}, -- 中主题剧情
	[4] = {426},--萌粒购
	[5] = {203},
}
-- 配置打开的view
local index2ViewName = {
	[1] = AutoGetActInfo.getMiddleSigninActViewName(), -- 中主题签到
	[2] = AutoGetActInfo.getMiddleShopActViewName(), -- 中主题商店
	[3] = AutoGetActInfo.getMiddleStoryActViewName(), -- 中主题剧情
	[4] = "ScrollingMachineView",
	[5] = "LanternShop2",
}
-- 配置打开的view
local btnNames = {
	[1] = "btnSign", -- 中主题签到
	[2] = "btnShop", -- 中主题商店
	[3] = "btnStory", -- 中主题剧情
	[4] = "btnTurntable",
	[5] = "btnReturnShop",
}

function MiddleActView:buildUI()
	MiddleActView.super.buildUI(self)
	self.middleactview = BaseLuaComponent.New(goutil.clone(self:getResInstance(MiddleActView.Url), "middleactview"))
	goutil.addChildToParent(self.middleactview._go, self.mainGO)
	self.middleactview:getBtn("btnclose/btnClose"):AddClickListener(self.close, self)
	self.middleactview:getBtn("btnhelp"):AddClickListener(self.onClickHelp, self)
	
	-- self.middleactview:getBtn("btnSign"):AddClickListener(self.onClickSign, self)
	-- self.middleactview:getBtn("btnShop"):AddClickListener(self.onClickShop, self)
	-- self.middleactview:getBtn("btnStory"):AddClickListener(self.onClickStory, self)
	-- self.middleactview:getBtn("btnTurntable"):AddClickListener(self.onClickTurntable, self)
	-- self.middleactview:getBtn("btnBuy"):AddClickListener(self.onClickBuy, self)
	-- self.middleactview:getBtn("btnGift"):AddClickListener(self.onClickGift, self)
	-- self.middleactview:getBtn("btnGame"):AddClickListener(self.onClickGame, self)
	-- self.middleactview:getBtn("btnReturnShop"):AddClickListener(self.onClickReturnShop, self)
	--互动家具
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnBuy/redPoint"), {"activity425Goods"})
	--马戏萌粒购
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnTurntable/redPoint"), {"act426"})
	--小丑签到
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnSign/redPoint"), {"DreamBoxSign"})
	-- 小主题活动商店的红点不需要在setting_short_red_point上配置，会自动在GroceryController:updateLanternShopTab上生成
	--小丑商店
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnShop/redPoint"), {"Shop" .. DreamBoxFacade.shopActivityId .. "Tab"})
	--小丑心愿
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnStory/redPoint"), {"DreamBoxStory_1"})
	--限时累充
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnGift/redPoint"), {"act430"})
	--花灯巡游
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnGame/redPoint"), {"lanternriddle"})
	-- RedPointController.instance:registerRedPoint(self.middleactview:getGo("btnReturnShop/redPoint"), {"Activity2285"})
	for i, name in ipairs(btnNames) do
		local btn = self:getBtn("middleactview/" .. name)
		btn:AddClickListener(self._onClickOpenView, self, {i})
		RedPointController.instance:registerRedPoint(goutil.findChild(btn.gameObject, "redPoint"), {redIndex2RedPointName[i]})
	end
end

function MiddleActView:destroyUI()
	for i, name in ipairs(btnNames) do
		RedPointController.instance:unregisterRedPoint(self:getGo("middleactview/" .. name .. "/redPoint"))
	end
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnBuy/redPoint"))
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnTurntable/redPoint"))
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnSign/redPoint"), {"DreamBoxSign"})
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnShop/redPoint"), {"Shop" .. DreamBoxFacade.shopActivityId .. "Tab"})
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnStory/redPoint"), {"DreamBoxStory_1"})
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnGift/redPoint"), {"act430"})
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnReturnShop/redPoint"))
	-- RedPointController.instance:unregisterRedPoint(self.middleactview:getGo("btnGame/redPoint"))
end

function MiddleActView:_onClickOpenView(params)
	local index = params[1]
	local viewName = index2ViewName[index]
	if viewName and tabId2ActivityId[index] then
		local info
		if tabId2ActivityId[index] [2] then
			info = ActivityModel.instance:getActivityInfo(tabId2ActivityId[index] [1])
		else
			info = ActivityModel.instance:getActInfoByActId(tabId2ActivityId[index] [1])
		end
		
		if info and info:getIsOpen() then
			if self.curTabId then
				local viewName = index2ViewName[self.curTabId]
				if viewName then
					ViewMgr.instance:close(viewName)
				end
			end
			self.curTabId = index
			ViewMgr.instance:open(viewName)
			if redIndex2RedPointName[index] then
				RedPointController.instance:setLastClick(redIndex2RedPointName[index], ServerTime.now())
			end
			-- 3.0 按策划要求，增加埋点，点击时记录4位数的活动id
			local actId = info:getActivityId()
			LogService.instance:logCustom("activity_icon_tap", {interface_activity = actId})
		else
			FlyTextManager.instance:showFlyText(lang("活动未开启~"))
		end
	else
		printError("配置一下", index)
	end
end

function MiddleActView:onEnter()
	MiddleActView.super.onEnter(self)
end

function MiddleActView:onClickHelp()
	ViewMgr.instance:open("ActivityRuleCommon", lang(HelpDescKey[self.activityDefineId].title), lang(HelpDescKey[self.activityDefineId].content))
end
-- --小丑签到
-- function MiddleActView:onClickSign()
-- 	RedPointController.instance:setLastClick("DreamBoxSign")
-- 	ViewMgr.instance:open("PrayDaySigninView")
-- end
-- --小丑商店
-- function MiddleActView:onClickShop()
-- 	RedPointController.instance:setLastClick("Shop" .. DreamBoxFacade.shopActivityId .. "Tab")
-- 	ViewMgr.instance:open("LanternShop")
-- end
-- --小丑心愿
-- function MiddleActView:onClickStory()
-- 	RedPointController.instance:setLastClick("DreamBoxStory_1")
-- 	ViewMgr.instance:open("DreamBoxStoryView_1")
-- end
-- --马戏萌粒购
-- function MiddleActView:onClickTurntable()
-- 	RedPointController.instance:setLastClick("act426")
-- 	ViewMgr.instance:open("ScrollingMachineView")
-- end
-- --限时累充
-- function MiddleActView:onClickGift()
-- 	RedPointController.instance:setLastClick("act430")
-- 	ViewMgr.instance:open("ActivityTotalRecharge430Panel")
-- end
-- --互动家具
-- function MiddleActView:onClickBuy()
-- 	RedPointController.instance:setLastClick("activity425Goods")
-- 	RechargeFacade.openRechargeView()
-- end
-- --花灯巡游
-- function MiddleActView:onClickGame()
-- 	RedPointController.instance:setLastClick("lanternriddle")
-- 	ViewMgr.instance:open("LanternMoonRiddleView")
-- end
-- --返场商店 
-- function MiddleActView:onClickReturnShop()
-- 	RedPointController.instance:setLastClick("Activity2285")
-- 	ViewMgr.instance:open("LanternShop2")
-- end
return MiddleActView 