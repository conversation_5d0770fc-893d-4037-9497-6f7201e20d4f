module("logic.extensions.activity.flyingchess.model.FlyingChessGameModel", package.seeall)

local FlyingChessGameModel = class("FlyingChessGameModel",BaseModel)

function FlyingChessGameModel:ctor()
    FlyingChessGameModel.super.ctor(self)
    self:init()
    
    self.hasGetRewards = nil
    self.totalJoinTimes = 0

    self.appearanceId = 0
    self.isMatchAgain = false
    self.unlockAppearanceId = nil
    self.isGetDailyMatchReward = false
end

function FlyingChessGameModel:init()
    
    self.isAutoState = false
    self.selfPosIdx = 0
    self.curUserTimes = 1
    self.diceTimes = 1
    self.dicePoints = 0
    self.nextUserTimes = 1
    self.nextAutoTime = 0
    self.maxChessPiece = 0
    self.isExtraRound = false
    --偏移位置（保证玩家自己在正中位置用）
    self.offsetPosIdx = 0
    self.chessId2GridIdx = nil
    self.posIdx2EventId = nil
    self.posIdx2ActionId = nil
    self.chessId2HomePos = {}
    self.posIdx2UserId = {}
    --叠棋子的数据
    self.overlapChessDict = nil

    self.playerDataList = nil
    self.winPlayerPosIdx = nil
    self.dailyEffectTimes = 0
    self.delayEvent2PosIdx = {}
    self.isAutoState2PosIdx = {}
    self.isChessArrivedMap = {}
end

function FlyingChessGameModel:initGameInfo(msg)
    self.curUserTimes = msg.userTimes
    self.diceTimes = msg.diceTimes
    --self.dicePoints不为0，就是处于投完骰子且未选棋子的阶段
    self.dicePoints = msg.dicePoints
    self.nextUserTimes = msg.userTimes
    self.nextAutoTime = msg.nextAutoTime
    print("初始数据", self.curUserTimes, self.diceTimes, self.dicePoints,self.nextAutoTime,GameCentreController.instance:getRoomTime())

    self.playerCount = table.nums(GameCentreModel.instance:getAllPlayer())
    local chessCount = 0
    self.chessId2GridIdx = {}
    for k,v in ipairs(msg.chessPos) do
        print("initGameInfo chess", v.pos, v.id)
        chessCount = chessCount + 1
        if v.pos == FlyingChessPosUtils.specialEndGridIdx then
            self:setArriveChess(v.id)
            self.chessId2GridIdx[v.id] = FlyingChessPosUtils.homeGridIdx
        else
            self.chessId2GridIdx[v.id] = v.pos
        end
    end
    self.maxChessPiece = chessCount/self.playerCount

    self.posIdx2EventId = {}
    for k,v in ipairs(msg.eventPos) do
        self.posIdx2EventId[v.pos] = v.id
        print("initGameInfo eventInfo", v.pos, v.id)
    end

    self.posIdx2ActionId = {}
    for k,v in ipairs(msg.clientEventPos) do
        self.posIdx2ActionId[v.pos] = v.id
        print("initGameInfo actionInfo", v.pos, v.id)
    end

    self:chessAllChessOverlap()
end

function FlyingChessGameModel:updateNextAutoTime(nextAutoTime)
    -- print("FlyingChessGameModel:updateNextAutoTime",nextAutoTime,GameCentreController.instance:getRoomTime())
    if self.nextAutoTime ~= nextAutoTime then
        self.nextAutoTime = nextAutoTime
        if FlyingChessGameController.instance:getGameState() == FlyingChessGameState.Performing then
            --回合数不变，是额外的回合
            if self.curUserTimes == self.nextUserTimes then
                self.isExtraRound = true
                FlyingChessGameController.instance:localNotify(FlyingChessNotify.OnExtraRound)
            end
            self:autoNextPlayer()
            FlyingChessGameController.instance:setGameState(FlyingChessGameState.RollDice)
            self.isExtraRound = false
        end
    end
end

function FlyingChessGameModel:updateChessEventInfo(msg)
    self.diceTimes = msg.diceTimes
    self.posIdx2EventId = {}
    for k,v in ipairs(msg.evtPosNos) do
        self.posIdx2EventId[v.pos] = v.id
        print("updateChessEventInfo eventInfo", v.pos, v.id)
    end
    -- FlyingChessGameController.instance:localNotify(FlyingChessNotify.OnMapEventUpdate)
    FlyingChessGameController.instance:refreshMapEvent()
end

function FlyingChessGameModel:updateChessActionInfo(msg)
    self.diceTimes = msg.diceTimes
    self.posIdx2ActionId = {}
    for k,v in ipairs(msg.evtPosNos) do
        self.posIdx2ActionId[v.pos] = v.id
        print("updateChessActionInfo actionInfo", v.pos, v.id)
    end
    -- FlyingChessGameController.instance:localNotify(FlyingChessNotify.OnMapEventUpdate)
    FlyingChessGameController.instance:refreshMapAction()
end

function FlyingChessGameModel:getAllEventDict()
   return self.posIdx2EventId 
end

function FlyingChessGameModel:getAllActionDict()
    return self.posIdx2ActionId 
 end

function FlyingChessGameModel:setSelfPos(realPosIdx)
    self.selfPosIdx = realPosIdx
    self.offsetPosIdx = 4 - realPosIdx + 1
end

function FlyingChessGameModel:isSelfRound()
    return self.selfPosIdx == self:getCurRoundPosIdx()
end

function FlyingChessGameModel:getCurRoundPosIdx()
    return FlyingChessPosUtils.userTimes2PosIdx(self.curUserTimes)
end

function FlyingChessGameModel:getSelfPosIdx()
    return self.selfPosIdx
end

function FlyingChessGameModel:getClientPosIdx(realPosIdx)
    local posIdx = realPosIdx + self.offsetPosIdx
    return (posIdx - 1) % 4 + 1
end

function FlyingChessGameModel:getCurUserTimes()
    return self.curUserTimes
end

--前后端都是这样子算游戏状态的
function FlyingChessGameModel:calGameState()
    local nowTime = GameCentreController.instance:getRoomTime()
    local readyCostTime = tonumber(FlyingChessConfig.getCommonValue("readyCostTime"))*1000
    --这里增加一秒，减少准备阶段的误差
    if nowTime + 1000 < readyCostTime then
        return FlyingChessGameState.Ready
    else
        if self.dicePoints == 0 then
            local rollDiceCostTime = tonumber(FlyingChessConfig.getCommonValue("rollDiceCostTime"))*1000
            local startRollDiceTime = self.nextAutoTime - rollDiceCostTime
            if nowTime > startRollDiceTime then
                return FlyingChessGameState.RollDice
            else
                return FlyingChessGameState.Performing
            end
        else
            local rollDiceAniTime = tonumber(FlyingChessConfig.getCommonValue("rollDiceAniTime"))*1000
            local startRollDiceAniTime = self.nextAutoTime - rollDiceAniTime
            --self.dicePoints不为0，就是处于投完骰子播动画的阶段，或者处于选棋子的阶段
            if nowTime > startRollDiceAniTime then
                return FlyingChessGameState.RollDice
            else
                return FlyingChessGameState.SelectChessPiece
            end
        end
    end
end

function FlyingChessGameModel:setChessGridIdx(chessId, gridIdx)
    self.chessId2GridIdx[chessId] = gridIdx
end

function FlyingChessGameModel:getChessGridIdx(chessId)
    return self.chessId2GridIdx[chessId] or FlyingChessPosUtils.homeGridIdx
end

function FlyingChessGameModel:setChessHomePos(chessId, homePos)
    self.chessId2HomePos[chessId] = homePos
end

function FlyingChessGameModel:getChessHomePos(chessId)
    return self.chessId2HomePos[chessId]
end

function FlyingChessGameModel:isOverlapAndShow(unit)
    local chessId = unit.info.id
    local posIdx = unit.info.posIdx
    local gridIdx = self:getChessGridIdx(chessId)
    if gridIdx ~=  FlyingChessPosUtils.homeGridIdx then
        local chessIds = self:getOverlapChessIds(posIdx, gridIdx)
        if chessIds ~= nil then
            if #chessIds > 1 and chessId ~= chessIds[1] then
                return false
            end
        end
    end
    return true
end

function FlyingChessGameModel:getOverlapChessIds(playerPosIdx, index)
    if index ~= FlyingChessPosUtils.homeGridIdx and self.overlapChessDict then
        if index == FlyingChessPosUtils.flyingGridIdx then
            index = index * playerPosIdx
        elseif index >= FlyingChessPosUtils.specialStartGridIdx and index <= FlyingChessPosUtils.specialEndGridIdx then
            index = index + 10 * playerPosIdx
        end
        for k, v in pairs(self.overlapChessDict) do
            print("getOverlapChessIds",k,table.concat( v, ", ", 1, #v ))
        end
        return self.overlapChessDict[index]
    end
end

function FlyingChessGameModel:setChessPlayer(posIdx, userId)
    self.posIdx2UserId[posIdx] = userId
end

function FlyingChessGameModel:getUserIdByPosIdx(posIdx)
    return self.posIdx2UserId[posIdx]
end

function FlyingChessGameModel:getApearanceId(posIdx)
    local userId = self:getUserIdByPosIdx(posIdx)
    return GameCentreModel.instance:getPlayerInfo(userId).apearanceId
    -- return 25030002
end

function FlyingChessGameModel:getCurRoundUserId()
    return self.posIdx2UserId[self:getCurRoundPosIdx()]
end

function FlyingChessGameModel:setArriveChess(chessId)
    self.isChessArrivedMap[chessId] = true
end
--到达终点
function FlyingChessGameModel:isArriveEnd(chessId)
    return self.isChessArrivedMap[chessId] == true or self:getChessGridIdx(chessId) == FlyingChessPosUtils.specialEndGridIdx
end

function FlyingChessGameModel:isInHome(chessId)
    return self:getChessGridIdx(chessId) == FlyingChessPosUtils.homeGridIdx
end

function FlyingChessGameModel:setIsAutoState(posIdx, isAuto)
    self.isAutoState2PosIdx[posIdx] = isAuto
end

function FlyingChessGameModel:isAutoStateByPlayerPosIdx(posIdx)
    return self.isAutoState2PosIdx[posIdx]
end

function FlyingChessGameModel:setDelayEvent(posIdx, delayEvent)
    local dict = self.delayEvent2PosIdx[posIdx]
    if not dict then
        dict = {}
        self.delayEvent2PosIdx[posIdx] = dict
    end
    dict[delayEvent.realEventTimes] = delayEvent.realEventId
end

function FlyingChessGameModel:getDelayEvent(posIdx)
    local dict = self.delayEvent2PosIdx[posIdx]
    if dict and dict[self.curUserTimes] ~= nil then
        return dict[self.curUserTimes]
    end
    return 0
end

--自动累加userTimes,轮到下个玩家掷骰子
function FlyingChessGameModel:autoNextPlayer()
    --这里置零，重新投骰子用
    self.dicePoints = 0
    self.curUserTimes = self.nextUserTimes
end

function FlyingChessGameModel:updateChessDicePoint(msg)
    self.curUserTimes = msg.userTimes
    self.diceTimes = msg.diceTimes
    self.nextUserTimes = msg.nextUserTimes
    self.dicePoints = msg.dicePoints
    print("当前回合骰子数：",self.dicePoints, self.curUserTimes, self.nextUserTimes)
    FlyingChessGameController.instance:localNotify(FlyingChessNotify.OnDicePointUpdate)
end

function FlyingChessGameModel:updateCurRoundInfo(msg)
    if self.curUserTimes > msg.userTimes then
        print(string.format("后端发的回合数据跟前端对不上,前端回合：%d,后端回合：%d",self.curUserTimes,msg.userTimes))
        return
    end
    --这时候赋值0，为投骰子做准备（其实这个时候直接投骰子，后端也可以通过的）
    self.dicePoints = 0
    self.curUserTimes = msg.userTimes
    self.diceTimes = msg.diceTimes
    self.nextUserTimes = msg.nextUserTimes
    print("当前回合数据：", self.diceTimes, self.curUserTimes,self.nextUserTimes, FlyingChessPosUtils.userTimes2PosIdx(self.curUserTimes), FlyingChessPosUtils.userTimes2PosIdx(self.nextUserTimes))
    local chessIds = msg.moveChessId
    table.sort(chessIds, function(a, b)
        return a < b
    end)

    local playerIdx = FlyingChessPosUtils.userTimes2PosIdx(self.curUserTimes)
    self.curRoundRouteInfo = {}
    self.curRoundRouteInfo.curUserTimes = self.curUserTimes
    self.curRoundRouteInfo.chessIds = chessIds
    --新路线开始前的格子
    self.curRoundRouteInfo.routeStartGridIdx = self:getChessGridIdx(chessIds[1])
    
    local routes = {} 
    local moveRoutes = msg.moveRouts
    local len = #moveRoutes
    for i=1, len do
        local mo = moveRoutes[i]
        print("updateCurRoundInfo route", mo.baseMoveType, mo.beginPos, mo.specialEventId)
        if i < len then
            local nextMo = moveRoutes[i + 1]
            local endPos = nextMo.beginPos
            for j=1,#chessIds do
                self:setChessGridIdx(chessIds[j], endPos)
            end
            if mo.specialEventId ~= nil and mo.specialEventId ~= 0 then
                local route, isReverse = FlyingChessPosUtils.calcuteChessRoute(playerIdx, mo.beginPos, endPos)
                --不包含起始点
                local num = (#route - 1)
                local footCount = not isReverse and num or -num
                table.insert(routes, {specialEventId = mo.specialEventId, startIdx = mo.beginPos, footCount = footCount})
                --事件触发的位置变化
                table.insert(routes, {moveType = FlyingChessMoveType.EventChangePos, startIdx = mo.beginPos, endIdx = endPos})
            else
                table.insert(routes, {moveType = mo.baseMoveType, startIdx = mo.beginPos, endIdx = endPos})
            end
        elseif i == len then
            --最后的数据是事件，不会有其他移动
            if mo.specialEventId ~= nil and mo.specialEventId ~= 0 then
                table.insert(routes, {specialEventId = mo.specialEventId, startIdx = mo.beginPos, footCount = 0})
            end
        end
        for k, v in ipairs(mo.crashChessId) do
            table.insert(routes, {crashChessId = v})
            self:setChessGridIdx(v, FlyingChessPosUtils.homeGridIdx)
        end
    end
    self.curRoundRouteInfo.routes = routes
    self:chessAllChessOverlap()
    -- for k, v in pairs(self.overlapChessDict) do
    --     print("updateCurRoundInfo getOverlapChessIds",k,table.concat( v, ", ", 1, #v ))
    -- end
    FlyingChessGameController.instance:setGameState(FlyingChessGameState.Performing)

end

-- function Fly

function FlyingChessGameModel:chessAllChessOverlap()
    self.overlapChessDict = {}
    local players = GameCentreModel.instance:getAllPlayer()
    for userId, playerInfo in pairs(players) do
        local chessIds = FlyingChessGameController.instance:getChessIdsByPlayerIdx(playerInfo.posIdx)
        for k, v in ipairs(chessIds) do
            local index = self:getChessGridIdx(v)
            -- local index = FlyingChessPosUtils.getClientGridIdx(playerInfo.posIdx, index)
            if index ~= FlyingChessPosUtils.homeGridIdx then
                --这里错开格子index
                if index == FlyingChessPosUtils.flyingGridIdx then
                    index = index * playerInfo.posIdx
                elseif index >= FlyingChessPosUtils.specialStartGridIdx and index <= FlyingChessPosUtils.specialEndGridIdx then
                    index = index + 10 * playerInfo.posIdx
                end
                if not self.overlapChessDict[index] then
                    self.overlapChessDict[index] = {}
                end
                table.insert(self.overlapChessDict[index], v)
            end
        end
    end
end

function FlyingChessGameModel:isVehichleUnlock(itemId)
    if itemId == 0 then return true end
    if self.unlockAppearanceId ~= nil then
        return table.indexof(self.unlockAppearanceId, itemId) ~= false
    end
    return false
end

FlyingChessGameModel.instance = FlyingChessGameModel:New()
return FlyingChessGameModel