-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
local USEREXTENSION_PB = require("logic.proto.UserExtension_pb")
module("logic.proto.Activity405Extension_pb", package.seeall)


local tb = {}
tb.ACT405RECEIVELIMITTYPE_ENUM = protobuf.EnumDescriptor()
tb.ACT405RECEIVELIMITTYPE_ALL_CARD_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT405RECEIVELIMITTYPE_ONLY_REQUEST_CARD_ENUMITEM = protobuf.EnumValueDescriptor()
GETACT405INFOREQUEST_MSG = protobuf.Descriptor()
GETACT405REWARDREQUEST_MSG = protobuf.Descriptor()
tb.GETACT405REWARDREQUEST_THEMEID_FIELD = protobuf.FieldDescriptor()
USEACT405RANDOMBOXREPLY_MSG = protobuf.Descriptor()
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD = protobuf.FieldDescriptor()
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD = protobuf.FieldDescriptor()
GETACT405RECEIVEINFOREQUEST_MSG = protobuf.Descriptor()
MODIFYACT405RECEIVELIMITREPLY_MSG = protobuf.Descriptor()
GETACT405INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_CURLEVEL_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_CUREXP_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD = protobuf.FieldDescriptor()
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD = protobuf.FieldDescriptor()
ACT405GETLEVELREWARDREQUEST_MSG = protobuf.Descriptor()
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD = protobuf.FieldDescriptor()
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD = protobuf.FieldDescriptor()
MODIFYACT405RECEIVELIMITREQUEST_MSG = protobuf.Descriptor()
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD = protobuf.FieldDescriptor()
ACT405RANDOMBOXINFONO_MSG = protobuf.Descriptor()
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD = protobuf.FieldDescriptor()
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD = protobuf.FieldDescriptor()
GETACT405REWARDREPLY_MSG = protobuf.Descriptor()
tb.GETACT405REWARDREPLY_CHANGEID_FIELD = protobuf.FieldDescriptor()
ACT405GETLEVELREWARDREPLY_MSG = protobuf.Descriptor()
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD = protobuf.FieldDescriptor()
GETACT405RECEIVEINFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD = protobuf.FieldDescriptor()
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD = protobuf.FieldDescriptor()
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD = protobuf.FieldDescriptor()
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD = protobuf.FieldDescriptor()
ACT405RECEIVEINFONO_MSG = protobuf.Descriptor()
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD = protobuf.FieldDescriptor()
tb.ACT405RECEIVEINFONO_CARDID_FIELD = protobuf.FieldDescriptor()
tb.ACT405RECEIVEINFONO_TIME_FIELD = protobuf.FieldDescriptor()
USEACT405RANDOMBOXREQUEST_MSG = protobuf.Descriptor()
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD = protobuf.FieldDescriptor()

tb.ACT405RECEIVELIMITTYPE_ALL_CARD_ENUMITEM.name = "ALL_CARD"
tb.ACT405RECEIVELIMITTYPE_ALL_CARD_ENUMITEM.index = 0
tb.ACT405RECEIVELIMITTYPE_ALL_CARD_ENUMITEM.number = 0
tb.ACT405RECEIVELIMITTYPE_ONLY_REQUEST_CARD_ENUMITEM.name = "ONLY_REQUEST_CARD"
tb.ACT405RECEIVELIMITTYPE_ONLY_REQUEST_CARD_ENUMITEM.index = 1
tb.ACT405RECEIVELIMITTYPE_ONLY_REQUEST_CARD_ENUMITEM.number = 1
tb.ACT405RECEIVELIMITTYPE_ENUM.name = "Act405ReceiveLimitType"
tb.ACT405RECEIVELIMITTYPE_ENUM.full_name = ".Act405ReceiveLimitType"
tb.ACT405RECEIVELIMITTYPE_ENUM.values = {tb.ACT405RECEIVELIMITTYPE_ALL_CARD_ENUMITEM,tb.ACT405RECEIVELIMITTYPE_ONLY_REQUEST_CARD_ENUMITEM}
GETACT405INFOREQUEST_MSG.name = "GetAct405InfoRequest"
GETACT405INFOREQUEST_MSG.full_name = ".GetAct405InfoRequest"
GETACT405INFOREQUEST_MSG.filename = "Activity405Extension"
GETACT405INFOREQUEST_MSG.nested_types = {}
GETACT405INFOREQUEST_MSG.enum_types = {}
GETACT405INFOREQUEST_MSG.fields = {}
GETACT405INFOREQUEST_MSG.is_extendable = false
GETACT405INFOREQUEST_MSG.extensions = {}
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.name = "themeId"
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.full_name = ".GetAct405RewardRequest.themeId"
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.number = 1
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.index = 0
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.label = 1
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.has_default_value = false
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.default_value = 0
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.type = 5
tb.GETACT405REWARDREQUEST_THEMEID_FIELD.cpp_type = 1

GETACT405REWARDREQUEST_MSG.name = "GetAct405RewardRequest"
GETACT405REWARDREQUEST_MSG.full_name = ".GetAct405RewardRequest"
GETACT405REWARDREQUEST_MSG.filename = "Activity405Extension"
GETACT405REWARDREQUEST_MSG.nested_types = {}
GETACT405REWARDREQUEST_MSG.enum_types = {}
GETACT405REWARDREQUEST_MSG.fields = {tb.GETACT405REWARDREQUEST_THEMEID_FIELD}
GETACT405REWARDREQUEST_MSG.is_extendable = false
GETACT405REWARDREQUEST_MSG.extensions = {}
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.name = "randomCardIds"
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.full_name = ".UseAct405RandomBoxReply.randomCardIds"
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.number = 1
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.index = 0
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.label = 3
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.has_default_value = false
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.default_value = {}
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.type = 5
tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD.cpp_type = 1

tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.name = "extraRewards"
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.full_name = ".UseAct405RandomBoxReply.extraRewards"
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.number = 2
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.index = 1
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.label = 3
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.has_default_value = false
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.default_value = {}
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.message_type = ACT405RANDOMBOXINFONO_MSG
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.type = 11
tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD.cpp_type = 10

USEACT405RANDOMBOXREPLY_MSG.name = "UseAct405RandomBoxReply"
USEACT405RANDOMBOXREPLY_MSG.full_name = ".UseAct405RandomBoxReply"
USEACT405RANDOMBOXREPLY_MSG.filename = "Activity405Extension"
USEACT405RANDOMBOXREPLY_MSG.nested_types = {}
USEACT405RANDOMBOXREPLY_MSG.enum_types = {}
USEACT405RANDOMBOXREPLY_MSG.fields = {tb.USEACT405RANDOMBOXREPLY_RANDOMCARDIDS_FIELD, tb.USEACT405RANDOMBOXREPLY_EXTRAREWARDS_FIELD}
USEACT405RANDOMBOXREPLY_MSG.is_extendable = false
USEACT405RANDOMBOXREPLY_MSG.extensions = {}
GETACT405RECEIVEINFOREQUEST_MSG.name = "GetAct405ReceiveInfoRequest"
GETACT405RECEIVEINFOREQUEST_MSG.full_name = ".GetAct405ReceiveInfoRequest"
GETACT405RECEIVEINFOREQUEST_MSG.filename = "Activity405Extension"
GETACT405RECEIVEINFOREQUEST_MSG.nested_types = {}
GETACT405RECEIVEINFOREQUEST_MSG.enum_types = {}
GETACT405RECEIVEINFOREQUEST_MSG.fields = {}
GETACT405RECEIVEINFOREQUEST_MSG.is_extendable = false
GETACT405RECEIVEINFOREQUEST_MSG.extensions = {}
MODIFYACT405RECEIVELIMITREPLY_MSG.name = "ModifyAct405ReceiveLimitReply"
MODIFYACT405RECEIVELIMITREPLY_MSG.full_name = ".ModifyAct405ReceiveLimitReply"
MODIFYACT405RECEIVELIMITREPLY_MSG.filename = "Activity405Extension"
MODIFYACT405RECEIVELIMITREPLY_MSG.nested_types = {}
MODIFYACT405RECEIVELIMITREPLY_MSG.enum_types = {}
MODIFYACT405RECEIVELIMITREPLY_MSG.fields = {}
MODIFYACT405RECEIVELIMITREPLY_MSG.is_extendable = false
MODIFYACT405RECEIVELIMITREPLY_MSG.extensions = {}
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.name = "hasGetFinalReward"
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.full_name = ".GetAct405InfoReply.hasGetFinalReward"
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.number = 1
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.index = 0
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.label = 1
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.default_value = false
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.type = 8
tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD.cpp_type = 7

tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.name = "dailySendTimes"
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.full_name = ".GetAct405InfoReply.dailySendTimes"
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.number = 2
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.index = 1
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.label = 1
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.default_value = 0
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.type = 5
tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD.cpp_type = 1

tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.name = "dailyGetTimes"
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.full_name = ".GetAct405InfoReply.dailyGetTimes"
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.number = 3
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.index = 2
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.label = 1
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.default_value = 0
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.type = 5
tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD.cpp_type = 1

tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.name = "getThemeRewardIds"
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.full_name = ".GetAct405InfoReply.getThemeRewardIds"
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.number = 4
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.index = 3
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.label = 3
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.default_value = {}
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.type = 5
tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD.cpp_type = 1

tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.name = "limitType"
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.full_name = ".GetAct405InfoReply.limitType"
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.number = 5
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.index = 4
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.label = 1
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.default_value = nil
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.enum_type = ACT405RECEIVELIMITTYPE_ENUM
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.type = 14
tb.GETACT405INFOREPLY_LIMITTYPE_FIELD.cpp_type = 8

tb.GETACT405INFOREPLY_CURLEVEL_FIELD.name = "curLevel"
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.full_name = ".GetAct405InfoReply.curLevel"
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.number = 6
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.index = 5
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.label = 1
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.default_value = 0
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.type = 5
tb.GETACT405INFOREPLY_CURLEVEL_FIELD.cpp_type = 1

tb.GETACT405INFOREPLY_CUREXP_FIELD.name = "curExp"
tb.GETACT405INFOREPLY_CUREXP_FIELD.full_name = ".GetAct405InfoReply.curExp"
tb.GETACT405INFOREPLY_CUREXP_FIELD.number = 7
tb.GETACT405INFOREPLY_CUREXP_FIELD.index = 6
tb.GETACT405INFOREPLY_CUREXP_FIELD.label = 1
tb.GETACT405INFOREPLY_CUREXP_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_CUREXP_FIELD.default_value = 0
tb.GETACT405INFOREPLY_CUREXP_FIELD.type = 5
tb.GETACT405INFOREPLY_CUREXP_FIELD.cpp_type = 1

tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.name = "levelRewardBitMark"
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.full_name = ".GetAct405InfoReply.levelRewardBitMark"
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.number = 8
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.index = 7
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.label = 1
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.default_value = 0
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.type = 5
tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD.cpp_type = 1

tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.name = "curLoopRewardCanGet"
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.full_name = ".GetAct405InfoReply.curLoopRewardCanGet"
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.number = 9
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.index = 8
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.label = 1
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.has_default_value = false
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.default_value = false
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.type = 8
tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD.cpp_type = 7

GETACT405INFOREPLY_MSG.name = "GetAct405InfoReply"
GETACT405INFOREPLY_MSG.full_name = ".GetAct405InfoReply"
GETACT405INFOREPLY_MSG.filename = "Activity405Extension"
GETACT405INFOREPLY_MSG.nested_types = {}
GETACT405INFOREPLY_MSG.enum_types = {}
GETACT405INFOREPLY_MSG.fields = {tb.GETACT405INFOREPLY_HASGETFINALREWARD_FIELD, tb.GETACT405INFOREPLY_DAILYSENDTIMES_FIELD, tb.GETACT405INFOREPLY_DAILYGETTIMES_FIELD, tb.GETACT405INFOREPLY_GETTHEMEREWARDIDS_FIELD, tb.GETACT405INFOREPLY_LIMITTYPE_FIELD, tb.GETACT405INFOREPLY_CURLEVEL_FIELD, tb.GETACT405INFOREPLY_CUREXP_FIELD, tb.GETACT405INFOREPLY_LEVELREWARDBITMARK_FIELD, tb.GETACT405INFOREPLY_CURLOOPREWARDCANGET_FIELD}
GETACT405INFOREPLY_MSG.is_extendable = false
GETACT405INFOREPLY_MSG.extensions = {}
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.name = "level"
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.full_name = ".Act405GetLevelRewardRequest.level"
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.number = 1
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.index = 0
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.label = 1
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.has_default_value = false
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.default_value = 0
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.type = 5
tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD.cpp_type = 1

tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.name = "getAll"
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.full_name = ".Act405GetLevelRewardRequest.getAll"
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.number = 2
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.index = 1
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.label = 1
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.has_default_value = false
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.default_value = false
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.type = 8
tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD.cpp_type = 7

ACT405GETLEVELREWARDREQUEST_MSG.name = "Act405GetLevelRewardRequest"
ACT405GETLEVELREWARDREQUEST_MSG.full_name = ".Act405GetLevelRewardRequest"
ACT405GETLEVELREWARDREQUEST_MSG.filename = "Activity405Extension"
ACT405GETLEVELREWARDREQUEST_MSG.nested_types = {}
ACT405GETLEVELREWARDREQUEST_MSG.enum_types = {}
ACT405GETLEVELREWARDREQUEST_MSG.fields = {tb.ACT405GETLEVELREWARDREQUEST_LEVEL_FIELD, tb.ACT405GETLEVELREWARDREQUEST_GETALL_FIELD}
ACT405GETLEVELREWARDREQUEST_MSG.is_extendable = false
ACT405GETLEVELREWARDREQUEST_MSG.extensions = {}
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.name = "limitType"
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.full_name = ".ModifyAct405ReceiveLimitRequest.limitType"
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.number = 1
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.index = 0
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.label = 1
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.has_default_value = false
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.default_value = nil
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.enum_type = ACT405RECEIVELIMITTYPE_ENUM
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.type = 14
tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD.cpp_type = 8

MODIFYACT405RECEIVELIMITREQUEST_MSG.name = "ModifyAct405ReceiveLimitRequest"
MODIFYACT405RECEIVELIMITREQUEST_MSG.full_name = ".ModifyAct405ReceiveLimitRequest"
MODIFYACT405RECEIVELIMITREQUEST_MSG.filename = "Activity405Extension"
MODIFYACT405RECEIVELIMITREQUEST_MSG.nested_types = {}
MODIFYACT405RECEIVELIMITREQUEST_MSG.enum_types = {}
MODIFYACT405RECEIVELIMITREQUEST_MSG.fields = {tb.MODIFYACT405RECEIVELIMITREQUEST_LIMITTYPE_FIELD}
MODIFYACT405RECEIVELIMITREQUEST_MSG.is_extendable = false
MODIFYACT405RECEIVELIMITREQUEST_MSG.extensions = {}
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.name = "itemId"
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.full_name = ".Act405RandomBoxInfoNO.itemId"
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.number = 1
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.index = 0
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.label = 1
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.has_default_value = false
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.default_value = 0
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.type = 5
tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD.cpp_type = 1

tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.name = "count"
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.full_name = ".Act405RandomBoxInfoNO.count"
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.number = 2
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.index = 1
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.label = 1
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.has_default_value = false
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.default_value = 0
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.type = 5
tb.ACT405RANDOMBOXINFONO_COUNT_FIELD.cpp_type = 1

ACT405RANDOMBOXINFONO_MSG.name = "Act405RandomBoxInfoNO"
ACT405RANDOMBOXINFONO_MSG.full_name = ".Act405RandomBoxInfoNO"
ACT405RANDOMBOXINFONO_MSG.filename = "Activity405Extension"
ACT405RANDOMBOXINFONO_MSG.nested_types = {}
ACT405RANDOMBOXINFONO_MSG.enum_types = {}
ACT405RANDOMBOXINFONO_MSG.fields = {tb.ACT405RANDOMBOXINFONO_ITEMID_FIELD, tb.ACT405RANDOMBOXINFONO_COUNT_FIELD}
ACT405RANDOMBOXINFONO_MSG.is_extendable = false
ACT405RANDOMBOXINFONO_MSG.extensions = {}
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.name = "changeId"
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.full_name = ".GetAct405RewardReply.changeId"
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.number = 1
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.index = 0
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.label = 1
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.has_default_value = false
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.default_value = 0
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.type = 5
tb.GETACT405REWARDREPLY_CHANGEID_FIELD.cpp_type = 1

GETACT405REWARDREPLY_MSG.name = "GetAct405RewardReply"
GETACT405REWARDREPLY_MSG.full_name = ".GetAct405RewardReply"
GETACT405REWARDREPLY_MSG.filename = "Activity405Extension"
GETACT405REWARDREPLY_MSG.nested_types = {}
GETACT405REWARDREPLY_MSG.enum_types = {}
GETACT405REWARDREPLY_MSG.fields = {tb.GETACT405REWARDREPLY_CHANGEID_FIELD}
GETACT405REWARDREPLY_MSG.is_extendable = false
GETACT405REWARDREPLY_MSG.extensions = {}
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.full_name = ".Act405GetLevelRewardReply.changeSetId"
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.name = "curExp"
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.full_name = ".Act405GetLevelRewardReply.curExp"
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.number = 2
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.index = 1
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.label = 1
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.has_default_value = false
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.default_value = 0
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.type = 5
tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD.cpp_type = 1

ACT405GETLEVELREWARDREPLY_MSG.name = "Act405GetLevelRewardReply"
ACT405GETLEVELREWARDREPLY_MSG.full_name = ".Act405GetLevelRewardReply"
ACT405GETLEVELREWARDREPLY_MSG.filename = "Activity405Extension"
ACT405GETLEVELREWARDREPLY_MSG.nested_types = {}
ACT405GETLEVELREWARDREPLY_MSG.enum_types = {}
ACT405GETLEVELREWARDREPLY_MSG.fields = {tb.ACT405GETLEVELREWARDREPLY_CHANGESETID_FIELD, tb.ACT405GETLEVELREWARDREPLY_CUREXP_FIELD}
ACT405GETLEVELREWARDREPLY_MSG.is_extendable = false
ACT405GETLEVELREWARDREPLY_MSG.extensions = {}
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.name = "sendRecords"
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.full_name = ".GetAct405ReceiveInfoReply.sendRecords"
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.number = 1
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.index = 0
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.label = 3
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.has_default_value = false
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.default_value = {}
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.message_type = ACT405RECEIVEINFONO_MSG
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.type = 11
tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD.cpp_type = 10

tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.name = "receiveRecords"
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.full_name = ".GetAct405ReceiveInfoReply.receiveRecords"
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.number = 2
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.index = 1
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.label = 3
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.has_default_value = false
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.default_value = {}
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.message_type = ACT405RECEIVEINFONO_MSG
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.type = 11
tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD.cpp_type = 10

tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.name = "dailySendTimes"
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.full_name = ".GetAct405ReceiveInfoReply.dailySendTimes"
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.number = 3
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.index = 2
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.label = 1
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.has_default_value = false
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.default_value = 0
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.type = 5
tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD.cpp_type = 1

tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.name = "dailyReceiveTimes"
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.full_name = ".GetAct405ReceiveInfoReply.dailyReceiveTimes"
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.number = 4
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.index = 3
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.label = 1
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.has_default_value = false
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.default_value = 0
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.type = 5
tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD.cpp_type = 1

GETACT405RECEIVEINFOREPLY_MSG.name = "GetAct405ReceiveInfoReply"
GETACT405RECEIVEINFOREPLY_MSG.full_name = ".GetAct405ReceiveInfoReply"
GETACT405RECEIVEINFOREPLY_MSG.filename = "Activity405Extension"
GETACT405RECEIVEINFOREPLY_MSG.nested_types = {}
GETACT405RECEIVEINFOREPLY_MSG.enum_types = {}
GETACT405RECEIVEINFOREPLY_MSG.fields = {tb.GETACT405RECEIVEINFOREPLY_SENDRECORDS_FIELD, tb.GETACT405RECEIVEINFOREPLY_RECEIVERECORDS_FIELD, tb.GETACT405RECEIVEINFOREPLY_DAILYSENDTIMES_FIELD, tb.GETACT405RECEIVEINFOREPLY_DAILYRECEIVETIMES_FIELD}
GETACT405RECEIVEINFOREPLY_MSG.is_extendable = false
GETACT405RECEIVEINFOREPLY_MSG.extensions = {}
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.name = "roleInfo"
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.full_name = ".Act405ReceiveInfoNO.roleInfo"
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.number = 1
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.index = 0
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.label = 1
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.has_default_value = false
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.default_value = nil
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.type = 11
tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD.cpp_type = 10

tb.ACT405RECEIVEINFONO_CARDID_FIELD.name = "cardId"
tb.ACT405RECEIVEINFONO_CARDID_FIELD.full_name = ".Act405ReceiveInfoNO.cardId"
tb.ACT405RECEIVEINFONO_CARDID_FIELD.number = 2
tb.ACT405RECEIVEINFONO_CARDID_FIELD.index = 1
tb.ACT405RECEIVEINFONO_CARDID_FIELD.label = 1
tb.ACT405RECEIVEINFONO_CARDID_FIELD.has_default_value = false
tb.ACT405RECEIVEINFONO_CARDID_FIELD.default_value = 0
tb.ACT405RECEIVEINFONO_CARDID_FIELD.type = 5
tb.ACT405RECEIVEINFONO_CARDID_FIELD.cpp_type = 1

tb.ACT405RECEIVEINFONO_TIME_FIELD.name = "time"
tb.ACT405RECEIVEINFONO_TIME_FIELD.full_name = ".Act405ReceiveInfoNO.time"
tb.ACT405RECEIVEINFONO_TIME_FIELD.number = 3
tb.ACT405RECEIVEINFONO_TIME_FIELD.index = 2
tb.ACT405RECEIVEINFONO_TIME_FIELD.label = 1
tb.ACT405RECEIVEINFONO_TIME_FIELD.has_default_value = false
tb.ACT405RECEIVEINFONO_TIME_FIELD.default_value = 0
tb.ACT405RECEIVEINFONO_TIME_FIELD.type = 5
tb.ACT405RECEIVEINFONO_TIME_FIELD.cpp_type = 1

ACT405RECEIVEINFONO_MSG.name = "Act405ReceiveInfoNO"
ACT405RECEIVEINFONO_MSG.full_name = ".Act405ReceiveInfoNO"
ACT405RECEIVEINFONO_MSG.filename = "Activity405Extension"
ACT405RECEIVEINFONO_MSG.nested_types = {}
ACT405RECEIVEINFONO_MSG.enum_types = {}
ACT405RECEIVEINFONO_MSG.fields = {tb.ACT405RECEIVEINFONO_ROLEINFO_FIELD, tb.ACT405RECEIVEINFONO_CARDID_FIELD, tb.ACT405RECEIVEINFONO_TIME_FIELD}
ACT405RECEIVEINFONO_MSG.is_extendable = false
ACT405RECEIVEINFONO_MSG.extensions = {}
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.name = "useBoxInfos"
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.full_name = ".UseAct405RandomBoxRequest.useBoxInfos"
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.number = 1
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.index = 0
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.label = 3
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.has_default_value = false
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.default_value = {}
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.message_type = ACT405RANDOMBOXINFONO_MSG
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.type = 11
tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD.cpp_type = 10

USEACT405RANDOMBOXREQUEST_MSG.name = "UseAct405RandomBoxRequest"
USEACT405RANDOMBOXREQUEST_MSG.full_name = ".UseAct405RandomBoxRequest"
USEACT405RANDOMBOXREQUEST_MSG.filename = "Activity405Extension"
USEACT405RANDOMBOXREQUEST_MSG.nested_types = {}
USEACT405RANDOMBOXREQUEST_MSG.enum_types = {}
USEACT405RANDOMBOXREQUEST_MSG.fields = {tb.USEACT405RANDOMBOXREQUEST_USEBOXINFOS_FIELD}
USEACT405RANDOMBOXREQUEST_MSG.is_extendable = false
USEACT405RANDOMBOXREQUEST_MSG.extensions = {}

ALL_CARD = 0
Act405GetLevelRewardReply = protobuf.Message(ACT405GETLEVELREWARDREPLY_MSG)
Act405GetLevelRewardRequest = protobuf.Message(ACT405GETLEVELREWARDREQUEST_MSG)
Act405RandomBoxInfoNO = protobuf.Message(ACT405RANDOMBOXINFONO_MSG)
Act405ReceiveInfoNO = protobuf.Message(ACT405RECEIVEINFONO_MSG)
GetAct405InfoReply = protobuf.Message(GETACT405INFOREPLY_MSG)
GetAct405InfoRequest = protobuf.Message(GETACT405INFOREQUEST_MSG)
GetAct405ReceiveInfoReply = protobuf.Message(GETACT405RECEIVEINFOREPLY_MSG)
GetAct405ReceiveInfoRequest = protobuf.Message(GETACT405RECEIVEINFOREQUEST_MSG)
GetAct405RewardReply = protobuf.Message(GETACT405REWARDREPLY_MSG)
GetAct405RewardRequest = protobuf.Message(GETACT405REWARDREQUEST_MSG)
ModifyAct405ReceiveLimitReply = protobuf.Message(MODIFYACT405RECEIVELIMITREPLY_MSG)
ModifyAct405ReceiveLimitRequest = protobuf.Message(MODIFYACT405RECEIVELIMITREQUEST_MSG)
ONLY_REQUEST_CARD = 1
UseAct405RandomBoxReply = protobuf.Message(USEACT405RANDOMBOXREPLY_MSG)
UseAct405RandomBoxRequest = protobuf.Message(USEACT405RANDOMBOXREQUEST_MSG)

return _G["logic.proto.Activity405Extension_pb"]
