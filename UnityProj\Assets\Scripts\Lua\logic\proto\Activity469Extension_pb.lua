-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity469Extension_pb", package.seeall)


local tb = {}
tb.ACT469GAMEENUM_ENUM = protobuf.EnumDescriptor()
tb.ACT469GAMEENUM_FLYING_CHESS_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT469GAMEENUM_LIARS_BAR_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT469GAMEENUM_ACT_252_GAME_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT469GAMEENUM_ACT_404_GAME_ENUMITEM = protobuf.EnumValueDescriptor()
tb.ACT469GAMEENUM_TETRIS_ENUMITEM = protobuf.EnumValueDescriptor()
GAINACT469REWARDREQUEST_MSG = protobuf.Descriptor()
tb.GAINACT469REWARDREQUEST_TASKID_FIELD = protobuf.FieldDescriptor()
GAINACT469REWARDREPLY_MSG = protobuf.Descriptor()
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ACT469TASKNO_MSG = protobuf.Descriptor()
tb.ACT469TASKNO_TASKID_FIELD = protobuf.FieldDescriptor()
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD = protobuf.FieldDescriptor()
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD = protobuf.FieldDescriptor()
GETACT469INFOREQUEST_MSG = protobuf.Descriptor()
GETACT469INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT469INFOREPLY_CURTASKID_FIELD = protobuf.FieldDescriptor()
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD = protobuf.FieldDescriptor()
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD = protobuf.FieldDescriptor()

tb.ACT469GAMEENUM_FLYING_CHESS_ENUMITEM.name = "FLYING_CHESS"
tb.ACT469GAMEENUM_FLYING_CHESS_ENUMITEM.index = 0
tb.ACT469GAMEENUM_FLYING_CHESS_ENUMITEM.number = 1
tb.ACT469GAMEENUM_LIARS_BAR_ENUMITEM.name = "LIARS_BAR"
tb.ACT469GAMEENUM_LIARS_BAR_ENUMITEM.index = 1
tb.ACT469GAMEENUM_LIARS_BAR_ENUMITEM.number = 2
tb.ACT469GAMEENUM_ACT_252_GAME_ENUMITEM.name = "ACT_252_GAME"
tb.ACT469GAMEENUM_ACT_252_GAME_ENUMITEM.index = 2
tb.ACT469GAMEENUM_ACT_252_GAME_ENUMITEM.number = 3
tb.ACT469GAMEENUM_ACT_404_GAME_ENUMITEM.name = "ACT_404_GAME"
tb.ACT469GAMEENUM_ACT_404_GAME_ENUMITEM.index = 3
tb.ACT469GAMEENUM_ACT_404_GAME_ENUMITEM.number = 4
tb.ACT469GAMEENUM_TETRIS_ENUMITEM.name = "TETRIS"
tb.ACT469GAMEENUM_TETRIS_ENUMITEM.index = 4
tb.ACT469GAMEENUM_TETRIS_ENUMITEM.number = 5
tb.ACT469GAMEENUM_ENUM.name = "Act469GameEnum"
tb.ACT469GAMEENUM_ENUM.full_name = ".Act469GameEnum"
tb.ACT469GAMEENUM_ENUM.values = {tb.ACT469GAMEENUM_FLYING_CHESS_ENUMITEM,tb.ACT469GAMEENUM_LIARS_BAR_ENUMITEM,tb.ACT469GAMEENUM_ACT_252_GAME_ENUMITEM,tb.ACT469GAMEENUM_ACT_404_GAME_ENUMITEM,tb.ACT469GAMEENUM_TETRIS_ENUMITEM}
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.name = "taskId"
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.full_name = ".GainAct469RewardRequest.taskId"
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.number = 1
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.index = 0
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.label = 2
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.has_default_value = false
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.default_value = 0
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.type = 5
tb.GAINACT469REWARDREQUEST_TASKID_FIELD.cpp_type = 1

GAINACT469REWARDREQUEST_MSG.name = "GainAct469RewardRequest"
GAINACT469REWARDREQUEST_MSG.full_name = ".GainAct469RewardRequest"
GAINACT469REWARDREQUEST_MSG.filename = "Activity469Extension"
GAINACT469REWARDREQUEST_MSG.nested_types = {}
GAINACT469REWARDREQUEST_MSG.enum_types = {}
GAINACT469REWARDREQUEST_MSG.fields = {tb.GAINACT469REWARDREQUEST_TASKID_FIELD}
GAINACT469REWARDREQUEST_MSG.is_extendable = false
GAINACT469REWARDREQUEST_MSG.extensions = {}
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.full_name = ".GainAct469RewardReply.changeSetId"
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GAINACT469REWARDREPLY_MSG.name = "GainAct469RewardReply"
GAINACT469REWARDREPLY_MSG.full_name = ".GainAct469RewardReply"
GAINACT469REWARDREPLY_MSG.filename = "Activity469Extension"
GAINACT469REWARDREPLY_MSG.nested_types = {}
GAINACT469REWARDREPLY_MSG.enum_types = {}
GAINACT469REWARDREPLY_MSG.fields = {tb.GAINACT469REWARDREPLY_CHANGESETID_FIELD}
GAINACT469REWARDREPLY_MSG.is_extendable = false
GAINACT469REWARDREPLY_MSG.extensions = {}
tb.ACT469TASKNO_TASKID_FIELD.name = "taskId"
tb.ACT469TASKNO_TASKID_FIELD.full_name = ".Act469TaskNO.taskId"
tb.ACT469TASKNO_TASKID_FIELD.number = 1
tb.ACT469TASKNO_TASKID_FIELD.index = 0
tb.ACT469TASKNO_TASKID_FIELD.label = 2
tb.ACT469TASKNO_TASKID_FIELD.has_default_value = false
tb.ACT469TASKNO_TASKID_FIELD.default_value = 0
tb.ACT469TASKNO_TASKID_FIELD.type = 5
tb.ACT469TASKNO_TASKID_FIELD.cpp_type = 1

tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.name = "gamePlayCount"
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.full_name = ".Act469TaskNO.gamePlayCount"
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.number = 2
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.index = 1
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.label = 2
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.has_default_value = false
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.default_value = 0
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.type = 5
tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD.cpp_type = 1

tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.name = "hasGainedReward"
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.full_name = ".Act469TaskNO.hasGainedReward"
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.number = 3
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.index = 2
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.label = 2
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.has_default_value = false
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.default_value = false
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.type = 8
tb.ACT469TASKNO_HASGAINEDREWARD_FIELD.cpp_type = 7

ACT469TASKNO_MSG.name = "Act469TaskNO"
ACT469TASKNO_MSG.full_name = ".Act469TaskNO"
ACT469TASKNO_MSG.filename = "Activity469Extension"
ACT469TASKNO_MSG.nested_types = {}
ACT469TASKNO_MSG.enum_types = {}
ACT469TASKNO_MSG.fields = {tb.ACT469TASKNO_TASKID_FIELD, tb.ACT469TASKNO_GAMEPLAYCOUNT_FIELD, tb.ACT469TASKNO_HASGAINEDREWARD_FIELD}
ACT469TASKNO_MSG.is_extendable = false
ACT469TASKNO_MSG.extensions = {}
GETACT469INFOREQUEST_MSG.name = "GetAct469InfoRequest"
GETACT469INFOREQUEST_MSG.full_name = ".GetAct469InfoRequest"
GETACT469INFOREQUEST_MSG.filename = "Activity469Extension"
GETACT469INFOREQUEST_MSG.nested_types = {}
GETACT469INFOREQUEST_MSG.enum_types = {}
GETACT469INFOREQUEST_MSG.fields = {}
GETACT469INFOREQUEST_MSG.is_extendable = false
GETACT469INFOREQUEST_MSG.extensions = {}
tb.GETACT469INFOREPLY_CURTASKID_FIELD.name = "curTaskId"
tb.GETACT469INFOREPLY_CURTASKID_FIELD.full_name = ".GetAct469InfoReply.curTaskId"
tb.GETACT469INFOREPLY_CURTASKID_FIELD.number = 1
tb.GETACT469INFOREPLY_CURTASKID_FIELD.index = 0
tb.GETACT469INFOREPLY_CURTASKID_FIELD.label = 1
tb.GETACT469INFOREPLY_CURTASKID_FIELD.has_default_value = false
tb.GETACT469INFOREPLY_CURTASKID_FIELD.default_value = 0
tb.GETACT469INFOREPLY_CURTASKID_FIELD.type = 5
tb.GETACT469INFOREPLY_CURTASKID_FIELD.cpp_type = 1

tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.name = "curTaskProgress"
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.full_name = ".GetAct469InfoReply.curTaskProgress"
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.number = 2
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.index = 1
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.label = 1
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.has_default_value = false
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.default_value = 0
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.type = 5
tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD.cpp_type = 1

tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.name = "startedTasks"
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.full_name = ".GetAct469InfoReply.startedTasks"
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.number = 4
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.index = 2
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.label = 3
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.has_default_value = false
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.default_value = {}
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.message_type = ACT469TASKNO_MSG
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.type = 11
tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD.cpp_type = 10

GETACT469INFOREPLY_MSG.name = "GetAct469InfoReply"
GETACT469INFOREPLY_MSG.full_name = ".GetAct469InfoReply"
GETACT469INFOREPLY_MSG.filename = "Activity469Extension"
GETACT469INFOREPLY_MSG.nested_types = {}
GETACT469INFOREPLY_MSG.enum_types = {}
GETACT469INFOREPLY_MSG.fields = {tb.GETACT469INFOREPLY_CURTASKID_FIELD, tb.GETACT469INFOREPLY_CURTASKPROGRESS_FIELD, tb.GETACT469INFOREPLY_STARTEDTASKS_FIELD}
GETACT469INFOREPLY_MSG.is_extendable = false
GETACT469INFOREPLY_MSG.extensions = {}

ACT_252_GAME = 3
ACT_404_GAME = 4
Act469TaskNO = protobuf.Message(ACT469TASKNO_MSG)
FLYING_CHESS = 1
GainAct469RewardReply = protobuf.Message(GAINACT469REWARDREPLY_MSG)
GainAct469RewardRequest = protobuf.Message(GAINACT469REWARDREQUEST_MSG)
GetAct469InfoReply = protobuf.Message(GETACT469INFOREPLY_MSG)
GetAct469InfoRequest = protobuf.Message(GETACT469INFOREQUEST_MSG)
LIARS_BAR = 2
TETRIS = 5

return _G["logic.proto.Activity469Extension_pb"]
