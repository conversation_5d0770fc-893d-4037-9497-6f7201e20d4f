module("logic.extensions.sharestreet.view.ShareStreetFurnitureView",package.seeall)
---@class ShareStreetFurnitureView
local ShareStreetFurnitureView = class("ShareStreetFurnitureView",ViewComponent)

function ShareStreetFurnitureView:ctor()
	ShareStreetFurnitureView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetFurnitureView:buildUI()
	ShareStreetFurnitureView.super.buildUI(self)

    self.closeBtn = self:getBtn("btnclose/btnClose")
    self.closeBtn:AddClickListener(self.onClickClose, self)

	self._btnCancel = self:getBtn("rightGo/btnCancel")
	self._btnAdd = self:getBtn("btnAdd")

	self._tgLeftTab = ToggleGroup.New(handler(self._onClickLeftTab, self))
	self._tgLeftTab:addView(self:getGo("tabGo/Content/btnAll"))
	self._tgLeftTab:addView(self:getGo("tabGo/Content/btn_1"))
	self._tgLeftTab:addView(self:getGo("tabGo/Content/btn_2"))
	self._tgLeftTab:addView(self:getGo("tabGo/Content/btn_3"))
	self._tgLeftTab:addView(self:getGo("tabGo/Content/btn_4"))
	self._tgLeftTab:addView(self:getGo("tabGo/Content/btn_5"))

	-- self._txtTip = self:getText("countGo/tipTxt")
	self._txtCount = self:getText("countGo/countTxt")

	self._goLine = self:getGo("leftGo/Image")
end

--- view初始化时会执行，在buildUI之后
function ShareStreetFurnitureView:bindEvents()
	ShareStreetFurnitureView.super.bindEvents(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetFurnitureView:onEnter()
	ShareStreetFurnitureView.super.onEnter(self)

	-- ShareStreetAgent.instance:sendGetPersonShareFurnitureCountRequest(function(list)
	-- 	self._viewPresentor.listComp:setItems(list)
	-- end, self)
	self._userId2ItemList = {}
	self._userId2ItemMap = {}
	self._allItemList = {}
	ShareStreetAgent.instance:sendGetStreetShareFurnitureCountRequest(false, handler(self._onGetFurnitureInfo, self))

	self._btnCancel:AddClickListener(self._onClickCancel, self)
	self._btnAdd:AddClickListener(self._onClickAdd, self)

	self._myStreetInfo = ShareStreetModel.instance:getUserInfo()
	local memberList = self._myStreetInfo:getMemberList()
	for i = 1, 5 do
		local goTab = self:getGo("tabGo/Content/btn_" .. i)
		local member = memberList[i]
		if member then
			goTab:SetActive(true)
			goutil.findChild(goTab, "imgYou"):SetActive(member.roleInfo.id == UserInfo.userId)
			goutil.findChild(goTab, "imgMaster"):SetActive(i == 1)
			HeadPortraitHelper.instance:setHeadPortraitWithUserId(goutil.findChild(goTab, "headIcon"), member.roleInfo.id)
		else
			goTab:SetActive(false)
		end
	end
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetFurnitureView:onEnterFinished()
	ShareStreetFurnitureView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetFurnitureView:onExit()
	ShareStreetFurnitureView.super.onExit(self)

	self._btnCancel:RemoveClickListener()
	self._btnAdd:RemoveClickListener()
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetFurnitureView:onExitFinished()
	ShareStreetFurnitureView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetFurnitureView:unbindEvents()
	ShareStreetFurnitureView.super.unbindEvents(self)
end

--- view销毁时会执行
function ShareStreetFurnitureView:destroyUI()
	ShareStreetFurnitureView.super.destroyUI(self)
end

-- function ShareStreetFurnitureView:onClickShare()
--     local selectItems = self._viewPresentor.listComp:getShareItems()
--     if #selectItems > 0 then
--         ShareStreetAgent.instance:sendStreetShareFurnitureRequest(selectItems, function()
--             self._viewPresentor.listComp:setItems(ShareStreetFacade.instance:getCanShareFurnitures())
--         end, self)
--     end
-- end

function ShareStreetFurnitureView:onClickClose()
    self:close()
end

function ShareStreetFurnitureView:_onClickAdd()
	-- self:close()
	ViewMgr.instance:open("ShareStreetAddFurnitureView", ShareStreetAddFurnitureView.ADD_SHARE, self._totalFurnitureCount)
end

function ShareStreetFurnitureView:_onGetFurnitureInfo(totalCount, userFurnitureCount)
	if userFurnitureCount then
		for _, no in ipairs(userFurnitureCount) do
			local list = self._userId2ItemList[no.userId]
			if not list then
				list = {}
				self._userId2ItemList[no.userId] = list
			end
			if self._userId2ItemMap[no.userId] == nil then
				self._userId2ItemMap[no.userId] = {}
			end
			for __, item in ipairs(no.furnitureCount) do
				local newItem = Item.New(item.furnitureId, item.count)
				newItem.useCount = item.useCount
				self._userId2ItemMap[no.userId][newItem.id] = newItem
				table.insert(list, newItem)
				table.insert(self._allItemList, newItem)
			end
		end
	end

	local memberList = self._myStreetInfo:getMemberList()
	local myIndex = 1
	for i, member in ipairs(memberList) do
		if member.roleInfo.id == UserInfo.userId then
			myIndex = i + 1 ---因为第一个tab是全部, 所以这里要加1
			break
		end
	end
	self._tgLeftTab:clickViews(true, myIndex) ---默认选中自己的tab
end

function ShareStreetFurnitureView:_onClickLeftTab(index, isSelected)
	if not isSelected then return end
	self._viewPresentor.listComp:reset()
	self._selectedLeftTabIndex = index
	self:_updateView()
end

function ShareStreetFurnitureView:_updateView()
	local itemList
	local canShare
	local totalFurnitureCount = 0
	if self._selectedLeftTabIndex == 1 then
		itemList = self:_getAllItemList()
		canShare = false
	else
		local memberList = self._myStreetInfo:getMemberList()
		local member = memberList[self._selectedLeftTabIndex - 1]
		canShare = member.roleInfo.id == UserInfo.userId
		itemList = self._userId2ItemList[member.roleInfo.id] or {}
	end
	self._viewPresentor.listComp:setCanShare(canShare)
	self._btnCancel.gameObject:SetActive(canShare)
	self._viewPresentor.listComp:setItems(itemList)
	self._goLine:SetActive(#itemList > 0)

	for _, item in ipairs(itemList) do
		totalFurnitureCount = totalFurnitureCount + item.num
	end
	local maxCount = ShareStreetCfg.instance:getCommonConfigValue("maxShareFurnitureCount")
	if self._selectedLeftTabIndex == 1 then
		maxCount = maxCount * self._myStreetInfo:getMemberCount()
	end
	self._txtCount.text = string.format("%s/%s", totalFurnitureCount, maxCount)
	self._totalFurnitureCount = totalFurnitureCount
end

function ShareStreetFurnitureView:_getAllItemList()
	local itemMap = {}
	for _, item in ipairs(self._allItemList) do
		if not itemMap[item.id] then
			itemMap[item.id] = Item.New(item.id, item.num)
			itemMap[item.id].useCount = item.useCount
		else
			itemMap[item.id].num = itemMap[item.id].num + item.num
			itemMap[item.id].useCount = itemMap[item.id].useCount + item.useCount
		end
	end
	local itemList = {}
	for k, v in pairs(itemMap) do
		table.insert(itemList, v)
	end
	return itemList
end

function ShareStreetFurnitureView:_onClickCancel()
	local selectItems = self._viewPresentor.listComp:getShareItems()
	local hasUsedItem = false
	for _, item in ipairs(selectItems) do
		if item.useCount and item.useCount > 0 then
			hasUsedItem = true
			break
		end
	end
	if hasUsedItem then
		DialogHelper.showConfirmDlg(lang("其他成员将无法使用被选择的家具，是否取消共享？"), handler(self._onConfirmCancel, self))
	else
		DialogHelper.showConfirmDlg(lang("是否取消共享选中的家具？"), handler(self._onConfirmCancel, self))
	end
end

function ShareStreetFurnitureView:_onConfirmCancel(bool)
	if bool then
		local selectItems = self._viewPresentor.listComp:getShareItems()
		if #selectItems > 0 then
			self._selectedItems = selectItems
			ShareStreetAgent.instance:sendStreetCancelShareFurnitureRequest(selectItems, handler(self._onCancelFurniture, self))
		end
	end
end

function ShareStreetFurnitureView:_onCancelFurniture()
	local items = self._viewPresentor.listComp:getSelectItems()
	for _, selectedItem in ipairs(self._selectedItems) do
		print("id", selectedItem.id, "num", selectedItem.num)
		local item = self._userId2ItemMap[UserInfo.userId][selectedItem.id]
		if item.num == selectedItem.num then
			table.removebyvalue(self._userId2ItemList[UserInfo.userId], item)
			table.removebyvalue(self._allItemList, item)
			self._userId2ItemMap[UserInfo.userId][item.id] = nil
		else
			item.num = item.num - selectedItem.num
		end
	end
	self._selectedItems = nil
	self:_updateView()
	self._viewPresentor.listComp:onMultiSelectChange(self._viewPresentor.listComp.isMultiSelect)
end	

return ShareStreetFurnitureView