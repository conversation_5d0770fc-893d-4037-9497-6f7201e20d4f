module("logic.extensions.room.core.unit.handler.base.ButterflyDreamHandler", package.seeall)

local ButterflyDreamHandler = class("ButterflyDreamHandler", ChairHandler)

function ButterflyDreamHandler:setViewInRoomPreview(isBinding, playerUnit, avatarInfo, pos)
	if isBinding then
		self.isNormal = true
		playerUnit:setClothes(avatarInfo)
		if self._unit and self._unit.roomSuite and self._unit.roomSuite.houseMo then
			if self._unit.roomSuite.houseMo:getRoomParams(RoomParams.Params_Mode) ~= RoomScene.Type.Normal then
				playerUnit:setDirection(self._unit:getMirror() and 1 or 0)
				self.isNormal = false
			end
		end
		if self.isNormal then
			-- playerUnit.skinView:hideHandItem()
			playerUnit:setDirection(0)
		end
		if playerUnit.setShadowActive then
			playerUnit:setShadowActive(false)
		else
			playerUnit.shadow:setVisible(false)
		end
		self:playAnimtion(self:getPlayKey(), true, false, playerUnit, true)
	else
		self:_trySetAnimation(tostring(self._unit.itemId), "idle", true)
		self:_trySetAnimation(tostring(self._unit.itemId) .. "_1", "idle", true)
	end
end
function ButterflyDreamHandler:setViewInScene()
	ButterflyDreamHandler.super.setViewInScene(self)
	self.isPlayed = false
	if self._unit and self._unit.serverId > 0 then
		local furnireutMO = self:_getFurnireutMO()
		self.isPlayed = furnireutMO.furnitureMark and furnireutMO.furnitureMark ~= 0
	end

	if self._unit and self._unit.roomSuite and self._unit.roomSuite.houseMo then
		if self._unit.roomSuite.houseMo:getRoomParams(RoomParams.Params_Mode) ~= RoomScene.Type.Normal then
			local extConfig = FurnitureConfig.getFurnitureDefine(self._unit.itemId)
			if extConfig and extConfig.ext and extConfig.ext.automation then
				self.isPlayed = true
			end
		end
	end
	local fun = function()
		self:playAnimtion(self.isPlayed and self:getPlayKey() or self:getIdleKey(), self.isPlayed, true)
	end
	self._unit:setUnitInfoEndHandler(fun)
	RoomController.instance:registerLocalNotify(RoomNotifyName.EnterHideHand, self.beginPlayAnim, self)
	RoomController.instance:registerLocalNotify(RoomNotifyName.LeaveHideHand, self.leaveHideHand, self)
	print("ButterflyDreamHandler:setViewInScene ", self.isPlayed)
	GlobalDispatcher:addListener(GlobalNotify.FurnitureInteractionSync, self.forceAnimSync, self)
end

function ButterflyDreamHandler:onMirror(mirror)
	self.mirror = mirror
	self:_setEffectActive(mirror)
end

function ButterflyDreamHandler:_setEffectActive(active)
	if self._unit and self._unit.loader and self._unit.loader:getInst() then
		goutil.setActive(goutil.findChild(self._unit.loader:getInst(), "effect/effect"), not active)
		goutil.setActive(goutil.findChild(self._unit.loader:getInst(), "effect/effect_mirror"), active)
	end
end

function ButterflyDreamHandler:setEffect(active)
	if self._unit and self._unit.loader and self._unit.loader:getInst() then
		goutil.setActive(goutil.findChild(self._unit.loader:getInst(), "effect"), active)
	end
end

function ButterflyDreamHandler:_startAction(walkPos, sitTrigger)
	local enable = self:_getHouseMo():getRoomParams(RoomParams.Furniture_Click_Enable)
	if not enable then return end
	if self.hasPlayer then
		if self.hasPlayer ~= UserInfo.userId then
			FlyTextManager.instance:showFlyText(lang("已经被其他小奥比占位了哦"))
		end
		return
	end
	self.onclick = true
	local furInfo = {self._unit.serverId, self.isPlayed and 1 or 0}
	SceneController.instance:furnitureStatusSync(furInfo)
end

function ButterflyDreamHandler:forceAnimSync(params)
	local param = params.params
	if param[1] and self._unit.serverId and param[1] == tostring(self._unit.serverId) then
		if param[2] then
			self.isPlayed = param[2] == "1"
			if self.onclick then
				local sitTrigger = self.triggerList[1]
				local walkPos = sitTrigger.config.triggerValue.startPos
				SceneController.instance:useObj(walkPos, sitTrigger.id, UseObjectType.HideHand, true, nil, self._unit.itemId)
			end
		end
	end
end

function ButterflyDreamHandler:beginPlayAnim(serverid, bindPos, trigger, playerUnit)
	print("ButterflyDreamHandler:beginPlayAnim")
	if self._unit:isBind(serverid) then
		self.hasPlayer = playerUnit.id
		if not self.isPlayed then
			playerUnit:setDirection(self:_getFurnireutMO():getMirror() and 1 or 0)
			playerUnit:lockDirection(true)
		end
		if HouseModel.instance:isMyHouse() then
			local houseId = self._unit.roomSuite.houseMo:getUserProperty(HouseUserProperty.HouseId, 0)
			if houseId > 0 then
				HouseAgent.instance:sendHouseFurnitureSwitchMarkRequest(houseId,self._unit.serverId, 1)
			else
				IslandAgent.instance:sendIslandFurnitureSwitchMarkRequest(self._unit.serverId, 1)
			end
		end
		self.isPlayed = not self.isPlayed
		self:playAnimtion(self.isPlayed and self:getPlayKey() or self:getIdleKey(), self.isPlayed, false, playerUnit)
	end
end

function ButterflyDreamHandler:playAnimtion(animState, isplay, skipPre, playerUnit, isUI)
	print("ButterflyDreamHandler:playAnimtion ", animState, isplay, skipPre, self.nowStatus)
	self:setEffect(false)
	if isplay then
		if not skipPre then
			local scusse, helper = self:_trySetAnimation(nil, self:beginPlayed(), false, nil, true)
			self.helper = helper
			if playerUnit then
				self.playerUnit = playerUnit
				if scusse and helper then
					helper:addCustomEventCallback("click", "start", self.playerEffect, self, true)
				end
				local bGo = goutil.findChild(self:_getFurGo(), "pos1/triggerPos")
				local bPos = GameUtils.getPos(bGo)
				local posGo = goutil.findChild(self:_getFurGo(), "pos1/endPos")
				local endPos = GameUtils.getPos(posGo)
				if not isUI then
					playerUnit:setPos(bPos.x, bPos.y, bPos.z)
					playerUnit.skinView:setAnimation("walk", true)
					playerUnit:lookTo(endPos.x,endPos.y, 0)
					playerUnit:moveTo(endPos.x, endPos.y, 0.5)
				else
					tfutil.SetXY(playerUnit.go, bPos.x, bPos.y)
					playerUnit.skinView:setAnimation("walk", true)
					if self.tween then
						self.tween:Kill()
						self.tween = nil
					end
					self.tween = DOTweenHelper.UpdateValue(bPos.x, endPos.x, 4.5, self.setContainerPos, self)
				end
			end
		end
		self:_tryAddAnimation(nil,self:getPlayKey(), true)
		self.nowStatus = true
	else
		if self.tween then
			self.tween:Kill()
			self.tween = nil
		end
		if self.helper then
			self.helper:removeAllCallback()
			self.helper = nil
		end
		local bGo = goutil.findChild(self:_getFurGo(), "pos1/triggerPos")
		local bPos = GameUtils.getPos(bGo)
		if self.playerUnit then
			tfutil.SetXY(self.playerUnit.go, bPos.x, bPos.y)
			self.playerUnit.skinView:setAnimation("idle", true)
		end
		if playerUnit then
			tfutil.SetXY(playerUnit.go, bPos.x, bPos.y)
			playerUnit.skinView:setAnimation("idle", true)
			if playerUnit.id == UserInfo.userId then
				SceneController.instance:stopAction(SceneActionType.UseObj)
			end
		end
		self:_trySetAnimation(nil, self:getCloseKey(), false, nil, true)
		self:_tryAddAnimation(nil, self:getIdleKey(), true)
		self.nowStatus = false
	end
	self:initNightFactor(self.nowStatus)
end

function ButterflyDreamHandler:setContainerPos(valua)
	tfutil.SetX(self.playerUnit.go, valua)
end

function ButterflyDreamHandler:playerEffect()
	if self.playerUnit then
		self.playerUnit.skinView:setAnimation("2.0_jiaju_huadiezhijing_click", false)
		self.playerUnit.skinView:addAnimation("idle", true)
		self:setEffect(true)
		removetimer(self.hideEffect, self)
		settimer(9, self.hideEffect, self, false)
	end
end

function ButterflyDreamHandler:hideEffect()
	self:setEffect(false)
	-- SceneController.instance:stopAction()
end

function ButterflyDreamHandler:initNightFactor(isPlay)
	if self._unit and self._unit.roomSuite then
		local houseid = self._unit.roomSuite.houseMo:getUserProperty(HouseUserProperty.HouseId, 0)
		if houseid <= 0 then
			-- PjAobi.CSGameUtils.SetMaterialBlock(self.animEffectGo, "_NightFactor", not isPlay and 1 or 0)
			if self._unit and self._unit.view then
				self._unit.view:setNightFactor(not isPlay)
			end
		end
	end
end

function ButterflyDreamHandler:leaveHideHand(serverid, bindPos, trigger, playerUnit)
	print("ButterflyDreamHandler:leaveHideHand")
	if self._unit:isBind(serverid) then
		self.onclick = false
		self.playerUnit = nil
		self.hasPlayer = false
		if self.tween then
			self.tween:Kill()
			self.tween = nil
		end
		if self.helper then
			self.helper:removeAllCallback()
			self.helper = nil
		end
		self:setEffect(false)
		if self.isPlayed then
			self:_trySetAnimation(nil,self:getPlayKey(), true, nil, true)
		else
			self:_trySetAnimation(nil,self:getIdleKey(), true, nil, true)
		end
	end
end

function ButterflyDreamHandler:getIdleKey()
	return "idle"
end

function ButterflyDreamHandler:beginPlayed()
	return "click"
end

function ButterflyDreamHandler:getPlayKey()
	return "clicked"
end

function ButterflyDreamHandler:getCloseKey()
	return "close"
end

function ButterflyDreamHandler:destroy()
	ButterflyDreamHandler.super.destroy(self)
	removetimer(self.hideEffect, self)
	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.EnterHideHand, self.beginPlayAnim, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.LeaveHideHand, self.leaveHideHand, self)
	GlobalDispatcher:removeListener(GlobalNotify.FurnitureInteractionSync, self.forceAnimSync, self)
	print("ButterflyDreamHandler:destroy ")
end

function ButterflyDreamHandler:dispose()
	print("ButterflyDreamHandler:dispose ")
end

return ButterflyDreamHandler