-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
module("logic.proto.Activity463Extension_pb", package.seeall)


local tb = {}
GAINACT463DAILYREWARDREQUEST_MSG = protobuf.Descriptor()
GAINACT463PL<PERSON>YCOUNTREWARDREQUEST_MSG = protobuf.Descriptor()
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD = protobuf.FieldDescriptor()
GAINACT463DAILYREWARDREPLY_MSG = protobuf.Descriptor()
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
LIARSBARSETTLEPUSH_MSG = protobuf.Descriptor()
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD = protobuf.FieldDescriptor()
LIARSBARPLAYCARDREPLY_MSG = protobuf.Descriptor()
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG = protobuf.Descriptor()
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD = protobuf.FieldDescriptor()
LIARSBARPLAYCARDREQUEST_MSG = protobuf.Descriptor()
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD = protobuf.FieldDescriptor()
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG = protobuf.Descriptor()
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD = protobuf.FieldDescriptor()
LIARSBARUSERSETTLEINFONO_MSG = protobuf.Descriptor()
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD = protobuf.FieldDescriptor()
LIARSBARQUESTIONREPLY_MSG = protobuf.Descriptor()
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD = protobuf.FieldDescriptor()
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG = protobuf.Descriptor()
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD = protobuf.FieldDescriptor()
LIARSBARDEALCARDPUSH_MSG = protobuf.Descriptor()
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD = protobuf.FieldDescriptor()
LIARSBARAUTOPLAYCARDPUSH_MSG = protobuf.Descriptor()
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD = protobuf.FieldDescriptor()
LIARSBARQUESTIONREQUEST_MSG = protobuf.Descriptor()
LIARSBARQUESTIONPUSH_MSG = protobuf.Descriptor()
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARQUESTIONPUSH_LYING_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD = protobuf.FieldDescriptor()
LIARSBARPLAYCARDPUSH_MSG = protobuf.Descriptor()
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD = protobuf.FieldDescriptor()
LIARSBARGAMEINFONO_MSG = protobuf.Descriptor()
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD = protobuf.FieldDescriptor()
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD = protobuf.FieldDescriptor()
GETACT463INFOREPLY_MSG = protobuf.Descriptor()
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD = protobuf.FieldDescriptor()
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD = protobuf.FieldDescriptor()
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD = protobuf.FieldDescriptor()
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD = protobuf.FieldDescriptor()
GAINACT463PLAYCOUNTREWARDREPLY_MSG = protobuf.Descriptor()
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
LIARSBAREARLYSETTLEREQUEST_MSG = protobuf.Descriptor()
LIARSBAREARLYSETTLEREPLY_MSG = protobuf.Descriptor()
GETACT463INFOREQUEST_MSG = protobuf.Descriptor()

GAINACT463DAILYREWARDREQUEST_MSG.name = "GainAct463DailyRewardRequest"
GAINACT463DAILYREWARDREQUEST_MSG.full_name = ".GainAct463DailyRewardRequest"
GAINACT463DAILYREWARDREQUEST_MSG.filename = "Activity463Extension"
GAINACT463DAILYREWARDREQUEST_MSG.nested_types = {}
GAINACT463DAILYREWARDREQUEST_MSG.enum_types = {}
GAINACT463DAILYREWARDREQUEST_MSG.fields = {}
GAINACT463DAILYREWARDREQUEST_MSG.is_extendable = false
GAINACT463DAILYREWARDREQUEST_MSG.extensions = {}
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.name = "rewardId"
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.full_name = ".GainAct463PlayCountRewardRequest.rewardId"
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.number = 1
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.index = 0
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.label = 2
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.has_default_value = false
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.default_value = 0
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.type = 5
tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD.cpp_type = 1

GAINACT463PLAYCOUNTREWARDREQUEST_MSG.name = "GainAct463PlayCountRewardRequest"
GAINACT463PLAYCOUNTREWARDREQUEST_MSG.full_name = ".GainAct463PlayCountRewardRequest"
GAINACT463PLAYCOUNTREWARDREQUEST_MSG.filename = "Activity463Extension"
GAINACT463PLAYCOUNTREWARDREQUEST_MSG.nested_types = {}
GAINACT463PLAYCOUNTREWARDREQUEST_MSG.enum_types = {}
GAINACT463PLAYCOUNTREWARDREQUEST_MSG.fields = {tb.GAINACT463PLAYCOUNTREWARDREQUEST_REWARDID_FIELD}
GAINACT463PLAYCOUNTREWARDREQUEST_MSG.is_extendable = false
GAINACT463PLAYCOUNTREWARDREQUEST_MSG.extensions = {}
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.full_name = ".GainAct463DailyRewardReply.changeSetId"
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GAINACT463DAILYREWARDREPLY_MSG.name = "GainAct463DailyRewardReply"
GAINACT463DAILYREWARDREPLY_MSG.full_name = ".GainAct463DailyRewardReply"
GAINACT463DAILYREWARDREPLY_MSG.filename = "Activity463Extension"
GAINACT463DAILYREWARDREPLY_MSG.nested_types = {}
GAINACT463DAILYREWARDREPLY_MSG.enum_types = {}
GAINACT463DAILYREWARDREPLY_MSG.fields = {tb.GAINACT463DAILYREWARDREPLY_CHANGESETID_FIELD}
GAINACT463DAILYREWARDREPLY_MSG.is_extendable = false
GAINACT463DAILYREWARDREPLY_MSG.extensions = {}
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.name = "useSettleInfos"
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.full_name = ".LiarsBarSettlePush.useSettleInfos"
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.number = 1
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.index = 0
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.label = 3
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.has_default_value = false
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.default_value = {}
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.message_type = LIARSBARUSERSETTLEINFONO_MSG
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.type = 11
tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD.cpp_type = 10

LIARSBARSETTLEPUSH_MSG.name = "LiarsBarSettlePush"
LIARSBARSETTLEPUSH_MSG.full_name = ".LiarsBarSettlePush"
LIARSBARSETTLEPUSH_MSG.filename = "Activity463Extension"
LIARSBARSETTLEPUSH_MSG.nested_types = {}
LIARSBARSETTLEPUSH_MSG.enum_types = {}
LIARSBARSETTLEPUSH_MSG.fields = {tb.LIARSBARSETTLEPUSH_USESETTLEINFOS_FIELD}
LIARSBARSETTLEPUSH_MSG.is_extendable = false
LIARSBARSETTLEPUSH_MSG.extensions = {}
LIARSBARPLAYCARDREPLY_MSG.name = "LiarsBarPlayCardReply"
LIARSBARPLAYCARDREPLY_MSG.full_name = ".LiarsBarPlayCardReply"
LIARSBARPLAYCARDREPLY_MSG.filename = "Activity463Extension"
LIARSBARPLAYCARDREPLY_MSG.nested_types = {}
LIARSBARPLAYCARDREPLY_MSG.enum_types = {}
LIARSBARPLAYCARDREPLY_MSG.fields = {}
LIARSBARPLAYCARDREPLY_MSG.is_extendable = false
LIARSBARPLAYCARDREPLY_MSG.extensions = {}
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.name = "dead"
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.full_name = ".LiarsBarPlayRussiaRouletteReply.dead"
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.number = 1
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.index = 0
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.label = 2
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.has_default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.type = 8
tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD.cpp_type = 7

LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.name = "LiarsBarPlayRussiaRouletteReply"
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.full_name = ".LiarsBarPlayRussiaRouletteReply"
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.filename = "Activity463Extension"
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.nested_types = {}
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.enum_types = {}
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.fields = {tb.LIARSBARPLAYRUSSIAROULETTEREPLY_DEAD_FIELD}
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.is_extendable = false
LIARSBARPLAYRUSSIAROULETTEREPLY_MSG.extensions = {}
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.name = "cards"
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.full_name = ".LiarsBarPlayCardRequest.cards"
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.number = 1
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.index = 0
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.label = 3
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.has_default_value = false
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.default_value = {}
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.type = 5
tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD.cpp_type = 1

LIARSBARPLAYCARDREQUEST_MSG.name = "LiarsBarPlayCardRequest"
LIARSBARPLAYCARDREQUEST_MSG.full_name = ".LiarsBarPlayCardRequest"
LIARSBARPLAYCARDREQUEST_MSG.filename = "Activity463Extension"
LIARSBARPLAYCARDREQUEST_MSG.nested_types = {}
LIARSBARPLAYCARDREQUEST_MSG.enum_types = {}
LIARSBARPLAYCARDREQUEST_MSG.fields = {tb.LIARSBARPLAYCARDREQUEST_CARDS_FIELD}
LIARSBARPLAYCARDREQUEST_MSG.is_extendable = false
LIARSBARPLAYCARDREQUEST_MSG.extensions = {}
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.name = "index"
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.full_name = ".LiarsBarPlayRussiaRouletteRequest.index"
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.number = 1
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.index = 0
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.label = 2
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.has_default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.default_value = 0
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.type = 5
tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD.cpp_type = 1

LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.name = "LiarsBarPlayRussiaRouletteRequest"
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.full_name = ".LiarsBarPlayRussiaRouletteRequest"
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.filename = "Activity463Extension"
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.nested_types = {}
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.enum_types = {}
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.fields = {tb.LIARSBARPLAYRUSSIAROULETTEREQUEST_INDEX_FIELD}
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.is_extendable = false
LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG.extensions = {}
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.name = "userId"
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.full_name = ".LiarsBarUserSettleInfoNO.userId"
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.number = 1
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.index = 0
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.label = 2
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.has_default_value = false
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.default_value = ""
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.type = 9
tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD.cpp_type = 9

tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.name = "ranking"
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.full_name = ".LiarsBarUserSettleInfoNO.ranking"
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.number = 2
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.index = 1
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.label = 2
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.has_default_value = false
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.default_value = 0
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.type = 5
tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD.cpp_type = 1

LIARSBARUSERSETTLEINFONO_MSG.name = "LiarsBarUserSettleInfoNO"
LIARSBARUSERSETTLEINFONO_MSG.full_name = ".LiarsBarUserSettleInfoNO"
LIARSBARUSERSETTLEINFONO_MSG.filename = "Activity463Extension"
LIARSBARUSERSETTLEINFONO_MSG.nested_types = {}
LIARSBARUSERSETTLEINFONO_MSG.enum_types = {}
LIARSBARUSERSETTLEINFONO_MSG.fields = {tb.LIARSBARUSERSETTLEINFONO_USERID_FIELD, tb.LIARSBARUSERSETTLEINFONO_RANKING_FIELD}
LIARSBARUSERSETTLEINFONO_MSG.is_extendable = false
LIARSBARUSERSETTLEINFONO_MSG.extensions = {}
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.name = "trueCard"
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.full_name = ".LiarsBarQuestionReply.trueCard"
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.number = 1
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.index = 0
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.label = 2
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.has_default_value = false
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.default_value = false
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.type = 8
tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD.cpp_type = 7

LIARSBARQUESTIONREPLY_MSG.name = "LiarsBarQuestionReply"
LIARSBARQUESTIONREPLY_MSG.full_name = ".LiarsBarQuestionReply"
LIARSBARQUESTIONREPLY_MSG.filename = "Activity463Extension"
LIARSBARQUESTIONREPLY_MSG.nested_types = {}
LIARSBARQUESTIONREPLY_MSG.enum_types = {}
LIARSBARQUESTIONREPLY_MSG.fields = {tb.LIARSBARQUESTIONREPLY_TRUECARD_FIELD}
LIARSBARQUESTIONREPLY_MSG.is_extendable = false
LIARSBARQUESTIONREPLY_MSG.extensions = {}
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.name = "operatorId"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.full_name = ".LiarsBarPlayRussiaRoulettePush.operatorId"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.number = 1
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.index = 0
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.label = 2
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.has_default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.default_value = ""
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.type = 9
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD.cpp_type = 9

tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.name = "index"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.full_name = ".LiarsBarPlayRussiaRoulettePush.index"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.number = 2
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.index = 1
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.label = 2
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.has_default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.default_value = 0
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.type = 5
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD.cpp_type = 1

tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.name = "dead"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.full_name = ".LiarsBarPlayRussiaRoulettePush.dead"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.number = 3
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.index = 2
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.label = 2
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.has_default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.type = 8
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD.cpp_type = 7

tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.name = "remainIndexes"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.full_name = ".LiarsBarPlayRussiaRoulettePush.remainIndexes"
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.number = 4
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.index = 3
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.label = 3
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.has_default_value = false
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.default_value = {}
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.type = 5
tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD.cpp_type = 1

LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.name = "LiarsBarPlayRussiaRoulettePush"
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.full_name = ".LiarsBarPlayRussiaRoulettePush"
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.filename = "Activity463Extension"
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.nested_types = {}
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.enum_types = {}
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.fields = {tb.LIARSBARPLAYRUSSIAROULETTEPUSH_OPERATORID_FIELD, tb.LIARSBARPLAYRUSSIAROULETTEPUSH_INDEX_FIELD, tb.LIARSBARPLAYRUSSIAROULETTEPUSH_DEAD_FIELD, tb.LIARSBARPLAYRUSSIAROULETTEPUSH_REMAININDEXES_FIELD}
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.is_extendable = false
LIARSBARPLAYRUSSIAROULETTEPUSH_MSG.extensions = {}
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.name = "roundNum"
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.full_name = ".LiarsBarDealCardPush.roundNum"
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.number = 1
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.index = 0
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.label = 2
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.has_default_value = false
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.default_value = 0
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.type = 5
tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD.cpp_type = 1

tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.name = "trueCard"
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.full_name = ".LiarsBarDealCardPush.trueCard"
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.number = 2
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.index = 1
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.label = 2
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.has_default_value = false
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.default_value = 0
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.type = 5
tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD.cpp_type = 1

tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.name = "selfCards"
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.full_name = ".LiarsBarDealCardPush.selfCards"
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.number = 3
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.index = 2
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.label = 3
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.has_default_value = false
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.default_value = {}
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.type = 5
tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD.cpp_type = 1

tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.name = "updatedOperatorId"
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.full_name = ".LiarsBarDealCardPush.updatedOperatorId"
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.number = 4
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.index = 3
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.label = 2
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.has_default_value = false
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.default_value = ""
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.type = 9
tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD.cpp_type = 9

LIARSBARDEALCARDPUSH_MSG.name = "LiarsBarDealCardPush"
LIARSBARDEALCARDPUSH_MSG.full_name = ".LiarsBarDealCardPush"
LIARSBARDEALCARDPUSH_MSG.filename = "Activity463Extension"
LIARSBARDEALCARDPUSH_MSG.nested_types = {}
LIARSBARDEALCARDPUSH_MSG.enum_types = {}
LIARSBARDEALCARDPUSH_MSG.fields = {tb.LIARSBARDEALCARDPUSH_ROUNDNUM_FIELD, tb.LIARSBARDEALCARDPUSH_TRUECARD_FIELD, tb.LIARSBARDEALCARDPUSH_SELFCARDS_FIELD, tb.LIARSBARDEALCARDPUSH_UPDATEDOPERATORID_FIELD}
LIARSBARDEALCARDPUSH_MSG.is_extendable = false
LIARSBARDEALCARDPUSH_MSG.extensions = {}
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.name = "card"
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.full_name = ".LiarsBarAutoPlayCardPush.card"
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.number = 1
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.index = 0
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.label = 2
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.has_default_value = false
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.default_value = 0
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.type = 5
tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD.cpp_type = 1

LIARSBARAUTOPLAYCARDPUSH_MSG.name = "LiarsBarAutoPlayCardPush"
LIARSBARAUTOPLAYCARDPUSH_MSG.full_name = ".LiarsBarAutoPlayCardPush"
LIARSBARAUTOPLAYCARDPUSH_MSG.filename = "Activity463Extension"
LIARSBARAUTOPLAYCARDPUSH_MSG.nested_types = {}
LIARSBARAUTOPLAYCARDPUSH_MSG.enum_types = {}
LIARSBARAUTOPLAYCARDPUSH_MSG.fields = {tb.LIARSBARAUTOPLAYCARDPUSH_CARD_FIELD}
LIARSBARAUTOPLAYCARDPUSH_MSG.is_extendable = false
LIARSBARAUTOPLAYCARDPUSH_MSG.extensions = {}
LIARSBARQUESTIONREQUEST_MSG.name = "LiarsBarQuestionRequest"
LIARSBARQUESTIONREQUEST_MSG.full_name = ".LiarsBarQuestionRequest"
LIARSBARQUESTIONREQUEST_MSG.filename = "Activity463Extension"
LIARSBARQUESTIONREQUEST_MSG.nested_types = {}
LIARSBARQUESTIONREQUEST_MSG.enum_types = {}
LIARSBARQUESTIONREQUEST_MSG.fields = {}
LIARSBARQUESTIONREQUEST_MSG.is_extendable = false
LIARSBARQUESTIONREQUEST_MSG.extensions = {}
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.name = "operatorId"
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.full_name = ".LiarsBarQuestionPush.operatorId"
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.number = 1
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.index = 0
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.label = 2
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.has_default_value = false
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.default_value = ""
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.type = 9
tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD.cpp_type = 9

tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.name = "targetUserId"
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.full_name = ".LiarsBarQuestionPush.targetUserId"
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.number = 2
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.index = 1
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.label = 2
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.has_default_value = false
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.default_value = ""
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.type = 9
tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD.cpp_type = 9

tb.LIARSBARQUESTIONPUSH_LYING_FIELD.name = "lying"
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.full_name = ".LiarsBarQuestionPush.lying"
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.number = 3
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.index = 2
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.label = 2
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.has_default_value = false
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.default_value = false
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.type = 8
tb.LIARSBARQUESTIONPUSH_LYING_FIELD.cpp_type = 7

tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.name = "cards"
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.full_name = ".LiarsBarQuestionPush.cards"
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.number = 4
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.index = 3
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.label = 3
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.has_default_value = false
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.default_value = {}
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.type = 5
tb.LIARSBARQUESTIONPUSH_CARDS_FIELD.cpp_type = 1

tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.name = "updatedOperatorId"
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.full_name = ".LiarsBarQuestionPush.updatedOperatorId"
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.number = 5
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.index = 4
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.label = 2
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.has_default_value = false
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.default_value = ""
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.type = 9
tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD.cpp_type = 9

LIARSBARQUESTIONPUSH_MSG.name = "LiarsBarQuestionPush"
LIARSBARQUESTIONPUSH_MSG.full_name = ".LiarsBarQuestionPush"
LIARSBARQUESTIONPUSH_MSG.filename = "Activity463Extension"
LIARSBARQUESTIONPUSH_MSG.nested_types = {}
LIARSBARQUESTIONPUSH_MSG.enum_types = {}
LIARSBARQUESTIONPUSH_MSG.fields = {tb.LIARSBARQUESTIONPUSH_OPERATORID_FIELD, tb.LIARSBARQUESTIONPUSH_TARGETUSERID_FIELD, tb.LIARSBARQUESTIONPUSH_LYING_FIELD, tb.LIARSBARQUESTIONPUSH_CARDS_FIELD, tb.LIARSBARQUESTIONPUSH_UPDATEDOPERATORID_FIELD}
LIARSBARQUESTIONPUSH_MSG.is_extendable = false
LIARSBARQUESTIONPUSH_MSG.extensions = {}
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.name = "operatorId"
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.full_name = ".LiarsBarPlayCardPush.operatorId"
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.number = 1
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.index = 0
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.label = 2
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.has_default_value = false
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.default_value = ""
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.type = 9
tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD.cpp_type = 9

tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.name = "cardNum"
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.full_name = ".LiarsBarPlayCardPush.cardNum"
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.number = 2
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.index = 1
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.label = 2
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.has_default_value = false
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.default_value = 0
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.type = 5
tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD.cpp_type = 1

tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.name = "remainCardNum"
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.full_name = ".LiarsBarPlayCardPush.remainCardNum"
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.number = 3
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.index = 2
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.label = 1
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.has_default_value = false
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.default_value = 0
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.type = 5
tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD.cpp_type = 1

tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.name = "updatedOperatorId"
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.full_name = ".LiarsBarPlayCardPush.updatedOperatorId"
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.number = 4
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.index = 3
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.label = 2
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.has_default_value = false
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.default_value = ""
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.type = 9
tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD.cpp_type = 9

LIARSBARPLAYCARDPUSH_MSG.name = "LiarsBarPlayCardPush"
LIARSBARPLAYCARDPUSH_MSG.full_name = ".LiarsBarPlayCardPush"
LIARSBARPLAYCARDPUSH_MSG.filename = "Activity463Extension"
LIARSBARPLAYCARDPUSH_MSG.nested_types = {}
LIARSBARPLAYCARDPUSH_MSG.enum_types = {}
LIARSBARPLAYCARDPUSH_MSG.fields = {tb.LIARSBARPLAYCARDPUSH_OPERATORID_FIELD, tb.LIARSBARPLAYCARDPUSH_CARDNUM_FIELD, tb.LIARSBARPLAYCARDPUSH_REMAINCARDNUM_FIELD, tb.LIARSBARPLAYCARDPUSH_UPDATEDOPERATORID_FIELD}
LIARSBARPLAYCARDPUSH_MSG.is_extendable = false
LIARSBARPLAYCARDPUSH_MSG.extensions = {}
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.name = "roundNum"
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.full_name = ".LiarsBarGameInfoNO.roundNum"
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.number = 1
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.index = 0
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.label = 2
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.default_value = 0
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.type = 5
tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD.cpp_type = 1

tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.name = "trueCard"
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.full_name = ".LiarsBarGameInfoNO.trueCard"
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.number = 2
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.index = 1
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.label = 2
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.default_value = 0
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.type = 5
tb.LIARSBARGAMEINFONO_TRUECARD_FIELD.cpp_type = 1

tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.name = "curOperatorId"
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.full_name = ".LiarsBarGameInfoNO.curOperatorId"
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.number = 3
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.index = 2
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.label = 1
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.default_value = ""
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.type = 9
tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD.cpp_type = 9

tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.name = "curOperationDeadline"
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.full_name = ".LiarsBarGameInfoNO.curOperationDeadline"
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.number = 4
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.index = 3
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.label = 1
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.default_value = 0
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.type = 5
tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD.cpp_type = 1

tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.name = "selfCards"
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.full_name = ".LiarsBarGameInfoNO.selfCards"
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.number = 5
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.index = 4
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.label = 3
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.default_value = {}
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.type = 5
tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD.cpp_type = 1

tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.name = "russiaRouletteRemainIndexes"
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.full_name = ".LiarsBarGameInfoNO.russiaRouletteRemainIndexes"
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.number = 6
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.index = 5
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.label = 3
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.default_value = {}
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.type = 5
tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD.cpp_type = 1

tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.name = "stateParams"
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.full_name = ".LiarsBarGameInfoNO.stateParams"
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.number = 7
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.index = 6
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.label = 1
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.default_value = ""
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.type = 9
tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD.cpp_type = 9

tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.name = "gameRoomState"
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.full_name = ".LiarsBarGameInfoNO.gameRoomState"
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.number = 8
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.index = 7
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.label = 1
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.has_default_value = false
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.default_value = 0
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.type = 5
tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD.cpp_type = 1

LIARSBARGAMEINFONO_MSG.name = "LiarsBarGameInfoNO"
LIARSBARGAMEINFONO_MSG.full_name = ".LiarsBarGameInfoNO"
LIARSBARGAMEINFONO_MSG.filename = "Activity463Extension"
LIARSBARGAMEINFONO_MSG.nested_types = {}
LIARSBARGAMEINFONO_MSG.enum_types = {}
LIARSBARGAMEINFONO_MSG.fields = {tb.LIARSBARGAMEINFONO_ROUNDNUM_FIELD, tb.LIARSBARGAMEINFONO_TRUECARD_FIELD, tb.LIARSBARGAMEINFONO_CUROPERATORID_FIELD, tb.LIARSBARGAMEINFONO_CUROPERATIONDEADLINE_FIELD, tb.LIARSBARGAMEINFONO_SELFCARDS_FIELD, tb.LIARSBARGAMEINFONO_RUSSIAROULETTEREMAININDEXES_FIELD, tb.LIARSBARGAMEINFONO_STATEPARAMS_FIELD, tb.LIARSBARGAMEINFONO_GAMEROOMSTATE_FIELD}
LIARSBARGAMEINFONO_MSG.is_extendable = false
LIARSBARGAMEINFONO_MSG.extensions = {}
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.name = "matchScore"
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.full_name = ".GetAct463InfoReply.matchScore"
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.number = 1
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.index = 0
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.label = 2
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.has_default_value = false
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.default_value = 0
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.type = 5
tb.GETACT463INFOREPLY_MATCHSCORE_FIELD.cpp_type = 1

tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.name = "todayMatchPlayCount"
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.full_name = ".GetAct463InfoReply.todayMatchPlayCount"
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.number = 2
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.index = 1
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.label = 2
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.has_default_value = false
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.default_value = 0
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.type = 5
tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD.cpp_type = 1

tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.name = "totalMatchPlayCount"
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.full_name = ".GetAct463InfoReply.totalMatchPlayCount"
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.number = 3
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.index = 2
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.label = 2
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.has_default_value = false
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.default_value = 0
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.type = 5
tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD.cpp_type = 1

tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.name = "gainedPlayCountRewards"
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.full_name = ".GetAct463InfoReply.gainedPlayCountRewards"
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.number = 4
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.index = 3
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.label = 3
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.has_default_value = false
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.default_value = {}
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.type = 5
tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD.cpp_type = 1

tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.name = "hasGainedDailyReward"
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.full_name = ".GetAct463InfoReply.hasGainedDailyReward"
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.number = 5
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.index = 4
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.label = 2
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.has_default_value = false
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.default_value = false
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.type = 8
tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD.cpp_type = 7

GETACT463INFOREPLY_MSG.name = "GetAct463InfoReply"
GETACT463INFOREPLY_MSG.full_name = ".GetAct463InfoReply"
GETACT463INFOREPLY_MSG.filename = "Activity463Extension"
GETACT463INFOREPLY_MSG.nested_types = {}
GETACT463INFOREPLY_MSG.enum_types = {}
GETACT463INFOREPLY_MSG.fields = {tb.GETACT463INFOREPLY_MATCHSCORE_FIELD, tb.GETACT463INFOREPLY_TODAYMATCHPLAYCOUNT_FIELD, tb.GETACT463INFOREPLY_TOTALMATCHPLAYCOUNT_FIELD, tb.GETACT463INFOREPLY_GAINEDPLAYCOUNTREWARDS_FIELD, tb.GETACT463INFOREPLY_HASGAINEDDAILYREWARD_FIELD}
GETACT463INFOREPLY_MSG.is_extendable = false
GETACT463INFOREPLY_MSG.extensions = {}
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.full_name = ".GainAct463PlayCountRewardReply.changeSetId"
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.number = 1
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.index = 0
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.label = 1
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.has_default_value = false
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.default_value = 0
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.type = 5
tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD.cpp_type = 1

GAINACT463PLAYCOUNTREWARDREPLY_MSG.name = "GainAct463PlayCountRewardReply"
GAINACT463PLAYCOUNTREWARDREPLY_MSG.full_name = ".GainAct463PlayCountRewardReply"
GAINACT463PLAYCOUNTREWARDREPLY_MSG.filename = "Activity463Extension"
GAINACT463PLAYCOUNTREWARDREPLY_MSG.nested_types = {}
GAINACT463PLAYCOUNTREWARDREPLY_MSG.enum_types = {}
GAINACT463PLAYCOUNTREWARDREPLY_MSG.fields = {tb.GAINACT463PLAYCOUNTREWARDREPLY_CHANGESETID_FIELD}
GAINACT463PLAYCOUNTREWARDREPLY_MSG.is_extendable = false
GAINACT463PLAYCOUNTREWARDREPLY_MSG.extensions = {}
LIARSBAREARLYSETTLEREQUEST_MSG.name = "LiarsBarEarlySettleRequest"
LIARSBAREARLYSETTLEREQUEST_MSG.full_name = ".LiarsBarEarlySettleRequest"
LIARSBAREARLYSETTLEREQUEST_MSG.filename = "Activity463Extension"
LIARSBAREARLYSETTLEREQUEST_MSG.nested_types = {}
LIARSBAREARLYSETTLEREQUEST_MSG.enum_types = {}
LIARSBAREARLYSETTLEREQUEST_MSG.fields = {}
LIARSBAREARLYSETTLEREQUEST_MSG.is_extendable = false
LIARSBAREARLYSETTLEREQUEST_MSG.extensions = {}
LIARSBAREARLYSETTLEREPLY_MSG.name = "LiarsBarEarlySettleReply"
LIARSBAREARLYSETTLEREPLY_MSG.full_name = ".LiarsBarEarlySettleReply"
LIARSBAREARLYSETTLEREPLY_MSG.filename = "Activity463Extension"
LIARSBAREARLYSETTLEREPLY_MSG.nested_types = {}
LIARSBAREARLYSETTLEREPLY_MSG.enum_types = {}
LIARSBAREARLYSETTLEREPLY_MSG.fields = {}
LIARSBAREARLYSETTLEREPLY_MSG.is_extendable = false
LIARSBAREARLYSETTLEREPLY_MSG.extensions = {}
GETACT463INFOREQUEST_MSG.name = "GetAct463InfoRequest"
GETACT463INFOREQUEST_MSG.full_name = ".GetAct463InfoRequest"
GETACT463INFOREQUEST_MSG.filename = "Activity463Extension"
GETACT463INFOREQUEST_MSG.nested_types = {}
GETACT463INFOREQUEST_MSG.enum_types = {}
GETACT463INFOREQUEST_MSG.fields = {}
GETACT463INFOREQUEST_MSG.is_extendable = false
GETACT463INFOREQUEST_MSG.extensions = {}

GainAct463DailyRewardReply = protobuf.Message(GAINACT463DAILYREWARDREPLY_MSG)
GainAct463DailyRewardRequest = protobuf.Message(GAINACT463DAILYREWARDREQUEST_MSG)
GainAct463PlayCountRewardReply = protobuf.Message(GAINACT463PLAYCOUNTREWARDREPLY_MSG)
GainAct463PlayCountRewardRequest = protobuf.Message(GAINACT463PLAYCOUNTREWARDREQUEST_MSG)
GetAct463InfoReply = protobuf.Message(GETACT463INFOREPLY_MSG)
GetAct463InfoRequest = protobuf.Message(GETACT463INFOREQUEST_MSG)
LiarsBarAutoPlayCardPush = protobuf.Message(LIARSBARAUTOPLAYCARDPUSH_MSG)
LiarsBarDealCardPush = protobuf.Message(LIARSBARDEALCARDPUSH_MSG)
LiarsBarEarlySettleReply = protobuf.Message(LIARSBAREARLYSETTLEREPLY_MSG)
LiarsBarEarlySettleRequest = protobuf.Message(LIARSBAREARLYSETTLEREQUEST_MSG)
LiarsBarGameInfoNO = protobuf.Message(LIARSBARGAMEINFONO_MSG)
LiarsBarPlayCardPush = protobuf.Message(LIARSBARPLAYCARDPUSH_MSG)
LiarsBarPlayCardReply = protobuf.Message(LIARSBARPLAYCARDREPLY_MSG)
LiarsBarPlayCardRequest = protobuf.Message(LIARSBARPLAYCARDREQUEST_MSG)
LiarsBarPlayRussiaRoulettePush = protobuf.Message(LIARSBARPLAYRUSSIAROULETTEPUSH_MSG)
LiarsBarPlayRussiaRouletteReply = protobuf.Message(LIARSBARPLAYRUSSIAROULETTEREPLY_MSG)
LiarsBarPlayRussiaRouletteRequest = protobuf.Message(LIARSBARPLAYRUSSIAROULETTEREQUEST_MSG)
LiarsBarQuestionPush = protobuf.Message(LIARSBARQUESTIONPUSH_MSG)
LiarsBarQuestionReply = protobuf.Message(LIARSBARQUESTIONREPLY_MSG)
LiarsBarQuestionRequest = protobuf.Message(LIARSBARQUESTIONREQUEST_MSG)
LiarsBarSettlePush = protobuf.Message(LIARSBARSETTLEPUSH_MSG)
LiarsBarUserSettleInfoNO = protobuf.Message(LIARSBARUSERSETTLEINFONO_MSG)

return _G["logic.proto.Activity463Extension_pb"]
