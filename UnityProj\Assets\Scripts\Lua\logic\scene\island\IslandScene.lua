module("logic.scene.island.IslandScene",package.seeall)
local IslandScene = class("IslandScene", CommonSceneBase)

function IslandScene:overrideComps()
	self:_addComponent("mapMgr", RoomMapManager)
	self:_addComponent("controller", RoomSceneController)
	self:_addComponent("stage", IslandSceneStage)
	self:_addComponent("unitFactory", RoomSceneFactory)
	self:_addComponent("party", ScenePartyComp)
	self:_addComponent("traveller", TravellerBehSceneComp)
	self:_addComponent("species", SpeciesBehSceneComp)
	self:_addComponent("activity", IslandActivity)
end

function IslandScene:getHUDView()
	return "IslandHUD"
end

function IslandScene:onExit()
	FarmController.instance:onLeaveFarm()
	IslandModel.instance:clear()
	IslandScene.super.onExit(self)
	self._bolLoadRubbish = false
	self._boolEnterFinish = false
	self._loadShareStreetBgFinish = false
end

function IslandScene:onEnterFinished()
	self._loadShareStreetBgFinish = not HouseModel.instance:isInBlock()
	RoomController.instance:registerLocalNotify(RoomNotifyName.AllFurnitureLoaded, self.onLoadFurnitureFinshed, self)
	if not self._loadShareStreetBgFinish then
		ShareStreetController.instance:registerLocalNotify(ShareStreetLocalNotify.OnLoadShareStreetBg, self._onLoadBg, self)
	end
	IslandScene.super.onEnterFinished(self)
end

function IslandScene:setEnterFinishedHandler(enterFinishedHandler, enterFinishedObj)
	IslandScene.super.setEnterFinishedHandler(self, self._onEnterFinish, self)
	self._myFinishHandler = enterFinishedHandler
	self._myFinishTarget = enterFinishedObj
end

function IslandScene:_onLoadBg()
	self._loadShareStreetBgFinish = true
	self:_handlerEnterFinish()
	ShareStreetController.instance:unregisterLocalNotify(ShareStreetLocalNotify.OnLoadShareStreetBg, self._onLoadBg, self)
end

function IslandScene:onLoadFurnitureFinshed(factory)
	if factory ~= self.unitFactory then return end
	if self.unitFactory:getRoomSuite() ~= nil then
		FarmController.instance:onEnterFarm()
	end
	self._bolLoadRubbish = true
	self:_handlerEnterFinish()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.AllFurnitureLoaded, self.onLoadFurnitureFinshed, self)
end

function IslandScene:_onEnterFinish()
	self._boolEnterFinish = true
	self:_handlerEnterFinish()
end

function IslandScene:_handlerEnterFinish()
	if not self._bolLoadRubbish or not self._boolEnterFinish or not self._loadShareStreetBgFinish then return end
	if self._myFinishHandler then
		self._myFinishHandler(self._myFinishTarget)
	end
	self._boolEnterFinish = false
end

return IslandScene