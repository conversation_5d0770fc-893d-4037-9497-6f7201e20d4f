-- {excel:429合成玩法.xlsx, sheetName:export_杂项}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act_429_common", package.seeall)

local title = {activityId=1,key=2,value=3}

local dataList = {
	{2108, "costMaterialItemId", "16000684"},
	{2108, "costMaterialItemNum", "1"},
	{2108, "composeReward", "16000679:3"},
	{2108, "composeAreaWidth", "3"},
	{2108, "composeAreaLength", "7"},
	{2108, "CoinId", "16000679"},
	{2108, "taskType", "200274"},
	{2282, "costMaterialItemId", "16000744"},
	{2282, "costMaterialItemNum", "1"},
	{2282, "composeReward", "16000740:3"},
	{2282, "composeAreaWidth", "3"},
	{2282, "composeAreaLength", "7"},
	{2282, "CoinId", "16000740"},
	{2282, "taskType", "200274"},
	{2509, "costMaterialItemId", "16000744"},
	{2509, "costMaterialItemNum", "1"},
	{2509, "composeReward", "16000740:3"},
	{2509, "composeAreaWidth", "3"},
	{2509, "composeAreaLength", "7"},
	{2509, "CoinId", "16000740"},
	{2509, "taskType", "200274"},
}

local t_act_429_common = {
	[2108] = {
		["costMaterialItemId"] = dataList[1],
		["costMaterialItemNum"] = dataList[2],
		["composeReward"] = dataList[3],
		["composeAreaWidth"] = dataList[4],
		["composeAreaLength"] = dataList[5],
		["CoinId"] = dataList[6],
		["taskType"] = dataList[7],
	},
	[2282] = {
		["costMaterialItemId"] = dataList[8],
		["costMaterialItemNum"] = dataList[9],
		["composeReward"] = dataList[10],
		["composeAreaWidth"] = dataList[11],
		["composeAreaLength"] = dataList[12],
		["CoinId"] = dataList[13],
		["taskType"] = dataList[14],
	},
	[2509] = {
		["costMaterialItemId"] = dataList[15],
		["costMaterialItemNum"] = dataList[16],
		["composeReward"] = dataList[17],
		["composeAreaWidth"] = dataList[18],
		["composeAreaLength"] = dataList[19],
		["CoinId"] = dataList[20],
		["taskType"] = dataList[21],
	},
}

t_act_429_common.dataList = dataList
local mt
if Act429CommonDefine then
	mt = {
		__cname =  "Act429CommonDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act429CommonDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act_429_common