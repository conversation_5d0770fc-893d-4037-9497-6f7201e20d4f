module("logic.extensions.sharestreet.view.ShareStreetRankViewPresentor",package.seeall)
---@class ShareStreetRankViewPresentor
local ShareStreetRankViewPresentor = class("ShareStreetRankViewPresentor",ViewPresentor)

ShareStreetRankViewPresentor.Url_Item = "ui/street/streerpreviewitem.prefab"

function ShareStreetRankViewPresentor:ctor()
	ShareStreetRankViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetRankViewPresentor:dependWhatResources()
	return {"ui/street/streetpreview.prefab", ShareStreetRankViewPresentor.Url_Item}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetRankViewPresentor:buildViews()
	return {ShareStreetRankView.New()}
end

--- 配置view所在的ui层
function ShareStreetRankViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetRankViewPresentor