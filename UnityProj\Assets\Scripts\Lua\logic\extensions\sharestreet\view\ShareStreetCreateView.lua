module("logic.extensions.sharestreet.view.ShareStreetCreateView", package.seeall)
---@class ShareStreetCreateView
local ShareStreetCreateView = class("ShareStreetCreateView", ViewComponent)

function ShareStreetCreateView:ctor()
    ShareStreetCreateView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetCreateView:buildUI()
    ShareStreetCreateView.super.buildUI(self)

	self._btnOpen = self:getBtn("openGo/btnOpen")
    self._btnOk = self:getBtn("letterGo/desGo/btnOk")
    self._iptDes = self:getInput("letterGo/desGo/desInput")
    self._iptName = self:getInput("letterGo/desGo/nameInput")

	self._goOpen = self:getGo("openGo")
	self._goLetter = self:getGo("letterGo")
	self._goDes = self:getGo("letterGo/desGo")
	self._goSucc = self:getGo("letterGo/succeedGo")
	self._txtSuccName = self:getText("letterGo/succeedGo/txtName")
	self._txtSuccDes = self:getText("letterGo/succeedGo/txtDes")
	self._goOwner = self:getGo("letterGo/succeedGo/player/go")

    self._goEft1 = self:getGo("ui_createstreetview_effect_01")
    self._goEft2 = self:getGo("ui_createstreetview_effect_02")
	self._goEft2:SetActive(false)
	self._ani = self.mainGO:GetComponent("Animation")
	self._ani:Stop()
	-- self._go:GetComponent(typeof(UnityEngine.CanvasGroup))
    self._txtCount = self:getText("letterGo/desGo/desInput/txtCount")

	self._goShowStreetMap = self:getGo("letterGo/succeedGo/closeAndShowStreetMap")
	self._ctShowStreetMap = Framework.UIClickTrigger.Get(self._goShowStreetMap)

	self._streetNameMaxLength = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetNameMaxLength")) -- 街区名字最大长度
	self._streetNameMinLength = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetNameMinLength")) -- 街区名字最小长度
	self._streetDescMaxLength = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetDescMaxLength")) -- 街区描述最大长度
end

--- view初始化时会执行，在buildUI之后
function ShareStreetCreateView:bindEvents()
    ShareStreetCreateView.super.bindEvents(self)

    self._iptName:AddOnValueChanged(self._onNameChange, self)
    self._iptDes:AddOnValueChanged(self._onDescChange, self)

	self._btnOpen:AddClickListener(self._onBtnOpenClick, self)
    self._btnOk:AddClickListener(self._onBtnOkClick, self)

	self._ctShowStreetMap:AddClickListener(self._onShowStreetMapClick, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetCreateView:onEnter()
    ShareStreetCreateView.super.onEnter(self)

	self._goOpen:SetActive(true)
	self._goLetter:SetActive(false)
    self._goEft1:SetActive(true)

	-- self._ani:get_Item("ui_createstreetview_anim_01").time = 0
	-- self._ani:Rewind()
	self._ani.clip:SampleAnimation(self._ani.gameObject, 0)

	self._isTestMode = self:getFirstParam()
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetCreateView:onEnterFinished()
    ShareStreetCreateView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetCreateView:onExit()
    ShareStreetCreateView.super.onExit(self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetCreateView:onExitFinished()
    ShareStreetCreateView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetCreateView:unbindEvents()
    ShareStreetCreateView.super.unbindEvents(self)

    self._iptName:RemoveOnValueChanged()
    self._iptDes:RemoveOnValueChanged()

    self._btnOk:RemoveClickListener()
	self._btnOpen:RemoveClickListener()
end

--- view销毁时会执行
function ShareStreetCreateView:destroyUI()
    ShareStreetCreateView.super.destroyUI(self)

	if self.model then
        self.model:dispose()
        self.model = nil
    end
end

function ShareStreetCreateView:_onBtnOpenClick()
    self._goEft1:SetActive(false)
	self._goEft2:SetActive(true)
	TaskUtil.BlockClick(true, "CreateShareStreetView")
	settimer(1, self._onClickEftEnd, self, false)
end

function ShareStreetCreateView:_onClickEftEnd()
	self._goEft2:SetActive(false)
	--TODO:接入动画
	-- self._goOpen:SetActive(false)
	self._goLetter:SetActive(true)
	self._goDes:SetActive(true)
	self._goSucc:SetActive(false)
	self._iptName:SetText(lang("我的街区"))
	self._iptDes:SetText(lang("来共建街区吧"))
	self._txtCount.text = string.format("(%d/%d)", math.ceil(GameUtils.getStrLen(self._iptDes:GetText()) / 2), self._streetDescMaxLength / 2)

	self._ani:Rewind()
	self._ani:Play()
	local duration = self._ani.clip.length
	settimer(duration, self._onAniEnd, self, false)
end

function ShareStreetCreateView:_onAniEnd()
	self._goOpen:SetActive(false)
	self._ani:Stop()
	TaskUtil.BlockClick(false, "CreateShareStreetView")
end

function ShareStreetCreateView:_onNameChange(inputStr)
    print("ShareStreetCreateView:onNameChange", inputStr)
    inputStr = GameUtils.getBriefName(inputStr, self._streetNameMaxLength, '')
    self._iptName:SetText(inputStr)
end

function ShareStreetCreateView:_onDescChange(inputStr)
    print("ShareStreetCreateView:_onDescChange", inputStr)
    inputStr = GameUtils.getBriefName(inputStr, self._streetDescMaxLength, '')
    self._iptDes:SetText(inputStr)
    self._txtCount.text = string.format("(%d/%d)", math.ceil(GameUtils.getStrLen(inputStr) / 2), self._streetDescMaxLength / 2)
end

function ShareStreetCreateView:_onBtnOkClick()
	local strName = self._iptName:GetText()
	local strDes = self._iptDes:GetText()
	if not ShareStreetUtil.isStreetNameLegal(strName) then
		return
	end
	if not ShareStreetUtil.isStreetDescLegal(strDes) then
		return
	end
	if self._isTestMode then
		self:_onCreateSucc() --测效果用
	else
		ShareStreetController.instance:createShareStreet(strName, strDes, handler(self._onCreateSucc, self))
	end
end

function ShareStreetCreateView:_onCreateSucc()
	self._ani:Play("ui_createstreetview_anim_02") --盖章动画
	self._goDes:SetActive(false)
	self._goSucc:SetActive(true)
	self._txtSuccName.text = self._iptName:GetText()
	self._txtSuccDes.text = self._iptDes:GetText()

	self._goOwner:SetActive(true)
	if self.model == nil then
        self.model = ModelPhotoBase.New(self._goOwner, 1, ModelPhotoBase.Type_MainPlayer)
		self.model:setCameraPosition(-0.1, 3.23, -5.28)
		self.model._photoBase.producer.rtCamera.orthographicSize = 2
    end
end

function ShareStreetCreateView:_onShowStreetMapClick()
	self:close()
	ShareStreetFacade.instance:showMapView()
end

return ShareStreetCreateView
