module("logic.scene.unit.playeraction.JoinRideMountHandler",package.seeall)
local JoinRideMountHandler = class("JoinRideMountHandler", PlayerActionHandlerBase)
JoinRideMountHandler.UnitNamePrefix = "Mount_"

function JoinRideMountHandler:onStart()
	print("JoinRideMountHandler:onStart")
	self._isLeaveScene = false
    self.scene = SceneManager.instance:getCurScene()
	self.unitFactory = self.scene.unitFactory
	self.mountId = tonumber(self.info.params[1])
    self.ownerId = self.info.params[2]
	self.joinerId = self.info.params[3]
	self.dir = 1
	self.unit.notSort = true
	self.unit:setFace(true)
	self.unit:setAlwaysFront(true)
	local cfg = PoseConfig.getInteractiveProp(self.mountId)
	self.lowerAniName = cfg.idle[1]
	self.upperAniName = cfg.idle[2]
	self.walkAniName = cfg.idle[3] or self.upperAniName
	print("self.walkAniName", self.walkAniName)
	local mountSpeed = cfg.properties.mountSpeed
	self.unit:specificWalkAni(self.walkAniName)
	self.unit:specificIdleAni(self.upperAniName)
	if self.ownerId == self.unit.id then
		self:createMount()
	else
		self:_findMount()
	end

	if self.unit.seaAniComp then
		self.unit.seaAniComp:setMountState(self.upperAniName, mountSpeed)
	else
		self.unit:setDirection(self.unit.dir)
		local add = mountSpeed / 10000
		local speed = self.unit:getSpeed()
		speed = speed * (add + 1)
		self.unit:setSpeed(speed)
	end
	self.unit.walkSoundComp:setSpecialMove(true)
	self.unit.skinView:hideHandItem()
	GlobalDispatcher:addListener(GlobalNotify.LeaveScene, self.onLeaveScene, self)
	GlobalDispatcher:addListener(GlobalNotify.ReloadPlayer, self._onReconect, self)
end

function JoinRideMountHandler:onUpdate()
	self.joinerId = self.info.params[3]
end

function JoinRideMountHandler:onLeaveScene()
	self._isLeaveScene = true
end

function JoinRideMountHandler:_onReconect()
	self.unit:specificWalkAni(self.walkAniName)
	self.unit:specificIdleAni(self.upperAniName)
	local mount = self.unitFactory:getUnit(SceneUnitType.SeaMountUnit, JoinRideMountHandler.UnitNamePrefix .. self.ownerId)
	if mount then
		mount:onReconect()
	end
end

function JoinRideMountHandler:onStop(isLeaveScene)
	GlobalDispatcher:removeListener(GlobalNotify.LeaveScene, self.onLeaveScene, self)
	GlobalDispatcher:removeListener(GlobalNotify.ReloadPlayer, self._onReconect, self)
	local cfg = PoseConfig.getInteractiveProp(self.mountId)
	self.unit.skinView:showHandItem()
	if self.ownerId == self.unit.id then
		self.unitFactory:removeUnit(SceneUnitType.SeaMountUnit, JoinRideMountHandler.UnitNamePrefix .. self.unit.id)
		self.unit:removeListener(UnitNotify.DirectionChange, self.onDirChange, self)
		self.unit:setMount(nil)
	else
		--重新get一下坐骑对象，是因为可能坐骑发起者取消了坐骑，已经把坐骑对象移除了
		local mount = self.unitFactory:getUnit(SceneUnitType.SeaMountUnit, JoinRideMountHandler.UnitNamePrefix .. self.ownerId)
		if mount then
			mount:setJoiner(nil)
		end
		
		SceneTimer:removeTimer(self._findMount, self)
		if self.ownerUnit then
			self.ownerUnit:removeListener(UnitNotify.DirectionChange, self.onOwnerDirChange, self)
		end
	end
	if self.ownerId == self.unit.id then
		x, y, z = self.unit:getPos()
		self.unit:setPos(x, y, z)
	elseif self.ownerUnit then
		x, y, z = self.ownerUnit:getPos()
		self.ownerUnit:setPos(x, y, z)
	end

	if self.unit.isUser then
		-- SeaController.instance:localNotify(SeaNotify.onMountHandlerChange)
		-- if not self._isLeaveScene then
		-- 	local x, y
		-- 	if self.ownerId == self.unit.id then
		-- 		x, y = self.unit:getPos()
		-- 		SceneController.instance:teleport(x, y, nil, true)
		-- 	elseif self.ownerUnit then
		-- 		x, y = self.ownerUnit:getPos()
		-- 		SceneController.instance:teleport(x, y, nil, true)
		-- 	end
		-- end

		-- if self.scene then
		-- 	if not SceneManager.instance:getCurScene().actionMgr:hasAction(SceneActionType.Track) then
		-- 		self.scene:setOverrideClick(nil)
		-- 	end
		-- end
	end

	self.unit:specificWalkAni(nil)
	self.unit:specificIdleAni(nil)
	self.unit:setAlwaysFront(false)
	if self.unit.seaAniComp then
		self.unit.seaAniComp:stopLowerBodyAni()
		self.unit.seaAniComp:setMountState()
	else
		self.unit:setSpeed(SceneConfig.GetPlayerSpeed())
		self:stopAni()
	end
	self.unit.notSort = false
	self.unit.walkSoundComp:setSpecialMove(false)
	self.mount = nil
	self.unitFactory = nil
	self.scene = nil

end


function JoinRideMountHandler:createMount()
	local unitId = JoinRideMountHandler.UnitNamePrefix .. self.unit.id
	self.mount = self.unitFactory:addUnit(SceneUnitType.SeaMountUnit, {id = unitId, ownerUnit=self.unit, mountId = self.mountId, enableTime = true})
	self.mount.dir = self.dir
	-- self.mount:setMountId(self.mountId)
	self.mount:setView(self.mountId, self.dir, self._onLoadUnit, self)

	self.unit:addListener(UnitNotify.DirectionChange, self.onDirChange, self)
	self.unit:setMount(self.mount)
end

function JoinRideMountHandler:_onLoadUnit()
	if self.mount == nil then return end
	
	if self.unit.id == UserInfo.userId and self.ownerId ~= self.unit.id then
		--如果不是观光车就覆盖
		if not SceneManager.instance:getCurScene().actionMgr:hasAction(SceneActionType.Track) then
			self.scene:setOverrideClick(self.onClickEmpty, self)
		end
	end
	self.mount:addClickPosListener(self._onClickJoin, self)
	self:onDirChange()
	self:playAni()
end

function JoinRideMountHandler:onDirChange()
	local dir = self.unit:getDirection()
	self.mount:setDir(dir)
end

function JoinRideMountHandler:onOwnerDirChange()
	local dir = self.ownerUnit:getDirection()
	self.unit:setDirection(dir)
end

function JoinRideMountHandler:_onClickJoin()
	if self.unit.isUser then
		return
	end
	SceneController.instance:joinRideMount(self.unit.id)
end

function JoinRideMountHandler:_findMount()
    if not self.scene then return end   --防一手同帧执行，定时器还在

    local unitId = JoinRideMountHandler.UnitNamePrefix .. self.ownerId
	self.mount = self.unitFactory:getUnit(SceneUnitType.SeaMountUnit, unitId)
	self.ownerUnit = self.scene:getPlayer(self.ownerId)
    if not self.mount or not self.ownerUnit then
        SceneTimer:setTimer(0.1, self._findMount, self, false)
        return
    end
    self.mount:setJoiner(self.unit)
	self.ownerUnit:addListener(UnitNotify.DirectionChange, self.onOwnerDirChange, self)
	self:playAni()
	self:onOwnerDirChange()
	self.mount:setVisible(self.unitFactory:_isShow(self.mount))
	if self.unit.id == UserInfo.userId and self.ownerId ~= self.unit.id then
		self.scene:setOverrideClick(self.onClickEmpty, self)
	end
end

function JoinRideMountHandler:onClickEmpty()
	--local msg = self.unit.id == self.ownerId and lang("是否收回坐骑？") or lang("是否离开坐骑？")
	--DialogHelper.showConfirmDlg(msg, function(isOk)
	--	if isOk then
	--		SeaController.instance:localNotify(SeaNotify.onMountHandlerChange, true)
	--	end
	--end)
	-- print("onClickEmpty")
	-- if self.ownerId ~= self.unit.id then
		SeaController.instance:localNotify(SeaNotify.onMountHandlerChange, true)
	-- end
	end

function JoinRideMountHandler:onStartMove()
	if self.mount then
		self.mount:onStartMove()
	end
	if self.unit.seaAniComp and self.lowerAniName then
		self.unit.skinView:setAnimation(self.unit:getWalkAniName(), true, true)
		self.unit.seaAniComp:setLowerBodyAni(self.lowerAniName)
	else
		self:playAni()
	end
end

function JoinRideMountHandler:onStopMove()
	if self.mount then
		self.mount:onStopMove()
	end
	if self.unit.seaAniComp and self.lowerAniName then
		self.unit.skinView:setAnimation(self.unit:getIdleAniName(), true, true)
		self.unit.seaAniComp:setLowerBodyAni(self.lowerAniName)
	else
		self:playAni()
	end
end

function JoinRideMountHandler:onShortActionStart()
	self.unit.idleComp:setEnable(false)
end

function JoinRideMountHandler:onShortActionOver()
	local unit = self.ownerId == self.unit.id and self.unit or self.ownerUnit
	if unit:isMoving() then
		self:onStartMove()
	else
		self:onStopMove()
	end
	if self.ownerId ~= self.unit.id then
		self:onOwnerDirChange()
	end
end

function JoinRideMountHandler:playAni()
	local aniName
	if self.unit.isMoving and self.unit:isMoving() then
		aniName = self.unit:getWalkAniName()
	else
		aniName = self.unit:getIdleAniName()
	end
	local skinView = self.unit.skinView
	skinView.isLoop = true
		local aniHelper = skinView:getAnimHelper()
		if aniHelper then
		aniHelper:setAnimation(aniName, true, 1)
		aniHelper:setAnimation(self.lowerAniName, true, 3)
		end
	end

function JoinRideMountHandler:stopAni()
	local skinView = self.unit.skinView
	local aniHelper = skinView:getAnimHelper()
	if aniHelper then
		aniHelper:stopAnimation(1)
		aniHelper:stopAnimation(3)
	end
end

return JoinRideMountHandler