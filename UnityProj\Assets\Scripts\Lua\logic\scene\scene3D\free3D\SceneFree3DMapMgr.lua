module("logic.scene.scene3D.free3D.SceneFree3DMapMgr", package.seeall)

local SceneFree3DMapMgr = class("SceneFree3DMapMgr", Scene3DMapMgr)

function SceneFree3DMapMgr:getWalkDirectionPath_Vector3(unit, x, y, distance)
    local v3 = self._scene.camera.camTrs:TransformDirection(gvec3(x, 0, y))
    return SceneFree3DMapMgr.super.getWalkDirectionPath_Vector3(self, unit, v3.x, v3.z, distance)
end

function SceneFree3DMapMgr:getWalkDirectionPath(unit, x, y, distance)
    local v3 = self._scene.camera.camTrs:TransformDirection(gvec3(x, 0, y))
    return SceneFree3DMapMgr.super.getWalkDirectionPath(self, unit, v3.x, v3.z, distance)
end


return SceneFree3DMapMgr
