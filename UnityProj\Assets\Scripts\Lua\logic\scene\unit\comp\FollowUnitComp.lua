module("logic.scene.unit.comp.FollowUnitComp",package.seeall)
local FollowUnitComp = class("FollowUnitComp", UnitComponentBase)

FollowUnitComp.FollowUpdateInterval = 0.3
local RecordDistance = 0.1


local function _createPoint()
	return {}
end

function FollowUnitComp:onInit()
	self._followTarget = false
	self._followDistance = 0
	self.mapMgr = SceneManager.instance:getCurScene():getMapMgr()
	self.updateTime = 0
	self._autoJump = true
end

function FollowUnitComp:reset()
	self:setFollowTarget(nil)
end

--寻路式跟随
function FollowUnitComp:setFollowTarget(target, distance,noDirectSetPos)
	self._followTarget = target
	self._followType = 1
	self._followDistance = distance
	self._updatTime = 0
	if target and (not noDirectSetPos) then
		local targetPosX, targetPosY= self._followTarget:getPos()
		self:setFollowRolePosDirectly(targetPosX,targetPosY)
	end
end
--依附式跟随
function FollowUnitComp:setAttachTarget(target, offsetX, offsetY)
	self._followTarget = target
	if target then
		self._followType = 3
		self._offsetX = offsetX
		self._offsetY = offsetY
		self:_updateAttach()
	end
end
--排队式跟随
function FollowUnitComp:setQueueTarget(target, distance)
	self._followTarget = target
	if target then
		self._followType = 2
		self._followDistance = 0
		self._stopCount = math.ceil(distance / RecordDistance)
		if not self.pool then
			self.pool = ObjectPool.New(0, _createPoint)
		end
		self.wayPoints = {}
		local targetPosX, targetPosY= self._followTarget:getPos()
		self:setFollowRolePosDirectly(targetPosX,targetPosY)
	end
end

--直接设置随从的位置
function FollowUnitComp:setFollowRolePosDirectly(x,z)
	if not self._followTarget then
		return
	end
	local dx
	if self._followTarget:getDirection() == 1 or self._followTarget:getDirection() == 3 then
		dx = -self._followDistance
	else
		dx = self._followDistance
	end
	if self.mapMgr:isWalkable(x + dx,z + 0.2) then
		z = z + 0.2
		x = x + dx
	else
	end
	printInfo("set follow pos")
	self._unit:setDirection(self._followTarget:getDirection())
	self._unit:setPos(x,z)
end

function FollowUnitComp:onUpdate()
	if not self._followTarget then
		return
	end
	if self._followType == 1 then
		self:_updateNormal()
	elseif self._followType == 2 then
		self:_updateQueue()
	elseif self._followType == 3 then
		self:_updateAttach()
	end
end

function FollowUnitComp:_updateNormal()
	self._updatTime = self._updatTime + Time.deltaTime
	if self._updatTime >= FollowUnitComp.FollowUpdateInterval then
		self._updatTime = self._updatTime - FollowUnitComp.FollowUpdateInterval
		local px, py = self._unit:getPos()
		local tx, ty = self._followTarget:getPos()
		local dis = Mathf.Sqrt((tx - px) ^ 2 + (ty - py) ^ 2)
		local np
		local maxDis = self._followDistance + self._unit:getSpeed() * FollowUnitComp.FollowUpdateInterval
		if self._autoJump and dis > maxDis * 2 then
			self:setFollowRolePosDirectly(tx, ty)
		elseif dis > maxDis then
			-- local speed = self._followTarget:getSpeed()
			local t = (dis - self._followDistance) / dis
			local moveX = Mathf.Lerp(px, tx, t)
			local moveY = Mathf.Lerp(py, ty, t)
			local find = self.mapMgr:isDirectWalkable(self._unit, moveX, moveY)
			if find then
				-- printInfo("move to", px, py, tx, ty, t, moveX,moveY)
				self._unit:moveTo(moveX, moveY)
			else
				-- printInfo("walk to", tx,ty)
				self._unit:walkTo(tx, ty, nil, self._followDistance, self._autoJump, true)
			end
		end
	end
end

function FollowUnitComp:_updateQueue()
	--记录新的路点
	local lastPos = self.wayPoints[#self.wayPoints]
	local tx, ty = self._followTarget:getPos()
	local needInsert = true
	local dis = 0
	if lastPos then
		dis = math.sqrt((tx-lastPos.x) ^ 2 + (ty-lastPos.y) ^ 2)
		needInsert =  dis > RecordDistance
	end
	if dis > RecordDistance * self._stopCount * 2 then
		self:setFollowRolePosDirectly(tx,ty)
		local removeCount = #self.wayPoints-1
		for i=1,removeCount do
			self.pool:returnObject(self.wayPoints[1])
			table.remove(self.wayPoints, 1)
		end
		local pos = self.wayPoints[1]
		pos.x, pos.y = tx, ty
	else
		if needInsert then
			local pos = self.pool:fetchObject()
			pos.x, pos.y = tx, ty
			table.insert(self.wayPoints, pos)
		end
		if #self.wayPoints > self._stopCount  + 1 then
			local pos = table.remove(self.wayPoints, 1)
			local mover = self._unit.mover
			mover:setWayPoint(pos.x, pos.y)
			self.pool:returnObject(pos)
			--多移动一个节点
			pos = self.wayPoints[1]
			mover:addWayPoint(pos.x, pos.y)
		end
	end
	-- printInfo(#self.wayPoints)
end

function FollowUnitComp:_updateAttach()
	local targetPosX, targetPosY, targetPosZ = self._followTarget:getPos()
	
	self._unit:setDirection(self._followTarget:getDirection())
	if HouseModel.instance:isInHouseOrIsland() or SceneManager.instance:isInSea() then
		--小岛排序比较特殊，z轴直接跟主玩家来，需要提前将self._unit.isSort 置为false
		local offset = SceneManager.instance:isInSea() and 0.05 or 0.001
		local vec = GameUtils.getLocalPos(self._followTarget.go)
		self._unit.mover:setPosDirectly(targetPosX + self._offsetX, targetPosY + self._offsetY,vec.z + offset)
		self._unit:setNavEnable(false)
	else
		if SceneManager.instance:isInFree3DScene() then
			local p = self._followTarget.go.transform:TransformPoint(self._offsetX, 0, self._offsetY)
			local find = self.mapMgr:isDirectWalkable(self._followTarget,p.x, p.z)
			if find then 
				self._unit.mover:setPosDirectly(p.x, p.z)
				self._unit:setNavEnable(true)
			else
				self._unit.mover:setPosDirectly(p.x, p.z)
				self._unit:setNavEnable(false)
			end
		else 
			local z = GameUtils.GetPosYOnGround(targetPosX, targetPosY)
			local find = self.mapMgr:isDirectWalkable(self._followTarget, targetPosX + self._offsetX, targetPosY + self._offsetY)
			if find then 
				self._unit.mover:setPosDirectly(targetPosX + self._offsetX, targetPosY + self._offsetY)
				self._unit:setNavEnable(true)
			else
				self._unit.mover:setPosDirectly(targetPosX + self._offsetX, targetPosY + self._offsetY,z)
				self._unit:setNavEnable(false)
			end
		end
	end
end

function FollowUnitComp:setAutoJump(value)
	self._autoJump = value
end


return FollowUnitComp