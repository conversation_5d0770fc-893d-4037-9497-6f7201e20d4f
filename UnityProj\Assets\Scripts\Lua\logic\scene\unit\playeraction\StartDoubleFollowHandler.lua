module("logic.scene.unit.playeraction.StartDoubleFollowHandler",package.seeall)

local StartDoubleFollowHandler = class("StartDoubleFollowHandler",PlayerActionHandlerBase)
--params
--1.itemId
--2.isDouble 是否是双人模式
--3.""
--4.playerId --加入进来玩家的id

local function getResPath(name)
    return "prefabs/umbrella/" .. name .. ".prefab"
end

function StartDoubleFollowHandler:onStart()
	self.poseId = tonumber(self.info.params[1])	
	self.cfg = PoseConfig.getDoublePose(self.poseId)
	self.unit:finishMove()
	self.unit.skinView:loadAnimation({"anif_shuangrendongzuo","anib_shuangrendongzuo"}, self.onLoadAnimation, self)
	self.unit:setAlwaysFront(true)
	self.unit:setFace(true)
    self.oldSpeed = self.unit:getSpeed()
	self.unit:setSpeed(2.5)
end


function StartDoubleFollowHandler:onLoadAnimation()
	local targetId = self.info.params[2]
	-- local dir = tonumber(self.info.params[3])
	-- self.unit:setDirection(dir)

	self.unit.skinView:setAnimation(self.cfg.startAniName, true)
	self:setupShareItemAndTips()
	if self.cfg.startClothes > 0 then
		self.unit.skinView:addTempClothes(self.cfg.startClothes)
	end
	if self.cfg.mainHideHandItem >= 0 then
		self.unit.skinView:hideHandItem("pose", self.cfg.mainHideHandItem)
	end
	if not string.nilorempty(self.cfg.startEffect) then
		local slot = self.unit.effectLoader:getSlot(EffectLoader.Pose)
		slot:load(GameUrl.getPoseURL(self.cfg.startEffect))
	end	
end

function StartDoubleFollowHandler:setupShareItemAndTips()
	local targetId = self.info.params[2]
	if  self.unit.isUser or (self.info.params[3] == "0" and targetId == UserInfo.userId) then
		if self.cfg.itemId  > 0 then
			self.unit.shareItem:setShareIconForItem(self.cfg.itemId, handler(self.onClick, self), nil ,true)	
		else
			self.unit.shareItem:setShareIconForAtlas(AtlasUrl.DoublePose, self.poseId, handler(self.onClick, self), nil ,true)	
		end	
		if targetId == UserInfo.userId then
			self:showTips(targetId, self.unit:getNickname())
		end
	else
		self.unit.shareItem:clear()		
	end
end

function StartDoubleFollowHandler:onUpdate()
	self:setupShareItemAndTips()
	self.unit.skinView:setAnimation(self.cfg.mainAniName .. "_idle", true)
end

function StartDoubleFollowHandler:onStop()
	self.unit.shareItem:clear()	
	self.unit:setSpeed(self.oldSpeed)
	self.unit.skinView:removeLoadAnimationCallback(self.onLoadAnimation, self)
	if self.cfg.startClothes > 0 then
		self.unit.skinView:removeTempClothes(self.cfg.startClothes)
	end	
	if self.cfg.mainHideHandItem >= 0 then
		self.unit.skinView:showHandItem("pose", self.cfg.mainHideHandItem)
	end	
	if not string.nilorempty(self.cfg.startEffect) then
		local slot = self.unit.effectLoader:getSlot(EffectLoader.Pose)
		slot:clear()
	end	
end





function StartDoubleFollowHandler:onShortActionStart()
end

function StartDoubleFollowHandler:onStartMove()
    self.unit.skinView:setAnimation(self.cfg.mainAniName .. "_walk", true)
	if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_move")
	end
end

function StartDoubleFollowHandler:onStopMove()
	local aniName = self.info.params[3] == "0" and self.cfg.startAniName or self.cfg.mainAniName .. "_idle"
    self.unit.skinView:setAnimation(aniName, true)
	if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_idle")
	end	
end

local function _onShowTip(go,params)
	if not isTipsInit then
		isTipsInit = true
		tipsObj._iconGo = goutil.findChild(go, "icon")
		tipsObj._nameTxt = goutil.findChildTextComponent(go, "txtName")
		tipsObj._txtTips = goutil.findChildTextComponent(go, "txtTips")
		goutil.findChild(go, "btnDetail"):SetActive(false)
	end
	tipsObj._nameTxt.text = params.userName
	local cfg = PoseConfig.getDoublePose(params.poseId)	
	tipsObj._txtTips.text = lang("双人动作邀请描述", cfg.name)
	HeadPortraitHelper.instance:setHeadPortraitWithUserId(tipsObj._iconGo, params.userId)
end

function StartDoubleFollowHandler:showTips(userId, userName)
	if tipsObj == nil then
		tipsObj = InstantTipsMgr.instance:buildPushObj(CommonResPath.CarnivalPartyTip, _onShowTip, InstantTipsType.CarnivalPush)
		
	end
	tipsObj:showTips({userName=userName, userId = userId, totalTime=5, poseId=self.poseId},false,0.1,
		function()
			tipsObj:close()
		end,
		function()
			self:onClick(true)
		end
		)	

end

function StartDoubleFollowHandler:onClick(isClick)
	if isClick then
		if tipsObj ~= nil then
			tipsObj:close()
		end
		SceneController.instance:acceptDoubleFollow(self.poseId, self.unit)
	else
		SceneController.instance:stopAction()
	end
end



return StartDoubleFollowHandler