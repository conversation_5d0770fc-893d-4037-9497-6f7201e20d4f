module("logic.extensions.workshop.View.WorkshopPanelForZuoWu",package.seeall)

local WorkshopPanelForZuoWu = class("WorkshopPanelForZuoWu",ViewComponent)

function WorkshopPanelForZuoWu:ctor()
	WorkshopPanelForZuoWu.super.ctor(self)
--s
	self._isSelect = false
end

function WorkshopPanelForZuoWu:buildUI()
	self._panelGo = self:getGo("rightViewForZuoWu")
	self._anotherPanelGo = self:getGo("rightView")

	self._nameTxt = self:getText("rightViewForZuoWu/topTip/middleTip/txtName")
	self._collectBtn = self:getBtn("rightViewForZuoWu/topTip/middleTip/imgCollection/imgSelect")
	self._unCollectBtn = self:getBtn("rightViewForZuoWu/topTip/middleTip/imgCollection/imgUnSelect")

	self._iconGo = self:getGo("rightViewForZuoWu/middleView/targetItem/icon")
	self._iconBtn = self:getBtn("rightViewForZuoWu/middleView/targetItem/icon")
	self._detailBtn = self:getBtn("rightViewForZuoWu/middleView/targetItem/detailBtn")

	self._grayGo = self:getGo("rightViewForZuoWu/middleView/targetItem/count/type2")
	self._currentCountTxt = self:getText("rightViewForZuoWu/middleView/targetItem/count/countTxt")

	self._countTipGo = self:getGo("rightViewForZuoWu/middleView/countTip")
	self._countTipTxt = self:getText("rightViewForZuoWu/middleView/countTip/countTxt")

	self._outputTxt = self:getText("rightViewForZuoWu/middleView/outputTipGo/outputTipTxt")
	
	self._additionGo = self:getGo("rightViewForZuoWu/topTip/additionGo")
	self._expTxt = self:getText("rightViewForZuoWu/topTip/additionGo/expAddition/countTxt")
	self._expGo = self:getGo("rightViewForZuoWu/topTip/additionGo/expAddition")

	self._makeBtn = self:getBtn("rightViewForZuoWu/certain")
	self._priceTxt = self:getText("rightViewForZuoWu/certain/iconAndCount/countTxt")
	self._lockBtn = self:getBtn("rightViewForZuoWu/lockGo")

	self._certainCurrencyGo = self:getGo("rightViewForZuoWu/certain/iconAndCount/icon")
	self._certainTxt = self:getText("rightViewForZuoWu/certain/txtOpen")

	self._foodInfoGo = self:getGo("rightViewForZuoWu/topTip/additionGo/foodInfo")
	self._foodInfoTxt =  self:getText("rightViewForZuoWu/topTip/additionGo/foodInfo/eatCount/txt")
	self._txtBonusCount =  self:getText("rightViewForZuoWu/topTip/additionGo/foodInfo/bonus/txt")
	self._expBuffIconGo = self:getGo("rightViewForZuoWu/topTip/additionGo/expAddition/expBuffIcon")
	self._expIconGo = self:getGo("rightViewForZuoWu/topTip/additionGo/expAddition/Text1")
	self._expBuffGo = self:getGo("rightViewForZuoWu/topTip/additionGo/expAddition/expBuffGo")
	self._expBuffIcon = self:getGo("rightViewForZuoWu/topTip/additionGo/expAddition/expBuffGo/buffIcon")
	self._expBuffTxt = self:getText("rightViewForZuoWu/topTip/additionGo/expAddition/expBuffGo/buffValue")
	self._plantInfoGo = self:getGo("rightViewForZuoWu/topTip/additionGo/plantInfo")
	self._lockPanel = self:getGo("notUnlock_panel")
	self._tipRootGo = self:getGo("notUnlock_panel/Image3")
	self._unLockTxt = self:getText("notUnlock_panel/opentips/pleyerTipTxt")
end

function WorkshopPanelForZuoWu:bindEvents()
	self._collectBtn:AddClickListener(self._onClickCollection,self)
	self._unCollectBtn:AddClickListener(self._onClickUnCollection,self)
	self._makeBtn:AddClickListener(self._onClickMake,self)
	self._iconBtn:AddClickListener(self._onClickDetail,self)
	self._detailBtn:AddClickListener(self._onClickDetail,self)
end

function WorkshopPanelForZuoWu:onEnter()
	self._isEnoughToMake = false
	self:registerLocalNotify(WorkshopNotify.ChangeSelectItemForZuoWu, self._onChangeSelectItem, self)
	self:registerLocalNotify(WorkshopNotify.OnTargetIsLock, self._onTargetIsLock, self)
	self:registerLocalNotify(WorkshopNotify.ChangeSelectItem,self._onSelectAnother,self)
end

function WorkshopPanelForZuoWu:onExit()
	self:modificationBackParam()
	self:unregisterLocalNotify(WorkshopNotify.ChangeSelectItemForZuoWu, self._onChangeSelectItem, self)
	self:unregisterLocalNotify(WorkshopNotify.OnTargetIsLock, self._onTargetIsLock, self)
	self:unregisterLocalNotify(WorkshopNotify.ChangeSelectItem,self._onSelectAnother,self)
end

function WorkshopPanelForZuoWu:_onTargetIsLock(itemMo)
	if itemMo:isZuoWu() then
		self._itemMo = itemMo
		goutil.setActive(self._panelGo, true)
		goutil.setActive(self._anotherPanelGo, false)
		self:_refreshView()
	end
end

function WorkshopPanelForZuoWu:unbindEvents()

end

function WorkshopPanelForZuoWu:destroyUI()

end

function WorkshopPanelForZuoWu:_onChangeSelectItem(itemMo)
	self._isSelect = true
	self._itemMo = itemMo
	local isCanMake,resultTxt = self._itemMo:isCanMake()

	goutil.setActive(self._panelGo, isCanMake)
	goutil.setActive(self._lockPanel, not isCanMake)
	goutil.setActive(self._anotherPanelGo, false)
	if(isCanMake) then
		self:_refreshView()
	else
		local jurenModelGo = goutil.findChild(self._lockPanel,"modelGo/juren")
		local npcModelGo = goutil.findChild(self._lockPanel,"modelGo/npcModel")
		goutil.setActive(jurenModelGo,true)
		goutil.setActive(npcModelGo,false)
		self._unLockTxt.text = resultTxt
	end
end

function WorkshopPanelForZuoWu:modificationBackParam()
	if(self._isSelect) then
		local openParam = self:getOpenParam()
		if(self._itemMo ~= nil) then
			openParam[1] = WorkshopSetting.OpenState.AppointItem
			openParam[2] = self._itemMo
			self._itemMo = nil
		end
	end
end


function WorkshopPanelForZuoWu:_onSelectAnother()
	self._isSelect = false
end

function WorkshopPanelForZuoWu:_onClickDetail()
	ViewMgr.instance:open("WorkshopGain",self._itemMo.id,true)
end

function WorkshopPanelForZuoWu:_refreshView()
	self:_setPlantDetail(self._itemMo.id, self._plantInfoGo)
	self:_initInfo()
	self:_initCurrency()
	self:_initBuff()

	self:_contentFiliterImmiediate()
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self._additionGo.transform)
end

function WorkshopPanelForZuoWu:_setPlantDetail(id, go)
	local plantDef = HarvestConfig.getDefineBySeedId(id)
	if plantDef == nil or plantDef.subType == 0 then
		goutil.setActive(go, false)
		return
	end
	goutil.setActive(go, true)
	local plant = HarvestConfig.getDefineBySeedId(id)
	local matureTime = BuffService.instance:calculateResultValue(BuffType.AllType.ProduceTimeDecrease, plant.matureTime, plant.subType, plant.id)
	local hasBuff = matureTime ~= plant.matureTime
    local day, hour, min, sec = TimeUtil.getDay_Hour_Min_Sec(matureTime)
	local showDay = day > 0
	local showHour = hour > 0 or showDay
	local showMinute = min > 0 or showHour
	local showSecond = not showHour
	local formatStr = (showDay and lang("%d天") or '')..(showHour and lang("%h时") or '')..(showMinute and lang("%m分") or '')..(showSecond and lang("%s秒") or '')

	local matureTimeGo = goutil.findChild(go, "matureTime")
    goutil.setActive(goutil.findChild(matureTimeGo, "imgNormal"), not hasBuff)
    goutil.setActive(goutil.findChild(matureTimeGo, "imgBuff"), hasBuff)
    if hasBuff then
		IconLoader.setSpriteToImg(goutil.findChild(matureTimeGo, "imgBuff"), GameUrl.getBuffUrl(6))
    end

    local txtTime = goutil.findChildTextComponent(matureTimeGo, "txt")

    self._matureTime = ""
    if(day~=0) then
		self._matureTime = self._matureTime .. day .. lang("天")
	end
	if(hour~=0) then
		self._matureTime = self._matureTime .. hour .. lang("小时")
	end
	if(min~=0) then
		self._matureTime = self._matureTime .. min .. lang("分钟")
	end
	if(sec~=0) then
		self._matureTime = self._matureTime .. sec .. lang("秒")
	end
    txtTime.text = self._matureTime
end

function WorkshopPanelForZuoWu:_initBuff()
	local define = HarvestConfig.getDefineBySeedId(self._itemMo.id)
	local expIncreaseBuff = BuffService.instance:calculateResultValue(
		BuffType.AllType.ProduceExpIncrease, define.exp,
		define.subType, define.id)
	local hasExpBuff = expIncreaseBuff ~= define.exp 
	local expIncreaseBuffValue = expIncreaseBuff - define.exp
	goutil.setActive(self._expBuffIconGo, hasExpBuff)
	goutil.setActive(self._expBuffGo, hasExpBuff)
	goutil.setActive(self._expIconGo, not hasExpBuff)
	if(hasExpBuff) then
		local buffUrl = GameUrl.getBuffUrl(BuffType.AllType.ProduceExpIncrease)
		IconLoader.setSpriteToImg(self._expBuffIcon, buffUrl)
		IconLoader.setSpriteToImg(self._expBuffIconGo, buffUrl)
		self._expBuffTxt.text = (expIncreaseBuffValue >= 0) and ("+" .. expIncreaseBuffValue) or expIncreaseBuffValue
	end
end

function WorkshopPanelForZuoWu:setFoodDetail(id, go)
	local foodConfig = FoodConfig.getFoodById(id)
	if foodConfig then
		goutil.setActive(go,true)
		self._foodInfoTxt.text = foodConfig.count
		self._txtBonusCount.text = foodConfig.hostReward[1].count
	else
		goutil.setActive(go,false)
		self._foodInfoTxt.text = 0
		self._txtBonusCount.text = 0
	end
end



function WorkshopPanelForZuoWu:_initInfo()
	local itemMo = self._itemMo

	self._certainTxt.text = WorkshopConfig.getSubtypeName(itemMo.subtype)
	local itemCfg = ItemService.instance:getDefine(itemMo.id)

	ItemService.instance:setItemDetail(itemMo.id, self._additionGo)
	self:setFoodDetail(itemMo.id,self._foodInfoGo)

	if(itemCfg ~= nil) then
		self._nameTxt.text = itemCfg.name
		--IconLoader.setSpriteToImg(self._iconGo, viewUrl, nil, nil, nil, nil)
		--防止物品表没有的物品的情况
		ItemService.instance:setItemDetail(self._itemMo.id, self._additionGo)
	end
	IconLoader.setIconForItem(self._iconGo, self._itemMo.id)
	local define = HarvestConfig.getDefineBySeedId(itemMo.id)
	local currentCount = self._itemMo:getCurrentCount()
	local maxCount = define:getMyMaxCount()
	if(currentCount < maxCount) then
		self._isEnoughToMake = true
		goutil.setActive(self._grayGo, false)
	else
		self._isEnoughToMake = false
		goutil.setActive(self._grayGo, true)
	end
	self._currentCountTxt.text = currentCount .. "/" .. maxCount

	if(define.exp == 0) then
		goutil.setActive(self._expGo, false)
	else
		goutil.setActive(self._expGo, true)
		self._expTxt.text = "+" .. define.exp
	end

	local nextLevelCfg = define:getNextCountLimit()
	if(nextLevelCfg == nil) then
		goutil.setActive(self._countTipGo, false)
	else
		goutil.setActive(self._countTipGo, true)
		local nextCount = nextLevelCfg.countLimit
		local dCount = nextCount - define:getMyMaxCount()
		local color = "<color=#e38534>"
		local userLevel = UserInfo.getUserLevel()
		local tip = ""
		if nextLevelCfg.roleLevel > userLevel then
			tip = lang("角色达到Lv.") ..color ..nextLevelCfg.roleLevel .. "</color>"
		else
			tip = lang("角色达到Lv.") ..nextLevelCfg.roleLevel
		end
		local workshopMo = WorkshopModel.instance:getWorkshopById(101)
		if nextLevelCfg.workShopLevel > workshopMo.level then
			local tip2 = string.format(itemMo.countDesc .. "。",lang("工坊等级描述") .. color .. nextLevelCfg.workShopLevel .. "</color>",dCount)
			tip = tip .. lang(",工坊") ..tip2
		else
			local tip2 = string.format(itemMo.countDesc .. "。",lang("工坊等级描述") .. nextLevelCfg.workShopLevel,dCount)
			tip = tip .. lang(",工坊") ..tip2
		end

		--local tipStr = lang("角色达到Lv.") ..nextLevelCfg.roleLevel .. lang(",工坊") .. string.format(itemMo.countDesc .. "。",lang("工坊等级描述") .. nextLevelCfg.workShopLevel,dCount)
		self._countTipTxt.text = tip
	end

	if(define:getFixedGainMaxCount() == 0) then
		goutil.setActive(self._outputTxt.gameObject, false)
	else
		goutil.setActive(self._outputTxt.gameObject, true)
		local minCount = define:getFixedGainMinCount()
		local maxCount = define:getFixedGainMaxCount()
		-- local day,hour,min,sec = TimeUtil.getDay_Hour_Min_Sec(define.matureTime)
		-- local matureTimeTxt = ""
		-- if(day~=0) then
		-- 	matureTimeTxt = matureTimeTxt .. day .. lang("天")
		-- end
		-- if(hour~=0) then
		-- 	matureTimeTxt = matureTimeTxt .. hour .. lang("小时")
		-- end
		-- if(min~=0) then
		-- 	matureTimeTxt = matureTimeTxt .. min .. lang("分钟")
		-- end
		-- if(sec~=0) then
		-- 	matureTimeTxt = matureTimeTxt .. sec .. lang("秒")
		-- end

		-- self._outputTxt.text = lang("每<color=#f07100>{1}</color>可以产出<color=#f07100>{2}份~{3}份</color>{4}",matureTimeTxt,minCount,maxCount,define:getFixedGainItem().name)
		self._outputTxt.text = lang("作物工坊产出说明",self._matureTime,minCount,maxCount,define:getFixedGainItem().name)
	end

	if(itemMo.isCollect) then
		goutil.setActive(self._collectBtn.gameObject,true)
		goutil.setActive(self._unCollectBtn.gameObject,false)
	else
		goutil.setActive(self._collectBtn.gameObject,false)
		goutil.setActive(self._unCollectBtn.gameObject,true)
	end
end

function WorkshopPanelForZuoWu:_initCurrency()
	local tmp = 1
	local currencyArrs = self._itemMo:getCurrencyConsume()
	IconLoader.setIconForItem(self._certainCurrencyGo, currencyArrs[1])
	local needCount = currencyArrs[2] * tmp
	local currentCount = ItemService.instance:getItemNum(currencyArrs[1])
	local currencyEnough = true

	if(currentCount < needCount) then
		self._priceTxt.text = "<color=#FF0000>" .. needCount .."</color>" 
		currencyEnough = false
	else
		self._priceTxt.text = needCount
		currencyEnough = true
	end
	if(self._isEnoughToMake and currencyEnough) then
		goutil.setActive(self._lockBtn.gameObject, false)
	else
		goutil.setActive(self._lockBtn.gameObject, true)
		if(self._isEnoughToMake) then
			self._lockBtn:AddClickListener(
				function()
					RechargeFacade.isCurrencyEnough(currencyArrs[1],needCount,true,
						function()
							self:_onClickMake()
						end
					)
				end
				)
		else
			self._lockBtn:AddClickListener(
				function()
					local btnName = WorkshopConfig.getSubtypeName(self._itemMo.subtype)
					DialogHelper.showMsg(lang("已达到") .. btnName .. lang("上限"), nil)
				end
				)
		end
	end
end

function WorkshopPanelForZuoWu:_onClickMake()
	if self._isEnoughToMake == false then
		return
	end
	local state = RedPointController.instance:getNodeByName("limitChange_Workshop"):isExist()
	if state == true then
		local intParams = RedPointController.instance:getNodeByName("limitChange_Workshop"):getNumberParams()
		if intParams ~= nil then
			for i = 1, #intParams do
				if self._itemMo.id == intParams[i] then
					table.remove(intParams,i)
					WorkshopController.instance:_setChangeRedPoint()
					break
				end
			end
		end
		if #intParams == 0 then
			RedPointController.instance:setRedIsExist("limitChange_Workshop",false)
		end
	end
	--只有RoomFacade才有物品判定
	RoomFacade.instance:enterMyRoomAndPlace(self._itemMo.id, nil, false)
end

function WorkshopPanelForZuoWu:_onClickCollection()
	WorkshopModel.instance:changeCollectionState(self._itemMo.uniqueId)
	self:_setCollectionState(false)
end

function WorkshopPanelForZuoWu:_onClickUnCollection()
	WorkshopModel.instance:changeCollectionState(self._itemMo.uniqueId)
	self:_setCollectionState(true)
end

function WorkshopPanelForZuoWu:_setCollectionState(flag)
	self._itemMo.isCollect = flag
	goutil.setActive(self._collectBtn.gameObject,flag)
	goutil.setActive(self._unCollectBtn.gameObject,not flag)
	self:localNotify(WorkshopNotify.OnCollectionChange,self._itemMo)
end

function WorkshopPanelForZuoWu:_contentFiliterImmiediate()

	Framework.ContentSizeImmediate.Get(self:getGo("rightViewForZuoWu/topTip/middleTip/txtName")):GetPreferredSize()
	Framework.ContentSizeImmediate.Get(self:getGo("rightViewForZuoWu/topTip/middleTip")):GetPreferredSize()
	Framework.ContentSizeImmediate.Get(self:getGo("rightViewForZuoWu/middleView/countTip/countTxt")):GetPreferredSize()
	Framework.ContentSizeImmediate.Get(self:getGo("rightViewForZuoWu/middleView/countTip")):GetPreferredSize()
	Framework.ContentSizeImmediate.Get(self:getGo("rightViewForZuoWu/topTip/additionGo/plantInfo")):GetPreferredSize()
	Framework.ContentSizeImmediate.Get(self:getGo("rightViewForZuoWu/middleView/targetItem/count")):GetPreferredSize()
end

return WorkshopPanelForZuoWu