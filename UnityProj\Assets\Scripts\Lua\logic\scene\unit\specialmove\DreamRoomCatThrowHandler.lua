module("logic.scene.unit.specialmove.DreamRoomCatThrowHandler",package.seeall)

local DreamRoomCatThrowHandler = class("DreamRoomCatThrowHandler", PlayerShortActionHandler)

function DreamRoomCatThrowHandler:onStart()
    print("start fly")
    self.unit:setNavEnable(false)
    self.unit:setDirection(1)
    self.info.z = GameUtils.GetPosYOnGround(self.info.x, self.info.y)
    printInfo("jump pos", self.info.x, self.info.y, self.info.z, self.unit.id)
    self:prepareDate()
    self.aniHelper = self.unit.skinView:getAnimHelper()

    self:startUpDotween()
    self.aniHelper:setAnimation(self._hitUpAni, false, 1)
    self.aniHelper:addAnimation(self._hitDownAni, false, 1)
    self.unit:addListener(UnitNotify.Arrive, self.onArrive, self)
end

--准备各种参数
function DreamRoomCatThrowHandler:prepareDate()
    --飞行速度
    self.flySpeed = 15
    -- tonumber(GameUtils.getCommonConfig("SpeedToOutArea"))
    --落地高度
    self.height = 2
    local startX, startY = self.unit:getPos()
    --加高度
    local dis1 = math.sqrt((self.info.x - startX) ^ 2 + (self.info.y + self.height - startY) ^ 2)
    self.uptime = dis1 / self.flySpeed
    local minFlyTime = 0.5
    if self.uptime < minFlyTime then
        self.uptime = minFlyTime
    end
    self.downtime = 0.2

    self._hitUpAni = "hy_srh_beitaotai"
    self._hitDownAni = "zy_fei_2"
end

function DreamRoomCatThrowHandler:startUpDotween()
    --飞行时间
    print("起飞")
    self.aniHelper:setSpeed(1.233 / self.uptime)
    self.tween =
        self.unit.go.transform:DOMove(Vector3.New(self.info.x, self.info.z + self.height, self.info.y), self.uptime)
    self.tween:SetEase(DG.Tweening.Ease.OutSine)
    self.tween:OnComplete(handler(self.startDownDotween, self))
end

function DreamRoomCatThrowHandler:startDownDotween()
    print("下落")
    self.aniHelper:setSpeed(1.8)
    self.tween = self.unit.go.transform:DOMove(Vector3.New(self.info.x, self.info.z, self.info.y), self.downtime)
    self.tween:SetEase(DG.Tweening.Ease.OutSine)
    self.tween:OnComplete(handler(self._hitAniOver, self))
end

function DreamRoomCatThrowHandler:_hitAniOver()
    print("沾地")
    local t = self.aniHelper:getAnimationDuration(self._hitDownAni) - self.downtime - 0.15
    settimer(t / 1.8, self.hitAniOver, self, false)
end

function DreamRoomCatThrowHandler:hitAniOver()
    print("结束")
    print("hitAniOver", self.info.x, self.info.y, self.info.z, self.unit.id)
    self:finish()
    self.unit:teleport(self.info.x, self.info.y)
end

function DreamRoomCatThrowHandler:onStop()
    print("DreamRoomCatThrowHandler stop")
    if self.hasStop then
        return
    end
    self.hasStop = true
    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end
    self.unit:specificWalkAni()
    self.unit:specificIdleAni()
    if self.aniHelper then
        self.aniHelper:setSpeed(1)
        self.aniHelper:stopAnimation(1)
        self.aniHelper = nil
    end

    removetimer(self.hitAniOver, self)
    self.unit:removeListener(UnitNotify.Arrive, self.onArrive, self)
end

function DreamRoomCatThrowHandler:onArrive()
    self:finish(true)
end

return DreamRoomCatThrowHandler