-- {excel:J交互式物品配置.xlsx, sheetName:export_头像框}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_head_windows", package.seeall)

local title = {id=1,subType=2,name=3,sortId=4,quality=5,desc=6,useCondition=7,showDate=8,gainWayText=9,canFlip=10,frameType=11,expireTime=12}

local dataList = {
	{18000001, 1, "奥比熊熊头像框", 0, 2, "奥比岛最经典的熊熊头像框，岛民们的最爱，官网预约可获得。", 0, "2022-01-01T05:00:00", "官网预约", true, 1, 0},
	{18000002, 1, "搞怪小丑头像框", 0, 3, "尽显马戏风情的搞怪小丑头像框。", 0, "2022-09-16T05:00:00", "快乐签到", true, 1, 0},
	{18000003, 1, "打工人头像框", 0, 1, "充满朴实气息的打工人头像框，尽显劳模岛主们的勤劳气质！", 0, nil, "", true, 1, 0},
	{18000004, 1, "小精灵头像框", 0, 1, "头像框上的小精灵，会在你的耳边说什么悄悄话呢？", 0, nil, "", true, 1, 0},
	{18000005, 1, "游戏时光头像框", 0, 2, "戴上游戏时光头像框，下次摇汽水一定会获胜！", 0, "2022-09-02T05:00:00", "小游戏时光", false, 1, 0},
	{18000006, 1, "雾镜霜华头像框", 0, 2, "戴上雾镜霜华头像框，与小熊雪人漫步雪国胜境。", 0, nil, "", true, 1, 0},
	{18000007, 1, "风色花季头像框", 0, 3, "浪漫的风色花季头像框，适合搭配春意盎然的服饰。", 0, nil, "花神节签到", true, 1, 0},
	{18000008, 1, "金块头像框", 0, 3, "戴上金块头像框，拥有金块同款的小红帽与大耳朵。", 0, nil, "", true, 1, 0},
	{18000009, 1, "甜点师头像框", 0, 3, "戴上可爱的甜点师头像框，手打奶油都更有劲了！", 0, nil, "", true, 1, 0},
	{18000010, 1, "童年回忆头像框", 0, 3, "五彩缤纷的童年回忆头像框，就算到了99岁也要戴。", 0, nil, "官网活动", true, 1, 0},
	{18000011, 1, "海岛派对头像框", 0, 3, "充满夏日风情的海岛派对头像框，关键词是阳光、海浪与小麦汁！", 0, nil, "", true, 1, 0},
	{18000012, 1, "小耶头像框", 0, 4, "戴上服装设计师小耶的头像框，下次搭配衣服会更加得心应手。", 0, "2022-10-27T05:00:00", "郁香之愿熊熊宝", true, 1, 0},
	{18000013, 1, "黑古拉头像框", 0, 4, "黑古拉强烈要求，要在他的头像框上加上象征他身份的金属装饰。", 0, nil, "", true, 1, 0},
	{18000014, 1, "红宝石头像框", 0, 5, "红宝石会员的闪耀的象征！", 1, "2022-01-01T05:00:00", "", true, 1, 0},
	{18000015, 1, "纳多王子头像框", 0, 4, "剑盾与皇冠，是属于纳多王子的标志。", 0, "2022-01-01T05:00:00", "新手七天目标", true, 1, 0},
	{18000016, 1, "星空幻想头像框", 0, 4, "穿越漫天星河，只为再见你一面...", 0, "2022-01-01T05:00:00", "首充", true, 1, 0},
	{18000017, 1, "默认", 1, 1, "初始无头像框", 0, "2022-01-01T05:00:00", "", true, 1, 0},
	{18000018, 1, "奥比回忆头像框", 0, 3, "无论这个世界怎么变化，我们还是原来的我们...", 0, "2022-01-01T05:00:00", "多多号绑定", true, 1, 0},
	{18000019, 1, "心里的花", 0, 4, "你还记得吗？", 0, "2022-01-01T05:00:00", "愿望值奖励", true, 1, 0},
	{18000020, 1, "星际之旅", 0, 4, "在这片茫茫星际旅行当中，幸与你相伴...", 0, "2022-01-01T05:00:00", "愿望值奖励", true, 1, 0},
	{18000021, 1, "欢乐庆典", 0, 4, "欢乐的头像框，很适合热闹的季节呢。", 0, nil, "", true, 1, 0},
	{18000022, 1, "铃兰花语", 0, 4, "仔细听，铃兰在唱什么歌谣？", 0, "2022-01-01T05:00:00", "繁花颂", false, 1, 0},
	{18000023, 1, "星际守护者", 0, 3, "守护这一片星空，与你一起！", 0, "2022-07-21T05:00:00", "星际签到", true, 1, 0},
	{18000024, 1, "黑元素力量", 0, 3, "沉睡在深渊的元素之力，听从我的呼唤，降临到这片大地上...", 0, "2022-08-11T05:00:00", "探索签到", true, 1, 0},
	{18000025, 1, "明月佳期", 0, 4, "小兔在月亮上做些什么呢？", 0, "2022-09-02T05:00:00", "星愿卡", true, 1, 0},
	{18000026, 1, "光源与夜影", 0, 4, "光与影，总是相伴相随。", 0, "2022-08-11T05:00:00", "影之谜限时图鉴", true, 1, 0},
	{18000027, 1, "奶油棒棒糖", 0, 4, "仿佛被甜蜜环绕了。", 0, nil, "", true, 1, 0},
	{18000028, 1, "蜜糖王冠", 0, 4, "甜蜜美味的蜜糖王冠，是美味甜点师的最爱。", 0, "2022-09-29T05:00:00", "甜蜜梦工场", true, 1, 0},
	{18000029, 1, "奇趣马戏团", 0, 4, "来看奇趣马戏团的表演吧！", 0, "2022-09-16T05:00:00", "快乐节限时图鉴", true, 1, 0},
	{18000030, 1, "白雪姬", 0, 4, "要吃一个美味的苹果吗？", 0, "2022-09-16T05:00:00", "奥比生活", true, 1, 0},
	{18000031, 1, "枫叶飘零", 0, 4, "既然这就是我的宿命，当初又何必给我假的希望呢...", 0, "2022-09-16T05:00:00", "快乐节小熊钱罐", true, 1, 0},
	{18000032, 1, "奇妙饮品", 0, 4, "你要微糖、少糖还是全糖？", 0, "2022-10-09T05:00:00", "奇异食盒", true, 1, 0},
	{18000033, 1, "郁香之季", 0, 4, "在郁金香盛开的季节，与你共赏花海。", 0, "2022-10-27T05:00:00", "郁香之愿限时图鉴", true, 1, 0},
	{18000034, 1, "沉睡荆棘", 0, 4, "于荆棘中沉睡，等待命定之人。", 0, "2022-10-27T05:00:00", "奥比生活", true, 1, 0},
	{18000035, 1, "黄宝石花园", 0, 6, "甜甜的，是蜜糖与花的香气。", 0, "2022-10-27T05:00:00", "隐藏成就", true, 1, 0},
	{18000036, 1, "黄宝石城堡", 0, 6, "甜甜的，华丽的城堡也开出花来。", 0, "2022-10-27T05:00:00", "隐藏成就", true, 1, 0},
	{18000037, 1, "水晶之盼", 0, 4, "这是你的期待吗？是你盼望的未来吗？", 0, "2022-11-23T05:00:00", "奥比生活", true, 1, 0},
	{18000038, 1, "向阳而生", 0, 4, "向阳而生，逐光前行。", 0, "2022-11-23T05:00:00", "嗡运会限时图鉴", true, 1, 0},
	{18000039, 1, "藏金翼使", 0, 3, "甜甜的花酿，能和你一起分享，实在太好了。", 0, "2022-11-23T05:00:00", "嗡嗡签到", true, 1, 0},
	{18000040, 1, "柯莱头像框", 0, 4, "戴上机器熊柯莱的头像框，做数学题会不会更快呢？", 0, "2022-11-23T05:00:00", "嗡运会熊熊宝", true, 1, 0},
	{18000041, 1, "冰与火的碰撞", 0, 4, "冰点与沸点之间的碰撞，水火会相融吗？", 0, "2022-12-15T05:00:00", "凛冬节限时图鉴", true, 1, 0},
	{18000042, 1, "星纳头像框", 0, 4, "戴上奥柏财团少爷星纳的头像框，离优雅更近一步了~", 0, "2022-12-15T05:00:00", "凛冬节熊熊宝", true, 1, 0},
	{18000043, 1, "冰晶之誓", 0, 4, "许下誓言之心如水晶般澄澈。", 0, "2022-12-15T05:00:00", "奥比生活", true, 1, 0},
	{18000044, 1, "冰碎星尘", 0, 3, "听说灵魂会化作星星，那星尘会不会是她为我留下的眼泪呢？", 0, "2022-12-15T05:00:00", "凛冬签到", true, 1, 0},
	{18000045, 1, "如诗如歌", 0, 4, "就在这童话般的世界度过梦幻的一天吧~", 0, "2022-12-29T05:00:00", "祈福节抽奖", true, 1, 0},
	{18000046, 1, "西游历险记", 0, 3, "山山水水，隐隐迢迢，此行便是万里。", 0, "2023-01-19T05:00:00", "游仙记", true, 1, 0},
	{18000047, 1, "玉兔呈祥", 0, 4, "福气多多，快乐连连，万事圆圆，微笑甜甜。", 0, "2023-01-12T05:00:00", "风华节限时图鉴", true, 1, 0},
	{18000048, 1, "剑与花", 0, 4, "剑与花的故事，是陪伴与救赎。", 0, "2023-01-12T05:00:00", "奥比生活", true, 1, 0},
	{18000049, 1, "阿达头像框", 0, 4, "戴上来自大炎国阿达的头像框，一起领略大炎风情吧！", 0, "2023-01-12T05:00:00", "风华节小熊钱罐", true, 1, 0},
	{18000050, 1, "迎春风貌", 0, 3, "奥比岛将迎来2023年，新的一年，将会有新的风貌！", 0, "2023-01-12T05:00:00", "风华节签到", false, 1, 0},
	{18000051, 1, "禄显灵至", 0, 4, "由仙鹤亲手编织、象征吉祥如意的圆环。", 0, "2023-01-19T05:00:00", "风华秀", true, 1, 0},
	{18000052, 1, "黑猫与玫瑰", 0, 4, "赠与你世间最美的玫瑰。", 0, "2023-02-09T05:00:00", "奥比生活", true, 1, 0},
	{18000053, 1, "如梦誓言", 0, 4, "戴在她头上的头纱，衬得她的脸庞更加美丽。", 0, "2023-02-09T05:00:00", "爱之旅限时图鉴", true, 1, 0},
	{18000054, 1, "金块头像框", 0, 4, "戴上金块头像框，拥有金块同款的小红帽与大耳朵。", 0, "2023-02-09T05:00:00", "爱之旅熊熊宝", true, 1, 0},
	{18000055, 1, "心跳触动", 0, 3, "当他单膝下跪那一刻，我不由自主地说出那三个字...", 0, "2023-02-09T05:00:00", "爱之旅签到", true, 1, 0},
	{18000056, 1, "轻羽流光", 0, 4, "一起走吧，去向更广阔的天空。", 0, "2023-03-09T05:00:00", "奥比生活", true, 1, 0},
	{18000057, 1, "海神之歌", 0, 4, "据说在这片海域中，曾有人听到海神美妙的歌声。", 0, "2023-03-09T05:00:00", "鱼之歌显示图鉴", true, 1, 0},
	{18000058, 1, "梅尔头像框", 0, 4, "戴上梅尔头像框，陪梅尔一起打游戏吧。", 0, "2023-03-09T05:00:00", "鱼之歌熊熊宝", true, 1, 0},
	{18000059, 1, "海神桂冠", 0, 3, "镶嵌着红宝石的王冠，或许曾亲吻过海神的秀发。", 0, "2023-03-09T05:00:00", "海神签到", true, 1, 0},
	{18000060, 1, "造梦之旅", 0, 4, "转动魔方，进入神奇的梦境世界。", 0, "2023-03-22T05:00:00", "造梦盒", true, 1, 0},
	{18000061, 1, "花之旅", 0, 4, "跟着花与叶，来一场春季的旅行吧！", 0, "2023-04-06T05:00:00", "奥比生活", true, 1, 0},
	{18000062, 1, "云端咖啡", 0, 4, "出自云端最受欢迎的咖啡店，要来一杯品尝试试吗？", 0, "2023-04-06T05:00:00", "音之咖限时图鉴", true, 1, 0},
	{18000063, 1, "理真头像框", 0, 4, "戴上理真头像框，会变得和她一样可爱吗？", 0, "2023-04-06T05:00:00", "音之咖小熊钱罐", true, 1, 0},
	{18000064, 1, "音乐咖啡", 0, 3, "人生的苦涩，都会在一曲悠扬旋律后疏散。", 0, "2023-04-06T05:00:00", "音之咖签到", true, 1, 0},
	{18000065, 1, "偶像公式照", 0, 4, "登上万众瞩目的舞台，你就是最闪耀的偶像！", 0, "2023-04-27T05:00:00", "星歌会", true, 1, 0},
	{18000066, 1, "异域奇缘", 0, 4, "在这流砂之城相遇，便是一段奇缘。", 0, "2023-05-11T05:00:00", "奥比生活", true, 1, 0},
	{18000067, 1, "玩偶奇幻夜", 0, 4, "玩具宫殿的奇幻夜晚，是谁先敲响了欢乐铜铃。", 0, "2023-05-11T05:00:00", "玩具屋限时图鉴", true, 1, 0},
	{18000068, 1, "阿拉斯头像框", 0, 4, "戴上阿拉斯头像框，一起感受魔法的魅力！", 0, "2023-05-11T05:00:00", "玩具屋熊熊宝", true, 1, 0},
	{18000069, 1, "玩具童话", 0, 3, "一起进入玩具世界，享受童趣。", 0, "2023-05-11T05:00:00", "闹铃叮叮", true, 1, 0},
	{18000070, 1, "头号玩家", 0, 3, "虚拟与现实的完美契合，称为头号玩家。", 0, "2023-05-11T05:00:00", "官网活动", true, 1, 0},
	{18000071, 1, "秘境茶会", 0, 4, "进入秘境森林，和EMMA一起享受茶会吧！", 0, "2023-05-25T05:00:00", "祈祷之树", false, 1, 0},
	{18000072, 1, "双子座的小时光", 0, 4, "最快乐的事情，就是有你在身边。", 0, "2023-06-08T05:00:00", "奥比生活", true, 1, 0},
	{18000073, 1, "荒漠之迹", 0, 4, "隐迹于荒漠之城的神像，即将在月下苏醒。", 0, "2023-06-08T05:00:00", "奇游夜限时图鉴", true, 1, 0},
	{18000074, 1, "索菲亚头像框", 0, 4, "戴上索菲亚头像框，拥有同款的暖色系小耳朵。", 0, "2023-06-08T05:00:00", "奇游夜熊熊宝", true, 1, 0},
	{18000075, 1, "奇馆之夜", 0, 3, "一起探索奇妙的博物馆吧！", 0, "2023-06-08T05:00:00", "奇游签到", true, 1, 0},
	{18000076, 1, "甜甜蜜豆", 0, 4, "和红小豆一起郊游，可别忘了戴上最最重要的小黄鸭帽！", 0, "2023-06-22T05:00:00", "甜蜜之愿", false, 1, 0},
	{18000077, 1, "巨蟹座治愈时刻", 0, 4, "偶尔也给自己放个假，去一趟海边吧。", 0, "2023-07-06T05:00:00", "奥比生活", true, 1, 0},
	{18000078, 1, "炫彩星河", 0, 4, "来自星河的独角兽使者，为庆典送上最璀璨的虹光。", 0, "2023-07-06T05:00:00", "炫彩季限时图鉴", false, 1, 0},
	{18000079, 1, "莉卡头像框", 0, 4, "戴上莉卡头像框，拥有同款帅气的小龙角！", 0, "2023-07-06T05:00:00", "炫彩季小熊钱罐", true, 1, 0},
	{18000080, 1, "缤纷庆典", 0, 3, "一起参加缤纷的炫彩季庆典！", 0, "2023-07-06T05:00:00", "炫彩签到", true, 1, 0},
	{18000081, 1, "绒绒小羊", 0, 4, "绒绒的头像框，是羊羊们友好的赠礼。", 0, "2023-07-12T05:00:00", "小羊村", false, 1, 0},
	{18000082, 1, "夏日茶饮", 0, 4, "炎炎夏日，怎么少得了一杯清凉的茶饮？", 0, "2023-07-27T05:00:00", "冰柠有礼", true, 1, 0},
	{18000083, 1, "狮子座的荣誉", 0, 4, "让我们不负努力，顶峰相见。", 0, "2023-08-10T05:00:00", "奥比生活", true, 1, 0},
	{18000084, 1, "银河守护", 0, 4, "银河中漫游的守护者，守护生命和守护银河同样重要。", 0, "2023-08-10T05:00:00", "星际赛限时图鉴", true, 1, 0},
	{18000085, 1, "风儿头像框", 0, 4, "和风儿一起，静听风吹过花海的声音。", 0, "2023-08-10T05:00:00", "星际赛熊熊宝", true, 1, 0},
	{18000086, 1, "星际领航", 0, 3, "从银河中穿梭，来一场星际冒险吧！", 0, "2023-08-10T05:00:00", "星际签到", true, 1, 0},
	{18000087, 1, "梦中花园", 0, 4, "沉睡的囡茜，在梦中打造了一片盛放的花园。", 0, "2023-08-22T05:00:00", "银汉迢迢", true, 1, 0},
	{18000088, 1, "处女座馥郁花香", 0, 4, "甜甜的香气萦绕身侧，透着淡淡的玫瑰花香。", 0, "2023-09-07T05:00:00", "奥比生活", true, 1, 0},
	{18000089, 1, "蘑菇小精灵", 0, 4, "从金色的落叶中，钻出了一只蘑菇小精灵！", 0, "2023-09-07T05:00:00", "快乐节限时图鉴", true, 1, 0},
	{18000090, 1, "友谊音符", 0, 4, "在音符树下，与你许下友谊的约定。", 0, "2023-09-07T05:00:00", "快乐节小熊钱罐", true, 1, 0},
	{18000091, 1, "欢庆秋日", 0, 3, "金黄的银杏叶冲你打着招呼：看，秋天来了！", 0, "2023-09-07T05:00:00", "快乐签到", true, 1, 0},
	{18000092, 1, "玉兔望月", 0, 4, "低头吃月饼，抬头看月亮。这就是最棒的满月节啦！", 0, "2023-09-27T05:00:00", "玉兔拜月", true, 1, 0},
	{18000093, 1, "京韵戏腔", 0, 4, "婉转的戏腔，将这幕戏缓缓拉开。", 0, "2023-09-22T05:00:00", "芳华曲", true, 1, 0},
	{18000094, 1, "快乐奥比庆典", 0, 3, "奥比庆15周年头像框，和小奥比们一起快乐共舞。", 0, "2023-09-22T05:00:00", "奥比庆", false, 1, 0},
	{18000095, 1, "天秤座的守护", 0, 4, "让我们一起，守护每一位熊熊的公平和正义！", 0, "2023-10-12T05:00:00", "奥比生活", true, 1, 0},
	{18000096, 1, "月灵之约", 0, 4, "与月灵许下约定，一起享受月灵盛宴吧！", 0, "2023-10-12T05:00:00", "月灵节限时图鉴", true, 1, 0},
	{18000097, 1, "星星花云头像框", 0, 4, "踩在花云上，一起去摘星星吧！", 0, "2023-10-12T05:00:00", "月灵节熊熊宝", true, 1, 0},
	{18000098, 1, "迷糊瓜瓜", 0, 3, "呆呆瓜也想尝尝，糖果是什么滋味！", 0, "2023-10-12T05:00:00", "月灵签到", true, 1, 0},
	{18000099, 1, "曼珠花开", 0, 4, "曼珠沙华伸出细长的花丝，似乎在期待月光的降临。", 0, "2023-10-26T05:00:00", "月典星愿", true, 1, 0},
	{18000100, 1, "天蝎座的命运", 0, 4, "接受命运，或是抵抗命运，你要如何选择？", 0, "2023-11-09T05:00:00", "奥比生活", true, 1, 0},
	{18000101, 1, "音符之花", 0, 4, "在奏响的音符中，希望逐渐绽放成花。", 0, "2023-11-09T05:00:00", "雾月颂限时图鉴", true, 1, 0},
	{18000102, 1, "艾拉头像框", 0, 4, "和艾拉一起，守护可爱的小动物吧！", 0, "2023-11-09T05:00:00", "雾月颂熊熊宝", true, 1, 0},
	{18000103, 1, "假面舞会", 0, 3, "戴上面具，在舞会上尽情狂欢吧！", 0, "2023-11-09T05:00:00", "雾月签到", true, 1, 0},
	{18000104, 1, "电视奇旅", 0, 4, "电视剧已经打开了，让SUSUMI陪大家来一场奇妙的冒险之旅吧！", 0, "2023-11-23T05:00:00", "魔法奇愿", true, 1, 0},
	{18000105, 1, "射手座的冒险", 0, 4, "未知的世界，等着我来探索吧！", 0, "2023-12-07T05:00:00", "奥比生活", true, 1, 0},
	{18000106, 1, "冬日晨星", 0, 4, "晨星的辉光映照在雪地上，仿佛在诉说着星空的心事。", 0, "2023-12-07T05:00:00", "凛冬节限时图鉴", true, 1, 0},
	{18000107, 1, "青木森林头像框", 0, 4, "“早上好啊，小奥比！”明媚的阳光下，土拨鼠正向你挥手问好。", 0, "2023-12-07T05:00:00", "凛冬节熊熊宝", true, 1, 0},
	{18000108, 1, "雪人堆堆", 0, 3, "阿——阿嚏！虽然是雪人，但也要戴好围巾呢。", 0, "2023-12-07T05:00:00", "凛冬签到", true, 1, 0},
	{18000109, 1, "糖霜姜饼", 0, 4, "香喷喷的姜饼裹上一层甜蜜的糖霜，让空气中都弥漫着一股幸福的味道。", 0, "2023-12-21T05:00:00", "饼饼星愿", true, 1, 0},
	{18000110, 1, "摩羯座的思绪", 0, 4, "为了实现心中的理想，忍受孤独只是最基本的必修课。", 0, "2024-01-04T05:00:00", "奥比生活", true, 1, 0},
	{18000111, 1, "光影交辉", 0, 4, "过去与未来之神重聚，光明与暗影交辉。", 0, "2024-01-04T05:00:00", "光与影限时图鉴", true, 1, 0},
	{18000112, 1, "马露头像框", 0, 4, "和马露一起，让每一天都过得精致美好吧！", 0, "2024-01-04T05:00:00", "光与影熊熊宝", true, 1, 0},
	{18000113, 1, "时光往复", 0, 3, "过去与未来在时间沙漏中循环，明天会是今天的重复，还是崭新的一天？", 0, "2024-01-04T05:00:00", "光影签到", true, 1, 0},
	{18000114, 1, "芋泥波波", 0, 4, "来一份芋泥波波，波波要做成可爱的小兔子形状呦！", 0, "2024-01-18T05:00:00", "糖水星愿", true, 1, 0},
	{18000115, 1, "水瓶座的倾听", 0, 4, "浪花翻滚的声音，是水在向你讲述过去的故事。", 0, "2024-02-01T05:00:00", "奥比生活", true, 1, 0},
	{18000116, 1, "海晏河清", 0, 4, "河清海晏，时和岁丰。", 0, "2024-02-01T05:00:00", "风华节限时图鉴", true, 1, 0},
	{18000117, 1, "紫禁传说", 0, 4, "在落雪之日，点一盏明灯，听一听紫禁城的故事。", 0, "2024-02-01T05:00:00", "风华节小熊钱罐", true, 1, 0},
	{18000118, 1, "祥龙贺岁", 0, 3, "新年有绚烂的烟花、香喷喷的饺子、有趣的联欢晚会，还有你在我身边。", 0, "2024-02-01T05:00:00", "紫禁画卯", false, 1, 0},
	{18000119, 1, "灯火年年", 0, 4, "年年岁岁花灯亮，岁岁年年共此时。", 0, "2024-02-22T05:00:00", "元灯星愿", true, 1, 0},
	{18000120, 1, "龙凤呈祥", 0, 4, "龙腾盛世，有凤来仪。", 0, "2024-02-08T05:00:00", "神龙谱", true, 1, 0},
	{18000121, 1, "双鱼座的羁绊", 0, 4, "鱼儿相遇又相离，何时能找到属于它们的目的地？", 0, "2024-03-07T05:00:00", "奥比生活", true, 1, 0},
	{18000122, 1, "蝶栖蔓语", 0, 4, "那些讲给蝴蝶的故事，能否也讲给我听呢？", 0, "2024-03-07T05:00:00", "春日游限时图鉴", true, 1, 0},
	{18000123, 1, "奥比雪山头像框", 0, 4, "你怎么穿得这么多呀？我可是一点都不冷呢！", 0, "2024-03-07T05:00:00", "春日游熊熊宝", true, 1, 0},
	{18000124, 1, "复苏之春", 0, 3, "戴上遮阳帽，一起去郊游吧！", 0, "2024-03-07T05:00:00", "春光签到", true, 1, 0},
	{18000125, 1, "春日茶会", 0, 4, "完美的春茶会，需要有春风、花香、一份精致的茶和巧克力蛋。", 0, "2024-03-21T05:00:00", "茶会星愿", false, 1, 0},
	{18000126, 1, "白羊座的毛茸茸", 0, 4, "枕在羊咩咩柔软的绒毛中，就像是枕在了云朵上一般，柔软而宁静。", 0, "2024-04-03T05:00:00", "奥比生活", true, 1, 0},
	{18000127, 1, "万象交汇", 0, 4, "所有奇幻、美妙、不拘一格的景象，皆在万象之港。", 0, "2024-04-03T05:00:00", "城市派限时图鉴", true, 1, 0},
	{18000128, 1, "莱昂头像框", 0, 4, "戴上莱昂头像框，说话都变得更有信服力了！", 0, "2024-04-03T05:00:00", "城市派小熊钱罐", true, 1, 0},
	{18000129, 1, "港口明星", 0, 3, "港口最闪亮的明星，当然是鸥鸥我啊！快为本鸥献上你们的薯条！", 0, "2024-04-03T05:00:00", "城市签到", true, 1, 0},
	{18000130, 1, "葡萄庆典头像框", 0, 4, "在庆典的狂欢之日，怎么少的了晶莹的葡萄呢？来，让我们举葡共庆！", 0, "2024-04-18T05:00:00", "文明遗诗", true, 1, 0},
	{18000131, 1, "美食盛宴头像框", 0, 4, "端好小碗碗，准备享受一场美食盛宴吧！", 0, "2024-04-29T05:00:00", "星厨宴", true, 1, 0},
	{18000132, 1, "幽灵酒店", 0, 4, "欢迎光临幽灵酒店，接下来，您将享受到跟我一样的美好睡眠。", 0, "2024-04-03T05:00:00", "幽灵熊", true, 1, 0},
	{18000133, 1, "金牛座的偏爱", 0, 4, "珍藏无数宝物，更珍藏对你独一无二的偏爱。", 0, "2024-05-09T05:00:00", "奥比生活", true, 1, 0},
	{18000134, 1, "勇者之旅", 0, 4, "戴上勇士的勋章，开启独一无二的冒险之旅！", 0, "2024-05-09T05:00:00", "游戏家限时图鉴", true, 1, 0},
	{18000135, 1, "奥比广场头像框", 0, 4, "大鲸鱼在奥比广场听到了很多小秘密，不过它可不会说出来呢。", 0, "2024-05-09T05:00:00", "游戏家熊熊宝", true, 1, 0},
	{18000136, 1, "冒险游戏", 0, 3, "新一轮冒险又要开始了，这次我一定会躲开藤蔓的攻击，成功获得宝箱！", 0, "2024-05-09T05:00:00", "玩家登录", true, 1, 0},
	{18000137, 1, "童趣鸭鸭", 0, 4, "门前大桥下，跑过一只鸭，哎呀哎呀摔倒啦！", 0, "2024-05-23T05:00:00", "童梦奇盒", true, 1, 0},
	{18000138, 1, "爱意蔓延", 0, 4, "这酸涩又甜蜜的感觉，是口中苹果的滋味，还是心中爱意的悸动？", 0, "2024-06-07T05:00:00", "奥比生活", true, 1, 0},
	{18000139, 1, "恋心魅影", 0, 4, "世人都说我们陷入了疯狂的爱恋旋涡，但我甘心沉溺其中。", 0, "2024-06-07T05:00:00", "名侦探限时图鉴", true, 1, 0},
	{18000140, 1, "薇丽塔头像框", 0, 4, "和薇丽塔一起，元气满满地面对每天的工作吧！", 0, "2024-06-07T05:00:00", "名侦探熊熊宝", true, 1, 0},
	{18000141, 1, "超级侦探", 0, 3, "成为超级侦探的第一步：头戴猎鹿帽，怀揣放大镜。", 0, "2024-06-07T05:00:00", "侦探报到", true, 1, 0},
	{18000142, 1, "校园明星", 0, 4, "绽放青春的光芒，做校园里永远闪耀的星。", 0, "2024-06-20T05:00:00", "青春心愿", true, 1, 0},
	{18000143, 1, "萌二甜甜梦", 0, 4, "睡前来杯牛奶，将会拥有甜甜的梦哦！", 0, "2024-07-25T05:00:00", "萌二", true, 1, 0},
	{18000144, 1, "秘瓶真理", 0, 4, "耐心等待，世界的真理正在瓶中酝酿。", 0, "2024-07-04T05:00:00", "奥比生活", true, 1, 0},
	{18000145, 1, "幻海之螺", 0, 4, "侧耳细听，海螺里传出了如梦似幻的旋律，仿佛在轻声诉说着深海的故事。", 0, "2024-07-04T05:00:00", "星海季限时图鉴", true, 1, 0},
	{18000146, 1, "海底小镇", 0, 4, "滴滴！坐上海龟出租车，游览海底的小镇吧！", 0, "2024-07-04T05:00:00", "星海季小熊钱罐", true, 1, 0},
	{18000147, 1, "奇趣海洋", 0, 3, "除了圆滚滚的章鱼、胖嘟嘟的海星，海洋里还藏着什么？快去海里看看！", 0, "2024-07-04T05:00:00", "星海签到", true, 1, 0},
	{18000148, 1, "甜蜜心愿", 0, 4, "我的心愿，就是希望吃了蛋糕的小熊，都能甜甜蜜蜜、快快乐乐！", 0, "2024-07-18T05:00:00", "蛋糕甜愿", true, 1, 0},
	{18000149, 1, "魔仙之心", 0, 4, "怀揣善良和坚强的心，必定能成为无畏且闪耀的小魔仙！", 0, "2024-07-12T05:00:00", "巴啦啦", true, 1, 0},
	{18000150, 1, "命运之翼", 0, 4, "展开羽翼，坚定不移地翱翔，终将穿透命运的云雾，迎接胜利的晨曦。", 0, "2024-08-02T05:00:00", "奥比生活", true, 1, 0},
	{18000151, 1, "爱宠挚友", 0, 4, "我永远是你最亲密的家人、最好的朋友、最忠实的支持者！", 0, "2024-08-02T05:00:00", "星际赛限时图鉴", true, 1, 0},
	{18000152, 1, "海底古堡", 0, 4, "数百年前的海底古堡，藏着许多梦幻又奇异的景象。", 0, "2024-08-02T05:00:00", "星际赛熊熊宝", true, 1, 0},
	{18000153, 1, "萌宠搭档", 0, 3, "和你的萌宠搭档击个掌，一起在星际大赛大展身手吧！", 0, "2024-08-02T05:00:00", "星际签到", true, 1, 0},
	{18000154, 1, "心动夏夜", 0, 4, "我心中的悸动，是因为夜空中绚烂的烟火，还是因为我身边的你？", 0, "2024-08-20T05:00:00", "夏夜转转乐", true, 1, 0},
	{18000155, 1, "正义明心", 0, 4, "有他的智慧协助，她必能理智地判断一切事物，做出公正的决定。", 0, "2024-08-29T05:00:00", "奥比生活", true, 1, 0},
	{18000156, 1, "学院之神", 0, 4, "可爱的小奥比，遇上心软的守护神，碰撞出有趣的校园故事。", 0, "2024-08-29T05:00:00", "奇院旅限时图鉴", true, 1, 0},
	{18000157, 1, "莫顿头像框", 0, 4, "寂静的夜里，怀表在他的耳边，一遍遍诉说着那个忧伤的故事。", 0, "2024-08-29T05:00:00", "奇院旅熊熊宝", true, 1, 0},
	{18000158, 1, "奇妙校园", 0, 3, "纸张上的一笔一划，编织了校园里奇妙又欢乐的青春诗篇。", 0, "2024-08-29T05:00:00", "学生报到", true, 1, 0},
	{18000159, 1, "快乐无忧", 0, 3, "小奥比们来庆祝16周年奥比庆，一起做快乐无忧的小熊熊吧！", 0, "2024-09-22T05:00:00", "奥比庆", false, 1, 0},
	{18000160, 1, "快乐开学", 0, 3, "新学期拍了拍你：准备好迎接快乐的学习生活了吗？", 0, "2024-08-29T05:00:00", "", true, 1, 0},
	{18000161, 1, "倒吊愚者", 0, 4, "他以乐观的心态奔赴梦想，她以倒悬的视角静观世界。", 0, "2024-09-20T05:00:00", "奥比生活", true, 1, 0},
	{18000162, 1, "奇妙游乐园", 0, 4, "在奇妙游乐园里，每一秒都是快乐的开始。", 0, "2024-09-20T05:00:00", "快乐节主题图鉴", true, 1, 0},
	{18000163, 1, "兰瑟圣殿", 0, 4, "海底的古老圣殿里，每一块石壁都在低语着往昔的传说。", 0, "2024-09-20T05:00:00", "快乐节小熊钱罐", true, 1, 0},
	{18000164, 1, "快乐游园", 0, 3, "跟着旋转茶杯转呀转，游遍整个乐园吧！", 0, "2024-09-20T05:00:00", "快乐签到", true, 1, 0},
	{18000165, 1, "田园草莓", 0, 4, "草莓藤上草莓果，草莓园中你和我。", 0, "2024-10-11T05:00:00", "甜莓萌粒购", true, 1, 0},
	{18000166, 1, "红白之镜", 0, 4, "挣脱魔镜的束缚，寻回消失的快乐。", 0, "2024-09-25T05:00:00", "镜之国", true, 1, 0},
	{18000167, 1, "弗尔帕斯头像框", 0, 4, "他仍在记忆的废墟中徘徊，唱着被时间遗忘的哀歌。", 0, "2024-09-27T05:00:00", "心镜迷影", true, 1, 0},
	{18000168, 1, "宿命契约", 0, 4, "所有的结局，早在契约签下的那刻便已注定，无论如何挣扎，终究无法逃离。", 0, "2024-10-24T05:00:00", "奥比生活", true, 1, 0},
	{18000169, 1, "缝补希望", 0, 4, "尽管身心早已满是伤痕，仍有温柔的心在默默修补，缝缀出一丝丝希望。", 0, "2024-10-24T05:00:00", "人偶屋主题图鉴", true, 1, 0},
	{18000170, 1, "海洋帝国", 0, 4, "千年前的深海，回荡着来自遥远星球的歌谣。", 0, "2024-10-24T05:00:00", "人偶屋熊熊宝", true, 1, 0},
	{18000171, 1, "庄园之夜", 0, 3, "当夜色笼罩着庄园的时候，它会永远注视着你……永远永远……", 0, "2024-10-24T05:00:00", "庄园签到", true, 1, 0},
	{18000172, 1, "岁月有情", 0, 4, "往昔的岁月像老照片一样逐渐褪色，唯有她与他之间的情谊始终不变。", 0, "2024-11-07T05:00:00", "影楼萌粒购", true, 1, 0},
	{18000173, 1, "小泡芙泡澡澡", 0, 4, "呼呼~一边喝牛奶，一边泡澡澡的感觉，真是太棒了！", 0, "2024-10-31T05:00:00", "小泡芙", false, 1, 0},
	{18000174, 1, "审判之月", 0, 4, "流云与明月相依，恰似庄严的审判使者，时刻守护着神秘的月亮女神。", 0, "2024-11-21T05:00:00", "奥比生活", true, 1, 0},
	{18000175, 1, "雾散妖现", 0, 4, "月光驱散浓雾，如舞台灯般落入妖界，夜色拉开帷幕，属于妖者的故事即将上演……", 0, "2024-11-21T05:00:00", "逢妖记主题图鉴", true, 1, 0},
	{18000176, 1, "奥比斯山脚", 0, 4, "缤纷的百花园、毛茸茸的羊驼、飞翔的蒲公英，构成了美妙的奥比斯山脚。", 0, "2024-11-21T05:00:00", "逢妖记熊熊宝", true, 1, 0},
	{18000177, 1, "妖火弥漫", 0, 3, "妖火弥漫，百妖横行，夜幕之下，万灵震颤。", 0, "2024-11-21T05:00:00", "雾月签到", true, 1, 0},
	{18000178, 1, "LAURA快乐小丑", 0, 4, "最可爱的小丑LAURA，为你带来最快乐的表演！", 0, "2024-12-05T05:00:00", "马戏萌粒购", false, 1, 0},
	{18000179, 1, "迷雾中的指引", 0, 4, "被生活迷雾围绕时，不妨信赖那些悄然出现的指引，或许那就是希望。", 0, "2024-12-19T05:00:00", "奥比生活", true, 1, 0},
	{18000180, 1, "晚安毛茸茸", 0, 4, "和世界轻声说一句晚安，随后轻轻地坠入毛茸茸的美梦里。", 0, "2024-12-19T05:00:00", "凛冬节主题图鉴", true, 1, 0},
	{18000181, 1, "索娅秘书头像框", 0, 4, "和索娅秘书一起，为热爱的事业倾注一百分的努力吧！", 0, "2024-12-19T05:00:00", "凛冬节熊熊宝", true, 1, 0},
	{18000182, 1, "柔软的角落", 0, 3, "在心里留出一个柔软的角落，轻轻安放每一份珍贵的情绪。", 0, "2024-12-19T05:00:00", "绮梦签到", true, 1, 0},
	{18000183, 1, "冬日情绵", 0, 4, "你踏着雪向我跑来的身影，就是我心中关于冬日最美好的记忆。", 0, "2025-01-09T05:00:00", "恋冬萌粒购", true, 1, 0},
	{18000184, 1, "奇迹幸运熊", 0, 5, "超级幸运的小熊，总能收获奇迹般的惊喜！", 0, "2024-12-19T05:00:00", "成就获得", true, 1, 0},
	{18000185, 1, "光辉冠冕", 0, 4, "丰饶之光与权力之辉，在她与他的冠冕上交相辉映。", 0, "2025-01-22T05:00:00", "奥比生活", true, 1, 0},
	{18000186, 1, "江湖传奇", 0, 4, "传闻影视城这个“江湖”里，藏龙卧虎，有好多新朋友等你结识！", 0, "2025-01-22T05:00:00", "风华节主题图鉴", true, 1, 0},
	{18000187, 1, "风华影视城", 0, 4, "影视风华迎蛇年，火树银花映盛世，共庆佳节喜洋洋！", 0, "2025-01-22T05:00:00", "风华节小熊钱罐", true, 1, 0},
	{18000188, 1, "快意侠客", 0, 3, "豪迈地痛饮一碗小麦汁，这便是畅快的侠客熊生！", 0, "2025-01-22T05:00:00", "影城签到", true, 1, 0},
	{18000189, 1, "赏味团圆夜", 0, 4, "满笼糕点映桃红，共庆甜蜜团圆夜。", 0, "2025-02-10T05:00:00", "赏味萌粒购", true, 1, 0},
	{18000190, 1, "缤纷闪耀兔", 0, 4, "对你的喜欢是缤纷闪耀的，藏也藏不住！", 0, "2025-02-06T05:00:00", "Starbliss", false, 1, 0},
	{18000191, 1, "莲华之音", 0, 4, "琵琶弦动，莲瓣轻展，交织出一幅跨越千年的古韵长卷。", 0, "2025-01-27T05:00:00", "蛇仙缘", true, 1, 0},
	{18000192, 1, "暗夜玫瑰", 0, 6, "暗夜之中，蓝玫瑰悄然绽放。", 0, "2025-02-21T05:00:00", "愿望值奖励", true, 2, 0},
	{18000193, 1, "春花灿烂", 0, 4, "灿烂朝阳，揭开春日序幕。", 0, "2025-02-21T05:00:00", "奥比生活", true, 1, 0},
	{18000194, 1, "花语心声", 0, 4, "鲜花将化作信使，向她倾诉他的心声。", 0, "2025-02-21T05:00:00", "时之花主题图鉴", true, 1, 0},
	{18000195, 1, "淘宝街", 0, 4, "繁华的淘宝街，各式商品应有尽有，满足你所有购物欲望！", 0, "2025-02-21T05:00:00", "时之花熊熊宝", true, 1, 0},
	{18000196, 1, "春日花艺", 0, 3, "细心修剪花枝，修剪出心目中的春日景致。", 0, "2025-02-21T05:00:00", "时花签到", true, 1, 0},
	{18000197, 1, "快乐酥酥米", 0, 4, "SUSUMI施展魔法，就能让你拥有最纯真的快乐！", 0, "2025-03-06T05:00:00", "漫展萌粒购", false, 1, 0},
	{18000198, 1, "超级玩家", 0, 4, "跨越虚拟与现实的界限，引领游戏风潮。", 0, nil, "外宣投放", true, 2, 30},
	{18000199, 1, "月映高塔", 0, 4, "闪电划破云层，一轮弯月便在空中显现，神秘又静谧的月光慢慢洒落在高塔上。", 0, "2025-03-20T05:00:00", "奥比生活", true, 1, 0},
	{18000200, 1, "梦幻星愿", 0, 4, "此后，MiMiA的每一个心愿，都有米卷为她点亮。", 0, "2025-03-20T05:00:00", "心之森主题图鉴", true, 1, 0},
	{18000201, 1, "弗里奇头像框", 0, 4, "有问题尽管找我，安全维护员弗里奇为您服务！", 0, "2025-03-20T05:00:00", "心之森小熊钱罐", true, 1, 0},
	{18000202, 1, "奇幻心森", 0, 3, "奇幻美妙的心之森，会吸引来最绚丽的星星与彩虹。", 0, "2025-03-20T05:00:00", "心森签到", true, 1, 0},
	{18000203, 1, "巡游气球", 0, 4, "带上缤纷多彩的气球，准备好做巡游队伍里最亮眼的小熊吧！", 0, "2025-04-03T05:00:00", "巡游萌粒购", true, 1, 0},
	{18000204, 1, "馥郁之香", 0, 4, "沉稳内敛的木质香调，遇上张扬辛辣的东方美食调，一段馥郁的故事由此拉开序幕。", 0, "2025-03-27T05:00:00", "馥郁香", true, 1, 0},
	{18000205, 1, "万物平衡", 0, 4, "世间万物都处于变化之中，又在变化中共同维系着平衡。", 0, "2025-04-17T05:00:00", "奥比生活", true, 1, 0},
	{18000206, 1, "时光之约", 0, 4, "穿越过漫长时光，她终将捧着鲜花，赶赴与他的旧日之约。", 0, "2025-04-17T05:00:00", "闯港城主题图鉴", true, 1, 0},
	{18000207, 1, "家族基地", 0, 4, "蜜泉的雕像旁、暖和的温泉里、漂亮的花坛前，处处都是家族小熊欢聚的身影。", 0, "2025-04-17T05:00:00", "闯港城熊熊宝", true, 1, 0},
	{18000208, 1, "港城故事", 0, 3, "在霓虹闪耀的港城里，书写属于你的黄金年代。", 0, "2025-04-17T05:00:00", "重返港城", true, 1, 0},
	{18000210, 1, "米花派对", 0, 4, "忙了一天，猪米和花花终于把水果、奶油蛋糕、装饰都弄好了，今晚别忘了来参加派对哦！", 0, "2025-05-01T05:00:00", "添点米花", false, 1, 0},
	{18000211, 1, "舞台幽影", 0, 4, "面具下扭曲的灵魂，终将在破碎的旋律里燃烧成灰烬。", 0, "2025-05-15T05:00:00", "奥比生活", true, 1, 0},
	{18000212, 1, "甜酷momo狗", 0, 4, "听，劲爆的音乐在呼唤，我要带上我的吉他，踏上我的甜酷音乐之旅啦！", 0, "2025-05-15T05:00:00", "萌市集主题图鉴", false, 1, 0},
	{18000213, 1, "超甜nono狗", 0, 4, "谁比糖果还甜？当然是nono狗啦！", 0, "2025-05-15T05:00:00", "萌市集熊熊宝", false, 1, 0},
	{18000214, 1, "怪萌市集", 0, 3, "别怕在市集表现得奇奇怪怪，这里的大家都能懂你的可可爱爱。", 0, "2025-05-15T05:00:00", "市集签到", true, 1, 0},
	{18000215, 1, "糖果牙牙", 0, 4, "甜甜的糖果来捣蛋啦，它会让小熊长出蛀牙！别担心，快请牙膏小卫士闪亮登场！", 0, "2025-05-22T05:00:00", "小牙仙", true, 1, 0},
	{18000216, 1, "友谊纽带", 0, 4, "友谊是一片温柔的土壤，洒下名为“真诚”的养料，便能在其中孕育出情谊之藤。", 0, "2025-06-07T05:00:00", "奥比生活", true, 1, 0},
	{18000217, 1, "青春墨彩", 0, 4, "叮！检测到野生灵感出没，是否用羽毛笔捕捉？", 0, "2025-06-07T05:00:00", "友晴天主题图鉴", true, 1, 0},
	{18000218, 1, "公主日记", 0, 4, "每时每刻都陪伴着你的手帐本，把你的点点滴滴都装在心里。", 0, "2025-06-07T05:00:00", "友晴天熊熊宝", true, 1, 0},
	{18000219, 1, "手帐之爱", 0, 3, "嘘……听见花开的声音了吗？那是你对手帐的热爱在悄悄发芽哦！", 0, "2025-06-07T05:00:00", "每日记录", true, 1, 0},
	{18000220, 1, "雨中旋律", 0, 4, "一把伞，和那轻快的舞步，惊艳了那场夜雨。", 0, "2025-07-03T05:00:00", "奥比生活", true, 1, 0},
	{18000221, 1, "心灵火焰", 0, 4, "当灵魂吻上火焰的瞬间，无数灵感在天空绽放出美丽的烟花。", 0, "2025-07-03T05:00:00", "熊次元主题图鉴", true, 1, 0},
	{18000222, 1, "奶噗噗派对", 0, 4, "跟奶噗噗一起参加周年派对吧，这一次，势必要萌力全开！", 0, "2025-07-03T05:00:00", "熊次元小熊钱罐", true, 1, 0},
	{18000223, 1, "小茄头像框", 0, 3, "叮铃铃~铃兰精灵小茄正带着气球赶来庆贺周年啦！", 0, "2025-07-03T05:00:00", "跨越次元", true, 1, 0},
	{18000224, 1, "仲夏花露", 0, 4, "你对我而言，就是仲夏夜里，那朵独一无二的牵牛花。", 0, "2025-07-17T05:00:00", "夏花萌粒购", true, 1, 0},
	{18000225, 1, "纯爱礼赞", 0, 4, "挥舞翅膀，乘风而起，将这份爱意传递到宇宙的每个角落。", 0, "2025-07-12T05:00:00", "行星记", true, 1, 0},
	{18000226, 1, "恋爱魔法使", 0, 4, "喵~我好像闻到甜甜的恋爱气息了，但还有些青涩，那就让我去助他们一臂之力吧！", 0, "2025-07-24T05:00:00", "优米鹿", false, 1, 0},
	{18000227, 1, "彩虹心愿", 0, 4, "看到彩虹的幸运小熊快快向它许愿，彩虹会一一帮你实现。", 0, "2025-07-03T05:00:00", "首充", false, 1, 0},
	{18000228, 1, "破次元猫咪", 0, 4, "大猫咪载着小奥比，一路奔跑，穿越次元啦！", 0, "2025-07-03T05:00:00", "周年限定累充", true, 2, 0},
}

local t_head_windows = {
	[18000001] = dataList[1],
	[18000002] = dataList[2],
	[18000003] = dataList[3],
	[18000004] = dataList[4],
	[18000005] = dataList[5],
	[18000006] = dataList[6],
	[18000007] = dataList[7],
	[18000008] = dataList[8],
	[18000009] = dataList[9],
	[18000010] = dataList[10],
	[18000011] = dataList[11],
	[18000012] = dataList[12],
	[18000013] = dataList[13],
	[18000014] = dataList[14],
	[18000015] = dataList[15],
	[18000016] = dataList[16],
	[18000017] = dataList[17],
	[18000018] = dataList[18],
	[18000019] = dataList[19],
	[18000020] = dataList[20],
	[18000021] = dataList[21],
	[18000022] = dataList[22],
	[18000023] = dataList[23],
	[18000024] = dataList[24],
	[18000025] = dataList[25],
	[18000026] = dataList[26],
	[18000027] = dataList[27],
	[18000028] = dataList[28],
	[18000029] = dataList[29],
	[18000030] = dataList[30],
	[18000031] = dataList[31],
	[18000032] = dataList[32],
	[18000033] = dataList[33],
	[18000034] = dataList[34],
	[18000035] = dataList[35],
	[18000036] = dataList[36],
	[18000037] = dataList[37],
	[18000038] = dataList[38],
	[18000039] = dataList[39],
	[18000040] = dataList[40],
	[18000041] = dataList[41],
	[18000042] = dataList[42],
	[18000043] = dataList[43],
	[18000044] = dataList[44],
	[18000045] = dataList[45],
	[18000046] = dataList[46],
	[18000047] = dataList[47],
	[18000048] = dataList[48],
	[18000049] = dataList[49],
	[18000050] = dataList[50],
	[18000051] = dataList[51],
	[18000052] = dataList[52],
	[18000053] = dataList[53],
	[18000054] = dataList[54],
	[18000055] = dataList[55],
	[18000056] = dataList[56],
	[18000057] = dataList[57],
	[18000058] = dataList[58],
	[18000059] = dataList[59],
	[18000060] = dataList[60],
	[18000061] = dataList[61],
	[18000062] = dataList[62],
	[18000063] = dataList[63],
	[18000064] = dataList[64],
	[18000065] = dataList[65],
	[18000066] = dataList[66],
	[18000067] = dataList[67],
	[18000068] = dataList[68],
	[18000069] = dataList[69],
	[18000070] = dataList[70],
	[18000071] = dataList[71],
	[18000072] = dataList[72],
	[18000073] = dataList[73],
	[18000074] = dataList[74],
	[18000075] = dataList[75],
	[18000076] = dataList[76],
	[18000077] = dataList[77],
	[18000078] = dataList[78],
	[18000079] = dataList[79],
	[18000080] = dataList[80],
	[18000081] = dataList[81],
	[18000082] = dataList[82],
	[18000083] = dataList[83],
	[18000084] = dataList[84],
	[18000085] = dataList[85],
	[18000086] = dataList[86],
	[18000087] = dataList[87],
	[18000088] = dataList[88],
	[18000089] = dataList[89],
	[18000090] = dataList[90],
	[18000091] = dataList[91],
	[18000092] = dataList[92],
	[18000093] = dataList[93],
	[18000094] = dataList[94],
	[18000095] = dataList[95],
	[18000096] = dataList[96],
	[18000097] = dataList[97],
	[18000098] = dataList[98],
	[18000099] = dataList[99],
	[18000100] = dataList[100],
	[18000101] = dataList[101],
	[18000102] = dataList[102],
	[18000103] = dataList[103],
	[18000104] = dataList[104],
	[18000105] = dataList[105],
	[18000106] = dataList[106],
	[18000107] = dataList[107],
	[18000108] = dataList[108],
	[18000109] = dataList[109],
	[18000110] = dataList[110],
	[18000111] = dataList[111],
	[18000112] = dataList[112],
	[18000113] = dataList[113],
	[18000114] = dataList[114],
	[18000115] = dataList[115],
	[18000116] = dataList[116],
	[18000117] = dataList[117],
	[18000118] = dataList[118],
	[18000119] = dataList[119],
	[18000120] = dataList[120],
	[18000121] = dataList[121],
	[18000122] = dataList[122],
	[18000123] = dataList[123],
	[18000124] = dataList[124],
	[18000125] = dataList[125],
	[18000126] = dataList[126],
	[18000127] = dataList[127],
	[18000128] = dataList[128],
	[18000129] = dataList[129],
	[18000130] = dataList[130],
	[18000131] = dataList[131],
	[18000132] = dataList[132],
	[18000133] = dataList[133],
	[18000134] = dataList[134],
	[18000135] = dataList[135],
	[18000136] = dataList[136],
	[18000137] = dataList[137],
	[18000138] = dataList[138],
	[18000139] = dataList[139],
	[18000140] = dataList[140],
	[18000141] = dataList[141],
	[18000142] = dataList[142],
	[18000143] = dataList[143],
	[18000144] = dataList[144],
	[18000145] = dataList[145],
	[18000146] = dataList[146],
	[18000147] = dataList[147],
	[18000148] = dataList[148],
	[18000149] = dataList[149],
	[18000150] = dataList[150],
	[18000151] = dataList[151],
	[18000152] = dataList[152],
	[18000153] = dataList[153],
	[18000154] = dataList[154],
	[18000155] = dataList[155],
	[18000156] = dataList[156],
	[18000157] = dataList[157],
	[18000158] = dataList[158],
	[18000159] = dataList[159],
	[18000160] = dataList[160],
	[18000161] = dataList[161],
	[18000162] = dataList[162],
	[18000163] = dataList[163],
	[18000164] = dataList[164],
	[18000165] = dataList[165],
	[18000166] = dataList[166],
	[18000167] = dataList[167],
	[18000168] = dataList[168],
	[18000169] = dataList[169],
	[18000170] = dataList[170],
	[18000171] = dataList[171],
	[18000172] = dataList[172],
	[18000173] = dataList[173],
	[18000174] = dataList[174],
	[18000175] = dataList[175],
	[18000176] = dataList[176],
	[18000177] = dataList[177],
	[18000178] = dataList[178],
	[18000179] = dataList[179],
	[18000180] = dataList[180],
	[18000181] = dataList[181],
	[18000182] = dataList[182],
	[18000183] = dataList[183],
	[18000184] = dataList[184],
	[18000185] = dataList[185],
	[18000186] = dataList[186],
	[18000187] = dataList[187],
	[18000188] = dataList[188],
	[18000189] = dataList[189],
	[18000190] = dataList[190],
	[18000191] = dataList[191],
	[18000192] = dataList[192],
	[18000193] = dataList[193],
	[18000194] = dataList[194],
	[18000195] = dataList[195],
	[18000196] = dataList[196],
	[18000197] = dataList[197],
	[18000198] = dataList[198],
	[18000199] = dataList[199],
	[18000200] = dataList[200],
	[18000201] = dataList[201],
	[18000202] = dataList[202],
	[18000203] = dataList[203],
	[18000204] = dataList[204],
	[18000205] = dataList[205],
	[18000206] = dataList[206],
	[18000207] = dataList[207],
	[18000208] = dataList[208],
	[18000210] = dataList[209],
	[18000211] = dataList[210],
	[18000212] = dataList[211],
	[18000213] = dataList[212],
	[18000214] = dataList[213],
	[18000215] = dataList[214],
	[18000216] = dataList[215],
	[18000217] = dataList[216],
	[18000218] = dataList[217],
	[18000219] = dataList[218],
	[18000220] = dataList[219],
	[18000221] = dataList[220],
	[18000222] = dataList[221],
	[18000223] = dataList[222],
	[18000224] = dataList[223],
	[18000225] = dataList[224],
	[18000226] = dataList[225],
	[18000227] = dataList[226],
	[18000228] = dataList[227],
}

t_head_windows.dataList = dataList
local mt
if HeadWindowsDefine then
	mt = {
		__cname =  "HeadWindowsDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or HeadWindowsDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_head_windows