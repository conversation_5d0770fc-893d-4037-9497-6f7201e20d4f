module("logic.extensions.tetris.game.view.TetrisGameView", package.seeall)
---@class TetrisGameView
local TetrisGameView = class("TetrisGameView", ViewComponent)

local operationBtnType = {
	left = 0,
	right = 1,
	down = 2,
	turn = 3
}

--- view初始化时会执行
function TetrisGameView:buildUI()
	TetrisGameView.super.buildUI(self)
	self._closeBtn = self:getBtn("btnClose")
	self._stopBtn = self:getBtn("btnStop")
	self._continueBtn = self:getBtn("continueGo/btnContinue")
	self._stopSelectGo = self:getGo("btnStop/select")
	self._stopUnselectGo = self:getGo("btnStop/unSelect")
	self._continueGo = self:getGo("continueGo")

	self._bornPoint = self:getGo("left/bornPoint")
	self._pkGo = self:getGo("pkGo")
	self._displayPoint = self:getGo("pkGo/right/display")
	self._shapeParent = self:getGo("shape")
	self._finalCoordShape = self:getGo("shape/layer0/finalCoordShape")
	self._rightMask = self:getGo("mask/right")
	self._endTip = self:getGo("endTip")
	self._passTip = self:getGo("passTip")

	for i = 1, 4 do
		local go = self:getGo("shape/layer0/speedupeffect" .. i)
		goutil.setActive(go, false)
	end

	self._playerItemGo = self:getGo("playerItem_1")
	self._playerPKItemGo1 = self:getGo("PkPlayerItems/playerItem_1")
	self._playerPKItemGo2 = self:getGo("PkPlayerItems/playerItem_2")
	self._scoreText = goutil.findChildTextComponent(self._playerItemGo, "txtGo/countGo/txtCount")
	self._scoreTextTitle = goutil.findChildTextComponent(self._playerItemGo, "txtGo/countGo/txt")

	self._playDesGo = self:getGo("flytextitem")
	self._txtAddScore = self:getText("txtAdd")
	self._txtAttack = self:getText("txtAttack")
	self._addScoreAnimation = Framework.AnimationAdapter.Get(self:getGo("txtAdd"))
	self._addScoreCanvasGroup = self:getGo("txtAdd"):GetComponent("CanvasGroup")
	self._txtAttackAnimation = self:getGo("txtAttack"):GetComponent("Animation")
	self._txtAttackCanvasGroup = self:getGo("txtAttack"):GetComponent("CanvasGroup")

	self._attackPoint1 = self:getGo("pkGo/attackPoint1")
	self._attackPoint2 = self:getGo("pkGo/attackPoint2")
	self._playerPunishPoint = self:getGo("pkGo/punishPoint1")
	local _score1CountGo = goutil.findChild(self._playerPKItemGo1, "txtGo/countGo")
	_score1CountGo:SetActive(false)
	local _score1Text = goutil.findChild(self._playerPKItemGo1, "txtGo/countGo/txtCount")
	_score1Text:SetActive(false)
	local _score1TextTitle = goutil.findChild(self._playerPKItemGo1, "txtGo/countGo/txt")
	_score1TextTitle:SetActive(false)
	self._displayPunishPoint = self:getGo("pkGo/punishPoint2")
	--预览砖块
	self._previewShapeList = {}
	for i = 1, 3 do
		local go = self:getGo("left/imgIcon_" .. i)
		local shape = TetrisGamePreviewShape.New(go, i)
		table.insert(self._previewShapeList, shape)
	end

	self._leftBtn = Framework.UIClickTrigger.Get(self:getGo("btnGo/btnLeft"))
	self._rightBtn = Framework.UIClickTrigger.Get(self:getGo("btnGo/btnRight"))
	self._downBtn = Framework.UIClickTrigger.Get(self:getGo("btnGo/btnDown"))
	self._turnBtn = Framework.UIClickTrigger.Get(self:getGo("btnGo/btnTurn"))

	self._leftBtn:SetMultiClickEnable(true)
	self._rightBtn:SetMultiClickEnable(true)
	self._downBtn:SetMultiClickEnable(true)
	self._turnBtn:SetMultiClickEnable(true)

	self._debugtxt = self:getText("debugtxt")
end

--- view初始化时会执行，在buildUI之后
function TetrisGameView:bindEvents()
	self._closeBtn:AddClickListener(self._onCloseBtnClick, self)
	self._stopBtn:AddClickListener(self._onStopBtnClick, self)
	self._continueBtn:AddClickListener(self._onContinueBtnClick, self)
	self._leftBtn:AddClickDownListener(self._onButtonDown, self, {operationBtnType.left})
	self._rightBtn:AddClickDownListener(self._onButtonDown, self, {operationBtnType.right})
	self._downBtn:AddClickDownListener(self._onButtonDown, self, {operationBtnType.down})
	self._turnBtn:AddClickDownListener(self._onButtonDown, self, {operationBtnType.turn})
	self._leftBtn:AddClickUpListener(self._onButtonUp, self, {operationBtnType.left})
	self._rightBtn:AddClickUpListener(self._onButtonUp, self, {operationBtnType.right})
	self._downBtn:AddClickUpListener(self._onButtonUp, self, {operationBtnType.down})
	TetrisGameViewModel.instance:registerData("skill1Point", self._debugTextChanged, self)
end
--- view销毁时会执行，在destroyUI之前
function TetrisGameView:unbindEvents()
	self._closeBtn:RemoveClickListener()
	self._stopBtn:RemoveClickListener()
	self._continueBtn:RemoveClickListener()
	self._leftBtn:RemoveClickDownListener()
	self._rightBtn:RemoveClickDownListener()
	self._downBtn:RemoveClickDownListener()
	self._turnBtn:RemoveClickDownListener()
	self._leftBtn:RemoveClickUpListener()
	self._rightBtn:RemoveClickUpListener()
	self._downBtn:RemoveClickUpListener()
	--self._turnBtn:RemoveClickUpListener()
	TetrisGameViewModel.instance:unregisterData("skill1Point", self._debugTextChanged, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function TetrisGameView:onEnter()
	self._controller = TetrisController.instance
	self._controller:setGameView(self)
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	goutil.setActive(self._endTip, false)
	goutil.setActive(self._passTip, false)
	goutil.setActive(self._pkGo, not isStandalone)
	goutil.setActive(self._playerPKItemGo1, not isStandalone)
	goutil.setActive(self._playerPKItemGo2, not isStandalone)
	goutil.setActive(self._pkGo, not isStandalone)
	goutil.setActive(self._playerItemGo, isStandalone)
	goutil.setActive(self._rightMask, not isStandalone)
	self._addScoreCanvasGroup.alpha = 0
	self._txtAttackCanvasGroup.alpha = 0
	if isStandalone then
		self._curScore = 0
		local needScore = self:getLevelScore()
		self._scoreText.text = "0"
		if needScore > 0 then
			self._scoreText.text = "0" .. "/" .. needScore
		else
			self._scoreText.text = "0"
		end
		self:_initPlayerItem(self._playerItemGo, UserInfo.userId, UserInfo.nickname)
		local levelId = TetrisUIModel.instance:getNowLevelId()
		if levelId then
			local levelCfg = TetrisConfig.getLevelConfigById(levelId)
			if levelCfg and levelCfg.isPlot then
				self._scoreTextTitle.text = lang("通关分数")
			else
				self._scoreTextTitle.text = lang("得分")
			end
		end
	else
		local players = GameCentreModel.instance:getAllPlayer()
		for userId, playerInfo in pairs(players) do
			local id = playerInfo.id
			local nickname = playerInfo.roleSimpleInfo.nickname
			if playerInfo.id == UserInfo.userId then
				self:_initPlayerItem(self._playerPKItemGo1, id, nickname)
			else
				self:_initPlayerItem(self._playerPKItemGo2, id, nickname)
			end
		end
	end
	local gameProcedure = self._controller.gameProcedure
	self:_gameProcedureChange(gameProcedure)
	self:_refreshPreviewShape()
	self:_updateStopBtnState()
	UpdateBeat:Add(self._update, self)

	GlobalDispatcher:addListener(TetrisNotify.curShapeTypeChange, self._curShapeTypeChange, self)
	GlobalDispatcher:addListener(TetrisNotify.gameProcedureChange, self._gameProcedureChange, self)
	GlobalDispatcher:addListener(TetrisNotify.gameScoreChange, self._gameScoreChange, self)
	GlobalDispatcher:addListener(TetrisNotify.sendDebuffFinish, self._sendDebuffFinish, self)
	GlobalDispatcher:dispatch(TetrisNotify.openViewOperation, "openViewOperation")
	GlobalDispatcher:addListener(GlobalNotify.OnApplicationPause, self._onApplicationPause, self)

	self._debugtxt.gameObject:SetActive(TetrisController.instance._isDebug)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function TetrisGameView:onExit()
	self._isClickLeft = false
	self._isClickRight = false
	self._isClickDown = false
	TaskUtil.BlockClick(false, "TetrisGameView")
	ViewMgr.instance:close("ColaHelpPanel")
	UpdateBeat:Remove(self._update, self)
	GlobalDispatcher:removeListener(TetrisNotify.curShapeTypeChange, self._curShapeTypeChange, self)
	GlobalDispatcher:removeListener(TetrisNotify.gameProcedureChange, self._gameProcedureChange, self)
	GlobalDispatcher:removeListener(TetrisNotify.gameScoreChange, self._gameScoreChange, self)
	GlobalDispatcher:removeListener(TetrisNotify.sendDebuffFinish, self._sendDebuffFinish, self)
	GlobalDispatcher:removeListener(GlobalNotify.OnApplicationPause, self._onApplicationPause, self)
end

--- view销毁时会执行
function TetrisGameView:destroyUI()
	if self._previewShapeList then
		for i = 1, #self._previewShapeList do
			self._previewShapeList[i]:onDestory()
		end
		self._previewShapeList = nil
	end
end

function TetrisGameView:_debugTextChanged(value)
	self._debugtxt.text = "skill1Point : " .. value
end

function TetrisGameView:_setPlayDesActive(isShow)
	goutil.setActive(self._playDesGo, isShow)
end

--初始化玩家信息
function TetrisGameView:_initPlayerItem(go, id, nickname)
	local headIcon = goutil.findChild(go, "mask/icon")
	local txtName = goutil.findChildTextComponent(go, "txtGo/txtName") or goutil.findChildTextComponent(go, "txtName")

	HeadPortraitHelper.instance:setHeadPortraitWithUserId(headIcon, id)
	txtName.text = nickname
end

--游戏分数变化
function TetrisGameView:_gameScoreChange(curScore, isOver)
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if isStandalone and self._scoreText then
		local needScore = self:getLevelScore()
		if needScore > 0 then
			self._scoreText.text = isOver and tostring(curScore) .. "+" or tostring(curScore) .. "/" .. needScore
		else
			self._scoreText.text = isOver and tostring(curScore) .. "+" or tostring(curScore)
		end
		self:_showAddScore(curScore - self._curScore)
		self._curScore = curScore
	end
end

function TetrisGameView:getLevelScore()
	local needScore = 0
	local levelId = TetrisUIModel.instance:getNowLevelId()
	if levelId and self._curScore then
		local levelCfg = TetrisConfig.getLevelConfigById(levelId)
		if levelCfg and levelCfg.isPlot then
			local defines = TetrisConfig.getTargetRewardByLevelId(levelId)
			if defines[1] and defines[1].needScore then
				needScore = defines[1].needScore
			end
		end
	end
	return needScore
end

--扔完debuff
function TetrisGameView:_sendDebuffFinish(attackNum, startY, endY, isDisplay)
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if not isStandalone and self._scoreText then
		self:_showAttack(attackNum, startY, endY, isDisplay)
	end
end

--显示增加的积分
function TetrisGameView:_showAddScore(addScore)
	self._txtAddScore.text = "+" .. addScore
	self._addScoreAnimation:Stop("ani_tetrisgameview_txtadd_01")
	self._addScoreAnimation:Play("ani_tetrisgameview_txtadd_01",true)
end

--显示攻击文本
function TetrisGameView:_showAttack(attackNum, startY, endY, isDisplay)
	self._txtAttack.text = "*" .. attackNum
	self._txtAttackAnimation:Play("ani_tetrisgameview_txtattack_01")
	local pos = nil
	if isDisplay then
		pos = self._attackPoint1.transform.position
	else
		pos = self._attackPoint2.transform.position
	end
	GameUtils.setPos(self._txtAttackAnimation.gameObject, pos.x, pos.y, pos.z)
end

--游戏流程变化时
function TetrisGameView:_gameProcedureChange(procedure)
	self:_setPlayDesActive(procedure == TetrisNotify.gameProcedure.prepare)
	local levelId = TetrisUIModel.instance:getNowLevelId()
	if levelId and self._curScore then
		local levelCfg = TetrisConfig.getLevelConfigById(levelId)
		if levelCfg and levelCfg.isPlot then
			local defines = TetrisConfig.getTargetRewardByLevelId(levelId)
			if defines[1] and defines[1].needScore <= self._curScore then
				goutil.setActive(self._passTip, procedure == TetrisNotify.gameProcedure.gameOver)
			else
				goutil.setActive(self._endTip, procedure == TetrisNotify.gameProcedure.gameOver)
			end
		else
			goutil.setActive(self._endTip, procedure == TetrisNotify.gameProcedure.gameOver)
		end
	else
		goutil.setActive(self._endTip, procedure == TetrisNotify.gameProcedure.gameOver)
	end
end

--正在下落砖块的类型变化时
function TetrisGameView:_curShapeTypeChange()
	self:_refreshPreviewShape()
end

--刷新预览状况
function TetrisGameView:_refreshPreviewShape()
	local typeList = TetrisModel.instance:getPreviewShapeType()
	if typeList == nil or #typeList == 0 then
		return
	end
	if self._previewShapeList then
		for i = 1, #self._previewShapeList do
			self._previewShapeList[i]:refresh(typeList[i])
		end
	end
end

function TetrisGameView:changeNextPreviewShape(type)
	self._previewShapeList[1]:refresh(type)
end

--
local Input = UnityEngine.Input
function TetrisGameView:_update()
	if self._isClickLeft or self._isClickRight or self._isClickDown then
		local curShape = self._controller:getCurShape()
		if curShape == nil then
			return
		end
		if not curShape:getIsInit() then
			return
		end
		if curShape:getIsFix() then
			return
		end

		if self._isClickLeft then
			if not curShape:getIsClickLeft() then
				curShape:btnLeft(true)
			end
		end

		if self._isClickRight then
			if not curShape:getIsClickRight() then
				curShape:btnRight(true)
			end
		end

	--if self._isClickDown then
	--if not curShape:getIsClickDown() then
	--curShape:btnDown(true)
	--end
	--end
	end
	if not Input.GetMouseButton(0) and Input.touchCount == 0 and not Input.anyKey then
		self:_onButtonUp(nil, {operationBtnType.left})
		self:_onButtonUp(nil, {operationBtnType.right})
		self:_onButtonUp(nil, {operationBtnType.down})
	end
end

-------------------- 按键 --------------------
--退出按钮
function TetrisGameView:_onCloseBtnClick()
	if not self._controller:isGameStarted() then
		return
	end

	--local selfIsDead = self._controller:isDead()
	--local isGameOver = self._controller:isGameOver()
	--if selfIsDead or isGameOver then
	--FlyTextManager.instance:showFlyText(lang("游戏已结束，请等待结算。"))
	--return
	--end
	local msg = lang("俄罗斯方块中途退出游戏提醒")
	local func = function(confirm)
		if confirm then
			self:tryExitGame()
		end
	end
	DialogHelper.showConfirmDlg(msg, func, lang("确定"), lang("取消"))
end
function TetrisGameView:tryExitGame()
	--local isGameOver = self._controller:isGameOver()
	--local selfIsDead = self._controller:isDead()
	--if selfIsDead or isGameOver then
	--FlyTextManager.instance:showFlyText(lang("游戏已结束，请等待结算。"))
	--return
	--end
	local roomType = GameCentreModel.instance:getRoomCreateType()
	GameRoomAgent.instance:sendExitGameRoomSceneRequest()
	self:close()
	self._controller:quickGame(roomType)
end

--暂停
function TetrisGameView:_onStopBtnClick()
	self._controller:setGamePause(true)
	self:_updateStopBtnState()
end
function TetrisGameView:_updateStopBtnState()
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if isStandalone then
		local pauseState = self._controller:isPausingGame()
		self._stopSelectGo:SetActive(pauseState)
		self._stopUnselectGo:SetActive(not pauseState)
		self._continueGo:SetActive(pauseState)
	else
		self._continueGo:SetActive(false)
	end
	self._stopBtn.gameObject:SetActive(isStandalone)
end

--继续
function TetrisGameView:_onContinueBtnClick()
	TaskUtil.BlockClick(true, "TetrisGameView")
	self._continueGo:SetActive(false)
	ViewMgr.instance:open("ColaHelpPanel", handler(self._continueGame, self), 3, {2.5, -233.2, 0})
end
function TetrisGameView:_continueGame()
	TaskUtil.BlockClick(false, "TetrisGameView")
	self._controller:setGamePause(false)
	self:_updateStopBtnState()
end

--游戏操作按键
--按下
function TetrisGameView:_onButtonDown(evt, params)
	local key = params[1]
	if key == operationBtnType.left then
		self._isClickLeft = true
	end
	if key == operationBtnType.right then
		self._isClickRight = true
	end
	if key == operationBtnType.down then
		self._isClickDown = true
	end

	local curShape = self._controller:getCurShape()
	if curShape == nil then
		return
	end
	if not curShape:getIsInit() then
		return
	end
	if curShape:getIsFix() then
		return
	end

	if key == operationBtnType.left then
		curShape:btnLeft(true)
	end
	if key == operationBtnType.right then
		curShape:btnRight(true)
	end
	if key == operationBtnType.down then
		curShape:btnDown(true)
	end
	if key == operationBtnType.turn then
		curShape:rotateShape()
	end
end

--抬起
function TetrisGameView:_onButtonUp(evt, params)
	local key = params[1]
	if key == operationBtnType.left then
		self._isClickLeft = false
	end
	if key == operationBtnType.right then
		self._isClickRight = false
	end
	if key == operationBtnType.down then
		self._isClickDown = false
	end
	if key == operationBtnType.turn then
	end

	local curShape = self._controller:getCurShape()
	if curShape == nil then
		return
	end
	if not curShape:getIsInit() then
		return
	end
	if curShape:getIsFix() then
		return
	end
	if key == operationBtnType.left then
		curShape:btnLeft(false)
	end
	if key == operationBtnType.right then
		curShape:btnRight(false)
	end
	if key == operationBtnType.down then
		curShape:btnDown(false)
	end
	if key == operationBtnType.turn then
	end
end

--后台
function TetrisGameView:_onApplicationPause()
	local isStandalone = TetrisModel.instance:getGameModeIsStandalone()
	if not isStandalone then
		return
	end
	self:_onStopBtnClick()
end

return TetrisGameView
