module("logic.extensions.task.controller.TaskController", package.seeall)

local TaskController = class("TaskController", BaseController)

function TaskController:ctor()
	TaskController.super:ctor(self)
	self:registerNotify(GlobalNotify.OnFunc<PERSON><PERSON>lock, self._onFuncUnlock, self)
	self:registerNotify(GlobalNotify.UserLevelUp, self._onLevelUp, self)
	self:registerNotify(GlobalNotify.OnNpcFavorabilityLevelUp, self._onLevelUp, self)
	self:registerNotify(GlobalNotify.EnterScene, self._onEnterScene, self)
	self:registerNotify(GlobalNotify.LeaveScene, self._onLeaveScene, self)
	self:registerNotify(GlobalNotify.OnTaskStoryStart, self._onTaskStoryStart, self)
	self:registerNotify(GlobalNotify.OnTaskStoryEnd, self._onTaskStoryEnd, self)
	self:registerNotify(GlobalNotify.OnShowDialogue, self._onShowDialogue, self)
	self:registerNotify(GlobalNotify.OnCloseDialogue, self._onCloseDialogue, self)
	self:registerNotify(GlobalNotify.ReloadScene, self._onSceneReload, self)
	self:registerNotify(GlobalNotify.OnServerRefresh, self._onServerRefresh, self)

	-- self._taskIdsToStart = {}
	-- self._tryingStartIds = {}
end

function TaskController:onInit()
	self:registerLocalNotify(TaskLocalNotify.OnChapterComplete, self._onChapterComplete, self)
	self._curSWMap = {} --start watcher map(key:taskId value:SWList)
	self._taskIdsToStart = {}
	self._tryingStartIds = {}
	self._startTaskFailIds = {}
end

function TaskController:onReset()
	if self._curSWMap then
		for k, v in ipairs(self._curSWMap) do
			v:dispose()
		end
		self._curSWMap = nil
	end
end

function TaskController:_onEnterScene()
	print("TaskController:_onEnterScene")
	self.inScene = true
end

function TaskController:_onLeaveScene()
	print("TaskController:_onLeaveScene")
	self.inScene = false
end

function TaskController:_onSceneReload()
	if TaskFacade.instance:isNewbieTaskFinish() then
		local brokenCmdIdList, brokenCmdTriggerTypeList = TaskCmdModel.instance:breakAll()
		if #brokenCmdIdList > 0 then
			print("由于断线重新加载场景,任务剧情中断")
		end
	end
end

function TaskController:_onServerRefresh()
    print("TaskController:_onServerRefresh()", ServerTime.formatNowServerLook("%Y/%m/%d %H:%M:%S"))
    local taskTypes = TaskTypeConfig.instance:getDailyRefreshTypes()
	for _, taskType in ipairs(taskTypes) do
		TaskStepController.instance:destoryZombieWatchersByTaskType(taskType)
	end
    TaskAgent.instance:sendTaskInfoRequest(taskTypes, handler(self._onServerRefreshSucc, self))
end

function TaskController:_onServerRefreshSucc()
	print("TaskController:_onServerRefreshSucc()", ServerTime.formatNowServerLook("%Y/%m/%d %H:%M:%S"))
	self:localNotify(TaskLocalNotify.OnServerRefreshSucc)
	settimer(1, self.tryStartTasks, self, false) --暂时打下补丁修复五点刷新后限时活动没有实时开启的问题
end

function TaskController:_onTaskStoryStart()
	self._isInStory = true
end

function TaskController:_onTaskStoryEnd()
	self._isInStory = false
end

function TaskController:isInStory()
	return self._isInStory
end

function TaskController:_onShowDialogue()
	self._isInDialogue = true
end

function TaskController:_onCloseDialogue()
	self._isInDialogue = false
end

function TaskController:isInDialogue()
	return self._isInDialogue
end

function TaskController:isDisconnected()
	return ReConnectionMgr.instance:isReconnecting()
end

---完成步骤(发送命令前)
function TaskController:completeStep(stepId, cmdCode, records)
	self._started = nil
	if TaskStepConfig.instance:getNextStepCOByStepId(stepId) then
		TaskAgent.instance:sendFinishStepRequest(stepId, cmdCode, records)
	else
		self:finishStep(stepId)
	end
end
---完成步骤(收到服务器成功返回后)
function TaskController:finishStep(lastStepId, taskInfo, extraInfos)
	local nextStepId = taskInfo and taskInfo.currentStep or 0
	print("向服务器请求任务步骤完成成功", lastStepId, "=>", nextStepId)
	if extraInfos and #extraInfos > 0 then
		TaskModel.instance:addExtraInfo(extraInfos)
	end
	TaskStepController.instance:finishStep(lastStepId)

	if nextStepId > 0 then
		TaskController.instance:gotoStepByServerObj(taskInfo)
	end
	TaskController.instance:sendOrDelaySendNotify()
	GlobalDispatcher:dispatch(GlobalNotify.OnTaskFinishStepReply, lastStepId)
end

function TaskController:gotoStepByServerObj(serverTaskObj)
	self:gotoStep(serverTaskObj.currentStep, serverTaskObj.finishedTargetCounts, serverTaskObj.startTime)
end

function TaskController:gotoStep(stepId, finishTargetCounts, startTime)
	local nextStepCO = TaskStepConfig.instance:getStepCO(stepId)
	if nextStepCO then
		if enableLog then
			print("to next step: " .. stepId)
		end
		TaskModel.instance:setTaskDoing(stepId, finishTargetCounts, startTime)
	end
end

function TaskController:getTaskRewards(taskId, extraInfos)
	self._tmpTaskId = taskId
	TaskAgent.instance:sendReceiveTaskAwardsRequest(taskId, extraInfos, handler(self.onGetTaskRewardsHandle, self))
end

function TaskController:onGetTaskRewardsHandle(status, msg)
	if status ~= 0 then
		printError("领取奖励失败", status)
		return
	end
	self:handleTaskFinish(self._tmpTaskId, msg.changeSetId)
end

function TaskController:handleTaskFinish(taskId, changeSetId)
	local lastStepId = TaskModel.instance:getMO(taskId).curStepId
	local finishTask = TaskConfig.instance:getTaskCO(taskId)

	TaskModel.instance:setTaskCompleted(taskId)
	self:sendOrDelaySendNotify()

	if enableLog then
		print("finish task: #" .. taskId .. "-" .. finishTask.name)
	end
	local title = taskId == TaskSetting.LastNewbieTaskId and "gift" or nil
	TaskFacade.instance:showTaskFinishPanelByCI(
		finishTask.name,
		finishTask.taskFinishNPC,
		finishTask.taskFinishNPCAni,
		finishTask.taskFinishWords,
		changeSetId,
		function()
			FuncUnlockFacade.instance:checkShowUnlockEffect(
				function()
					TaskController.instance:registerLocalNotify(TaskLocalNotify.OnAllCmdCompleted, self._onAllCmdCompleted, self)
					local isRunCmd = TaskCmdFacade.instance:tryStartCmd(lastStepId, TaskCmdTriggerType.AfterGetReward)
					print("GlobalNotify.OnTaskFinish", taskId, ServerTime.formatNowServerLook("%Y/%m/%d %H:%M:%S"))
					TaskStepController.instance:destroyWatchersByTaskId(taskId)
					GlobalDispatcher:dispatch(GlobalNotify.OnTaskFinish, taskId)
					-- if taskId == TaskSetting.LastNewbieTaskId then
					-- 	GlobalDispatcher:addListener(GlobalNotify.OnFinishActivityShow, self._onFinishActivityShow, self)
					-- 	return
					-- end
					local taskCO = TaskConfig.instance:getTaskCO(taskId)
					if taskCO.type == GameEnum.TaskTypeEnum.NPC then
						local chapterCO, npcId, taskIndex = TaskChapterConfig.instance:getNPCChapterCOByTaskId(taskId)
						local chapterTaskIdList = chapterCO.taskIds
						print(string.format("NPCTaskFinish npcId:%s chapterId:%s taskIndex:%s taskCount:%s", npcId, chapterCO.id, taskIndex, #chapterTaskIdList))
						if taskIndex == #chapterTaskIdList then
							DialogHelper.showConfirmDlg(lang("<color='#e38534'>《{1}》</color>已全部完成, 是否前往领取奖励?", chapterCO.title), function(isOK)
								if isOK then
									ViewMgr.instance:close("TaskObjectivePanel")
									ViewMgr.instance:open("FavorabilityTaskView", npcId, {chapterCO.id})
								end
							end)
						end
					end
					if not isRunCmd then
						self:tryStartTasks()
					end
				end,
				self
			)
		end,
		self,
		title
	)
end

function TaskController:getChapterRewards(chapterId)
	TaskAgent.instance:sendReceiveTaskChapterAwardsRequest(
		chapterId,
		handler(
			function(obj, ci)
				TaskChapterModel.instance:setComplete(chapterId)
				local callback = function()
					TaskController.instance:localNotify(TaskLocalNotify.OnGetChapterReward, chapterId)
				end
				local chapterCO = TaskChapterConfig.instance:getChapterCO(chapterId)
				if chapterCO.finishNPC and chapterCO.finishNPC > 0 then
					TaskFacade.instance:showTaskFinishPanelByCI(
						chapterCO.name,
						chapterCO.finishNPC,
						chapterCO.finishNPCAni,
						chapterCO.finishWords,
						ci,
						callback,
						nil,
						"targetComplete"
					)
				else
					DialogHelper.showRewardsDlgByCI(ci, nil, callback)
				end
			end,
			self
		)
	)
end

function TaskController:_onAllCmdCompleted()
	TaskController.instance:unregisterLocalNotify(TaskLocalNotify.OnAllCmdCompleted, self._onAllCmdCompleted, self)
	self:tryStartTasks()
end

-- function TaskController:_onFinishActivityShow()
-- 	print("TaskController:_onFinishActivityShow()")
-- 	GlobalDispatcher:removeListener(GlobalNotify.OnFinishActivityShow, self._onFinishActivityShow, self)
-- 	self:tryStartTasks()
-- end

function TaskController:_onLevelUp(level)
	self:tryStartTasks()
end

function TaskController:_onChapterComplete()
	self:tryStartTasks()
end

function TaskController:_onFuncUnlock()
	self:tryStartTasks()
end

function TaskController:startTask(taskId)
	print("TaskController:startTask", taskId, ServerTime.formatNowServerLook("%Y/%m/%d %H:%M:%S"))
	self:disposeStartWatcher(taskId)
	TaskAgent.instance:sendStartTaskRequest({taskId})
end

function TaskController:_genStartWatcher(type)
	print("TaskController:_genStartWatcher", type)
	local cmdIns
	local cfg = TaskStartConfig.instance:getConfig(type)
	if cfg == nil then
		printError("未知的任务开启类型:" .. tostring(type))
	end
	local cls = usingnow("logic.extensions.task.controller.taskstartwatcher.TaskStartWatcher_" .. cfg.cls)
	cmdIns = cls.New()
	return cmdIns, cfg
end

function TaskController:getStartWatcher(taskId)
	return self._curSWMap[taskId]
end

function TaskController:disposeStartWatcher(taskId)
	print("TaskController:disposeStartWatcher", taskId)
	if self._curSWMap[taskId] then
		self._curSWMap[taskId]:dispose()
		self._curSWMap[taskId] = nil
	end
end

function TaskController:tryStartTasks()
	if TaskModel.instance.isTaskLocal then
		return
	end
	if enableLog then
		print("TaskController:tryStartTasks()", ServerTime.formatNowServerLook("%Y/%m/%d %H:%M:%S"), debug.traceback())
	end
	if #self._tryingStartIds > 0 then
		print("starting...")
		return
	end
	local list = TaskModel.instance:findCanStartTasks()
	print(table.concat(list, ","))
	local taskCO
	local sw
	for i, taskId in ipairs(list) do
		taskCO = TaskConfig.instance:getTaskCO(taskId)
		if taskCO.startParam then
			if self._curSWMap[taskId] == nil then
				sw = self:_genStartWatcher(taskCO.startParam.type)
				sw:initData(taskId)
				self._curSWMap[taskId] = sw
			end
		else
			if not table.indexof(self._taskIdsToStart, taskId) then
				if not table.indexof(self._startTaskFailIds, taskId) then
					table.insert(self._taskIdsToStart, taskId)
				end
			end
		end
	end
	if #self._taskIdsToStart > 0 then
		self._tryingStartIds = self._taskIdsToStart
		self._taskIdsToStart = {}
		TaskAgent.instance:sendStartTaskRequest(self._tryingStartIds)
	else
		TaskController.instance:localNotify(TaskLocalNotify.OnTryStartTasksSucc)
	end
end

function TaskController:onStartTaskReply(status, msg)
	if status ~= 0 then
		table.insert(self._startTaskFailIds, self._tryingStartIds[1]) ---防止一直失败一直向服务器发开启
		table.removebyvalue(self._tryingStartIds, self._tryingStartIds[1]) ---防止卡住其他任务的开启
		TaskController.instance:localNotify(TaskLocalNotify.OnTryStartTasksSucc) ---防止策划配置错误导致进岛卡住
		printError(string.format("向服务器请求开始任务失败:%s %s, 任务Id:%s", status, DialogHelper.getErrorMsg(status), table.concat(self._tryingStartIds, ",")))
		for _, taskId in ipairs(self._tryingStartIds) do
			printError(string.format("taskId:%s, taskName:%s", taskId, TaskConfig.instance:getTaskCO(taskId).name))
		end
		return
	end
	local taskServerObjList = msg.task
	local taskIdList = {}
	for _, taskServerObj in ipairs(taskServerObjList) do
		if taskServerObj.result == 0 then
			table.insert(taskIdList, taskServerObj.id)
			self:onStartTaskSucc(taskServerObj)
			print("向服务器请求开始任务成功 taskIds:", table.concat(taskIdList, ","), ServerTime.formatNowServerLook("%Y/%m/%d %H:%M:%S"))
		else
			printError(
				string.format(
					"向服务器请求开始任务失败 result:%s errorMsg:%s taskId:%s taskName:%s time:%s ",
					taskServerObj.result,
					DialogHelper.getErrorMsg(taskServerObj.result),
					taskServerObj.id,
					TaskConfig.instance:getTaskCO(taskServerObj.id).name,
					ServerTime.formatNow("%Y/%m/%d %H:%M:%S")
				)
			)
			table.removebyvalue(self._tryingStartIds, taskServerObj.id) ---防止卡住其他任务的开启
			table.insert(self._startTaskFailIds, taskServerObj.id) ---防止一直失败一直向服务器发开启
			TaskController.instance:localNotify(TaskLocalNotify.OnTryStartTasksSucc) ---防止策划配置错误导致进岛卡住
		end
	end
end

function TaskController:onStartTaskSucc(taskServerObj)
	local step = taskServerObj.currentStep
	local stepCO = TaskStepConfig.instance:getStepCO(step)
	local taskId = stepCO.taskId
	local taskCO = TaskConfig.instance:getTaskCO(taskId)
	table.removebyvalue(self._tryingStartIds, taskId)
	-- if table.indexof(TaskSetting.TaskTypes4TaskTrack, taskCO.type) then
	-- 	TaskModel.instance:setNewState(taskId, true)
	-- end
	TaskController.instance:gotoStepByServerObj(taskServerObj)
	print("OnTaskStart", taskId)
	TaskController.instance:localNotify(TaskLocalNotify.OnTaskStarted, taskCO)
	GlobalDispatcher:dispatch(GlobalNotify.OnTaskStart, taskId)
	if #self._tryingStartIds == 0 then
		-- TaskController.instance:localNotify(TaskLocalNotify.OnTryStartTasksSucc)
		self:tryStartTasks()
	end
	TaskController.instance:sendOrDelaySendNotify()
end
-- function TaskController:giveupTask( taskCode )
-- 	TaskAgent.instance:sendGiveUpTaskRequest(taskCode)
-- end
function TaskController:sendOrDelaySendNotify()
	-- if not ViewMgr.instance:isOpen(ViewName.TaskFinish) then
	-- 	if ViewMgr.instance:isOpen(ViewName.StoryBlackWait) then
	-- 		GlobalDispatcher:dispatch(GlobalNotify.OnDelaySendTaskUpdate)
	-- 		if enableWarnLog then printWarn("TaskControllerOnDelaySendTaskUpdate") end
	-- 	else
	-- 		if enableWarnLog then printWarn("TaskControllerOnTaskUpdate") end
	GlobalDispatcher:dispatch(GlobalNotify.OnTaskUpdate)
	-- 	end
	-- end
end

function TaskController:endTask(taskId)
	TaskModel.instance:setTaskUnOpen(taskId)
	TaskController.instance:sendOrDelaySendNotify()
end

function TaskController:printAllTaskInfo()
	print(".................任务状态信息................", ServerTime.formatNowServerLook("%Y/%m/%d %H:%M:%S"))
	print("是否已完成新手任务:", tostring(TaskFacade.instance:isNewbieTaskFinish()))
	print("未开启的任务:")
	self:_printTaskList(TaskModel.instance:getUnOpenTaskIds())
	print("进行中的任务:")
	self:_printDoingTaskInfo(TaskModel.instance:getDoingTaskIds())
	print("已完成的任务:")
	self:_printTaskList(TaskModel.instance:getCompletedTaskIds())
	local taskIdList = {}
	for k, v in pairs(self._curSWMap) do
		table.insert(taskIdList, k)
	end
	print("可接取的任务:", table.concat(taskIdList, ","))
	print("............................................")
end
function TaskController:_printTaskList(taskIdList)
	print(table.concat(taskIdList, ", "))
end
function TaskController:_printDoingTaskInfo(taskIdList)
	for _, taskId in ipairs(taskIdList) do
		local stepId = TaskModel.instance:getMO(taskId).curStepId
		local progressTxt = ""
		local wl = TaskStepController.instance:getWatcherListByStepId(stepId)
		if wl then
			for __, w in ipairs(TaskStepController.instance:getWatcherListByStepId(stepId)) do
				progressTxt = progressTxt .. w:getProgressDesc() .. " "
			end
		end
		print(string.format("%s(%s)[%s]", taskId, stepId, progressTxt))
	end
end
function TaskController:print(...)
	if TaskModel.instance.isTestMode then
		print(...)
	end
end
TaskController.instance = TaskController.New()
return TaskController
