module("logic.scene.common.action.JumpAction",package.seeall)
local JumpAction = class("JumpAction", SceneActionBase)
function JumpAction:onStart()
	local targetPos = self.params.targetPos
	local scene = SceneManager.instance:getCurScene()
	self.userPlayer = scene:getUserPlayer()
	local px, py = self.userPlayer:getPos()
	local canJump = SceneManager.instance:getCurScene().mapMgr:isWalkable(targetPos.posX, targetPos.posY)
	if canJump or self.params.forceJump then
		SceneManager.instance:getCurScene():getUserPlayer():addListener(UnitNotify.Arrive, self.onArrive, self)	
		print("sdfsdf", targetPos.posZ)
		self.userPlayer:specialMove(targetPos.posX, targetPos.posY, self.params.type, targetPos.posZ)
		SceneAgent.instance:sendMoveRequest({targetPos}, self.params.type)
		-- self.isWorking = true
	else
		self:finish(false)
	end
end

function JumpAction:onArrive()
	-- self.isWorking = false
	SceneManager.instance:getCurScene():getUserPlayer():removeListener(UnitNotify.Arrive, self.onArrive, self)	
	self:finish(true)
end

function JumpAction:getType()
	return SceneActionType.Jump
end
return JumpAction