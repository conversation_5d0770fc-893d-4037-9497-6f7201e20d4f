module("logic.extensions.activity.toyhouse.AutoGetActInfo",package.seeall)

local AutoGetActInfo = class("AutoGetActInfo")

AutoGetActInfo.middleOriginTaskId = 1000675 -- 给个默认值

function AutoGetActInfo.refreshActivityId()
	local allActCfg = ActivityConfig.getAllActivityConfig()
    local hasFindSignin_middle, hasFindShop_middle, hasFindStory_middle
    -- local hasFindMap_big
    for i=#allActCfg,1,-1 do
        if not hasFindSignin_middle and allActCfg[i].activityDefineId == 138 then -- 中主题签到
            DreamBoxFacade.signinActivityId = allActCfg[i].activityId
            hasFindSignin_middle = true
        end
        if not hasFindShop_middle and allActCfg[i].activityDefineId == 203 then -- 中主题商店
            if allActCfg[i].params and allActCfg[i].params.middle then
                DreamBoxFacade.shopActivityId_1 = allActCfg[i].activityId
                hasFindShop_middle = true
            end
        end
        if not hasFindStory_middle and allActCfg[i].activityDefineId == 174 then -- 中主题剧情
            if allActCfg[i].params and allActCfg[i].params.middle then
                DreamBoxFacade.storyActivityId_1 = allActCfg[i].activityId
                if allActCfg[i].params.taskId then
                    AutoGetActInfo.middleOriginTaskId = allActCfg[i].params.taskId
                else
                    printError("中主题剧情没配任务id，速速找策划在活动配置表配！")
                end
                hasFindStory_middle = true
            end
        end
        -- 全部找完就退出
        if hasFindSignin_middle and hasFindShop_middle and hasFindStory_middle then
            break
        end
    end
end

-- 获取中主题活动剧情的起始id
function AutoGetActInfo.getMiddleActStoryId()
    return AutoGetActInfo.middleOriginTaskId
end



-- 外部获取中主题签到的配置信息
function AutoGetActInfo.getMiddleSigninActId()
    return DreamBoxFacade.signinActivityId
end
function AutoGetActInfo.getMiddleSigninActViewName()
    return "PrayDaySigninView"
end
function AutoGetActInfo.getMiddleSigninActRedName()
    return "DreamBoxSign"
end
function AutoGetActInfo.getMiddleSigninOpenViewHandler()
    return DreamBoxFacade.openSignin
end

-- 外部获取中主题商店的配置信息
function AutoGetActInfo.getMiddleShopActId()
    return DreamBoxFacade.shopActivityId_1
end
function AutoGetActInfo.getMiddleShopActViewName()
    return "LanternShop"
end
function AutoGetActInfo.getMiddleShopActRedName()
    return "Shop"..tostring(DreamBoxFacade.shopActivityId_1).."Tab"
end
function AutoGetActInfo.getMiddleShopOpenViewHandler()
    return DreamBoxFacade.openShop_1
end

-- 外部获取中主题剧情的配置信息
function AutoGetActInfo.getMiddleStoryActId()
    return DreamBoxFacade.storyActivityId_1
end
function AutoGetActInfo.getMiddleStoryActViewName()
    return "DreamBoxStoryView_1"
end
function AutoGetActInfo.getMiddleStoryActRedName()
    return "DreamBoxStory_1"
end
function AutoGetActInfo.getMiddleStoryOpenViewHandler()
    return DreamBoxFacade.openDreamBoxStory_1
end

return AutoGetActInfo