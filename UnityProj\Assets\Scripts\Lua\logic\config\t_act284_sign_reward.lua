-- {excel:284周末与周一福利.xlsx, sheetName:export_act284周末签到奖励表}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act284_sign_reward", package.seeall)

local title = {activityId=1,weekday=2,accumualateWight=3,rewards=4,rewards2=5}

local dataList = {
	{1550, 5, 1, {{count=1,id=16000074},{count=1,id=16000019},{count=1,id=16000347}}, {{count=1,id=16000074},{count=1,id=16000019},{count=1,id=16000347}}},
	{1550, 6, 1, {{count=1,id=16000075},{count=1,id=16000020},{count=5,id=16000038}}, {{count=1,id=16000075},{count=1,id=16000787},{count=5,id=16000038}}},
	{1550, 7, 1, {{count=1,id=16000076},{count=1,id=16000018},{count=5,id=16000034}}, {{count=1,id=16000076},{count=1,id=16000788},{count=5,id=16000034}}},
	{1550, 1, 0, {{count=1,id=16000006},{count=1,id=16000054},{count=1,id=16000347}}, {{count=1,id=16000006},{count=1,id=16000054},{count=1,id=16000347}}},
}

local t_act284_sign_reward = {
	[1550] = {
		[5] = dataList[1],
		[6] = dataList[2],
		[7] = dataList[3],
		[1] = dataList[4],
	},
}

t_act284_sign_reward.dataList = dataList
local mt
if Activity284SignRewardDefine then
	mt = {
		__cname =  "Activity284SignRewardDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Activity284SignRewardDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act284_sign_reward