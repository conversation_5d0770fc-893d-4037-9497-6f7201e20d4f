module("logic.extensions.sharestreet.view.ShareFurnitureTab",package.seeall)

local ShareFurnitureTab = class("ShareFurnitureTab", ViewComponent)



local blockKey = "ShareFurnitureTabBlock"
local HeighterY = 388.41
local LowerY = 222.24
local HideInHeigherMode = {
	"horRight/btnSave", "horRight/btnCancel"
}

function ShareFurnitureTab:buildUI()
    NotifyDispatcher.extend(self)
	self._tgCon1 = self:getGo("leftGo/secTab/Viewport/Content")
	self._tg1 = ToggleGroup.New(handler(self._onSelectType1, self), false, true)
	self._tg2 = ToggleGroup.New(handler(self._onSelectType2, self))
	self._viewPortWidth = goutil.getWidth(self:getGo("leftGo/secTab/Viewport").transform)
	self._viewport = self:getGo("leftGo/secTab/Viewport/Content")
	self._scrollRect = self:getGo("leftGo/secTab"):GetComponent(typeof(UnityEngine.UI.ScrollRect))
end

function ShareFurnitureTab:onExit()
	UpdateBeat:Remove(self._moveTg1, self)
	UpdateBeat:Remove(self._moveTg2, self)
	self._selectType1 = nil
	self:_clearTG(self._tg1)
	self:_clearTG(self._tg2)
end

function ShareFurnitureTab:onEnter()
    self._type0 = FurnitureConfig.getPartDefine(0)
    self._types1 = self:_createToggleView(self._tg1, self._tgCon1, 0, 1, true)
	self._selectTg1View = nil
	self._tg1:clickViews(true, 1)

end

function ShareFurnitureTab:backToMain()
	self:selectType({1, 1})
end

function ShareFurnitureTab:selectType(selectedIndexs)
	self._selectedIndexs = selectedIndexs
end

function ShareFurnitureTab:_findTypeIndex(target, list)
	local hasAll = target.parentId ~= - 1 and FurnitureConfig.getPartDefine(target.parentId).parentId == - 1 and target.parentId ~= 16
	for i, part in ipairs(list) do
		if part.id == target.id then
			return i +(hasAll and 1 or 0)
		end
	end
	return 1
end

function ShareFurnitureTab:getFurTypes(parentId)
	local children = FurnitureConfig.getPartChildren(parentId)
	local list = {}
	for i = 1, #children do
		if children[i].canShare then
			table.insert(list, children[i])
		end
	end
	return list
end

function ShareFurnitureTab:_createToggleView(toggle, container, parentId, tgType, needAll)
	self:_clearTG(toggle)
	if needAll then
		local view = self:_createView(container, 1, 0, lang("全部"))
		toggle:addView(view)
	end
	local parts = self:getFurTypes(parentId)
    print("parts:", #parts)
	for i, part in ipairs(parts) do
        print("part:", part.id, part.name, part.tgType)
		local view = self:_createView(container, tgType, part.id, part.name, part.redKey)
		toggle:addView(view)
	end
	return parts
end

function ShareFurnitureTab:_createView(container, tgType, urlId, name, redKeys)
	local view = self:getResInstance(ShareStreetAddFurnitureViewPresentor["BtnFilter" .. tgType])
	goutil.setActive(view, true)
	goutil.addChildToParent(view, container)
	goutil.setActive(goutil.findChild(view, "redPoint"), false)
	if redKeys ~= nil and goutil.findChild(view, "redPoint") ~= nil then
		RedPointController.instance:registerRedPoint(goutil.findChild(view, "redPoint"), redKeys)
	end
	self:_setIcon(view, "icon", urlId)
	self:_setIcon(view, "imgSelect/icon", urlId)
	self:_setIcon(view, "imgUnSelect/icon", urlId)
	self:_setText(view, "imgSelect/txt", name)
	self:_setText(view, "imgUnSelect/txt", name)
	return view
end

function ShareFurnitureTab:_onSelectType1(index, isSelected)
	UpdateBeat:Remove(self._moveTg2, self)
	if index ~= 1 then
		if self._selectTg1View then
			goutil.findChild(self._selectTg1View, "btnGo"):SetActive(false)
			-- goutil.findChild(self._selectTg1View, "btnGo1"):SetActive(false)
			goutil.findChild(self._selectTg1View, "imgBg"):SetActive(false)
		end
		self._selectTg1View = self._tg1:getViews() [index]
		-- self._tgCtrlCon2 = goutil.findChild(self._selectTg1View, "btnGo1")
		-- local container = goutil.findChild(self._selectTg1View, "secondtab")
		self._tgCtrlCon2 = goutil.findChild(self._selectTg1View, "btnGo")
		-- local container = self._tgCtrlCon2
		local selectIndex = index - 1 
		if self._types1[selectIndex] ~= self._selectType1 or not self._showSubType then
			self._showSubType = true
			self._selectType1 = self._types1[selectIndex]
			self._types2 = self:_createToggleView(self._tg2, self._tgCtrlCon2, self._selectType1.id, 2)
			self._targetWidthTg2 = #self._types2 * 70 +(#self._types2 - 1) * 10 + 25
			goutil.setWidth(self._selectTg1View.transform, self._targetWidthTg2)
			goutil.setWidth(self._tgCtrlCon2.transform, 0)
			if #self._types2 > 0 then
				UpdateBeat:Add(self._moveTg2, self)
			end
		else
			self._showSubType = false
			self._types2 = nil
		end
		local active = self._showSubType and self._types2 ~= nil and #self._types2 > 0
		self._tgCtrlCon2:SetActive(active)
		goutil.findChild(self._selectTg1View, "imgBg"):SetActive(active)
	else
		self:_clearTG(self._tg2)
		self._showSubType = false
		self._types2 = nil
		self._selectType1 = self._type0
		if self._selectTg1View then
			goutil.findChild(self._selectTg1View, "btnGo"):SetActive(false)
			goutil.findChild(self._selectTg1View, "imgBg"):SetActive(false)
		end
	end
	if self._selectedIndexs and self._selectedIndexs[3] then
		self._tg2:clickViews(true, self._selectedIndexs[3])
	else
		if self._showSubType and self._selectType1.id == 42 then
			self._tg2:clickViews(true, 1)
		else
			self._tg2:setIndex(0)
			self:_notifySelect()
		end
		-- self._tg2:clickViews(true, 1)
	end
	if self._showSubType then
		settimer(0, function()
			local focusView = self._tg1:getViews() [self._tg1:getIndex()]
			self._targetFocusX = GameUtils.getLocalPos(focusView).x
			UpdateBeat:Add(self._moveTg1, self)
		end, nil, false)
	else
		UpdateBeat:Remove(self._moveTg1, self)
	end
end

function ShareFurnitureTab:_moveTg1()
	if self._scrollRect == nil then return end
	local current = self._scrollRect.horizontalNormalizedPosition
	local target = math.max(0, math.min(1, self._targetFocusX /(goutil.getWidth(self._viewport.transform) - self._viewPortWidth)))
	local isEnd = math.abs(current - target) < 0.001
	if isEnd then
		self._scrollRect.horizontalNormalizedPosition = target
		UpdateBeat:Remove(self._moveTg1, self)
	else
		self._scrollRect.horizontalNormalizedPosition = current +(target - current) / 3
	end
end
function ShareFurnitureTab:_moveTg2()
	if self._tgCtrlCon2 == nil then return end
	local current = goutil.getWidth(self._tgCtrlCon2.transform)
	local isEnd = self._targetWidthTg2 - current < 0.01
	if isEnd then
		goutil.setWidth(self._tgCtrlCon2.transform, self._targetWidthTg2)
		UpdateBeat:Remove(self._moveTg2, self)
	else
		goutil.setWidth(self._tgCtrlCon2.transform, current +(self._targetWidthTg2 - current) / 3)
	end
end
function ShareFurnitureTab:_onSelectType2(index, isSelected)
	if not isSelected or index == nil then return end
	self:_notifySelect()
end

function ShareFurnitureTab:_notifySelect()
	local selectedType
	if self._types2 and self._tg2:getIndex() ~= 0 then
		selectedType = self._types2[self._tg2:getIndex()]
	elseif self._types1 and self._tg1:getIndex() ~= 1 then
		local selectIndex = self._tg1:getIndex() - 1
		selectedType = self._types1[selectIndex]
    else
        selectedType = self._type0
    end
	print("_notifySelect :", selectedType.id, selectedType.name)
	self._selectedIndexs = nil

	self._lastSelectedType = selectedType
    self:dispatch(ItemNotify.ChangeTab, selectedType)
end

function ShareFurnitureTab:_clearTG(tgGroup)
	for _, view in ipairs(tgGroup:getViews()) do
		if goutil.findChild(view, "redPoint") ~= nil then
			RedPointController.instance:unregisterRedPoint(goutil.findChild(view, "redPoint"))
		end
		goutil.destroy(view)
	end
	tgGroup:clear()
end

function ShareFurnitureTab:_setIcon(view, path, urlId)
	local icon = goutil.findChild(view, path)
	if icon then
		IconLoader.setAtlasToImg(icon, "atlas/furnituretype.spriteatlas", urlId)
	end
end

function ShareFurnitureTab:_setText(view, path, desc)
	if goutil.findChild(view, path) then
		goutil.findChildTextComponent(view, path).text = desc
	end
end


return ShareFurnitureTab