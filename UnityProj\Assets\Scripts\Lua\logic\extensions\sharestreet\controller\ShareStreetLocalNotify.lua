module("logic.extensions.sharestreet.controller.ShareStreetLocalNotify",package.seeall)

local ShareStreetLocalNotify = class("ShareStreetLocalNotify")

ShareStreetLocalNotify.OnUnlockArea = "OnUnlockArea" -- 解锁区域 参数:upgradeAreaId
ShareStreetLocalNotify.OnInviteFriend = "OnInviteFriend" -- 向好友发出邀请 参数:playerId
ShareStreetLocalNotify.OnJoinOtherStreetSucc = "OnJoinOtherStreetSucc" -- 加入别人的街区成功 参数:ownerId
ShareStreetLocalNotify.OnCancelJoinApplySucc = "OnCancelJoinApplySucc" -- 取消加入别人街区的申请 参数:ownerId
ShareStreetLocalNotify.OnAcceptJoinRequestSucc = "OnAcceptJoinRequestSucc" -- 同意别人的加入申请 参数:playerId, askList(repeated StreetSimpleInfoNO)
ShareStreetLocalNotify.OnRefuseJoinRequestSucc = "OnRefuseJoinRequestSucc" -- 拒绝别人的加入申请 参数:playerId, askList(repeated StreetSimpleInfoNO)
ShareStreetLocalNotify.OnChangeSceneItem = "OnChangeSceneItem" -- 换皮肤、场景物
ShareStreetLocalNotify.OnSaveAndDeleteFurniture = "OnSaveAndDeleteFurniture" -- 保存的共享家具被他人占用
ShareStreetLocalNotify.WalkToOtherShareStreet = "WalkToOtherShareStreet" -- 导航到别人的街区出生点
ShareStreetLocalNotify.OnFurnitureInfoChangePush = "OnFurnitureInfoChangePush" -- 后端推送家具变更
ShareStreetLocalNotify.OnLoadShareStreetBg = "OnLoadShareStreetBg" -- 下载背景成功
ShareStreetLocalNotify.OnChangeNameSucc = "OnChangeNameSucc" -- 名字修改成功 参数:newName
ShareStreetLocalNotify.OnChangeDescSucc = "OnChangeDescSucc" -- 描述修改成功 参数:newDesc
ShareStreetLocalNotify.OnQuitSucc = "OnQuitSucc" -- 退出别人的街区
ShareStreetLocalNotify.OnHasInvitedStatusChanged = "OnHasInvitedStatusChanged" -- 邀请状态变更 参数:hasInvited
ShareStreetLocalNotify.OnHasApplyStatusChanged = "OnHasApplyStatusChanged" -- 申请状态变更 参数:hasApply

return ShareStreetLocalNotify