module("logic.scene.common.action.StartDoubleFollowAction",package.seeall)
local StartDoubleFollowAction = class("StartDoubleFollowAction", SceneActionBase)

function StartDoubleFollowAction:onStart()
	print("StartDoubleFollowAction:onStart", self.params.poseId, self.params.targetId)
	self.isWorking = true
	local player = SceneManager.instance:getCurScene():getUserPlayer()
	local dir = UnitDirection.setFrontBack(player:getDirection(), true)
	SceneAgent.instance:sendSceneShareRequest(PlayerActionType.StartDoubleFollow.typeId, {self.params.poseId, self.params.targetId}, handler(self.onShareResponse, self))
end

function StartDoubleFollowAction:onShareResponse(status)
	if status == 0 then
		SceneController.instance:registerLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		-- local userPlayer = SceneManager.instance:getCurScene():getUserPlayer()
		-- SceneAgent.instance:sendMoveRequest(self.sitPos, {})
	else
		FlyTextManager.instance:showFlyText(DialogHelper.getErrorMsg(status))
		self:finish(false)
	end
	self.isWorking = false
end

function StartDoubleFollowAction:onActionChange(nowState, oldState)
	if oldState == PlayerActionType.StartDoubleFollow and nowState ~= PlayerActionType.StartDoubleFollow then
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		SceneManager.instance:getCurScene():getUserPlayer():setTriggerEnable(true)		
		ViewMgr.instance:close("JoinUmbrellaPanel")
		self:finish(true)
	end
end

function StartDoubleFollowAction:onStop()
	SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
	SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.StartDoubleFollow.typeId, handler(self.onCancelRequest, self))
end

-- function StartDoubleFollowAction:onDispose()
-- 	SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onStateChange, self)
-- 	SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.Share.typeId)
-- end

function StartDoubleFollowAction:onCancelRequest(status)
	if status == 0 then
		self:finish(true)
	else
		DialogHelper.showErrorMsg(status)
		self:finish(false)
	end
end

-- function StartDoubleFollowAction:canOverlap(type)
-- 	return type == SceneActionType.Walk or type == SceneActionType.Joystick
-- end

-- function StartDoubleFollowAction:canInterrupt()
-- 	return false
-- end

function StartDoubleFollowAction:getType()
	return SceneActionType.StartDoubleFollow
end

return StartDoubleFollowAction