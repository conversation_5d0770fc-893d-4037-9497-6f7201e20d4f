module("logic.extensions.transformskin.TransformSkinConfig",package.seeall)

local TransformSkinConfig = class("TransformSkinConfig")

local scene_placing_item_common = ConfigLoader.New("scene_placing_item_common")
local scene_placing_item = ConfigLoader.New("scene_placing_item")
local transformskin_prop = ConfigLoader.New("transformskin_prop")
local transformskin_interact = ConfigLoader.New("transformskin_interact")


--通过物品id获取放置物品配置
function TransformSkinConfig.getCommonCfgByKey(key)
	local config = scene_placing_item_common:getConfig()[key]
	if config == nil then
		printError("scene_placing_item_common配置获取失败，id: " .. tostring(key))
	end
	return config.value
end

--通过物品id获取放置物品配置
function TransformSkinConfig.getPlacingItemByItemId(itemId)
	local config = scene_placing_item:getConfig()[itemId]
	if config == nil then
		printError("c场景摆放配置获取失败，id: " .. tostring(itemId))
	end
	return config
end
--通过物品类型获取放置物品配置
function TransformSkinConfig.getPlacingItemByType(type)
	local configs = scene_placing_item:getConfig()
	local tempConfigs = {}
	for k, v in pairs(configs) do
		if v.type == type then
			table.insert(tempConfigs,v)
		end
	end
	return tempConfigs
end

--通过物品id获取变身道具配置
function TransformSkinConfig.getTransformSkinByItemId(itemId)
	local config = transformskin_prop:getConfig()[itemId]
	if config == nil then
		printError("c场景摆放变身道具配置获取失败，id: " .. tostring(itemId))
	end
	return config
end

--通过物品id获取变身道具配置
function TransformSkinConfig.getAllTransformSkinInteract()
	local config = transformskin_interact:getConfig()
	return config
end

function TransformSkinConfig.getTransformSkinInteractByItemId(itemId)
	local all = TransformSkinConfig.getAllTransformSkinInteract()
	for i=1,#all do 
		local interactConfig = all[i]
		if table.indexof(interactConfig.itemIds,itemId) then
			return interactConfig
		end
	end
end

return TransformSkinConfig