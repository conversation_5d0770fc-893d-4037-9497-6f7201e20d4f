module("logic.scene.common.SceneEffectMgr",package.seeall)
local SceneEffectMgr = class("SceneEffectMgr", SceneComponentBase)

-- local Weather_Sunny = 0
-- local Weather_Rain = 1
-- local Weather_MassSnow = 4
-- local Weather_HeavyRain = 31
-- local Weather_HeavySnow = 34
-- local Weather_Snow = 35


-- local weatherColorConfig = {
-- 	[1] = "#B1D1FF",
-- 	[6] = "#FFF1DA",
-- 	[7] = "#FFE7D5",
-- 	[8] = "#FFEBF4",
-- 	[9] = "#FFF2F8",
-- 	[10] = "#FFFFFF",
-- 	[11] = "#F2FDFF",
-- 	[12] = "#F4FFF2",
-- 	[13] = "#F9FCFF",
-- 	[15] = "#94B4FF",
-- 	[16] = "#FFFFFF",
-- 	[17] = "#9D8FBE",
-- 	[18] = "#8A88CA",
-- 	[19] = "#FFEBF4",
-- 	[20] = "#B7BED9",
-- 	[21] = "#DBC7C3",
-- 	[22] = "#DBC3C7",
-- 	[23] = "#96B0CC",
-- 	[24] = "#96B0CC",
-- 	[25] = "#96B0CC",
-- 	[26] = "#788FA8",
-- 	[27] = "#96B0CC",
-- 	[28] = "#94B4BE",
-- 	[29] = "#94C1FF",
-- 	[30] = "#94C1FF",
-- 	[31] = "#94C1FF",
-- }



function SceneEffectMgr:onEnterSceneFinished(sceneId,bornX,bornZ)
	self._triggerCtrl = self._scene.triggerController
	self._hitEffects = {}
	self._curWeather = nil
	self._curIsDaytime = nil
	local setting = self._scene.stage.stageGO:GetComponent(typeof(PjAobi.SceneSetting))
	-- if VirtualCameraMgr.instance:is3DScene() then
	-- 	self.effectRoot = goutil.findChild(self._scene.stage.stageGO, "StandStill/SceneEffect")
	-- else
	-- 	self.effectRoot = goutil.findChild(self._scene.stage.stageGO, "effect")
	-- end
	self:addHitEffectTrigger(self._scene.stage:getEffectRoot(), 1000, false)
	if setting then
		setting.enabled = false
		PjAobi.SceneTimeController.Instance.setting = setting	
		settimer(10, self.onUpdateSceneTime, self)
		self:onUpdateSceneTime()
		-- PjAobi.SceneTimeController.Instance:SetTime(12)		
	else
		SceneTime.setTime(12, 0)
	end
	-- if sceneId == 7 then
	-- 	self.sceneSound = 130087
	-- 	SoundManager.instance:playEffect(self.sceneSound) 
	-- end
	
	local xRay = goutil.findChild(self._scene.stage.stageGO, "StandStill/XRay")
	self.openXRay = not goutil.isNil(xRay)
	
end

function SceneEffectMgr:onExitScene()
	removetimer(self.onUpdateSceneTime, self)
	self:clearWeather()
	self:clearTimeEffect()
	-- if self.sceneSound then
	-- 	SoundManager.instance:stopEffect(self.sceneSound)
	-- 	self.sceneSound = false
	-- end
end

function SceneEffectMgr:onUpdateSceneTime()
	local now = ServerTime.nowDateServerLook()
	local weather = SceneWeather.getWeather(self._scene:getSceneId())
	-- local now = os.date("*t", os.time())
	-- weather为-2代表正在取数据
	if weather == -2 then
		weather = nil
		SceneWeather.getTodayWeather()
	end
	self:_setTimeAndWeather(now.hour, now.min, weather)	
end

function SceneEffectMgr:setFixedTimeAndWeather(hour, min, weather)
	removetimer(self.onUpdateSceneTime, self)
	self:_setTimeAndWeather(hour, min, weather)
end

function SceneEffectMgr:restoreTimeAndWeather()
	settimer(10, self.onUpdateSceneTime, self)
	self:onUpdateSceneTime()
end

function SceneEffectMgr:stopUpdateSceneTime()
	removetimer(self.onUpdateSceneTime, self)
end

function SceneEffectMgr:_setTimeAndWeather(hour, min, weather)
	if hour then
		SceneTime.setTime(hour, min)	

		if self._curIsDaytime ~= SceneTime.isDaytime() then
			self._curIsDaytime = SceneTime.isDaytime()
			self:clearTimeEffect()
			self:loadTimeEffect(self._curIsDaytime and "day" or "night")
			if SoundManager.instance:isPlayingSceneBGM() then
				SoundManager.instance:playSceneBGM()
			end
			SceneController.instance:localNotify(SceneNotify.DaytimeChange, SceneTime.isDaytime())
		end	
	end
	if weather then
		if weather > 0 then
			local config = SceneConfig.getWeatherConfig(weather)
			if self._curIsDaytime and config.weatherTime > 0 then
				PjAobi.SceneTimeController.Instance:SetTime(config.weatherTime)	
			end
		end
		if self._curWeather ~= weather then
			self:clearWeather()
			if weather > 0 then
				self:setWeather(weather)
			end
			GlobalDispatcher:dispatch(GlobalNotify.OnSceneWeatherChanged, weather)
		end
	end
end




function SceneEffectMgr:loadTimeEffect(name)
	self._loadingEffectUrl = GameUrl.getTimeEffectURL(self._scene:getSceneId(), name)
	getres(self._loadingEffectUrl, self._onTimeEffectLoaded, self)
end

function SceneEffectMgr:_onTimeEffectLoaded(res)
	self._loadingEffectUrl = false	
	if res.IsSuccess then
		self.timeEffectRes = res
		res:Retain()
		self.timeEffectGO = goutil.clone(res:GetMainAsset(), "TimeEffect")
		--不知道为什么会nil，判断一下
		if self.timeEffectGO then
			GameUtils.applyHighEffects(self.timeEffectGO)
			goutil.addChildToParent(self.timeEffectGO, self._scene.stage:getEffectRoot())
			self:addHitEffectTrigger(self.timeEffectGO, 2000, true)
			self.timeSky = goutil.findChild(self.timeEffectGO, "sky")
			if self.weatherSky and self.timeSky then
				self.timeSky:SetActive(false)
			end
			GlobalDispatcher:dispatch(GlobalNotify.OnSceneEffectLoadFinish)
		end

	end
end

function SceneEffectMgr:addHitEffectTrigger(go, startIndex, needRemove)
	local hitGO = goutil.findChild(go, "HitEffect")
	if hitGO then
		local trs = hitGO.transform
		local count = trs.childCount
		for i = 1,count do
			local triggerGO = trs:GetChild(i-1).gameObject
			local trigger = self._triggerCtrl:addTrigger(startIndex + i, triggerGO, SceneObjectType.HitEffect, nil, nil)
			if needRemove then
				self._hitEffects[i] = trigger
			end
		end	
	end
end

function SceneEffectMgr:clearTimeEffect()

	if self._loadingEffectUrl then
		removeresl(self._loadingEffectUrl, self._onTimeEffectLoaded, self)
		self._loadingEffectUrl = false
	end
	if self.timeEffectRes then
		for i=1, #self._hitEffects do
			self._triggerCtrl:removeTrigger(self._hitEffects[i].id)
		end
		self._hitEffects = {}
		goutil.destroy(self.timeEffectGO)
		self.timeEffectRes:Release()
		rescache:DestroyResource(self.timeEffectRes)
		self.timeEffectRes = nil
	end
	self.timeSky = nil
end

function SceneEffectMgr:setWeather(weather)
	-- printInfo("set weather", weather.type)
	self._curWeather = weather
	local config = SceneConfig.getWeatherConfig(weather)
	if config.weatherSound > 0 then
		self.weatherSound = config.weatherSound
		SoundManager.instance:playEffect(self.weatherSound)	
	end
	if self._curIsDaytime then
		if not string.nilorempty(config.dayColor) then
			SceneTime.SetWeatherColor(config.dayColor)
		end
	else
		if not string.nilorempty(config.nightColor) then
			SceneTime.SetWeatherColor(config.nightColor)
		end	
	end
	if not string.nilorempty(config.postEffect) then
		self.weatherEffect = config.postEffect
		PostProcessMgr.instance:setEffectEnable(self.weatherEffect, true)		
	end
		self:loadWeatherPrefab(weather)

	-- if weather == Weather_Rain then
	-- 	-- self:setSnow(-0.075, 12, 17, 0.5, 0.45)
	-- 	self.weatherEffect = "rain"
	-- 	PostProcessMgr.instance:setEffectEnable(self.weatherEffect, true)
	-- 	self:loadWeatherPrefab(weather)
	-- 	if self._curIsDaytime then
	-- 		SceneTime.SetWeatherColor(weatherColorConfig[weather])
	-- 	end
	-- 	self.weatherSound = 130096
	-- 	SoundManager.instance:playEffect(self.weatherSound)
	-- elseif weather == Weather_HeavyRain then
	-- 	-- self:setSnow(-0.075, 12, 17, 0.5, 0.45)
	-- 	self.weatherEffect = "heavyrain"
	-- 	PostProcessMgr.instance:setEffectEnable(self.weatherEffect, true)
	-- 	self:loadWeatherPrefab(weather)
	-- 	if self._curIsDaytime then
	-- 		SceneTime.SetWeatherColor(weatherColorConfig[weather])
	-- 	end
	-- 	self.weatherSound = 141360
	-- 	SoundManager.instance:playEffect(self.weatherSound)		
	-- else 
	-- 	if weatherColorConfig[weather] then
	-- 		printInfo("set weather color")
	-- 		SceneTime.SetWeatherColor(weatherColorConfig[weather])
	-- 	end
	-- 	self:loadWeatherPrefab(weather)
	-- 	if weather == Weather_HeavySnow or weather == Weather_Snow or Weather_MassSnow then
	-- 		self.weatherSound = 141576
	-- 		SoundManager.instance:playEffect(self.weatherSound)
	-- 	end
	-- end

end

function SceneEffectMgr:clearWeather()
	if self.weatherSound then
		SoundManager.instance:stopEffect(self.weatherSound)
		self.weatherSound = nil
	end
	if self.weatherEffect then	
		PostProcessMgr.instance:setEffectEnable(self.weatherEffect, false)
		self.weatherEffect = nil
	end
	self:clearWeatherPrefab()
	SceneTime.clearWeatherColor()
	self._curWeather = nil	
end

function SceneEffectMgr:loadWeatherPrefab(name)
	self._loadingWeatherUrl = GameUrl.getWeatherURL(self._scene:getSceneId(), name)
	getres(self._loadingWeatherUrl, self._onWeatherLoaded, self)
end

function SceneEffectMgr:_onWeatherLoaded(res)
	self._loadingWeatherUrl = false	
	if res.IsSuccess then
		self.weatherRes = res
		res:Retain()
		self.weatherGO = goutil.clone(res:GetMainAsset(), "weather")
		goutil.addChildToParent(self.weatherGO, self._scene.stage:getEffectRoot())
		self.weatherSky = goutil.findChild(self.weatherGO, "sky")
		if self.weatherSky and self.timeSky then
			self.timeSky:SetActive(false)
		end
	end
end

function SceneEffectMgr:clearWeatherPrefab()
	if self._loadingWeatherUrl then
		removeresl(self._loadingWeatherUrl, self._onWeatherLoaded, self)
		self._loadingWeatherUrl = false	
	end
	if self.weatherSky and self.timeSky then
		self.timeSky:SetActive(true)
	end
	self.weatherSky = nil
	if self.weatherRes then
		goutil.destroy(self.weatherGO)
		self.weatherRes:Release()
		rescache:DestroyResource(self.weatherRes)
	end
end





-- function SceneEffectMgr:setSnow(wind, fallSpeed, tilingX, tilingY, density)
-- 	local vec = Vector4.New(wind, fallSpeed, tilingX, tilingY)
-- 	PostProcessMgr.get():SetSnow(vec, density, parsecolor("#D5D6FF64"))
-- end

function SceneEffectMgr:cancelSnow()
	PostProcessMgr.get():CancelSnow()
end

return SceneEffectMgr