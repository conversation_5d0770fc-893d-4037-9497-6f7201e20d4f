module("logic.scene.unit.playeraction.JoinDoubleFollowHandler",package.seeall)

local JoinDoubleFollowHandler = class("JoinDoubleFollowHandler",PlayerActionHandlerBase)

function JoinDoubleFollowHandler:onStart()
    if SceneManager.instance:isInSea() then
        self._seaSceneZ = GameUtils.getLocalPos(self.unit.go).z
    end
    local poseId = tonumber(self.info.params[1])
	self.cfg = PoseConfig.getDoublePose(poseId)	
	self.targetId =  self.info.params[2]
    self.unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
	self.unit.skinView:loadAnimation({"anif_shuangrendongzuo","anib_shuangrendongzuo"}, self._playPose, self)


end

function JoinDoubleFollowHandler:_playPose()
    self.unit:setFace(true)
    self.cfg = PoseConfig.getDoublePose(tonumber(self.info.params[1]))
    if self.cfg.subHideHandItem >= 0 then
        self.unit.skinView:hideHandItem("pose", self.cfg.subHideHandItem)
    end

    if not VirtualCameraMgr.instance:is3DScene() then
        self.unit.notSort = true
    end


    if self.cfg.subClothes > 0 then
        self.unit.skinView:addTempClothes(self.cfg.subClothes)
    end
    if not string.nilorempty(self.cfg.subEffect) then
        local slot = self.unit.effectLoader:getSlot(EffectLoader.Pose)
        slot:load(GameUrl.getPoseURL(self.cfg.subEffect))
    end

    if self.cfg.sound then
        if iskindof(self.unit, "UIModel") then
            SoundManager.instance:playEffect(self.cfg.sound)
        else
            SoundManager.instance:playEffect(self.cfg.sound, self.unit.go)
        end
    end		

    self:setTartget(self.targetId)
    --self.unit:lockMove(true)
    self.oldSpeed = self.unit:getSpeed()
	self.unit:setSpeed(2.5)	
    self:onUpdate()
    self.unit:setNavEnable(false)
    self.unit:setAlwaysFront(true)
    
    --self.unit.walkEffect:setSpecialMove(true) --关闭脚印
    UpdateBeat:Add(self.updateAttach,self)
end

function JoinDoubleFollowHandler:onStop()
    UpdateBeat:Remove(self.updateAttach,self)
	self.unit.notSort = false
    if self.cfg.subHideHandItem >= 0 then
        self.unit.skinView:showHandItem("pose", self.cfg.subHideHandItem)
    end	
    self.unit:setAlwaysFront(false)
    local curScene = SceneManager.instance:getCurScene()
    self.unit.followComp:setFollowTarget(nil)
    self.unit:setSpeed(self.oldSpeed)
    --self.unit:lockMove(false)
    
    local x,y = self.unit:getPos()
    local walkable = curScene:getMapMgr():isWalkable(x,y)
    if not walkable and self._master then 
        local x,y = self._master:getPos()
        self.unit:moveTo(x,y)
    end
    --self.unit.walkEffect:setSpecialMove(false)
    if SceneManager.instance:isInSea() then
        local nowPos = GameUtils.getLocalPos(self.unit.go)
        GameUtils.setLocalPos(self.unit.go,nowPos.x,nowPos.y,self._seaSceneZ)
    end
    self.unit:setNavEnable(true)
end

function JoinDoubleFollowHandler:onUpdate()

end

function JoinDoubleFollowHandler:onShortActionStart()

end

function JoinDoubleFollowHandler:onAttachMove()
    self._isAttachMoveing = true
    self.unit:onStartMove()
end

function JoinDoubleFollowHandler:onAttachStop()
    self._isAttachMoveing = false
    self.unit:onStopMove()
end


function JoinDoubleFollowHandler:onStartMove()
    self.unit:playAnimation(self.cfg.subAniName .. "_walk", true,false,true)
    if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_move")
	end
end

function JoinDoubleFollowHandler:onStopMove()
    self.unit:playAnimation(self.cfg.subAniName .. "_idle", true,false,true)	
    if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_idle")
		self.unit.idleComp:setEnable(false)
	end
end

function JoinDoubleFollowHandler:setTartget(userId)
    local curScene = SceneManager.instance:getCurScene()
    self.unit.followComp:setFollowTarget(nil)
    local master = curScene:getPlayer(userId)
    if not master then 
        return
    end
    self._master = master
    local dir = self._master:getDirection()

    --z轴参数只在3d场景生效，其他场景在followComp中有特殊处理
    local isReverse = UnitDirection.isFaceLeft(dir)
    local offsetX = isReverse and -self.cfg.subOffset[1] or self.cfg.subOffset[1]
    local offsetY = self.cfg.subOffset[2]
    self.unit.followComp:setAttachTarget(self._master,offsetX,offsetY)

    --self.unit.followComp:setQueueTarget(master,0.3)
end

function JoinDoubleFollowHandler:updateAttach()
    if not self._master then 
        return
    end
    local dir = self._master:getDirection()
    local isReverse = UnitDirection.isFaceLeft(dir)
    local offsetX = isReverse and -self.cfg.subOffset[1] or self.cfg.subOffset[1]
    local offsetY = self.cfg.subOffset[2]
    self.unit.followComp:setAttachTarget(self._master,offsetX,offsetY)

    if self._master:isMoving() and not self._isAttachMoveing then  
        self:onAttachMove()
    elseif not self._master:isMoving() and self._isAttachMoveing then 
        self:onAttachStop()
    end
end

return JoinDoubleFollowHandler