module("logic.extensions.activity.scrollmachine.config.ScrollingMachineConfig",package.seeall)

local ScrollingMachineConfig = class("ScrollingMachineConfig")

local poolConfig = ConfigLoader.New("act426_reward_pool")
local targetConfig = ConfigLoader.New("act426_target_reward")

ScrollingMachineConfig.ActId = 2496

ScrollingMachineConfig.iconDic = {
	[1] = "40mlg_13",
	[2] = "40mlg_14",
	[3] = "40mlg_15",
}

function ScrollingMachineConfig.getConst(key)
	return CommonConfig.getConfig("act426_common")[ScrollingMachineConfig.ActId][key].value
end

function ScrollingMachineConfig.getNumberConst(key)
	local value = CommonConfig.getConfig("act426_common")[ScrollingMachineConfig.ActId][key].value
    return tonumber(value)
end

function ScrollingMachineConfig.getItemConst(key)
	local value = CommonConfig.getConfig("act426_common")[ScrollingMachineConfig.ActId][key].value
    local nums = string.splitToNumber(value,":")
    return {id = nums[1], count = nums[2]}
end

function ScrollingMachineConfig.getRewardConfig(rewardId)
    return poolConfig:getConfig()[ScrollingMachineConfig.ActId][rewardId]
end

function ScrollingMachineConfig.isLotteryTicket(id)
    return id == ScrollingMachineConfig.getItemConst("lotterySpentItem").id
end

function ScrollingMachineConfig.getAllTartgetReward()
    return targetConfig:getConfig()[ScrollingMachineConfig.ActId]
end

function ScrollingMachineConfig.getTartgetRewardById(id)
	return targetConfig:getConfig()[ScrollingMachineConfig.ActId][id]
end


return ScrollingMachineConfig