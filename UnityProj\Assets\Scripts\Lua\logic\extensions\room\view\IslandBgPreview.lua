module("logic.extensions.room.view.IslandBgPreview", package.seeall)

local IslandBgPreview = class("IslandBgPreview", ViewComponent)

function IslandBgPreview.show(itemId)
	-- local path
	-- if RoomConfig.isOutsideBg(itemId) then
	--     path = "ui/scene/room/editorbgshowview.prefab"
	-- elseif RoomConfig.isStreetBg(itemId) then
	--     path = "ui/archive/archivestreetshowview.prefab"
	-- end
	ViewMgr.instance:open("IslandBgPreview", itemId)
end

function IslandBgPreview:buildUI()
	self._txtName1 = self:getText("txtName1")
	self._txtName2 = self:getText("txtName2")
	self._icon1 = self:getGo("maks1/icon")
	self._icon2 = self:getGo("maks2/icon")
	self._imgTips = self:getGo("imgTips")
	self._txtTips = self:getText("imgTips/txtTips")
	self._mask1 = self:getGo("maks1")
	self._mask2 = self:getGo("maks2")
	self._imgMask1 = self:getGo("imgMask1")
	self._imgMask2 = self:getGo("imgMask2")
end

function IslandBgPreview:onExit()
	IconLoader.clearImage(self._icon1)
	IconLoader.clearImage(self._icon2)
end

function IslandBgPreview:onEnter()
	local id = self:getFirstParam()
	self._txtName1.text = ItemService.instance:getName(id)
	self._txtName2.text = ItemService.instance:getName(id)
	local part = FurnitureConfig.getPartDefine(FurnitureConfig.getFurnitureDefine(id).subType)
	if self._txtTips then
		self._txtTips.text = part.name
	end
	local isOutSide = RoomConfig.isOutsideBg(id)
	local isStreet = RoomConfig.isStreetBg(id)
    self._imgTips:SetActive(isOutSide)
    self._mask1:SetActive(isOutSide)
    self._mask2:SetActive(isStreet)
    self._imgMask1:SetActive(isOutSide)
    self._imgMask2:SetActive(isStreet)
	self._txtName1.gameObject:SetActive(isOutSide)
	self._txtName2.gameObject:SetActive(isStreet)
	if isOutSide then
        IconLoader.setSpriteToImg(self._icon1, string.format("image/archive/furniture/%d.png", id))
	elseif isStreet then
        IconLoader.setSpriteToImg(self._icon2, string.format("image/archive/furniture/%d.png", id))
	end
end

return IslandBgPreview 