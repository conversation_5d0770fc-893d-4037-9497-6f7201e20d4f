module("logic.extensions.sharestreet.view.ShareStreetMapItem", package.seeall)

local ShareStreetMapItem = class("ShareStreetMapItem", BaseLuaComponent)

function ShareStreetMapItem:ctor(go, index, areaId)
    ShareStreetMapItem.super.ctor(self, go)
    self._index = index
    self._areaId = areaId

    self._aniUnlock = go:GetComponent("Animation")

    self._goImgMaster = self:getGo("imgMaster")
    self._goNull = self:getGo("nullGo")
    self._btnUnlock = self:getBtn("nullGo")
    self._goLockTxt = self:getGo("nullGo/lockGo")
    self._goNullImage = self:getGo("nullGo/Image_02")
    self._imgNull = self._goNullImage:GetComponent("Image")

    self._goPlayer = self:getGo("player/go")
    self._goPlayer:SetActive(true)
    self._goPlayerContainer = self:getGo("player")
    self._ctPlayer = Framework.UIClickTrigger.Get(self._goPlayerContainer)
    self._goAdd = self:getGo("addGo")
    self._btnAdd = self:getBtn("addGo/btnAdd")
    self._btnMore = self:getBtn("btnMore")
    self._goMore = self:getGo("moreGo")
    self._btnDelete = self:getBtn("moreGo/objDelete")
    self._btnQuit = self:getBtn("btnQuit")
    self._txtName = self:getText("txtName")

    self._btnAdd:AddClickListener(self._onClickAdd, self)
    self._btnMore:AddClickListener(self._onClickMore, self)
    self._btnDelete:AddClickListener(self._onClickDelete, self)
    self._btnUnlock:AddClickListener(self._onClickUnlock, self)
    self._btnQuit:AddClickListener(self._onClickQuit, self)
    self._ctPlayer:AddClickListener(self._onClickPlayer, self)
end

function ShareStreetMapItem:setInfo(isOwner, isReadOnly, isUnlock, memberInfo)
    self._info = memberInfo --RoleSimpleInfoNO
    self._isReadOnly = isReadOnly
    self._isOwner = isOwner
    if not isUnlock then
        TaskUtil.showGOThenHideOthers(self._goNull)
        self._goLockTxt:SetActive(not isReadOnly)
        return
    end
    if not memberInfo then
        TaskUtil.showGOThenHideOthers(self._goAdd)
        if isReadOnly then
            self._goAdd:SetActive(false)
        end
        return
    end
    self._playerId = memberInfo.id

    self.avatar = AvatarInfo.New()
    self.avatar:setAvatar(memberInfo.clothes, true)
    self._goPlayerContainer:SetActive(true)
    self._goPlayer:SetActive(true)
    if self.model == nil then
        self.model = ModelPhotoBase.New(self._goPlayer, 1, ModelPhotoBase.Type_Player)
    end
    local avatarView = self.model:getAvatarView()
    if avatarView then
        avatarView:isShowCardView(false)
        avatarView:setHasBack(false)
    end
    self.model:setClothes(2, self.avatar)
    self.model:setDirection(self._isOwner and UnitSpineDir.Left or UnitSpineDir.Right)

    self._goImgMaster:SetActive(isOwner)
    self._btnMore.gameObject:SetActive(not self._isReadOnly and memberInfo.id ~= UserInfo.userId)
    self._goMore:SetActive(false)
    self._goAdd:SetActive(false)
    self._goNull:SetActive(false)
    self._btnQuit.gameObject:SetActive(self._isReadOnly and memberInfo.id == UserInfo.userId)
    self._txtName.gameObject:SetActive(true)
    self._txtName.text = memberInfo.id == UserInfo.userId and string.format("<color=#FEC853>%s</color>", memberInfo.nickname) or memberInfo.nickname
end

function ShareStreetMapItem:setNullImageAlpha(alpha)
    PjAobi.CSGameUtils.SetImageAlpha(self._imgNull, alpha)
    self._goLockTxt:SetActive(alpha >= 1)
end

function ShareStreetMapItem:playUnlockAni(endHandler)
    self._aniEndHandler = endHandler
    self._aniUnlock.clip:SampleAnimation(self._aniUnlock.gameObject, 0)
    self._aniUnlock:Play()
    local duration = self._aniUnlock.clip.length
	settimer(duration, self._onAniEnd, self, false)
end

function ShareStreetMapItem:_onAniEnd()
    if self._aniEndHandler then
        self._aniEndHandler()
        self._aniEndHandler = nil
    end
end

function ShareStreetMapItem:addUnlockHandler(handler)
    self._unlockHandler = handler
end

function ShareStreetMapItem:addDeleteHandler(handler)
    self._deleteHandler = handler
end

function ShareStreetMapItem:addAddHandler(handler)
    self._addHandler = handler
end

function ShareStreetMapItem:addQuitHandler(handler)
    self._quitHandler = handler
end

function ShareStreetMapItem:_onClickMore()
    if self._isReadOnly then
        return
    end
    self._goMore:SetActive(not self._goMore.activeSelf)
end

function ShareStreetMapItem:_onClickDelete()
    if self._isReadOnly then
        return
    end
    if self._deleteHandler then 
        self._deleteHandler(self._playerId)
    end
end

function ShareStreetMapItem:_onClickAdd()
    if self._isReadOnly then
        return
    end
    if self._addHandler then
        self._addHandler()
    end
end

function ShareStreetMapItem:_onClickUnlock()
    if self._isReadOnly then
        return
    end
    if self._unlockHandler then
        self._unlockHandler(self._index, self._areaId)
    end
end

function ShareStreetMapItem:_onClickQuit()
    if self._quitHandler then
        self._quitHandler()
    end
end

function ShareStreetMapItem:_onClickPlayer()
    ViewFacade.showUserInfo(self._playerId)
end

function ShareStreetMapItem:destroy()
    self._btnAdd:RemoveClickListener()
    self._btnDelete:RemoveClickListener()
    self._btnMore:RemoveClickListener()
    self._btnUnlock:RemoveClickListener()
    self._btnQuit:RemoveClickListener()
    self._ctPlayer:RemoveClickListener()

    self._unlockHandler = nil
    self._deleteHandler = nil
    self._addHandler = nil
    self._quitHandler = nil

    if self.model then
        self.model:dispose()
        self.model = nil
    end
end

function ShareStreetMapItem:getAreaId()
    return self._areaId
end

return ShareStreetMapItem
