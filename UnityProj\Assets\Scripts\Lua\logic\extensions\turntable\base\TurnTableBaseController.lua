module("logic.extensions.turntable.base.TurnTableBaseController", package.seeall)

local TurnTableBaseController = class("TurnTableBaseController", BaseController)

--普通状态，仅此状态可以自动关闭活动面板
TurnTableBaseController.NormalState = 1
--动画状态,锁死，不可交互
TurnTableBaseController.PlayingAnimState = 2
--领奖状态
TurnTableBaseController.RewardDialogState = 3


--转盘活动controller的通用逻辑，后代一定要找set activityId
function TurnTableBaseController:setActivityId(activityId)
    self.activityId = activityId
    self.data = TurnTableBaseData.New(activityId)
end

function TurnTableBaseController:setActInfo(actInfo)
    self:setActivityId(actInfo.activityId)
    self.activityName = actInfo.activityName
end

function TurnTableBaseController:onInit()
    TurnTableBaseController.super.onInit(self)
    self.curState = TurnTableBaseController.NormalState
    TurnTableConfig.initCfg()
end

function TurnTableBaseController:resetData()
    self.data:reset()
end

function TurnTableBaseController:initData(ids, drawprocess, totallotterycount)
    self.data:set(ids, drawprocess, totallotterycount)
end

--------------------------------数据获取接口
--获取特殊收集的数量
function TurnTableBaseController:getSpecialCount()
    return self.data.specialCount
end

--获取某池子已抽取的数量
function TurnTableBaseController:getTypeHasGetCount(type)
    return self.data:getTypeHasGetCount(type)
end

-- 获取某奖池剩余的奖励数量
function TurnTableBaseController:getReamainCountByType(type)
    return self.data:getReamainCountByType(type)
end

--获取某奖池当前概率
function TurnTableBaseController:getRateByType(type)
    return self.data.poolRate[type]
end

--剩余奖励数量
function TurnTableBaseController:getRemainCount()
    return self.data:getRemainCount()
end

function TurnTableBaseController:getHasGetSpecialReward()
    return self.data.hasGetSpecialReward
end

--清盘
function TurnTableBaseController:getHasGetAllClearReward()
    return self.data.hasGetAllClearReward
end

--奖券上限
function TurnTableBaseController:getBuyMaxCount(costId)
    local hasCount = ItemService.instance:getItemNum(costId)
    local remainReward = self:getRemainCount()
    local result = remainReward - hasCount
    return result
end

--抽奖次数，不一定等于获得奖励数量
function TurnTableBaseController:getProgress()
    return self.data:getProgress()
end

--判断此进度奖励是否可选
function TurnTableBaseController:checkProgressRewardItemCanSelect(poolId)
    --尝试获取此奖励对应的奖池id
    local rewardId = poolId
    if rewardId then
        --未抽中则可选
        return not self:checkHasGet(rewardId)
    else
        --无需联动
        return true
    end
end

--某个奖励是否已获得
function TurnTableBaseController:checkHasGet(id)
    return self.data.rewardRecord[id] == true
end

--进度奖励状态
function TurnTableBaseController:getProgressAwardStatus(index)
    return self.data.progressStatus[index]
end

--转盘们
function TurnTableBaseController:getTurnTable()
    return self.data.tableResult
end

---------------------------------流程控制
--刷新面板
function TurnTableBaseController:updateView(isInit)
    if isInit then
        TurnTableAgent.instance:sendGetAct161InfoRequest(handler(self.onGetInfo, self))
    else
        self:resetTurnTable()
    end
end

function TurnTableBaseController:onGetInfo(ids, drawprocess, totallotterycount, tiredvalue, drawtiredrewardprocess,
    drawscorerewardsprocess)
    self:resetData()
    self:initData(ids, drawprocess, totallotterycount)
    self:setTiredInfo(tiredvalue, drawtiredrewardprocess)
    self:setExProgressInfo(drawscorerewardsprocess)
    self:resetTurnTable()
end

--疲劳值奖励相关
function TurnTableBaseController:setTiredInfo(tiredvalue, drawtiredrewardprocess)
    --可能没开
    if tiredvalue == nil then
        return
    end
    self.tiredValue = tiredvalue
    self.tiredProgressStatus = {}
    local cfg = TurnTableConfig.getFatigueRewards(self.activityId)
    if cfg then
        for i, v in ipairs(cfg) do
            local hasGet = bitutil.checkBitValue(drawtiredrewardprocess, i - 1)
            print(i, hasGet)
            table.insert(self.tiredProgressStatus, hasGet)
        end
    end
end

function TurnTableBaseController:getTiredRewards(id, callback)
    self._tiredCallback = callback
    self._tiredId = id
    TurnTableAgent.instance:sendAct161DrawFatigueRewardRequest(id, handler(self.onGetTiredRewards, self))
end

function TurnTableBaseController:onGetTiredRewards(changesetid)
    DialogHelper.showRewardsDlgByCI(
        changesetid,
        nil,
        function()
            self.tiredProgressStatus[self._tiredId] = true
            if self._tiredCallback then
                self._tiredCallback()
                self._tiredCallback = nil
            end
        end
    )
end

--额外积分奖励相关
function TurnTableBaseController:setExProgressInfo(drawscorerewardsprocess)
    --可能没开
    if drawscorerewardsprocess == nil or not TurnTableConfig.hasExProgressReward(self.activityId) then
        return
    end
    self.exProgressStatus = {}
    for i, v in ipairs(TurnTableConfig.getExProgressRewardCfg(self.activityId)) do
        local hasGet = bitutil.checkBitValue(drawscorerewardsprocess, i - 1)
        table.insert(self.exProgressStatus, hasGet)
    end
end

function TurnTableBaseController:getExProgressRewards(id, callback)
    self.exProgressCallback = callback
    self.exProgressId = id
    TurnTableAgent.instance:sendAct161DrawLotteryScoreRewardRequest(id, handler(self.onGetExProgressRewards, self))
end

function TurnTableBaseController:onGetExProgressRewards(changesetid)
    DialogHelper.showRewardsDlgByCI(
        changesetid,
        nil,
        function()
            self.exProgressStatus[self.exProgressId] = true
            if self.exProgressCallback then
                self.exProgressCallback()
                self.exProgressCallback = nil
            end
        end
    )
end

--重新绘制转盘
function TurnTableBaseController:resetTurnTable()
    self.curState = TurnTableBaseController.NormalState
    self.data:buildTableData() -- 更新概率
    print("resettttttt")
    self:localNotify(TurnTableNotify.ReSetTurnTable)
    self:localNotify(TurnTableNotify.UpdateProgress)
end

--抽奖
function TurnTableBaseController:lottery(times, isSkip)
    local isten = times > 1
    self.isSkip = isSkip
    TurnTableAgent.instance:sendAct161LotteryRequest(isten, handler(self.onLottery, self))
end

function TurnTableBaseController:onLottery(ids, processIds)
    -- if #ids == 1 or #ids == 10 then
    --抽奖结果
    self.lotteryResult = ids
    if processIds and #processIds > 0 then
        self.processIds = processIds
    end

    self:playAnim()
    -- else
    --     self.curState = TurnTableBaseController.NormalState
    --     printError("后端返回的奖励数量有问题，应该是1个或10个，本次返回", #ids)
    -- end
end

function TurnTableBaseController:playAnim()
    self.curState = TurnTableBaseController.PlayingAnimState
    TaskUtil.BlockClick(true, "TurnTableBaseController:playAnim")
    if self.isSkip then
        self:endAnim()
    else
        self:localNotify(TurnTableNotify.PlayTurnAnim)
    end
end

function TurnTableBaseController:endAnim()
    self.curState = TurnTableBaseController.RewardDialogState
    TaskUtil.BlockClick(false, "TurnTableBaseController:playAnim")
    self:openResultView()
end

--打开抽奖结果界面
function TurnTableBaseController:openResultView()
    local rewardItems = {}
    print("抽奖结果：")
    for i, v in ipairs(self.lotteryResult) do
        local rewardDefine = TurnTableConfig.getBonus(self.activityId)[v]
        local item = {id = rewardDefine.rewards.id, num = rewardDefine.rewards.count}
        table.insert(rewardItems, item)
    end
    local shareType = TurnTableConfig.getShareType(self.activityId)
    print("跳过？跳过没有大大的effect", self.isSkip)
    ViewMgr.instance:open(
        "TurnTableJumpReward",
        rewardItems,
        handler(self.updateData, self),
        shareType,
        self.activityId,
        not self.isSkip
    )
end

--获取奖励之后，更新数据，并判断是否满足大奖
function TurnTableBaseController:updateData()
    for i, v in ipairs(self.lotteryResult) do
        self.data:getPoolRewards(v)
    end
    self.data:updateProcessAfterLottery(#self.lotteryResult)
    if self.processIds then
        for i=1, #self.processIds do
            self.data:updateSelectGift(self.processIds[i])
        end
    end
    self:checkNeedShowDialog()
end

--更新自选数据,并判断是否满足大奖
function TurnTableBaseController:selectProgressReward(id, bagId)
    self.data:updateSelectGift(bagId)
    if id then
        self.data:getPoolRewards(id)
        self:checkNeedShowDialog()
    else
        self:resetTurnTable()
    end
end

function TurnTableBaseController:checkNeedShowDialog()
    if self.processIds then
        local items = {}
        for i=1, #self.processIds do
            local config = TurnTableConfig.getProgressAwardConfig(self.activityId, self.processIds[i])
            if config then
                table.insert(items, {id=config.activityEndBoxId, num=1})
            end
        end
        self.processIds = nil
        DialogHelper.showRewardsDlg(items, "获得特殊大奖", handler(self.checkSpecialReward, self))
    else
        self:checkSpecialReward()
    end
end

function TurnTableBaseController:checkSpecialReward()
    if self.data:checkHasAllKeyReward() and self.data.hasGetSpecialReward == false then
        print("收集完成！show！")
        FlyTextManager.instance:showFlyText(lang("特殊奖励已全部集齐！"))
        local items = TurnTableConfig.getSpecialRewardItems(self.activityId)
        for i, v in ipairs(items) do
            v.num = v.count
        end
        self.data.hasGetSpecialReward = true
        DialogHelper.showRewardsDlg(items, "获得特殊大奖", handler(self.checkAllClear, self))
    else
        self:checkAllClear()
    end
end

function TurnTableBaseController:checkAllClear()
    if self.data:checkHasAllClearReward() and self.data.hasGetAllClearReward == false then
        print("全收集！！")
        FlyTextManager.instance:showFlyText(lang("辉月奖励已全部集齐！"))
        local items = TurnTableConfig.getAllClearRewardItems(self.activityId)
        for i, v in ipairs(items) do
            v.num = v.count
        end
        self.data.hasGetAllClearReward = true
        DialogHelper.showRewardsDlg(items, "获得辉月全收集大奖", handler(self.resetTurnTable, self))
    else
        self:resetTurnTable()
    end
end

function TurnTableBaseController:checkAllClearReward()
        
end

--判断能否自动关闭面板
function TurnTableBaseController:canClose()
    return self.curState == TurnTableBaseController.NormalState
end

TurnTableBaseController.instance = TurnTableBaseController.New()
return TurnTableBaseController
