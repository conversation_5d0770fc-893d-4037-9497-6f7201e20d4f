module("logic.extensions.sharestreet.view.ShareStreetEditNameView",package.seeall)
---@class ShareStreetEditNameView
local ShareStreetEditNameView = class("ShareStreetEditNameView",ViewComponent)

function ShareStreetEditNameView:ctor()
	ShareStreetEditNameView.super.ctor(self)
end

--- view初始化时会执行
function ShareStreetEditNameView:buildUI()
	ShareStreetEditNameView.super.buildUI(self)

	self.iptName = self:getInput("iptName")
	self.btnDiamond = self:getBtn("btnDiamond")
	self.btnDiamond:AddClickListener(self.onClickDiamond, self)
	self.txtDiamondNum = self:getText("btnDiamond/iconAndCount/countTxt")
	self.btnClose = self:getBtn("btnClose")
	self.btnClose:AddClickListener(self.close, self)
	self.btnFree = self:getBtn("btnFree")
	self.btnFree:AddClickListener(self.onClickFree, self)
end

--- view初始化时会执行，在buildUI之后
function ShareStreetEditNameView:bindEvents()
	ShareStreetEditNameView.super.bindEvents(self)

	self.iptName:AddOnValueChanged(self._onNameChange, self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetEditNameView:onEnter()
	ShareStreetEditNameView.super.onEnter(self)

	self._streetNameMaxLength = tonumber(ShareStreetCfg.instance:getCommonConfigValue("streetNameMaxLength")) -- 街区名字最大长度

	self._streetInfo = ShareStreetModel.instance:getUserInfo()
	local strCost = ShareStreetCfg.instance:getCommonConfigValue("modifyNameItems")
	local costArr = string.splitToNumber(strCost, ":")
	self._costId = costArr[1]
	self._costNum = costArr[2]
	print(strCost, "strCost", self._costId, self._costNum)
	self:_update()
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetEditNameView:onEnterFinished()
	ShareStreetEditNameView.super.onEnterFinished(self)
end

--- 在ViewPresentor:playEnterAnimation()调用时执行
function ShareStreetEditNameView:onExit()
	ShareStreetEditNameView.super.onExit(self)
end

--- 在ViewPresentor:_onPlayAnimationDone()调用时执行
function ShareStreetEditNameView:onExitFinished()
	ShareStreetEditNameView.super.onExitFinished(self)
end

--- view销毁时会执行，在destroyUI之前
function ShareStreetEditNameView:unbindEvents()
	ShareStreetEditNameView.super.unbindEvents(self)

    self.iptName:RemoveOnValueChanged()

	self.btnClose:RemoveClickListener()
	self.btnDiamond:RemoveClickListener()
	self.btnFree:RemoveClickListener()
end

--- view销毁时会执行
function ShareStreetEditNameView:destroyUI()
	ShareStreetEditNameView.super.destroyUI(self)
end

function ShareStreetEditNameView:_onNameChange(inputStr)
    print("ShareStreetEditNameView:onNameChange", inputStr)
    inputStr = GameUtils.getBriefName(inputStr, self._streetNameMaxLength, '')
    self.iptName:SetText(inputStr)
end

function ShareStreetEditNameView:_update()
	self.iptName:SetText(self._streetInfo:getName())

	local count = ShareStreetModel.instance:getModifyNameCount()
	self.btnFree.gameObject:SetActive(count == 0)
	self.btnDiamond.gameObject:SetActive(count > 0)
	if count > 0 then
		self.txtDiamondNum.text = tostring(self._costNum)
	end
end

function ShareStreetEditNameView:onClickDiamond()
	RechargeFacade.isCurrencyEnough(self._costId, self._costNum, true, function()
		self:_sendReq()
	end)
end

function ShareStreetEditNameView:onClickFree()
	self:_sendReq()
end

function ShareStreetEditNameView:_sendReq()
	local streetName = self.iptName:GetText()
	if streetName == self._streetInfo:getName() then
		DialogHelper.showMsg(lang("请输入新的街区名字"))
		return
	end
	if not ShareStreetUtil.isStreetNameLegal(streetName) then
		return
	end
	ShareStreetController.instance:changeName(streetName, handler(self._onRspn, self))
end

function ShareStreetEditNameView:_onRspn()
	FlyTextManager.instance:showFlyText(lang("修改成功"))
	local count = ShareStreetModel.instance:getModifyNameCount()
	ShareStreetModel.instance:setModifyNameCount(count + 1)
	self:close()
end

return ShareStreetEditNameView