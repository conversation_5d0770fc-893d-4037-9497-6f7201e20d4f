module("logic.scene.unit.TransformSkinUnit",package.seeall)

local TransformSkinUnit = class("TransformSkinUnit",WalkableUnit)

function TransformSkinUnit:_initComponents()
	TransformSkinUnit.super._initComponents(self)
	self:_addComponent("view", SpineUnitComp)
	self:_addComponent("followUIComp", UnitCompFollowUI)
	self:_addComponent("effectLoader", EffectLoader)
	self._prefabLoader = PrefabLoader.Get(self.go)
	self.view:addListener(UnitNotify.SpineLoaded, self.onLoaded, self)
	self.view:setDefaultAnimState(nil)
	self.view:setLayer(self:getLayer())
end

function TransformSkinUnit:onSetInfo(params)
	self._curScene = SceneManager.instance:getCurScene()
	self._refGOWorldPos = Vector3.zero
	self._mainCamera = CameraTargetMgr.instance:getMainCameraTarget():getCamera()
	self._isEditor = params.isEditor
	self._info = self:parseInfo(params.info)
	self._placingConfig = TransformSkinConfig.getPlacingItemByItemId(self._info.itemId)
	self:setView(self._placingConfig.propUrl, nil, nil,self._placingConfig.isSpine)
	self:setPos(self._info.posX, self._info.posY,self._info.posZ)
end

--更新数据
function TransformSkinUnit:updatInfo(info)
	self._info = self:parseInfo(info)
	self:setPos(self._info.posX, self._info.posY,self._info.posZ)
end
function TransformSkinUnit:update(info)
	self:updatInfo(info)
end

--解析数据
function TransformSkinUnit:parseInfo(info)
	if self._isEditor then
		return info
	end
	local tempInfo = {}
	tempInfo.uniqueId = info.uniqueId
	tempInfo.itemId = info.itemId
	tempInfo.posStr = info.posStr
	local posList = string.split(info.posStr,',')

	if info.actId and info.actId > 0 then 
		tempInfo.actId = info.actId
		tempInfo.params = info.params
		tempInfo.posX = tonumber(posList[1])
		tempInfo.posY = tonumber(posList[3])
		tempInfo.posZ = tonumber(posList[2])
		tempInfo.nextRefreshTime = info.nextRefreshTime
		tempInfo.remainTime = info.nextRefreshTime <= ServerTime.now() and 999999999 or 0
	else 
		tempInfo.posX = tonumber(posList[1])
		tempInfo.posY = tonumber(posList[2])
		tempInfo.posZ = (posList[3] == nil or posList[3] == "") and nil or tonumber(posList[3])
		tempInfo.remainTime = info.remainTime
	end
	
	return tempInfo
end

--获取数据
function TransformSkinUnit:getInfo()
	return self._info
end


--设置位置
function TransformSkinUnit:setPos(x, y, z)
	self:setNavEnable(false)
	-- self.posCtrl:setPos(x, y)
	local hasZ = z ~= nil
	if VirtualCameraMgr.instance:is3DScene() then
		if not hasZ then
			z = GameUtils.GetPosYOnGround(x, y)
		end
		-- tfutil.SetY(self.go, z)
		self.posCtrl:setPos(x, y, z)
		self:setNavEnable(not hasZ)
	else
		self.posCtrl:setPos(x, y, z)
	end
end

function TransformSkinUnit:setView(url, onLoadedCallback, callbackTarget, clickable,isSpine)
	self.loadedHandler = handler(onLoadedCallback, callbackTarget)
	self.callbackTarget = callbackTarget
	self.clickable = clickable
	
	self._isSpine = isSpine
	if isSpine then
		self.view:setBody(url, false)
	else
		self._prefabLoader:load(url, self.onLoaded,self)
	end
end

function TransformSkinUnit:onLoaded()
	if self._isSpine then
		self.viewGo = self.view:getInst()
	else
		self.viewGo = self._prefabLoader:getInst()
	end
	
	if not VirtualCameraMgr.instance:is3DScene() then
		self._userPlayer = SceneManager.instance:getCurScene():getUserPlayer()
		self._userMover = self._userPlayer.mover
		self._userMover:addListener(UnitNotify.PosChanged, self.onPosChanged, self)
	else
		SceneManager.instance:getCurScene().triggerController:addCollideTrigger(goutil.findChild(self.viewGo,"triggerGo"),nil,self._onCollideTriggerEnter,self,true)
	end
	
	--拖动禁用
	TransformSkinController.instance:registerLocalNotify(TransformSkinNotify.OpSettingBeginDrag, self._onBeginGlobalDrag, self)
	TransformSkinController.instance:registerLocalNotify(TransformSkinNotify.OpSettingDrag, self._onGlobalDrag, self)
	TransformSkinController.instance:registerLocalNotify(TransformSkinNotify.OpSettingEndDrag, self._onEndGlobalDrag, self)
	
	if self.loadedHandler then
		self.loadedHandler(self)
	end
	
	--self.go:SetActive(true)
	if self._placingConfig.viewGoPos then
		self:setViewGoOffset(self._placingConfig.viewGoPos[1],self._placingConfig.viewGoPos[2],self._placingConfig.viewGoPos[3])
	end
	
	self:_loadEffect()
end

--释放
function TransformSkinUnit:onReset()
	if not VirtualCameraMgr.instance:is3DScene() then
		self._userMover:removeListener(UnitNotify.PosChanged, self.onPosChanged, self)
	else
		if self.viewGo then
			SceneManager.instance:getCurScene().triggerController:removeCollideTrigger(goutil.findChild(self.viewGo,"triggerGo"))
		end
	end
	
	TransformSkinController.instance:unregisterLocalNotify(TransformSkinNotify.OpSettingBeginDrag, self._onBeginGlobalDrag, self)
	TransformSkinController.instance:unregisterLocalNotify(TransformSkinNotify.OpSettingDrag, self._onGlobalDrag, self)
	TransformSkinController.instance:unregisterLocalNotify(TransformSkinNotify.OpSettingEndDrag, self._onEndGlobalDrag, self)
	
	self.viewGo = nil
	self._lastSqrDis = nil
	self:setSearchRange(nil)
	self._prefabLoader:clear()
	self.view:clear()
	self.view:setDirection(UnitSpineDir.Default)
	self.view:setDefaultAnimState(nil)
	removetimer(self._delayRemoveUnit,self)
end

-------------------------------------- 碰撞相关 --------------------------------------
--玩家位置改变。（2D场景时检测玩家是否进入触发范围
function TransformSkinUnit:onPosChanged()
	local userPos = self._userPlayer.go.transform.position
	local mPos = self.go.transform.position
	local sqrDis = (mPos - userPos):SqrMagnitude()
	local range = self._searchRange == nil and 1 or self._searchRange
	if self._lastSqrDis == nil then
		self._lastSqrDis = sqrDis
		return
	end
	if sqrDis <= (range * range) and self._lastSqrDis > (range * range) then
		self:_onCollideTriggerEnter(true,nil,nil,self._userPlayer)
	end
	self._lastSqrDis = sqrDis
end
--设置检测范围
function TransformSkinUnit:setSearchRange(range)
	self._searchRange = range
end
--触发器：触发进入或者退出
function TransformSkinUnit:_onCollideTriggerEnter(isEnter,triggerGO,properties,unit)
	if self._isEditor then
		return
	end
	if not isEnter or unit == nil then
		return
	end
	if unit.id ~= UserInfo.userId then
		return
	end
	if not self:isCanTransform(true) then
		return
	end
	if self._info.remainTime <= 0 then
		return
	end
	
	if self._placingConfig.type == 1 then
		local skinName = TransformSkinController.instance:returnRandomSkinName(self._info.itemId)
		local skinIndex = TransformSkinController.instance:returnRandomSkinIndex(self._info.itemId)
		local info = {self._info.uniqueId,0,self._info.itemId,self._info.posX,self._info.posY,skinName or "",skinIndex}
		TransformSkinController.instance:beginTransformSkin(info)
	end
end
--变身
function TransformSkinUnit:transformFinish()
	--self.go:SetActive(false)
	self._info.remainTime = 0
end
--是否可以变身
function TransformSkinUnit:isCanTransform(showTips)
	--已经处于变身状态
	local isAtTransformState = TransformSkinController.instance:getTransformSkinState()
	if isAtTransformState then
		return false
	end
	
	--行走且不处于其他状态下才能变身
	local actionMgr = SceneManager.instance:getCurScene().actionMgr
	if actionMgr == nil then
		return false
	end
	local curActions = actionMgr.curActions
	if curActions == nil or #curActions == 0 or #curActions > 1 then
		return false
	end
	local isInWalkAction = SceneManager.instance:getCurScene().actionMgr:hasAction(SceneActionType.Walk)
	local isOpJoystick = SceneManager.instance:getCurScene().actionMgr:hasAction(SceneActionType.Joystick)
	if not isInWalkAction and not isOpJoystick then
		return false
	end
	
	--该道具已经不存在
	if not self._info or self._info.remainTime <= 0 then
		return
	end

	--不在CD
	local cdTime = TransformSkinController.instance:getUserCD()
	if cdTime ~= nil and cdTime > 0 then
		if showTips then
			FlyTextManager.instance:showFlyText(lang("距离下一次可以变身时间：{1}s",cdTime))
		end
		return false
	end
	return true
end

-------------------------------------- 特效 --------------------------------------
--加载特效
function TransformSkinUnit:_loadEffect()
	if not self._isEditor then
		--爆炸特效
		local skinConfig = TransformSkinConfig.getTransformSkinByItemId(self._info.itemId)
		if skinConfig.propDisappearUrl then
			self.effectLoader:getSlot(EffectLoader.OnEffectLoaded):load(skinConfig.propDisappearUrl,self._loadExplodeEffectFinish,self)
		end
	end
	--常驻特效
	if self._placingConfig.propEffectUrl then
		self.effectLoader:getSlot(EffectLoader.Pose):load(self._placingConfig.propEffectUrl,self._loadSparkEffectFinish,self)
	end
end
--爆炸特效加载完成
function TransformSkinUnit:_loadExplodeEffectFinish()
	local viewPos = self.viewGo.transform.position
	local go = self.effectLoader:getSlot(EffectLoader.OnEffectLoaded):getInst()
	GameUtils.setPos(go,viewPos.x,viewPos.y,viewPos.z)
	self._disappearEffectGo = goutil.findChild(go,"Effect_01"):GetComponent("ParticleSystem")
end
--常驻特效加载完成
function TransformSkinUnit:_loadSparkEffectFinish()
	self._sparkEffectGo = self.effectLoader:getSlot(EffectLoader.Pose):getInst()
	self._sparkEffectGo:SetActive(true)
	local effect = goutil.findChild(self.viewGo,"effect")
	if effect then
		GameUtils.setLocalPos(self._sparkEffectGo,0,0,0)
	end
end
--假如有消失特效则显示，没有直接移除此unit
function TransformSkinUnit:showDisappearEffect()
	local unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
	local body = self.viewGo
	if not body or not self._disappearEffectGo then
		unitMgr:removeUnit(SceneUnitType.TransformSkinUnit, self.id)
		return
	end
	if self._sparkEffectGo then
		self._sparkEffectGo:SetActive(false)
	end
	body:SetActive(false)
	self._disappearEffectGo:Play()
	removetimer(self._delayRemoveUnit,self)
	settimer(3,self._delayRemoveUnit,self,false)
end
function TransformSkinUnit:_delayRemoveUnit()
	local unitMgr = SceneManager.instance:getCurScene():getUnitMgr()
	unitMgr:removeUnit(SceneUnitType.TransformSkinUnit, self.id)
end

-------------------------------------- 拖动 --------------------------------------
--拖动
function TransformSkinUnit:_onBeginGlobalDrag(pos)
	if not self._isEditor then
		return
	end
	if not self.viewGo then
		return
	end
	self._lastPos = pos
end
function TransformSkinUnit:_onGlobalDrag(pos)
	if not self._isEditor then
		return
	end
	if not self.viewGo then
		return
	end
	local curX,curY,curZ = self:getPos()
	
	local lastMouseWorldPos = GameUtils.get3DWorldPosFromScreen(self._lastPos, self._mainCamera, self._refGOWorldPos)
	local curMouseWorldPos = GameUtils.get3DWorldPosFromScreen(pos, self._mainCamera, self._refGOWorldPos)
	local offset = curMouseWorldPos - lastMouseWorldPos
	local nextPosX = curX + offset.x
	local nextPosY = curY + offset.y
	local walkable = self._curScene.mapMgr:isWalkable(nextPosX, nextPosY)
	if walkable then
		self:setPos(nextPosX,nextPosY,self._info.posZ)
		TransformSkinController.instance:localNotify(TransformSkinNotify.OpUnitDrag, self.go.transform.position)
		self._lastPos = pos
	end
end
function TransformSkinUnit:_onEndGlobalDrag(pos)
	if not self._isEditor then
		return
	end
	if not self.viewGo then
		return
	end
end

--检测存在剩余时间
function TransformSkinUnit:loopCheckExistRemainTime()
	if self._isEditor then
		return
	end

	if self._info.actId and self._info.actId > 0 then 
		
	else
		local remainTime = self._info.remainTime
		if remainTime == nil or remainTime <= 0 then
			return
		end
		self._info.remainTime = remainTime - 1
	end	
end

function TransformSkinUnit:setViewGoOffset(x,y,z)
	if self.viewGo == nil then
		return
	end
	GameUtils.setLocalPos(self.viewGo,x,y,z)
end

return TransformSkinUnit