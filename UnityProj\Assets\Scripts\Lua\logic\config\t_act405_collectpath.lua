-- {excel:405赛季集卡活动.xlsx, sheetName:export_卡包获取途径}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act405_collectpath", package.seeall)

local title = {activityId=1,id=2,taskName=3,iconUrl=4,taskContent=5,free=6,jumpId=7,jumpActivityId=8}

local dataList = {
	{1806, 1, "日常", "image/task/objectiveicon/mubiao.png", "完成日常任务，可获得幽灵卡盒。", true, 1, 0},
	{1806, 2, "狂欢周", "image/task/objectiveicon/jksjtc_icon_youxiji.png", "参与游戏狂欢周，可获得幽灵卡盒。", true, 2, 0},
	{1806, 3, "藏品", "image/task/objectiveicon/jksjtc_icon_lihe.png", "购买藏品礼包，可获得幽灵卡盒。", false, 3, 0},
	{1806, 4, "百货", "image/task/objectiveicon/jksjtc_icon_shop.png", "百货商店里也可能有幽灵卡盒。", false, 4, 0},
	{1806, 5, "生活", "image/task/objectiveicon/jksjtc_icon_shenghuo.png", "参与奥比生活，可获得幽灵卡盒。", false, 5, 0},
	{1949, 1, "日常", "image/task/objectiveicon/mubiao.png", "完成日常任务，可获得萌二卡盒。", true, 1, 0},
	{1949, 2, "狂欢周", "image/task/objectiveicon/jksjtc_icon_youxiji.png", "参与游戏狂欢周，可获得萌二卡盒。", true, 2, 0},
	{1949, 3, "周末福利", "image/task/objectiveicon/jksjtc_icon_jinkuai.png", "领取周末福利，可获得萌二卡盒。", true, 7, 57},
	{1949, 4, "快乐节", "image/task/objectiveicon/jksjtc_icon_dazhuti_33.png", "参与快乐节活动，可获得萌二卡盒。", true, 6, 2103},
	{1949, 5, "草莓园", "image/task/objectiveicon/jksjtc_icon_zhongzhuti_33.png", "参与草莓园活动，可获得萌二卡盒。", true, 6, 2105},
	{1949, 6, "满月节", "image/task/objectiveicon/jksjtc_icon_zhongzhuti_32.png", "参与满月节活动，可获得萌二卡盒。", true, 6, 2056},
	{1949, 7, "藏品", "image/task/objectiveicon/jksjtc_icon_lihe.png", "购买藏品礼包，可获得萌二卡盒。", false, 3, 0},
	{1949, 8, "百货", "image/task/objectiveicon/jksjtc_icon_shop.png", "百货商店里也可能有萌二卡盒。", false, 4, 0},
	{1949, 9, "生活", "image/task/objectiveicon/jksjtc_icon_shenghuo.png", "参与奥比生活，可获得萌二卡盒。", false, 5, 0},
	{2126, 1, "日常", "image/task/objectiveicon/mubiao.png", "完成日常任务，可获得泡芙卡盒。", true, 1, 0},
	{2126, 2, "狂欢周", "image/task/objectiveicon/jksjtc_icon_youxiji.png", "参与游戏狂欢周，可获得泡芙卡盒。", true, 2, 2172},
	{2126, 3, "狂欢周", "image/task/objectiveicon/jksjtc_icon_youxiji.png", "参与游戏狂欢周，可获得泡芙卡盒。", true, 2, 2128},
	{2126, 4, "捕鱼狂欢周", "image_item/16/16000702.png", "参与捕鱼狂欢周，可获得泡芙卡盒。", true, 2, 2171},
	{2126, 5, "周末福利", "image/task/objectiveicon/jksjtc_icon_jinkuai.png", "领取周末福利，可获得泡芙卡盒。", true, 7, 57},
	{2126, 6, "人偶屋", "image/task/objectiveicon/jksjtc_icon_dazhuti_34.png", "参与人偶屋活动，可获得泡芙卡盒。", true, 6, 2139},
	{2126, 7, "草莓园", "image/task/objectiveicon/jksjtc_icon_zhongzhuti_33.png", "参与草莓园活动，可获得泡芙卡盒。", true, 6, 2105},
	{2126, 8, "藏品", "image/task/objectiveicon/jksjtc_icon_lihe.png", "购买藏品礼包，可获得泡芙卡盒。", false, 3, 0},
	{2126, 9, "百货", "image/task/objectiveicon/jksjtc_icon_shop.png", "百货商店里也可能有泡芙卡盒。", false, 4, 0},
	{2243, 1, "日常", "image/task/objectiveicon/mubiao.png", "完成日常任务，可获得尚文卡盒。", true, 1, 0},
	{2243, 2, "狂欢周", "image/task/objectiveicon/jksjtc_icon_youxiji.png", "参与游戏狂欢周，可获得尚文卡盒。", true, 2, 2247},
	{2243, 3, "捕鱼狂欢周", "image_item/16/16000702.png", "参与捕鱼狂欢周，可获得尚文卡盒。", true, 2, 2246},
	{2243, 4, "周末福利", "image/task/objectiveicon/jksjtc_icon_jinkuai.png", "领取周末福利，可获得尚文卡盒。", true, 7, 57},
	{2243, 5, "花灯节", "image/task/objectiveicon/jksjtc_icon_zhongzhuti_37.png", "参与花灯节活动，可获得尚文卡盒。", true, 6, 2279},
	{2243, 6, "藏品", "image/task/objectiveicon/jksjtc_icon_lihe.png", "购买藏品礼包，可获得尚文卡盒。", false, 3, 0},
	{2243, 7, "百货", "image/task/objectiveicon/jksjtc_icon_shop.png", "百货商店里也可能有尚文卡盒。", false, 4, 0},
	{2371, 1, "日常", "image/task/objectiveicon/mubiao.png", "完成日常任务，可获得米花卡盒。", true, 1, 0},
	{2371, 2, "狂欢周", "image/task/objectiveicon/jksjtc_icon_youxiji.png", "参与游戏狂欢周，可获得米花卡盒。", true, 2, 2375},
	{2371, 3, "捕鱼狂欢周", "image_item/16/16000702.png", "参与捕鱼狂欢周，可获得米花卡盒。", true, 2, 2374},
	{2371, 4, "周末福利", "image/task/objectiveicon/jksjtc_icon_jinkuai.png", "领取周末福利，可获得米花卡盒。", true, 7, 57},
	{2371, 5, "藏品", "image/task/objectiveicon/jksjtc_icon_lihe.png", "购买藏品礼包，可获得米花卡盒。", false, 3, 0},
	{2371, 6, "百货", "image/task/objectiveicon/jksjtc_icon_shop.png", "百货商店里也可能有米花卡盒。", false, 4, 0},
	{2462, 1, "日常", "image/task/objectiveicon/mubiao.png", "完成日常任务，可获得米花卡盒。", true, 1, 0},
	{2462, 2, "狂欢周", "image/task/objectiveicon/jksjtc_icon_youxiji.png", "参与游戏狂欢周，可获得米花卡盒。", true, 2, 2466},
	{2462, 3, "捕鱼狂欢周", "image_item/16/16000702.png", "参与捕鱼狂欢周，可获得米花卡盒。", true, 2, 2465},
	{2462, 4, "周末福利", "image/task/objectiveicon/jksjtc_icon_jinkuai.png", "领取周末福利，可获得米花卡盒。", true, 7, 57},
	{2462, 5, "藏品", "image/task/objectiveicon/jksjtc_icon_lihe.png", "购买藏品礼包，可获得米花卡盒。", false, 3, 0},
	{2462, 6, "百货", "image/task/objectiveicon/jksjtc_icon_shop.png", "百货商店里也可能有米花卡盒。", false, 4, 0},
}

local t_act405_collectpath = {
	[1806] = {
		[1] = dataList[1],
		[2] = dataList[2],
		[3] = dataList[3],
		[4] = dataList[4],
		[5] = dataList[5],
	},
	[1949] = {
		[1] = dataList[6],
		[2] = dataList[7],
		[3] = dataList[8],
		[4] = dataList[9],
		[5] = dataList[10],
		[6] = dataList[11],
		[7] = dataList[12],
		[8] = dataList[13],
		[9] = dataList[14],
	},
	[2126] = {
		[1] = dataList[15],
		[2] = dataList[16],
		[3] = dataList[17],
		[4] = dataList[18],
		[5] = dataList[19],
		[6] = dataList[20],
		[7] = dataList[21],
		[8] = dataList[22],
		[9] = dataList[23],
	},
	[2243] = {
		[1] = dataList[24],
		[2] = dataList[25],
		[3] = dataList[26],
		[4] = dataList[27],
		[5] = dataList[28],
		[6] = dataList[29],
		[7] = dataList[30],
	},
	[2371] = {
		[1] = dataList[31],
		[2] = dataList[32],
		[3] = dataList[33],
		[4] = dataList[34],
		[5] = dataList[35],
		[6] = dataList[36],
	},
	[2462] = {
		[1] = dataList[37],
		[2] = dataList[38],
		[3] = dataList[39],
		[4] = dataList[40],
		[5] = dataList[41],
		[6] = dataList[42],
	},
}

t_act405_collectpath.dataList = dataList
local mt
mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act405_collectpath