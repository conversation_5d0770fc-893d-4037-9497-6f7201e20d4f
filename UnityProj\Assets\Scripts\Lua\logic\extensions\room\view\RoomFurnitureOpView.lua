module("logic.extensions.room.view.RoomFurnitureOpView", package.seeall)
local RoomFurnitureOpView = class("RoomFurnitureOpView", RoomDragOpView)

RoomFurnitureOpView.Url = "ui/scene/room/roomfurnitureop.prefab"

function RoomFurnitureOpView:ctor()
	RoomFurnitureOpView.super.ctor(self)
	self._furnitureUnit = nil
	self._isShow = false
	self._view = nil
	self._canOK = false
end

function RoomFurnitureOpView:show(furnitureUnit, furnitureMO, canOK)
	print("1 RoomFurnitureOpView:show", furnitureMO, furnitureUnit, canOK, self._isShow, self._furnitureUnit == furnitureUnit)
	if self._isShow and self._furnitureUnit == furnitureUnit then
		return
	end
	print("2 RoomFurnitureOpView:show", furnitureMO, furnitureUnit, canOK)
	self._furnitureUnit = furnitureUnit
	self._furnitureMO = furnitureMO
	self._isShow = true
	local scene = SceneManager.instance:getCurScene()
	if self._view == nil then
		self:setView(scene.stage:getInstance(RoomFurnitureOpView.Url))
		goutil.addChildToParent(self._view, SceneRoot2DMgr.instance:getRoot(SceneRoot2DType.NameBar))
		self._followComp = GameUtils.addSceneFollowComp(self._view)
		self:_buildUI()
	end
	goutil.setActive(self._view, true)
	self:_updateBtnState()
	
	local access = self._furnitureMO:getFoodAccess()
	local oriAccess = self._tgGroup:getIndex()
	self._tgGroup:clickViews(true, access == nil and(oriAccess == nil and 4 or oriAccess) or access)
	
	if not RoomTaskHelper.instance:doingNewTask() then
		tfutil.SetScale(self._view, 0.2)
		local tween = self._view.transform:DOScale(scene.tipsMgr.scale, 0.2)
		tween:SetEase(DG.Tweening.Ease.OutElastic)
	end
	self:addListener()
	self:_followUnitMove(canOK)
end

function RoomFurnitureOpView:followGo()
	if self._furnitureUnit then
		return self._furnitureUnit.go
	end
end

function RoomFurnitureOpView:hide()
	print("3 RoomFurnitureOpView:hide", debug.traceback())
	self._furnitureUnit = nil
	self._isShow = false
	if self._view then
		goutil.setActive(self._view, false)
	end
	self:removeListener()
	self._furnitureMO = nil
end

function RoomFurnitureOpView:destroy()
	self:hide()
	if self._view then
		goutil.destroy(self._view)
	end
	self._view = nil
end

function RoomFurnitureOpView:_buildUI()
	RoomFurnitureOpView.super._buildUI(self)
	self._foodLimitView = goutil.findChild(self._view, "foodLimit")
	self._clothesStageLimit = goutil.findChild(self._view, "astrict")
	self._txtClothesStage = goutil.findChildTextComponent(self._view, "astrict/txtAstrict")
	self._tgGroup = ToggleGroup.New(handler(self._onSelectFoodLimit, self))
	self._foodArrow = goutil.findChild(self._view, "btnFoodLimit/img")
	for i = 1, 4 do
		self._tgGroup:addView(goutil.findChild(self._foodLimitView, "tgLimit" .. i))
	end
	Framework.UIGlobalTouchTrigger.Get(self._foodLimitView):AddIgnoreTargetListener(self._onClickFoodLimit, self)
end

function RoomFurnitureOpView:_updateBtnState()
	if not self._furnitureMO then return end
	local isWall = self._furnitureMO:getCoordType() == FurnitureType.CoordType.Wall
	
	local takeOffActive = HouseModel.instance:getRoomParams(RoomParams.OpView_BtnTakeOff_Active)
	local takeOffActive2 = HouseModel.instance:getRoomParams(RoomParams.OpView_BtnTakeOff2_Active)
	local isOwn = self._furnitureMO:getIsOwn()
	goutil.setActive(self:getButton("btnDelete").gameObject, takeOffActive)
	goutil.setActive(self:getButton("btnTakeDown").gameObject, takeOffActive2)
	
	local foodEnable = HouseModel.instance:getRoomParams(RoomParams.Food_Enable)
	goutil.setActive(self._btnFoodLimit.gameObject, foodEnable and self._furnitureMO:isFood())
	
	local soldEnable = HouseModel.instance:getRoomParams(RoomParams.OpView_BtnSold_Enable)
	self:getButton("btnDelete").btn.interactable = isOwn and self._furnitureUnit.handler:canTakeBack()
	self:getButton("btnSale").btn.interactable = isOwn and soldEnable and self._furnitureUnit.handler:canSell()
	self:getButton("btnInfo").btn.interactable = self._furnitureUnit.handler:canShowInfo()
	self:getButton("btnCancel").btn.interactable = self._furnitureUnit.handler:canCancel()
	self:getButton("btnMirror").btn.interactable = RoomSceneStage.TestPlaceFurniture or(not isWall and self._furnitureUnit.handler:canMirror())
	
	local isClothesStage = FurnitureConfig.getFurnitureDefine(self._furnitureMO.id).subType == FurnitureType.ClothesStage
	self._clothesStageLimit:SetActive(isClothesStage)
	if isClothesStage then
		local maxCount = 0
		if HouseModel.instance:isInBlock() then
			maxCount = tonumber(GameUtils.getCommonConfig("MannequinStreetMax"))
		elseif HouseModel.instance:isInIsland() then
			maxCount = tonumber(GameUtils.getCommonConfig("MannequinIslandMax"))
		else
			maxCount = tonumber(GameUtils.getCommonConfig("MannequinHouseMax"))
		end
		local curCount = self:_getClothesStageCount()
		local color =(curCount <= maxCount) and "FFFFFF" or "ff5c7b"
		local msg = lang("摆放上限<color=#{1}>{2}</color>/{3}", color, curCount, maxCount)
		self._txtClothesStage.text = msg
	end
end

function RoomFurnitureOpView:_getClothesStageCount()
	local count = 0
	local allFurs = RoomEditFurnitureModel.instance:getEditFurnitureList()
	for _, mo in ipairs(allFurs) do
		if FurnitureConfig.getFurnitureDefine(mo.id).subType == FurnitureType.ClothesStage then
			count = count + 1
		end
	end
	return count
end

function RoomFurnitureOpView:_followUnitMove(canOK)
	local offset
	if self._furnitureMO:getDefine().ext and self._furnitureMO:getDefine().ext.operaOffset then
		offset = self._furnitureMO:getDefine().ext.operaOffset:splitToNumber(",")
	end
	if offset == nil then
		local part = self._furnitureMO:getDefine():getPart()
		if part.operaOffset then
			offset = {part.operaOffset.x, part.operaOffset.y}
		end
	end
	if offset == nil then
		offset = {0, 0}
	end
	RoomFurnitureOpView.super._followUnitMove(self, canOK, Vector3.New(offset[1], offset[2], 0))
	self._canOK = canOK
	-- self:setBtuttonEnable("btnOK", self._canOK)
	self:getButton("btnOK").btn.interactable = self._canOK
end

function RoomFurnitureOpView:_onClickOK()
	self:_setFoodLimitActive(false)
	if not self:getButtonEnable("btnOK") then return end
	if not self._isShow or self._isDraging then return end
	if not self._canOK then return end
	GlobalDispatcher:dispatch(GlobalNotify.OnEditFurnitureOK)
	RoomEdit.instance:tryOkCurEditFurniture(true)
end

function RoomFurnitureOpView:_onClickSale()
	if not self:getButtonEnable("btnSale") then return end
	self:_setFoodLimitActive(false)
	if not self._isShow or self._isDraging then return end
	GlobalDispatcher:dispatch(GlobalNotify.OnSellFurniture)
	self._furnitureUnit.handler:preSell(self.showSellConfirmer, self)
end

function RoomFurnitureOpView:showSellConfirmer()
	ItemService.instance:openSoldPanel(self._furnitureMO.id, true, false, self._sureToSell, self, true)
end

function RoomFurnitureOpView:_sureToSell(isOk)
	if isOk then
		RoomController.instance:localNotify(RoomNotifyName.OnClickSale)
	end
end

function RoomFurnitureOpView:_onClickDelete()
	if not self:getButtonEnable("btnDelete") and not self:getButtonEnable("btnTakeDown") then return end
	self:_setFoodLimitActive(false)
	if not self._isShow or self._isDraging then return end
	RoomController.instance:localNotify(RoomNotifyName.OnClickDelete)
end

function RoomFurnitureOpView:_onClickMirror()
	if not self:getButtonEnable("btnMirror") then return end
	self:_setFoodLimitActive(false)
	if not self._isShow or self._isDraging then return end
	RoomController.instance:localNotify(RoomNotifyName.MirrorFurniture)
end

function RoomFurnitureOpView:_onClickCancel()
	if not self:getButtonEnable("btnCancel") then return end
	self:_setFoodLimitActive(false)
	if not self._isShow or self._isDraging then return end
	RoomController.instance:localNotify(RoomNotifyName.CancelOpFurniture)
end

function RoomFurnitureOpView:_onClickInfo()
	if not self:getButtonEnable("btnInfo") then return end
	self:_setFoodLimitActive(false)
	if not self._isShow or self._isDraging then return end
	ViewFacade.showItemInfo(self._furnitureMO.id)
end

function RoomFurnitureOpView:_onClickFoodLimit()
	self:_setFoodLimitActive(not self._foodLimitView.activeSelf)
end

function RoomFurnitureOpView:_onSelectFoodLimit(index, select)
	self:_setFoodLimitActive(false)
	self._furnitureMO:setFoodAccess(index)
	goutil.findChildTextComponent(self._btnFoodLimit, "txt").text =
	goutil.findChildTextComponent(self._tgGroup:getViews() [self._tgGroup:getIndex()], "imgSelect/txt").text
end

function RoomFurnitureOpView:_setFoodLimitActive(active)
	local scale = active and 1 or - 1
	tfutil.SetScaleY(self._foodArrow, scale)
	goutil.setActive(self._foodLimitView, active)
end

return RoomFurnitureOpView 