module("logic.extensions.sea.model.SeaModel", package.seeall)

local SeaModel = class("SeaModel", BaseModel)

function SeaModel:init()
    self._createUUID = 0
    self._inputInfo = nil
    self._isFishing = nil
end

function SeaModel:clear()
    self._createUUID = nil
    self._inputInfo = nil
    self._isFishing = nil
end

function SeaModel:createUUID()
    local uuid = self._createUUID
    self._createUUID = uuid + 1
    return uuid
end

function SeaModel:setInputInfo(inputInfo)
    self._inputInfo = inputInfo
end
function SeaModel:getInputInfo()
    return self._inputInfo
end

function SeaModel:setIsFishing(isFishing)
    self._isFishing = isFishing
end
function SeaModel:getIsFishing()
    return self._isFishing
end

function SeaModel:setPlayerInfo(playerInfo)
    self._playerInfo = playerInfo
end
function SeaModel:getPlayerInfo()
    return self._playerInfo
end

function SeaModel:setFishInfos(fishInfos)
    self._fishInfos = {}
    for _, fishInfo in ipairs(fishInfos) do
        self:setFishInfo(fishInfo.fishId, fishInfo)
    end
end
function SeaModel:setFishInfo(fishId, fishInfo)
    self._fishInfos[fishId] = fishInfo
end
function SeaModel:getFishInfo(fishId)
    return self._fishInfos[fishId]
end

function SeaModel:setRidingMountId(inMountId)
    print("setRidingMountId", inMountId)
    local saveId = inMountId or 0
    LocalStorage.instance:setValue(StorageKey.MountStatus, saveId)
    SeaController.instance:localNotify(SeaNotify.onChangeMounts)
end

function SeaModel:setSaveMountId(inMountId)
    local saveId = inMountId or 0
    LocalStorage.instance:setValue(StorageKey.MountSaveId,saveId)
    SeaController.instance:localNotify(SeaNotify.onChangeMounts)
end

function SeaModel:getMountState()
    local id = LocalStorage.instance:getValue(StorageKey.MountStatus, 0)
    return id
end

function SeaModel:getMountSaveId()
    local id = LocalStorage.instance:getValue(StorageKey.MountSaveId, 0)
    return id
end

function SeaModel:setRidingLandMountId(inMountId)
    print("setRidingLandMountId", inMountId)
    local saveId = inMountId or 0
    LocalStorage.instance:setValue(StorageKey.LandMountStatus, saveId)
    SeaController.instance:localNotify(SeaNotify.onChangeMounts)
end

function SeaModel:setSaveLandMountId(inMountId)
    local saveId = inMountId or 0
    LocalStorage.instance:setValue(StorageKey.LandMountSaveId,saveId)
    SeaController.instance:localNotify(SeaNotify.onChangeMounts)
end

function SeaModel:getLandMountState()
    local id = LocalStorage.instance:getValue(StorageKey.LandMountStatus, 0)
    return id
end

function SeaModel:getLandMountSaveId()
    local id = LocalStorage.instance:getValue(StorageKey.LandMountSaveId, 0)
    return id
end

function SeaModel:setPhotoPosition(seaPhotoId, unit)
    if self.photoPosition == nil then
        self.photoPosition = {}
    end
    self.photoPosition[seaPhotoId] = unit
end

function SeaModel:getPhotoPosition(seaPhotoId, unit)
    if self.photoPosition == nil then
        self.photoPosition = {}
    end
    return self.photoPosition[seaPhotoId]
end

function SeaModel:getPhotoPositions()
    return self.photoPosition
end

function SeaModel:clearPhotoPosition()
    self.photoPosition = nil
end

SeaModel.instance = SeaModel.New()

return SeaModel
