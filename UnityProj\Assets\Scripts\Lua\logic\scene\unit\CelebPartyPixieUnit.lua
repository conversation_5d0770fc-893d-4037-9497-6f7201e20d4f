module("logic.scene.unit.CelebPartyPixieUnit", package.seeall)

local CelebPartyPixieUnit = class("CelebPartyPixieUnit", StateObjUnit)

function CelebPartyPixieUnit:defineStateMachine()
    return {
        {
            stateType = "idle",
            interrupt = 0,
            target = self,
            handler = {
                enter = function()
                    self:play("idle", true)
                end
            }
        }
    }
end

function CelebPartyPixieUnit:defineFirstState()
    return "idle"
end

function CelebPartyPixieUnit:isHasPathFinding()
    return false
end

function CelebPartyPixieUnit:onSetInfo()
    self:setView(string.format("prefabs/celebparty/pixie/%s.prefab", self.info.res))
end

return CelebPartyPixieUnit
