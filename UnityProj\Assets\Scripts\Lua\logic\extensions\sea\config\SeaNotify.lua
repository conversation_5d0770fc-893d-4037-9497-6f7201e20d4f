module("logic.extensions.sea.config.SeaNotify", package.seeall)

local SeaNotify = {}

local id = 0
local function genId()
    id = id + 1
    return id
end

SeaNotify.onJoystickDown = genId()
SeaNotify.onJoystickUp = genId()
SeaNotify.onUpdateInput = genId()
SeaNotify.onAddSucTips = genId()
SeaNotify.onMainShow = genId()
SeaNotify.onMainHide = genId()
SeaNotify.onAimDone = genId()
SeaNotify.onAimInterrupt = genId()
SeaNotify.onAddArrowTips = genId()
SeaNotify.onRemoveArrowTips = genId()
SeaNotify.onAddArrowPosTips = genId()
SeaNotify.onRemoveArrowPosTips = genId()
SeaNotify.onEnterSceneFinished = genId()
SeaNotify.onFishSucc = genId()
SeaNotify.onFishFail = genId()

-- 科技树开始
SeaNotify.onClickTechTreeItem = genId()
SeaNotify.oUpgradeTech = genId()
SeaNotify.oUpgradeTechFinish = genId()

-- 剧情碎片
SeaNotify.onTriggerFragment = genId()

-- 任务
SeaNotify.onEnterTaskArea = genId()
SeaNotify.onExitTaskArea = genId()
SeaNotify.onTaskUpdate = genId()
SeaNotify.onTaskFinish = genId()
SeaNotify.onUpdateRobProgress = genId()
SeaNotify.onRescueGuide = genId()
SeaNotify.onClickTaskGo = genId()
SeaNotify.onCloseRobView = genId()
SeaNotify.onShowTaskProcessGo = genId()
SeaNotify.onHideTaskProcessGo = genId()
SeaNotify.onSeaTaskExpiration = genId()

-- 活动
SeaNotify.onAct438Start = genId()
SeaNotify.onAct438End = genId()
SeaNotify.onAct438Update = genId()
SeaNotify.onAct441Start = genId()
SeaNotify.onAct441End = genId()
SeaNotify.onAct441Update = genId()

-- buff
SeaNotify.onBuffUpdate = genId()

-- 坐骑
SeaNotify.onMountHandlerChange = genId()
SeaNotify.onMountChange = genId()

-- 科技树
SeaNotify.onSeaTechTreeUpgrade = genId()
SeaNotify.onSeaTechTreeUpgradeFinish = genId()

--生物鱼
SeaNotify.onEnterBiotaArea = genId()
SeaNotify.onExitBiotaArea = genId()
--奇观
SeaNotify.onEnterSeaPhoto = genId()
SeaNotify.onExitSeaPhoto = genId()

--炫耀
SeaNotify.onClickFinishItem = genId()

--Boss
SeaNotify.onEnterBossArea = genId()
SeaNotify.onExitBossArea = genId()
SeaNotify.onReleaseSkills = genId()
SeaNotify.onBossInfoUpdate = genId()
--前端模拟受伤效果，但实际血条不动
SeaNotify.onBossInjuredEffect = genId()
SeaNotify.onBossMove = genId()
SeaNotify.onBossDeath = genId()
--更新玩家boss的状态，例如击昏，和禁锢
SeaNotify.onBossPlayerState = genId()
--玩家状态推送，打断数和禁锢数目更新
SeaNotify.onPlayerStateNumUpdateView = genId()
SeaNotify.onPlayerStateNumUpdateCloseView = genId()
SeaNotify.onBossWeak = genId()
SeaNotify.onBossActivityStart = genId()
SeaNotify.onBossActivityEnd = genId()
SeaNotify.onBossDeathViewShow = genId()
SeaNotify.onBossTest = genId()
SeaNotify.onChangeMounts = genId()
return SeaNotify

--  SeaController.instance:localNotify(SeaNotify.onBossDeath)