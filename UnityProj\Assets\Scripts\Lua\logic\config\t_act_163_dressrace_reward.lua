-- {excel:163时装周大赛.xlsx, sheetName:export_时装周大赛奖励}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act_163_dressrace_reward", package.seeall)

local title = {score=1,type=2,reward=3,points=4}

local dataList = {
	{0, 1, {{count=10,id=16000809}}, 2},
	{1, 1, {{count=10,id=16000809}}, 3},
	{2, 1, {{count=20,id=16000809}}, 4},
	{3, 1, {{count=20,id=16000809}}, 5},
	{4, 1, {{count=30,id=16000809}}, 6},
	{5, 1, {{count=30,id=16000809}}, 8},
	{0, 2, {{count=10,id=16000809}}, 2},
	{1, 2, {{count=10,id=16000809}}, 3},
	{2, 2, {{count=20,id=16000809}}, 4},
	{3, 2, {{count=20,id=16000809}}, 5},
	{4, 2, {{count=30,id=16000809}}, 6},
	{5, 2, {{count=30,id=16000809}}, 8},
	{6, 2, {{count=30,id=16000809}}, 8},
}

local t_act_163_dressrace_reward = {
	[0] = {
		[1] = dataList[1],
		[2] = dataList[7],
	},
	[1] = {
		[1] = dataList[2],
		[2] = dataList[8],
	},
	[2] = {
		[1] = dataList[3],
		[2] = dataList[9],
	},
	[3] = {
		[1] = dataList[4],
		[2] = dataList[10],
	},
	[4] = {
		[1] = dataList[5],
		[2] = dataList[11],
	},
	[5] = {
		[1] = dataList[6],
		[2] = dataList[12],
	},
	[6] = {
		[2] = dataList[13],
	},
}

t_act_163_dressrace_reward.dataList = dataList
local mt
if Act163DresssraceRewardDefine then
	mt = {
		__cname =  "Act163DresssraceRewardDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act163DresssraceRewardDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act_163_dressrace_reward