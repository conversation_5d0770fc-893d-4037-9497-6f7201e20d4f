-- {excel:N扭蛋.xlsx, sheetName:export_自选主题返场扭蛋}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_lottery_choose", package.seeall)

local title = {activityId=1,lotteryPool=2}

local dataList = {
	{1898, {240,303}},
	{1927, {304,305}},
	{2137, {304,108}},
	{2166, {305,208}},
	{2251, {306,307}},
	{2324, {309,137}},
	{2358, {310,253}},
	{2400, {311,171}},
	{2511, {300,313}},
}

local t_lottery_choose = {
	[1898] = dataList[1],
	[1927] = dataList[2],
	[2137] = dataList[3],
	[2166] = dataList[4],
	[2251] = dataList[5],
	[2324] = dataList[6],
	[2358] = dataList[7],
	[2400] = dataList[8],
	[2511] = dataList[9],
}

t_lottery_choose.dataList = dataList
local mt
if LotteryChooseDefine then
	mt = {
		__cname =  "LotteryChooseDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or LotteryChooseDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_lottery_choose