module("logic.scene.common.action.SceneActionType", package.seeall)
local SceneActionType = {}

SceneActionType.Walk = 1
SceneActionType.UseObj = 2
SceneActionType.Share = 3
SceneActionType.Pose = 4
SceneActionType.CancelAction = 5
SceneActionType.TakeShare = 6
SceneActionType.Teleport = 7
SceneActionType.StopWalk = 8
SceneActionType.Collect = 10
-- SceneActionType.Sleep = 11
SceneActionType.Talk = 12
SceneActionType.Joystick = 13
SceneActionType.StartGame = 14
SceneActionType.JoinGame = 15
SceneActionType.Fishing = 16
SceneActionType.LoadScene = 17
SceneActionType.CatchPet = 18
SceneActionType.TreasureHunt = 19
SceneActionType.Harvest = 20
SceneActionType.EditFurniture = 21
SceneActionType.IslanderEventFind = 22
SceneActionType.Jump = 23
SceneActionType.PlayingMusic = 24
SceneActionType.Read = 25
SceneActionType.SimpleInteraction = 26
SceneActionType.StartDoublePose = 27
SceneActionType.AcceptDoublePose = 28
SceneActionType.JoinSceneGame = 29
SceneActionType.Spa = 30
SceneActionType.HitIce = 31
SceneActionType.Throw = 32
-- SceneActionType.Shower = 33
SceneActionType.MultiInteraction = 34
SceneActionType.CableCar = 35
SceneActionType.DoThrow = 36
SceneActionType.SceneTrap = 37
SceneActionType.StartQueue = 38
SceneActionType.JoinQueue = 39
SceneActionType.Dandelion = 40
SceneActionType.TouchCouncilElf = 41
SceneActionType.DanceByHappy = 42
SceneActionType.CushionJoin = 43
SceneActionType.Parttime = 45
SceneActionType.happyBattle = 46
SceneActionType.PlayHandItem = 48
SceneActionType.LoadWerewolfSceneAction = 49
SceneActionType.WerewolfJoystick = 50
SceneActionType.PlaySoda = 52
SceneActionType.Navigator = 53
SceneActionType.StartElfBattlePractice = 54
SceneActionType.JoinElfBattlePractice = 55
SceneActionType.CommonGameJoystick = 56
SceneActionType.CouncilThrow = 60
SceneActionType.CouncilDrop = 61
SceneActionType.CouncilHoldBullet = 62
SceneActionType.CouncilFire = 63
SceneActionType.CouncilBossCollect = 64
SceneActionType.FurnitureInteract = 65
SceneActionType.AssemblyLineAction = 66
SceneActionType.AR = 67

SceneActionType.FurnitureStatusSync = 68
SceneActionType.MusicParty = 69
SceneActionType.Fly3D = 70
SceneActionType.GobangFurniture = 71
SceneActionType.GobangMatchWatchEntry = 72
SceneActionType.FlyJoystickAction = 74

SceneActionType.CatchInsect = 73
SceneActionType.AnniversaryGalaSceneGame = 75
SceneActionType.RainbowTrail = 76
SceneActionType.AnniversaryGalaColorGrid = 77
--周年庆排队
SceneActionType.AnniversaryGalaSite = 78
SceneActionType.AnniversaryGalaAskAttached = 79
SceneActionType.AnniversaryGalaAnswerAttached = 80
SceneActionType.AnniversaryGalaInCar = 81
SceneActionType.AnniversaryGalaStartHand = 82
SceneActionType.AnniversaryGalaAcceptHand = 83
SceneActionType.AnniversaryGalaJoinGodlike = 84
SceneActionType.AnniversaryGalaJoinFirework = 86
SceneActionType.WealthGodFollow = 87
SceneActionType.FurnitureSceneAction = 88
SceneActionType.MusicPartyWaveAction = 89
SceneActionType.CreatePortal = 90
SceneActionType.CityPartyTrain = 91
SceneActionType.TransformSkinPlayerAction = 92
SceneActionType.WeatherUmbrella = 93
SceneActionType.JoinWeatherUmbrella = 94
SceneActionType.StartRideMount = 95
SceneActionType.JoinRideMount = 96
SceneActionType.LaunchSeaDance = 98
SceneActionType.JoinSeaDance = 99
SceneActionType.WaitTogetherDance = 100
SceneActionType.SeaAim = 97
SceneActionType.SeaTask = 101
SceneActionType.SeaFishing = 102
SceneActionType.SeaShowFish = 103
SceneActionType.SeaBossState = 104
SceneActionType.SeaMural = 105
SceneActionType.Track = 106
SceneActionType.FurnitureCushionDonate = 107
SceneActionType.LongStreetBanquet = 108
SceneActionType.BonfireParty = 109
SceneActionType.BonfirePartyCall = 110
SceneActionType.CelebParty = 111
SceneActionType.StartDoubleFollow = 112
SceneActionType.AcceptDoubleFollow = 113

--这里配了还要去场景动作配置表配置
local config = {}
config[SceneActionType.Walk] = {WalkAction}
config[SceneActionType.UseObj] = {UseObjectAction}
config[SceneActionType.Share] = {StopWalkAction, ShareAction}
config[SceneActionType.Pose] = {StopWalkAction, PoseAction}
config[SceneActionType.TakeShare] = {WalkAction, TakeShareAction}
config[SceneActionType.CancelAction] = {StopWalkAction}
config[SceneActionType.Teleport] = {TeleportAction}
config[SceneActionType.StopWalk] = {StopWalkAction}
config[SceneActionType.Collect] = {TeleportAction, CollectAction}
-- config[SceneActionType.Sleep] = {WalkAction, SleepAction}
config[SceneActionType.Talk] = {WalkAction, TalkAction}
config[SceneActionType.Joystick] = {JoystickAction}
config[SceneActionType.StartGame] = {StopWalkAction, StartGameAction}
config[SceneActionType.JoinGame] = {WalkAction, JoinGameAction}
config[SceneActionType.Fishing] = {FishingAction}
config[SceneActionType.LoadScene] = {LoadSceneAction}
config[SceneActionType.CatchPet] = {StopWalkAction, CatchPetAction}
config[SceneActionType.TreasureHunt] = {StopWalkAction, TreasureHuntAction}
config[SceneActionType.Harvest] = {HarvestAction}
config[SceneActionType.EditFurniture] = {EditFurnitureAction}
config[SceneActionType.IslanderEventFind] = {WalkAction, TeleportAction, IslanderEventFindAction}
config[SceneActionType.Jump] = {JumpAction}
config[SceneActionType.PlayingMusic] = {WalkAction, TeleportAction, PlayingMusicAction}
config[SceneActionType.Read] = {StopWalkAction, ReadAction}
config[SceneActionType.SimpleInteraction] = {WalkAction, SimpleInteractionAction}
config[SceneActionType.StartDoublePose] = {WalkAction, StartDoublePoseAction}
config[SceneActionType.AcceptDoublePose] = {WalkAction, AcceptDoublePoseAction}
config[SceneActionType.JoinSceneGame] = {WalkAction, JoinSceneGameAction}
config[SceneActionType.Spa] = {StopWalkAction, SpaAction}
config[SceneActionType.HitIce] = {WalkAction, HitIceAction}
config[SceneActionType.Throw] = {StopWalkAction, ThrowAction}
-- config[SceneActionType.Shower] = {WalkAction, ShowerAction}
config[SceneActionType.MultiInteraction] = {WalkAction, MultiInteractionAction}
config[SceneActionType.CableCar] = {WalkAction, CableCarAction}
config[SceneActionType.DoThrow] = {StopWalkAction, DoThrowAction}
config[SceneActionType.SceneTrap] = {StopWalkAction, SceneTrapAction}
config[SceneActionType.StartQueue] = {StopWalkAction, StartQueueAction}
config[SceneActionType.JoinQueue] = {WalkAction, JoinQueueAction, StopWalkAction}
config[SceneActionType.Dandelion] = {WalkAction, DandelionAction}
config[SceneActionType.TouchCouncilElf] = {StopWalkAction, TouchCouncilElfAction}
config[SceneActionType.DanceByHappy] = {DanceAction}
config[SceneActionType.CushionJoin] = {SceneCushionAction}
config[SceneActionType.Parttime] = {ParttimeAction}
config[SceneActionType.happyBattle] = {HappyBattleAction}
config[SceneActionType.PlayHandItem] = {StopWalkAction, PlayHandItemAction}
config[SceneActionType.LoadWerewolfSceneAction] = {LoadWerewolfSceneAction}
config[SceneActionType.WerewolfJoystick] = {WerewolfJoystickAction}
config[SceneActionType.PlaySoda] = {PlaySodaAction}
config[SceneActionType.CouncilThrow] = {CouncilBossThrowAction}
config[SceneActionType.CouncilDrop] = {StopWalkAction,CouncilBossDropAction}
config[SceneActionType.CouncilHoldBullet] = {CouncilBossHoldBullet}
config[SceneActionType.CouncilFire] = {CouncilBossHoldWeapon}
config[SceneActionType.CouncilBossCollect] = {CouncilBossCollect}
config[SceneActionType.Navigator] = {StopWalkAction, NavigatorAction}
config[SceneActionType.StartElfBattlePractice] = {WalkAction, TeleportAction, StartElfBattlePracticeAction}
config[SceneActionType.JoinElfBattlePractice] = {WalkAction, JoinElfBattlePracticeAction}
config[SceneActionType.CommonGameJoystick] = {CommonGameJoyStickAction}
config[SceneActionType.FurnitureInteract] = {WalkAction, FurnitureInteractAction}
config[SceneActionType.AssemblyLineAction] = {WalkAction,AssemblyLineAction}
config[SceneActionType.AR] = {StopWalkAction, ARAction}
config[SceneActionType.FurnitureStatusSync] = {FurnitureStatusSyncAction}
config[SceneActionType.MusicParty] = {MusicPartyAction}
config[SceneActionType.GobangFurniture] = {StopWalkAction,GobangFurnitureAction}
config[SceneActionType.GobangMatchWatchEntry] = {GobangMatchWatchEntryAction}
config[SceneActionType.Fly3D] = {StopWalkAction,Fly3DAction}
config[SceneActionType.FlyJoystickAction] = {FlyJoystickAction}

config[SceneActionType.CatchInsect] = {WalkAction,CatchInsectAction}
config[SceneActionType.AnniversaryGalaSceneGame] = {AnniversaryGalaSceneGameAction}
config[SceneActionType.RainbowTrail] = {WalkAction, RainbowTrailAction}
config[SceneActionType.AnniversaryGalaColorGrid] = {AnniversaryGalaColorGridAction}
config[SceneActionType.AnniversaryGalaSite] = {AnniversaryGalaSiteAction}
config[SceneActionType.AnniversaryGalaAskAttached] = {WalkAction, AnniversaryGalaAskAttachedAction}
config[SceneActionType.AnniversaryGalaAnswerAttached] = {WalkAction, AnniversaryGalaAnswerAttachedAction}
config[SceneActionType.AnniversaryGalaInCar] = {AnniversaryGalaInCarAction}
config[SceneActionType.AnniversaryGalaStartHand] = {WalkAction, AnniversaryGalaStartHandAction}
config[SceneActionType.AnniversaryGalaAcceptHand] = {WalkAction, AnniversaryGalaAcceptHandAction}
config[SceneActionType.AnniversaryGalaJoinGodlike] = {AnniversaryGalaJoinGodlikeAction}
config[SceneActionType.AnniversaryGalaJoinFirework] = {AnniversaryGalaJoinFireworkAction}
config[SceneActionType.WealthGodFollow] = {WealthGodFollowAction}
config[SceneActionType.FurnitureSceneAction] = {WalkAction, FurnitureSceneAction} 
config[SceneActionType.MusicPartyWaveAction] = {StopWalkAction,MusicPartyWaveAction} 
config[SceneActionType.CreatePortal] = {StopWalkAction, CreatePortalAction} 
config[SceneActionType.CityPartyTrain] = {WalkAction, CityPartyTrainAction} 
config[SceneActionType.TransformSkinPlayerAction] = {TransformSkinPlayerAction}
config[SceneActionType.WeatherUmbrella] = {WeatherUmbrellaAction}
config[SceneActionType.JoinWeatherUmbrella] = {JoinWeatherUmbrellaAction,StopWalkAction}
config[SceneActionType.StartRideMount] = {StopWalkAction,StartRideMountAction}
config[SceneActionType.JoinRideMount] = {StopWalkAction,JoinRideMountAction}
config[SceneActionType.LaunchSeaDance] = {StopWalkAction, LaunchSeaDanceAction}
config[SceneActionType.JoinSeaDance] = {WalkAction, JoinSeaDanceAction}
config[SceneActionType.WaitTogetherDance] = {WaitTogetherDanceAction}
config[SceneActionType.SeaAim] = {SeaAimAction}
config[SceneActionType.SeaTask] = {SeaTaskAction}
config[SceneActionType.SeaFishing] = {StopWalkAction, SeaFishingAction}
config[SceneActionType.SeaShowFish] = {StopWalkAction, SeaShowFishAction}
config[SceneActionType.SeaBossState] = {SeaBossStateAction}
config[SceneActionType.SeaMural] = {SeaMuralAction}
config[SceneActionType.Track] = {TrackAction}
config[SceneActionType.FurnitureCushionDonate] = {WalkAction, FurnitureCushionDonateAction}
config[SceneActionType.LongStreetBanquet] = {WalkAction,LongStreetBanquetAction}
config[SceneActionType.BonfireParty] = {StopWalkAction, BonfirePartyAction}
config[SceneActionType.BonfirePartyCall] = {StopWalkAction, BonfirePartyCallAction}
config[SceneActionType.CelebParty] = {StopWalkAction, CelebPartyAction}
config[SceneActionType.StartDoubleFollow] = {WalkAction, StartDoubleFollowAction}
config[SceneActionType.AcceptDoubleFollow] = {WalkAction, AcceptDoubleFollowAction, StopWalkAction}

function SceneActionType.getActionList(type)
	return config[type]
end
return SceneActionType
