-- {excel:R任务目标定义.xlsx, sheetName:export_任务目标}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_task_objective_define", package.seeall)

local title = {id=1,descPattern=2,quickFinish=3,iconName=4,helpPics=5,gainWayLimit=6,goLimit=7,goFuncUnlockCheck=8,noAutoTriggerNPCTask=9,cls=10,params=11}

local dataList = {
	{1, "收集{id}x{num}", 0.2, "", nil, nil, nil, nil, false, "ItemCommon", nil},
	{2, "使用{id}x{num}", 0.5, "", nil, nil, nil, nil, false, "ItemCommon", {itemAnyGo="BackPackPanel"}},
	{3, "种植{num}次{id}", 0.2, "", nil, {1,2,3}, nil, nil, false, "ItemCommon", nil},
	{4, "穿上{id}", 0.2, "", nil, nil, nil, nil, false, "ItemCommon", {itemEnoughGo="DressPanel"}},
	{5, "收获{id}x{num}", 0.2, "", nil, {1,2,3}, nil, nil, false, "ItemCommon", nil},
	{6, "制作{id}x{num}", 0.2, "", nil, {1,2,3}, nil, nil, false, "ItemCommon", nil},
	{7, "摆放{id}x{num}", 0.2, "", nil, nil, nil, nil, false, "ItemCommon", {itemAnyBtn={btnName="backpack_btn",viewName="BackPackPanel"}}},
	{8, "提交{id}x{num}", 1, "", nil, nil, nil, nil, false, "SubmitItem", nil},
	{9, "采集{id}x{num}", 0.2, "", nil, {2}, nil, nil, false, "ItemCommon", nil},
	{10, "解锁{id}", 0, "", nil, nil, nil, nil, false, "UnlockArea", nil},
	{11, "分享{num}次{id}", 0.2, "", {"share1","share2","share3"}, nil, nil, nil, false, "ItemCommon", {itemAnyGo="BackPackPanel"}},
	{12, "达到岛建等级{level}", 0, "", {"level1","level2"}, nil, nil, nil, false, "ReachLevel", nil},
	{13, "从动物收获{num}次", 10, "zhongzhi", {"animal"}, nil, nil, nil, false, "ServerCommon", {btnGo="None"}},
	{14, "从果树收获{num}次", 10, "zhongzhi", {"tree"}, nil, nil, nil, false, "ServerCommon", {btnGo="None"}},
	{15, "从土壤作物收获{num}次", 10, "zhongzhi", {"plant"}, nil, nil, nil, false, "ServerCommon", {btnGo="None"}},
	{16, "修改{num}次个人信息", 10, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="InfoCardPanel"}},
	{17, "工坊制作物品x{num}", 10, "zhizuo", nil, nil, nil, nil, false, "ServerCommon", {workShopId=102,goType="ShowWorkshop"}},
	{18, "成功钓鱼{num}次", 10, "zhongzhi", nil, nil, nil, nil, false, "ServerCommon", {goType="GoFishing"}},
	{19, "野外收集木材{num}次", 10, "zhongzhi", nil, nil, nil, nil, false, "ServerCommon", nil},
	{20, "野外采蘑菇{num}次", 10, "zhongzhi", nil, nil, nil, nil, false, "ServerCommon", nil},
	{21, "拜访{num}个人的家", 10, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FriendView"}},
	{22, "在别人家吃{num}次食物", 10, "yinshi", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FriendView"}},
	{23, "完成{num}次订单气球订单", 50, "weituo", nil, nil, nil, {4}, false, "ServerCommon", {goType="ShowView",goParam="OrderMain"}},
	{24, "体验{num}次{type}", 0, "niudan", nil, nil, nil, nil, false, "ServerCommon", {typeDefine="LotteryEgg",goType="ShowView",goParam="Lottery"}},
	{25, "升级{id}至{level}级", 0, "", nil, nil, nil, nil, false, "ItemCommon", {goType="ShowWorkshop",isUpgrade=true}},
	{26, "点击料理，邀请{npcId}食用{num}次", 20, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{27, "添加{num}位好友", 20, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goParam2=4,goType="ShowView",goParam="FriendView"}},
	{28, "关注{num}个人", 20, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goParam2=4,goType="ShowView",goParam="FriendView"}},
	{29, "发起{id}的动作{num}次", 20, "haoyou", {"action1","action2","action3"}, nil, nil, nil, false, "ServerCommon", nil},
	{30, "参加{num}次小屋派对", 20, "haoyou", nil, nil, nil, {17}, false, "ServerCommon", {goType="ShowView",goParam="PartyMainView"}},
	{31, "钓鱼获得{num}个{id}", 0.2, "", nil, nil, nil, nil, false, "ItemCommon", {goType="GoFishing"}},
	{32, "提交{npcId}需要的物品", 1, "", nil, nil, nil, nil, true, "SubmitToNPC", nil},
	{33, "钓鱼获得{num}种不同的鱼", 10, "zhongzhi", nil, nil, nil, nil, false, "ServerCommon", {goType="GoFishing"}},
	{34, "拜访{num}次{npcId}的家", 20, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="EnterNPCRoomScene",goParam=1}},
	{35, "宣传{num}次", 50, "haoyou", {"advertise1","advertise2"}, nil, nil, nil, false, "ServerCommon", nil},
	{36, "钓出{num}次{id}(deprecated)", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{37, "升级{id}至{level}级", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="HouseUpgrade"}},
	{38, "采矿{num}次", 10, "zhongzhi", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowCollect",goParam=14000602}},
	{39, "钓鱼获得{id}x{num}", 0.2, "", nil, nil, nil, nil, false, "ItemCommon", {goType="GoFishing"}},
	{40, "舒适度达到{num}", 1, "", {"wow1","wow2"}, nil, nil, nil, false, "ServerCommon", nil},
	{41, "在奥比圈发{num}次动态", 50, "haoyou", nil, nil, nil, {5}, false, "ServerCommon", {goType="ShowView",goParam="AobiCircle"}},
	{42, "保有{id}x{num}", 0.2, "", nil, nil, nil, nil, false, "ItemCommon", nil},
	{43, "在别人家留言{num}次", 10, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FriendView"}},
	{44, "市集购买{id}x{num}", 0.2, "", nil, {101}, nil, {6}, false, "ItemCommon", nil},
	{45, "在交易行上架货物{num}次", 10, "jiaoyi", nil, nil, nil, {8}, false, "ServerCommon", {goType="ShowView",goParam="TradingMarket"}},
	{46, "在市集购买商品{num}次", 10, "jiaoyi", nil, nil, nil, {6}, false, "ServerCommon", {goType="ShowView",goParam="GroceryView"}},
	{47, "完成当天全部日常{num}次", 0, "mubiao", nil, nil, nil, {13}, false, "ServerCommon", {goParam2=3,goType="ShowView",goParam="TaskObjectivePanel"}},
	{48, "在交易行购买{num}次", 10, "", nil, nil, nil, {8}, false, "ServerCommon", {goParam2={tab=2},goType="ShowView",goParam="TradingMarket"}},
	{49, "在交易行上架{id}x{num}", 1, "", nil, nil, nil, {8}, false, "ServerCommon", {goParam2={tab=1},goType="ShowView",goParam="TradingMarket"}},
	{50, "开启背包空间{num}格", 5, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="BackPackPanel"}},
	{51, "发出{id}的订单x{num}", 0.2, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="OrderMain"}},
	{52, "抓取{num}次宠物", 20, "chongwu", {"pet1","pet2"}, nil, nil, {22}, false, "ServerCommon", nil},
	{53, "喂食{num}次宠物", 20, "chongwu", {"feed1","feed2"}, nil, nil, {22}, false, "ServerCommon", nil},
	{54, "邀请岛民食用料理{num}次", 20, "yinshi", {"invite1","invite2","invite3"}, nil, nil, nil, false, "ServerCommon", nil},
	{55, "发出{num}次添加好友的申请", 10, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goParam2=4,goType="ShowView",goParam="FriendView"}},
	{56, "在{groceryId}购买{id}x{num}", 1, "", nil, {12}, nil, nil, false, "ServerCommon", {goType="GotoGrocery"}},
	{57, "料理店购买{num}次", 10, "", nil, nil, nil, {25}, false, "ServerCommon", {goParam2=4,goType="ShowView",goParam="GroceryView"}},
	{58, "服装店购买{num}次", 10, "", nil, nil, nil, {23}, false, "ServerCommon", {goParam2=2,goType="ShowView",goParam="GroceryView"}},
	{59, "家具店购买{num}次", 10, "", nil, nil, nil, {24}, false, "ServerCommon", {goParam2=3,goType="ShowView",goParam="GroceryView"}},
	{60, "探宝{num}次", 10, "wabao", {"wabao1","wabao2","wabao3"}, nil, nil, nil, false, "ServerCommon", nil},
	{61, "派遣{num}次宠物探险", 10, "chongwu", nil, nil, nil, {22}, false, "ServerCommon", {goParam2="ExploreArrow",goType="ShowView",goParam="PetHome"}},
	{62, "参加装扮评选赛{num}次", 10, "", nil, nil, nil, {27}, false, "ServerCommon", {goType="ShowView",goParam="FashionRace"}},
	{63, "家居图鉴解锁至{num}个", 10, "", nil, nil, nil, nil, false, "ServerCommon", {goType="Archive",goParam=2}},
	{64, "装扮图鉴解锁至{num}个", 10, "", nil, nil, nil, nil, false, "ServerCommon", {goType="Archive",goParam=1}},
	{66, "宠物图鉴解锁至{num}个", 10, "", nil, nil, nil, nil, false, "ServerCommon", {goType="Archive",goParam=4}},
	{67, "{npcId}的好感度解锁到{level}级", 10, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FavorabilityNPCView"}},
	{68, "放置{id}房型", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="OpenMyIsland",goParam="btnPut"}},
	{69, "让1只宠物跟随", 0, "chongwu", nil, nil, nil, {22}, false, "ServerCommon", {goType="ShowView",goParam="PetHome"}},
	{70, "加工{num}次鱼肉", 0, "zhongzhi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoNPC",goParam={npcId=1000000}}},
	{71, "做{num}次好友双人动作", 0, "haoyou", {"shuangren1","shuangren2","shuangren3"}, nil, nil, nil, false, "ServerCommon", nil},
	{72, "解锁{num}种鱼类", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GoFishing"}},
	{73, "精灵升到{num}阶", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="ElfEvolutionView",checkParam={[1]={key="ElfEgg"}}}},
	{74, "给自己的友情树浇水{num}次", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoFurniture",goParam={[1]=13000708}}},
	{75, "帮好友友情树浇水{num}次", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FriendView"}},
	{76, "打{num}次苹果", 0, "dapingguo", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-8.3,posY=7,sceneId=3}}},
	{77, "玩{num}次打地鼠", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=8.58,posY=5.24,sceneId=3}}},
	{78, "玩{num}次滚雪球", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-13.32,posY=-17.03,sceneId=12}}},
	{79, "列表关注{num}个人", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goParam2=4,goType="ShowView",goParam="FriendView"}},
	{80, "广场演奏{num}次", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-1.16,posY=-20.80,sceneId=6}}},
	{81, "修复{id}", 0, "", nil, nil, nil, nil, false, "ServerCommon", {idType="Building",goType="GotoUnlockArea"}},
	{82, "打开一次岛民事件簿面板", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="IslanderEventBook"}},
	{83, "主屋中置有{num}件家具", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="MyHouse",goParam={arrowKey="HUD_EditRoom",uiOffset={x=0,y=50}}}},
	{84, "做{num}次棉花糖", 0, "mianhuatang", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-14.81,posY=-12.79,sceneId=6}}},
	{85, "收纳{num}种鱼到水族馆", 0, "", nil, nil, nil, {29}, false, "ServerCommon", {goType="ShowView",goParam="Aquarium"}},
	{86, "完成第{chapterId}章剧情任务", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="TaskStoryPanel"}},
	{87, "玩{num}次摇汽水", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", nil},
	{88, "参与哈皮大作战{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="DaoDanGui",goParam=3}},
	{89, "参加乐斗嘟嘟{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1861}},
	{90, "在哈皮大作战中胜利{num}场", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="DaoDanGui",goParam=3}},
	{91, "参加擂台大乐斗胜利{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="DaoDanGui",goParam=4}},
	{92, "参加擂台大乐斗取得{num}分", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="DaoDanGui",goParam=4}},
	{93, "完成委托任务{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="DaoDanGui",goParam=8}},
	{94, "抽取旅行者{num}次(在图书馆抽取{num}次)", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="LoadScene",goParam=14}},
	{95, "解锁{num}个旅行者", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerMainPanel"}},
	{96, "升级旅行者天赋{num}次", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerMainPanel"}},
	{97, "拥有{num}位旅行者，天赋达到{level}级", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerMainPanel"}},
	{98, "升级（修补）记忆链接{num}次", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerMainPanel"}},
	{99, "拥有{num}个记忆链接，等级达到{level}级", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerMainPanel"}},
	{100, "升星（翻新）记忆链接{num}次", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerMainPanel"}},
	{101, "邀请{num}位旅行者上岛", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerLibrary"}},
	{102, "岛上有{num}位旅行者", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goType="ShowView",goParam="TravellerLibrary"}},
	{103, "为旅行者送礼{num}次", 0, "", {"lxzsongli1","lxzsongli2"}, nil, nil, {33}, false, "ServerCommon", nil},
	{104, "{npcId}旅行者好感度达到{level}级", 0, "", nil, nil, nil, {33}, false, "ServerCommon", {goParam2=3,goType="ShowView",goParam="TravellerMainPanel"}},
	{105, "捐赠{id}x{num}", 0, "", nil, nil, nil, {30}, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-10.45,posY=26.76,sceneId=17}}},
	{106, "捐赠{num}个道具", 0, "", nil, nil, nil, {30}, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-10.45,posY=26.76,sceneId=17}}},
	{107, "将旅行者移动到主位小屋", 0, "", {"weiyi1","weiyi2","weiyi3"}, nil, nil, {33}, false, "ServerCommon", nil},
	{108, "发出{num}次亲密任务邀请", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goParam2=4,goType="ShowView",goParam="TaskObjectivePanel"}},
	{109, "完成{num}次亲密任务", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goParam2=4,goType="ShowView",goParam="TaskObjectivePanel"}},
	{110, "和{num}个好友亲密度达到{count}", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FriendView"}},
	{111, "发出{num}次亲友申请", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=20.93,posY=-7.43,sceneId=15}}},
	{112, "拥有{num}个亲友", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=20.93,posY=-7.43,sceneId=15}}},
	{113, "亲和力达到{num}", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-3.77,posY=-1.06,sceneId=15}}},
	{114, "许愿池许愿{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=6.93,posY=-8.27,sceneId=15}}},
	{115, "蘑菇祝福{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=0.86,posY=-17.68,sceneId=15}}},
	{116, "邀请{npcId}上岛", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="TravellerLibrary"}},
	{117, "{npcId}好感度提升1级", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goParam2=3,goType="ShowView",goParam="TravellerMainPanel"}},
	{118, "给{npcId}赠送好感度礼物", 0, "", {"lxzsongli1","lxzsongli2"}, nil, nil, nil, false, "ServerCommon", nil},
	{119, "制作料理{num}个", 0, "yinshi", nil, nil, nil, nil, false, "ServerCommon", {workShopId=103,goType="ShowWorkshop"}},
	{120, "制作家具{num}个", 0, "zhizuo", nil, nil, nil, nil, false, "ServerCommon", {workShopId=102,goType="ShowWorkshop"}},
	{121, "制作服装{num}个", 0, "zhizuo", nil, nil, nil, nil, false, "ServerCommon", {workShopId=104,goType="ShowWorkshop"}},
	{122, "创建或加入家族", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-6.00,posY=3.32,sceneId=22}}},
	{123, "完成{num}次家族礼奉", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-22.36,posY=12.94,onEnd={goType="ShowView",goParam="CouncilDonate"},sceneId=20},checkParam={[1]={key="JoinCouncil"}}}},
	{124, "给{npcId}送{num}次礼物", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoNPC"}},
	{125, "在奥柏百货购买{goodsId}{num}个", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="RechargeFacade.openStoreByGoodsId",goodsIdType="RechargeStore"}},
	{126, "在奥柏百货购买{goodsId}{num}套", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="RechargeFacade.openStoreBySuiteId",goodsIdType="SuitStore"}},
	{127, "收藏{num}套搭配", 0, "", {"outfit_save1","outfit_save2"}, nil, nil, nil, false, "ServerCommon", nil},
	{128, "展示{num}套搭配", 0, "", {"outfit_show1","outfit_show2","outfit_show3"}, nil, nil, nil, false, "ServerCommon", nil},
	{129, "参加一次茶话会", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{130, "到{npcId}家吃1次料理", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="EnterNPCRoomScene",goParam=1}},
	{131, "完成{id}动作{num}次", 0, "", {"clothesaction1","clothesaction2","clothesaction3"}, nil, nil, nil, false, "ServerCommon", {idType="pose"}},
	{132, "选择精灵蛋", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{133, "拍摄大头贴{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoTrigger",goParam={sceneId=6,name="trigger48"}}},
	{134, "扎花{num}束", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoNPC",goParam={npcId=29}}},
	{135, "送花{num}束", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FriendView"}},
	{136, "购买变异种子{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoNPC",goParam={npcId=29}}},
	{137, "种植变异种子{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoFarmAndOpenEdit",goParam={subType=34}}},
	{138, "玩{num}次分麦子", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoTrigger",goParam={sceneId=17,name="trigger10"}}},
	{139, "玩{num}次采蜂蜜", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoTrigger",goParam={sceneId=17,name="trigger15"}}},
	{140, "打工{num}次", 0, "zhizuo", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="ParttimeSelectView"}},
	{141, "学习{num}次精灵魔法", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoNPC",goParam={npcId=1},checkParam={[1]={key="ElfEgg"}}}},
	{142, "参与{num}次精灵魔法杜尔", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="ElfBattleEntrance",checkParam={[1]={key="ElfEgg"}}}},
	{143, "考核{num}次爱好家", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="TalentMainView"}},
	{144, "领取第{level}级岛建奖励", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="MyIsland",goParam={pointId={[1]=13000014}}}},
	{145, "成功雕刻木雕{num}次", 50, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GOF",goParam={id=130}}},
	{146, "使用花神之泪击中花魇{num}次", 50, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GOF",goParam={id=133}}},
	{147, "选择群岛分区", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{148, "集齐{num}套服装套装", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goParam2=true,goType="Archive",goParam=1}},
	{149, "集齐{num}套家具套装", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goParam2=true,goType="Archive",goParam=2}},
	{150, "领取{num}次签到奖励", 0, "mubiao", nil, nil, nil, nil, false, "ServerCommon", {goParam2={id=2},goType="ShowView",goParam="ActivityCalendar"}},
	{151, "参与{num}次快乐射击", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2081}},
	{152, "参与{num}次贪吃蛇", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goParam2=true,goType="Activity",goParam=150}},
	{153, "参与{num}次花云挑战", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1688}},
	{154, "收集{num}张星际写真", 0, "xiezhen", nil, nil, nil, nil, false, "ServerCommon", {goParam2=true,goType="Activity",goParam=152}},
	{155, "参与{num}次宠物问答", 0, "wenda", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2017}},
	{156, "完成{num}次小记者任务", 0, "jizhe", nil, nil, nil, nil, false, "ServerCommon", {goParam2=true,goType="Activity",goParam=145}},
	{157, "参加{num}次别人的派对", 0, "haoyou", nil, nil, nil, {17}, false, "ServerCommon", {goType="ShowView",goParam="PartyMainView"}},
	{158, "完成{num}次影城宣传", 0, "37ycdl_props_209", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2267}},
	{159, "参与{num}次线索翻牌", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1911}},
	{160, "通过第{count}关线索翻牌", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1911}},
	{161, "参与{num}次雾月留影", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1594}},
	{162, "通过第{count}关雾月留影", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1594}},
	{163, "完成第{count}章推理探案", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1906}},
	{164, "参加{num}次岛务科研馆", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=11.50,posY=-22.70,sceneId=28}}},
	{165, "以白创造师身份参加{num}次源晶溯源实验", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=11.50,posY=-22.70,sceneId=28}}},
	{166, "以黑创造师身份参加{num}次源晶溯源实验", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=11.50,posY=-22.70,sceneId=28}}},
	{167, "参加{num}次快乐面包", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="DaoDanGui",goParam=4}},
	{168, "收集{num}丰收积分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1503}},
	{169, "今天抽取{num}次{type}", 0, "niudan", nil, nil, nil, nil, false, "ServerCommon", {typeDefine="LotteryEgg",goType="ShowView",goParam="Lottery"}},
	{170, "拍摄{num}次{id}大头贴", 0, "mubiao", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoTrigger",goParam={sceneId=6,name="trigger48"}}},
	{171, "参加{num}次奥比摘星", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=10.12,posY=-3.10,sceneId=12}}},
	{172, "奥比摘星单次达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=10.12,posY=-3.10,sceneId=12}}},
	{173, "堆堆雪人中完成任意{num}个雪人", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2224}},
	{174, "参加{num}次火柴妙妙屋", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-21.35,posY=20.65,sceneId=28}}},
	{177, "生日填写任务", 0, "", nil, nil, nil, nil, false, "ShowView", {viewName="InfoCardPanel",goType="ShowView",goParam="InfoCardPanel"}},
	{178, "增加{num}点疲劳值", 0, "zhongzhi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoFurniture",goParam={[1]=13000029}}},
	{179, "成功分享{num}次分享物", 0, "haoyou", {"share1","share2","share3"}, nil, nil, nil, false, "ServerCommon", nil},
	{180, "参与{num}次星乐咖", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoTrigger",goParam={sceneId=28,name="trigger7"}}},
	{181, "领取{num}次签到奖励", 0, "mubiao", nil, nil, nil, nil, false, "ServerCommon", {goParam2={id=2},goType="ShowView",goParam="ActivityCalendar"}},
	{182, "参加{num}次快乐星球的游戏", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goParam2={page=2},goType="ShowView",goParam="GameMachineView"}},
	{183, "参加{num}次土木敲敲", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2057}},
	{184, "土木敲敲单局获得{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2057}},
	{185, "参与{num}次积木高高匹配模式", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1330}},
	{186, "功能{funcId}解锁", 0, "", nil, nil, nil, nil, false, "", nil},
	{187, "精灵蛋孵化成功", 0, "", nil, nil, nil, nil, false, "", nil},
	{188, "摆放一个精灵的家", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoElf",checkParam={[1]={key="ElfEgg"},[2]={key="ElfRoom"}}}},
	{189, "在精灵的家摆放任意一件家具", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{190, "更换一次精灵装扮", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{191, "拜访{num}次好友精灵的家", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{192, "参加{num}次游戏机中的游戏", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goParam2={page=2},goType="ShowView",goParam="GameMachineView"}},
	{193, "参加{num}次游戏机中的游戏", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goParam2={page=1},goType="ShowView",goParam="GameMachineView"}},
	{194, "参加{num}次游戏机中的游戏", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goParam2={page=3},goType="ShowView",goParam="GameMachineView"}},
	{195, "参加{num}次快乐星球的游戏", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goParam2={page=1},goType="ShowView",goParam="GameMachineView"}},
	{196, "在永恒乐园放养{num}只动物", 0, "leyuan", nil, nil, nil, {56}, false, "ServerCommon", {goType="EcoParkEdit"}},
	{197, "在结缘商店购买{num}只动物", 0, "leyuan", nil, nil, nil, {56}, false, "ServerCommon", {goType="EcoParkStore"}},
	{198, "在永恒乐园摆放{num}个{id]", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{199, "在永恒乐园使用{num}个{id]", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{200, "在海底成功捕获{num}条鱼", 0, "buyu", nil, nil, nil, {58}, false, "ServerCommon", {goType="GotoSeaAndCatchFish"}},
	{201, "在海底成功捕获{num}条{fishId}", 0, "", nil, nil, nil, {58}, false, "ServerCommon", {goType="GotoSeaAndCatchFish"}},
	{202, "在海底第{depth}层成功捕获{num}条鱼", 0, "", nil, nil, nil, {58}, false, "ServerCommon", {numTypeList={[1]="depth"},goType="GotoSeaAndCatchFish"}},
	{203, "在海底成功捕获{num}条闪光鱼", 0, "", nil, nil, nil, {58}, false, "ServerCommon", {goType="GotoSeaAndCatchFish"}},
	{204, "在海底成功捕获{num}条鱼王", 0, "", nil, nil, nil, {58}, false, "ServerCommon", {goType="GotoSeaAndCatchFish"}},
	{205, "完成{num}次海贸订单", 0, "haimao", nil, nil, nil, nil, false, "ServerCommon", {goType="EnterAndOpenOrder"}},
	{206, "解锁海底地块{id}", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="UnlockSeaArea"}},
	{207, "海贸气球升到{num}级", 0, "haimao", nil, nil, nil, nil, false, "ServerCommon", {goType="EnterAndOpenTechUpgrade"}},
	{208, "解锁海底科技{id}", 0, "haimao", nil, nil, nil, nil, false, "ServerCommon", {idType="SeaTech",goType="EnterAndOpenTech"}},
	{209, "海底科技{id}升到{num}级", 0, "", nil, nil, nil, nil, false, "ServerCommon", {idType="SeaTech",goType="EnterAndOpenTech"}},
	{210, "在海底开启{num}个宝箱", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoSeaAndOpenBox"}},
	{211, "解锁小屋卡位{houseAreaId}", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="EnterAndPointArea"}},
	{212, "参加{num}次人偶庄园", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="gameRoomWalkToTrigger",goParam=1}},
	{213, "成功进入庄园作客{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="gameRoomWalkToTrigger",goParam=1}},
	{214, "成为人偶庄园的小主人{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="gameRoomWalkToTrigger",goParam=1}},
	{215, "在街区摆放{num}件别人共享的家具", 0, "jiequ", nil, nil, nil, {65}, false, "ServerCommon", {goType="ShowMapPanel",goParam={arrowKey="Map_Scene_109",uiOffset={x=0,y=50}}}},
	{216, "解锁过街区区域{num}次", 0, "jiequ", nil, nil, nil, {65}, false, "ServerCommon", {goType="ShowMapPanel",goParam={arrowKey="Map_Scene_109",uiOffset={x=0,y=50}}}},
	{217, "在街区摆放{num}件家具", 0, "jiequ", nil, nil, nil, {65}, false, "ServerCommon", {goType="ShowMapPanel",goParam={arrowKey="Map_Scene_109",uiOffset={x=0,y=50}}}},
	{218, "在街区共享{num}件家具", 0, "jiequ", nil, nil, nil, {65}, false, "ServerCommon", {goType="ShowMapPanel",goParam={arrowKey="Map_Scene_109",uiOffset={x=0,y=50}}}},
	{219, "进入自己的街区{num}次", 0, "jiequ", nil, nil, nil, {65}, false, "ServerCommon", {goType="ShowMapPanel",goParam={arrowKey="Map_Scene_109",uiOffset={x=0,y=50}}}},
	{220, "拜访别人的街区{num}次", 0, "jiequ", nil, nil, nil, {65}, false, "ServerCommon", {goType="ShowMapPanel",goParam={arrowKey="Map_Scene_109",uiOffset={x=0,y=50}}}},
	{221, "加入或者创建街区", 0, "jiequ", nil, nil, nil, {65}, false, "ServerCommon", {goType="ShowMapPanel",goParam={arrowKey="Map_Scene_109",uiOffset={x=0,y=50}}}},
	{222, "帮别人除虫{num}次", 0, "haoyou", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="FriendView"}},
	{2001, "整理{num}次稻草", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-8.50,posY=-32.40,sceneId=17}}},
	{2002, "泡{num}次火锅温泉", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-23.16,posY=-13.86,sceneId=12}}},
	{2003, "在冰雪市集上购买{num}次", 0, "jiaoyi", nil, nil, nil, nil, false, "ServerCommon", {goParam3=2,goType="ShowView",goParam="SnowCelebration"}},
	{2004, "参加{num}次千食百味", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1824}},
	{2005, "完成第{count}关千食百味", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1824}},
	{2006, "参加{num}次{actId}", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity"}},
	{2007, "完成第{count}张秋叶入画", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1504}},
	{2008, "完成第{count}关玩具跳跳", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1329}},
	{2009, "在美食荟萃成功提交{num}次", 0, "37ycdl_props_205", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2278}},
	{2010, "通过第{count}关御廷家宴", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1736}},
	{2011, "参加团圆串串{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoTrigger",goParam={sceneId=42,name="trigger32"}}},
	{2012, "在海神之梦擦亮{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1261}},
	{2013, "海神之梦进度达到{num}%", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1261}},
	{2014, "完成第{count}关寻心拼趣", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2347}},
	{2015, "完成第{count}关音乐豆豆", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1295}},
	{2016, "领取奈娃合影奖励{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2393}},
	{2017, "完成{num}次冰柠茶饮", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1419}},
	{2018, "在冰柠商店购买{num}件服装或家具", 0, "jiaoyi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1416}},
	{2019, "结交{num}位御猫", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1704}},
	{2020, "结交{num}位不同的月灵", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1537}},
	{2021, "在魔药店交换{num}次物品", 0, "caihong", nil, nil, nil, {53}, false, "ServerCommon", {goType="GotoLocation",goParam={posX=9.32,posY=26.35,onEnd={goType="ShowView",goParam="PotionsRefineView"},sceneId=8}}},
	{2022, "在天才蛋糕单次得分达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1776}},
	{2023, "参加岛务科研馆潜伏模式{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=11.50,posY=-22.70,sceneId=28}}},
	{2024, "在星际科学中答对{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1450}},
	{2025, "在单局小猪飞飞达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1883}},
	{2026, "在单局花雨纷纷达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2308}},
	{2027, "结交{num}位{insectId}", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1704}},
	{2028, "在{sceneId}结交{num}只御猫", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1704}},
	{2029, "结交{num}位{type}", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {typeDefine="Insect",goType="Activity",goParam=1704}},
	{2030, "在变装舞台中搭配{num}次", 0, "40xcy_yyzn_207", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="CrossDressEntrance"}},
	{2031, "在雾月变装中上传1次搭配", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="CrossDressEntrance"}},
	{2032, "在春日花艺单次得分达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1777}},
	{2033, "整理{num}次冰雕", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="GotoLocation",goParam={posX=-8.50,posY=-32.40,sceneId=17}}},
	{2034, "参加{num}次游戏狂欢周的游戏", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="MultiActivity",goParam="2466"}},
	{2035, "在单局拼贴泡泡达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2419}},
	{2036, "在海洋共建中提交材料{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=1960}},
	{2037, "在单局海妖试炼达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goParam2=true,goType="Activity",goParam=420}},
	{2038, "参加{num}次妖力强袭匹配对抗", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2173}},
	{2039, "通关妖力强袭剧情模式{id}", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {idType="BreakoutLevel",goType="Activity",goParam=2173}},
	{2040, "妖力强袭无尽模式最高达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2173}},
	{2041, "捕鱼挑战{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {viewName="TinyGameTimeView",goType="MultiActivity",goParam="2465"}},
	{2042, "通过悠悠滑冰剧情模式第{count}关", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2212}},
	{2043, "悠悠滑冰单人无尽模式达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2212}},
	{2044, "悠悠滑冰双人无尽模式达到{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2212}},
	{2045, "在经营恋冬街累计赚{num}金币", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", nil},
	{2046, "解锁经营恋冬街第{count}个商店", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", nil},
	{2047, "经营恋冬街第{count}个商店升级到{num}级", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", nil},
	{2048, "领取影城签到奖励{num}次", 0, "40xcy_yyzn_101", nil, nil, nil, nil, false, "ServerCommon", {goParam2=true,goType="Activity",goParam=196}},
	{2049, "团年食宴吃{num}次宴席", 0, "37ycdl_props_106", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2257}},
	{2050, "完成{num}次龙门镖局任务", 0, "40xcy_yyzn_103", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2478}},
	{2051, "参加风华表演{num}次", 0, "37ycdl_props_104", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2258}},
	{2053, "团圆祝福获得{num}个{id}", 0, "37ycdl_props_206", nil, nil, nil, nil, false, "ServerCommon", {goParam2=2,goType="ShowView",goParam="TaskObjectivePanel"}},
	{2054, "江湖巡游领取{num}个红包", 0, "37ycdl_props_207", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2256}},
	{2055, "变装舞台活动点赞{num}次", 0, "40xcy_yyzn_207", nil, nil, nil, nil, false, "ServerCommon", {goType="ShowView",goParam="CrossDressEntrance"}},
	{2056, "在同福客栈兑换{num}次物品", 0, "40xcy_yyzn_301", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity"}},
	{2057, "小精灵游学记领奖{num}天", 0, "37ycdl_props_306", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2492}},
	{2060, "抓到{num}只宠物", 0, "40xcy_yyzn_401", {"pet1","pet2"}, nil, nil, nil, false, "ServerCommon", nil},
	{2061, "完成市集消消剧情第{count}关", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2415}},
	{2062, "成功解救{num}次人偶师", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="gameRoomWalkToTrigger",goParam=1}},
	{2064, "完成{id}", 0, "40xcy_yyzn_102", nil, nil, nil, nil, false, "ServerCommon", {goParam2=2,idType="task",goType="ShowView",goParam="TaskObjectivePanel"}},
	{2063, "成功制作人偶{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="gameRoomWalkToTrigger",goParam=1}},
	{2065, "通关花雨纷纷剧情第{count}关", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2308}},
	{2066, "在海底添置家具", 0, "", nil, nil, nil, nil, false, "ServerCommon", {goType="EnterSeaIslandAndPointToEdit"}},
	{2067, "在心森逐灵累计位移{num}米", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2341}},
	{2068, "在心森逐灵累计变身{num}次", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2341}},
	{2069, "通关港城好味剧情关卡第{count}关", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2389}},
	{2070, "在港城好味匹配模式累计获得{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2389}},
	{2071, "在市集消消单人模式累计获得{num}分", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2415}},
	{2072, "参加{num}次市集消消匹配模式", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2415}},
	{2073, "参加{num}次拼贴泡泡匹配模式", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2419}},
	{2074, "周年蛋糕提交{num}次材料", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2485}},
	{2075, "周年华服提交{num}次材料", 0, "youxi", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2484}},
	{2076, "加入{num}次周年巡游", 0, "40xcy_yyzn_106", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2497}},
	{2077, "参与{num}次爱心留言", 0, "40xcy_yyzn_107", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2483}},
	{2078, "参加{num}次织梦蛋糕", 0, "40xcy_yyzn_204", nil, nil, nil, nil, false, "ServerCommon", {goType="Activity",goParam=2444}},
	{5001, "完成{npcId}岛民跑图找物事件", 0, "", nil, nil, nil, {26}, false, "IslanderCommon", nil},
	{9001, "做{num}次双人动作", 0, "haoyou", {"shuangren1","shuangren2","shuangren3"}, nil, nil, nil, false, "FriendServerCommon", nil},
	{9002, "一起玩{num}次游戏盒子", 0, "", {"gamebox1","gamebox2"}, nil, nil, nil, false, "FriendServerCommon", nil},
	{9003, "一起玩{num}次场景多人游戏", 0, "", {"scenegame"}, nil, nil, nil, false, "FriendServerCommon", nil},
	{9004, "一起参加派对{num}次", 0, "", nil, nil, nil, {17}, false, "FriendServerCommon", {goType="ShowView",goParam="PartyMainView"}},
	{9005, "各给对方打理友情树{num}次", 0, "", nil, nil, nil, nil, false, "GoToFriendIsland", nil},
	{9006, "各给对方除虫{num}次", 0, "", nil, nil, nil, nil, false, "GoToFriendIsland", nil},
	{9007, "各食用对方料理{num}次", 0, "", nil, nil, nil, nil, false, "GoToFriendIsland", nil},
	{9008, "使用彼此的分享物{num}次", 0, "", {"share1","share2","share3","share4"}, nil, nil, nil, false, "FriendServerCommon", nil},
	{9009, "各给对方留言板留言{num}次", 0, "", nil, nil, nil, nil, false, "GoToFriendIsland", nil},
	{9101, "与队长一起做{num}次双人动作", 0, "haoyou", {"shuangren1","shuangren2","shuangren3"}, nil, nil, nil, false, "ServerCommon", nil},
	{9102, "与队长一起玩{num}次游戏盒子", 0, "", {"gamebox1","gamebox2"}, nil, nil, nil, false, "ServerCommon", nil},
	{9103, "与队长一起玩{num}次场景游戏", 0, "", {"scenegame"}, nil, nil, nil, false, "ServerCommon", nil},
	{9104, "与队长一起参加派对{num}次", 0, "", nil, nil, nil, {17}, false, "ServerCommon", {goType="ShowView",goParam="PartyMainView"}},
	{9105, "给队长打理友情树{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{9106, "给队长除虫{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{9107, "食用队长料理{num}次", 0, "", nil, nil, nil, nil, false, "ServerCommon", nil},
	{9108, "使用队长的分享物{num}次", 0, "", {"share1","share2","share3","share4"}, nil, nil, nil, false, "ServerCommon", nil},
	{10000, "完成剧情", 0, "", nil, nil, nil, nil, false, "AllCmdCompleted", nil},
	{10001, "与{id}聊天", 0, "", nil, nil, nil, nil, false, "TalkToNPC", nil},
	{10002, "抵达{id}", 10, "", nil, nil, nil, nil, false, "GoToScene", nil},
	{10003, "修改1次个性签名", 50, "", nil, nil, nil, nil, false, "ShowView", {viewName="InfoCardPanel",goType="ShowView",goParam="InfoCardPanel"}},
	{10004, "返回小屋", 10, "", nil, nil, nil, nil, false, "GoToScene", {tag="MyHouse"}},
	{10005, "返回小岛", 10, "", nil, nil, nil, nil, false, "GoToScene", {tag="MyIsland"}},
	{10006, "拍照1次", 0, "", {"takephoto","takephoto2","takephoto3"}, nil, nil, nil, false, "ClientEvent", {eventKey="OnTakePhotoSucc"}},
	{10007, "逛一次奥柏百货", 0, "jiaoyi", nil, nil, nil, nil, false, "ShowView", {viewName="Recharge",goType="ShowView",goParam="TimeStore"}},
	{10008, "找{npcId}闲聊{num}次", 0, "", nil, nil, nil, nil, false, "ChatToNPC", nil},
	{10009, "找{npcId}闲聊{num}次", 0, "", {"lxzxianliao1","lxzxianliao2"}, nil, nil, {33}, false, "ChatToTravellerNPC", nil},
	{10010, "逛一次树屋商店", 0, "", nil, nil, nil, {28}, false, "ClientEvent", {goParam2=5,eventParam=5,goType="ShowView",goParam="GroceryView",eventKey="OnShowGrocery"}},
	{10011, "和旅行者闲聊{num}次", 0, "", {"lxzxianliao1","lxzxianliao2"}, nil, nil, {33}, false, "ChatToTravellerNPC", nil},
	{10012, "逛1次{groceryId}", 0, "jiaoyi", nil, nil, nil, nil, false, "ClientEvent_Grocery", {goType="GotoGrocery",eventKey="OnShowGrocery"}},
	{10013, "打开1次旅行者面板", 0, "", nil, nil, nil, {33}, false, "ShowView", {viewName="TravellerRolePanel",goType="ShowView",goParam="TravellerRolePanel"}},
	{10014, "查看1次精灵信息", 0, "", nil, nil, nil, nil, false, "ClientEvent", {goType="GotoElf",eventKey="OnOpenElfInfoView",checkParam={[1]={key="ElfEgg"}}}},
	{10015, "与精灵互动1次", 0, "", nil, nil, nil, nil, false, "ClientEvent", {goType="GotoElf",eventKey="OnPlayElf",checkParam={[1]={key="ElfEgg"}}}},
	{10016, "逛1次魔法学堂", 0, "", nil, nil, nil, nil, false, "ClientEvent", {goType="GotoNPC",goParam={npcId=1},eventKey="OnOpenElfSchool",checkParam={[1]={key="ElfEgg"}}}},
	{10017, "点击家具", 0, "", nil, nil, nil, nil, false, "ClientEvent_ClickFurniture", nil},
	{10018, "打开1次旅行者面板", 0, "", nil, nil, nil, {33}, false, "ShowView", {viewName="TravellerRolePanel",goType="ShowView",goParam="TravellerRolePanel"}},
	{10019, "打开1次冰柠有礼", 0, "", nil, nil, nil, nil, false, "ShowView", {viewName="PleasanceStarWishCardView",goType="Activity",goParam=1417}},
	{10020, "找{npcId}打招呼{num}次", 0, "", nil, nil, nil, nil, false, "ChatToNPC", {isOpenDialog=true}},
	{10021, "拜访1次阿拉斯的魔药店", 0, "", nil, nil, nil, nil, false, "ShowView", {viewName="PotionsRefineView",goType="GotoLocation",goParam={posX=9.32,posY=26.35,onEnd={goType="ShowView",goParam="PotionsRefineView"},sceneId=8}}},
	{10022, "打开一次铸星面板", 0, "", nil, nil, nil, nil, false, "ShowView", {viewName="Recycle",goType="GotoLocation",goParam={posX=9.61,posY=15.09,sceneId=17}}},
	{10023, "参加1次嘉年华", 0, "youxi", nil, nil, nil, nil, false, "ClientEvent", {goType="Activity",goParam=1387,eventKey="OnGalaEnterScene"}},
	{10024, "进入一次精灵的家", 10, "", nil, nil, nil, nil, false, "GoToScene", {tag="MyElfRoom"}},
	{10025, "打开一次精灵的家房型更换界面", 10, "", nil, nil, nil, nil, false, "ClientEvent", {eventKey="OnOpenElfHouseList"}},
	{10026, "打开一次精灵的家“衣柜”界面", 10, "", nil, nil, nil, nil, false, "ShowView", {viewName="ElfDressPanel",goType="GotoElfDress"}},
	{10027, "打开一次精灵的家拜访界面", 10, "", nil, nil, nil, nil, false, "ShowView", {viewName="ElfFriendView",goType="GotoFurniture",goParam={[1]=13000729}}},
	{10028, "完成指定剧情", 0, "", nil, nil, nil, nil, false, "TaskCmdCompleted", nil},
	{10029, "打开一次样板广场", 0, "yangban", nil, nil, nil, nil, false, "ShowView", {viewName="FurnitureTempalteSquare",goType="ShowView",goParam="DressMatchEntrance"}},
	{10030, "打开一次游戏狂欢周", 0, "youxi", nil, nil, nil, nil, false, "ShowView", {viewName="TinyGameTimeView",goType="MultiActivity",goParam="2466"}},
	{10031, "打开一次奥比剧", 0, "youxi", nil, nil, nil, nil, false, "ShowView", {viewName="StoryTellerMenuPanel",goType="ShowView",goParam="StoryTellerMenuPanel"}},
	{10032, "进入一次海底小岛", 0, "", nil, nil, nil, nil, false, "GoToScene", {tag="MySea"}},
	{10033, "完成剧情", 1, "", nil, nil, nil, nil, false, "AllCmdCompleted", nil},
	{10034, "打开一次捕鱼狂欢周", 0, "youxi", nil, nil, nil, nil, false, "ShowView", {viewName="ActivitySeaShopView",goType="MultiActivity",goParam="2465"}},
	{10035, "打开一次周年蛋糕", 0, "40xcy_yyzn_104", nil, nil, nil, nil, false, "ShowView", {viewName="CarnivalPartyCakeView",goType="MultiActivity",goParam="2485"}},
	{10036, "打开一次周年华服", 0, "40xcy_yyzn_105", nil, nil, nil, nil, false, "ShowView", {viewName="AnniversaryCostumeView",goType="MultiActivity",goParam="2484"}},
	{10037, "打开1次周年限定累充", 0, "40xcy_yyzn_304", nil, nil, nil, nil, false, "ShowView", {viewName="LimitRechargeView",goType="MultiActivity",goParam="2482"}},
	{20001, "逛1次冰雪市集", 0, "", nil, nil, nil, nil, false, "ClientEvent", {goParam2=2,goType="ShowView",goParam="SnowCelebration",eventKey="OnClickIceMarket"}},
	{20002, "分享{num}次蜂王涂鸦作品到奥比圈", 0, "youxi", nil, nil, nil, nil, false, "ClientEvent", {goType="Activity",goParam=1142,eventKey="OnShareGraffitiSucc"}},
	{20003, "分享{num}次境之森活动", 0, "youxi", nil, nil, nil, nil, false, "ClientEvent", {goType="Activity",goParam=1341,eventKey="OnShareMirrorForestSucc"}},
	{30001, "拍摄区域照片", 0, "", nil, nil, nil, nil, false, "Snapshot", nil},
	{30002, "打开1次绘声演绎", 0, "37ycdl_props_303", nil, nil, nil, nil, false, "ShowView", {viewName="ActivityRabbitSpeakersView",goType="Activity",goParam=2248}},
	{30003, "打开1次织梦乐章", 0, "37ycdl_props_305", nil, nil, nil, nil, false, "ShowView", {viewName="ActivityRabbitGiftView",goType="Activity",goParam=2249}},
	{30004, "拍摄区域照片", 0, "40xcy_yyzn_402", nil, nil, nil, nil, false, "Snapshot", nil},
	{50001, "唤醒石巨人", 0, "", nil, nil, nil, nil, false, "IslanderCommon", nil},
	{50002, "铃铛记者", 0, "", nil, nil, nil, nil, false, "IslanderCommon", nil},
	{50003, "风儿种子商人", 0, "", nil, nil, nil, nil, false, "IslanderCommon", nil},
	{50004, "小偷", 0, "", nil, nil, nil, nil, false, "IslanderCommon", nil},
	{50005, "哈根赶羊", 0, "", nil, nil, nil, nil, false, "IslanderCommon", nil},
	{50006, "好运者", 0, "", nil, nil, nil, nil, false, "IslanderCommon", nil},
	{60001, "在自己家床铺睡觉", 0, "", nil, nil, nil, nil, false, "GoToBed", nil},
	{60002, "阅读{id}", 0, "", nil, nil, nil, nil, false, "Read", nil},
	{60003, "摆放物品", 0, "", nil, nil, nil, nil, false, "DragItem", nil},
	{60004, "点击物品", 0, "", nil, nil, nil, nil, false, "ClickSceneObj", nil},
	{60005, "完成交互", 0, "", nil, nil, nil, nil, false, "TinyGame", nil},
	{60006, "点击证据", 0, "", nil, nil, nil, nil, false, "ClickSceneEventObj", nil},
	{90001, "在同线同场景{num}分钟", 0, "", nil, nil, nil, nil, false, "", nil},
	{90002, "一起演奏{num}次", 0, "", nil, nil, nil, nil, false, "PlayingMusic", {goParam={posX=-1.16,posY=-20.80,sceneId=6}}},
	{90003, "一起坐跷跷板{num}次", 0, "", nil, nil, nil, nil, false, "Seesaw", {goParam={posX=-8.61,posY=-9.10,sceneId=8}}},
	{90004, "一起参加茶话会{num}次", 0, "", nil, nil, nil, nil, false, "", nil},
	{90005, "一起被皮皮藤甩起{num}次", 0, "", nil, nil, nil, nil, false, "", nil},
	{90006, "一起拍大头贴{num}次", 0, "", nil, nil, nil, nil, false, "MakeBigHead", {goParam={posX=3.08,posY=-22.83,sceneId=6}}},
	{90007, "拍摄照片", 0, "37ycdl_props_408", nil, nil, nil, nil, false, "CheckInAndSnapshot", nil},
}

local t_task_objective_define = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
	[9] = dataList[9],
	[10] = dataList[10],
	[11] = dataList[11],
	[12] = dataList[12],
	[13] = dataList[13],
	[14] = dataList[14],
	[15] = dataList[15],
	[16] = dataList[16],
	[17] = dataList[17],
	[18] = dataList[18],
	[19] = dataList[19],
	[20] = dataList[20],
	[21] = dataList[21],
	[22] = dataList[22],
	[23] = dataList[23],
	[24] = dataList[24],
	[25] = dataList[25],
	[26] = dataList[26],
	[27] = dataList[27],
	[28] = dataList[28],
	[29] = dataList[29],
	[30] = dataList[30],
	[31] = dataList[31],
	[32] = dataList[32],
	[33] = dataList[33],
	[34] = dataList[34],
	[35] = dataList[35],
	[36] = dataList[36],
	[37] = dataList[37],
	[38] = dataList[38],
	[39] = dataList[39],
	[40] = dataList[40],
	[41] = dataList[41],
	[42] = dataList[42],
	[43] = dataList[43],
	[44] = dataList[44],
	[45] = dataList[45],
	[46] = dataList[46],
	[47] = dataList[47],
	[48] = dataList[48],
	[49] = dataList[49],
	[50] = dataList[50],
	[51] = dataList[51],
	[52] = dataList[52],
	[53] = dataList[53],
	[54] = dataList[54],
	[55] = dataList[55],
	[56] = dataList[56],
	[57] = dataList[57],
	[58] = dataList[58],
	[59] = dataList[59],
	[60] = dataList[60],
	[61] = dataList[61],
	[62] = dataList[62],
	[63] = dataList[63],
	[64] = dataList[64],
	[66] = dataList[65],
	[67] = dataList[66],
	[68] = dataList[67],
	[69] = dataList[68],
	[70] = dataList[69],
	[71] = dataList[70],
	[72] = dataList[71],
	[73] = dataList[72],
	[74] = dataList[73],
	[75] = dataList[74],
	[76] = dataList[75],
	[77] = dataList[76],
	[78] = dataList[77],
	[79] = dataList[78],
	[80] = dataList[79],
	[81] = dataList[80],
	[82] = dataList[81],
	[83] = dataList[82],
	[84] = dataList[83],
	[85] = dataList[84],
	[86] = dataList[85],
	[87] = dataList[86],
	[88] = dataList[87],
	[89] = dataList[88],
	[90] = dataList[89],
	[91] = dataList[90],
	[92] = dataList[91],
	[93] = dataList[92],
	[94] = dataList[93],
	[95] = dataList[94],
	[96] = dataList[95],
	[97] = dataList[96],
	[98] = dataList[97],
	[99] = dataList[98],
	[100] = dataList[99],
	[101] = dataList[100],
	[102] = dataList[101],
	[103] = dataList[102],
	[104] = dataList[103],
	[105] = dataList[104],
	[106] = dataList[105],
	[107] = dataList[106],
	[108] = dataList[107],
	[109] = dataList[108],
	[110] = dataList[109],
	[111] = dataList[110],
	[112] = dataList[111],
	[113] = dataList[112],
	[114] = dataList[113],
	[115] = dataList[114],
	[116] = dataList[115],
	[117] = dataList[116],
	[118] = dataList[117],
	[119] = dataList[118],
	[120] = dataList[119],
	[121] = dataList[120],
	[122] = dataList[121],
	[123] = dataList[122],
	[124] = dataList[123],
	[125] = dataList[124],
	[126] = dataList[125],
	[127] = dataList[126],
	[128] = dataList[127],
	[129] = dataList[128],
	[130] = dataList[129],
	[131] = dataList[130],
	[132] = dataList[131],
	[133] = dataList[132],
	[134] = dataList[133],
	[135] = dataList[134],
	[136] = dataList[135],
	[137] = dataList[136],
	[138] = dataList[137],
	[139] = dataList[138],
	[140] = dataList[139],
	[141] = dataList[140],
	[142] = dataList[141],
	[143] = dataList[142],
	[144] = dataList[143],
	[145] = dataList[144],
	[146] = dataList[145],
	[147] = dataList[146],
	[148] = dataList[147],
	[149] = dataList[148],
	[150] = dataList[149],
	[151] = dataList[150],
	[152] = dataList[151],
	[153] = dataList[152],
	[154] = dataList[153],
	[155] = dataList[154],
	[156] = dataList[155],
	[157] = dataList[156],
	[158] = dataList[157],
	[159] = dataList[158],
	[160] = dataList[159],
	[161] = dataList[160],
	[162] = dataList[161],
	[163] = dataList[162],
	[164] = dataList[163],
	[165] = dataList[164],
	[166] = dataList[165],
	[167] = dataList[166],
	[168] = dataList[167],
	[169] = dataList[168],
	[170] = dataList[169],
	[171] = dataList[170],
	[172] = dataList[171],
	[173] = dataList[172],
	[174] = dataList[173],
	[177] = dataList[174],
	[178] = dataList[175],
	[179] = dataList[176],
	[180] = dataList[177],
	[181] = dataList[178],
	[182] = dataList[179],
	[183] = dataList[180],
	[184] = dataList[181],
	[185] = dataList[182],
	[186] = dataList[183],
	[187] = dataList[184],
	[188] = dataList[185],
	[189] = dataList[186],
	[190] = dataList[187],
	[191] = dataList[188],
	[192] = dataList[189],
	[193] = dataList[190],
	[194] = dataList[191],
	[195] = dataList[192],
	[196] = dataList[193],
	[197] = dataList[194],
	[198] = dataList[195],
	[199] = dataList[196],
	[200] = dataList[197],
	[201] = dataList[198],
	[202] = dataList[199],
	[203] = dataList[200],
	[204] = dataList[201],
	[205] = dataList[202],
	[206] = dataList[203],
	[207] = dataList[204],
	[208] = dataList[205],
	[209] = dataList[206],
	[210] = dataList[207],
	[211] = dataList[208],
	[212] = dataList[209],
	[213] = dataList[210],
	[214] = dataList[211],
	[215] = dataList[212],
	[216] = dataList[213],
	[217] = dataList[214],
	[218] = dataList[215],
	[219] = dataList[216],
	[220] = dataList[217],
	[221] = dataList[218],
	[222] = dataList[219],
	[2001] = dataList[220],
	[2002] = dataList[221],
	[2003] = dataList[222],
	[2004] = dataList[223],
	[2005] = dataList[224],
	[2006] = dataList[225],
	[2007] = dataList[226],
	[2008] = dataList[227],
	[2009] = dataList[228],
	[2010] = dataList[229],
	[2011] = dataList[230],
	[2012] = dataList[231],
	[2013] = dataList[232],
	[2014] = dataList[233],
	[2015] = dataList[234],
	[2016] = dataList[235],
	[2017] = dataList[236],
	[2018] = dataList[237],
	[2019] = dataList[238],
	[2020] = dataList[239],
	[2021] = dataList[240],
	[2022] = dataList[241],
	[2023] = dataList[242],
	[2024] = dataList[243],
	[2025] = dataList[244],
	[2026] = dataList[245],
	[2027] = dataList[246],
	[2028] = dataList[247],
	[2029] = dataList[248],
	[2030] = dataList[249],
	[2031] = dataList[250],
	[2032] = dataList[251],
	[2033] = dataList[252],
	[2034] = dataList[253],
	[2035] = dataList[254],
	[2036] = dataList[255],
	[2037] = dataList[256],
	[2038] = dataList[257],
	[2039] = dataList[258],
	[2040] = dataList[259],
	[2041] = dataList[260],
	[2042] = dataList[261],
	[2043] = dataList[262],
	[2044] = dataList[263],
	[2045] = dataList[264],
	[2046] = dataList[265],
	[2047] = dataList[266],
	[2048] = dataList[267],
	[2049] = dataList[268],
	[2050] = dataList[269],
	[2051] = dataList[270],
	[2053] = dataList[271],
	[2054] = dataList[272],
	[2055] = dataList[273],
	[2056] = dataList[274],
	[2057] = dataList[275],
	[2060] = dataList[276],
	[2061] = dataList[277],
	[2062] = dataList[278],
	[2064] = dataList[279],
	[2063] = dataList[280],
	[2065] = dataList[281],
	[2066] = dataList[282],
	[2067] = dataList[283],
	[2068] = dataList[284],
	[2069] = dataList[285],
	[2070] = dataList[286],
	[2071] = dataList[287],
	[2072] = dataList[288],
	[2073] = dataList[289],
	[2074] = dataList[290],
	[2075] = dataList[291],
	[2076] = dataList[292],
	[2077] = dataList[293],
	[2078] = dataList[294],
	[5001] = dataList[295],
	[9001] = dataList[296],
	[9002] = dataList[297],
	[9003] = dataList[298],
	[9004] = dataList[299],
	[9005] = dataList[300],
	[9006] = dataList[301],
	[9007] = dataList[302],
	[9008] = dataList[303],
	[9009] = dataList[304],
	[9101] = dataList[305],
	[9102] = dataList[306],
	[9103] = dataList[307],
	[9104] = dataList[308],
	[9105] = dataList[309],
	[9106] = dataList[310],
	[9107] = dataList[311],
	[9108] = dataList[312],
	[10000] = dataList[313],
	[10001] = dataList[314],
	[10002] = dataList[315],
	[10003] = dataList[316],
	[10004] = dataList[317],
	[10005] = dataList[318],
	[10006] = dataList[319],
	[10007] = dataList[320],
	[10008] = dataList[321],
	[10009] = dataList[322],
	[10010] = dataList[323],
	[10011] = dataList[324],
	[10012] = dataList[325],
	[10013] = dataList[326],
	[10014] = dataList[327],
	[10015] = dataList[328],
	[10016] = dataList[329],
	[10017] = dataList[330],
	[10018] = dataList[331],
	[10019] = dataList[332],
	[10020] = dataList[333],
	[10021] = dataList[334],
	[10022] = dataList[335],
	[10023] = dataList[336],
	[10024] = dataList[337],
	[10025] = dataList[338],
	[10026] = dataList[339],
	[10027] = dataList[340],
	[10028] = dataList[341],
	[10029] = dataList[342],
	[10030] = dataList[343],
	[10031] = dataList[344],
	[10032] = dataList[345],
	[10033] = dataList[346],
	[10034] = dataList[347],
	[10035] = dataList[348],
	[10036] = dataList[349],
	[10037] = dataList[350],
	[20001] = dataList[351],
	[20002] = dataList[352],
	[20003] = dataList[353],
	[30001] = dataList[354],
	[30002] = dataList[355],
	[30003] = dataList[356],
	[30004] = dataList[357],
	[50001] = dataList[358],
	[50002] = dataList[359],
	[50003] = dataList[360],
	[50004] = dataList[361],
	[50005] = dataList[362],
	[50006] = dataList[363],
	[60001] = dataList[364],
	[60002] = dataList[365],
	[60003] = dataList[366],
	[60004] = dataList[367],
	[60005] = dataList[368],
	[60006] = dataList[369],
	[90001] = dataList[370],
	[90002] = dataList[371],
	[90003] = dataList[372],
	[90004] = dataList[373],
	[90005] = dataList[374],
	[90006] = dataList[375],
	[90007] = dataList[376],
}

t_task_objective_define.dataList = dataList
local mt
if TaskObjectiveDefine then
	mt = {
		__cname =  "TaskObjectiveDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or TaskObjectiveDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_task_objective_define