module("logic.extensions.scene.view.mappanel.MapPanel", package.seeall)
local MapPanel = class("MapPanel", ViewComponent)
local R = {
    Tabs = {
        {
            path = "tabsGo/btnIsland",
            ePage = 1,
            requestHandler = function(self)
                TaskAgent.instance:sendIslanderEventInfoRequest(handler(self._refreshIslanderItem, self))
                self:notify(GlobalNotify.ClickEventTab)
                removetimer(self._secondHandler, self)
            end
        },
        {
            path = "tabsGo/btnCollect",
            ePage = 2,
            requestHandler = function(self)
                CollectionAgent.instance:sendGetAllSceneCollectionInfoRequest(handler(self._refreshCollectItem, self))

                settimer(1, self._secondHandler, self, true)
            end
        },
        {
            path = "tabsGo/btnParttime",
            ePage = 3,
            requestHandler = function(self)
                ParttimeController.instance:getParttimeInfoRequest(handler(self._refreshParttimeItem, self))

                removetimer(self._secondHand<PERSON>, self)
            end
        },
        {
            path = "tabsGo/btnInsects",
            ePage = 4,
            requestHandler = function(self)
                self:_hideItem()
                self:notify(GlobalNotify.ClickGoCatch)
            end
        },
        {
            path = "tabsGo/btnWeather",
            ePage = 5,
            requestHandler = function(self)
                self:_hideItem()
                self:notify(GlobalNotify.OnClickWeatherTab)
            end
        }
    }
}

function MapPanel:ctor()
    MapPanel.super.ctor(self)
    self._tabList = {}
    self._btnList = {}
    self._btnSeaList = {}
    self._selectedPage = nil
    self._islandItemGoList = {}
    self._collectItemGoList = {}
    self._parttimeItemGoList = {}
end

function MapPanel:buildUI()
    self:_initTabs()
    self:_initBtns()

    self._imgSign = self:getGo("imgSign")
    self._btnClose = self:getBtn("btnClose/btnClose")
    self._btnProduce = self:getBtn("bottomGo/btnProduce")
    self._btnEvent = self:getBtn("bottomGo/btnEvent")
    self._friendWishredPoint = self:getGo("listview/content/btnsGo/btnScene15/tipsGo_1")
    self._relativeAsk = self:getGo("listview/content/btnsGo/btnScene15/tipsGo_2")
    self._imgMushroom = self:getGo("listview/content/btnsGo/btnScene15/imgMushroom")
    self._btnRandom = self:getBtn("btnRandom")
    self._goTabs = self:getGo("tabsGo")
    self._goBottom = self:getGo("bottomGo")
    self._goImgHome_1 = self:getGo("btnScene5/img_1")
    self._goImgHome_2 = self:getGo("btnScene5/img_2")
    self._goImgRandom_1 = self:getGo("btnScene4/img_1")
    self._goImgRandom_2 = self:getGo("btnScene4/img_2")
    self._goBtnScene5 = self:getGo("btnScene5")
    self._goBtnScene4 = self:getGo("btnScene4")
    self._goBtnRandom = self:getGo("btnRandom")


    self._btnArea = {}
    self._btnArea[1] = self:getBtn("islandChangeGo/btnIsland")
    self._btnArea[2] = self:getBtn("islandChangeGo/btnSea")
    self._goListView = self:getGo("listview")
    self._scrollRect = self._goListView:GetComponent("ScrollRect")
    self._dragTrigger = Framework.UIDragTrigger.Get(self._goListView)
    self._btnMyIsland = self:getBtn("btnScene5")
    self._btnMyHome = self:getBtn("btnScene4")

    self._goGuides = {}
    self._goGuides[1] = self:getGo("islandChangeGo/btnSea/unSelect/guide_1")
    self._goGuides[2] = self:getGo("btnScene5/img_2/guide_2")


    self._goEventBg = self:getGo("bottomGo/btnEvent")
    self._goEvent = self:getGo("bottomGo/btnEvent/imgText")
    self._goSeaBoss = self:getGo("bottomGo/btnEvent/imgSeaText")
    self._goSeaSeaBus = self:getGo("bottomGo/btnEvent/imgSeaBusText")
end

function MapPanel:destroyUI()
    for _, go in ipairs(self._islandItemGoList) do
        goutil.destroy(go)
    end
    for _, go in ipairs(self._collectItemGoList) do
        goutil.destroy(go)
    end
    for _, go in ipairs(self._parttimeItemGoList) do
        goutil.destroy(go)
    end
    self._tabList = nil
    self._btnList = nil
    self._btnSeaList = nil
    self._islandItemGoList = nil
    self._collectItemGoList = nil
    self._parttimeItemGoList = nil
end

function MapPanel:_initTabs()
    for _, v in ipairs(R.Tabs) do
        local ePage = v.ePage
        local btnGo = self:getBtn(v.path)
        local selectedGo = goutil.findChild(btnGo.gameObject, "selected")
        local unselectedGo = goutil.findChild(btnGo.gameObject, "unselected")
        local tab = {
            ePage = ePage,
            btnGo = btnGo,
            selectedGo = selectedGo,
            unselectedGo = unselectedGo,
            requestHandler = v.requestHandler
        }
        self._tabList[ePage] = tab
    end
end

function MapPanel:_initBtns()
    local trs = self:getGo("listview/content/btnsGo").transform
    local count = trs.childCount
    for i = count, 1, -1 do
        local go = trs:GetChild(i - 1).gameObject
        local s = go.name
        local index = string.find(s, "%d")
        local sceneId = tonumber(string.sub(s, index))
        local typeStr = string.sub(s, 0, index - 1)
        local listIslandGo = goutil.findChild(go, "listIslandGo")
        local listCollectGo = goutil.findChild(go, "listCollectGo")
        local listParttimeGo = goutil.findChild(go, "listParttimeGo")
        local signGo = goutil.findChild(go, "sign")
        local btn = {
            go = go,
            sceneId = sceneId,
            listIslandGo = listIslandGo,
            listCollectGo = listCollectGo,
            listParttimeGo = listParttimeGo,
            signGo = signGo
        }
        if typeStr == "btnScene" then
            self._btnList[sceneId] = btn
        elseif typeStr == "btnSea_" then
            self._btnSeaList[sceneId] = btn
        end
    end
end

function MapPanel:_switchTo(ePage)
    if self._selectedPage == ePage then
        return
    end
    self._selectedPage = ePage

    for _, tab in pairs(self._tabList) do
        goutil.setActive(tab.selectedGo.gameObject, ePage == tab.ePage)
        goutil.setActive(tab.unselectedGo.gameObject, ePage ~= tab.ePage)
    end

    local requestHandler = self._tabList[self._selectedPage].requestHandler
    requestHandler(self)
end

function MapPanel:bindEvents()
    for _, tab in pairs(self._tabList) do
        tab.btnGo:AddClickListener(self._onClickTab, self, tab)
    end
    for _, btn in pairs(self._btnList) do
        Framework.UIClickTrigger.Get(btn.go):AddClickListener(self._onClickBtn, self, btn.sceneId)
        local arrowKey = "Map_Scene_" .. tostring(btn.sceneId)
        if GuideArrowKey[arrowKey] then
            GuideArrowManager.instance:regKeyGO(arrowKey, btn.go)
        end
    end

    for _, btn in pairs(self._btnSeaList) do
        Framework.UIClickTrigger.Get(btn.go):AddClickListener(self._onClickSeaBtn, self, btn.sceneId)
    end

    for i=1,2 do
        self._btnArea[i]:AddClickListener(self._onClickArea, self, {i})
    end

    self._dragTrigger:AddBeginDragListener(self._onBeginDrag, self)
    self._dragTrigger:AddEndDragListener(self._onEndDrag, self)

    self._btnClose:AddClickListener(self._onClickBtnClose, self)
    self._btnProduce:AddClickListener(self._onClickBtnProduce, self)
    self._btnEvent:AddClickListener(self._onClickBtnEvent, self)
    self._btnRandom:AddClickListener(self._onClickRandom, self)
    self._btnMyHome:AddClickListener(self._onClickMyHome, self)
    self._btnMyIsland:AddClickListener(self._onClickMyIsland, self)
    --self._btnSeaScene:AddClickListener(self._onClickSeaScene, self)

    self._customCheckEnter = {}
    self._customCheckEnter[109] = handler(self._checkEnterShareStreet, self)
end

function MapPanel:unbindEvents()
    for _, tab in pairs(self._tabList) do
        tab.btnGo:RemoveClickListener()
    end
    for _, btn in pairs(self._btnList) do
        Framework.UIClickTrigger.Get(btn.go):RemoveClickListener()
        local arrowKey = "Map_Scene_" .. tostring(btn.sceneId)
        if GuideArrowKey[arrowKey] then
            GuideArrowManager.instance:unregKeyGO(arrowKey)
        end
    end

    for _, btn in pairs(self._btnSeaList) do
        Framework.UIClickTrigger.Get(btn.go):RemoveClickListener()
    end

    for i=1,2 do
        self._btnArea[i]:RemoveClickListener()
    end

    self._dragTrigger:RemoveBeginDragListener()
    self._dragTrigger:RemoveEndDragListener()

    self._btnMyHome:RemoveClickListener()
    self._btnMyIsland:RemoveClickListener()
    self._btnClose:RemoveClickListener()
    self._btnProduce:RemoveClickListener()
    self._btnEvent:RemoveClickListener()
    self._btnRandom:RemoveClickListener()
    --self._btnSeaScene:RemoveClickListener()
end

function MapPanel:onEnter()
    CommonHUDFacade.instance:hideHUD("Map")

    -- 亲友红点提示
    local state = FriendModel.instance:getRelativeAsk()
    if state == true then
        local relativesstate = LocalStorage.instance:getValue(StorageKey.RelativesApplication,false)
        if relativesstate == false then
            goutil.setActive(self._relativeAsk,true)
        else
            self._prefabLoader = PrefabLoader.New(self.mainGO)
            self._prefabLoader:load("prefabs/affinitytrigger/ui_relative_h_01.prefab")
            goutil.setActive(self._relativeAsk,false)
        end
    else
        goutil.setActive(self._relativeAsk,false)
    end

    --活动相关数据
    self._isOpenSeaBoss = self:_checkActivityState(GameEnum.ActivityEnum.ACTIVITY_428)
    self._isOpenSeaCar = self:_checkActivityState(GameEnum.ActivityEnum.ACTIVITY_433)
    self._showEvent =  self._isOpenSeaBoss or self._isOpenSeaCar
    -- 亲友祝福红点显示
    local relativesBlessing = LocalStorage.instance:getValue(StorageKey.RelativesBlessingAdd,false)
    goutil.setActive(self._friendWishredPoint,false)
    goutil.setActive(self._imgMushroom,false)
    --printError(tostring(relativesBlessing) .. "== relativesBlessing")
    local wishred = self._imgMushroom
    if relativesBlessing == false then
        wishred = self._friendWishredPoint
    end
    RedPointController.instance:registerRedPoint(wishred, {
        "FriendRelative_Wish"
    })
    --local state = RedPointController.instance:getNodeByName("FriendRelative_Wish"):isExist()
    --printError(tostring(state) .. "== state")

    self:_updateArea()
    self:_updateSign()
    self:_updateSeaDouble()

    if not self._nowSeaMap then
        FuncUnlockFacade.instance:checkAndShowGuide(2)
    end

    local defaultSelect = self:getFirstParam() or 1
    self:_switchTo(defaultSelect)

    local showGuide = self:getOpenParam()[2] and true or false
    for _,v in pairs(self._goGuides) do
        v:SetActive(showGuide)
    end
end

function MapPanel:onExit()
    CommonHUDFacade.instance:showHUD("Map")
    removetimer(self._secondHandler, self)

    self._selectedPage = nil
    if self._prefabLoader then
        self._prefabLoader:clear()
        self._prefabLoader = nil
    end

    --消息红点
    local relativesBlessing = LocalStorage.instance:getValue(StorageKey.RelativesBlessingAdd,false)
    local wishred = self._imgMushroom
    if relativesBlessing == false then
        wishred = self._friendWishredPoint
    end
    RedPointController.instance:unregisterRedPoint(wishred, {
        "FriendRelative_Wish"
    })

    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

    self._isTweening = false
end

function MapPanel:_onClickBtnClose()
    self:close()
end

function MapPanel:_onClickBtnProduce()
    if self._nowSeaMap then
        ViewMgr.instance:open("SeaProductNumPanel")
    else
        ViewMgr.instance:open("ProductNumPanel")
    end

end

function MapPanel:_onClickBtnEvent()
    if not self._nowSeaMap then
        ViewMgr.instance:open("IslanderEventBook")
    else
        if self._isOpenSeaBoss then
            ViewMgr.instance:open("SeaBossEventView")
        elseif self._isOpenSeaCar then
            Activity433Agent.instance:sendAct433GetInfoRequest(function(msg)
                SeaSpaCarController.instance:SaveDailyAwardTimesLimit(msg.todayAwardTimes)
                SeaSpaCarController.instance:saveDailyAwardDepth(msg.nowDepth)
                ViewMgr.instance:open("SeaBusRuleView")
            end)
        end
    end
end

function MapPanel:_onClickRandom()
    SceneAgent.instance:sendRanVisitUserIdRequest(function(roleInfo)
        self:close()
        RoomFacade.instance:enterRoomOrIsland(roleInfo.id, false, nil, nil, true)
    end)
end

function MapPanel:_onClickSeaScene()
    self:close()
    ViewMgr.instance:open("SeaChooseDepthPanel")
end

function MapPanel:_onClickTab(tab)
    SoundManager.instance:playClickSound()
    self:_switchTo(tab.ePage)
end

function MapPanel:_onClickBtn(evt, sceneId)
    SoundManager.instance:playClickSound()
    if not TaskFacade.instance:isNewbieTaskFinish() then
        self:close()
        GlobalDispatcher:dispatch(GlobalNotify.OnClickMapIcon)
    else
        if self._customCheckEnter[sceneId] then
            self._customCheckEnter[sceneId]()
            return
        end
        if not SceneConfig.getSceneConfig(sceneId) then
            FlyTextManager.instance:showFlyText(lang("该场景尚未开启"))
            return
        end
        if SceneConfig.checkEnter(sceneId) then
            self:close()
            SceneManager.instance:loadScene(sceneId)
        end
    end
end

function MapPanel:_onClickSeaBtn(evt, depth)
    SoundManager.instance:playClickSound()
    if not TaskFacade.instance:isNewbieTaskFinish() then
        self:close()
        GlobalDispatcher:dispatch(GlobalNotify.OnClickMapIcon)
    else
        if not SeaFacade.getDepthIsOpen(depth, true) then
            return
        else
            self:close()
            SeaFacade.gotoSea(depth)
        end
    end
end

function MapPanel:_updateSign()
    local isShow = false
    local curSceneId = SceneManager.instance:getCurSceneId()
    --水下小屋与小岛
    local isInSeaHouse = (table.indexof({80,81,82},curSceneId) and HouseModel.instance:isMyHouse()) and true or false
    local isInSeaIsland = curSceneId == 79 and HouseModel.instance:isMyHouse()
    --水上小屋
    local isInLandHouse = (table.indexof({4,9,10,11},curSceneId) and HouseModel.instance:isMyHouse()) and true or false
    if not SceneManager.instance:isInSea() and not isInSeaHouse and not isInSeaIsland then
        for _, btn in pairs(self._btnList) do
            if self:_isShowSign(btn.sceneId, curSceneId) then
                goutil.addChildToParent(self._imgSign, btn.signGo)
                isShow = true
                break
            end
        end

        --小岛小屋从列表分出来了 单独处理
        if isInLandHouse and not self._nowSeaMap then
            goutil.addChildToParent(self._imgSign, goutil.findChild(self._btnMyHome.gameObject,"sign"))
            isShow = true
        end

        if self:_isShowSign(5, curSceneId) and not self._nowSeaMap then
            goutil.addChildToParent(self._imgSign, goutil.findChild(self._btnMyIsland.gameObject,"sign"))
            isShow = true
        end
    else
        local depth = SeaFacade.getCurDepth()

        if isInSeaHouse and self._nowSeaMap then
            goutil.addChildToParent(self._imgSign, goutil.findChild(self._btnMyHome.gameObject,"sign"))
            isShow = true
        elseif isInSeaIsland and self._nowSeaMap then
            goutil.addChildToParent(self._imgSign, goutil.findChild(self._btnMyIsland.gameObject,"sign"))
            isShow = true
        else 
            for _, btn in pairs(self._btnSeaList) do
                if self:_isShowSign(btn.sceneId, depth) then
                    goutil.addChildToParent(self._imgSign, btn.signGo)
                    isShow = true
                    break
                end
            end
        end
    end

    goutil.setActive(self._imgSign, isShow)
end

function MapPanel:_isShowSign(sceneId, curSceneId)
    if HouseModel.instance:isInIsland(HouseModel.Block) or HouseModel.instance:isInHouse(true, HouseModel.Block) then
        return sceneId == 109 and (curSceneId == 109 or curSceneId == 110)
    elseif HouseModel.instance:isInHouseOrIsland(nil,nil,nil,HouseModel.HouseOrIsland) then -- 自己的小岛或小屋
        return HouseModel.instance:isInIsland() and sceneId == 5 or HouseModel.instance:isInHouse() and sceneId == 4
    elseif HouseModel.instance:isInHouseOrIsland(nil,HouseModel.Other,nil,HouseModel.HouseOrIsland) then -- 别人的小屋或小岛
        return false
    else
        return sceneId == curSceneId
    end
end

function MapPanel:_isOpen()
    return ViewMgr.instance:isOpen("MapPanel")
end

function MapPanel:_hideItem()
    for _, btn in pairs(self._btnList) do
        goutil.setActive(btn.listIslandGo, false)
        goutil.setActive(btn.listCollectGo, false)
        goutil.setActive(btn.listParttimeGo, false)
    end
    self:notify(GlobalNotify.HideGoCatchIcon)
    self:notify(GlobalNotify.HideWeatherIcon)
end

-- 刷新岛民事件列表
function MapPanel:_refreshIslanderItem()
    if not self:_isOpen() then
        return
    end
    self:_hideItem()

    local todayEvents = IslanderEventBookModel.instance:getTodayEvents()
    local i = 1

    for _, go in ipairs(self._islandItemGoList) do
        goutil.addChildToParent(go, nil)
    end
    for _, v in ipairs(todayEvents) do
        local eventHandler = IslanderEventController.instance:getHandler(v.eventType)
        local isMeet = eventHandler:isMeetNpc()
        if not isMeet then
            local btn = self._btnList[eventHandler:getSceneId()]
            if btn then
                local listIslandGo = btn.listIslandGo
                goutil.setActive(listIslandGo, true)

                local go = self._islandItemGoList[i]
                if go == nil then
                    go = self:getResInstance(MapPanelPresentor.Url_IslandItem)
                    table.insert(self._islandItemGoList, go)
                end
                local item = PjAobi.LuaComponent.GetOrAdd(go, MapPanelIslandItem)
                item:setMo({
                    npcId = v.npcId
                })
                goutil.setActive(go, true)
                goutil.addChildToParent(go, listIslandGo)

                i = i + 1
            end
        end
    end
end

-- 刷新采集列表
function MapPanel:_refreshCollectItem(scenecollections)
    if not self:_isOpen() then
        return
    end
    self:_hideItem()

    local i = 1

    for _, go in ipairs(self._collectItemGoList) do
        goutil.addChildToParent(go, nil)
    end
    for _, v in ipairs(scenecollections) do
        local btn = self._btnList[v.sceneId]
        if btn then
            local listCollectGo = btn.listCollectGo
            goutil.setActive(listCollectGo, true)

            for _, collect in ipairs(v.collection) do
                local cfg = CollectionConfig.getCollectionType(collect.type)
                local totalCdTime = CollectService.instance:getCdTime(cfg.id)

                local go = self._collectItemGoList[i]
                if go == nil then
                    go = self:getResInstance(MapPanelPresentor.Url_CollectItem)
                    table.insert(self._collectItemGoList, go)
                end
                local item = PjAobi.LuaComponent.GetOrAdd(go, MapPanelCollectItem)
                item:setMo({
                    itemId = cfg.baseOutput[1].id,
                    leftCdTime = collect.leftCdTime,
                    totalCdTime = totalCdTime,
                    count = collect.count,
                    isFull = collect.count >= cfg.maxCount
                })
                goutil.setActive(go, true)
                goutil.addChildToParent(go, goutil.findChild(listCollectGo, "listview/viewport/content"))

                i = i + 1
            end
        end
    end
end

-- 刷新打工列表
function MapPanel:_refreshParttimeItem()
    if not self:_isOpen() then
        return
    end
    self:_hideItem()

    local unlockParttimes = {}
    for _, cfg in ipairs(ParttimeConfig.getParttimeTypes()) do
        if ParttimeModel.instance:isUnlock(cfg.type) then
            if not unlockParttimes[cfg.sceneId] then
                unlockParttimes[cfg.sceneId] = {
                    sceneId = cfg.sceneId,
                    list = {}
                }
            end
            table.insert(unlockParttimes[cfg.sceneId].list, cfg)
        end
    end

    local i = 1

    for _, go in ipairs(self._parttimeItemGoList) do
        goutil.addChildToParent(go, nil)
    end
    for _, v in pairs(unlockParttimes) do
        local btn = self._btnList[v.sceneId]
        if btn then
            local listParttimeGo = btn.listParttimeGo
            goutil.setActive(listParttimeGo, true)

            for _, cfg in ipairs(v.list) do
                local go = self._parttimeItemGoList[i]
                if go == nil then
                    go = self:getResInstance(MapPanelPresentor.Url_ParttimeItem)
                    table.insert(self._parttimeItemGoList, go)
                end
                local item = PjAobi.LuaComponent.GetOrAdd(go, MapPanelParttimeItem)
                item:setMo({
                    icon = cfg.icon,
                    name = cfg.name,
                    isComplete = ParttimeModel.instance:isComplete(cfg.type)
                })
                goutil.setActive(go, true)
                goutil.addChildToParent(go, listParttimeGo)

                i = i + 1
            end
        end
    end
end

-- 每秒刷新函数
function MapPanel:_secondHandler()
    for _, go in ipairs(self._collectItemGoList) do
        local item = PjAobi.LuaComponent.Get(go, MapPanelCollectItem)
        item:countDownTime()
    end
end

function MapPanel:_updateArea()
    local sceneId = SceneManager.instance:getCurSceneId()
    local isInSea = SceneManager.instance:isInSea() or (table.indexof({79,80,81,82},sceneId) and true or false)
    goutil.findChild(self._btnArea[1].gameObject,"select"):SetActive(not isInSea)
    goutil.findChild(self._btnArea[1].gameObject,"unSelect"):SetActive(isInSea)
    goutil.findChild(self._btnArea[2].gameObject,"select"):SetActive(isInSea)
    goutil.findChild(self._btnArea[2].gameObject,"unSelect"):SetActive(not isInSea)
    --    self._btnEvent.gameObject:SetActive(not isInSea)
    --self._goBottom:SetActive(not isInSea)
    self._goTabs:SetActive(not isInSea)
    self._goImgHome_1:SetActive(not isInSea)
    self._goImgHome_2:SetActive(isInSea)
    self._goImgRandom_1:SetActive(not isInSea)
    self._goImgRandom_2:SetActive(isInSea)

    if not isInSea then
        self._scrollRect.verticalNormalizedPosition = 1
    else
        self._scrollRect.verticalNormalizedPosition = 0.15
    end

    self._nowSeaMap = isInSea
    self._goEvent.gameObject:SetActive(not self._nowSeaMap)
    if not self._nowSeaMap then
        self._goEventBg.gameObject:SetActive(true)
    else
        self._goEventBg.gameObject:SetActive(self._isOpenSeaBoss or self._isOpenSeaCar)
    end
    self._goSeaBoss.gameObject:SetActive(self._nowSeaMap and self._isOpenSeaBoss)
    self._goSeaSeaBus.gameObject:SetActive(self._nowSeaMap and self._isOpenSeaCar)
end

function MapPanel:_onClickArea(param)
    if self._isTweening then
        return
    end

    local i = param[1]
    if self._nowSeaMap == (i == 2) then
        return
    end

    self._nowSeaMap = i == 2

    goutil.findChild(self._btnArea[1].gameObject,"select"):SetActive(i == 1)
    goutil.findChild(self._btnArea[1].gameObject,"unSelect"):SetActive(i ~= 1)
    goutil.findChild(self._btnArea[2].gameObject,"select"):SetActive(i == 2)
    goutil.findChild(self._btnArea[2].gameObject,"unSelect"):SetActive(i ~= 2)

    self._goImgHome_1:SetActive(not self._nowSeaMap)
    self._goImgHome_2:SetActive(self._nowSeaMap)
    self._goImgRandom_1:SetActive(not self._nowSeaMap)
    self._goImgRandom_2:SetActive(self._nowSeaMap)
    --self._goBottom:SetActive(not self._nowSeaMap)
    --self._btnEvent.gameObject:SetActive(not self._nowSeaMap)

    self._goEvent.gameObject:SetActive(not self._nowSeaMap)
    if not self._nowSeaMap then
        self._goEventBg.gameObject:SetActive(true)
    else
        self._goEventBg.gameObject:SetActive(self._isOpenSeaBoss or self._isOpenSeaCar)
    end
    self._goSeaBoss.gameObject:SetActive(self._nowSeaMap and self._isOpenSeaBoss)
    self._goSeaSeaBus.gameObject:SetActive(self._nowSeaMap and self._isOpenSeaCar)

    self._goTabs:SetActive(not self._nowSeaMap)

    self._goBtnScene4:SetActive(false)
    self._goBtnScene5:SetActive(false)
    self._goBtnRandom:SetActive(false)

    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

    local nowPos = self._scrollRect.verticalNormalizedPosition
    local target = i == 1 and 1 or 0.15
    self.tween = DOTweenHelper.UpdateValue(nowPos,target, 0.3,
            self._updatePosValue, self):OnComplete(handler(self._onMovieEnd, self)):SetEase(DG.Tweening.Ease.InOutSine)
    self._isTweening = true

    local animation = self.mainGO:GetComponent("Animation")
    local animName = i == 1 and "ani_v3_0_mappanel_transform_02" or "ani_v3_0_mappanel_transform_01"
    animation:Play(animName)

    self:_updateSign()
end

function MapPanel:_updatePosValue(value)
    self._scrollRect.verticalNormalizedPosition = value
end

function MapPanel:_onMovieEnd(param)
    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

    self._goBtnScene4:SetActive(true)
    self._goBtnScene5:SetActive(true)
    self._goBtnRandom:SetActive(true)

    self._isTweening = false
end

function MapPanel:_onBeginDrag(pointEvent)
    self._startDragPos = pointEvent.position
end

function MapPanel:_onEndDrag(pointEvent)
    local offset = 0
    if self._startDragPos then
        offset = self._startDragPos.y - pointEvent.position.y
    end

    if math.abs(offset) > 50 then
        self:_onClickArea(offset > 0 and {1} or {2})
    end
end

function MapPanel:_onClickMyHome()
    if self._nowSeaMap and not FuncUnlockFacade.instance:checkIsUnlocked(60) then
        FlyTextManager.instance:showFlyText("请先解锁海底小屋")
        return
    end

    self:_onClickBtn(_,self._nowSeaMap and 80 or 4)
end

function MapPanel:_onClickMyIsland()
    if self._nowSeaMap and not FuncUnlockFacade.instance:checkIsUnlocked(60,true) then
        return
    end

    self:_onClickBtn(_,self._nowSeaMap and 79 or 5)
end


function MapPanel:_checkActivityState(actId)
    local info = ActivityModel.instance:getActivityInfo(actId)
    if info == nil or info:getIsOpen() == false then
        return false
    else
        if info:getIsOpen() == true then
            return true
        end
    end
    return false
end

function MapPanel:_updateSeaDouble()
    local activityInfo = ActivityModel.instance:getActivityInfo(GameEnum.ActivityEnum.ACTIVITY_432)
    local currentDoubleSeaDepth = -1
    if activityInfo and activityInfo:getIsOpen() then
        local randomConfig = Activity432Config.getRandoms(activityInfo.simpleInfo.activityId)[activityInfo:getOpenDay() + 1]
        currentDoubleSeaDepth = randomConfig.layer
    end
    for _, btn in pairs(self._btnSeaList) do
        local imgDouble = goutil.findChild(btn.go, "imgDouble")
        imgDouble:SetActive(btn.sceneId == currentDoubleSeaDepth)
    end
end

function MapPanel:_checkEnterShareStreet()
    if not FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.ShareStreet, true) then
        return
    end
    ShareStreetFacade.instance:enterMyStreetScene(handler(function(_, result)
        print("result", result)
        if result then
            self:close()
        end
    end, self))
end

return MapPanel
