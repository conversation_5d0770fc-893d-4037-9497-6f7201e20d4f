-- {excel:426滚滚机.xlsx, sheetName:export_滚滚机奖池配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_act426_reward_pool", package.seeall)

local title = {actId=1,id=2,quality=3,rewardCount=4,pattern=5}

local dataList = {
	{2019, 1, 3, 600, {1,1,1}},
	{2019, 2, 2, 300, {1,1,2}},
	{2019, 3, 2, 300, {1,1,3}},
	{2019, 4, 2, 300, {1,2,1}},
	{2019, 5, 2, 300, {1,2,2}},
	{2019, 6, 1, 200, {1,2,3}},
	{2019, 7, 2, 300, {1,3,1}},
	{2019, 8, 1, 200, {1,3,2}},
	{2019, 9, 2, 300, {1,3,3}},
	{2019, 10, 2, 300, {2,1,1}},
	{2019, 11, 2, 300, {2,1,2}},
	{2019, 12, 1, 200, {2,1,3}},
	{2019, 13, 2, 300, {2,2,1}},
	{2019, 14, 3, 600, {2,2,2}},
	{2019, 15, 2, 300, {2,2,3}},
	{2019, 16, 1, 200, {2,3,1}},
	{2019, 17, 2, 300, {2,3,3}},
	{2019, 18, 2, 300, {2,3,3}},
	{2019, 19, 2, 300, {3,1,1}},
	{2019, 20, 1, 200, {3,1,2}},
	{2019, 21, 2, 300, {3,1,3}},
	{2019, 22, 1, 200, {3,2,1}},
	{2019, 23, 2, 300, {3,2,2}},
	{2019, 24, 2, 300, {3,2,3}},
	{2019, 25, 2, 300, {3,3,1}},
	{2019, 26, 2, 300, {3,3,2}},
	{2019, 27, 3, 600, {3,3,3}},
	{2104, 1, 3, 600, {1,1,1}},
	{2104, 2, 2, 300, {1,1,2}},
	{2104, 3, 2, 300, {1,1,3}},
	{2104, 4, 2, 300, {1,2,1}},
	{2104, 5, 2, 300, {1,2,2}},
	{2104, 6, 1, 200, {1,2,3}},
	{2104, 7, 2, 300, {1,3,1}},
	{2104, 8, 1, 200, {1,3,2}},
	{2104, 9, 2, 300, {1,3,3}},
	{2104, 10, 2, 300, {2,1,1}},
	{2104, 11, 2, 300, {2,1,2}},
	{2104, 12, 1, 200, {2,1,3}},
	{2104, 13, 2, 300, {2,2,1}},
	{2104, 14, 3, 600, {2,2,2}},
	{2104, 15, 2, 300, {2,2,3}},
	{2104, 16, 1, 200, {2,3,1}},
	{2104, 17, 2, 300, {2,3,3}},
	{2104, 18, 2, 300, {2,3,3}},
	{2104, 19, 2, 300, {3,1,1}},
	{2104, 20, 1, 200, {3,1,2}},
	{2104, 21, 2, 300, {3,1,3}},
	{2104, 22, 1, 200, {3,2,1}},
	{2104, 23, 2, 300, {3,2,2}},
	{2104, 24, 2, 300, {3,2,3}},
	{2104, 25, 2, 300, {3,3,1}},
	{2104, 26, 2, 300, {3,3,2}},
	{2104, 27, 3, 600, {3,3,3}},
	{2146, 1, 3, 600, {1,1,1}},
	{2146, 2, 2, 300, {1,1,2}},
	{2146, 3, 2, 300, {1,1,3}},
	{2146, 4, 2, 300, {1,2,1}},
	{2146, 5, 2, 300, {1,2,2}},
	{2146, 6, 1, 200, {1,2,3}},
	{2146, 7, 2, 300, {1,3,1}},
	{2146, 8, 1, 200, {1,3,2}},
	{2146, 9, 2, 300, {1,3,3}},
	{2146, 10, 2, 300, {2,1,1}},
	{2146, 11, 2, 300, {2,1,2}},
	{2146, 12, 1, 200, {2,1,3}},
	{2146, 13, 2, 300, {2,2,1}},
	{2146, 14, 3, 600, {2,2,2}},
	{2146, 15, 2, 300, {2,2,3}},
	{2146, 16, 1, 200, {2,3,1}},
	{2146, 17, 2, 300, {2,3,3}},
	{2146, 18, 2, 300, {2,3,3}},
	{2146, 19, 2, 300, {3,1,1}},
	{2146, 20, 1, 200, {3,1,2}},
	{2146, 21, 2, 300, {3,1,3}},
	{2146, 22, 1, 200, {3,2,1}},
	{2146, 23, 2, 300, {3,2,2}},
	{2146, 24, 2, 300, {3,2,3}},
	{2146, 25, 2, 300, {3,3,1}},
	{2146, 26, 2, 300, {3,3,2}},
	{2146, 27, 3, 600, {3,3,3}},
	{2183, 1, 3, 600, {1,1,1}},
	{2183, 2, 2, 300, {1,1,2}},
	{2183, 3, 2, 300, {1,1,3}},
	{2183, 4, 2, 300, {1,2,1}},
	{2183, 5, 2, 300, {1,2,2}},
	{2183, 6, 1, 200, {1,2,3}},
	{2183, 7, 2, 300, {1,3,1}},
	{2183, 8, 1, 200, {1,3,2}},
	{2183, 9, 2, 300, {1,3,3}},
	{2183, 10, 2, 300, {2,1,1}},
	{2183, 11, 2, 300, {2,1,2}},
	{2183, 12, 1, 200, {2,1,3}},
	{2183, 13, 2, 300, {2,2,1}},
	{2183, 14, 3, 600, {2,2,2}},
	{2183, 15, 2, 300, {2,2,3}},
	{2183, 16, 1, 200, {2,3,1}},
	{2183, 17, 2, 300, {2,3,3}},
	{2183, 18, 2, 300, {2,3,3}},
	{2183, 19, 2, 300, {3,1,1}},
	{2183, 20, 1, 200, {3,1,2}},
	{2183, 21, 2, 300, {3,1,3}},
	{2183, 22, 1, 200, {3,2,1}},
	{2183, 23, 2, 300, {3,2,2}},
	{2183, 24, 2, 300, {3,2,3}},
	{2183, 25, 2, 300, {3,3,1}},
	{2183, 26, 2, 300, {3,3,2}},
	{2183, 27, 3, 600, {3,3,3}},
	{2186, 1, 3, 600, {1,1,1}},
	{2186, 2, 2, 300, {1,1,2}},
	{2186, 3, 2, 300, {1,1,3}},
	{2186, 4, 2, 300, {1,2,1}},
	{2186, 5, 2, 300, {1,2,2}},
	{2186, 6, 1, 200, {1,2,3}},
	{2186, 7, 2, 300, {1,3,1}},
	{2186, 8, 1, 200, {1,3,2}},
	{2186, 9, 2, 300, {1,3,3}},
	{2186, 10, 2, 300, {2,1,1}},
	{2186, 11, 2, 300, {2,1,2}},
	{2186, 12, 1, 200, {2,1,3}},
	{2186, 13, 2, 300, {2,2,1}},
	{2186, 14, 3, 600, {2,2,2}},
	{2186, 15, 2, 300, {2,2,3}},
	{2186, 16, 1, 200, {2,3,1}},
	{2186, 17, 2, 300, {2,3,3}},
	{2186, 18, 2, 300, {2,3,3}},
	{2186, 19, 2, 300, {3,1,1}},
	{2186, 20, 1, 200, {3,1,2}},
	{2186, 21, 2, 300, {3,1,3}},
	{2186, 22, 1, 200, {3,2,1}},
	{2186, 23, 2, 300, {3,2,2}},
	{2186, 24, 2, 300, {3,2,3}},
	{2186, 25, 2, 300, {3,3,1}},
	{2186, 26, 2, 300, {3,3,2}},
	{2186, 27, 3, 600, {3,3,3}},
	{2277, 1, 3, 600, {1,1,1}},
	{2277, 2, 2, 300, {1,1,2}},
	{2277, 3, 2, 300, {1,1,3}},
	{2277, 4, 2, 300, {1,2,1}},
	{2277, 5, 2, 300, {1,2,2}},
	{2277, 6, 1, 200, {1,2,3}},
	{2277, 7, 2, 300, {1,3,1}},
	{2277, 8, 1, 200, {1,3,2}},
	{2277, 9, 2, 300, {1,3,3}},
	{2277, 10, 2, 300, {2,1,1}},
	{2277, 11, 2, 300, {2,1,2}},
	{2277, 12, 1, 200, {2,1,3}},
	{2277, 13, 2, 300, {2,2,1}},
	{2277, 14, 3, 600, {2,2,2}},
	{2277, 15, 2, 300, {2,2,3}},
	{2277, 16, 1, 200, {2,3,1}},
	{2277, 17, 2, 300, {2,3,3}},
	{2277, 18, 2, 300, {2,3,3}},
	{2277, 19, 2, 300, {3,1,1}},
	{2277, 20, 1, 200, {3,1,2}},
	{2277, 21, 2, 300, {3,1,3}},
	{2277, 22, 1, 200, {3,2,1}},
	{2277, 23, 2, 300, {3,2,2}},
	{2277, 24, 2, 300, {3,2,3}},
	{2277, 25, 2, 300, {3,3,1}},
	{2277, 26, 2, 300, {3,3,2}},
	{2277, 27, 3, 600, {3,3,3}},
	{2312, 1, 3, 600, {1,1,1}},
	{2312, 2, 2, 300, {1,1,2}},
	{2312, 3, 2, 300, {1,1,3}},
	{2312, 4, 2, 300, {1,2,1}},
	{2312, 5, 2, 300, {1,2,2}},
	{2312, 6, 1, 200, {1,2,3}},
	{2312, 7, 2, 300, {1,3,1}},
	{2312, 8, 1, 200, {1,3,2}},
	{2312, 9, 2, 300, {1,3,3}},
	{2312, 10, 2, 300, {2,1,1}},
	{2312, 11, 2, 300, {2,1,2}},
	{2312, 12, 1, 200, {2,1,3}},
	{2312, 13, 2, 300, {2,2,1}},
	{2312, 14, 3, 600, {2,2,2}},
	{2312, 15, 2, 300, {2,2,3}},
	{2312, 16, 1, 200, {2,3,1}},
	{2312, 17, 2, 300, {2,3,3}},
	{2312, 18, 2, 300, {2,3,3}},
	{2312, 19, 2, 300, {3,1,1}},
	{2312, 20, 1, 200, {3,1,2}},
	{2312, 21, 2, 300, {3,1,3}},
	{2312, 22, 1, 200, {3,2,1}},
	{2312, 23, 2, 300, {3,2,2}},
	{2312, 24, 2, 300, {3,2,3}},
	{2312, 25, 2, 300, {3,3,1}},
	{2312, 26, 2, 300, {3,3,2}},
	{2312, 27, 3, 600, {3,3,3}},
	{2355, 1, 3, 600, {1,1,1}},
	{2355, 2, 2, 300, {1,1,2}},
	{2355, 3, 2, 300, {1,1,3}},
	{2355, 4, 2, 300, {1,2,1}},
	{2355, 5, 2, 300, {1,2,2}},
	{2355, 6, 1, 200, {1,2,3}},
	{2355, 7, 2, 300, {1,3,1}},
	{2355, 8, 1, 200, {1,3,2}},
	{2355, 9, 2, 300, {1,3,3}},
	{2355, 10, 2, 300, {2,1,1}},
	{2355, 11, 2, 300, {2,1,2}},
	{2355, 12, 1, 200, {2,1,3}},
	{2355, 13, 2, 300, {2,2,1}},
	{2355, 14, 3, 600, {2,2,2}},
	{2355, 15, 2, 300, {2,2,3}},
	{2355, 16, 1, 200, {2,3,1}},
	{2355, 17, 2, 300, {2,3,3}},
	{2355, 18, 2, 300, {2,3,3}},
	{2355, 19, 2, 300, {3,1,1}},
	{2355, 20, 1, 200, {3,1,2}},
	{2355, 21, 2, 300, {3,1,3}},
	{2355, 22, 1, 200, {3,2,1}},
	{2355, 23, 2, 300, {3,2,2}},
	{2355, 24, 2, 300, {3,2,3}},
	{2355, 25, 2, 300, {3,3,1}},
	{2355, 26, 2, 300, {3,3,2}},
	{2355, 27, 3, 600, {3,3,3}},
	{2496, 1, 3, 600, {1,1,1}},
	{2496, 2, 2, 300, {1,1,2}},
	{2496, 3, 2, 300, {1,1,3}},
	{2496, 4, 2, 300, {1,2,1}},
	{2496, 5, 2, 300, {1,2,2}},
	{2496, 6, 1, 200, {1,2,3}},
	{2496, 7, 2, 300, {1,3,1}},
	{2496, 8, 1, 200, {1,3,2}},
	{2496, 9, 2, 300, {1,3,3}},
	{2496, 10, 2, 300, {2,1,1}},
	{2496, 11, 2, 300, {2,1,2}},
	{2496, 12, 1, 200, {2,1,3}},
	{2496, 13, 2, 300, {2,2,1}},
	{2496, 14, 3, 600, {2,2,2}},
	{2496, 15, 2, 300, {2,2,3}},
	{2496, 16, 1, 200, {2,3,1}},
	{2496, 17, 2, 300, {2,3,3}},
	{2496, 18, 2, 300, {2,3,3}},
	{2496, 19, 2, 300, {3,1,1}},
	{2496, 20, 1, 200, {3,1,2}},
	{2496, 21, 2, 300, {3,1,3}},
	{2496, 22, 1, 200, {3,2,1}},
	{2496, 23, 2, 300, {3,2,2}},
	{2496, 24, 2, 300, {3,2,3}},
	{2496, 25, 2, 300, {3,3,1}},
	{2496, 26, 2, 300, {3,3,2}},
	{2496, 27, 3, 600, {3,3,3}},
}

local t_act426_reward_pool = {
	[2019] = {
		[1] = dataList[1],
		[2] = dataList[2],
		[3] = dataList[3],
		[4] = dataList[4],
		[5] = dataList[5],
		[6] = dataList[6],
		[7] = dataList[7],
		[8] = dataList[8],
		[9] = dataList[9],
		[10] = dataList[10],
		[11] = dataList[11],
		[12] = dataList[12],
		[13] = dataList[13],
		[14] = dataList[14],
		[15] = dataList[15],
		[16] = dataList[16],
		[17] = dataList[17],
		[18] = dataList[18],
		[19] = dataList[19],
		[20] = dataList[20],
		[21] = dataList[21],
		[22] = dataList[22],
		[23] = dataList[23],
		[24] = dataList[24],
		[25] = dataList[25],
		[26] = dataList[26],
		[27] = dataList[27],
	},
	[2104] = {
		[1] = dataList[28],
		[2] = dataList[29],
		[3] = dataList[30],
		[4] = dataList[31],
		[5] = dataList[32],
		[6] = dataList[33],
		[7] = dataList[34],
		[8] = dataList[35],
		[9] = dataList[36],
		[10] = dataList[37],
		[11] = dataList[38],
		[12] = dataList[39],
		[13] = dataList[40],
		[14] = dataList[41],
		[15] = dataList[42],
		[16] = dataList[43],
		[17] = dataList[44],
		[18] = dataList[45],
		[19] = dataList[46],
		[20] = dataList[47],
		[21] = dataList[48],
		[22] = dataList[49],
		[23] = dataList[50],
		[24] = dataList[51],
		[25] = dataList[52],
		[26] = dataList[53],
		[27] = dataList[54],
	},
	[2146] = {
		[1] = dataList[55],
		[2] = dataList[56],
		[3] = dataList[57],
		[4] = dataList[58],
		[5] = dataList[59],
		[6] = dataList[60],
		[7] = dataList[61],
		[8] = dataList[62],
		[9] = dataList[63],
		[10] = dataList[64],
		[11] = dataList[65],
		[12] = dataList[66],
		[13] = dataList[67],
		[14] = dataList[68],
		[15] = dataList[69],
		[16] = dataList[70],
		[17] = dataList[71],
		[18] = dataList[72],
		[19] = dataList[73],
		[20] = dataList[74],
		[21] = dataList[75],
		[22] = dataList[76],
		[23] = dataList[77],
		[24] = dataList[78],
		[25] = dataList[79],
		[26] = dataList[80],
		[27] = dataList[81],
	},
	[2183] = {
		[1] = dataList[82],
		[2] = dataList[83],
		[3] = dataList[84],
		[4] = dataList[85],
		[5] = dataList[86],
		[6] = dataList[87],
		[7] = dataList[88],
		[8] = dataList[89],
		[9] = dataList[90],
		[10] = dataList[91],
		[11] = dataList[92],
		[12] = dataList[93],
		[13] = dataList[94],
		[14] = dataList[95],
		[15] = dataList[96],
		[16] = dataList[97],
		[17] = dataList[98],
		[18] = dataList[99],
		[19] = dataList[100],
		[20] = dataList[101],
		[21] = dataList[102],
		[22] = dataList[103],
		[23] = dataList[104],
		[24] = dataList[105],
		[25] = dataList[106],
		[26] = dataList[107],
		[27] = dataList[108],
	},
	[2186] = {
		[1] = dataList[109],
		[2] = dataList[110],
		[3] = dataList[111],
		[4] = dataList[112],
		[5] = dataList[113],
		[6] = dataList[114],
		[7] = dataList[115],
		[8] = dataList[116],
		[9] = dataList[117],
		[10] = dataList[118],
		[11] = dataList[119],
		[12] = dataList[120],
		[13] = dataList[121],
		[14] = dataList[122],
		[15] = dataList[123],
		[16] = dataList[124],
		[17] = dataList[125],
		[18] = dataList[126],
		[19] = dataList[127],
		[20] = dataList[128],
		[21] = dataList[129],
		[22] = dataList[130],
		[23] = dataList[131],
		[24] = dataList[132],
		[25] = dataList[133],
		[26] = dataList[134],
		[27] = dataList[135],
	},
	[2277] = {
		[1] = dataList[136],
		[2] = dataList[137],
		[3] = dataList[138],
		[4] = dataList[139],
		[5] = dataList[140],
		[6] = dataList[141],
		[7] = dataList[142],
		[8] = dataList[143],
		[9] = dataList[144],
		[10] = dataList[145],
		[11] = dataList[146],
		[12] = dataList[147],
		[13] = dataList[148],
		[14] = dataList[149],
		[15] = dataList[150],
		[16] = dataList[151],
		[17] = dataList[152],
		[18] = dataList[153],
		[19] = dataList[154],
		[20] = dataList[155],
		[21] = dataList[156],
		[22] = dataList[157],
		[23] = dataList[158],
		[24] = dataList[159],
		[25] = dataList[160],
		[26] = dataList[161],
		[27] = dataList[162],
	},
	[2312] = {
		[1] = dataList[163],
		[2] = dataList[164],
		[3] = dataList[165],
		[4] = dataList[166],
		[5] = dataList[167],
		[6] = dataList[168],
		[7] = dataList[169],
		[8] = dataList[170],
		[9] = dataList[171],
		[10] = dataList[172],
		[11] = dataList[173],
		[12] = dataList[174],
		[13] = dataList[175],
		[14] = dataList[176],
		[15] = dataList[177],
		[16] = dataList[178],
		[17] = dataList[179],
		[18] = dataList[180],
		[19] = dataList[181],
		[20] = dataList[182],
		[21] = dataList[183],
		[22] = dataList[184],
		[23] = dataList[185],
		[24] = dataList[186],
		[25] = dataList[187],
		[26] = dataList[188],
		[27] = dataList[189],
	},
	[2355] = {
		[1] = dataList[190],
		[2] = dataList[191],
		[3] = dataList[192],
		[4] = dataList[193],
		[5] = dataList[194],
		[6] = dataList[195],
		[7] = dataList[196],
		[8] = dataList[197],
		[9] = dataList[198],
		[10] = dataList[199],
		[11] = dataList[200],
		[12] = dataList[201],
		[13] = dataList[202],
		[14] = dataList[203],
		[15] = dataList[204],
		[16] = dataList[205],
		[17] = dataList[206],
		[18] = dataList[207],
		[19] = dataList[208],
		[20] = dataList[209],
		[21] = dataList[210],
		[22] = dataList[211],
		[23] = dataList[212],
		[24] = dataList[213],
		[25] = dataList[214],
		[26] = dataList[215],
		[27] = dataList[216],
	},
	[2496] = {
		[1] = dataList[217],
		[2] = dataList[218],
		[3] = dataList[219],
		[4] = dataList[220],
		[5] = dataList[221],
		[6] = dataList[222],
		[7] = dataList[223],
		[8] = dataList[224],
		[9] = dataList[225],
		[10] = dataList[226],
		[11] = dataList[227],
		[12] = dataList[228],
		[13] = dataList[229],
		[14] = dataList[230],
		[15] = dataList[231],
		[16] = dataList[232],
		[17] = dataList[233],
		[18] = dataList[234],
		[19] = dataList[235],
		[20] = dataList[236],
		[21] = dataList[237],
		[22] = dataList[238],
		[23] = dataList[239],
		[24] = dataList[240],
		[25] = dataList[241],
		[26] = dataList[242],
		[27] = dataList[243],
	},
}

t_act426_reward_pool.dataList = dataList
local mt
if Act426RewardPoolDefine then
	mt = {
		__cname =  "Act426RewardPoolDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or Act426RewardPoolDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_act426_reward_pool