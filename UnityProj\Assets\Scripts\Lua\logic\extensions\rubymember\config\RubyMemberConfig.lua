module("logic.extensions.rubymember.config.RubyMemberConfig", package.seeall)

local RubyMemberConfig = class("RubyMemberConfig")

local allVIPBuffConfig = ConfigLoader.New("vip_card_buff")
local allVIPCardConfig = ConfigLoader.New("vip_card")
local allVIPListConfig = ConfigLoader.New("vip_list")

-- allVIPCardtypeConfig=ConfigLoader.New("vip_card_type")

--通用特权(1,2,3,4) type此外还应有月季年(5,6,7)礼物的类型对应
function RubyMemberConfig.getRubyRights()
	return allVIPListConfig:getConfig().dataList
end

function RubyMemberConfig.getTextByType(type)
	return allVIPListConfig:getConfig()[type].tips
end

function RubyMemberConfig.getDiscountInfo()
	local result = {}
	for i, v in ipairs(allVIPCardConfig:getConfig().dataList) do
		local info = {
			showDiscount = (v.discount ~= -1),
			DiscountPrice = v.discount
		}
		table.insert(result, info)
	end
	return result
end

function RubyMemberConfig.getDayCountInfo()
	local result = {}
	for i, v in ipairs(allVIPCardConfig:getConfig().dataList) do
		table.insert(result, v.activeDays)
	end
	return result
end

function RubyMemberConfig.getRewardPinkDiamondInfo()
	local result = {}
	for i, v in ipairs(allVIPCardConfig:getConfig().dataList) do
		table.insert(result, v.pinkDiamonds)
	end
	return result
end

--首充礼物
function RubyMemberConfig.getGiftByType(type)
	return allVIPCardConfig:getConfig().dataList[type].rewards
end

function RubyMemberConfig.getDailyGift()
	local dailyGift = {
		gifts = allVIPCardConfig:getConfig()[16030001].dailyRewards,
		BGImgUrl = allVIPListConfig:getConfig()[1].BGImgUrl,
		tips = allVIPListConfig:getConfig()[1].tips
	}
	return dailyGift
end

function RubyMemberConfig.getBuffConfig(vipType)
	local buffconfig = allVIPBuffConfig:getConfig()[vipType].buffIds
	return buffconfig
end

function RubyMemberConfig.getBuffInfo()
	--主界面拿年卡的配置
	local buffconfig = allVIPBuffConfig:getConfig()[3].buffIds
	local buffresult = {}
	for _, v in ipairs(buffconfig) do
		local buff = {
			buffId = v.id,
			content = BuffConfig.getBuffDesc(v.id, v.count),
			id = GameUrl.getBuffUrl(BuffConfig.getBuffDefineById(v.id).parent)
		}
		table.insert(buffresult, buff)
	end
	local BuffInfo = {
		buffList = buffresult,
		BGImgUrl = allVIPListConfig:getConfig()[2].BGImgUrl,
		tips = allVIPListConfig:getConfig()[2].tips
	}
	return BuffInfo
end

function RubyMemberConfig.getLevelLimit()
	local cfg = FuncUnlockConfig.getUnlockInfoCO(37)
	return cfg.condition.level
end

function RubyMemberConfig.getCardInfo()
	return allVIPCardConfig:getConfig().dataList
end

function RubyMemberConfig.getTypeById(ID)
	for i, v in ipairs(allVIPCardConfig:getConfig().dataList) do
		if v.id == ID then
			return v.type
		end
	end
end

function RubyMemberConfig.getPetExploreFreeTime(vipType)
	for i, v in ipairs(RubyMemberConfig.getBuffConfig(vipType)) do
		if v.id == GameEnum.BuffTypeEnum.PET_EXPEDITION_FREE_REFRESH_TIME_INCR then
			return v.count
		end
	end
end

return RubyMemberConfig
