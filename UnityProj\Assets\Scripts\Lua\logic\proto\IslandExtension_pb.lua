-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
local HOUSEEXTENSION_PB = require("logic.proto.HouseExtension_pb")
local BACKPACKEXTENSION_PB = require("logic.proto.BackpackExtension_pb")
local USEREXTENSION_PB = require("logic.proto.UserExtension_pb")
local TRAVELLEREXTENSION_PB = require("logic.proto.TravellerExtension_pb")
local FOODEXTENSION_PB = require("logic.proto.FoodExtension_pb")
local DRAGONCHILDEXTENSION_PB = require("logic.proto.DragonChildExtension_pb")
local ECOPARKEXTENSION_PB = require("logic.proto.EcoparkExtension_pb")
local PETEXTENSION_PB = require("logic.proto.PetExtension_pb")
module("logic.proto.IslandExtension_pb", package.seeall)


local tb = {}
tb.DECORATEISLANDTYPE_ENUM = protobuf.EnumDescriptor()
tb.DECORATEISLANDTYPE_FURNITURE_ENUMITEM = protobuf.EnumValueDescriptor()
tb.DECORATEISLANDTYPE_TERRAIN_ENUMITEM = protobuf.EnumValueDescriptor()
tb.DECORATEISLANDTYPE_ISLAND_OUTSIDE_ENUMITEM = protobuf.EnumValueDescriptor()
tb.DECORATEISLANDTYPE_WEATHER_ENUMITEM = protobuf.EnumValueDescriptor()
tb.DECORATEISLANDTYPE_STREET_PERSON_ENUMITEM = protobuf.EnumValueDescriptor()
tb.DECORATEISLANDTYPE_STREET_PUBLIC_ENUMITEM = protobuf.EnumValueDescriptor()
ISLANDWORKSHOPNO_MSG = protobuf.Descriptor()
tb.ISLANDWORKSHOPNO_ID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDWORKSHOPNO_LEVEL_FIELD = protobuf.FieldDescriptor()
MANORSPEEDUPCROPREQUEST_MSG = protobuf.Descriptor()
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD = protobuf.FieldDescriptor()
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
FRIENDTREEGAINREQUEST_MSG = protobuf.Descriptor()
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
FRIENDTREEFOSTERREQUEST_MSG = protobuf.Descriptor()
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG = protobuf.Descriptor()
GETALLISLANDDIARYREQUEST_MSG = protobuf.Descriptor()
RUBBISHNO_MSG = protobuf.Descriptor()
tb.RUBBISHNO_ID_FIELD = protobuf.FieldDescriptor()
tb.RUBBISHNO_TYPE_FIELD = protobuf.FieldDescriptor()
ISLANDFURNITURECHANGECLOTHESREPLY_MSG = protobuf.Descriptor()
MANORUNLOCKSECTORREQUEST_MSG = protobuf.Descriptor()
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD = protobuf.FieldDescriptor()
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD = protobuf.FieldDescriptor()
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD = protobuf.FieldDescriptor()
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG = protobuf.Descriptor()
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD = protobuf.FieldDescriptor()
MANORPLANTREQUEST_MSG = protobuf.Descriptor()
tb.MANORPLANTREQUEST_PLANTS_FIELD = protobuf.FieldDescriptor()
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG = protobuf.Descriptor()
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD = protobuf.FieldDescriptor()
CATCHBUTTERFLYREQUEST_MSG = protobuf.Descriptor()
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD = protobuf.FieldDescriptor()
MANORMOVEANIMALREPLY_MSG = protobuf.Descriptor()
OPENBUTTERFLYVIEWREQUEST_MSG = protobuf.Descriptor()
MANORGATHERCROPREPLY_MSG = protobuf.Descriptor()
tb.MANORGATHERCROPREPLY_CROPS_FIELD = protobuf.FieldDescriptor()
tb.MANORGATHERCROPREPLY_GAINS_FIELD = protobuf.FieldDescriptor()
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD = protobuf.FieldDescriptor()
MANORCOMPLETEUNLOCKSECTORREPLY_MSG = protobuf.Descriptor()
FURNITUREINFOCHANGEPUSH_MSG = protobuf.Descriptor()
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD = protobuf.FieldDescriptor()
ISLANDFURNITURESWITCHMARKREQUEST_MSG = protobuf.Descriptor()
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG = protobuf.Descriptor()
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD = protobuf.FieldDescriptor()
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD = protobuf.FieldDescriptor()
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD = protobuf.FieldDescriptor()
MANORCLEANRUBBISHREQUEST_MSG = protobuf.Descriptor()
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD = protobuf.FieldDescriptor()
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG = protobuf.Descriptor()
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD = protobuf.FieldDescriptor()
FRIENDTREEGAINREPLY_MSG = protobuf.Descriptor()
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
MANORMOVEANIMALREQUEST_MSG = protobuf.Descriptor()
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD = protobuf.FieldDescriptor()
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
MANORCROPNO_MSG = protobuf.Descriptor()
tb.MANORCROPNO_ID_FIELD = protobuf.FieldDescriptor()
tb.MANORCROPNO_CROPTYPE_FIELD = protobuf.FieldDescriptor()
tb.MANORCROPNO_END_FIELD = protobuf.FieldDescriptor()
tb.MANORCROPNO_FIXED_FIELD = protobuf.FieldDescriptor()
tb.MANORCROPNO_RANDOM_FIELD = protobuf.FieldDescriptor()
tb.MANORCROPNO_BUFFDEFINEID_FIELD = protobuf.FieldDescriptor()
tb.MANORCROPNO_FINALSTATE_FIELD = protobuf.FieldDescriptor()
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD = protobuf.FieldDescriptor()
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG = protobuf.Descriptor()
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD = protobuf.FieldDescriptor()
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
MANORGETRUBBISHINFOREQUEST_MSG = protobuf.Descriptor()
GETISLANDINFOREQUEST_MSG = protobuf.Descriptor()
tb.GETISLANDINFOREQUEST_USERID_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREQUEST_NPC_FIELD = protobuf.FieldDescriptor()
CATCHBUTTERFLYREPLY_MSG = protobuf.Descriptor()
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
MANORCLEANRUBBISHREPLY_MSG = protobuf.Descriptor()
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD = protobuf.FieldDescriptor()
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG = protobuf.Descriptor()
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG = protobuf.Descriptor()
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD = protobuf.FieldDescriptor()
TERRAININFONO_MSG = protobuf.Descriptor()
tb.TERRAININFONO_ID_FIELD = protobuf.FieldDescriptor()
tb.TERRAININFONO_FURNITUREID_FIELD = protobuf.FieldDescriptor()
tb.TERRAININFONO_POSX_FIELD = protobuf.FieldDescriptor()
tb.TERRAININFONO_POSY_FIELD = protobuf.FieldDescriptor()
tb.TERRAININFONO_DELETE_FIELD = protobuf.FieldDescriptor()
tb.TERRAININFONO_LENGTH_FIELD = protobuf.FieldDescriptor()
tb.TERRAININFONO_WIDTH_FIELD = protobuf.FieldDescriptor()
ISLANDFURNITURESWITCHMARKREPLY_MSG = protobuf.Descriptor()
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD = protobuf.FieldDescriptor()
MANORGATHERCROPREQUEST_MSG = protobuf.Descriptor()
tb.MANORGATHERCROPREQUEST_IDS_FIELD = protobuf.FieldDescriptor()
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD = protobuf.FieldDescriptor()
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
CROPITEMCHANGEPUSH_MSG = protobuf.Descriptor()
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD = protobuf.FieldDescriptor()
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
OPENBUTTERFLYVIEWREPLY_MSG = protobuf.Descriptor()
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD = protobuf.FieldDescriptor()
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD = protobuf.FieldDescriptor()
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD = protobuf.FieldDescriptor()
MANORPLANTREPLY_MSG = protobuf.Descriptor()
tb.MANORPLANTREPLY_PLANTS_FIELD = protobuf.FieldDescriptor()
MANORGETRUBBISHINFOREPLY_MSG = protobuf.Descriptor()
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD = protobuf.FieldDescriptor()
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD = protobuf.FieldDescriptor()
DECORATEISLANDREQUEST_MSG = protobuf.Descriptor()
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD = protobuf.FieldDescriptor()
FRIENDTREENO_MSG = protobuf.Descriptor()
tb.FRIENDTREENO_TID_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREENO_DEFINEID_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREENO_EXP_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREENO_TODAYEXP_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREENO_LEVEL_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD = protobuf.FieldDescriptor()
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD = protobuf.FieldDescriptor()
MANORUNLOCKSECTORREPLY_MSG = protobuf.Descriptor()
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD = protobuf.FieldDescriptor()
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG = protobuf.Descriptor()
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
TAKEBUTTERFLYBOXHEARTREPLY_MSG = protobuf.Descriptor()
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
GETISLANDINFOREPLY_MSG = protobuf.Descriptor()
tb.GETISLANDINFOREPLY_SECTORS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_CROPS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_COMFORT_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_BUILDING_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_PICTURE_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_TERRAINS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD = protobuf.FieldDescriptor()
tb.GETISLANDINFOREPLY_STREETID_FIELD = protobuf.FieldDescriptor()
MANORSPEEDUPCROPREPLY_MSG = protobuf.Descriptor()
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD = protobuf.FieldDescriptor()
GETALLISLANDDIARYREPLY_MSG = protobuf.Descriptor()
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD = protobuf.FieldDescriptor()
ISLANDDIARYINFONO_MSG = protobuf.Descriptor()
tb.ISLANDDIARYINFONO_ID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDDIARYINFONO_IMAGEID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDDIARYINFONO_TIME_FIELD = protobuf.FieldDescriptor()
ISLANDWEATHERNO_MSG = protobuf.Descriptor()
tb.ISLANDWEATHERNO_ID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD = protobuf.FieldDescriptor()
tb.ISLANDWEATHERNO_USING_FIELD = protobuf.FieldDescriptor()
UNLOCKINFONO_MSG = protobuf.Descriptor()
tb.UNLOCKINFONO_ID_FIELD = protobuf.FieldDescriptor()
tb.UNLOCKINFONO_UNLOCKTIME_FIELD = protobuf.FieldDescriptor()
ISLANDFURNITURETEMPLATEINFONO_MSG = protobuf.Descriptor()
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD = protobuf.FieldDescriptor()
GATHERGAINNO_MSG = protobuf.Descriptor()
tb.GATHERGAINNO_ID_FIELD = protobuf.FieldDescriptor()
tb.GATHERGAINNO_NUM_FIELD = protobuf.FieldDescriptor()
tb.GATHERGAINNO_CROPID_FIELD = protobuf.FieldDescriptor()
DECORATEISLANDREPLY_MSG = protobuf.Descriptor()
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREPLY_CROPS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREPLY_TERRAINS_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD = protobuf.FieldDescriptor()
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD = protobuf.FieldDescriptor()
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG = protobuf.Descriptor()
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD = protobuf.FieldDescriptor()
FRIENDTREEFOSTERREPLY_MSG = protobuf.Descriptor()
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG = protobuf.Descriptor()
ISLANDDECORATEPILLARREQUEST_MSG = protobuf.Descriptor()
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD = protobuf.FieldDescriptor()
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD = protobuf.FieldDescriptor()
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD = protobuf.FieldDescriptor()
TAKEBUTTERFLYBOXHEARTREQUEST_MSG = protobuf.Descriptor()
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG = protobuf.Descriptor()
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD = protobuf.FieldDescriptor()
ISLANDDECORATEPILLARREPLY_MSG = protobuf.Descriptor()

tb.DECORATEISLANDTYPE_FURNITURE_ENUMITEM.name = "FURNITURE"
tb.DECORATEISLANDTYPE_FURNITURE_ENUMITEM.index = 0
tb.DECORATEISLANDTYPE_FURNITURE_ENUMITEM.number = 1
tb.DECORATEISLANDTYPE_TERRAIN_ENUMITEM.name = "TERRAIN"
tb.DECORATEISLANDTYPE_TERRAIN_ENUMITEM.index = 1
tb.DECORATEISLANDTYPE_TERRAIN_ENUMITEM.number = 2
tb.DECORATEISLANDTYPE_ISLAND_OUTSIDE_ENUMITEM.name = "ISLAND_OUTSIDE"
tb.DECORATEISLANDTYPE_ISLAND_OUTSIDE_ENUMITEM.index = 2
tb.DECORATEISLANDTYPE_ISLAND_OUTSIDE_ENUMITEM.number = 4
tb.DECORATEISLANDTYPE_WEATHER_ENUMITEM.name = "WEATHER"
tb.DECORATEISLANDTYPE_WEATHER_ENUMITEM.index = 3
tb.DECORATEISLANDTYPE_WEATHER_ENUMITEM.number = 8
tb.DECORATEISLANDTYPE_STREET_PERSON_ENUMITEM.name = "STREET_PERSON"
tb.DECORATEISLANDTYPE_STREET_PERSON_ENUMITEM.index = 4
tb.DECORATEISLANDTYPE_STREET_PERSON_ENUMITEM.number = 16
tb.DECORATEISLANDTYPE_STREET_PUBLIC_ENUMITEM.name = "STREET_PUBLIC"
tb.DECORATEISLANDTYPE_STREET_PUBLIC_ENUMITEM.index = 5
tb.DECORATEISLANDTYPE_STREET_PUBLIC_ENUMITEM.number = 32
tb.DECORATEISLANDTYPE_ENUM.name = "DecorateIslandType"
tb.DECORATEISLANDTYPE_ENUM.full_name = ".DecorateIslandType"
tb.DECORATEISLANDTYPE_ENUM.values = {tb.DECORATEISLANDTYPE_FURNITURE_ENUMITEM,tb.DECORATEISLANDTYPE_TERRAIN_ENUMITEM,tb.DECORATEISLANDTYPE_ISLAND_OUTSIDE_ENUMITEM,tb.DECORATEISLANDTYPE_WEATHER_ENUMITEM,tb.DECORATEISLANDTYPE_STREET_PERSON_ENUMITEM,tb.DECORATEISLANDTYPE_STREET_PUBLIC_ENUMITEM}
tb.ISLANDWORKSHOPNO_ID_FIELD.name = "id"
tb.ISLANDWORKSHOPNO_ID_FIELD.full_name = ".IslandWorkShopNO.id"
tb.ISLANDWORKSHOPNO_ID_FIELD.number = 1
tb.ISLANDWORKSHOPNO_ID_FIELD.index = 0
tb.ISLANDWORKSHOPNO_ID_FIELD.label = 1
tb.ISLANDWORKSHOPNO_ID_FIELD.has_default_value = false
tb.ISLANDWORKSHOPNO_ID_FIELD.default_value = 0
tb.ISLANDWORKSHOPNO_ID_FIELD.type = 5
tb.ISLANDWORKSHOPNO_ID_FIELD.cpp_type = 1

tb.ISLANDWORKSHOPNO_LEVEL_FIELD.name = "level"
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.full_name = ".IslandWorkShopNO.level"
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.number = 2
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.index = 1
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.label = 1
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.has_default_value = false
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.default_value = 0
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.type = 5
tb.ISLANDWORKSHOPNO_LEVEL_FIELD.cpp_type = 1

ISLANDWORKSHOPNO_MSG.name = "IslandWorkShopNO"
ISLANDWORKSHOPNO_MSG.full_name = ".IslandWorkShopNO"
ISLANDWORKSHOPNO_MSG.filename = "IslandExtension"
ISLANDWORKSHOPNO_MSG.nested_types = {}
ISLANDWORKSHOPNO_MSG.enum_types = {}
ISLANDWORKSHOPNO_MSG.fields = {tb.ISLANDWORKSHOPNO_ID_FIELD, tb.ISLANDWORKSHOPNO_LEVEL_FIELD}
ISLANDWORKSHOPNO_MSG.is_extendable = false
ISLANDWORKSHOPNO_MSG.extensions = {}
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.name = "id"
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.full_name = ".ManorSpeedUpCropRequest.id"
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.number = 1
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.index = 0
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.label = 1
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.has_default_value = false
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.default_value = 0
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.type = 5
tb.MANORSPEEDUPCROPREQUEST_ID_FIELD.cpp_type = 1

tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.full_name = ".ManorSpeedUpCropRequest.decorationSceneId"
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.number = 2
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.index = 1
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

MANORSPEEDUPCROPREQUEST_MSG.name = "ManorSpeedUpCropRequest"
MANORSPEEDUPCROPREQUEST_MSG.full_name = ".ManorSpeedUpCropRequest"
MANORSPEEDUPCROPREQUEST_MSG.filename = "IslandExtension"
MANORSPEEDUPCROPREQUEST_MSG.nested_types = {}
MANORSPEEDUPCROPREQUEST_MSG.enum_types = {}
MANORSPEEDUPCROPREQUEST_MSG.fields = {tb.MANORSPEEDUPCROPREQUEST_ID_FIELD, tb.MANORSPEEDUPCROPREQUEST_DECORATIONSCENEID_FIELD}
MANORSPEEDUPCROPREQUEST_MSG.is_extendable = false
MANORSPEEDUPCROPREQUEST_MSG.extensions = {}
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.name = "treeUids"
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.full_name = ".FriendTreeGainRequest.treeUids"
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.number = 1
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.index = 0
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.label = 3
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.has_default_value = false
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.default_value = {}
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.type = 5
tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD.cpp_type = 1

tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.full_name = ".FriendTreeGainRequest.decorationSceneId"
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.number = 2
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.index = 1
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

FRIENDTREEGAINREQUEST_MSG.name = "FriendTreeGainRequest"
FRIENDTREEGAINREQUEST_MSG.full_name = ".FriendTreeGainRequest"
FRIENDTREEGAINREQUEST_MSG.filename = "IslandExtension"
FRIENDTREEGAINREQUEST_MSG.nested_types = {}
FRIENDTREEGAINREQUEST_MSG.enum_types = {}
FRIENDTREEGAINREQUEST_MSG.fields = {tb.FRIENDTREEGAINREQUEST_TREEUIDS_FIELD, tb.FRIENDTREEGAINREQUEST_DECORATIONSCENEID_FIELD}
FRIENDTREEGAINREQUEST_MSG.is_extendable = false
FRIENDTREEGAINREQUEST_MSG.extensions = {}
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.name = "uid"
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.full_name = ".FriendTreeFosterRequest.uid"
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.number = 1
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.index = 0
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.label = 1
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.has_default_value = false
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.default_value = ""
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.type = 9
tb.FRIENDTREEFOSTERREQUEST_UID_FIELD.cpp_type = 9

tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.name = "treeUids"
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.full_name = ".FriendTreeFosterRequest.treeUids"
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.number = 2
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.index = 1
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.label = 3
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.has_default_value = false
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.default_value = {}
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.type = 5
tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD.cpp_type = 1

tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.name = "fosterId"
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.full_name = ".FriendTreeFosterRequest.fosterId"
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.number = 3
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.index = 2
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.label = 2
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.has_default_value = false
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.default_value = 0
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.type = 5
tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD.cpp_type = 1

tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.full_name = ".FriendTreeFosterRequest.decorationSceneId"
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.number = 4
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.index = 3
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

FRIENDTREEFOSTERREQUEST_MSG.name = "FriendTreeFosterRequest"
FRIENDTREEFOSTERREQUEST_MSG.full_name = ".FriendTreeFosterRequest"
FRIENDTREEFOSTERREQUEST_MSG.filename = "IslandExtension"
FRIENDTREEFOSTERREQUEST_MSG.nested_types = {}
FRIENDTREEFOSTERREQUEST_MSG.enum_types = {}
FRIENDTREEFOSTERREQUEST_MSG.fields = {tb.FRIENDTREEFOSTERREQUEST_UID_FIELD, tb.FRIENDTREEFOSTERREQUEST_TREEUIDS_FIELD, tb.FRIENDTREEFOSTERREQUEST_FOSTERID_FIELD, tb.FRIENDTREEFOSTERREQUEST_DECORATIONSCENEID_FIELD}
FRIENDTREEFOSTERREQUEST_MSG.is_extendable = false
FRIENDTREEFOSTERREQUEST_MSG.extensions = {}
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.name = "IslandModifyFurnitureTemplateNameReply"
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.full_name = ".IslandModifyFurnitureTemplateNameReply"
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.filename = "IslandExtension"
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.nested_types = {}
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.enum_types = {}
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.fields = {}
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.is_extendable = false
ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG.extensions = {}
GETALLISLANDDIARYREQUEST_MSG.name = "GetAllIslandDiaryRequest"
GETALLISLANDDIARYREQUEST_MSG.full_name = ".GetAllIslandDiaryRequest"
GETALLISLANDDIARYREQUEST_MSG.filename = "IslandExtension"
GETALLISLANDDIARYREQUEST_MSG.nested_types = {}
GETALLISLANDDIARYREQUEST_MSG.enum_types = {}
GETALLISLANDDIARYREQUEST_MSG.fields = {}
GETALLISLANDDIARYREQUEST_MSG.is_extendable = false
GETALLISLANDDIARYREQUEST_MSG.extensions = {}
tb.RUBBISHNO_ID_FIELD.name = "id"
tb.RUBBISHNO_ID_FIELD.full_name = ".RubbishNO.id"
tb.RUBBISHNO_ID_FIELD.number = 1
tb.RUBBISHNO_ID_FIELD.index = 0
tb.RUBBISHNO_ID_FIELD.label = 1
tb.RUBBISHNO_ID_FIELD.has_default_value = false
tb.RUBBISHNO_ID_FIELD.default_value = 0
tb.RUBBISHNO_ID_FIELD.type = 5
tb.RUBBISHNO_ID_FIELD.cpp_type = 1

tb.RUBBISHNO_TYPE_FIELD.name = "type"
tb.RUBBISHNO_TYPE_FIELD.full_name = ".RubbishNO.type"
tb.RUBBISHNO_TYPE_FIELD.number = 2
tb.RUBBISHNO_TYPE_FIELD.index = 1
tb.RUBBISHNO_TYPE_FIELD.label = 1
tb.RUBBISHNO_TYPE_FIELD.has_default_value = false
tb.RUBBISHNO_TYPE_FIELD.default_value = 0
tb.RUBBISHNO_TYPE_FIELD.type = 5
tb.RUBBISHNO_TYPE_FIELD.cpp_type = 1

RUBBISHNO_MSG.name = "RubbishNO"
RUBBISHNO_MSG.full_name = ".RubbishNO"
RUBBISHNO_MSG.filename = "IslandExtension"
RUBBISHNO_MSG.nested_types = {}
RUBBISHNO_MSG.enum_types = {}
RUBBISHNO_MSG.fields = {tb.RUBBISHNO_ID_FIELD, tb.RUBBISHNO_TYPE_FIELD}
RUBBISHNO_MSG.is_extendable = false
RUBBISHNO_MSG.extensions = {}
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.name = "IsLandFurnitureChangeClothesReply"
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.full_name = ".IsLandFurnitureChangeClothesReply"
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.filename = "IslandExtension"
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.nested_types = {}
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.enum_types = {}
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.fields = {}
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.is_extendable = false
ISLANDFURNITURECHANGECLOTHESREPLY_MSG.extensions = {}
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.name = "id"
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.full_name = ".ManorUnlockSectorRequest.id"
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.number = 1
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.index = 0
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.label = 1
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.has_default_value = false
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.default_value = 0
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.type = 5
tb.MANORUNLOCKSECTORREQUEST_ID_FIELD.cpp_type = 1

tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.name = "useDiamond"
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.full_name = ".ManorUnlockSectorRequest.useDiamond"
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.number = 2
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.index = 1
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.label = 1
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.has_default_value = false
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.default_value = false
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.type = 8
tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.cpp_type = 7

tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.name = "isBuilding"
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.full_name = ".ManorUnlockSectorRequest.isBuilding"
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.number = 3
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.index = 2
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.label = 1
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.has_default_value = false
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.default_value = false
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.type = 8
tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD.cpp_type = 7

MANORUNLOCKSECTORREQUEST_MSG.name = "ManorUnlockSectorRequest"
MANORUNLOCKSECTORREQUEST_MSG.full_name = ".ManorUnlockSectorRequest"
MANORUNLOCKSECTORREQUEST_MSG.filename = "IslandExtension"
MANORUNLOCKSECTORREQUEST_MSG.nested_types = {}
MANORUNLOCKSECTORREQUEST_MSG.enum_types = {}
MANORUNLOCKSECTORREQUEST_MSG.fields = {tb.MANORUNLOCKSECTORREQUEST_ID_FIELD, tb.MANORUNLOCKSECTORREQUEST_USEDIAMOND_FIELD, tb.MANORUNLOCKSECTORREQUEST_ISBUILDING_FIELD}
MANORUNLOCKSECTORREQUEST_MSG.is_extendable = false
MANORUNLOCKSECTORREQUEST_MSG.extensions = {}
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.name = "templateId"
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.full_name = ".IslandModifyFurnitureTemplateNameRequest.templateId"
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.number = 1
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.index = 0
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.label = 1
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.has_default_value = false
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.default_value = 0
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.type = 5
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD.cpp_type = 1

tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.name = "name"
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.full_name = ".IslandModifyFurnitureTemplateNameRequest.name"
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.number = 2
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.index = 1
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.label = 1
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.has_default_value = false
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.default_value = ""
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.type = 9
tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD.cpp_type = 9

ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.name = "IslandModifyFurnitureTemplateNameRequest"
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.full_name = ".IslandModifyFurnitureTemplateNameRequest"
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.filename = "IslandExtension"
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.nested_types = {}
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.enum_types = {}
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.fields = {tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_TEMPLATEID_FIELD, tb.ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_NAME_FIELD}
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.is_extendable = false
ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG.extensions = {}
tb.MANORPLANTREQUEST_PLANTS_FIELD.name = "plants"
tb.MANORPLANTREQUEST_PLANTS_FIELD.full_name = ".ManorPlantRequest.plants"
tb.MANORPLANTREQUEST_PLANTS_FIELD.number = 1
tb.MANORPLANTREQUEST_PLANTS_FIELD.index = 0
tb.MANORPLANTREQUEST_PLANTS_FIELD.label = 3
tb.MANORPLANTREQUEST_PLANTS_FIELD.has_default_value = false
tb.MANORPLANTREQUEST_PLANTS_FIELD.default_value = {}
tb.MANORPLANTREQUEST_PLANTS_FIELD.message_type = MANORCROPNO_MSG
tb.MANORPLANTREQUEST_PLANTS_FIELD.type = 11
tb.MANORPLANTREQUEST_PLANTS_FIELD.cpp_type = 10

tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.full_name = ".ManorPlantRequest.decorationSceneId"
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.number = 2
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.index = 1
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

MANORPLANTREQUEST_MSG.name = "ManorPlantRequest"
MANORPLANTREQUEST_MSG.full_name = ".ManorPlantRequest"
MANORPLANTREQUEST_MSG.filename = "IslandExtension"
MANORPLANTREQUEST_MSG.nested_types = {}
MANORPLANTREQUEST_MSG.enum_types = {}
MANORPLANTREQUEST_MSG.fields = {tb.MANORPLANTREQUEST_PLANTS_FIELD, tb.MANORPLANTREQUEST_DECORATIONSCENEID_FIELD}
MANORPLANTREQUEST_MSG.is_extendable = false
MANORPLANTREQUEST_MSG.extensions = {}
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.name = "furnitures"
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.full_name = ".IslandGetAllFurnitureTemplateReply.furnitures"
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.number = 1
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.index = 0
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.label = 3
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.has_default_value = false
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.default_value = {}
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.message_type = ISLANDFURNITURETEMPLATEINFONO_MSG
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.type = 11
tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD.cpp_type = 10

ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.name = "IslandGetAllFurnitureTemplateReply"
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.full_name = ".IslandGetAllFurnitureTemplateReply"
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.filename = "IslandExtension"
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.nested_types = {}
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.enum_types = {}
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.fields = {tb.ISLANDGETALLFURNITURETEMPLATEREPLY_FURNITURES_FIELD}
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.is_extendable = false
ISLANDGETALLFURNITURETEMPLATEREPLY_MSG.extensions = {}
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.full_name = ".CatchButterflyRequest.decorationSceneId"
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.number = 1
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.index = 0
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.name = "targetUserId"
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.full_name = ".CatchButterflyRequest.targetUserId"
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.number = 2
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.index = 1
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.label = 1
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.has_default_value = false
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.default_value = ""
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.type = 9
tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD.cpp_type = 9

CATCHBUTTERFLYREQUEST_MSG.name = "CatchButterflyRequest"
CATCHBUTTERFLYREQUEST_MSG.full_name = ".CatchButterflyRequest"
CATCHBUTTERFLYREQUEST_MSG.filename = "IslandExtension"
CATCHBUTTERFLYREQUEST_MSG.nested_types = {}
CATCHBUTTERFLYREQUEST_MSG.enum_types = {}
CATCHBUTTERFLYREQUEST_MSG.fields = {tb.CATCHBUTTERFLYREQUEST_DECORATIONSCENEID_FIELD, tb.CATCHBUTTERFLYREQUEST_TARGETUSERID_FIELD}
CATCHBUTTERFLYREQUEST_MSG.is_extendable = false
CATCHBUTTERFLYREQUEST_MSG.extensions = {}
MANORMOVEANIMALREPLY_MSG.name = "ManorMoveAnimalReply"
MANORMOVEANIMALREPLY_MSG.full_name = ".ManorMoveAnimalReply"
MANORMOVEANIMALREPLY_MSG.filename = "IslandExtension"
MANORMOVEANIMALREPLY_MSG.nested_types = {}
MANORMOVEANIMALREPLY_MSG.enum_types = {}
MANORMOVEANIMALREPLY_MSG.fields = {}
MANORMOVEANIMALREPLY_MSG.is_extendable = false
MANORMOVEANIMALREPLY_MSG.extensions = {}
OPENBUTTERFLYVIEWREQUEST_MSG.name = "OpenButterflyViewRequest"
OPENBUTTERFLYVIEWREQUEST_MSG.full_name = ".OpenButterflyViewRequest"
OPENBUTTERFLYVIEWREQUEST_MSG.filename = "IslandExtension"
OPENBUTTERFLYVIEWREQUEST_MSG.nested_types = {}
OPENBUTTERFLYVIEWREQUEST_MSG.enum_types = {}
OPENBUTTERFLYVIEWREQUEST_MSG.fields = {}
OPENBUTTERFLYVIEWREQUEST_MSG.is_extendable = false
OPENBUTTERFLYVIEWREQUEST_MSG.extensions = {}
tb.MANORGATHERCROPREPLY_CROPS_FIELD.name = "crops"
tb.MANORGATHERCROPREPLY_CROPS_FIELD.full_name = ".ManorGatherCropReply.crops"
tb.MANORGATHERCROPREPLY_CROPS_FIELD.number = 1
tb.MANORGATHERCROPREPLY_CROPS_FIELD.index = 0
tb.MANORGATHERCROPREPLY_CROPS_FIELD.label = 3
tb.MANORGATHERCROPREPLY_CROPS_FIELD.has_default_value = false
tb.MANORGATHERCROPREPLY_CROPS_FIELD.default_value = {}
tb.MANORGATHERCROPREPLY_CROPS_FIELD.message_type = MANORCROPNO_MSG
tb.MANORGATHERCROPREPLY_CROPS_FIELD.type = 11
tb.MANORGATHERCROPREPLY_CROPS_FIELD.cpp_type = 10

tb.MANORGATHERCROPREPLY_GAINS_FIELD.name = "gains"
tb.MANORGATHERCROPREPLY_GAINS_FIELD.full_name = ".ManorGatherCropReply.gains"
tb.MANORGATHERCROPREPLY_GAINS_FIELD.number = 2
tb.MANORGATHERCROPREPLY_GAINS_FIELD.index = 1
tb.MANORGATHERCROPREPLY_GAINS_FIELD.label = 3
tb.MANORGATHERCROPREPLY_GAINS_FIELD.has_default_value = false
tb.MANORGATHERCROPREPLY_GAINS_FIELD.default_value = {}
tb.MANORGATHERCROPREPLY_GAINS_FIELD.message_type = GATHERGAINNO_MSG
tb.MANORGATHERCROPREPLY_GAINS_FIELD.type = 11
tb.MANORGATHERCROPREPLY_GAINS_FIELD.cpp_type = 10

tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.name = "uprootIds"
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.full_name = ".ManorGatherCropReply.uprootIds"
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.number = 3
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.index = 2
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.label = 3
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.has_default_value = false
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.default_value = {}
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.type = 5
tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD.cpp_type = 1

MANORGATHERCROPREPLY_MSG.name = "ManorGatherCropReply"
MANORGATHERCROPREPLY_MSG.full_name = ".ManorGatherCropReply"
MANORGATHERCROPREPLY_MSG.filename = "IslandExtension"
MANORGATHERCROPREPLY_MSG.nested_types = {}
MANORGATHERCROPREPLY_MSG.enum_types = {}
MANORGATHERCROPREPLY_MSG.fields = {tb.MANORGATHERCROPREPLY_CROPS_FIELD, tb.MANORGATHERCROPREPLY_GAINS_FIELD, tb.MANORGATHERCROPREPLY_UPROOTIDS_FIELD}
MANORGATHERCROPREPLY_MSG.is_extendable = false
MANORGATHERCROPREPLY_MSG.extensions = {}
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.name = "ManorCompleteUnlockSectorReply"
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.full_name = ".ManorCompleteUnlockSectorReply"
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.filename = "IslandExtension"
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.nested_types = {}
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.enum_types = {}
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.fields = {}
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.is_extendable = false
MANORCOMPLETEUNLOCKSECTORREPLY_MSG.extensions = {}
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.name = "changeItems"
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.full_name = ".FurnitureInfoChangePush.changeItems"
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.number = 1
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.index = 0
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.label = 3
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.has_default_value = false
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.default_value = {}
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.type = 11
tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD.cpp_type = 10

tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.name = "remove"
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.full_name = ".FurnitureInfoChangePush.remove"
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.number = 2
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.index = 1
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.label = 3
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.has_default_value = false
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.default_value = {}
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.type = 5
tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD.cpp_type = 1

tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.name = "houseInfo"
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.full_name = ".FurnitureInfoChangePush.houseInfo"
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.number = 3
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.index = 2
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.label = 3
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.has_default_value = false
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.default_value = {}
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.message_type = HOUSEEXTENSION_PB.HOUSESHOWAREAINFO_MSG
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.type = 11
tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD.cpp_type = 10

tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.name = "islandType"
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.full_name = ".FurnitureInfoChangePush.islandType"
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.number = 4
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.index = 3
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.label = 1
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.has_default_value = false
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.default_value = 0
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.type = 5
tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD.cpp_type = 1

FURNITUREINFOCHANGEPUSH_MSG.name = "FurnitureInfoChangePush"
FURNITUREINFOCHANGEPUSH_MSG.full_name = ".FurnitureInfoChangePush"
FURNITUREINFOCHANGEPUSH_MSG.filename = "IslandExtension"
FURNITUREINFOCHANGEPUSH_MSG.nested_types = {}
FURNITUREINFOCHANGEPUSH_MSG.enum_types = {}
FURNITUREINFOCHANGEPUSH_MSG.fields = {tb.FURNITUREINFOCHANGEPUSH_CHANGEITEMS_FIELD, tb.FURNITUREINFOCHANGEPUSH_REMOVE_FIELD, tb.FURNITUREINFOCHANGEPUSH_HOUSEINFO_FIELD, tb.FURNITUREINFOCHANGEPUSH_ISLANDTYPE_FIELD}
FURNITUREINFOCHANGEPUSH_MSG.is_extendable = false
FURNITUREINFOCHANGEPUSH_MSG.extensions = {}
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.name = "furnitureUniqueId"
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.full_name = ".IslandFurnitureSwitchMarkRequest.furnitureUniqueId"
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.number = 1
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.index = 0
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.label = 1
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.has_default_value = false
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.default_value = 0
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.type = 5
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD.cpp_type = 1

tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.name = "furnitureMark"
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.full_name = ".IslandFurnitureSwitchMarkRequest.furnitureMark"
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.number = 2
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.index = 1
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.label = 1
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.has_default_value = false
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.default_value = 0
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.type = 5
tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD.cpp_type = 1

tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.full_name = ".IslandFurnitureSwitchMarkRequest.decorationSceneId"
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.number = 3
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.index = 2
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

ISLANDFURNITURESWITCHMARKREQUEST_MSG.name = "IslandFurnitureSwitchMarkRequest"
ISLANDFURNITURESWITCHMARKREQUEST_MSG.full_name = ".IslandFurnitureSwitchMarkRequest"
ISLANDFURNITURESWITCHMARKREQUEST_MSG.filename = "IslandExtension"
ISLANDFURNITURESWITCHMARKREQUEST_MSG.nested_types = {}
ISLANDFURNITURESWITCHMARKREQUEST_MSG.enum_types = {}
ISLANDFURNITURESWITCHMARKREQUEST_MSG.fields = {tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREUNIQUEID_FIELD, tb.ISLANDFURNITURESWITCHMARKREQUEST_FURNITUREMARK_FIELD, tb.ISLANDFURNITURESWITCHMARKREQUEST_DECORATIONSCENEID_FIELD}
ISLANDFURNITURESWITCHMARKREQUEST_MSG.is_extendable = false
ISLANDFURNITURESWITCHMARKREQUEST_MSG.extensions = {}
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.name = "id"
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.full_name = ".ManorCompleteUnlockSectorRequest.id"
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.number = 1
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.index = 0
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.label = 2
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.has_default_value = false
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.default_value = 0
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.type = 5
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD.cpp_type = 1

tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.name = "useDiamond"
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.full_name = ".ManorCompleteUnlockSectorRequest.useDiamond"
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.number = 2
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.index = 1
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.label = 1
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.has_default_value = false
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.default_value = false
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.type = 8
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD.cpp_type = 7

tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.name = "isBuilding"
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.full_name = ".ManorCompleteUnlockSectorRequest.isBuilding"
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.number = 3
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.index = 2
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.label = 1
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.has_default_value = false
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.default_value = false
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.type = 8
tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD.cpp_type = 7

MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.name = "ManorCompleteUnlockSectorRequest"
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.full_name = ".ManorCompleteUnlockSectorRequest"
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.filename = "IslandExtension"
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.nested_types = {}
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.enum_types = {}
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.fields = {tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ID_FIELD, tb.MANORCOMPLETEUNLOCKSECTORREQUEST_USEDIAMOND_FIELD, tb.MANORCOMPLETEUNLOCKSECTORREQUEST_ISBUILDING_FIELD}
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.is_extendable = false
MANORCOMPLETEUNLOCKSECTORREQUEST_MSG.extensions = {}
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.name = "ids"
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.full_name = ".ManorCleanRubbishRequest.ids"
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.number = 1
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.index = 0
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.label = 3
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.has_default_value = false
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.default_value = {}
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.type = 5
tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD.cpp_type = 1

MANORCLEANRUBBISHREQUEST_MSG.name = "ManorCleanRubbishRequest"
MANORCLEANRUBBISHREQUEST_MSG.full_name = ".ManorCleanRubbishRequest"
MANORCLEANRUBBISHREQUEST_MSG.filename = "IslandExtension"
MANORCLEANRUBBISHREQUEST_MSG.nested_types = {}
MANORCLEANRUBBISHREQUEST_MSG.enum_types = {}
MANORCLEANRUBBISHREQUEST_MSG.fields = {tb.MANORCLEANRUBBISHREQUEST_IDS_FIELD}
MANORCLEANRUBBISHREQUEST_MSG.is_extendable = false
MANORCLEANRUBBISHREQUEST_MSG.extensions = {}
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.name = "templateId"
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.full_name = ".IslandUnlockFurnitureTemplateRequest.templateId"
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.number = 1
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.index = 0
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.label = 1
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.has_default_value = false
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.default_value = 0
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.type = 5
tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD.cpp_type = 1

ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.name = "IslandUnlockFurnitureTemplateRequest"
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.full_name = ".IslandUnlockFurnitureTemplateRequest"
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.filename = "IslandExtension"
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.nested_types = {}
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.enum_types = {}
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.fields = {tb.ISLANDUNLOCKFURNITURETEMPLATEREQUEST_TEMPLATEID_FIELD}
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.is_extendable = false
ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG.extensions = {}
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.full_name = ".FriendTreeGainReply.changeSetId"
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.number = 1
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.index = 0
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.label = 1
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.has_default_value = false
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.default_value = 0
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.type = 5
tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD.cpp_type = 1

FRIENDTREEGAINREPLY_MSG.name = "FriendTreeGainReply"
FRIENDTREEGAINREPLY_MSG.full_name = ".FriendTreeGainReply"
FRIENDTREEGAINREPLY_MSG.filename = "IslandExtension"
FRIENDTREEGAINREPLY_MSG.nested_types = {}
FRIENDTREEGAINREPLY_MSG.enum_types = {}
FRIENDTREEGAINREPLY_MSG.fields = {tb.FRIENDTREEGAINREPLY_CHANGESETID_FIELD}
FRIENDTREEGAINREPLY_MSG.is_extendable = false
FRIENDTREEGAINREPLY_MSG.extensions = {}
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.name = "animals"
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.full_name = ".ManorMoveAnimalRequest.animals"
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.number = 1
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.index = 0
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.label = 3
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.has_default_value = false
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.default_value = {}
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.type = 11
tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD.cpp_type = 10

tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.full_name = ".ManorMoveAnimalRequest.decorationSceneId"
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.number = 2
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.index = 1
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

MANORMOVEANIMALREQUEST_MSG.name = "ManorMoveAnimalRequest"
MANORMOVEANIMALREQUEST_MSG.full_name = ".ManorMoveAnimalRequest"
MANORMOVEANIMALREQUEST_MSG.filename = "IslandExtension"
MANORMOVEANIMALREQUEST_MSG.nested_types = {}
MANORMOVEANIMALREQUEST_MSG.enum_types = {}
MANORMOVEANIMALREQUEST_MSG.fields = {tb.MANORMOVEANIMALREQUEST_ANIMALS_FIELD, tb.MANORMOVEANIMALREQUEST_DECORATIONSCENEID_FIELD}
MANORMOVEANIMALREQUEST_MSG.is_extendable = false
MANORMOVEANIMALREQUEST_MSG.extensions = {}
tb.MANORCROPNO_ID_FIELD.name = "id"
tb.MANORCROPNO_ID_FIELD.full_name = ".ManorCropNO.id"
tb.MANORCROPNO_ID_FIELD.number = 1
tb.MANORCROPNO_ID_FIELD.index = 0
tb.MANORCROPNO_ID_FIELD.label = 1
tb.MANORCROPNO_ID_FIELD.has_default_value = false
tb.MANORCROPNO_ID_FIELD.default_value = 0
tb.MANORCROPNO_ID_FIELD.type = 5
tb.MANORCROPNO_ID_FIELD.cpp_type = 1

tb.MANORCROPNO_CROPTYPE_FIELD.name = "cropType"
tb.MANORCROPNO_CROPTYPE_FIELD.full_name = ".ManorCropNO.cropType"
tb.MANORCROPNO_CROPTYPE_FIELD.number = 2
tb.MANORCROPNO_CROPTYPE_FIELD.index = 1
tb.MANORCROPNO_CROPTYPE_FIELD.label = 1
tb.MANORCROPNO_CROPTYPE_FIELD.has_default_value = false
tb.MANORCROPNO_CROPTYPE_FIELD.default_value = 0
tb.MANORCROPNO_CROPTYPE_FIELD.type = 5
tb.MANORCROPNO_CROPTYPE_FIELD.cpp_type = 1

tb.MANORCROPNO_END_FIELD.name = "end"
tb.MANORCROPNO_END_FIELD.full_name = ".ManorCropNO.end"
tb.MANORCROPNO_END_FIELD.number = 3
tb.MANORCROPNO_END_FIELD.index = 2
tb.MANORCROPNO_END_FIELD.label = 1
tb.MANORCROPNO_END_FIELD.has_default_value = false
tb.MANORCROPNO_END_FIELD.default_value = 0
tb.MANORCROPNO_END_FIELD.type = 3
tb.MANORCROPNO_END_FIELD.cpp_type = 2

tb.MANORCROPNO_FIXED_FIELD.name = "fixed"
tb.MANORCROPNO_FIXED_FIELD.full_name = ".ManorCropNO.fixed"
tb.MANORCROPNO_FIXED_FIELD.number = 4
tb.MANORCROPNO_FIXED_FIELD.index = 3
tb.MANORCROPNO_FIXED_FIELD.label = 3
tb.MANORCROPNO_FIXED_FIELD.has_default_value = false
tb.MANORCROPNO_FIXED_FIELD.default_value = {}
tb.MANORCROPNO_FIXED_FIELD.message_type = BACKPACKEXTENSION_PB.ITEMBASEINFONO_MSG
tb.MANORCROPNO_FIXED_FIELD.type = 11
tb.MANORCROPNO_FIXED_FIELD.cpp_type = 10

tb.MANORCROPNO_RANDOM_FIELD.name = "random"
tb.MANORCROPNO_RANDOM_FIELD.full_name = ".ManorCropNO.random"
tb.MANORCROPNO_RANDOM_FIELD.number = 5
tb.MANORCROPNO_RANDOM_FIELD.index = 4
tb.MANORCROPNO_RANDOM_FIELD.label = 3
tb.MANORCROPNO_RANDOM_FIELD.has_default_value = false
tb.MANORCROPNO_RANDOM_FIELD.default_value = {}
tb.MANORCROPNO_RANDOM_FIELD.message_type = BACKPACKEXTENSION_PB.ITEMBASEINFONO_MSG
tb.MANORCROPNO_RANDOM_FIELD.type = 11
tb.MANORCROPNO_RANDOM_FIELD.cpp_type = 10

tb.MANORCROPNO_BUFFDEFINEID_FIELD.name = "buffDefineId"
tb.MANORCROPNO_BUFFDEFINEID_FIELD.full_name = ".ManorCropNO.buffDefineId"
tb.MANORCROPNO_BUFFDEFINEID_FIELD.number = 6
tb.MANORCROPNO_BUFFDEFINEID_FIELD.index = 5
tb.MANORCROPNO_BUFFDEFINEID_FIELD.label = 1
tb.MANORCROPNO_BUFFDEFINEID_FIELD.has_default_value = false
tb.MANORCROPNO_BUFFDEFINEID_FIELD.default_value = 0
tb.MANORCROPNO_BUFFDEFINEID_FIELD.type = 5
tb.MANORCROPNO_BUFFDEFINEID_FIELD.cpp_type = 1

tb.MANORCROPNO_FINALSTATE_FIELD.name = "finalState"
tb.MANORCROPNO_FINALSTATE_FIELD.full_name = ".ManorCropNO.finalState"
tb.MANORCROPNO_FINALSTATE_FIELD.number = 7
tb.MANORCROPNO_FINALSTATE_FIELD.index = 6
tb.MANORCROPNO_FINALSTATE_FIELD.label = 1
tb.MANORCROPNO_FINALSTATE_FIELD.has_default_value = false
tb.MANORCROPNO_FINALSTATE_FIELD.default_value = 0
tb.MANORCROPNO_FINALSTATE_FIELD.type = 5
tb.MANORCROPNO_FINALSTATE_FIELD.cpp_type = 1

tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.name = "buffItemEffectValue"
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.full_name = ".ManorCropNO.buffItemEffectValue"
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.number = 8
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.index = 7
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.label = 1
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.has_default_value = false
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.default_value = 0
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.type = 5
tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD.cpp_type = 1

MANORCROPNO_MSG.name = "ManorCropNO"
MANORCROPNO_MSG.full_name = ".ManorCropNO"
MANORCROPNO_MSG.filename = "IslandExtension"
MANORCROPNO_MSG.nested_types = {}
MANORCROPNO_MSG.enum_types = {}
MANORCROPNO_MSG.fields = {tb.MANORCROPNO_ID_FIELD, tb.MANORCROPNO_CROPTYPE_FIELD, tb.MANORCROPNO_END_FIELD, tb.MANORCROPNO_FIXED_FIELD, tb.MANORCROPNO_RANDOM_FIELD, tb.MANORCROPNO_BUFFDEFINEID_FIELD, tb.MANORCROPNO_FINALSTATE_FIELD, tb.MANORCROPNO_BUFFITEMEFFECTVALUE_FIELD}
MANORCROPNO_MSG.is_extendable = false
MANORCROPNO_MSG.extensions = {}
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.name = "furnitureUniqueId"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.full_name = ".IslandChangeCustomFurnitureParamsRequest.furnitureUniqueId"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.number = 1
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.index = 0
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.label = 1
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.has_default_value = false
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.default_value = 0
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.type = 5
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD.cpp_type = 1

tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.name = "customParams"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.full_name = ".IslandChangeCustomFurnitureParamsRequest.customParams"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.number = 2
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.index = 1
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.label = 3
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.has_default_value = false
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.default_value = {}
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.type = 5
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD.cpp_type = 1

tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.full_name = ".IslandChangeCustomFurnitureParamsRequest.decorationSceneId"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.number = 3
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.index = 2
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.name = "IslandChangeCustomFurnitureParamsRequest"
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.full_name = ".IslandChangeCustomFurnitureParamsRequest"
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.filename = "IslandExtension"
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.nested_types = {}
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.enum_types = {}
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.fields = {tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_FURNITUREUNIQUEID_FIELD, tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_CUSTOMPARAMS_FIELD, tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_DECORATIONSCENEID_FIELD}
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.is_extendable = false
ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG.extensions = {}
MANORGETRUBBISHINFOREQUEST_MSG.name = "ManorGetRubbishInfoRequest"
MANORGETRUBBISHINFOREQUEST_MSG.full_name = ".ManorGetRubbishInfoRequest"
MANORGETRUBBISHINFOREQUEST_MSG.filename = "IslandExtension"
MANORGETRUBBISHINFOREQUEST_MSG.nested_types = {}
MANORGETRUBBISHINFOREQUEST_MSG.enum_types = {}
MANORGETRUBBISHINFOREQUEST_MSG.fields = {}
MANORGETRUBBISHINFOREQUEST_MSG.is_extendable = false
MANORGETRUBBISHINFOREQUEST_MSG.extensions = {}
tb.GETISLANDINFOREQUEST_USERID_FIELD.name = "userId"
tb.GETISLANDINFOREQUEST_USERID_FIELD.full_name = ".GetIslandInfoRequest.userId"
tb.GETISLANDINFOREQUEST_USERID_FIELD.number = 1
tb.GETISLANDINFOREQUEST_USERID_FIELD.index = 0
tb.GETISLANDINFOREQUEST_USERID_FIELD.label = 1
tb.GETISLANDINFOREQUEST_USERID_FIELD.has_default_value = true
tb.GETISLANDINFOREQUEST_USERID_FIELD.default_value = ""
tb.GETISLANDINFOREQUEST_USERID_FIELD.type = 9
tb.GETISLANDINFOREQUEST_USERID_FIELD.cpp_type = 9

tb.GETISLANDINFOREQUEST_NPC_FIELD.name = "npc"
tb.GETISLANDINFOREQUEST_NPC_FIELD.full_name = ".GetIslandInfoRequest.npc"
tb.GETISLANDINFOREQUEST_NPC_FIELD.number = 2
tb.GETISLANDINFOREQUEST_NPC_FIELD.index = 1
tb.GETISLANDINFOREQUEST_NPC_FIELD.label = 1
tb.GETISLANDINFOREQUEST_NPC_FIELD.has_default_value = false
tb.GETISLANDINFOREQUEST_NPC_FIELD.default_value = false
tb.GETISLANDINFOREQUEST_NPC_FIELD.type = 8
tb.GETISLANDINFOREQUEST_NPC_FIELD.cpp_type = 7

GETISLANDINFOREQUEST_MSG.name = "GetIslandInfoRequest"
GETISLANDINFOREQUEST_MSG.full_name = ".GetIslandInfoRequest"
GETISLANDINFOREQUEST_MSG.filename = "IslandExtension"
GETISLANDINFOREQUEST_MSG.nested_types = {}
GETISLANDINFOREQUEST_MSG.enum_types = {}
GETISLANDINFOREQUEST_MSG.fields = {tb.GETISLANDINFOREQUEST_USERID_FIELD, tb.GETISLANDINFOREQUEST_NPC_FIELD}
GETISLANDINFOREQUEST_MSG.is_extendable = false
GETISLANDINFOREQUEST_MSG.extensions = {}
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.full_name = ".CatchButterflyReply.changeSetId"
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.number = 1
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.index = 0
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.label = 1
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.has_default_value = false
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.default_value = 0
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.type = 5
tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD.cpp_type = 1

CATCHBUTTERFLYREPLY_MSG.name = "CatchButterflyReply"
CATCHBUTTERFLYREPLY_MSG.full_name = ".CatchButterflyReply"
CATCHBUTTERFLYREPLY_MSG.filename = "IslandExtension"
CATCHBUTTERFLYREPLY_MSG.nested_types = {}
CATCHBUTTERFLYREPLY_MSG.enum_types = {}
CATCHBUTTERFLYREPLY_MSG.fields = {tb.CATCHBUTTERFLYREPLY_CHANGESETID_FIELD}
CATCHBUTTERFLYREPLY_MSG.is_extendable = false
CATCHBUTTERFLYREPLY_MSG.extensions = {}
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.full_name = ".ManorCleanRubbishReply.changeSetId"
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.number = 1
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.index = 0
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.label = 1
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.has_default_value = false
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.default_value = 0
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.type = 5
tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD.cpp_type = 1

tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.name = "time"
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.full_name = ".ManorCleanRubbishReply.time"
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.number = 2
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.index = 1
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.label = 1
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.has_default_value = false
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.default_value = 0
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.type = 3
tb.MANORCLEANRUBBISHREPLY_TIME_FIELD.cpp_type = 2

MANORCLEANRUBBISHREPLY_MSG.name = "ManorCleanRubbishReply"
MANORCLEANRUBBISHREPLY_MSG.full_name = ".ManorCleanRubbishReply"
MANORCLEANRUBBISHREPLY_MSG.filename = "IslandExtension"
MANORCLEANRUBBISHREPLY_MSG.nested_types = {}
MANORCLEANRUBBISHREPLY_MSG.enum_types = {}
MANORCLEANRUBBISHREPLY_MSG.fields = {tb.MANORCLEANRUBBISHREPLY_CHANGESETID_FIELD, tb.MANORCLEANRUBBISHREPLY_TIME_FIELD}
MANORCLEANRUBBISHREPLY_MSG.is_extendable = false
MANORCLEANRUBBISHREPLY_MSG.extensions = {}
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.name = "IslandSaveFurnitureTemplateReply"
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.full_name = ".IslandSaveFurnitureTemplateReply"
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.filename = "IslandExtension"
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.nested_types = {}
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.enum_types = {}
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.fields = {}
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.is_extendable = false
ISLANDSAVEFURNITURETEMPLATEREPLY_MSG.extensions = {}
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.name = "changeId"
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.full_name = ".IslandUnlockFurnitureTemplateReply.changeId"
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.number = 1
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.index = 0
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.label = 1
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.has_default_value = false
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.default_value = 0
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.type = 5
tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD.cpp_type = 1

ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.name = "IslandUnlockFurnitureTemplateReply"
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.full_name = ".IslandUnlockFurnitureTemplateReply"
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.filename = "IslandExtension"
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.nested_types = {}
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.enum_types = {}
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.fields = {tb.ISLANDUNLOCKFURNITURETEMPLATEREPLY_CHANGEID_FIELD}
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.is_extendable = false
ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG.extensions = {}
tb.TERRAININFONO_ID_FIELD.name = "id"
tb.TERRAININFONO_ID_FIELD.full_name = ".TerrainInfoNO.id"
tb.TERRAININFONO_ID_FIELD.number = 1
tb.TERRAININFONO_ID_FIELD.index = 0
tb.TERRAININFONO_ID_FIELD.label = 1
tb.TERRAININFONO_ID_FIELD.has_default_value = false
tb.TERRAININFONO_ID_FIELD.default_value = 0
tb.TERRAININFONO_ID_FIELD.type = 5
tb.TERRAININFONO_ID_FIELD.cpp_type = 1

tb.TERRAININFONO_FURNITUREID_FIELD.name = "furnitureId"
tb.TERRAININFONO_FURNITUREID_FIELD.full_name = ".TerrainInfoNO.furnitureId"
tb.TERRAININFONO_FURNITUREID_FIELD.number = 2
tb.TERRAININFONO_FURNITUREID_FIELD.index = 1
tb.TERRAININFONO_FURNITUREID_FIELD.label = 1
tb.TERRAININFONO_FURNITUREID_FIELD.has_default_value = false
tb.TERRAININFONO_FURNITUREID_FIELD.default_value = 0
tb.TERRAININFONO_FURNITUREID_FIELD.type = 5
tb.TERRAININFONO_FURNITUREID_FIELD.cpp_type = 1

tb.TERRAININFONO_POSX_FIELD.name = "posX"
tb.TERRAININFONO_POSX_FIELD.full_name = ".TerrainInfoNO.posX"
tb.TERRAININFONO_POSX_FIELD.number = 3
tb.TERRAININFONO_POSX_FIELD.index = 2
tb.TERRAININFONO_POSX_FIELD.label = 1
tb.TERRAININFONO_POSX_FIELD.has_default_value = false
tb.TERRAININFONO_POSX_FIELD.default_value = 0
tb.TERRAININFONO_POSX_FIELD.type = 17
tb.TERRAININFONO_POSX_FIELD.cpp_type = 1

tb.TERRAININFONO_POSY_FIELD.name = "posY"
tb.TERRAININFONO_POSY_FIELD.full_name = ".TerrainInfoNO.posY"
tb.TERRAININFONO_POSY_FIELD.number = 4
tb.TERRAININFONO_POSY_FIELD.index = 3
tb.TERRAININFONO_POSY_FIELD.label = 1
tb.TERRAININFONO_POSY_FIELD.has_default_value = false
tb.TERRAININFONO_POSY_FIELD.default_value = 0
tb.TERRAININFONO_POSY_FIELD.type = 17
tb.TERRAININFONO_POSY_FIELD.cpp_type = 1

tb.TERRAININFONO_DELETE_FIELD.name = "delete"
tb.TERRAININFONO_DELETE_FIELD.full_name = ".TerrainInfoNO.delete"
tb.TERRAININFONO_DELETE_FIELD.number = 5
tb.TERRAININFONO_DELETE_FIELD.index = 4
tb.TERRAININFONO_DELETE_FIELD.label = 1
tb.TERRAININFONO_DELETE_FIELD.has_default_value = false
tb.TERRAININFONO_DELETE_FIELD.default_value = false
tb.TERRAININFONO_DELETE_FIELD.type = 8
tb.TERRAININFONO_DELETE_FIELD.cpp_type = 7

tb.TERRAININFONO_LENGTH_FIELD.name = "length"
tb.TERRAININFONO_LENGTH_FIELD.full_name = ".TerrainInfoNO.length"
tb.TERRAININFONO_LENGTH_FIELD.number = 6
tb.TERRAININFONO_LENGTH_FIELD.index = 5
tb.TERRAININFONO_LENGTH_FIELD.label = 1
tb.TERRAININFONO_LENGTH_FIELD.has_default_value = false
tb.TERRAININFONO_LENGTH_FIELD.default_value = 0
tb.TERRAININFONO_LENGTH_FIELD.type = 5
tb.TERRAININFONO_LENGTH_FIELD.cpp_type = 1

tb.TERRAININFONO_WIDTH_FIELD.name = "width"
tb.TERRAININFONO_WIDTH_FIELD.full_name = ".TerrainInfoNO.width"
tb.TERRAININFONO_WIDTH_FIELD.number = 7
tb.TERRAININFONO_WIDTH_FIELD.index = 6
tb.TERRAININFONO_WIDTH_FIELD.label = 1
tb.TERRAININFONO_WIDTH_FIELD.has_default_value = false
tb.TERRAININFONO_WIDTH_FIELD.default_value = 0
tb.TERRAININFONO_WIDTH_FIELD.type = 5
tb.TERRAININFONO_WIDTH_FIELD.cpp_type = 1

TERRAININFONO_MSG.name = "TerrainInfoNO"
TERRAININFONO_MSG.full_name = ".TerrainInfoNO"
TERRAININFONO_MSG.filename = "IslandExtension"
TERRAININFONO_MSG.nested_types = {}
TERRAININFONO_MSG.enum_types = {}
TERRAININFONO_MSG.fields = {tb.TERRAININFONO_ID_FIELD, tb.TERRAININFONO_FURNITUREID_FIELD, tb.TERRAININFONO_POSX_FIELD, tb.TERRAININFONO_POSY_FIELD, tb.TERRAININFONO_DELETE_FIELD, tb.TERRAININFONO_LENGTH_FIELD, tb.TERRAININFONO_WIDTH_FIELD}
TERRAININFONO_MSG.is_extendable = false
TERRAININFONO_MSG.extensions = {}
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.name = "updateFurnitureInfo"
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.full_name = ".IslandFurnitureSwitchMarkReply.updateFurnitureInfo"
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.number = 1
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.index = 0
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.label = 2
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.has_default_value = false
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.default_value = nil
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.type = 11
tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD.cpp_type = 10

ISLANDFURNITURESWITCHMARKREPLY_MSG.name = "IslandFurnitureSwitchMarkReply"
ISLANDFURNITURESWITCHMARKREPLY_MSG.full_name = ".IslandFurnitureSwitchMarkReply"
ISLANDFURNITURESWITCHMARKREPLY_MSG.filename = "IslandExtension"
ISLANDFURNITURESWITCHMARKREPLY_MSG.nested_types = {}
ISLANDFURNITURESWITCHMARKREPLY_MSG.enum_types = {}
ISLANDFURNITURESWITCHMARKREPLY_MSG.fields = {tb.ISLANDFURNITURESWITCHMARKREPLY_UPDATEFURNITUREINFO_FIELD}
ISLANDFURNITURESWITCHMARKREPLY_MSG.is_extendable = false
ISLANDFURNITURESWITCHMARKREPLY_MSG.extensions = {}
tb.MANORGATHERCROPREQUEST_IDS_FIELD.name = "ids"
tb.MANORGATHERCROPREQUEST_IDS_FIELD.full_name = ".ManorGatherCropRequest.ids"
tb.MANORGATHERCROPREQUEST_IDS_FIELD.number = 1
tb.MANORGATHERCROPREQUEST_IDS_FIELD.index = 0
tb.MANORGATHERCROPREQUEST_IDS_FIELD.label = 3
tb.MANORGATHERCROPREQUEST_IDS_FIELD.has_default_value = false
tb.MANORGATHERCROPREQUEST_IDS_FIELD.default_value = {}
tb.MANORGATHERCROPREQUEST_IDS_FIELD.type = 5
tb.MANORGATHERCROPREQUEST_IDS_FIELD.cpp_type = 1

tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.name = "isUproot"
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.full_name = ".ManorGatherCropRequest.isUproot"
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.number = 2
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.index = 1
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.label = 1
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.has_default_value = false
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.default_value = false
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.type = 8
tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD.cpp_type = 7

tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.full_name = ".ManorGatherCropRequest.decorationSceneId"
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.number = 3
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.index = 2
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

MANORGATHERCROPREQUEST_MSG.name = "ManorGatherCropRequest"
MANORGATHERCROPREQUEST_MSG.full_name = ".ManorGatherCropRequest"
MANORGATHERCROPREQUEST_MSG.filename = "IslandExtension"
MANORGATHERCROPREQUEST_MSG.nested_types = {}
MANORGATHERCROPREQUEST_MSG.enum_types = {}
MANORGATHERCROPREQUEST_MSG.fields = {tb.MANORGATHERCROPREQUEST_IDS_FIELD, tb.MANORGATHERCROPREQUEST_ISUPROOT_FIELD, tb.MANORGATHERCROPREQUEST_DECORATIONSCENEID_FIELD}
MANORGATHERCROPREQUEST_MSG.is_extendable = false
MANORGATHERCROPREQUEST_MSG.extensions = {}
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.name = "plants"
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.full_name = ".CropItemChangePush.plants"
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.number = 1
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.index = 0
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.label = 3
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.has_default_value = false
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.default_value = {}
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.message_type = MANORCROPNO_MSG
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.type = 11
tb.CROPITEMCHANGEPUSH_PLANTS_FIELD.cpp_type = 10

tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.full_name = ".CropItemChangePush.decorationSceneId"
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.number = 2
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.index = 1
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.label = 1
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.has_default_value = false
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.default_value = 0
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.type = 5
tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD.cpp_type = 1

CROPITEMCHANGEPUSH_MSG.name = "CropItemChangePush"
CROPITEMCHANGEPUSH_MSG.full_name = ".CropItemChangePush"
CROPITEMCHANGEPUSH_MSG.filename = "IslandExtension"
CROPITEMCHANGEPUSH_MSG.nested_types = {}
CROPITEMCHANGEPUSH_MSG.enum_types = {}
CROPITEMCHANGEPUSH_MSG.fields = {tb.CROPITEMCHANGEPUSH_PLANTS_FIELD, tb.CROPITEMCHANGEPUSH_DECORATIONSCENEID_FIELD}
CROPITEMCHANGEPUSH_MSG.is_extendable = false
CROPITEMCHANGEPUSH_MSG.extensions = {}
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.name = "secondSinceLast"
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.full_name = ".OpenButterflyViewReply.secondSinceLast"
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.number = 1
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.index = 0
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.label = 1
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.has_default_value = false
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.default_value = 0
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.type = 5
tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD.cpp_type = 1

tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.name = "visitorNum"
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.full_name = ".OpenButterflyViewReply.visitorNum"
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.number = 2
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.index = 1
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.label = 1
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.has_default_value = false
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.default_value = 0
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.type = 5
tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD.cpp_type = 1

tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.name = "gainHeart"
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.full_name = ".OpenButterflyViewReply.gainHeart"
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.number = 3
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.index = 2
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.label = 1
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.has_default_value = false
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.default_value = 0
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.type = 5
tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD.cpp_type = 1

OPENBUTTERFLYVIEWREPLY_MSG.name = "OpenButterflyViewReply"
OPENBUTTERFLYVIEWREPLY_MSG.full_name = ".OpenButterflyViewReply"
OPENBUTTERFLYVIEWREPLY_MSG.filename = "IslandExtension"
OPENBUTTERFLYVIEWREPLY_MSG.nested_types = {}
OPENBUTTERFLYVIEWREPLY_MSG.enum_types = {}
OPENBUTTERFLYVIEWREPLY_MSG.fields = {tb.OPENBUTTERFLYVIEWREPLY_SECONDSINCELAST_FIELD, tb.OPENBUTTERFLYVIEWREPLY_VISITORNUM_FIELD, tb.OPENBUTTERFLYVIEWREPLY_GAINHEART_FIELD}
OPENBUTTERFLYVIEWREPLY_MSG.is_extendable = false
OPENBUTTERFLYVIEWREPLY_MSG.extensions = {}
tb.MANORPLANTREPLY_PLANTS_FIELD.name = "plants"
tb.MANORPLANTREPLY_PLANTS_FIELD.full_name = ".ManorPlantReply.plants"
tb.MANORPLANTREPLY_PLANTS_FIELD.number = 1
tb.MANORPLANTREPLY_PLANTS_FIELD.index = 0
tb.MANORPLANTREPLY_PLANTS_FIELD.label = 3
tb.MANORPLANTREPLY_PLANTS_FIELD.has_default_value = false
tb.MANORPLANTREPLY_PLANTS_FIELD.default_value = {}
tb.MANORPLANTREPLY_PLANTS_FIELD.message_type = MANORCROPNO_MSG
tb.MANORPLANTREPLY_PLANTS_FIELD.type = 11
tb.MANORPLANTREPLY_PLANTS_FIELD.cpp_type = 10

MANORPLANTREPLY_MSG.name = "ManorPlantReply"
MANORPLANTREPLY_MSG.full_name = ".ManorPlantReply"
MANORPLANTREPLY_MSG.filename = "IslandExtension"
MANORPLANTREPLY_MSG.nested_types = {}
MANORPLANTREPLY_MSG.enum_types = {}
MANORPLANTREPLY_MSG.fields = {tb.MANORPLANTREPLY_PLANTS_FIELD}
MANORPLANTREPLY_MSG.is_extendable = false
MANORPLANTREPLY_MSG.extensions = {}
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.name = "rubbishes"
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.full_name = ".ManorGetRubbishInfoReply.rubbishes"
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.number = 1
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.index = 0
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.label = 3
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.has_default_value = false
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.default_value = {}
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.message_type = RUBBISHNO_MSG
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.type = 11
tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD.cpp_type = 10

tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.name = "time"
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.full_name = ".ManorGetRubbishInfoReply.time"
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.number = 2
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.index = 1
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.label = 1
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.has_default_value = false
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.default_value = 0
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.type = 3
tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD.cpp_type = 2

MANORGETRUBBISHINFOREPLY_MSG.name = "ManorGetRubbishInfoReply"
MANORGETRUBBISHINFOREPLY_MSG.full_name = ".ManorGetRubbishInfoReply"
MANORGETRUBBISHINFOREPLY_MSG.filename = "IslandExtension"
MANORGETRUBBISHINFOREPLY_MSG.nested_types = {}
MANORGETRUBBISHINFOREPLY_MSG.enum_types = {}
MANORGETRUBBISHINFOREPLY_MSG.fields = {tb.MANORGETRUBBISHINFOREPLY_RUBBISHES_FIELD, tb.MANORGETRUBBISHINFOREPLY_TIME_FIELD}
MANORGETRUBBISHINFOREPLY_MSG.is_extendable = false
MANORGETRUBBISHINFOREPLY_MSG.extensions = {}
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.full_name = ".DecorateIslandRequest.decoratedItems"
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.number = 1
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.index = 0
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.label = 3
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.default_value = {}
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.type = 11
tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD.cpp_type = 10

tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.name = "sellingItems"
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.full_name = ".DecorateIslandRequest.sellingItems"
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.number = 2
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.index = 1
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.label = 3
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.default_value = {}
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.type = 5
tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD.cpp_type = 1

tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.name = "buyingItems"
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.full_name = ".DecorateIslandRequest.buyingItems"
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.number = 3
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.index = 2
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.label = 3
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.default_value = {}
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.type = 5
tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD.cpp_type = 1

tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.name = "takeBackFurniture"
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.full_name = ".DecorateIslandRequest.takeBackFurniture"
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.number = 4
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.index = 3
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.label = 3
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.default_value = {}
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.type = 5
tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD.cpp_type = 1

tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.name = "terrains"
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.full_name = ".DecorateIslandRequest.terrains"
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.number = 5
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.index = 4
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.label = 3
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.default_value = {}
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.message_type = TERRAININFONO_MSG
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.type = 11
tb.DECORATEISLANDREQUEST_TERRAINS_FIELD.cpp_type = 10

tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.name = "islandOutsideInfo"
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.full_name = ".DecorateIslandRequest.islandOutsideInfo"
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.number = 6
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.index = 5
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.label = 3
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.default_value = {}
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.type = 5
tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD.cpp_type = 1

tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.name = "decorateType"
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.full_name = ".DecorateIslandRequest.decorateType"
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.number = 7
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.index = 6
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.label = 1
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.default_value = 0
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.type = 5
tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD.cpp_type = 1

tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.name = "itemLockPassword"
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.full_name = ".DecorateIslandRequest.itemLockPassword"
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.number = 8
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.index = 7
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.label = 1
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.default_value = ""
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.type = 9
tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD.cpp_type = 9

tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.name = "weatherId"
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.full_name = ".DecorateIslandRequest.weatherId"
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.number = 9
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.index = 8
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.label = 1
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.has_default_value = false
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.default_value = 0
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.type = 5
tb.DECORATEISLANDREQUEST_WEATHERID_FIELD.cpp_type = 1

DECORATEISLANDREQUEST_MSG.name = "DecorateIslandRequest"
DECORATEISLANDREQUEST_MSG.full_name = ".DecorateIslandRequest"
DECORATEISLANDREQUEST_MSG.filename = "IslandExtension"
DECORATEISLANDREQUEST_MSG.nested_types = {}
DECORATEISLANDREQUEST_MSG.enum_types = {}
DECORATEISLANDREQUEST_MSG.fields = {tb.DECORATEISLANDREQUEST_DECORATEDITEMS_FIELD, tb.DECORATEISLANDREQUEST_SELLINGITEMS_FIELD, tb.DECORATEISLANDREQUEST_BUYINGITEMS_FIELD, tb.DECORATEISLANDREQUEST_TAKEBACKFURNITURE_FIELD, tb.DECORATEISLANDREQUEST_TERRAINS_FIELD, tb.DECORATEISLANDREQUEST_ISLANDOUTSIDEINFO_FIELD, tb.DECORATEISLANDREQUEST_DECORATETYPE_FIELD, tb.DECORATEISLANDREQUEST_ITEMLOCKPASSWORD_FIELD, tb.DECORATEISLANDREQUEST_WEATHERID_FIELD}
DECORATEISLANDREQUEST_MSG.is_extendable = false
DECORATEISLANDREQUEST_MSG.extensions = {}
tb.FRIENDTREENO_TID_FIELD.name = "tid"
tb.FRIENDTREENO_TID_FIELD.full_name = ".FriendTreeNo.tid"
tb.FRIENDTREENO_TID_FIELD.number = 1
tb.FRIENDTREENO_TID_FIELD.index = 0
tb.FRIENDTREENO_TID_FIELD.label = 1
tb.FRIENDTREENO_TID_FIELD.has_default_value = false
tb.FRIENDTREENO_TID_FIELD.default_value = 0
tb.FRIENDTREENO_TID_FIELD.type = 5
tb.FRIENDTREENO_TID_FIELD.cpp_type = 1

tb.FRIENDTREENO_DEFINEID_FIELD.name = "defineId"
tb.FRIENDTREENO_DEFINEID_FIELD.full_name = ".FriendTreeNo.defineId"
tb.FRIENDTREENO_DEFINEID_FIELD.number = 2
tb.FRIENDTREENO_DEFINEID_FIELD.index = 1
tb.FRIENDTREENO_DEFINEID_FIELD.label = 1
tb.FRIENDTREENO_DEFINEID_FIELD.has_default_value = false
tb.FRIENDTREENO_DEFINEID_FIELD.default_value = 0
tb.FRIENDTREENO_DEFINEID_FIELD.type = 5
tb.FRIENDTREENO_DEFINEID_FIELD.cpp_type = 1

tb.FRIENDTREENO_EXP_FIELD.name = "exp"
tb.FRIENDTREENO_EXP_FIELD.full_name = ".FriendTreeNo.exp"
tb.FRIENDTREENO_EXP_FIELD.number = 3
tb.FRIENDTREENO_EXP_FIELD.index = 2
tb.FRIENDTREENO_EXP_FIELD.label = 1
tb.FRIENDTREENO_EXP_FIELD.has_default_value = false
tb.FRIENDTREENO_EXP_FIELD.default_value = 0
tb.FRIENDTREENO_EXP_FIELD.type = 5
tb.FRIENDTREENO_EXP_FIELD.cpp_type = 1

tb.FRIENDTREENO_TODAYEXP_FIELD.name = "todayExp"
tb.FRIENDTREENO_TODAYEXP_FIELD.full_name = ".FriendTreeNo.todayExp"
tb.FRIENDTREENO_TODAYEXP_FIELD.number = 4
tb.FRIENDTREENO_TODAYEXP_FIELD.index = 3
tb.FRIENDTREENO_TODAYEXP_FIELD.label = 1
tb.FRIENDTREENO_TODAYEXP_FIELD.has_default_value = false
tb.FRIENDTREENO_TODAYEXP_FIELD.default_value = 0
tb.FRIENDTREENO_TODAYEXP_FIELD.type = 5
tb.FRIENDTREENO_TODAYEXP_FIELD.cpp_type = 1

tb.FRIENDTREENO_LEVEL_FIELD.name = "level"
tb.FRIENDTREENO_LEVEL_FIELD.full_name = ".FriendTreeNo.level"
tb.FRIENDTREENO_LEVEL_FIELD.number = 5
tb.FRIENDTREENO_LEVEL_FIELD.index = 4
tb.FRIENDTREENO_LEVEL_FIELD.label = 1
tb.FRIENDTREENO_LEVEL_FIELD.has_default_value = false
tb.FRIENDTREENO_LEVEL_FIELD.default_value = 0
tb.FRIENDTREENO_LEVEL_FIELD.type = 5
tb.FRIENDTREENO_LEVEL_FIELD.cpp_type = 1

tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.name = "lastFosterTime"
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.full_name = ".FriendTreeNo.lastFosterTime"
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.number = 6
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.index = 5
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.label = 1
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.has_default_value = false
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.default_value = 0
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.type = 3
tb.FRIENDTREENO_LASTFOSTERTIME_FIELD.cpp_type = 2

tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.name = "useFosterItemIds"
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.full_name = ".FriendTreeNo.useFosterItemIds"
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.number = 7
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.index = 6
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.label = 3
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.has_default_value = false
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.default_value = {}
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.type = 5
tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD.cpp_type = 1

FRIENDTREENO_MSG.name = "FriendTreeNo"
FRIENDTREENO_MSG.full_name = ".FriendTreeNo"
FRIENDTREENO_MSG.filename = "IslandExtension"
FRIENDTREENO_MSG.nested_types = {}
FRIENDTREENO_MSG.enum_types = {}
FRIENDTREENO_MSG.fields = {tb.FRIENDTREENO_TID_FIELD, tb.FRIENDTREENO_DEFINEID_FIELD, tb.FRIENDTREENO_EXP_FIELD, tb.FRIENDTREENO_TODAYEXP_FIELD, tb.FRIENDTREENO_LEVEL_FIELD, tb.FRIENDTREENO_LASTFOSTERTIME_FIELD, tb.FRIENDTREENO_USEFOSTERITEMIDS_FIELD}
FRIENDTREENO_MSG.is_extendable = false
FRIENDTREENO_MSG.extensions = {}
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.name = "item"
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.full_name = ".ManorUnlockSectorReply.item"
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.number = 1
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.index = 0
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.label = 3
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.has_default_value = false
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.default_value = {}
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.type = 11
tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD.cpp_type = 10

MANORUNLOCKSECTORREPLY_MSG.name = "ManorUnlockSectorReply"
MANORUNLOCKSECTORREPLY_MSG.full_name = ".ManorUnlockSectorReply"
MANORUNLOCKSECTORREPLY_MSG.filename = "IslandExtension"
MANORUNLOCKSECTORREPLY_MSG.nested_types = {}
MANORUNLOCKSECTORREPLY_MSG.enum_types = {}
MANORUNLOCKSECTORREPLY_MSG.fields = {tb.MANORUNLOCKSECTORREPLY_ITEM_FIELD}
MANORUNLOCKSECTORREPLY_MSG.is_extendable = false
MANORUNLOCKSECTORREPLY_MSG.extensions = {}
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.name = "id"
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.full_name = ".IsLandFurnitureChangeClothesRequest.id"
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.number = 1
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.index = 0
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.label = 2
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.has_default_value = false
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.default_value = 0
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.type = 5
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD.cpp_type = 1

tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.name = "clothes"
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.full_name = ".IsLandFurnitureChangeClothesRequest.clothes"
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.number = 2
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.index = 1
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.label = 3
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.has_default_value = false
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.default_value = {}
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.type = 5
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD.cpp_type = 1

tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.full_name = ".IsLandFurnitureChangeClothesRequest.decorationSceneId"
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.number = 3
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.index = 2
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.label = 1
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.has_default_value = false
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.default_value = 0
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.type = 5
tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD.cpp_type = 1

ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.name = "IsLandFurnitureChangeClothesRequest"
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.full_name = ".IsLandFurnitureChangeClothesRequest"
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.filename = "IslandExtension"
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.nested_types = {}
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.enum_types = {}
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.fields = {tb.ISLANDFURNITURECHANGECLOTHESREQUEST_ID_FIELD, tb.ISLANDFURNITURECHANGECLOTHESREQUEST_CLOTHES_FIELD, tb.ISLANDFURNITURECHANGECLOTHESREQUEST_DECORATIONSCENEID_FIELD}
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.is_extendable = false
ISLANDFURNITURECHANGECLOTHESREQUEST_MSG.extensions = {}
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.full_name = ".TakeButterflyBoxHeartReply.changeSetId"
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.number = 1
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.index = 0
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.label = 1
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.has_default_value = false
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.default_value = 0
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.type = 5
tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD.cpp_type = 1

TAKEBUTTERFLYBOXHEARTREPLY_MSG.name = "TakeButterflyBoxHeartReply"
TAKEBUTTERFLYBOXHEARTREPLY_MSG.full_name = ".TakeButterflyBoxHeartReply"
TAKEBUTTERFLYBOXHEARTREPLY_MSG.filename = "IslandExtension"
TAKEBUTTERFLYBOXHEARTREPLY_MSG.nested_types = {}
TAKEBUTTERFLYBOXHEARTREPLY_MSG.enum_types = {}
TAKEBUTTERFLYBOXHEARTREPLY_MSG.fields = {tb.TAKEBUTTERFLYBOXHEARTREPLY_CHANGESETID_FIELD}
TAKEBUTTERFLYBOXHEARTREPLY_MSG.is_extendable = false
TAKEBUTTERFLYBOXHEARTREPLY_MSG.extensions = {}
tb.GETISLANDINFOREPLY_SECTORS_FIELD.name = "sectors"
tb.GETISLANDINFOREPLY_SECTORS_FIELD.full_name = ".GetIslandInfoReply.sectors"
tb.GETISLANDINFOREPLY_SECTORS_FIELD.number = 1
tb.GETISLANDINFOREPLY_SECTORS_FIELD.index = 0
tb.GETISLANDINFOREPLY_SECTORS_FIELD.label = 3
tb.GETISLANDINFOREPLY_SECTORS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_SECTORS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_SECTORS_FIELD.message_type = UNLOCKINFONO_MSG
tb.GETISLANDINFOREPLY_SECTORS_FIELD.type = 11
tb.GETISLANDINFOREPLY_SECTORS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.name = "workShops"
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.full_name = ".GetIslandInfoReply.workShops"
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.number = 2
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.index = 1
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.label = 3
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.message_type = ISLANDWORKSHOPNO_MSG
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.type = 11
tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.full_name = ".GetIslandInfoReply.decoratedItems"
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.number = 3
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.index = 2
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.label = 3
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.type = 11
tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_CROPS_FIELD.name = "crops"
tb.GETISLANDINFOREPLY_CROPS_FIELD.full_name = ".GetIslandInfoReply.crops"
tb.GETISLANDINFOREPLY_CROPS_FIELD.number = 4
tb.GETISLANDINFOREPLY_CROPS_FIELD.index = 3
tb.GETISLANDINFOREPLY_CROPS_FIELD.label = 3
tb.GETISLANDINFOREPLY_CROPS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_CROPS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_CROPS_FIELD.message_type = MANORCROPNO_MSG
tb.GETISLANDINFOREPLY_CROPS_FIELD.type = 11
tb.GETISLANDINFOREPLY_CROPS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.name = "rubbishes"
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.full_name = ".GetIslandInfoReply.rubbishes"
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.number = 5
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.index = 4
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.label = 3
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.message_type = RUBBISHNO_MSG
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.type = 11
tb.GETISLANDINFOREPLY_RUBBISHES_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.name = "rubbishTime"
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.full_name = ".GetIslandInfoReply.rubbishTime"
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.number = 6
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.index = 5
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.label = 1
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.default_value = 0
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.type = 3
tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD.cpp_type = 2

tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.name = "isAllowToEat"
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.full_name = ".GetIslandInfoReply.isAllowToEat"
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.number = 7
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.index = 6
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.label = 1
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.default_value = false
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.type = 8
tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD.cpp_type = 7

tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.name = "houseInfo"
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.full_name = ".GetIslandInfoReply.houseInfo"
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.number = 8
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.index = 7
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.label = 3
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.message_type = HOUSEEXTENSION_PB.HOUSESHOWAREAINFO_MSG
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.type = 11
tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.name = "roleInfo"
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.full_name = ".GetIslandInfoReply.roleInfo"
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.number = 9
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.index = 8
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.label = 1
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.default_value = nil
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLEINFONO_MSG
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.type = 11
tb.GETISLANDINFOREPLY_ROLEINFO_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_COMFORT_FIELD.name = "comfort"
tb.GETISLANDINFOREPLY_COMFORT_FIELD.full_name = ".GetIslandInfoReply.comfort"
tb.GETISLANDINFOREPLY_COMFORT_FIELD.number = 10
tb.GETISLANDINFOREPLY_COMFORT_FIELD.index = 9
tb.GETISLANDINFOREPLY_COMFORT_FIELD.label = 1
tb.GETISLANDINFOREPLY_COMFORT_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_COMFORT_FIELD.default_value = nil
tb.GETISLANDINFOREPLY_COMFORT_FIELD.message_type = HOUSEEXTENSION_PB.COMFORTVALUENO_MSG
tb.GETISLANDINFOREPLY_COMFORT_FIELD.type = 11
tb.GETISLANDINFOREPLY_COMFORT_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.name = "friendTrees"
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.full_name = ".GetIslandInfoReply.friendTrees"
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.number = 11
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.index = 10
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.label = 3
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.message_type = FRIENDTREENO_MSG
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.type = 11
tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.name = "catchButterfly"
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.full_name = ".GetIslandInfoReply.catchButterfly"
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.number = 12
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.index = 11
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.label = 1
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.default_value = false
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.type = 8
tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD.cpp_type = 7

tb.GETISLANDINFOREPLY_BUILDING_FIELD.name = "building"
tb.GETISLANDINFOREPLY_BUILDING_FIELD.full_name = ".GetIslandInfoReply.building"
tb.GETISLANDINFOREPLY_BUILDING_FIELD.number = 13
tb.GETISLANDINFOREPLY_BUILDING_FIELD.index = 12
tb.GETISLANDINFOREPLY_BUILDING_FIELD.label = 3
tb.GETISLANDINFOREPLY_BUILDING_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_BUILDING_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_BUILDING_FIELD.message_type = UNLOCKINFONO_MSG
tb.GETISLANDINFOREPLY_BUILDING_FIELD.type = 11
tb.GETISLANDINFOREPLY_BUILDING_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_PICTURE_FIELD.name = "picture"
tb.GETISLANDINFOREPLY_PICTURE_FIELD.full_name = ".GetIslandInfoReply.picture"
tb.GETISLANDINFOREPLY_PICTURE_FIELD.number = 14
tb.GETISLANDINFOREPLY_PICTURE_FIELD.index = 13
tb.GETISLANDINFOREPLY_PICTURE_FIELD.label = 1
tb.GETISLANDINFOREPLY_PICTURE_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_PICTURE_FIELD.default_value = nil
tb.GETISLANDINFOREPLY_PICTURE_FIELD.message_type = ISLANDDIARYINFONO_MSG
tb.GETISLANDINFOREPLY_PICTURE_FIELD.type = 11
tb.GETISLANDINFOREPLY_PICTURE_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.name = "endSaveTime"
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.full_name = ".GetIslandInfoReply.endSaveTime"
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.number = 15
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.index = 14
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.label = 1
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.default_value = 0
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.type = 5
tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD.cpp_type = 1

tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.name = "travellerInfos"
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.full_name = ".GetIslandInfoReply.travellerInfos"
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.number = 16
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.index = 15
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.label = 3
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.message_type = TRAVELLEREXTENSION_PB.TRAVELLERNO_MSG
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.type = 11
tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.full_name = ".GetIslandInfoReply.foodItemInfos"
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.number = 17
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.index = 16
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.label = 3
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.type = 11
tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.name = "takePhoto"
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.full_name = ".GetIslandInfoReply.takePhoto"
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.number = 18
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.index = 17
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.label = 1
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.default_value = false
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.type = 8
tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD.cpp_type = 7

tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.name = "investInfo"
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.full_name = ".GetIslandInfoReply.investInfo"
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.number = 19
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.index = 18
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.label = 1
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.default_value = nil
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.message_type = DRAGONCHILDEXTENSION_PB.INVESTDETAILINFO_MSG
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.type = 11
tb.GETISLANDINFOREPLY_INVESTINFO_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.name = "needHappyBirthday"
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.full_name = ".GetIslandInfoReply.needHappyBirthday"
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.number = 20
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.index = 19
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.label = 1
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.default_value = false
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.type = 8
tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD.cpp_type = 7

tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.name = "needBackFlow2Story"
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.full_name = ".GetIslandInfoReply.needBackFlow2Story"
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.number = 21
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.index = 20
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.label = 1
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.default_value = false
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.type = 8
tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD.cpp_type = 7

tb.GETISLANDINFOREPLY_TERRAINS_FIELD.name = "terrains"
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.full_name = ".GetIslandInfoReply.terrains"
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.number = 22
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.index = 21
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.label = 3
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.message_type = TERRAININFONO_MSG
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.type = 11
tb.GETISLANDINFOREPLY_TERRAINS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.name = "islandOutside"
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.full_name = ".GetIslandInfoReply.islandOutside"
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.number = 23
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.index = 22
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.label = 3
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.type = 5
tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD.cpp_type = 1

tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.name = "elfHouseId"
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.full_name = ".GetIslandInfoReply.elfHouseId"
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.number = 24
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.index = 23
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.label = 1
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.default_value = 0
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.type = 5
tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD.cpp_type = 1

tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.name = "weatherInfos"
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.full_name = ".GetIslandInfoReply.weatherInfos"
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.number = 25
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.index = 24
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.label = 3
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.message_type = ISLANDWEATHERNO_MSG
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.type = 11
tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.name = "showSpecies"
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.full_name = ".GetIslandInfoReply.showSpecies"
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.number = 26
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.index = 25
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.label = 1
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.default_value = nil
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.message_type = ECOPARKEXTENSION_PB.ECOPARKSHOWSPECIESINFONO_MSG
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.type = 11
tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.name = "scenePetInfoNos"
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.full_name = ".GetIslandInfoReply.scenePetInfoNos"
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.number = 27
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.index = 26
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.label = 3
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.default_value = {}
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.message_type = PETEXTENSION_PB.PETINFONO_MSG
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.type = 11
tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD.cpp_type = 10

tb.GETISLANDINFOREPLY_STREETID_FIELD.name = "streetId"
tb.GETISLANDINFOREPLY_STREETID_FIELD.full_name = ".GetIslandInfoReply.streetId"
tb.GETISLANDINFOREPLY_STREETID_FIELD.number = 28
tb.GETISLANDINFOREPLY_STREETID_FIELD.index = 27
tb.GETISLANDINFOREPLY_STREETID_FIELD.label = 1
tb.GETISLANDINFOREPLY_STREETID_FIELD.has_default_value = false
tb.GETISLANDINFOREPLY_STREETID_FIELD.default_value = ""
tb.GETISLANDINFOREPLY_STREETID_FIELD.type = 9
tb.GETISLANDINFOREPLY_STREETID_FIELD.cpp_type = 9

GETISLANDINFOREPLY_MSG.name = "GetIslandInfoReply"
GETISLANDINFOREPLY_MSG.full_name = ".GetIslandInfoReply"
GETISLANDINFOREPLY_MSG.filename = "IslandExtension"
GETISLANDINFOREPLY_MSG.nested_types = {}
GETISLANDINFOREPLY_MSG.enum_types = {}
GETISLANDINFOREPLY_MSG.fields = {tb.GETISLANDINFOREPLY_SECTORS_FIELD, tb.GETISLANDINFOREPLY_WORKSHOPS_FIELD, tb.GETISLANDINFOREPLY_DECORATEDITEMS_FIELD, tb.GETISLANDINFOREPLY_CROPS_FIELD, tb.GETISLANDINFOREPLY_RUBBISHES_FIELD, tb.GETISLANDINFOREPLY_RUBBISHTIME_FIELD, tb.GETISLANDINFOREPLY_ISALLOWTOEAT_FIELD, tb.GETISLANDINFOREPLY_HOUSEINFO_FIELD, tb.GETISLANDINFOREPLY_ROLEINFO_FIELD, tb.GETISLANDINFOREPLY_COMFORT_FIELD, tb.GETISLANDINFOREPLY_FRIENDTREES_FIELD, tb.GETISLANDINFOREPLY_CATCHBUTTERFLY_FIELD, tb.GETISLANDINFOREPLY_BUILDING_FIELD, tb.GETISLANDINFOREPLY_PICTURE_FIELD, tb.GETISLANDINFOREPLY_ENDSAVETIME_FIELD, tb.GETISLANDINFOREPLY_TRAVELLERINFOS_FIELD, tb.GETISLANDINFOREPLY_FOODITEMINFOS_FIELD, tb.GETISLANDINFOREPLY_TAKEPHOTO_FIELD, tb.GETISLANDINFOREPLY_INVESTINFO_FIELD, tb.GETISLANDINFOREPLY_NEEDHAPPYBIRTHDAY_FIELD, tb.GETISLANDINFOREPLY_NEEDBACKFLOW2STORY_FIELD, tb.GETISLANDINFOREPLY_TERRAINS_FIELD, tb.GETISLANDINFOREPLY_ISLANDOUTSIDE_FIELD, tb.GETISLANDINFOREPLY_ELFHOUSEID_FIELD, tb.GETISLANDINFOREPLY_WEATHERINFOS_FIELD, tb.GETISLANDINFOREPLY_SHOWSPECIES_FIELD, tb.GETISLANDINFOREPLY_SCENEPETINFONOS_FIELD, tb.GETISLANDINFOREPLY_STREETID_FIELD}
GETISLANDINFOREPLY_MSG.is_extendable = false
GETISLANDINFOREPLY_MSG.extensions = {}
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.name = "currencyDecrement"
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.full_name = ".ManorSpeedUpCropReply.currencyDecrement"
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.number = 1
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.index = 0
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.label = 1
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.has_default_value = false
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.default_value = 0
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.type = 5
tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD.cpp_type = 1

MANORSPEEDUPCROPREPLY_MSG.name = "ManorSpeedUpCropReply"
MANORSPEEDUPCROPREPLY_MSG.full_name = ".ManorSpeedUpCropReply"
MANORSPEEDUPCROPREPLY_MSG.filename = "IslandExtension"
MANORSPEEDUPCROPREPLY_MSG.nested_types = {}
MANORSPEEDUPCROPREPLY_MSG.enum_types = {}
MANORSPEEDUPCROPREPLY_MSG.fields = {tb.MANORSPEEDUPCROPREPLY_CURRENCYDECREMENT_FIELD}
MANORSPEEDUPCROPREPLY_MSG.is_extendable = false
MANORSPEEDUPCROPREPLY_MSG.extensions = {}
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.name = "pictures"
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.full_name = ".GetAllIslandDiaryReply.pictures"
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.number = 1
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.index = 0
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.label = 3
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.has_default_value = false
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.default_value = {}
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.message_type = ISLANDDIARYINFONO_MSG
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.type = 11
tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD.cpp_type = 10

GETALLISLANDDIARYREPLY_MSG.name = "GetAllIslandDiaryReply"
GETALLISLANDDIARYREPLY_MSG.full_name = ".GetAllIslandDiaryReply"
GETALLISLANDDIARYREPLY_MSG.filename = "IslandExtension"
GETALLISLANDDIARYREPLY_MSG.nested_types = {}
GETALLISLANDDIARYREPLY_MSG.enum_types = {}
GETALLISLANDDIARYREPLY_MSG.fields = {tb.GETALLISLANDDIARYREPLY_PICTURES_FIELD}
GETALLISLANDDIARYREPLY_MSG.is_extendable = false
GETALLISLANDDIARYREPLY_MSG.extensions = {}
tb.ISLANDDIARYINFONO_ID_FIELD.name = "id"
tb.ISLANDDIARYINFONO_ID_FIELD.full_name = ".IslandDiaryInfoNO.id"
tb.ISLANDDIARYINFONO_ID_FIELD.number = 1
tb.ISLANDDIARYINFONO_ID_FIELD.index = 0
tb.ISLANDDIARYINFONO_ID_FIELD.label = 1
tb.ISLANDDIARYINFONO_ID_FIELD.has_default_value = false
tb.ISLANDDIARYINFONO_ID_FIELD.default_value = 0
tb.ISLANDDIARYINFONO_ID_FIELD.type = 5
tb.ISLANDDIARYINFONO_ID_FIELD.cpp_type = 1

tb.ISLANDDIARYINFONO_IMAGEID_FIELD.name = "imageId"
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.full_name = ".IslandDiaryInfoNO.imageId"
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.number = 2
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.index = 1
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.label = 1
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.has_default_value = false
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.default_value = ""
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.type = 9
tb.ISLANDDIARYINFONO_IMAGEID_FIELD.cpp_type = 9

tb.ISLANDDIARYINFONO_TIME_FIELD.name = "time"
tb.ISLANDDIARYINFONO_TIME_FIELD.full_name = ".IslandDiaryInfoNO.time"
tb.ISLANDDIARYINFONO_TIME_FIELD.number = 3
tb.ISLANDDIARYINFONO_TIME_FIELD.index = 2
tb.ISLANDDIARYINFONO_TIME_FIELD.label = 1
tb.ISLANDDIARYINFONO_TIME_FIELD.has_default_value = false
tb.ISLANDDIARYINFONO_TIME_FIELD.default_value = 0
tb.ISLANDDIARYINFONO_TIME_FIELD.type = 5
tb.ISLANDDIARYINFONO_TIME_FIELD.cpp_type = 1

ISLANDDIARYINFONO_MSG.name = "IslandDiaryInfoNO"
ISLANDDIARYINFONO_MSG.full_name = ".IslandDiaryInfoNO"
ISLANDDIARYINFONO_MSG.filename = "IslandExtension"
ISLANDDIARYINFONO_MSG.nested_types = {}
ISLANDDIARYINFONO_MSG.enum_types = {}
ISLANDDIARYINFONO_MSG.fields = {tb.ISLANDDIARYINFONO_ID_FIELD, tb.ISLANDDIARYINFONO_IMAGEID_FIELD, tb.ISLANDDIARYINFONO_TIME_FIELD}
ISLANDDIARYINFONO_MSG.is_extendable = false
ISLANDDIARYINFONO_MSG.extensions = {}
tb.ISLANDWEATHERNO_ID_FIELD.name = "id"
tb.ISLANDWEATHERNO_ID_FIELD.full_name = ".IslandWeatherNO.id"
tb.ISLANDWEATHERNO_ID_FIELD.number = 1
tb.ISLANDWEATHERNO_ID_FIELD.index = 0
tb.ISLANDWEATHERNO_ID_FIELD.label = 1
tb.ISLANDWEATHERNO_ID_FIELD.has_default_value = false
tb.ISLANDWEATHERNO_ID_FIELD.default_value = 0
tb.ISLANDWEATHERNO_ID_FIELD.type = 5
tb.ISLANDWEATHERNO_ID_FIELD.cpp_type = 1

tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.name = "remainUseTime"
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.full_name = ".IslandWeatherNO.remainUseTime"
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.number = 2
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.index = 1
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.label = 1
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.has_default_value = false
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.default_value = ""
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.type = 9
tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD.cpp_type = 9

tb.ISLANDWEATHERNO_USING_FIELD.name = "using"
tb.ISLANDWEATHERNO_USING_FIELD.full_name = ".IslandWeatherNO.using"
tb.ISLANDWEATHERNO_USING_FIELD.number = 3
tb.ISLANDWEATHERNO_USING_FIELD.index = 2
tb.ISLANDWEATHERNO_USING_FIELD.label = 1
tb.ISLANDWEATHERNO_USING_FIELD.has_default_value = false
tb.ISLANDWEATHERNO_USING_FIELD.default_value = false
tb.ISLANDWEATHERNO_USING_FIELD.type = 8
tb.ISLANDWEATHERNO_USING_FIELD.cpp_type = 7

ISLANDWEATHERNO_MSG.name = "IslandWeatherNO"
ISLANDWEATHERNO_MSG.full_name = ".IslandWeatherNO"
ISLANDWEATHERNO_MSG.filename = "IslandExtension"
ISLANDWEATHERNO_MSG.nested_types = {}
ISLANDWEATHERNO_MSG.enum_types = {}
ISLANDWEATHERNO_MSG.fields = {tb.ISLANDWEATHERNO_ID_FIELD, tb.ISLANDWEATHERNO_REMAINUSETIME_FIELD, tb.ISLANDWEATHERNO_USING_FIELD}
ISLANDWEATHERNO_MSG.is_extendable = false
ISLANDWEATHERNO_MSG.extensions = {}
tb.UNLOCKINFONO_ID_FIELD.name = "id"
tb.UNLOCKINFONO_ID_FIELD.full_name = ".UnlockInfoNO.id"
tb.UNLOCKINFONO_ID_FIELD.number = 1
tb.UNLOCKINFONO_ID_FIELD.index = 0
tb.UNLOCKINFONO_ID_FIELD.label = 2
tb.UNLOCKINFONO_ID_FIELD.has_default_value = false
tb.UNLOCKINFONO_ID_FIELD.default_value = 0
tb.UNLOCKINFONO_ID_FIELD.type = 5
tb.UNLOCKINFONO_ID_FIELD.cpp_type = 1

tb.UNLOCKINFONO_UNLOCKTIME_FIELD.name = "unlockTime"
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.full_name = ".UnlockInfoNO.unlockTime"
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.number = 2
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.index = 1
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.label = 1
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.has_default_value = false
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.default_value = 0
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.type = 5
tb.UNLOCKINFONO_UNLOCKTIME_FIELD.cpp_type = 1

UNLOCKINFONO_MSG.name = "UnlockInfoNO"
UNLOCKINFONO_MSG.full_name = ".UnlockInfoNO"
UNLOCKINFONO_MSG.filename = "IslandExtension"
UNLOCKINFONO_MSG.nested_types = {}
UNLOCKINFONO_MSG.enum_types = {}
UNLOCKINFONO_MSG.fields = {tb.UNLOCKINFONO_ID_FIELD, tb.UNLOCKINFONO_UNLOCKTIME_FIELD}
UNLOCKINFONO_MSG.is_extendable = false
UNLOCKINFONO_MSG.extensions = {}
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.name = "templateId"
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.full_name = ".IslandFurnitureTemplateInfoNO.templateId"
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.number = 1
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.index = 0
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.label = 1
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.has_default_value = false
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.default_value = 0
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.type = 5
tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD.cpp_type = 1

tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.name = "furnitures"
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.full_name = ".IslandFurnitureTemplateInfoNO.furnitures"
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.number = 2
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.index = 1
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.label = 3
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.has_default_value = false
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.default_value = {}
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.type = 11
tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD.cpp_type = 10

tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.name = "name"
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.full_name = ".IslandFurnitureTemplateInfoNO.name"
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.number = 3
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.index = 2
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.label = 1
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.has_default_value = false
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.default_value = ""
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.type = 9
tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD.cpp_type = 9

tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.name = "shareUniqueId"
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.full_name = ".IslandFurnitureTemplateInfoNO.shareUniqueId"
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.number = 4
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.index = 3
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.label = 1
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.has_default_value = false
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.default_value = ""
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.type = 9
tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD.cpp_type = 9

tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.name = "decorationSceneId"
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.full_name = ".IslandFurnitureTemplateInfoNO.decorationSceneId"
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.number = 5
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.index = 4
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.label = 1
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.has_default_value = false
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.default_value = 0
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.type = 5
tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD.cpp_type = 1

ISLANDFURNITURETEMPLATEINFONO_MSG.name = "IslandFurnitureTemplateInfoNO"
ISLANDFURNITURETEMPLATEINFONO_MSG.full_name = ".IslandFurnitureTemplateInfoNO"
ISLANDFURNITURETEMPLATEINFONO_MSG.filename = "IslandExtension"
ISLANDFURNITURETEMPLATEINFONO_MSG.nested_types = {}
ISLANDFURNITURETEMPLATEINFONO_MSG.enum_types = {}
ISLANDFURNITURETEMPLATEINFONO_MSG.fields = {tb.ISLANDFURNITURETEMPLATEINFONO_TEMPLATEID_FIELD, tb.ISLANDFURNITURETEMPLATEINFONO_FURNITURES_FIELD, tb.ISLANDFURNITURETEMPLATEINFONO_NAME_FIELD, tb.ISLANDFURNITURETEMPLATEINFONO_SHAREUNIQUEID_FIELD, tb.ISLANDFURNITURETEMPLATEINFONO_DECORATIONSCENEID_FIELD}
ISLANDFURNITURETEMPLATEINFONO_MSG.is_extendable = false
ISLANDFURNITURETEMPLATEINFONO_MSG.extensions = {}
tb.GATHERGAINNO_ID_FIELD.name = "id"
tb.GATHERGAINNO_ID_FIELD.full_name = ".GatherGainNO.id"
tb.GATHERGAINNO_ID_FIELD.number = 1
tb.GATHERGAINNO_ID_FIELD.index = 0
tb.GATHERGAINNO_ID_FIELD.label = 1
tb.GATHERGAINNO_ID_FIELD.has_default_value = false
tb.GATHERGAINNO_ID_FIELD.default_value = 0
tb.GATHERGAINNO_ID_FIELD.type = 5
tb.GATHERGAINNO_ID_FIELD.cpp_type = 1

tb.GATHERGAINNO_NUM_FIELD.name = "num"
tb.GATHERGAINNO_NUM_FIELD.full_name = ".GatherGainNO.num"
tb.GATHERGAINNO_NUM_FIELD.number = 2
tb.GATHERGAINNO_NUM_FIELD.index = 1
tb.GATHERGAINNO_NUM_FIELD.label = 1
tb.GATHERGAINNO_NUM_FIELD.has_default_value = false
tb.GATHERGAINNO_NUM_FIELD.default_value = 0
tb.GATHERGAINNO_NUM_FIELD.type = 5
tb.GATHERGAINNO_NUM_FIELD.cpp_type = 1

tb.GATHERGAINNO_CROPID_FIELD.name = "cropId"
tb.GATHERGAINNO_CROPID_FIELD.full_name = ".GatherGainNO.cropId"
tb.GATHERGAINNO_CROPID_FIELD.number = 3
tb.GATHERGAINNO_CROPID_FIELD.index = 2
tb.GATHERGAINNO_CROPID_FIELD.label = 1
tb.GATHERGAINNO_CROPID_FIELD.has_default_value = false
tb.GATHERGAINNO_CROPID_FIELD.default_value = 0
tb.GATHERGAINNO_CROPID_FIELD.type = 5
tb.GATHERGAINNO_CROPID_FIELD.cpp_type = 1

GATHERGAINNO_MSG.name = "GatherGainNO"
GATHERGAINNO_MSG.full_name = ".GatherGainNO"
GATHERGAINNO_MSG.filename = "IslandExtension"
GATHERGAINNO_MSG.nested_types = {}
GATHERGAINNO_MSG.enum_types = {}
GATHERGAINNO_MSG.fields = {tb.GATHERGAINNO_ID_FIELD, tb.GATHERGAINNO_NUM_FIELD, tb.GATHERGAINNO_CROPID_FIELD}
GATHERGAINNO_MSG.is_extendable = false
GATHERGAINNO_MSG.extensions = {}
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.name = "decoratedItems"
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.full_name = ".DecorateIslandReply.decoratedItems"
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.number = 1
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.index = 0
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.label = 3
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.default_value = {}
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.type = 11
tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD.cpp_type = 10

tb.DECORATEISLANDREPLY_CROPS_FIELD.name = "crops"
tb.DECORATEISLANDREPLY_CROPS_FIELD.full_name = ".DecorateIslandReply.crops"
tb.DECORATEISLANDREPLY_CROPS_FIELD.number = 2
tb.DECORATEISLANDREPLY_CROPS_FIELD.index = 1
tb.DECORATEISLANDREPLY_CROPS_FIELD.label = 3
tb.DECORATEISLANDREPLY_CROPS_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_CROPS_FIELD.default_value = {}
tb.DECORATEISLANDREPLY_CROPS_FIELD.message_type = MANORCROPNO_MSG
tb.DECORATEISLANDREPLY_CROPS_FIELD.type = 11
tb.DECORATEISLANDREPLY_CROPS_FIELD.cpp_type = 10

tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.name = "friendTrees"
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.full_name = ".DecorateIslandReply.friendTrees"
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.number = 3
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.index = 2
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.label = 3
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.default_value = {}
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.message_type = FRIENDTREENO_MSG
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.type = 11
tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD.cpp_type = 10

tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.name = "foodItemInfos"
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.full_name = ".DecorateIslandReply.foodItemInfos"
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.number = 4
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.index = 3
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.label = 3
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.default_value = {}
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.message_type = FOODEXTENSION_PB.FOODITEMNO_MSG
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.type = 11
tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD.cpp_type = 10

tb.DECORATEISLANDREPLY_TERRAINS_FIELD.name = "terrains"
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.full_name = ".DecorateIslandReply.terrains"
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.number = 5
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.index = 4
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.label = 3
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.default_value = {}
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.message_type = TERRAININFONO_MSG
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.type = 11
tb.DECORATEISLANDREPLY_TERRAINS_FIELD.cpp_type = 10

tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.name = "islandOutsideInfo"
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.full_name = ".DecorateIslandReply.islandOutsideInfo"
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.number = 6
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.index = 5
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.label = 3
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.default_value = {}
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.type = 5
tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD.cpp_type = 1

tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.name = "elfHouseId"
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.full_name = ".DecorateIslandReply.elfHouseId"
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.number = 7
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.index = 6
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.label = 1
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.default_value = 0
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.type = 5
tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD.cpp_type = 1

tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.name = "weatherInfos"
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.full_name = ".DecorateIslandReply.weatherInfos"
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.number = 8
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.index = 7
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.label = 3
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.has_default_value = false
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.default_value = {}
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.message_type = ISLANDWEATHERNO_MSG
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.type = 11
tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD.cpp_type = 10

DECORATEISLANDREPLY_MSG.name = "DecorateIslandReply"
DECORATEISLANDREPLY_MSG.full_name = ".DecorateIslandReply"
DECORATEISLANDREPLY_MSG.filename = "IslandExtension"
DECORATEISLANDREPLY_MSG.nested_types = {}
DECORATEISLANDREPLY_MSG.enum_types = {}
DECORATEISLANDREPLY_MSG.fields = {tb.DECORATEISLANDREPLY_DECORATEDITEMS_FIELD, tb.DECORATEISLANDREPLY_CROPS_FIELD, tb.DECORATEISLANDREPLY_FRIENDTREES_FIELD, tb.DECORATEISLANDREPLY_FOODITEMINFOS_FIELD, tb.DECORATEISLANDREPLY_TERRAINS_FIELD, tb.DECORATEISLANDREPLY_ISLANDOUTSIDEINFO_FIELD, tb.DECORATEISLANDREPLY_ELFHOUSEID_FIELD, tb.DECORATEISLANDREPLY_WEATHERINFOS_FIELD}
DECORATEISLANDREPLY_MSG.is_extendable = false
DECORATEISLANDREPLY_MSG.extensions = {}
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.name = "updateFurnitureInfo"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.full_name = ".IslandChangeCustomFurnitureParamsReply.updateFurnitureInfo"
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.number = 1
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.index = 0
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.label = 2
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.has_default_value = false
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.default_value = nil
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.message_type = HOUSEEXTENSION_PB.FURNITUREINFONO_MSG
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.type = 11
tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD.cpp_type = 10

ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.name = "IslandChangeCustomFurnitureParamsReply"
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.full_name = ".IslandChangeCustomFurnitureParamsReply"
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.filename = "IslandExtension"
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.nested_types = {}
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.enum_types = {}
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.fields = {tb.ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_UPDATEFURNITUREINFO_FIELD}
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.is_extendable = false
ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG.extensions = {}
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.full_name = ".FriendTreeFosterReply.changeSetId"
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.number = 1
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.index = 0
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.label = 1
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.has_default_value = false
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.default_value = 0
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.type = 5
tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD.cpp_type = 1

FRIENDTREEFOSTERREPLY_MSG.name = "FriendTreeFosterReply"
FRIENDTREEFOSTERREPLY_MSG.full_name = ".FriendTreeFosterReply"
FRIENDTREEFOSTERREPLY_MSG.filename = "IslandExtension"
FRIENDTREEFOSTERREPLY_MSG.nested_types = {}
FRIENDTREEFOSTERREPLY_MSG.enum_types = {}
FRIENDTREEFOSTERREPLY_MSG.fields = {tb.FRIENDTREEFOSTERREPLY_CHANGESETID_FIELD}
FRIENDTREEFOSTERREPLY_MSG.is_extendable = false
FRIENDTREEFOSTERREPLY_MSG.extensions = {}
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.name = "IslandGetAllFurnitureTemplateRequest"
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.full_name = ".IslandGetAllFurnitureTemplateRequest"
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.filename = "IslandExtension"
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.nested_types = {}
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.enum_types = {}
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.fields = {}
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.is_extendable = false
ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG.extensions = {}
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.name = "furnitureUniqueId"
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.full_name = ".IslandDecoratePillarRequest.furnitureUniqueId"
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.number = 1
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.index = 0
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.label = 1
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.has_default_value = false
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.default_value = 0
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.type = 5
tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD.cpp_type = 1

tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.name = "pillarFurniture"
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.full_name = ".IslandDecoratePillarRequest.pillarFurniture"
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.number = 2
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.index = 1
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.label = 3
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.has_default_value = false
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.default_value = {}
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.message_type = HOUSEEXTENSION_PB.PILLARINFONO_MSG
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.type = 11
tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD.cpp_type = 10

tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.name = "houseId"
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.full_name = ".IslandDecoratePillarRequest.houseId"
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.number = 3
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.index = 2
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.label = 1
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.has_default_value = false
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.default_value = 0
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.type = 5
tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD.cpp_type = 1

ISLANDDECORATEPILLARREQUEST_MSG.name = "IslandDecoratePillarRequest"
ISLANDDECORATEPILLARREQUEST_MSG.full_name = ".IslandDecoratePillarRequest"
ISLANDDECORATEPILLARREQUEST_MSG.filename = "IslandExtension"
ISLANDDECORATEPILLARREQUEST_MSG.nested_types = {}
ISLANDDECORATEPILLARREQUEST_MSG.enum_types = {}
ISLANDDECORATEPILLARREQUEST_MSG.fields = {tb.ISLANDDECORATEPILLARREQUEST_FURNITUREUNIQUEID_FIELD, tb.ISLANDDECORATEPILLARREQUEST_PILLARFURNITURE_FIELD, tb.ISLANDDECORATEPILLARREQUEST_HOUSEID_FIELD}
ISLANDDECORATEPILLARREQUEST_MSG.is_extendable = false
ISLANDDECORATEPILLARREQUEST_MSG.extensions = {}
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.name = "TakeButterflyBoxHeartRequest"
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.full_name = ".TakeButterflyBoxHeartRequest"
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.filename = "IslandExtension"
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.nested_types = {}
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.enum_types = {}
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.fields = {}
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.is_extendable = false
TAKEBUTTERFLYBOXHEARTREQUEST_MSG.extensions = {}
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.name = "templateInfo"
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.full_name = ".IslandSaveFurnitureTemplateRequest.templateInfo"
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.number = 1
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.index = 0
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.label = 1
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.has_default_value = false
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.default_value = nil
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.message_type = ISLANDFURNITURETEMPLATEINFONO_MSG
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.type = 11
tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD.cpp_type = 10

ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.name = "IslandSaveFurnitureTemplateRequest"
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.full_name = ".IslandSaveFurnitureTemplateRequest"
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.filename = "IslandExtension"
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.nested_types = {}
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.enum_types = {}
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.fields = {tb.ISLANDSAVEFURNITURETEMPLATEREQUEST_TEMPLATEINFO_FIELD}
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.is_extendable = false
ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG.extensions = {}
ISLANDDECORATEPILLARREPLY_MSG.name = "IslandDecoratePillarReply"
ISLANDDECORATEPILLARREPLY_MSG.full_name = ".IslandDecoratePillarReply"
ISLANDDECORATEPILLARREPLY_MSG.filename = "IslandExtension"
ISLANDDECORATEPILLARREPLY_MSG.nested_types = {}
ISLANDDECORATEPILLARREPLY_MSG.enum_types = {}
ISLANDDECORATEPILLARREPLY_MSG.fields = {}
ISLANDDECORATEPILLARREPLY_MSG.is_extendable = false
ISLANDDECORATEPILLARREPLY_MSG.extensions = {}

CatchButterflyReply = protobuf.Message(CATCHBUTTERFLYREPLY_MSG)
CatchButterflyRequest = protobuf.Message(CATCHBUTTERFLYREQUEST_MSG)
CropItemChangePush = protobuf.Message(CROPITEMCHANGEPUSH_MSG)
DecorateIslandReply = protobuf.Message(DECORATEISLANDREPLY_MSG)
DecorateIslandRequest = protobuf.Message(DECORATEISLANDREQUEST_MSG)
FURNITURE = 1
FriendTreeFosterReply = protobuf.Message(FRIENDTREEFOSTERREPLY_MSG)
FriendTreeFosterRequest = protobuf.Message(FRIENDTREEFOSTERREQUEST_MSG)
FriendTreeGainReply = protobuf.Message(FRIENDTREEGAINREPLY_MSG)
FriendTreeGainRequest = protobuf.Message(FRIENDTREEGAINREQUEST_MSG)
FriendTreeNo = protobuf.Message(FRIENDTREENO_MSG)
FurnitureInfoChangePush = protobuf.Message(FURNITUREINFOCHANGEPUSH_MSG)
GatherGainNO = protobuf.Message(GATHERGAINNO_MSG)
GetAllIslandDiaryReply = protobuf.Message(GETALLISLANDDIARYREPLY_MSG)
GetAllIslandDiaryRequest = protobuf.Message(GETALLISLANDDIARYREQUEST_MSG)
GetIslandInfoReply = protobuf.Message(GETISLANDINFOREPLY_MSG)
GetIslandInfoRequest = protobuf.Message(GETISLANDINFOREQUEST_MSG)
ISLAND_OUTSIDE = 4
IsLandFurnitureChangeClothesReply = protobuf.Message(ISLANDFURNITURECHANGECLOTHESREPLY_MSG)
IsLandFurnitureChangeClothesRequest = protobuf.Message(ISLANDFURNITURECHANGECLOTHESREQUEST_MSG)
IslandChangeCustomFurnitureParamsReply = protobuf.Message(ISLANDCHANGECUSTOMFURNITUREPARAMSREPLY_MSG)
IslandChangeCustomFurnitureParamsRequest = protobuf.Message(ISLANDCHANGECUSTOMFURNITUREPARAMSREQUEST_MSG)
IslandDecoratePillarReply = protobuf.Message(ISLANDDECORATEPILLARREPLY_MSG)
IslandDecoratePillarRequest = protobuf.Message(ISLANDDECORATEPILLARREQUEST_MSG)
IslandDiaryInfoNO = protobuf.Message(ISLANDDIARYINFONO_MSG)
IslandFurnitureSwitchMarkReply = protobuf.Message(ISLANDFURNITURESWITCHMARKREPLY_MSG)
IslandFurnitureSwitchMarkRequest = protobuf.Message(ISLANDFURNITURESWITCHMARKREQUEST_MSG)
IslandFurnitureTemplateInfoNO = protobuf.Message(ISLANDFURNITURETEMPLATEINFONO_MSG)
IslandGetAllFurnitureTemplateReply = protobuf.Message(ISLANDGETALLFURNITURETEMPLATEREPLY_MSG)
IslandGetAllFurnitureTemplateRequest = protobuf.Message(ISLANDGETALLFURNITURETEMPLATEREQUEST_MSG)
IslandModifyFurnitureTemplateNameReply = protobuf.Message(ISLANDMODIFYFURNITURETEMPLATENAMEREPLY_MSG)
IslandModifyFurnitureTemplateNameRequest = protobuf.Message(ISLANDMODIFYFURNITURETEMPLATENAMEREQUEST_MSG)
IslandSaveFurnitureTemplateReply = protobuf.Message(ISLANDSAVEFURNITURETEMPLATEREPLY_MSG)
IslandSaveFurnitureTemplateRequest = protobuf.Message(ISLANDSAVEFURNITURETEMPLATEREQUEST_MSG)
IslandUnlockFurnitureTemplateReply = protobuf.Message(ISLANDUNLOCKFURNITURETEMPLATEREPLY_MSG)
IslandUnlockFurnitureTemplateRequest = protobuf.Message(ISLANDUNLOCKFURNITURETEMPLATEREQUEST_MSG)
IslandWeatherNO = protobuf.Message(ISLANDWEATHERNO_MSG)
IslandWorkShopNO = protobuf.Message(ISLANDWORKSHOPNO_MSG)
ManorCleanRubbishReply = protobuf.Message(MANORCLEANRUBBISHREPLY_MSG)
ManorCleanRubbishRequest = protobuf.Message(MANORCLEANRUBBISHREQUEST_MSG)
ManorCompleteUnlockSectorReply = protobuf.Message(MANORCOMPLETEUNLOCKSECTORREPLY_MSG)
ManorCompleteUnlockSectorRequest = protobuf.Message(MANORCOMPLETEUNLOCKSECTORREQUEST_MSG)
ManorCropNO = protobuf.Message(MANORCROPNO_MSG)
ManorGatherCropReply = protobuf.Message(MANORGATHERCROPREPLY_MSG)
ManorGatherCropRequest = protobuf.Message(MANORGATHERCROPREQUEST_MSG)
ManorGetRubbishInfoReply = protobuf.Message(MANORGETRUBBISHINFOREPLY_MSG)
ManorGetRubbishInfoRequest = protobuf.Message(MANORGETRUBBISHINFOREQUEST_MSG)
ManorMoveAnimalReply = protobuf.Message(MANORMOVEANIMALREPLY_MSG)
ManorMoveAnimalRequest = protobuf.Message(MANORMOVEANIMALREQUEST_MSG)
ManorPlantReply = protobuf.Message(MANORPLANTREPLY_MSG)
ManorPlantRequest = protobuf.Message(MANORPLANTREQUEST_MSG)
ManorSpeedUpCropReply = protobuf.Message(MANORSPEEDUPCROPREPLY_MSG)
ManorSpeedUpCropRequest = protobuf.Message(MANORSPEEDUPCROPREQUEST_MSG)
ManorUnlockSectorReply = protobuf.Message(MANORUNLOCKSECTORREPLY_MSG)
ManorUnlockSectorRequest = protobuf.Message(MANORUNLOCKSECTORREQUEST_MSG)
OpenButterflyViewReply = protobuf.Message(OPENBUTTERFLYVIEWREPLY_MSG)
OpenButterflyViewRequest = protobuf.Message(OPENBUTTERFLYVIEWREQUEST_MSG)
RubbishNO = protobuf.Message(RUBBISHNO_MSG)
STREET_PERSON = 16
STREET_PUBLIC = 32
TERRAIN = 2
TakeButterflyBoxHeartReply = protobuf.Message(TAKEBUTTERFLYBOXHEARTREPLY_MSG)
TakeButterflyBoxHeartRequest = protobuf.Message(TAKEBUTTERFLYBOXHEARTREQUEST_MSG)
TerrainInfoNO = protobuf.Message(TERRAININFONO_MSG)
UnlockInfoNO = protobuf.Message(UNLOCKINFONO_MSG)
WEATHER = 8

return _G["logic.proto.IslandExtension_pb"]
