-- Generated By protoc-gen-lua Do not Edit
local protobuf = require "protobuf.protobuf"
local USEREXTENSION_PB = require("logic.proto.UserExtension_pb")
module("logic.proto.OrderExtension_pb", package.seeall)


local tb = {}
REJECTORDERREQUEST_MSG = protobuf.Descriptor()
tb.REJECTORDERREQUEST_TYPE_FIELD = protobuf.FieldDescriptor()
tb.REJECTORDERREQUEST_ID_FIELD = protobuf.FieldDescriptor()
ORDERITEMNO_MSG = protobuf.Descriptor()
tb.ORDERITEMNO_NPC_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_ID_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_STATE_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_CREATETIME_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_ITEMID_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_ITEMCOUNT_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_CURRENCY_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_ENDWAITING_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_POOLID_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_COMMITTERINFO_FIELD = protobuf.FieldDescriptor()
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD = protobuf.FieldDescriptor()
COMMITORDERREQUEST_MSG = protobuf.Descriptor()
tb.COMMITORDERREQUEST_TYPE_FIELD = protobuf.FieldDescriptor()
tb.COMMITORDERREQUEST_ID_FIELD = protobuf.FieldDescriptor()
tb.COMMITORDERREQUEST_CONTENT_FIELD = protobuf.FieldDescriptor()
tb.COMMITORDERREQUEST_USECOUPON_FIELD = protobuf.FieldDescriptor()
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD = protobuf.FieldDescriptor()
COMMITORDERREPLY_MSG = protobuf.Descriptor()
tb.COMMITORDERREPLY_NEWORDER_FIELD = protobuf.FieldDescriptor()
tb.COMMITORDERREPLY_CHANGESETID_FIELD = protobuf.FieldDescriptor()
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD = protobuf.FieldDescriptor()
FINISHORDERREPLY_MSG = protobuf.Descriptor()
tb.FINISHORDERREPLY_COMMITTER_FIELD = protobuf.FieldDescriptor()
tb.FINISHORDERREPLY_FINISHTIMES_FIELD = protobuf.FieldDescriptor()
CREATEORDERREQUEST_MSG = protobuf.Descriptor()
tb.CREATEORDERREQUEST_ITEMID_FIELD = protobuf.FieldDescriptor()
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD = protobuf.FieldDescriptor()
tb.CREATEORDERREQUEST_CURRENCY_FIELD = protobuf.FieldDescriptor()
REJECTALLORDERREPLY_MSG = protobuf.Descriptor()
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD = protobuf.FieldDescriptor()
CLEARNPCORDERWAITINGREQUEST_MSG = protobuf.Descriptor()
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD = protobuf.FieldDescriptor()
FINISHORDERREQUEST_MSG = protobuf.Descriptor()
BUYCOMMITTIMESREQUEST_MSG = protobuf.Descriptor()
GETORDERINFOREQUEST_MSG = protobuf.Descriptor()
COMMITTERINFONO_MSG = protobuf.Descriptor()
tb.COMMITTERINFONO_UID_FIELD = protobuf.FieldDescriptor()
tb.COMMITTERINFONO_NICKNAME_FIELD = protobuf.FieldDescriptor()
tb.COMMITTERINFONO_MODELID_FIELD = protobuf.FieldDescriptor()
tb.COMMITTERINFONO_CLOTHES_FIELD = protobuf.FieldDescriptor()
tb.COMMITTERINFONO_CONTENT_FIELD = protobuf.FieldDescriptor()
REJECTALLORDERREQUEST_MSG = protobuf.Descriptor()
CREATEORDERREPLY_MSG = protobuf.Descriptor()
GETORDERINFOREPLY_MSG = protobuf.Descriptor()
tb.GETORDERINFOREPLY_ORDERS_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_MINE_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD = protobuf.FieldDescriptor()
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD = protobuf.FieldDescriptor()
REJECTORDERREPLY_MSG = protobuf.Descriptor()
tb.REJECTORDERREPLY_NEWORDER_FIELD = protobuf.FieldDescriptor()
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD = protobuf.FieldDescriptor()
CANCELORDERREPLY_MSG = protobuf.Descriptor()
CLEARNPCORDERWAITINGREPLY_MSG = protobuf.Descriptor()
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD = protobuf.FieldDescriptor()
CANCELORDERREQUEST_MSG = protobuf.Descriptor()
BUYCOMMITTIMESREPLY_MSG = protobuf.Descriptor()

tb.REJECTORDERREQUEST_TYPE_FIELD.name = "type"
tb.REJECTORDERREQUEST_TYPE_FIELD.full_name = ".RejectOrderRequest.type"
tb.REJECTORDERREQUEST_TYPE_FIELD.number = 1
tb.REJECTORDERREQUEST_TYPE_FIELD.index = 0
tb.REJECTORDERREQUEST_TYPE_FIELD.label = 1
tb.REJECTORDERREQUEST_TYPE_FIELD.has_default_value = false
tb.REJECTORDERREQUEST_TYPE_FIELD.default_value = 0
tb.REJECTORDERREQUEST_TYPE_FIELD.type = 5
tb.REJECTORDERREQUEST_TYPE_FIELD.cpp_type = 1

tb.REJECTORDERREQUEST_ID_FIELD.name = "id"
tb.REJECTORDERREQUEST_ID_FIELD.full_name = ".RejectOrderRequest.id"
tb.REJECTORDERREQUEST_ID_FIELD.number = 2
tb.REJECTORDERREQUEST_ID_FIELD.index = 1
tb.REJECTORDERREQUEST_ID_FIELD.label = 1
tb.REJECTORDERREQUEST_ID_FIELD.has_default_value = false
tb.REJECTORDERREQUEST_ID_FIELD.default_value = ""
tb.REJECTORDERREQUEST_ID_FIELD.type = 9
tb.REJECTORDERREQUEST_ID_FIELD.cpp_type = 9

REJECTORDERREQUEST_MSG.name = "RejectOrderRequest"
REJECTORDERREQUEST_MSG.full_name = ".RejectOrderRequest"
REJECTORDERREQUEST_MSG.filename = "OrderExtension"
REJECTORDERREQUEST_MSG.nested_types = {}
REJECTORDERREQUEST_MSG.enum_types = {}
REJECTORDERREQUEST_MSG.fields = {tb.REJECTORDERREQUEST_TYPE_FIELD, tb.REJECTORDERREQUEST_ID_FIELD}
REJECTORDERREQUEST_MSG.is_extendable = false
REJECTORDERREQUEST_MSG.extensions = {}
tb.ORDERITEMNO_NPC_FIELD.name = "npc"
tb.ORDERITEMNO_NPC_FIELD.full_name = ".OrderItemNO.npc"
tb.ORDERITEMNO_NPC_FIELD.number = 1
tb.ORDERITEMNO_NPC_FIELD.index = 0
tb.ORDERITEMNO_NPC_FIELD.label = 1
tb.ORDERITEMNO_NPC_FIELD.has_default_value = false
tb.ORDERITEMNO_NPC_FIELD.default_value = false
tb.ORDERITEMNO_NPC_FIELD.type = 8
tb.ORDERITEMNO_NPC_FIELD.cpp_type = 7

tb.ORDERITEMNO_ID_FIELD.name = "id"
tb.ORDERITEMNO_ID_FIELD.full_name = ".OrderItemNO.id"
tb.ORDERITEMNO_ID_FIELD.number = 2
tb.ORDERITEMNO_ID_FIELD.index = 1
tb.ORDERITEMNO_ID_FIELD.label = 1
tb.ORDERITEMNO_ID_FIELD.has_default_value = false
tb.ORDERITEMNO_ID_FIELD.default_value = ""
tb.ORDERITEMNO_ID_FIELD.type = 9
tb.ORDERITEMNO_ID_FIELD.cpp_type = 9

tb.ORDERITEMNO_STATE_FIELD.name = "state"
tb.ORDERITEMNO_STATE_FIELD.full_name = ".OrderItemNO.state"
tb.ORDERITEMNO_STATE_FIELD.number = 3
tb.ORDERITEMNO_STATE_FIELD.index = 2
tb.ORDERITEMNO_STATE_FIELD.label = 1
tb.ORDERITEMNO_STATE_FIELD.has_default_value = false
tb.ORDERITEMNO_STATE_FIELD.default_value = 0
tb.ORDERITEMNO_STATE_FIELD.type = 5
tb.ORDERITEMNO_STATE_FIELD.cpp_type = 1

tb.ORDERITEMNO_CREATETIME_FIELD.name = "createTime"
tb.ORDERITEMNO_CREATETIME_FIELD.full_name = ".OrderItemNO.createTime"
tb.ORDERITEMNO_CREATETIME_FIELD.number = 4
tb.ORDERITEMNO_CREATETIME_FIELD.index = 3
tb.ORDERITEMNO_CREATETIME_FIELD.label = 1
tb.ORDERITEMNO_CREATETIME_FIELD.has_default_value = false
tb.ORDERITEMNO_CREATETIME_FIELD.default_value = 0
tb.ORDERITEMNO_CREATETIME_FIELD.type = 3
tb.ORDERITEMNO_CREATETIME_FIELD.cpp_type = 2

tb.ORDERITEMNO_ITEMID_FIELD.name = "itemId"
tb.ORDERITEMNO_ITEMID_FIELD.full_name = ".OrderItemNO.itemId"
tb.ORDERITEMNO_ITEMID_FIELD.number = 5
tb.ORDERITEMNO_ITEMID_FIELD.index = 4
tb.ORDERITEMNO_ITEMID_FIELD.label = 1
tb.ORDERITEMNO_ITEMID_FIELD.has_default_value = false
tb.ORDERITEMNO_ITEMID_FIELD.default_value = 0
tb.ORDERITEMNO_ITEMID_FIELD.type = 5
tb.ORDERITEMNO_ITEMID_FIELD.cpp_type = 1

tb.ORDERITEMNO_ITEMCOUNT_FIELD.name = "itemCount"
tb.ORDERITEMNO_ITEMCOUNT_FIELD.full_name = ".OrderItemNO.itemCount"
tb.ORDERITEMNO_ITEMCOUNT_FIELD.number = 6
tb.ORDERITEMNO_ITEMCOUNT_FIELD.index = 5
tb.ORDERITEMNO_ITEMCOUNT_FIELD.label = 1
tb.ORDERITEMNO_ITEMCOUNT_FIELD.has_default_value = false
tb.ORDERITEMNO_ITEMCOUNT_FIELD.default_value = 0
tb.ORDERITEMNO_ITEMCOUNT_FIELD.type = 5
tb.ORDERITEMNO_ITEMCOUNT_FIELD.cpp_type = 1

tb.ORDERITEMNO_CURRENCY_FIELD.name = "currency"
tb.ORDERITEMNO_CURRENCY_FIELD.full_name = ".OrderItemNO.currency"
tb.ORDERITEMNO_CURRENCY_FIELD.number = 7
tb.ORDERITEMNO_CURRENCY_FIELD.index = 6
tb.ORDERITEMNO_CURRENCY_FIELD.label = 1
tb.ORDERITEMNO_CURRENCY_FIELD.has_default_value = false
tb.ORDERITEMNO_CURRENCY_FIELD.default_value = 0
tb.ORDERITEMNO_CURRENCY_FIELD.type = 5
tb.ORDERITEMNO_CURRENCY_FIELD.cpp_type = 1

tb.ORDERITEMNO_ENDWAITING_FIELD.name = "endWaiting"
tb.ORDERITEMNO_ENDWAITING_FIELD.full_name = ".OrderItemNO.endWaiting"
tb.ORDERITEMNO_ENDWAITING_FIELD.number = 8
tb.ORDERITEMNO_ENDWAITING_FIELD.index = 7
tb.ORDERITEMNO_ENDWAITING_FIELD.label = 1
tb.ORDERITEMNO_ENDWAITING_FIELD.has_default_value = false
tb.ORDERITEMNO_ENDWAITING_FIELD.default_value = 0
tb.ORDERITEMNO_ENDWAITING_FIELD.type = 5
tb.ORDERITEMNO_ENDWAITING_FIELD.cpp_type = 1

tb.ORDERITEMNO_POOLID_FIELD.name = "poolId"
tb.ORDERITEMNO_POOLID_FIELD.full_name = ".OrderItemNO.poolId"
tb.ORDERITEMNO_POOLID_FIELD.number = 9
tb.ORDERITEMNO_POOLID_FIELD.index = 8
tb.ORDERITEMNO_POOLID_FIELD.label = 1
tb.ORDERITEMNO_POOLID_FIELD.has_default_value = false
tb.ORDERITEMNO_POOLID_FIELD.default_value = 0
tb.ORDERITEMNO_POOLID_FIELD.type = 5
tb.ORDERITEMNO_POOLID_FIELD.cpp_type = 1

tb.ORDERITEMNO_COMMITTERINFO_FIELD.name = "committerInfo"
tb.ORDERITEMNO_COMMITTERINFO_FIELD.full_name = ".OrderItemNO.committerInfo"
tb.ORDERITEMNO_COMMITTERINFO_FIELD.number = 10
tb.ORDERITEMNO_COMMITTERINFO_FIELD.index = 9
tb.ORDERITEMNO_COMMITTERINFO_FIELD.label = 1
tb.ORDERITEMNO_COMMITTERINFO_FIELD.has_default_value = false
tb.ORDERITEMNO_COMMITTERINFO_FIELD.default_value = nil
tb.ORDERITEMNO_COMMITTERINFO_FIELD.message_type = COMMITTERINFONO_MSG
tb.ORDERITEMNO_COMMITTERINFO_FIELD.type = 11
tb.ORDERITEMNO_COMMITTERINFO_FIELD.cpp_type = 10

tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.name = "roleSimpleInfo"
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.full_name = ".OrderItemNO.roleSimpleInfo"
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.number = 11
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.index = 10
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.label = 1
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.has_default_value = false
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.default_value = nil
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.message_type = USEREXTENSION_PB.ROLESIMPLEINFONO_MSG
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.type = 11
tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD.cpp_type = 10

ORDERITEMNO_MSG.name = "OrderItemNO"
ORDERITEMNO_MSG.full_name = ".OrderItemNO"
ORDERITEMNO_MSG.filename = "OrderExtension"
ORDERITEMNO_MSG.nested_types = {}
ORDERITEMNO_MSG.enum_types = {}
ORDERITEMNO_MSG.fields = {tb.ORDERITEMNO_NPC_FIELD, tb.ORDERITEMNO_ID_FIELD, tb.ORDERITEMNO_STATE_FIELD, tb.ORDERITEMNO_CREATETIME_FIELD, tb.ORDERITEMNO_ITEMID_FIELD, tb.ORDERITEMNO_ITEMCOUNT_FIELD, tb.ORDERITEMNO_CURRENCY_FIELD, tb.ORDERITEMNO_ENDWAITING_FIELD, tb.ORDERITEMNO_POOLID_FIELD, tb.ORDERITEMNO_COMMITTERINFO_FIELD, tb.ORDERITEMNO_ROLESIMPLEINFO_FIELD}
ORDERITEMNO_MSG.is_extendable = false
ORDERITEMNO_MSG.extensions = {}
tb.COMMITORDERREQUEST_TYPE_FIELD.name = "type"
tb.COMMITORDERREQUEST_TYPE_FIELD.full_name = ".CommitOrderRequest.type"
tb.COMMITORDERREQUEST_TYPE_FIELD.number = 1
tb.COMMITORDERREQUEST_TYPE_FIELD.index = 0
tb.COMMITORDERREQUEST_TYPE_FIELD.label = 1
tb.COMMITORDERREQUEST_TYPE_FIELD.has_default_value = false
tb.COMMITORDERREQUEST_TYPE_FIELD.default_value = 0
tb.COMMITORDERREQUEST_TYPE_FIELD.type = 5
tb.COMMITORDERREQUEST_TYPE_FIELD.cpp_type = 1

tb.COMMITORDERREQUEST_ID_FIELD.name = "id"
tb.COMMITORDERREQUEST_ID_FIELD.full_name = ".CommitOrderRequest.id"
tb.COMMITORDERREQUEST_ID_FIELD.number = 2
tb.COMMITORDERREQUEST_ID_FIELD.index = 1
tb.COMMITORDERREQUEST_ID_FIELD.label = 1
tb.COMMITORDERREQUEST_ID_FIELD.has_default_value = false
tb.COMMITORDERREQUEST_ID_FIELD.default_value = ""
tb.COMMITORDERREQUEST_ID_FIELD.type = 9
tb.COMMITORDERREQUEST_ID_FIELD.cpp_type = 9

tb.COMMITORDERREQUEST_CONTENT_FIELD.name = "content"
tb.COMMITORDERREQUEST_CONTENT_FIELD.full_name = ".CommitOrderRequest.content"
tb.COMMITORDERREQUEST_CONTENT_FIELD.number = 3
tb.COMMITORDERREQUEST_CONTENT_FIELD.index = 2
tb.COMMITORDERREQUEST_CONTENT_FIELD.label = 1
tb.COMMITORDERREQUEST_CONTENT_FIELD.has_default_value = false
tb.COMMITORDERREQUEST_CONTENT_FIELD.default_value = ""
tb.COMMITORDERREQUEST_CONTENT_FIELD.type = 9
tb.COMMITORDERREQUEST_CONTENT_FIELD.cpp_type = 9

tb.COMMITORDERREQUEST_USECOUPON_FIELD.name = "useCoupon"
tb.COMMITORDERREQUEST_USECOUPON_FIELD.full_name = ".CommitOrderRequest.useCoupon"
tb.COMMITORDERREQUEST_USECOUPON_FIELD.number = 4
tb.COMMITORDERREQUEST_USECOUPON_FIELD.index = 3
tb.COMMITORDERREQUEST_USECOUPON_FIELD.label = 1
tb.COMMITORDERREQUEST_USECOUPON_FIELD.has_default_value = false
tb.COMMITORDERREQUEST_USECOUPON_FIELD.default_value = false
tb.COMMITORDERREQUEST_USECOUPON_FIELD.type = 8
tb.COMMITORDERREQUEST_USECOUPON_FIELD.cpp_type = 7

tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.name = "commitCount"
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.full_name = ".CommitOrderRequest.commitCount"
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.number = 5
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.index = 4
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.label = 1
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.has_default_value = false
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.default_value = 0
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.type = 5
tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD.cpp_type = 1

COMMITORDERREQUEST_MSG.name = "CommitOrderRequest"
COMMITORDERREQUEST_MSG.full_name = ".CommitOrderRequest"
COMMITORDERREQUEST_MSG.filename = "OrderExtension"
COMMITORDERREQUEST_MSG.nested_types = {}
COMMITORDERREQUEST_MSG.enum_types = {}
COMMITORDERREQUEST_MSG.fields = {tb.COMMITORDERREQUEST_TYPE_FIELD, tb.COMMITORDERREQUEST_ID_FIELD, tb.COMMITORDERREQUEST_CONTENT_FIELD, tb.COMMITORDERREQUEST_USECOUPON_FIELD, tb.COMMITORDERREQUEST_COMMITCOUNT_FIELD}
COMMITORDERREQUEST_MSG.is_extendable = false
COMMITORDERREQUEST_MSG.extensions = {}
tb.COMMITORDERREPLY_NEWORDER_FIELD.name = "newOrder"
tb.COMMITORDERREPLY_NEWORDER_FIELD.full_name = ".CommitOrderReply.newOrder"
tb.COMMITORDERREPLY_NEWORDER_FIELD.number = 1
tb.COMMITORDERREPLY_NEWORDER_FIELD.index = 0
tb.COMMITORDERREPLY_NEWORDER_FIELD.label = 1
tb.COMMITORDERREPLY_NEWORDER_FIELD.has_default_value = false
tb.COMMITORDERREPLY_NEWORDER_FIELD.default_value = nil
tb.COMMITORDERREPLY_NEWORDER_FIELD.message_type = ORDERITEMNO_MSG
tb.COMMITORDERREPLY_NEWORDER_FIELD.type = 11
tb.COMMITORDERREPLY_NEWORDER_FIELD.cpp_type = 10

tb.COMMITORDERREPLY_CHANGESETID_FIELD.name = "changeSetId"
tb.COMMITORDERREPLY_CHANGESETID_FIELD.full_name = ".CommitOrderReply.changeSetId"
tb.COMMITORDERREPLY_CHANGESETID_FIELD.number = 2
tb.COMMITORDERREPLY_CHANGESETID_FIELD.index = 1
tb.COMMITORDERREPLY_CHANGESETID_FIELD.label = 1
tb.COMMITORDERREPLY_CHANGESETID_FIELD.has_default_value = false
tb.COMMITORDERREPLY_CHANGESETID_FIELD.default_value = 0
tb.COMMITORDERREPLY_CHANGESETID_FIELD.type = 5
tb.COMMITORDERREPLY_CHANGESETID_FIELD.cpp_type = 1

tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.name = "diamondOrder"
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.full_name = ".CommitOrderReply.diamondOrder"
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.number = 3
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.index = 2
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.label = 1
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.has_default_value = false
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.default_value = nil
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.message_type = ORDERITEMNO_MSG
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.type = 11
tb.COMMITORDERREPLY_DIAMONDORDER_FIELD.cpp_type = 10

COMMITORDERREPLY_MSG.name = "CommitOrderReply"
COMMITORDERREPLY_MSG.full_name = ".CommitOrderReply"
COMMITORDERREPLY_MSG.filename = "OrderExtension"
COMMITORDERREPLY_MSG.nested_types = {}
COMMITORDERREPLY_MSG.enum_types = {}
COMMITORDERREPLY_MSG.fields = {tb.COMMITORDERREPLY_NEWORDER_FIELD, tb.COMMITORDERREPLY_CHANGESETID_FIELD, tb.COMMITORDERREPLY_DIAMONDORDER_FIELD}
COMMITORDERREPLY_MSG.is_extendable = false
COMMITORDERREPLY_MSG.extensions = {}
tb.FINISHORDERREPLY_COMMITTER_FIELD.name = "committer"
tb.FINISHORDERREPLY_COMMITTER_FIELD.full_name = ".FinishOrderReply.committer"
tb.FINISHORDERREPLY_COMMITTER_FIELD.number = 1
tb.FINISHORDERREPLY_COMMITTER_FIELD.index = 0
tb.FINISHORDERREPLY_COMMITTER_FIELD.label = 1
tb.FINISHORDERREPLY_COMMITTER_FIELD.has_default_value = false
tb.FINISHORDERREPLY_COMMITTER_FIELD.default_value = nil
tb.FINISHORDERREPLY_COMMITTER_FIELD.message_type = USEREXTENSION_PB.ROLEINFONO_MSG
tb.FINISHORDERREPLY_COMMITTER_FIELD.type = 11
tb.FINISHORDERREPLY_COMMITTER_FIELD.cpp_type = 10

tb.FINISHORDERREPLY_FINISHTIMES_FIELD.name = "finishTimes"
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.full_name = ".FinishOrderReply.finishTimes"
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.number = 2
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.index = 1
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.label = 1
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.has_default_value = false
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.default_value = 0
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.type = 5
tb.FINISHORDERREPLY_FINISHTIMES_FIELD.cpp_type = 1

FINISHORDERREPLY_MSG.name = "FinishOrderReply"
FINISHORDERREPLY_MSG.full_name = ".FinishOrderReply"
FINISHORDERREPLY_MSG.filename = "OrderExtension"
FINISHORDERREPLY_MSG.nested_types = {}
FINISHORDERREPLY_MSG.enum_types = {}
FINISHORDERREPLY_MSG.fields = {tb.FINISHORDERREPLY_COMMITTER_FIELD, tb.FINISHORDERREPLY_FINISHTIMES_FIELD}
FINISHORDERREPLY_MSG.is_extendable = false
FINISHORDERREPLY_MSG.extensions = {}
tb.CREATEORDERREQUEST_ITEMID_FIELD.name = "itemId"
tb.CREATEORDERREQUEST_ITEMID_FIELD.full_name = ".CreateOrderRequest.itemId"
tb.CREATEORDERREQUEST_ITEMID_FIELD.number = 1
tb.CREATEORDERREQUEST_ITEMID_FIELD.index = 0
tb.CREATEORDERREQUEST_ITEMID_FIELD.label = 1
tb.CREATEORDERREQUEST_ITEMID_FIELD.has_default_value = false
tb.CREATEORDERREQUEST_ITEMID_FIELD.default_value = 0
tb.CREATEORDERREQUEST_ITEMID_FIELD.type = 5
tb.CREATEORDERREQUEST_ITEMID_FIELD.cpp_type = 1

tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.name = "itemCount"
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.full_name = ".CreateOrderRequest.itemCount"
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.number = 2
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.index = 1
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.label = 1
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.has_default_value = false
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.default_value = 0
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.type = 5
tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD.cpp_type = 1

tb.CREATEORDERREQUEST_CURRENCY_FIELD.name = "currency"
tb.CREATEORDERREQUEST_CURRENCY_FIELD.full_name = ".CreateOrderRequest.currency"
tb.CREATEORDERREQUEST_CURRENCY_FIELD.number = 3
tb.CREATEORDERREQUEST_CURRENCY_FIELD.index = 2
tb.CREATEORDERREQUEST_CURRENCY_FIELD.label = 1
tb.CREATEORDERREQUEST_CURRENCY_FIELD.has_default_value = false
tb.CREATEORDERREQUEST_CURRENCY_FIELD.default_value = 0
tb.CREATEORDERREQUEST_CURRENCY_FIELD.type = 5
tb.CREATEORDERREQUEST_CURRENCY_FIELD.cpp_type = 1

CREATEORDERREQUEST_MSG.name = "CreateOrderRequest"
CREATEORDERREQUEST_MSG.full_name = ".CreateOrderRequest"
CREATEORDERREQUEST_MSG.filename = "OrderExtension"
CREATEORDERREQUEST_MSG.nested_types = {}
CREATEORDERREQUEST_MSG.enum_types = {}
CREATEORDERREQUEST_MSG.fields = {tb.CREATEORDERREQUEST_ITEMID_FIELD, tb.CREATEORDERREQUEST_ITEMCOUNT_FIELD, tb.CREATEORDERREQUEST_CURRENCY_FIELD}
CREATEORDERREQUEST_MSG.is_extendable = false
CREATEORDERREQUEST_MSG.extensions = {}
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.name = "newOrders"
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.full_name = ".RejectAllOrderReply.newOrders"
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.number = 1
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.index = 0
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.label = 3
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.has_default_value = false
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.default_value = {}
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.message_type = ORDERITEMNO_MSG
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.type = 11
tb.REJECTALLORDERREPLY_NEWORDERS_FIELD.cpp_type = 10

REJECTALLORDERREPLY_MSG.name = "RejectAllOrderReply"
REJECTALLORDERREPLY_MSG.full_name = ".RejectAllOrderReply"
REJECTALLORDERREPLY_MSG.filename = "OrderExtension"
REJECTALLORDERREPLY_MSG.nested_types = {}
REJECTALLORDERREPLY_MSG.enum_types = {}
REJECTALLORDERREPLY_MSG.fields = {tb.REJECTALLORDERREPLY_NEWORDERS_FIELD}
REJECTALLORDERREPLY_MSG.is_extendable = false
REJECTALLORDERREPLY_MSG.extensions = {}
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.name = "id"
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.full_name = ".ClearNpcOrderWaitingRequest.id"
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.number = 1
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.index = 0
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.label = 1
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.has_default_value = false
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.default_value = ""
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.type = 9
tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD.cpp_type = 9

CLEARNPCORDERWAITINGREQUEST_MSG.name = "ClearNpcOrderWaitingRequest"
CLEARNPCORDERWAITINGREQUEST_MSG.full_name = ".ClearNpcOrderWaitingRequest"
CLEARNPCORDERWAITINGREQUEST_MSG.filename = "OrderExtension"
CLEARNPCORDERWAITINGREQUEST_MSG.nested_types = {}
CLEARNPCORDERWAITINGREQUEST_MSG.enum_types = {}
CLEARNPCORDERWAITINGREQUEST_MSG.fields = {tb.CLEARNPCORDERWAITINGREQUEST_ID_FIELD}
CLEARNPCORDERWAITINGREQUEST_MSG.is_extendable = false
CLEARNPCORDERWAITINGREQUEST_MSG.extensions = {}
FINISHORDERREQUEST_MSG.name = "FinishOrderRequest"
FINISHORDERREQUEST_MSG.full_name = ".FinishOrderRequest"
FINISHORDERREQUEST_MSG.filename = "OrderExtension"
FINISHORDERREQUEST_MSG.nested_types = {}
FINISHORDERREQUEST_MSG.enum_types = {}
FINISHORDERREQUEST_MSG.fields = {}
FINISHORDERREQUEST_MSG.is_extendable = false
FINISHORDERREQUEST_MSG.extensions = {}
BUYCOMMITTIMESREQUEST_MSG.name = "BuyCommitTimesRequest"
BUYCOMMITTIMESREQUEST_MSG.full_name = ".BuyCommitTimesRequest"
BUYCOMMITTIMESREQUEST_MSG.filename = "OrderExtension"
BUYCOMMITTIMESREQUEST_MSG.nested_types = {}
BUYCOMMITTIMESREQUEST_MSG.enum_types = {}
BUYCOMMITTIMESREQUEST_MSG.fields = {}
BUYCOMMITTIMESREQUEST_MSG.is_extendable = false
BUYCOMMITTIMESREQUEST_MSG.extensions = {}
GETORDERINFOREQUEST_MSG.name = "GetOrderInfoRequest"
GETORDERINFOREQUEST_MSG.full_name = ".GetOrderInfoRequest"
GETORDERINFOREQUEST_MSG.filename = "OrderExtension"
GETORDERINFOREQUEST_MSG.nested_types = {}
GETORDERINFOREQUEST_MSG.enum_types = {}
GETORDERINFOREQUEST_MSG.fields = {}
GETORDERINFOREQUEST_MSG.is_extendable = false
GETORDERINFOREQUEST_MSG.extensions = {}
tb.COMMITTERINFONO_UID_FIELD.name = "uid"
tb.COMMITTERINFONO_UID_FIELD.full_name = ".CommitterInfoNO.uid"
tb.COMMITTERINFONO_UID_FIELD.number = 1
tb.COMMITTERINFONO_UID_FIELD.index = 0
tb.COMMITTERINFONO_UID_FIELD.label = 1
tb.COMMITTERINFONO_UID_FIELD.has_default_value = false
tb.COMMITTERINFONO_UID_FIELD.default_value = ""
tb.COMMITTERINFONO_UID_FIELD.type = 9
tb.COMMITTERINFONO_UID_FIELD.cpp_type = 9

tb.COMMITTERINFONO_NICKNAME_FIELD.name = "nickname"
tb.COMMITTERINFONO_NICKNAME_FIELD.full_name = ".CommitterInfoNO.nickname"
tb.COMMITTERINFONO_NICKNAME_FIELD.number = 2
tb.COMMITTERINFONO_NICKNAME_FIELD.index = 1
tb.COMMITTERINFONO_NICKNAME_FIELD.label = 1
tb.COMMITTERINFONO_NICKNAME_FIELD.has_default_value = false
tb.COMMITTERINFONO_NICKNAME_FIELD.default_value = ""
tb.COMMITTERINFONO_NICKNAME_FIELD.type = 9
tb.COMMITTERINFONO_NICKNAME_FIELD.cpp_type = 9

tb.COMMITTERINFONO_MODELID_FIELD.name = "modelId"
tb.COMMITTERINFONO_MODELID_FIELD.full_name = ".CommitterInfoNO.modelId"
tb.COMMITTERINFONO_MODELID_FIELD.number = 3
tb.COMMITTERINFONO_MODELID_FIELD.index = 2
tb.COMMITTERINFONO_MODELID_FIELD.label = 1
tb.COMMITTERINFONO_MODELID_FIELD.has_default_value = false
tb.COMMITTERINFONO_MODELID_FIELD.default_value = 0
tb.COMMITTERINFONO_MODELID_FIELD.type = 5
tb.COMMITTERINFONO_MODELID_FIELD.cpp_type = 1

tb.COMMITTERINFONO_CLOTHES_FIELD.name = "clothes"
tb.COMMITTERINFONO_CLOTHES_FIELD.full_name = ".CommitterInfoNO.clothes"
tb.COMMITTERINFONO_CLOTHES_FIELD.number = 4
tb.COMMITTERINFONO_CLOTHES_FIELD.index = 3
tb.COMMITTERINFONO_CLOTHES_FIELD.label = 3
tb.COMMITTERINFONO_CLOTHES_FIELD.has_default_value = false
tb.COMMITTERINFONO_CLOTHES_FIELD.default_value = {}
tb.COMMITTERINFONO_CLOTHES_FIELD.type = 5
tb.COMMITTERINFONO_CLOTHES_FIELD.cpp_type = 1

tb.COMMITTERINFONO_CONTENT_FIELD.name = "content"
tb.COMMITTERINFONO_CONTENT_FIELD.full_name = ".CommitterInfoNO.content"
tb.COMMITTERINFONO_CONTENT_FIELD.number = 5
tb.COMMITTERINFONO_CONTENT_FIELD.index = 4
tb.COMMITTERINFONO_CONTENT_FIELD.label = 1
tb.COMMITTERINFONO_CONTENT_FIELD.has_default_value = false
tb.COMMITTERINFONO_CONTENT_FIELD.default_value = ""
tb.COMMITTERINFONO_CONTENT_FIELD.type = 9
tb.COMMITTERINFONO_CONTENT_FIELD.cpp_type = 9

COMMITTERINFONO_MSG.name = "CommitterInfoNO"
COMMITTERINFONO_MSG.full_name = ".CommitterInfoNO"
COMMITTERINFONO_MSG.filename = "OrderExtension"
COMMITTERINFONO_MSG.nested_types = {}
COMMITTERINFONO_MSG.enum_types = {}
COMMITTERINFONO_MSG.fields = {tb.COMMITTERINFONO_UID_FIELD, tb.COMMITTERINFONO_NICKNAME_FIELD, tb.COMMITTERINFONO_MODELID_FIELD, tb.COMMITTERINFONO_CLOTHES_FIELD, tb.COMMITTERINFONO_CONTENT_FIELD}
COMMITTERINFONO_MSG.is_extendable = false
COMMITTERINFONO_MSG.extensions = {}
REJECTALLORDERREQUEST_MSG.name = "RejectAllOrderRequest"
REJECTALLORDERREQUEST_MSG.full_name = ".RejectAllOrderRequest"
REJECTALLORDERREQUEST_MSG.filename = "OrderExtension"
REJECTALLORDERREQUEST_MSG.nested_types = {}
REJECTALLORDERREQUEST_MSG.enum_types = {}
REJECTALLORDERREQUEST_MSG.fields = {}
REJECTALLORDERREQUEST_MSG.is_extendable = false
REJECTALLORDERREQUEST_MSG.extensions = {}
CREATEORDERREPLY_MSG.name = "CreateOrderReply"
CREATEORDERREPLY_MSG.full_name = ".CreateOrderReply"
CREATEORDERREPLY_MSG.filename = "OrderExtension"
CREATEORDERREPLY_MSG.nested_types = {}
CREATEORDERREPLY_MSG.enum_types = {}
CREATEORDERREPLY_MSG.fields = {}
CREATEORDERREPLY_MSG.is_extendable = false
CREATEORDERREPLY_MSG.extensions = {}
tb.GETORDERINFOREPLY_ORDERS_FIELD.name = "orders"
tb.GETORDERINFOREPLY_ORDERS_FIELD.full_name = ".GetOrderInfoReply.orders"
tb.GETORDERINFOREPLY_ORDERS_FIELD.number = 1
tb.GETORDERINFOREPLY_ORDERS_FIELD.index = 0
tb.GETORDERINFOREPLY_ORDERS_FIELD.label = 3
tb.GETORDERINFOREPLY_ORDERS_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_ORDERS_FIELD.default_value = {}
tb.GETORDERINFOREPLY_ORDERS_FIELD.message_type = ORDERITEMNO_MSG
tb.GETORDERINFOREPLY_ORDERS_FIELD.type = 11
tb.GETORDERINFOREPLY_ORDERS_FIELD.cpp_type = 10

tb.GETORDERINFOREPLY_MINE_FIELD.name = "mine"
tb.GETORDERINFOREPLY_MINE_FIELD.full_name = ".GetOrderInfoReply.mine"
tb.GETORDERINFOREPLY_MINE_FIELD.number = 2
tb.GETORDERINFOREPLY_MINE_FIELD.index = 1
tb.GETORDERINFOREPLY_MINE_FIELD.label = 1
tb.GETORDERINFOREPLY_MINE_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_MINE_FIELD.default_value = nil
tb.GETORDERINFOREPLY_MINE_FIELD.message_type = ORDERITEMNO_MSG
tb.GETORDERINFOREPLY_MINE_FIELD.type = 11
tb.GETORDERINFOREPLY_MINE_FIELD.cpp_type = 10

tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.name = "hadExpire"
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.full_name = ".GetOrderInfoReply.hadExpire"
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.number = 3
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.index = 2
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.label = 1
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.default_value = false
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.type = 8
tb.GETORDERINFOREPLY_HADEXPIRE_FIELD.cpp_type = 7

tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.name = "finishTimes"
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.full_name = ".GetOrderInfoReply.finishTimes"
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.number = 4
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.index = 3
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.label = 2
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.default_value = 0
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.type = 5
tb.GETORDERINFOREPLY_FINISHTIMES_FIELD.cpp_type = 1

tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.name = "commitRemainTimes"
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.full_name = ".GetOrderInfoReply.commitRemainTimes"
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.number = 5
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.index = 4
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.label = 2
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.default_value = 0
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.type = 5
tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD.cpp_type = 1

tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.name = "buyCommitTimes"
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.full_name = ".GetOrderInfoReply.buyCommitTimes"
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.number = 6
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.index = 5
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.label = 2
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.default_value = 0
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.type = 5
tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD.cpp_type = 1

tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.name = "lastCommitRecoverTime"
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.full_name = ".GetOrderInfoReply.lastCommitRecoverTime"
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.number = 7
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.index = 6
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.label = 1
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.default_value = 0
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.type = 5
tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD.cpp_type = 1

tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.name = "diamondOrder"
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.full_name = ".GetOrderInfoReply.diamondOrder"
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.number = 8
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.index = 7
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.label = 1
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.default_value = nil
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.message_type = ORDERITEMNO_MSG
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.type = 11
tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD.cpp_type = 10

tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.name = "diamondOrderOutDateTime"
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.full_name = ".GetOrderInfoReply.diamondOrderOutDateTime"
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.number = 9
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.index = 8
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.label = 1
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.has_default_value = false
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.default_value = 0
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.type = 5
tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD.cpp_type = 1

GETORDERINFOREPLY_MSG.name = "GetOrderInfoReply"
GETORDERINFOREPLY_MSG.full_name = ".GetOrderInfoReply"
GETORDERINFOREPLY_MSG.filename = "OrderExtension"
GETORDERINFOREPLY_MSG.nested_types = {}
GETORDERINFOREPLY_MSG.enum_types = {}
GETORDERINFOREPLY_MSG.fields = {tb.GETORDERINFOREPLY_ORDERS_FIELD, tb.GETORDERINFOREPLY_MINE_FIELD, tb.GETORDERINFOREPLY_HADEXPIRE_FIELD, tb.GETORDERINFOREPLY_FINISHTIMES_FIELD, tb.GETORDERINFOREPLY_COMMITREMAINTIMES_FIELD, tb.GETORDERINFOREPLY_BUYCOMMITTIMES_FIELD, tb.GETORDERINFOREPLY_LASTCOMMITRECOVERTIME_FIELD, tb.GETORDERINFOREPLY_DIAMONDORDER_FIELD, tb.GETORDERINFOREPLY_DIAMONDORDEROUTDATETIME_FIELD}
GETORDERINFOREPLY_MSG.is_extendable = false
GETORDERINFOREPLY_MSG.extensions = {}
tb.REJECTORDERREPLY_NEWORDER_FIELD.name = "newOrder"
tb.REJECTORDERREPLY_NEWORDER_FIELD.full_name = ".RejectOrderReply.newOrder"
tb.REJECTORDERREPLY_NEWORDER_FIELD.number = 1
tb.REJECTORDERREPLY_NEWORDER_FIELD.index = 0
tb.REJECTORDERREPLY_NEWORDER_FIELD.label = 1
tb.REJECTORDERREPLY_NEWORDER_FIELD.has_default_value = false
tb.REJECTORDERREPLY_NEWORDER_FIELD.default_value = nil
tb.REJECTORDERREPLY_NEWORDER_FIELD.message_type = ORDERITEMNO_MSG
tb.REJECTORDERREPLY_NEWORDER_FIELD.type = 11
tb.REJECTORDERREPLY_NEWORDER_FIELD.cpp_type = 10

tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.name = "diamondOrder"
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.full_name = ".RejectOrderReply.diamondOrder"
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.number = 2
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.index = 1
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.label = 1
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.has_default_value = false
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.default_value = nil
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.message_type = ORDERITEMNO_MSG
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.type = 11
tb.REJECTORDERREPLY_DIAMONDORDER_FIELD.cpp_type = 10

REJECTORDERREPLY_MSG.name = "RejectOrderReply"
REJECTORDERREPLY_MSG.full_name = ".RejectOrderReply"
REJECTORDERREPLY_MSG.filename = "OrderExtension"
REJECTORDERREPLY_MSG.nested_types = {}
REJECTORDERREPLY_MSG.enum_types = {}
REJECTORDERREPLY_MSG.fields = {tb.REJECTORDERREPLY_NEWORDER_FIELD, tb.REJECTORDERREPLY_DIAMONDORDER_FIELD}
REJECTORDERREPLY_MSG.is_extendable = false
REJECTORDERREPLY_MSG.extensions = {}
CANCELORDERREPLY_MSG.name = "CancelOrderReply"
CANCELORDERREPLY_MSG.full_name = ".CancelOrderReply"
CANCELORDERREPLY_MSG.filename = "OrderExtension"
CANCELORDERREPLY_MSG.nested_types = {}
CANCELORDERREPLY_MSG.enum_types = {}
CANCELORDERREPLY_MSG.fields = {}
CANCELORDERREPLY_MSG.is_extendable = false
CANCELORDERREPLY_MSG.extensions = {}
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.name = "newOrder"
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.full_name = ".ClearNpcOrderWaitingReply.newOrder"
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.number = 1
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.index = 0
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.label = 1
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.has_default_value = false
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.default_value = nil
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.message_type = ORDERITEMNO_MSG
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.type = 11
tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD.cpp_type = 10

CLEARNPCORDERWAITINGREPLY_MSG.name = "ClearNpcOrderWaitingReply"
CLEARNPCORDERWAITINGREPLY_MSG.full_name = ".ClearNpcOrderWaitingReply"
CLEARNPCORDERWAITINGREPLY_MSG.filename = "OrderExtension"
CLEARNPCORDERWAITINGREPLY_MSG.nested_types = {}
CLEARNPCORDERWAITINGREPLY_MSG.enum_types = {}
CLEARNPCORDERWAITINGREPLY_MSG.fields = {tb.CLEARNPCORDERWAITINGREPLY_NEWORDER_FIELD}
CLEARNPCORDERWAITINGREPLY_MSG.is_extendable = false
CLEARNPCORDERWAITINGREPLY_MSG.extensions = {}
CANCELORDERREQUEST_MSG.name = "CancelOrderRequest"
CANCELORDERREQUEST_MSG.full_name = ".CancelOrderRequest"
CANCELORDERREQUEST_MSG.filename = "OrderExtension"
CANCELORDERREQUEST_MSG.nested_types = {}
CANCELORDERREQUEST_MSG.enum_types = {}
CANCELORDERREQUEST_MSG.fields = {}
CANCELORDERREQUEST_MSG.is_extendable = false
CANCELORDERREQUEST_MSG.extensions = {}
BUYCOMMITTIMESREPLY_MSG.name = "BuyCommitTimesReply"
BUYCOMMITTIMESREPLY_MSG.full_name = ".BuyCommitTimesReply"
BUYCOMMITTIMESREPLY_MSG.filename = "OrderExtension"
BUYCOMMITTIMESREPLY_MSG.nested_types = {}
BUYCOMMITTIMESREPLY_MSG.enum_types = {}
BUYCOMMITTIMESREPLY_MSG.fields = {}
BUYCOMMITTIMESREPLY_MSG.is_extendable = false
BUYCOMMITTIMESREPLY_MSG.extensions = {}

BuyCommitTimesReply = protobuf.Message(BUYCOMMITTIMESREPLY_MSG)
BuyCommitTimesRequest = protobuf.Message(BUYCOMMITTIMESREQUEST_MSG)
CancelOrderReply = protobuf.Message(CANCELORDERREPLY_MSG)
CancelOrderRequest = protobuf.Message(CANCELORDERREQUEST_MSG)
ClearNpcOrderWaitingReply = protobuf.Message(CLEARNPCORDERWAITINGREPLY_MSG)
ClearNpcOrderWaitingRequest = protobuf.Message(CLEARNPCORDERWAITINGREQUEST_MSG)
CommitOrderReply = protobuf.Message(COMMITORDERREPLY_MSG)
CommitOrderRequest = protobuf.Message(COMMITORDERREQUEST_MSG)
CommitterInfoNO = protobuf.Message(COMMITTERINFONO_MSG)
CreateOrderReply = protobuf.Message(CREATEORDERREPLY_MSG)
CreateOrderRequest = protobuf.Message(CREATEORDERREQUEST_MSG)
FinishOrderReply = protobuf.Message(FINISHORDERREPLY_MSG)
FinishOrderRequest = protobuf.Message(FINISHORDERREQUEST_MSG)
GetOrderInfoReply = protobuf.Message(GETORDERINFOREPLY_MSG)
GetOrderInfoRequest = protobuf.Message(GETORDERINFOREQUEST_MSG)
OrderItemNO = protobuf.Message(ORDERITEMNO_MSG)
RejectAllOrderReply = protobuf.Message(REJECTALLORDERREPLY_MSG)
RejectAllOrderRequest = protobuf.Message(REJECTALLORDERREQUEST_MSG)
RejectOrderReply = protobuf.Message(REJECTORDERREPLY_MSG)
RejectOrderRequest = protobuf.Message(REJECTORDERREQUEST_MSG)

return _G["logic.proto.OrderExtension_pb"]
