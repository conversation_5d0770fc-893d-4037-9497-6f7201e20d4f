module("logic.extensions.turntable.subtable.dragon.view.DragonTurnTableRedBagViewPresentor", package.seeall)
local DragonTurnTableRedBagViewPresentor = class("DragonTurnTableRedBagViewPresentor", ViewPresentor)

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function DragonTurnTableRedBagViewPresentor:buildViews()
    return {DragonTurnTableRedBagView.New()}
end

--- 配置view需要的资源列表
function DragonTurnTableRedBagViewPresentor:dependWhatResources()
    local urls = {"ui/planetturntable/planetturntablehongbaoview.prefab"}
    return urls
end

--- 配置view所在的ui层
function DragonTurnTableRedBagViewPresentor:attachToWhichRoot()
    return ViewRootType.Popup
end

return DragonTurnTableRedBagViewPresentor
