module("logic.scene.unit.playeraction.PlayerCushionCreateHandler", package.seeall)

local PlayerCushionCreateHandler = class("PlayerCushionCreateHandler", PlayerActionHandlerBase)

PlayerCushionCreateHandler.CushionUnitNamePrefix = "Cushion_"

function PlayerCushionCreateHandler:onStop()
	if self.scene and self.unit.id == UserInfo.userId then
		self.scene:setOverrideClick(nil)
	end
	if self.cushion == nil then return end
	self.unitFactory:removeUnit(SceneUnitType.CushionUnit, PlayerCushionCreateHandler.CushionUnitNamePrefix .. self.unit.id)
	self.cushion = nil
	self.unitFactory = nil
	self.scene = nil
end

function PlayerCushionCreateHandler:onStart()
	self.scene = SceneManager.instance:getCurScene()
	self.unitFactory = self.scene.unitFactory
	self.cushionId = PoseConfig.getPoseByItemId(tonumber(self.info.params[1])).id
	self.dir = tonumber(self.info.params[2])
	
	local unitId = PlayerCushionCreateHandler.CushionUnitNamePrefix .. self.unit.id
	self.cushion = self.unitFactory:addUnit(SceneUnitType.CushionUnit, {id = unitId})
	local x, y = self.unit:getPos()
	self.cushion:setPos(x, y)
	self.cushion:setView(self.cushionId, self.dir, self._onLoadUnit, self, self.info.params[3])
	for i,param in ipairs(self.info.params) do
		print("PlayerCushionCreateHandler:params .....", param)
	end
	if self.unit.id == UserInfo.userId and not string.nilorempty(self.info.params[3]) then
		ChatFacade.instance:enterAndShowTeaParty(self.info.params[3], SceneCushionAction.teapartyPwd)
		SceneCushionAction.teapartyPwd = nil
	end
end

function PlayerCushionCreateHandler:getSitPos(userId)
	if string.nilorempty(userId) then userId = UserInfo.userId end
	--从第五个参数开始记录每个位置坐着的玩家
	for i = 5, #self.info.params do
		if self.info.params[i] == userId then
			return i - 4
		end
	end
	return 0
end

function PlayerCushionCreateHandler:hasPlayer(sitPos)
	return not string.nilorempty(self.info.params[sitPos + 4])
end

function PlayerCushionCreateHandler:onUpdate()
	local nextPos = self:getSitPos(self.unit.id)
	if self.currentPos == nextPos then return end
	self.currentPos = nextPos
	self.cushion:setPlayer(self.unit, nextPos)
end

function PlayerCushionCreateHandler:_onLoadUnit()
	if self.cushion == nil then return end
	self.currentPos = self:getSitPos(self.unit.id)
	self.cushion:setPlayer(self.unit, self.currentPos)
	if self.unit.id == UserInfo.userId then
		self.scene:setOverrideClick(self.onClickEmpty, self)
	end
	self.cushion:addClickPosListener(self._onClickSitPos, self)
end

function PlayerCushionCreateHandler:onClickEmpty()
	DialogHelper.showConfirmDlg(lang("是否收回座椅？"), function(isOk)
		if isOk then
			SceneController.instance:stopAction()
		end
	end)
end

function PlayerCushionCreateHandler:_onClickSitPos(x, y, sitPos)
	if self:hasPlayer(sitPos) then return end
	local isJoin = not self:_meIsInCushion()
	local teapartyId = self.info.params[3]
	local teapartyPwd = self.info.params[4]
	if isJoin then
		if string.nilorempty(teapartyId) or tonumber(teapartyPwd) == 0 then
			SceneController.instance:startSitCushion(x, y, self.cushionId, sitPos, self.unit.id, self.dir, false, teapartyId, "")
		else
			ViewMgr.instance:open("ChatTeaPartyEnterView", function(pwd)
				SceneController.instance:startSitCushion(x, y, self.cushionId, sitPos, self.unit.id, self.dir, false, teapartyId, pwd)
			end)
		end
	else
		local serverParams = {self.unit.id, sitPos - 1}
		SceneAgent.instance:sendSceneShareRequest(PlayerActionType.JoinCushion.typeId, serverParams, function(status)
			if status ~= 0 then
				DialogHelper.showErrorMsg(status)
			end
		end)
	end
end

function PlayerCushionCreateHandler:_meIsInCushion()
	return self:getSitPos() > 0
end

return PlayerCushionCreateHandler 