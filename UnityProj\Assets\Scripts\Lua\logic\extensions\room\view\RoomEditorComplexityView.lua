module("logic.extensions.room.view.RoomEditorComplexityView", package.seeall)

local RoomEditorComplexityView = class("RoomEditorComplexityView", ViewComponent)

function RoomEditorComplexityView:ctor(viewPath)
	RoomEditorComplexityView.super.ctor(self)
	self.viewPath = viewPath
end

function RoomEditorComplexityView:buildUI()
	self._complexityBar = self:getGo(self.viewPath .. "/complexityBar")
	self._complexityGO = self:getGo(self.viewPath .. "/complexityBar/complexityGO")
	self._complexityTips = self:getGo(self.viewPath .. "/complexityBar/complexityTips")
	
	self._txtComplexity = self:getText(self.viewPath .. "/complexityBar/txtComplexity")
	self._goGreenBar = self:getGo(self.viewPath .. "/complexityBar/SliderOrange/Handle")
	self._greenWidth = self._goGreenBar.transform.sizeDelta.x
	self._goRedBar = self:getGo(self.viewPath .. "/complexityBar/SliderRed/Handle")
	self._redWidth = self._goGreenBar.transform.sizeDelta.x
	
	Framework.UIClickTrigger.Get(self._complexityBar):AddClickListener(self._onClickComplexity, self)
	Framework.UIGlobalTouchTrigger.Get(self._complexityGO):AddIgnoreTargetListener(self._hideComplexity, self)
end

function RoomEditorComplexityView:onEnter()
	goutil.setActive(self._complexityGO, false)
	
	local complexityEnable = HouseModel.instance:getRoomParams(RoomParams.Editor_Complexity_Enable)
	goutil.setActive(self._complexityBar, complexityEnable)
	
	local goldBarEnable = HouseModel.instance:getRoomParams(RoomParams.Editor_Gold_Enable)
	goutil.setActive(self:getGo(self.viewPath .. "/barCoin"), goldBarEnable)
	self:_resetComplexity()
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnEditFurnitureChange, self._resetComplexity, self)
end

function RoomEditorComplexityView:onExit()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnEditFurnitureChange, self._resetComplexity, self)
end

function RoomEditorComplexityView:_resetComplexity()
	local curCount = ComplexityHelper.getEditComplexity()
	local maxCount = ComplexityHelper.getMaxComplexity()
	local percent = math.min(curCount, maxCount) / maxCount
	local complexity = math.floor(percent * 100)
	
	tfutil.SetSizeDeltaX(self._goGreenBar, percent * self._greenWidth)
	tfutil.SetSizeDeltaX(self._goRedBar, percent * self._redWidth)
	goutil.setActive(self._goGreenBar, complexity < 95)
	goutil.setActive(self._goRedBar, complexity >= 95)
	
	goutil.setActive(self._complexityTips, complexity >= 95)
	self._txtComplexity.text = complexity .. "%"
end

function RoomEditorComplexityView:_onClickComplexity()
	goutil.setActive(self._complexityGO, true)
end

function RoomEditorComplexityView:_hideComplexity()
	goutil.setActive(self._complexityGO, false)
end

return RoomEditorComplexityView 