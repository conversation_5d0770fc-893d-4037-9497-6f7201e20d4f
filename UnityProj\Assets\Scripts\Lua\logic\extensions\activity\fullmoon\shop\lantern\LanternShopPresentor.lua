module("logic.extensions.activity.fullmoon.shop.lantern.LanternShopPresentor", package.seeall)
---@class LanternShopPresentor
local LanternShopPresentor = class("LanternShopPresentor", CommonSmallShopPresentor)
LanternShopPresentor.item = "ui/middleact/middleactstoreitem.prefab"
--- 配置view需要的资源列表

function LanternShopPresentor:dependWhatResources()
    self:initSetting()
    return {"ui/middleact/middleactstoreview.prefab", self.setting.itemUrl}
end

function LanternShopPresentor:initSetting()
    self.setting = {}
    self.setting.activityId = DreamBoxFacade.shopActivityId_1
    self.setting.tab2GroceryId = GroceryConfig.getTabShopIds(self.setting.activityId)
    self.setting.currencyitems = {{"goldBar", GroceryConfig.getShopCurrency(self.setting.activityId)[1]}}
    self.setting.layoutParams = {kScrollDirV, 153, 211, 16.1, 7, 4}
    self.setting.viewName = "LanternShop"
    self.setting.hasNpcModel = false
    self.setting.itemUrl = LanternShopPresentor.item
    self.setting.npcDirection = UnitDirection.RightDown
    self.setting.npcPath = "imgGroup/imgNpc"
end

function LanternShopPresentor:buildViews()
    self.itemHelper = ItemChangeHelper.New()
    local viewList = {
        CommonSmallShopView.New(),
        LanternShopListView.New(self.setting.itemUrl, self.setting.layoutParams, LanternShopCell),
        ActivityEndTimeUltimateComp.New(self.setting.activityId, "txtTime"),
        self.itemHelper
    }
    if self.setting.hasNpcModel then
        self:buildNpc()
        table.insert(viewList, self.npcModel)
        table.insert(viewList, self.talkBubble)
    end
    -- 3.1.2抽出语言表key
    self.setting.tabKey = lang("小主题通用活动商店页签")
    return viewList
end

return LanternShopPresentor
