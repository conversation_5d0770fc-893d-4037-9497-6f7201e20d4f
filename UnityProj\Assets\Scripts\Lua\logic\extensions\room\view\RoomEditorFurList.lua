module("logic.extensions.room.view.RoomEditorFurList", package.seeall)
local RoomEditorFurList = class("RoomEditorFurList", RoomEditorFurListBase)

function RoomEditorFurList:_checkEnableAreas(furniture)
	return furniture:checkInOurDoorArea(self._viewPresentor:getInOutDoorFurType())
end

function RoomEditorFurList:_checkEnablePart(furniture)
	return furniture:getPart():checkPartFilter(self._viewPresentor:getInOutDoorPartType())
end

function RoomEditorFurList:_checkFasionRace(furniture)
	local isInRace = SceneManager.instance:getCurSceneId() == 25
	return not isInRace or not furniture.cantUseInMatch
end

function RoomEditorFurList:_filterByEnableAreas(allData)
	local result = {}
	local define
	for _, item in pairs(allData) do
		define = item:getDefine()
		if self:_checkEnableAreas(define) and self:_checkEnablePart(define) and self:_checkFasionRace(define) then
			table.insert(result, item)
		end
	end
	return result
end
function RoomEditorFurList:changeListView(isLower)
	RoomEditorFurList.super.changeListView(self, isLower)
	if self._focusWhenChange ~= false and self._focusWhenChange ~= nil then
		self._listView:focusCell(self._focusWhenChange - 1)
		self._focusWhenChange = nil
	end
end

function RoomEditorFurList:buildUI()
	self._globalTrigger = PjAobi.GlobalTouchTrigger.Get(self.mainGO)
	self._globalTrigger:SetIsIgnoreUIThrough(true)
	RoomEditorFurList.super.buildUI(self)
	self._stage = SceneManager.instance:getCurScene().stage
	
	self._btnSave = self:getBtn("horRight/btnSave")
	self._btnCancel = self:getBtn("horRight/btnCancel")
	self._btnSearch = self:getGo("verBottom/buttonGroup/btnSearch")
	self._btnSort = self:getGo("verBottom/buttonGroup/btnSort")
	self._btnType = self:getGo("verBottom/buttonGroup/btnType")
end

function RoomEditorFurList:bindEvents()
	RoomEditorFurList.super.bindEvents()
	self._btnSave:AddClickListener(self._onClickSave, self)
	self._btnCancel:AddClickListener(self._onClickCancel, self)
end

function RoomEditorFurList:unbindEvents()
	self._btnSave:RemoveClickListener()
	self._btnCancel:RemoveClickListener()
end

function RoomEditorFurList:_onClickSave()
	GlobalDispatcher:dispatch(GlobalNotify.OnEditFurnitureSave)
	if RoomEdit.instance:tryOkCurEditFurniture(false) then
		RoomController.instance:localNotify(RoomNotifyName.SaveEditFurniture)
	end
end

function RoomEditorFurList:_onClickCancel()
	RoomController.instance:localNotify(RoomNotifyName.CancelOpFurniture)
	RoomController.instance:localNotify(RoomNotifyName.ResetEditFurniture)
end

function RoomEditorFurList:onEnter()
	RoomEditorFurList.super.onEnter(self)
	self._filterTags = {}
	self._mainCamera = CameraTargetMgr.instance:getMainCameraTarget():getCamera()
	self._furComp = SceneManager.instance:getCurScene().unitFactory:getRoomSuite():getComponent(RoomCompFurniture)
	self:_setItemChangeListener(true)
end

function RoomEditorFurList:onExit()
	RoomEditorFurList.super.onExit(self)
	self._listView._listModel:clear()
	self._searchName = nil
	self._allMyFurniture = nil
	self:_setItemChangeListener(false)
	self:localNotify(RoomNotifyName.TryHideHouse, true)
end

function RoomEditorFurList:_setItemChangeListener(add)
	if add then
		RoomController.instance:registerLocalNotify(RoomNotifyName.OnEditItemNumChange, self._itemNumChange, self)
		RoomController.instance:registerLocalNotify(RoomNotifyName.OnResetEditFurniture, self.resetMoList, self)
		GlobalDispatcher:addListener(GlobalNotify.ItemChange, self.resetMoList, self)
	else
		GlobalDispatcher:removeListener(GlobalNotify.ItemChange, self.resetMoList, self)
		RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnEditItemNumChange, self._itemNumChange, self)
		RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnResetEditFurniture, self.resetMoList, self)
	end
end

function RoomEditorFurList:resetMoList(resetView)
	if self._searchType == RoomEditorSearcher.SearchType.Bag then
		self._allMyFurniture = self:_filterByEnableAreas(RoomEditModel.instance:getMyItems())
	elseif self._searchType == RoomEditorSearcher.SearchType.Put then
		self._allMyFurniture = {}
		local allMo = RoomEditFurnitureModel.instance:getEditFurnitureList()
		local unit
		for i, mo in ipairs(allMo) do
			unit = self._furComp:getFurUnitByUniqueId(mo.uniqueId)
			if unit and unit.handler and unit.handler:canEdit() and unit.handler:canShowInfo() then
				table.insert(self._allMyFurniture, mo)
			end
		end
	end
	if resetView and self._allMyFurniture then
		local result = self._subType:filterByPart(self._allMyFurniture)
		result = self:_filterByPaper(self._subType, result)
		result = self:_filterBySearchName(self._searchName, result)
		result = self._viewPresentor.sorter:sortData(result)
		if not RoomConfig.isSurfaceType(self._subType.id) then
			result = self:_filterByTags(result)
		end
		self._listView._listModel:setMoList(result)
		self._listView.txtNoFurniture.gameObject:SetActive(#result == 0)
	end
end

function RoomEditorFurList:changeSearchType(searchType, resetData)
	self._searchType = searchType
	if resetData then
		self:resetMoList(false)
		self:onSelectSubType(self._subType, true)
	end
end

function RoomEditorFurList:changeFilterTags(filteyTags)
	self._filterTags = filteyTags
	self:onSelectSubType(self._subType, true)
end

function RoomEditorFurList:searchFurniture(searchName)
	if self._searchName == searchName then return end
	self._searchName = searchName
	self:onSelectSubType(self._subType, true)
end

function RoomEditorFurList:showNum()
	if RoomConfig.isSurfaceType(self._subType.id) then
		return false
	end
	return self._searchType == RoomEditorSearcher.SearchType.Bag
end

function RoomEditorFurList:onSelectSubType(subType, foucsFirst)
	RoomEditorFurList.super.onSelectSubType(self, subType, foucsFirst)
	if self._allMyFurniture == nil then return end
	local result = subType:filterByPart(self._allMyFurniture)
	result = self:_filterByPaper(subType, result)
	result = self._viewPresentor.sorter:sortData(result)
	result = self:_filterBySearchName(self._searchName, result)
	if not RoomConfig.isSurfaceType(subType.id) then
		result = self:_filterByTags(result)
	end
	self._listView._listModel:setMoList(result)
	self._listView.txtNoFurniture.gameObject:SetActive(#result == 0)
	if foucsFirst then
		self._listView:focusCell(0)
	end
	self._btnSort:SetActive(not RoomConfig.isSurfaceType(subType.id))
	self._btnSearch:SetActive(not RoomConfig.isSurfaceType(subType.id))
	self._btnType:SetActive(not RoomConfig.isSurfaceType(subType.id))
end

function RoomEditorFurList:_filterByTags(list)
	if self._filterTags == nil or #self._filterTags == 0 then return list end
	local result = {}
	local define
	for i, mo in ipairs(list) do
		if mo.getDefine then
			define = mo:getDefine()
		else
			define = mo
		end
		if define.tags then
			for j, tag in ipairs(define.tags) do
				if table.indexof(self._filterTags, tag) ~= false then
					table.insert(result, mo)
					break
				end
			end
		end
	end
	return result
end

function RoomEditorFurList:onSorterChange()
	self._listView.sortType = self._viewPresentor.sorter:getSortType()
	local result = self._viewPresentor.sorter:sortData(self._listView._listModel:getMoList())
	self._listView._listModel:setMoList(result)
	self._listView:focusCell(0)
end

function RoomEditorFurList:_filterByPaper(type, list)
	if RoomConfig.isPaperType(type.id) then
		return list
	else
		local result = {}
		for i = 1, #list do
			if not RoomConfig.isPaper(list[i].id) then
				table.insert(result, list[i])
			end
		end
		return result
	end
end

function RoomEditorFurList:_filterBySurface(type, list)
	if RoomConfig.isSurfaceType(type.id) then
		return list
	else
		local result = {}
		for i = 1, #list do
			if not RoomConfig.isSurface(list[i].id) then
				table.insert(result, list[i])
			end
		end
		return result
	end
end

function RoomEditorFurList:_filterBySearchName(input, list)
	if string.nilorempty(input) then return list end
	local result = {}
	local regularExpression = FriendTool.builRegular(self._searchName)
	local define
	for i, mo in ipairs(list) do
		if mo.getDefine then
			define = mo:getDefine()
		else
			define = mo
		end
		if string.find(string.upper(define.name), string.upper(regularExpression)) then
			table.insert(result, mo)
		end
	end
	return result
end

function RoomEditorFurList:_itemNumChange(id, num, listChange)
	if listChange or self._searchType == RoomEditorSearcher.SearchType.Put then
		self._dirty = true
		self:_checkAndResetList()
	end
end

function RoomEditorFurList:_checkAndResetList()
	if not self._isDraging and self._dirty then
		self:resetMoList(true)
		self._dirty = false
	end
end

function RoomEditorFurList:_onClickCell(mo)
	if self._searchType == RoomEditorSearcher.SearchType.Put then
		RoomController.instance:localNotify(RoomNotifyName.CancelOpFurniture)
		RoomController.instance:localNotify(RoomNotifyName.SelectEditFurnitureUnit, self._furComp:getFurUnitByUniqueId(mo.uniqueId), true)
	elseif not RoomTaskHelper.instance:preventPressFurnitureToPlace() then
		self:_placeNewFurniture(mo.id, nil, mo.isShare)
	end
	settimer(0, function()
		self._focusWhenChange = self._listView._listModel:getMoIndex(mo)
		self._viewPresentor.toggle:setBgLower()
	end, nil, false)
end

function RoomEditorFurList:_onBeginDragCell(mo, pos)
	if self._searchType ~= RoomEditorSearcher.SearchType.Bag then return end
	if RoomConfig.isSurface(mo.id) then return end
	self._isDraging = true
	self._canDrag = self:_placeNewFurniture(mo.id, pos, mo.isShare)
	if self._canDrag then
		self:localNotify(RoomNotifyName.OnEditorGlobalBeginDrag, pos)
		GlobalDispatcher:dispatch(GlobalNotify.OnDragOutFurniture)
	else
		self._isDraging = false
	end
end

function RoomEditorFurList:_onDragCell(mo, pos)
	if self._searchType ~= RoomEditorSearcher.SearchType.Bag then return end
	self:localNotify(RoomNotifyName.OnEditorGlobalDrag, pos)
end

function RoomEditorFurList:_onEndDragCell(mo, pos)
	if self._searchType ~= RoomEditorSearcher.SearchType.Bag then return end
	self._isDraging = false
	self:localNotify(RoomNotifyName.OnEditorGlobalEndDrag, pos)
	self:_checkAndResetList()
end

function RoomEditorFurList:_placeNewFurniture(id, dragPos, isShare)
	local furniture = FurnitureConfig.getFurnitureDefine(id)
	if furniture.ext and furniture.ext.funcId then
		if not FuncUnlockFacade.instance:checkIsUnlocked(furniture.ext.funcId, true) then
			return
		end
	end
	if not RoomEdit.instance:tryOkCurEditFurniture(false) then
		return false
	end
	if RoomConfig.isSurface(id) then
		RoomEditFurnitureModel.instance.currentEditSurface = id
		RoomEditModel.instance:changeMode(RoomEditModel.Mode.Surface)
		return false
	end
	local worldPos
	if dragPos then
		worldPos = self:_getOffset(id, self._mainCamera:ScreenToWorldPoint(dragPos))
	end
	RoomEdit.instance:placeNewFurniture(id, true, false, worldPos, worldPos == nil, nil, false, isShare)
	return true
end

function RoomEditorFurList:_getOffset(id, position)
	if not position then return nil end
	if RoomConfig.isPaper(id) then
		return position
	else
		local offset = string.splitToNumber(GameUtils.getCommonConfig("DragFurnitureOffset"), ",")
		return position + Vector3.New(offset[1], offset[2], 0)
	end
end

return RoomEditorFurList 