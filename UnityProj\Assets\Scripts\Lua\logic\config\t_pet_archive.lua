-- {excel:T图鉴配置.xlsx, sheetName:export_宠物}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_pet_archive", package.seeall)

local title = {id=1,onlineTime=2,archiveRewards=3,txtSource=4,sortIndex=5}

local dataList = {
	{19000100, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比广场、淘宝街、友谊庄园", 100},
	{19000111, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 100},
	{19000121, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 100},
	{19000200, "2020-01-01T00:00:00", {{count=5,id=2}}, "所有可捉宠场景均可捕捉", 200},
	{19000211, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 200},
	{19000221, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 200},
	{19000300, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比广场、淘宝街、友谊庄园", 300},
	{19000311, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 300},
	{19000321, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 300},
	{19000400, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比广场、淘宝街、奥比斯雪山、奥比斯山脚", 400},
	{19000411, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 400},
	{19000421, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 400},
	{19000500, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山", 500},
	{19000511, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 500},
	{19000521, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 500},
	{19000600, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比广场、淘宝街、友谊庄园", 600},
	{19000611, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 600},
	{19000621, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 600},
	{19000700, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山、奥比斯山脚", 700},
	{19000711, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 700},
	{19000721, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 700},
	{19000722, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 700},
	{19000800, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比广场、淘宝街、友谊庄园、奥比斯山脚", 800},
	{19000811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 800},
	{19000821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 800},
	{19000900, "2020-01-01T00:00:00", {{count=5,id=2}}, "所有可捉宠场景均可捕捉", 900},
	{19000911, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 900},
	{19000921, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 900},
	{19001000, "2020-01-01T00:00:00", {{count=5,id=2}}, "所有可捉宠场景均可捕捉", 1000},
	{19001011, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1000},
	{19001021, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1000},
	{19001100, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山", 1100},
	{19001111, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1100},
	{19001121, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1100},
	{19001200, nil, {{count=5,id=2}}, "", 9000},
	{19001211, nil, {{count=5,id=2}}, "", 9000},
	{19001221, nil, {{count=5,id=2}}, "", 9000},
	{19001300, nil, {{count=5,id=2}}, "", 9100},
	{19001311, nil, {{count=5,id=2}}, "", 9100},
	{19001321, nil, {{count=5,id=2}}, "", 9100},
	{19001400, nil, {{count=5,id=2}}, "", 9200},
	{19001411, nil, {{count=5,id=2}}, "", 9200},
	{19001421, nil, {{count=5,id=2}}, "", 9200},
	{19001500, nil, {{count=5,id=2}}, "", 9300},
	{19001511, nil, {{count=5,id=2}}, "", 9300},
	{19001521, nil, {{count=5,id=2}}, "", 9300},
	{19001600, "2020-01-01T00:00:00", {{count=10,id=2}}, "所有可捉宠场景均可捕捉", 16000},
	{19001611, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 16000},
	{19001621, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 16000},
	{19001631, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 16000},
	{19001700, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山、奥比斯山脚", 1700},
	{19001711, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1700},
	{19001721, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1700},
	{19001800, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山", 1800},
	{19001811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1800},
	{19001821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1800},
	{19001900, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、友谊庄园", 1900},
	{19001911, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1900},
	{19001921, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1900},
	{19002000, "2020-01-01T00:00:00", {{count=5,id=2}}, "所有可捉宠场景均可捕捉", 2000},
	{19002011, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2000},
	{19002021, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2000},
	{19002100, "2020-01-01T00:00:00", {{count=10,id=2}}, "所有可捉宠场景均可捕捉", 21000},
	{19002111, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 21000},
	{19002121, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 21000},
	{19002131, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 21000},
	{19002200, "2020-01-01T00:00:00", {{count=10,id=2}}, "所有可捉宠场景均可捕捉", 22000},
	{19002211, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 22000},
	{19002221, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 22000},
	{19002231, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 22000},
	{19002300, "2020-01-01T00:00:00", {{count=10,id=2}}, "所有可捉宠场景均可捕捉", 23000},
	{19002311, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 23000},
	{19002321, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 23000},
	{19002331, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 23000},
	{19002400, "2020-01-01T00:00:00", {{count=10,id=2}}, "淘宝街、奥比斯山脚", 24000},
	{19002411, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 24000},
	{19002421, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 24000},
	{19002431, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 24000},
	{19002500, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林", 2500},
	{19002511, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2500},
	{19002521, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2500},
	{19002600, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、友谊庄园", 2600},
	{19002611, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2600},
	{19002621, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2600},
	{19002700, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、友谊庄园", 2700},
	{19002711, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2700},
	{19002721, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2700},
	{19002800, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山", 2800},
	{19002811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2800},
	{19002821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2800},
	{19002900, "2022-08-11T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 2900},
	{19002911, "2022-08-11T05:00:00", {{count=5,id=2}}, "", 2900},
	{19002921, "2022-08-11T05:00:00", {{count=5,id=2}}, "", 2900},
	{19003000, "2023-03-09T05:00:00", {{count=5,id=2}}, "“鱼之歌”活动期间，“海神之梦”功能孵蛋获得", 30000},
	{19003011, "2023-03-09T05:00:00", {{count=5,id=2}}, "", 30000},
	{19003021, "2023-03-09T05:00:00", {{count=5,id=2}}, "", 30000},
	{19003031, "2023-03-09T05:00:00", {{count=5,id=2}}, "", 30000},
	{19003100, "2020-01-01T00:00:00", {{count=10,id=2}}, "所有可捉宠场景均可捕捉", 31000},
	{19003111, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 31000},
	{19003121, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 31000},
	{19003122, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 31000},
	{19003131, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 31000},
	{19003132, "2020-01-01T00:00:00", {{count=10,id=2}}, "", 31000},
	{19003200, "2022-09-16T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 3200},
	{19003211, "2022-09-16T05:00:00", {{count=5,id=2}}, "", 3200},
	{19003221, "2022-09-16T05:00:00", {{count=5,id=2}}, "", 3200},
	{19003400, "2022-10-27T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 3400},
	{19003411, "2022-10-27T05:00:00", {{count=5,id=2}}, "", 3400},
	{19003421, "2022-10-27T05:00:00", {{count=5,id=2}}, "", 3400},
	{19003500, "2022-11-23T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 3500},
	{19003511, "2022-11-23T05:00:00", {{count=5,id=2}}, "", 3500},
	{19003521, "2022-11-23T05:00:00", {{count=5,id=2}}, "", 3500},
	{19003600, "2022-12-15T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 3600},
	{19003611, "2022-12-15T05:00:00", {{count=5,id=2}}, "", 3600},
	{19003621, "2022-12-15T05:00:00", {{count=5,id=2}}, "", 3600},
	{19003700, "2023-01-12T05:00:00", {{count=5,id=2}}, "“风华节”活动期间，所有可捉宠场景均可捕捉", 3700},
	{19003711, "2023-01-12T05:00:00", {{count=5,id=2}}, "", 3700},
	{19003721, "2023-01-12T05:00:00", {{count=5,id=2}}, "", 3700},
	{19003800, "2023-01-12T05:00:00", {{count=5,id=2}}, "“风华节”活动期间，所有可捉宠场景均可捕捉", 3800},
	{19003811, "2023-01-12T05:00:00", {{count=5,id=2}}, "", 3800},
	{19003821, "2023-01-12T05:00:00", {{count=5,id=2}}, "", 3800},
	{19003900, "2023-02-09T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 3900},
	{19003911, "2023-02-09T05:00:00", {{count=5,id=2}}, "", 3900},
	{19003921, "2023-02-09T05:00:00", {{count=5,id=2}}, "", 3900},
	{19004000, "2023-03-09T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 4000},
	{19004011, "2023-03-09T05:00:00", {{count=5,id=2}}, "", 4000},
	{19004021, "2023-03-09T05:00:00", {{count=5,id=2}}, "", 4000},
	{19004100, "2023-04-06T05:00:00", {{count=5,id=2}}, "“音之咖”活动期间，所有可捉宠场景均可捕捉", 4100},
	{19004111, "2023-04-06T05:00:00", {{count=5,id=2}}, "", 4100},
	{19004121, "2023-04-06T05:00:00", {{count=5,id=2}}, "", 4100},
	{19004200, "2023-05-11T05:00:00", {{count=5,id=2}}, "“玩具屋”活动期间，所有可捉宠场景均可捕捉", 4200},
	{19004211, "2023-05-11T05:00:00", {{count=5,id=2}}, "", 4200},
	{19004221, "2023-05-11T05:00:00", {{count=5,id=2}}, "", 4200},
	{19004300, "2023-06-08T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 4300},
	{19004311, "2023-06-08T05:00:00", {{count=5,id=2}}, "", 4300},
	{19004321, "2023-06-08T05:00:00", {{count=5,id=2}}, "", 4300},
	{19004400, "2023-07-06T05:00:00", {{count=10,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 44000},
	{19004411, "2023-07-06T05:00:00", {{count=10,id=2}}, "", 44000},
	{19004421, "2023-07-06T05:00:00", {{count=10,id=2}}, "", 44000},
	{19004422, "2023-07-06T05:00:00", {{count=10,id=2}}, "", 44000},
	{19004431, "2023-07-06T05:00:00", {{count=10,id=2}}, "", 44000},
	{19004432, "2023-07-06T05:00:00", {{count=10,id=2}}, "", 44000},
	{19004500, "2023-08-10T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 4500},
	{19004511, "2023-08-10T05:00:00", {{count=5,id=2}}, "", 4500},
	{19004521, "2023-08-10T05:00:00", {{count=5,id=2}}, "", 4500},
	{19004600, "2023-09-07T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 4600},
	{19004611, "2023-09-07T05:00:00", {{count=5,id=2}}, "", 4600},
	{19004621, "2023-09-07T05:00:00", {{count=5,id=2}}, "", 4600},
	{19004700, "2023-10-12T05:00:00", {{count=5,id=2}}, "“月灵节”活动期间，所有可捉宠场景均可捕捉", 4700},
	{19004711, "2023-10-12T05:00:00", {{count=5,id=2}}, "", 4700},
	{19004721, "2023-10-12T05:00:00", {{count=5,id=2}}, "", 4700},
	{19004800, "2023-11-09T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 4800},
	{19004811, "2023-11-09T05:00:00", {{count=5,id=2}}, "", 4800},
	{19004821, "2023-11-09T05:00:00", {{count=5,id=2}}, "", 4800},
	{19004900, "2023-12-07T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 4900},
	{19004911, "2023-12-07T05:00:00", {{count=5,id=2}}, "", 4900},
	{19004921, "2023-12-07T05:00:00", {{count=5,id=2}}, "", 4900},
	{19005000, "2024-01-04T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 5000},
	{19005011, "2024-01-04T05:00:00", {{count=5,id=2}}, "", 5000},
	{19005021, "2024-01-04T05:00:00", {{count=5,id=2}}, "", 5000},
	{19005100, "2024-02-01T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 5100},
	{19005111, "2024-02-01T05:00:00", {{count=5,id=2}}, "", 5100},
	{19005121, "2024-02-01T05:00:00", {{count=5,id=2}}, "", 5100},
	{19005200, "2024-03-07T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 5200},
	{19005211, "2024-03-07T05:00:00", {{count=5,id=2}}, "", 5200},
	{19005221, "2024-03-07T05:00:00", {{count=5,id=2}}, "", 5200},
	{19005300, "2024-04-03T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 5300},
	{19005311, "2024-04-03T05:00:00", {{count=5,id=2}}, "", 5300},
	{19005321, "2024-04-03T05:00:00", {{count=5,id=2}}, "", 5300},
	{19005400, "2024-05-09T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 5400},
	{19005411, "2024-05-09T05:00:00", {{count=5,id=2}}, "", 5400},
	{19005421, "2024-05-09T05:00:00", {{count=5,id=2}}, "", 5400},
	{19005500, "2024-06-07T05:00:00", {{count=5,id=2}}, "“名侦探”活动期间，所有可捉宠场景均可捕捉", 5500},
	{19005511, "2024-06-07T05:00:00", {{count=5,id=2}}, "", 5500},
	{19005521, "2024-06-07T05:00:00", {{count=5,id=2}}, "", 5500},
	{19005600, "2024-07-04T05:00:00", {{count=10,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 56000},
	{19005611, "2024-07-04T05:00:00", {{count=10,id=2}}, "", 56000},
	{19005621, "2024-07-04T05:00:00", {{count=10,id=2}}, "", 56000},
	{19005631, "2024-07-04T05:00:00", {{count=10,id=2}}, "", 56000},
	{19005700, "2024-08-02T05:00:00", {{count=5,id=2}}, "“星际赛”活动期间，所有可捉宠场景均可捕捉", 5700},
	{19005711, "2024-08-02T05:00:00", {{count=5,id=2}}, "", 5700},
	{19005721, "2024-08-02T05:00:00", {{count=5,id=2}}, "", 5700},
	{19005800, "2024-08-29T05:00:00", {{count=5,id=2}}, "“奇院旅”活动期间，所有可捉宠场景均可捕捉", 5800},
	{19005811, "2024-08-29T05:00:00", {{count=5,id=2}}, "", 5800},
	{19005821, "2024-08-29T05:00:00", {{count=5,id=2}}, "", 5800},
	{19005900, "2024-09-20T05:00:00", {{count=5,id=2}}, "“快乐节”活动期间，所有可捉宠场景均可捕捉", 5900},
	{19005911, "2024-09-20T05:00:00", {{count=5,id=2}}, "", 5900},
	{19005921, "2024-09-20T05:00:00", {{count=5,id=2}}, "", 5900},
	{19006000, "2024-10-24T05:00:00", {{count=5,id=2}}, "“人偶屋”活动期间，所有可捉宠场景均可捕捉", 6000},
	{19006011, "2024-10-24T05:00:00", {{count=5,id=2}}, "", 6000},
	{19006021, "2024-10-24T05:00:00", {{count=5,id=2}}, "", 6000},
	{19006100, "2024-11-21T05:00:00", {{count=5,id=2}}, "“逢妖记”活动期间，所有可捉宠场景均可捕捉", 6100},
	{19006111, "2024-11-21T05:00:00", {{count=5,id=2}}, "", 6100},
	{19006121, "2024-11-21T05:00:00", {{count=5,id=2}}, "", 6100},
	{19006200, "2024-12-19T05:00:00", {{count=5,id=2}}, "“凛冬节”活动期间，所有可捉宠场景均可捕捉", 6200},
	{19006211, "2024-12-19T05:00:00", {{count=5,id=2}}, "", 6200},
	{19006221, "2024-12-19T05:00:00", {{count=5,id=2}}, "", 6200},
	{19006300, "2025-01-22T05:00:00", {{count=5,id=2}}, "“风华节”活动期间，青木森林可捕捉", 6300},
	{19006311, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6300},
	{19006321, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6300},
	{19006400, "2025-01-22T05:00:00", {{count=5,id=2}}, "“风华节”活动期间，淘宝街可捕捉", 6400},
	{19006411, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6400},
	{19006421, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6400},
	{19006500, "2025-01-22T05:00:00", {{count=5,id=2}}, "“风华节”活动期间，奥比斯雪山可捕捉", 6500},
	{19006511, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6500},
	{19006521, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6500},
	{19006600, "2025-01-22T05:00:00", {{count=5,id=2}}, "“风华节”活动期间，奥比广场可捕捉", 6600},
	{19006611, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6600},
	{19006621, "2025-01-22T05:00:00", {{count=5,id=2}}, "", 6600},
	{19006700, "2025-02-21T05:00:00", {{count=5,id=2}}, "“时之花”活动期间，所有可捉宠场景均可捕捉", 6700},
	{19006711, "2025-02-21T05:00:00", {{count=5,id=2}}, "", 6700},
	{19006721, "2025-02-21T05:00:00", {{count=5,id=2}}, "", 6700},
	{19006800, "2025-03-20T05:00:00", {{count=5,id=2}}, "“心之森”活动期间，所有可捉宠场景均可捕捉", 6800},
	{19006811, "2025-03-20T05:00:00", {{count=5,id=2}}, "", 6800},
	{19006821, "2025-03-20T05:00:00", {{count=5,id=2}}, "", 6800},
	{19006900, "2025-04-17T05:00:00", {{count=5,id=2}}, "“闯港城”活动期间，所有可捉宠场景均可捕捉", 6900},
	{19006911, "2025-04-17T05:00:00", {{count=5,id=2}}, "", 6900},
	{19006921, "2025-04-17T05:00:00", {{count=5,id=2}}, "", 6900},
	{19007000, "2025-05-15T05:00:00", {{count=5,id=2}}, "“萌市集”活动期间，所有可捉宠场景均可捕捉", 7000},
	{19007011, "2025-05-15T05:00:00", {{count=5,id=2}}, "", 7000},
	{19007021, "2025-05-15T05:00:00", {{count=5,id=2}}, "", 7000},
	{19007100, "2025-06-07T05:00:00", {{count=5,id=2}}, "“友晴天”活动期间，所有可捉宠场景均可捕捉", 7100},
	{19007111, "2025-06-07T05:00:00", {{count=5,id=2}}, "", 7100},
	{19007121, "2025-06-07T05:00:00", {{count=5,id=2}}, "", 7100},
	{19007200, "2025-07-03T05:00:00", {{count=5,id=2}}, "“熊次元”活动期间，所有可捉宠场景均可捕捉", 7200},
	{19007211, "2025-07-03T05:00:00", {{count=5,id=2}}, "", 7200},
	{19007221, "2025-07-03T05:00:00", {{count=5,id=2}}, "", 7200},
	{19100100, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山、奥比斯山脚", 101},
	{19100111, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 101},
	{19100121, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 101},
	{19200100, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、友谊庄园", 102},
	{19200111, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 102},
	{19200121, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 102},
	{19300100, "2020-01-01T00:00:00", {{count=5,id=2}}, "友谊庄园", 103},
	{19300111, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 103},
	{19300121, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 103},
	{19400100, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比广场、淘宝街", 104},
	{19400111, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 104},
	{19400121, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 104},
	{19100300, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山", 301},
	{19100311, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 301},
	{19100321, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 301},
	{19100400, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比广场、淘宝街、友谊庄园", 401},
	{19100411, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 401},
	{19100421, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 401},
	{19200400, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山、奥比斯山脚", 402},
	{19200411, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 402},
	{19200421, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 402},
	{19100500, "2020-01-01T00:00:00", {{count=5,id=2}}, "淘宝街、奥比斯山脚", 501},
	{19100511, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 501},
	{19100521, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 501},
	{19100600, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比广场、淘宝街", 601},
	{19100611, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 601},
	{19100621, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 601},
	{19100700, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比广场", 701},
	{19100711, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 701},
	{19100721, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 701},
	{19100800, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比广场、友谊庄园", 801},
	{19100811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 801},
	{19100821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 801},
	{19200800, "2020-01-01T00:00:00", {{count=5,id=2}}, "友谊庄园", 802},
	{19200811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 802},
	{19200821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 802},
	{19100900, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比广场、淘宝街、友谊庄园", 901},
	{19100911, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 901},
	{19100921, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 901},
	{19200900, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山、奥比斯山脚", 902},
	{19200911, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 902},
	{19200921, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 902},
	{19101000, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比斯山脚", 1001},
	{19101011, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1001},
	{19101021, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1001},
	{19201000, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山", 1002},
	{19201011, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1002},
	{19201021, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1002},
	{19101800, "2020-01-01T00:00:00", {{count=5,id=2}}, "淘宝街、奥比斯山脚", 1801},
	{19101811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1801},
	{19101821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 1801},
	{19102000, "2022-07-21T05:00:00", {{count=5,id=2}}, "活动期间，所有可捉宠场景均可捕捉", 2001},
	{19102011, "2022-07-21T05:00:00", {{count=5,id=2}}, "", 2001},
	{19102021, "2022-07-21T05:00:00", {{count=5,id=2}}, "", 2001},
	{19102600, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比广场、淘宝街", 2601},
	{19102611, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2601},
	{19102621, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2601},
	{19202600, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山、奥比斯山脚", 2602},
	{19202611, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2602},
	{19202621, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2602},
	{19102700, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比广场、淘宝街", 2701},
	{19102711, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2701},
	{19102721, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2701},
	{19202700, "2020-01-01T00:00:00", {{count=5,id=2}}, "奥比斯雪山、奥比斯山脚", 2702},
	{19202711, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2702},
	{19202721, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2702},
	{19102800, "2020-01-01T00:00:00", {{count=5,id=2}}, "青木森林、奥比广场、淘宝街、奥比斯山脚", 2801},
	{19102811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2801},
	{19102821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2801},
	{19202800, "2020-01-01T00:00:00", {{count=5,id=2}}, "友谊庄园", 2802},
	{19202811, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2802},
	{19202821, "2020-01-01T00:00:00", {{count=5,id=2}}, "", 2802},
}

local t_pet_archive = {
	[19000100] = dataList[1],
	[19000111] = dataList[2],
	[19000121] = dataList[3],
	[19000200] = dataList[4],
	[19000211] = dataList[5],
	[19000221] = dataList[6],
	[19000300] = dataList[7],
	[19000311] = dataList[8],
	[19000321] = dataList[9],
	[19000400] = dataList[10],
	[19000411] = dataList[11],
	[19000421] = dataList[12],
	[19000500] = dataList[13],
	[19000511] = dataList[14],
	[19000521] = dataList[15],
	[19000600] = dataList[16],
	[19000611] = dataList[17],
	[19000621] = dataList[18],
	[19000700] = dataList[19],
	[19000711] = dataList[20],
	[19000721] = dataList[21],
	[19000722] = dataList[22],
	[19000800] = dataList[23],
	[19000811] = dataList[24],
	[19000821] = dataList[25],
	[19000900] = dataList[26],
	[19000911] = dataList[27],
	[19000921] = dataList[28],
	[19001000] = dataList[29],
	[19001011] = dataList[30],
	[19001021] = dataList[31],
	[19001100] = dataList[32],
	[19001111] = dataList[33],
	[19001121] = dataList[34],
	[19001200] = dataList[35],
	[19001211] = dataList[36],
	[19001221] = dataList[37],
	[19001300] = dataList[38],
	[19001311] = dataList[39],
	[19001321] = dataList[40],
	[19001400] = dataList[41],
	[19001411] = dataList[42],
	[19001421] = dataList[43],
	[19001500] = dataList[44],
	[19001511] = dataList[45],
	[19001521] = dataList[46],
	[19001600] = dataList[47],
	[19001611] = dataList[48],
	[19001621] = dataList[49],
	[19001631] = dataList[50],
	[19001700] = dataList[51],
	[19001711] = dataList[52],
	[19001721] = dataList[53],
	[19001800] = dataList[54],
	[19001811] = dataList[55],
	[19001821] = dataList[56],
	[19001900] = dataList[57],
	[19001911] = dataList[58],
	[19001921] = dataList[59],
	[19002000] = dataList[60],
	[19002011] = dataList[61],
	[19002021] = dataList[62],
	[19002100] = dataList[63],
	[19002111] = dataList[64],
	[19002121] = dataList[65],
	[19002131] = dataList[66],
	[19002200] = dataList[67],
	[19002211] = dataList[68],
	[19002221] = dataList[69],
	[19002231] = dataList[70],
	[19002300] = dataList[71],
	[19002311] = dataList[72],
	[19002321] = dataList[73],
	[19002331] = dataList[74],
	[19002400] = dataList[75],
	[19002411] = dataList[76],
	[19002421] = dataList[77],
	[19002431] = dataList[78],
	[19002500] = dataList[79],
	[19002511] = dataList[80],
	[19002521] = dataList[81],
	[19002600] = dataList[82],
	[19002611] = dataList[83],
	[19002621] = dataList[84],
	[19002700] = dataList[85],
	[19002711] = dataList[86],
	[19002721] = dataList[87],
	[19002800] = dataList[88],
	[19002811] = dataList[89],
	[19002821] = dataList[90],
	[19002900] = dataList[91],
	[19002911] = dataList[92],
	[19002921] = dataList[93],
	[19003000] = dataList[94],
	[19003011] = dataList[95],
	[19003021] = dataList[96],
	[19003031] = dataList[97],
	[19003100] = dataList[98],
	[19003111] = dataList[99],
	[19003121] = dataList[100],
	[19003122] = dataList[101],
	[19003131] = dataList[102],
	[19003132] = dataList[103],
	[19003200] = dataList[104],
	[19003211] = dataList[105],
	[19003221] = dataList[106],
	[19003400] = dataList[107],
	[19003411] = dataList[108],
	[19003421] = dataList[109],
	[19003500] = dataList[110],
	[19003511] = dataList[111],
	[19003521] = dataList[112],
	[19003600] = dataList[113],
	[19003611] = dataList[114],
	[19003621] = dataList[115],
	[19003700] = dataList[116],
	[19003711] = dataList[117],
	[19003721] = dataList[118],
	[19003800] = dataList[119],
	[19003811] = dataList[120],
	[19003821] = dataList[121],
	[19003900] = dataList[122],
	[19003911] = dataList[123],
	[19003921] = dataList[124],
	[19004000] = dataList[125],
	[19004011] = dataList[126],
	[19004021] = dataList[127],
	[19004100] = dataList[128],
	[19004111] = dataList[129],
	[19004121] = dataList[130],
	[19004200] = dataList[131],
	[19004211] = dataList[132],
	[19004221] = dataList[133],
	[19004300] = dataList[134],
	[19004311] = dataList[135],
	[19004321] = dataList[136],
	[19004400] = dataList[137],
	[19004411] = dataList[138],
	[19004421] = dataList[139],
	[19004422] = dataList[140],
	[19004431] = dataList[141],
	[19004432] = dataList[142],
	[19004500] = dataList[143],
	[19004511] = dataList[144],
	[19004521] = dataList[145],
	[19004600] = dataList[146],
	[19004611] = dataList[147],
	[19004621] = dataList[148],
	[19004700] = dataList[149],
	[19004711] = dataList[150],
	[19004721] = dataList[151],
	[19004800] = dataList[152],
	[19004811] = dataList[153],
	[19004821] = dataList[154],
	[19004900] = dataList[155],
	[19004911] = dataList[156],
	[19004921] = dataList[157],
	[19005000] = dataList[158],
	[19005011] = dataList[159],
	[19005021] = dataList[160],
	[19005100] = dataList[161],
	[19005111] = dataList[162],
	[19005121] = dataList[163],
	[19005200] = dataList[164],
	[19005211] = dataList[165],
	[19005221] = dataList[166],
	[19005300] = dataList[167],
	[19005311] = dataList[168],
	[19005321] = dataList[169],
	[19005400] = dataList[170],
	[19005411] = dataList[171],
	[19005421] = dataList[172],
	[19005500] = dataList[173],
	[19005511] = dataList[174],
	[19005521] = dataList[175],
	[19005600] = dataList[176],
	[19005611] = dataList[177],
	[19005621] = dataList[178],
	[19005631] = dataList[179],
	[19005700] = dataList[180],
	[19005711] = dataList[181],
	[19005721] = dataList[182],
	[19005800] = dataList[183],
	[19005811] = dataList[184],
	[19005821] = dataList[185],
	[19005900] = dataList[186],
	[19005911] = dataList[187],
	[19005921] = dataList[188],
	[19006000] = dataList[189],
	[19006011] = dataList[190],
	[19006021] = dataList[191],
	[19006100] = dataList[192],
	[19006111] = dataList[193],
	[19006121] = dataList[194],
	[19006200] = dataList[195],
	[19006211] = dataList[196],
	[19006221] = dataList[197],
	[19006300] = dataList[198],
	[19006311] = dataList[199],
	[19006321] = dataList[200],
	[19006400] = dataList[201],
	[19006411] = dataList[202],
	[19006421] = dataList[203],
	[19006500] = dataList[204],
	[19006511] = dataList[205],
	[19006521] = dataList[206],
	[19006600] = dataList[207],
	[19006611] = dataList[208],
	[19006621] = dataList[209],
	[19006700] = dataList[210],
	[19006711] = dataList[211],
	[19006721] = dataList[212],
	[19006800] = dataList[213],
	[19006811] = dataList[214],
	[19006821] = dataList[215],
	[19006900] = dataList[216],
	[19006911] = dataList[217],
	[19006921] = dataList[218],
	[19007000] = dataList[219],
	[19007011] = dataList[220],
	[19007021] = dataList[221],
	[19007100] = dataList[222],
	[19007111] = dataList[223],
	[19007121] = dataList[224],
	[19007200] = dataList[225],
	[19007211] = dataList[226],
	[19007221] = dataList[227],
	[19100100] = dataList[228],
	[19100111] = dataList[229],
	[19100121] = dataList[230],
	[19200100] = dataList[231],
	[19200111] = dataList[232],
	[19200121] = dataList[233],
	[19300100] = dataList[234],
	[19300111] = dataList[235],
	[19300121] = dataList[236],
	[19400100] = dataList[237],
	[19400111] = dataList[238],
	[19400121] = dataList[239],
	[19100300] = dataList[240],
	[19100311] = dataList[241],
	[19100321] = dataList[242],
	[19100400] = dataList[243],
	[19100411] = dataList[244],
	[19100421] = dataList[245],
	[19200400] = dataList[246],
	[19200411] = dataList[247],
	[19200421] = dataList[248],
	[19100500] = dataList[249],
	[19100511] = dataList[250],
	[19100521] = dataList[251],
	[19100600] = dataList[252],
	[19100611] = dataList[253],
	[19100621] = dataList[254],
	[19100700] = dataList[255],
	[19100711] = dataList[256],
	[19100721] = dataList[257],
	[19100800] = dataList[258],
	[19100811] = dataList[259],
	[19100821] = dataList[260],
	[19200800] = dataList[261],
	[19200811] = dataList[262],
	[19200821] = dataList[263],
	[19100900] = dataList[264],
	[19100911] = dataList[265],
	[19100921] = dataList[266],
	[19200900] = dataList[267],
	[19200911] = dataList[268],
	[19200921] = dataList[269],
	[19101000] = dataList[270],
	[19101011] = dataList[271],
	[19101021] = dataList[272],
	[19201000] = dataList[273],
	[19201011] = dataList[274],
	[19201021] = dataList[275],
	[19101800] = dataList[276],
	[19101811] = dataList[277],
	[19101821] = dataList[278],
	[19102000] = dataList[279],
	[19102011] = dataList[280],
	[19102021] = dataList[281],
	[19102600] = dataList[282],
	[19102611] = dataList[283],
	[19102621] = dataList[284],
	[19202600] = dataList[285],
	[19202611] = dataList[286],
	[19202621] = dataList[287],
	[19102700] = dataList[288],
	[19102711] = dataList[289],
	[19102721] = dataList[290],
	[19202700] = dataList[291],
	[19202711] = dataList[292],
	[19202721] = dataList[293],
	[19102800] = dataList[294],
	[19102811] = dataList[295],
	[19102821] = dataList[296],
	[19202800] = dataList[297],
	[19202811] = dataList[298],
	[19202821] = dataList[299],
}

t_pet_archive.dataList = dataList
local mt
if PetArchiveDefine then
	mt = {
		__cname =  "PetArchiveDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or PetArchiveDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_pet_archive