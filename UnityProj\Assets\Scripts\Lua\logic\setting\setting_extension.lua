
--[[

扩展ID定义，除了一些特定系统需用保留扩展id外，可以根据自己项目需要定义：
65535: 登陆
2：  角色而
3:   城市
4:   聊天

setting_extension中每一项的格式为：
[extId]={
		["models"]={"XxxModel"},               --扩展的数据管理器，可多个
		["configs"]={"XxxConfig"},             --扩展的配置管理器，可多个
		["agents"]={"XxxAgent"},               --扩展负责网络通信部分，可多个
		["controllers"]={"XxxController"},     --扩展数据层逻辑管理，可多个
	},

例如：
local setting_extension = {}

setting_extension = {
	[65535]={
		["models"]={"LoginModel"},
		["agents"]={"LoginAgent"},
		["controllers"]={"LoginController"},
	},
	[2]={
		["models"] = {"RoleModel"}
	},
	[3]={
		["agents"] = {"SceneAgent"},
		["configs"]={"SceneConfig"},
		["models"] = {"SceneModel"},
		["controllers"] = {"SceneController"}
	},
	[4]={
		["agents"] = {"ChatAgent"},
		["models"] = {"ChannelModel"},
		["controllers"] = {"ChatController"}
	}
}

return setting_extension

--]]

local setting_extension = {}

setting_extension = {
    [-1] = {
        ["agents"] = { "SystemAgent" },
        ["controllers"] = { "NewNoticeController" },
    },
    [1] = {
        ["agents"] = { "UserAgent" },
        ["controllers"] = { "SurveyController" },
    },
    [2] = {
        ["agents"] = { "SceneAgent" },
        ["controllers"] = { "SceneController","SceneTrapController" },
    },
    [3] = {
        ["agents"] = { "HouseAgent" },
        -- ["configs"] = { "RoomConfig" },
        -- ["models"] = { "RoomModel", "HourseModel", "RoomFurnitureModel", "RoomEditModel", "RoomHeroUpgradeModel", "RoomHeroFoodModel", "RoomVisitModel", "RoomHeroModel", "RoomEditFurnitureModel" },
        ["controllers"] = { "RoomController" },
    },
    [4] = {
        ["agents"] = { "ClothesAgent" },
        -- ["configs"] = { "RoomConfig" },
         ["models"] = { "DyeModel"},
         ["controllers"] = { "DressController" },
    },
    [5] = {
        ["agents"] = { "BackpackAgent" },
        -- ["configs"] = { "RoomConfig" },
        ["models"] = { "ItemService"},
        ["controllers"] = { "ItemSoldController" },
    },
    [6] = {
        ["agents"] = { "WorkshopAgent" },
        -- ["configs"] = { "RoomConfig" },
        ["models"] = { "WorkshopModel" },
        ["controllers"] = { "WorkshopController" },
    },
    [7] = {
        ["configs"] = {"TaskTypeConfig", "TaskStartConfig", "TaskStepObjectiveConfig", "DailyTaskConfig"},
        ["models"] = {"TaskModel", "TaskChapterModel", "TaskTrackListModel", "TaskNPCModel", "DailyTaskModel", "TaskPanelSelectTaskModel", "TaskBusinessSelectTaskModel", "TaskRedPointModel"},
        ["agents"] = {"TaskAgent"},
        ["controllers"] = {"TaskController","TaskStepController","DailyTaskController","TaskSceneObjController","TaskRedPointController","TaskStoryTipsController","OutDarkController"}
    },
    [8] = {
        ["agents"] = {"IslandAgent"},
        ["controllers"] = { "FarmController" ,"IslandDiaryController", "SurfaceFurnishingController"},
    },
    [9] = {
        ["agents"] = {"CollectionAgent"},
        ["controllers"] = { "CollectController" },
    },
    [10] = {
        ["agents"] = {"TimeStoreAgent"},
        ["controllers"] = { "StoreController" },
    },
    [11] = {
        ["agents"] = {"FriendAgent"},
        ["models"] = {"FriendModel","SelectFriendListViewModel","SelectAffinityTaskModel"},
        ["controllers"] = { "FriendController" },
    },
    [12] = {
        ["agents"] = {"LotteryEggAgent"},
        ["controllers"] = { "LotteryController" },
    },
    [13] =
    {
        ["configs"] = { "ChatConfig" },
        ["controllers"] = {"ChatController","ChatTeaPartyController","MarqueeController","InstantTipsMgr", "ChatVoiceMessageController"},
        ["agents"] = { "ChatAgent" ,"MarqueeAgent"},
        ["models"] = { "ChatModel" ,"MarqueeModel"},
    },
    [14] =
    {
        ["agents"] = { "FishingAgent" },
        ["controllers"] = { "AquariumController" },
		["models"] = { "ArchiveFishModel"},
    },
    [15] =
    {
        ["agents"] = { "PartyAgent" },
        ["controllers"] = {"PartyController"}
    },
    [16] = {
        ["agents"] = {"MailAgent"},
        ["models"] = { "MailModel" },
        ["controllers"] = { "MailController" }
    },
    [17] = {
        ["agents"] = {"FoodAgent"}
    },
    [18] = {
        ["agents"] = {"OrderAgent"},
        ["controllers"] = {"OrderController"}
    },
    [19] = {
        ["agents"] = {"FavorabilityAgent"},
        ["models"] = { "FavorabilityModel"},
		["controllers"] = {"FavorabilityController"}
    },
    [20] = {
        ["agents"] = {"TinyGameAgent"},
        ["controllers"] = { "TinyGameController" }
    },
    [21] = {
        ["agents"] = {"BuffAgent"},
        ["controllers"] = { "BuffController" }
    },
    [22] = {
        ["agents"] = {"SceneGameAgent"},
    },
    [23] = {
        ["models"] = { "HeadPortraitModel"},
        ["agents"] = {"ImageAgent"},
        ["controllers"] = { "ImageController" }
    },
    [24] = {
        ["agents"] = {"TradingAgent"},
        ["controllers"] = { "TradingController" }
    },
    [25] = {
        ["agents"] = {"GroceryAgent"},
        ["controllers"] = { "GroceryController" },
        ["models"] = { "GroceryModel" , "GroceryItemListModel"},
    },
    [26] = {
        ["agents"] = {"AobiCircleAgent"},
        ["models"] = {"MessageListModel","AobiCircleModel"},
        ["controllers"] = {"AobiCircleController"}
    },
    [27] = {
        ["agents"] = {"RedPointAgent"},
        ["controllers"] = {"RedPointController"}
    },
    [28] = {
        ["agents"] = {"AchievementAgent"},
        ["controllers"] = {"AchievementController"},
        ["models"] = {"AchievementModel"},
    },
    [29] = {
        ["agents"] = {"ArchiveAgent"},
		["controllers"] = {"ArchiveController", "AnimalWorldArchiveController"},
		["models"] = {"ArchiveSingleModel","ArchiveSuitModel"}
    },
    [30] = {
        ["models"] = {"PetModel", "PetHomeModel", "PetNewStateModel","PetExpeditionModel"},
        ["agents"] = {"PetAgent","PetExpeditionAgent"},
        ["controllers"] = {"PetController","PetCatchController","PetFollowController","PetExpeditionController","PetShowController"},
        -- ["configs"] = {"PetConfig"},
    },
    [31] = {
        ["agents"] = {"RecycleAgent"},
        ["controllers"] = {"RecycleController"},
    },
    [32] = {
        ["agents"] = {"FuncUnlockAgent"},
        ["controllers"] = {"FuncUnlockController"},
        ["models"] = {"FuncUnlockModel"}
    },
    [33] = {
        ["agents"] = {"RechargeAgent"},
        ["controllers"] = {"RechargeController", "PersonalizedGiftBagController"},
        ["models"] = {"RechargeModel","FirstRechargeModel"}
    },
    [34] = {
        ["agents"] = {"DecorationMatchAgent"},
        ["controllers"] = {"FashionRaceController"},
    },
    [35] = {
        ["agents"] = {"ElfAgent"},
        ["models"] = {"ElfModel"},
        ["controllers"] = {"ElfSceneController","ElfController"}
    },
    [36] = {
        ["agents"] = {"DragonChildAgent"},
        ["controllers"] = {"DragonChildController"},
        ["models"] = {"DragonChildModel"}
    },
    [37] = {
        ["agents"] = {"WishCloverAgent"},
        ["controllers"] = {"WishCloverController"},
        ["models"] = {"WishCloverModel"}
    },
    [38] = {
        ["agents"] = { "BigHeadMakeAgent" , "SimpleInteractionAgent","CottonAgent","AlbumAgent"},
        ["models"] = {"BigHeadMakerModel","SimpleInteractionModel"},
        ["controllers"] = { "BigHeadMakerController" ,"SimpleInteractionMgr","AlbumController"},
    },
    [39] = {
        ["agents"] = {"TravellerAgent"},
        ["controllers"] = {"TravellerController"},
        ["models"] = {"TravellerModel"},
    },
    [40] = {
        ["agents"] = {"TravellerTreasureAgent"},
        ["controllers"] = {"TravellerTreasureController"},
        ["models"] = {"TravellerTreasureModel"},
    },
	    [41] = {
        ["agents"] = {"FlowerGardenAgent"},
        ["controllers"] = {"GardenFlowerController"},
    },
    [42] ={
        ["agents"] = {"FriendTaskAgent"},
        ["models"] = {"FriendTaskModel"},
        ["controllers"] = {"FriendTaskController"},
    },
    [43] = {
        ["controllers"] = {"DreamLikeController", "DreamLikeConversationCtrl"},
        ["models"] = {"DreamLikeModel"},
        ["agents"] = {"DreamPalaceAgent"},
    },
    [44] = {
        ["models"] = {"FirstPalaceModel"},
        ["agents"] = {"FirstPalaceAgent"},
    },
    [45] = {
        ["models"] = {"CouncilInfoMo","CouncilLogListModel","CouncilMemberListModel","CouncilDonateModel","CouncilBackpackModel"},
        ["agents"] = {"CouncilAgent"},
        ["controllers"] = {"CouncilController","CouncilManager", "CouncilBuildingMgr", "CouncilBuildingCtrl"},
    },
    [46] = {
        ["agents"] = {"ATMAgent"},
        ["controllers"] = {"ATMController"},
    },
    [47] = {
        ["models"] = {"FriendBreezeModel"},
        ["agents"] = {"FriendshipCheckAgent"},
        ["controllers"] = {"AffinityCheckCtrl","FriendBreezeController","FriendCheckShimmerController","FriendDawnController"},
    },
    [48] = {
        ["agents"] = {"VipAgent"},
        ["controllers"] = {"RubyMemberController"},
    },
    [49] = {
        ["models"] = {"ParttimeModel"},
        ["agents"] = {"ParttimeAgent"},
        ["controllers"] = {"ParttimeController"},
    },
    [50] = {
        ["models"] = {"ElfBattleModel"},
        ["agents"] = {"ElfMagicAgent"},
        ["controllers"] = {"ElfBattleController"},
    },
    [51] = {
        ["agents"] = {"TalentAgent"},
        ["controllers"] = {"TalentManager", "NavigatorController","HelperController"},
        ["models"] = {"TalentInfoMo", "NavigatorModel"}
    },
    [52] = {
        ["models"] = {"IslandBuildModel"},
        ["agents"] = {"IslandBuildAgent"},
        ["controllers"] = {"IslandBuildController"},
    },
    [53] = {
        ["agents"] = {"RankAgent"},
        ["controllers"] = {"RankingListController"},
        ["models"] = {"RankingListClothModel"},
    },
    [54] = {
        ["agents"] = {"MistyForestAgent"},
        ["controllers"] = {"MistyForestController"},
        ["models"] = {"MistyForestModel"}
    },
    [55] = {
        ["agents"] = {"WerewolfAgent"},
        ["controllers"] = {"WerewolfSceneController", "WerewolfController"},
        ["models"] = {"WerewolfModel", "WerewolfCoordModel"},
    },
    [56] = {
        ["models"] = {"ExternalShareModel"},
        ["agents"] = {"ExternalShareAgent"},
        ["controllers"] = {"ExternalShareController"},
    },
    [57] = {
        ["models"] = {"FreeRaceModel"},
        ["agents"] = {"FreeRiceAgent"},
        ["controllers"] = {"FreeRaceController"},
    },

    [58] = {
        ["models"] = {"GameRoomListModel", "GameCentreModel"},
        ["agents"] = {"GameRoomAgent"},
        ["controllers"] = {"GameRoomController", "GameCentreController"},
    },
    [59] = {
        ["agents"] = {"CouncilDefendAgent"},
        ["controllers"] = {"CouncilBossController"},
        ["models"] = {"CouncilBossModel"}
    },
	[60] = {
		["models"] = {"GobangModel"},
		["agents"] = {"GobangAgent"},
		["controllers"] = {"GobangController","GobangBanController"},
	},
    [62] = {
        ["models"] = {"Magicalland2Model"},
        ["agents"] = {"Magicalland2Agent"},
        ["controllers"] = {"Magicalland2Controller"},
    },
	[63] = {
		["controllers"] = {"CatchStarController"},
		["agents"] = { "CatchStarAgent" },
	},
    [64] = {
        ["agents"] = {"MoveHouseAgent"},
        ["models"] = {"MoveHouseModel", "MoveHouseCoordModel"},
        ["controllers"] = {"MoveHouseController"},
    },
    [65] = {
        ["agents"] = {"MiaoHouseAgent"},
        ["models"] = {"CombineGameModel"},
        ["controllers"] = {"CombineGameController"},
    },
	[66] = {
		["agents"] = {"RedEnvelopAgent"},
		["models"] = {"RedEnvelopModel"},
		["controllers"] = {"RedEnvelopController"},
	},
    [67] = {
        ["controllers"] = {"MusicGameParser","MusicGameController"},
        ["agents"] = {"MusicGameAgent"},
        ["models"] = {"MusicGameModel"},
    },
    [68] = {
        ["controllers"] = {"AobiReturnController","AobiReturnTaskController"},
        ["agents"] = {"Backflow2Agent"},
    },
    [69] = {
        ["models"] = {"Magicalland3Model"},
        ["agents"] = {"Magicalland3Agent"},
        ["controllers"] = {"Magicalland3Controller"},
    },
    [70] = {
		["controllers"] = { "ActivityPlayerGuideController" },
	},
    [71] = {
        ["models"] = {"PotionsRefineModel"},
        ["agents"] = {"PotionsRefineAgent"},
        ["controllers"] = {"PotionsRefineController"},
    },
    [72] = {
        ["agents"] = {"MoveOrDieAgent"},
        ["controllers"] = {"MoveOrDieController"},
        -- ["models"] = {"MoveOrDieModel"}
    },
	[73] = {
		["agents"] = {"ItemLockAgent"},
		["controllers"] = {"ItemSoldController"},
	},
	[74] = {
		["agents"] = {"JukeboxAgent"},
		["controllers"] = {"JukeboxController"},
	},
	[75] = {
		["agents"] = {"SurpriseSignInAgent"},
		["controllers"] = { "SurpriseSignInController" },
	},
    [76] = {
        ["agents"] = {"MultiRoomListAgent"},
        ["controllers"] = {"RecruitBoardController"},
    },
    [77] = {
        ["models"] = {"Magicalland4Model"},
        ["agents"] = {"Magicalland4Agent"},
        ["controllers"] = {"Magicalland4Controller"},
    },
    [78] = {
        ["agents"] = {"DrawAndGuessAgent"},
        ["controllers"] = {"DrawAndGuessController"},
    },
    [79] = {
        ["models"] = {"EcoParkModel"},
        ["agents"] = {"EcoParkAgent"},
        ["controllers"] = {"EcoParkController"},
    },
    [80] = {
        ["agents"] = {"DrawTortoiseGameAgent"},
    },
    [81] = {
        ["agents"] = {"Backflow3Agent"},
    },
	[82] = {
		["agents"] = {"FurnitureTemplateShareSquareAgent"},
		["controllers"] = {"FurnitureTempalteSquareController"},
	},
    [83] = {
        ["agents"] = {"RhythmBattlesAgent"},  
        ["models"] = {"RhythmBattlesModel"},
        ["controllers"] = {"RhythmBattlesController"},
    },
    [85] = {
        ["agents"] = {"SeafloorIslandAgent"},
    },
    [84] = {
        ["models"] = {"StoryTellerBook_DressListModel", "StoryTellerBook_StoryListModel", "StoryTellerModel"},
        ["controllers"] = {"StoryTellerController"},
        ["agents"] = {"StoryTellerAgent"},
    },
    [86] = {
        ["models"] = {"SeaModel","SeaTaskModel"},
        ["agents"] = {"SeaAgent"},
        ["controllers"] = {"SeaController","SeaTaskController"},
    },
    [87] = {
        ["models"] = {"SeaTechTreeModel"},
        ["agents"] = {"SeaTechTreeAgent"},
        ["controllers"] = {"SeaTechTreeController"},
    },
    [88] = {
        ["agents"] = {"SeabedOrderAgent"},
    },
    [89] ={
        ["agents"] = {"DollHouseAgent"},
        ["controllers"] = {"DollHouseController"},
        ["models"] = {"DollHouseModel"}
    },
    [90] ={
        ["agents"] = {"FifthPalaceAgent"},
        ["controllers"] = {"Magicalland5Controller"},
    },
    [92] = {
        ["models"] = {"DuommModel"},
        ["agents"] = {"DuommAgent"},
        ["controllers"] = {"DuommController"},
    },

    [91] ={
        ["agents"] = {"ClothesCodeAgent"},
    },
    [93] ={
        ["controllers"] = {"ShareStreetController"},
        ["agents"] = {"ShareStreetAgent"},
    },

    [98] = {
        ["agents"] = {"SnakeAgent"},
    },
    -- 帧同步
    [99] = {
        ["agents"] = {"FrameSyncAgent"},
		["models"] = {"FrameSyncModel"},
        ["controllers"] = {"FrameSyncController"},
    },
    [100] = {
        ["agents"] = {"ActivityAgent"},
        ["controllers"] = { "ActivityController" ,"ActivityRedPointController", "Activity13AnniversaryController","CommonActivityTaskController","ActivityTabController"},
        ["models"] = {"SevenDaySignModel","ActivityModel","ActivityGainLimitModel","MiddleThemeModel"}
    },

    [101] = {
        ["agents"] = {"ActivityWinterAgent"},
        ["controllers"] = { "ActivityWinterController" },
    },

    [102] = {
        ["agents"] = {"AobiBankAgent"},
        ["models"] = {"AobiBankModel"},
        ["controllers"] = { "AobiBankController" },
    },

    [103] = {
        ["agents"] = {"Activity103Agent"},
        ["controllers"] = { "HotpotController" },
    },

    [107] = {
        ["agents"] = {"ActivityHappyAgent"},
        ["models"] = {"ActivityNpcModel"},
        ["controllers"] = { "ActivityNpcController" },
    },

    -- [109] = {
    --     ["agents"] = {"Activity109Agent"},
    -- },

    -- [110] = {
    --     ["agents"] = {"Activity110Agent"},
    --     ["controllers"] = {"FireworkController"},
    -- },

    [111] = {
        ["agents"] = {"Activity111Agent"},
        ["controllers"] = {"LifeGuideController"},
    },

    [114] = {
        ["agents"] = {"Activity114Agent"},
    },
    [115] = {
        ["agents"] = {"Activity115Agent"},
    },
    [116] = {
        ["agents"] = {"Activity116Agent"},
    },
    [117] = {
        ["agents"] = {"Activity117Agent"},
        ["controllers"] = {"SevenDayActiveController"},
    },
    [122] = {
        ["agents"] = {"Activity122Agent"},
    },
    [123] = {
        ["controllers"] = { "PlayingMusicController" }
    },
    [124] = {
        ["agents"] = { "Activity124Agent" }
    },
    [125] = {
        ["controllers"] = { "MonopolyController" },
        ["agents"] = {"Activity125Agent"}
    },
    [126] = {
        ["agents"] = {"Activity126Agent"},
    },
    [128] = {
        ["agents"] = {"Activity128Agent"},
    },
    [130] = {
        ["controllers"] = { "CarvingController" },
        ["agents"] = {"Activity130Agent"},
    },
    [131] = {
        ["agents"] = {"Activity131Agent"},
    },
    [132] = {
        ["agents"] = {"Activity132Agent"},
    },
    [133] = {
        ["agents"] = {"Activity133Agent"},
        ["controllers"] = {"GOFNpcController", "GOFRedPointController"},
    },
	[135] = {
        ["agents"] = {"Activity135Agent"},
        ["controllers"] = {"FireworkController135"},
    },
	[136] = {
        ["controllers"] = {"ActivityExclusiveGiftController"},
    },
    [138] = {
        ["agents"] = {"Activity138Extension"},
        ["controllers"] = {"StarGameSigninController"},
    },
	[140] = {
        ["models"] = {"StarMatchModel","StarMatchSupportModel"},
        ["agents"] = {"Activity140Agent"},
        ["controllers"] = {"StarMatchController","StarFarewellController","StarGameAwardController"}
    },
	[143] = {
		["agents"] = {"Activity143Agent"},
		["controllers"] = {"StarQuestionController"},
		["models"] = {"StarQuestionModel"},
	},
    [144]={
        ["agents"] = {"Activity144Agent"},
        ["controllers"] = {"ArcheryGameController"},
    },
    [145] = {
        ["controllers"] = {"StarGameReporterController"},
        ["agents"] = {"StarGameReporterAgent"},
    },
    [146]={
        ["agents"] = {"Activity146Agent"},
    },

	[150] = {
        ["controllers"] = {"SnakeController"},
        ["agents"] = {"Activity150Agent"},
    },


    [152] = {
        ["controllers"] = {"StarGamePhotoAlbumController"},
        ["agents"] = {"Activity152Agent"},
    },
    [153] = {
        ["agents"] = {"Activity153Agent"},
        ["controllers"] = {"OpeningCeremonyCreditController"},
    },
    [156] = {
        ["agents"] = {"Activity156Agent"},
        ["controllers"] = {"OpeningCeremonyPlotTaskController"},
    },
	[157] = {
		["agents"] = {"Activity157Agent"},
		["controllers"] = {"GardenPartyGiftController"},
		["models"] = {"GardenPartyGiftModel"},
	},
    [158] = {
        ["agents"] = {"Activity158Agent"},
        ["controllers"] = {"OCBigHeadMakerController", "AirShipPartyController"},
        ["models"] = {"OCBigHeadMakerFriendListModel","OCBigHeadMakerFriendHeadListModel","OCBigHeadMakerModel"},
    },
    [159] = {
        ["agents"] = {"Activity159Agent"},
    },

    [160] = {
        ["agents"] = {"Activity160Agent"},
        ["controllers"] = {"StarWishController"}
    },

    [161] = {
        ["agents"] = {"TurnTableAgent"},
        ["controllers"] = {"FengHuaTurntableController", "TurnTableController"}
    },
	[163] = {
		["agents"] = {"DressRaceAgent"},
		["controllers"] = {"DressRaceController"},
		["models"] = {"DressRaceModel"}
	},
    [166] = {
        ["agents"] = {"Activity166Agent"},
    },
    [167] = {
        ["controllers"] = {"FullMoonController"},
    },
    [170] = {
        ["agents"] = {"Activity170Agent"},
    },

    [173] = {
        ["agents"] = {"Activity173Agent"},
        ["controllers"] = {"FlopGameController"}
    },
    [175] = {
        ["agents"] = {"Activity175Agent"},
        ["controllers"] = {"DecodeGameController"}
    },
    [176] = {
        ["controllers"] = {"WereWolfExperimentController","MiaoMiaoWuTaskController"}
    },
	[178] = {
		["agents"] = {"Activity178Agent"},
		["controllers"] = {"WerewolfFindDiffController","WerewolfStoryController"},
		["models"] = {"WerewolfFindDiffModel"}
	},

    [179] = {
        ["agents"] = {"BackflowAgent"},
    },
    [180] = {
        ["controllers"] = {"StarCollectController"},
        ["agents"] = {"Activity180Agent"},
    },
	[183] = {
		["controllers"] = {"HarvestSeasonController"},
		["agents"] = {"Activity183Agent"}
	},
    [188] = {
		["controllers"] = {"Anniversary14Controller"},
        ["agents"] = {"Activity188Agent"}
    },
    [189] = {
        ["controllers"] = {"HappyBreadController"},
        ["agents"] = {"Activity189Agent"}
    },
    [190] = {
        ["agents"] = {"Activity190Agent"}
    },
    [191] = {
        ["agents"] = {"Activity191Agent"},
        ["controllers"] = {"InviteNewbieController"},
        ["models"] = {"InviteNewbieModel"}
    },
    [192] = {
        ["agents"] = {"Activity192Agent"},
        ["controllers"] = { "QixiCollectController" },
    },
    [193] = {
        ["agents"] = {"Activity193Agent"},
    },
	[194] = {
		["agents"] = {"Activity194Agent"},
		["controllers"] = { "BlindBoxController" },
	},
	[195] = {
		["agents"] = {"Activity195Agent"},
	},
	[196] = {
        ["agents"] = {"Activity196Agent"},
    },
	[198] = {
		["agents"] = {"Activity198Agent"},
	},
	[199] = {
        ["agents"] = {"Activity199Agent"},
        ["controllers"] = {"TermbeginsController"},
    },
    [200] = {
        ["agents"] = {"Activity200Agent"},
        ["controllers"] = {"NationalDayController"},
    },
    [201] = {
        ["controllers"] = {"ActivityPreviewController", "ActivityPreviewController"},
    },
    [202] = {
		["agents"] = {"Activity202Agent"},
        ["controllers"] = {"LinkGameController","OpenServiceCountDownController", "OpenServiceCountDownController"},
		["models"] = {"LinkGameModel"},
    },
    [204] = {
        ["agents"] = {"Activity204Agent"},
        --2.0.3策划要求加红点自动刷新，174没有协议类，只能蹭一个
        ["controllers"] = {"JumpBoxController","GardenPartyStoryController"}
    },
    -- [205] = {
    --     ["agents"] = {"Active205Agent"},
    --     ["controllers"] = {"BeeDrawController"},
    --     ["models"] = {"BeeDrawModel"}
    -- },
    [207] = {
        ["agents"] = {"Activity207Agent"},
        ["models"] = {"SnowmanModel"},
        ["controllers"] = {"SnowmanGameController"},
    },
	[209] = {
		["agents"] = {"Activity209Agent"},
	},
	[211] = {
		["agents"] = {"Activity211Agent"},
		["controllers"] = {"NewYearTimeDownController"},
	},
    -- [213] = {
    --     ["agents"] = {"Activity213Agent"},
    --     ["controllers"] = {"NewYearHongBaoController"},
    -- },
    [214] = {
        ["agents"] = {"Activity214Agent"},
        ["controllers"] = {"HappyRestaurantController","HappyRestaurantLogicController",
            "HappyRestaurantSpecialNPCCtrl","HappyRestaurantTableSearchCtrl"},
    },
	[215] = {
		["agents"] = {"SF_AssemblyLineAgent"},
		["controllers"] = {"SF_AssemblyLineController"},
		["models"] = {"SF_AssemblyLineModel"}
	},
    -- [217] = {
    --     ["agents"] = {"Activity217Agent"},
	-- 	["controllers"] = {"LanternRiddleController"},
	-- 	["models"] = {"LanternRiddleModel"}
    -- },
	[218] = {
		["agents"] = {"Activity218Agent"},
		["controllers"] = {"ActivityCollectController"},
	},
	[219] = {
		["agents"] = {"Activity219Agent"},
		["controllers"] = {"SFController"},
	},

    [220] = {
		["agents"] = {"Activity220Agent"},
		["controllers"] = {"CCTVCardController"},
	},
    [221] = {
        ["agents"] = {"Activity221Agent"},
        ["models"] = {"CCTVActFatigueModel"},
        ["controllers"] = {"CCTVActFatigueController"}
    },
    [222] = {
        ["agents"] = {"Activity222Agent"},
    },
    [227] = {
		["agents"] = { "Activity227Agent" },
		["controllers"] = {"IncubateController"},
	},
    [228] = {
        ["agents"] = {"Activity228Agent"},
    },
    [229] = {
        ["agents"] = {"Activity229Agent"},
    },
    [231] = {
        ["models"] = {"Activity231Model"},
        ["agents"] = {"Activity231Agent"},
        ["controllers"] = {"Activity231Controller", "PhantomCreatureController"},
    },
    [232] = {
        ["agents"] = {"Activity232Agent"},
        ["controllers"] = {"PuzzleGameController"},
    },
	[233] = {
		["models"] = {"TangramModel"},
		["agents"] = {"Activity233Agent"},
		["controllers"] = {"TangramController"},
	},
    [234] = {
        ["agents"] = {"Activity234Agent"},
        ["controllers"] = {"MakeDreamTableController"},
    },
    [235] = {
        ["agents"] = {"Activity235Agent"},
    },
    [236] = {
        ["models"] = {"BallDropModel"},
        ["agents"] = {"Act236Agent"},
        ["controllers"] = {"BallDropController"},
    },
    [237] = {
        ["agents"] = {"Activity237Agent"},
    },

	[238] = {
		["agents"] = { "MusicPartyAgent" },
		["controllers"] = {"MusicPartyController"},
		["models"] = {"MusicPartyModel"},
	},
	[241] = {
		["agents"] = { "Activity241Agent" },
	},
    [242] = {
		["agents"] = { "Activity242Agent" },
		["controllers"] = {"LumberJackController"},
		["models"] = {"LumberJackModel"},
	},
    [243] = {
		["agents"] = { "Activity243Agent" },
	},
	[244] = {
		["agents"] = { "PleasanceCollectStampAgent" },
		["controllers"] = {"PleasanceCollectStampController"},
	},
	[245] = {
		["agents"] = { "Activity245Agent" },
	},
    [246] = {
        ["agents"] = { "Act246Agent" },
        ["models"] = { "CatchingInsectModel" },
		["controllers"] = { "CatchingInsectController" },
	},
    [247] = {
		["agents"] = { "Activity247Agent" },
		["controllers"] = { "PredownloadRewardsController" },
	},
    [249] = {
		["agents"] = { "Activity249Agent" },
	},
    [250] = {
		["agents"] = { "Activity250Agent" },
        ["controllers"] = { "RunningGameController" }
	},
    [251] = {
		["agents"] = { "Activity251Agent" },
        ["controllers"] = { "CarnivalPartyCakeController" }
	},
	[252] = {
        ["models"] = {"ThrowCakeModel"},
        ["agents"] = { "ThrowCakeAgent" },
        ["controllers"] = {"ThrowCakeController"},
    },
    [254] = {
        ["models"] = {"Activity254Model", "ProgressiveTurnTableModel"},
        ["agents"] = {"Activity254Agent"},
        ["controllers"] = {"Activity254Controller"},
    },
    --[255] = {
    --    ["controllers"] = { "AnniversaryGalaController" },
    --    ["agents"] = { "Activity255Agent" },
    --    ["models"] = { "AnniversaryGalaColorGridModel","AnniversaryGalaModel"},
    --},
    [256] = {
        ["models"] = {"Activity256Model"},
        ["agents"] = {"Activity256Agent"},
        ["controllers"] = {"Activity256Controller"},
    },
    [257] = {
        ["models"] = {"Activity257Model"},
        ["agents"] = {"Activity257Agent"},
        ["controllers"] = {"Activity257Controller"},
    },
    [258] = {
        ["agents"] = {"Activity258Agent"},
        ["controllers"] = {"BeautyMakerController"},
    },
	[259] = {
		["agents"] = {"Activity259Agent"},
		["controllers"] = {"RainbowTrailController"},
	},
    [260] = {
		["agents"] = {"Activity260Agent"},
        ["controllers"] = {"DrinksMakeingController"},
	},
    [261] = {
        ["agents"] = {"Activity261Agent"},
    },
    [262] = {
        ["agents"] = {"Activity262Agent"},
    },
    [263] = {
        ["models"] = {"Activity263Model"},
        ["agents"] = {"Activity263Agent"},
        ["controllers"] = {"Activity263Controller"},
    },
    [264] = {
        ["agents"] = {"Activity264Agent"},
    },
	[265] = {
		["controllers"] = {"InterstellarScienceController"},
		["models"] = {"InterstellarScienceModel"},
		["agents"] = {"Activity265Agent"},
	},
    [266] = {
		["controllers"] = {"FlappyBirdGameController"},
		["agents"] = {"Activity266Agent"},
	},
    [267] = {
        ["agents"] = {"IronHeartAgent"},
    },
    [268] = {
        ["agents"] = {"Activity268Agent"},
        ["models"] = {"MagpieFestivalModel"},
        ["controllers"] = {"MagpieFestivalController"},
    },
    [272] = {
        ["agents"] = {"Activity272Agent"},
        ["controllers"] = {"PoemFillingCotroller"},
    },
    [276] = {
        ["agents"] = {"Activity276Agent"},
    },
    [277] = {
        ["agents"] = {"Activity277Agent"},
        ["controllers"] = {"Activity277Controller"},
    },
	[278] = {
		["agents"] = {"Activity278Agent"},
		["controllers"] = {"TetrisController","TetrisUIController"},
		["models"] = {"TetrisModel","TetrisUIModel"},
	},
    [279] = {
        ["agents"] = {"Activity279Agent"},
        ["controllers"] = {"MusicBandCotroller"},
    },
    [280] = {
        ["agents"] = {"Activity280Agent"},
		["controllers"] = {"LanternRiddleController"},
		["models"] = {"LanternRiddleModel"}
    },
    [281] = {
        ["agents"] = {"Activity281Agent"},
        ["controllers"] = {"FullMoonCommemorationController"},
        ["models"] = {"FullMoonCommemorationModel"},
    },
    [282] = {
        ["agents"] = {"Act282Agent"},
		["controllers"] = {"CatchingInsectController"},
		["models"] = {"CatchingInsectModel"}
    },
    [283] = {
        ["agents"] = {"Activity283Agent"},
    },
    [284] = {
        ["models"] = {"Activity284Model"},
        ["agents"] = {"Activity284Agent"},
        ["controllers"] = {"Activity284Controller"},
    },
    [285] = {
        ["agents"] = {"Activity285Agent"},
        ["controllers"] = {"DWOLController"},
    },
	[286] = {
		["agents"] = {"Activity286Agent"},
		["controllers"] = {"MagicCrystalBottleController"},
	},
    [287] = {
        ["agents"] = {"Activity287Agent"},
		["controllers"] = {"RockingboatController"},
		["models"] = {"RockingboatModel"}
    },
	[288] = {
		["agents"] = {"Activity288Agent"},
		["controllers"] = {"CrossDressController"},
	},
    [289] = {
        ["agents"] = {"Activity289Agent"},
    },
    [290] = {
        ["agents"] = {"Act290Agent"},
    },
    [291] = {
        ["agents"] = {"Activity291Agent"},
    },
    [292] = {
        ["models"] = {"ResidentBandModel"},
        ["agents"] = {"Activity292Agent"},
        ["controllers"] = {"ResidentBandController"},
    },
    [293] = {
        ["agents"] = {"Activity293Agent"},
        ["controllers"] = {"TaskSignController"},
    },
    [294] = {
        ["agents"] = {"Activity294Agent"},
        ["controllers"] = {"PumpTurtleController"},
    },
    [295] = {
        ["agents"] = {"Activity295Agent"},
        ["controllers"] = {"WealthGodActivityController"},
    },
    [296] = {
        ["agents"] = {"EcoParkRechargeAgent"},
    },
	[297] = {
		["agents"] = {"Activity297Agent"},
		["controllers"] = {"DIYCanvasBagController"},
		["models"] = {"DIYCanvasBagModel"},
	},
    [298] = {
        ["agents"] = {"Activity298Agent"},
    },
    [299] = {
        ["agents"] = {"Activity299Agent"},
    },
    [401] = {
        ["agents"] = {"Activity401Agent"},
    },
    [403] = {
        ["agents"] = {"Activity403Agent"},
        ["controllers"] = {"ConnectLineGameController"},
        ["models"] = {"ConnectLineGameModel"},
    },
    [404] = {
        ["agents"] = {"Activity404Agent"},
        ["controllers"] = {"EraseFlowerController"},
        ["models"] = {"Activity404Model"},
    },
    [405] = {
        ["agents"] = {"Activity405Agent"},
        ["controllers"] = {"CollectCardController"},
        -- ["models"] = {"Activity404Model"},
    },
    [406] = {
        ["agents"] = {"Activity406Agent"},
    },
    [407] = {
        ["agents"] = {"Activity407Agent"},
    },
    [408] = {
        ["agents"] = {"Activity408Agent"},
        ["controllers"] = {"MinesWeeperController"},
    },
    [409] = {
        ["agents"] = {"Activity409Agent"},
        ["controllers"] = {"TempleBeneficenceController"},
    },
    [410] = {
        ["agents"] = {"SeasonChainBagAgent"}
    },
    [411] = {
        ["agents"] = {"Activity411Agent"},
        ["controllers"] = {"PuzzleBobbleController"}
    },
    [412] = {
        ["agents"] = {"Act412Agent"},
        ["controllers"] = {"TwoZeroFourEightController"}
    },
    [414] = {
        ["agents"] = {"Activity414Agent"},
        ["controllers"] = {"BuildAobiSquareController"}
    },
    [415] = {
        ["agents"] = {"Activity415Agent"},
    },
    [416] = {
        ["agents"] = {"Activity416Agent"},
        ["controllers"] = {"SeaMileStoneMgr"},
        ["models"] = {"SeaMileStoneModel"},
    },
    [417] = {
        ["agents"] = {"Activity417Agent"},
        ["controllers"] = {"FindTheItemController"},
        ["models"] = {"Activity417Model"}
    },
	[418] = {
		["agents"] = {"Activity418Agent"},
		["controllers"] = {"CashRedEnvelopeController"},
	},
    [419] = {
        ["agents"] = {"Activity419Agent"},
        ["controllers"] = {"SeaDanceController"},
    },
    [420] = {
        ["agents"] = {"Activity420Agent"},
        ["models"] = {"ClientGameModel","SeaGameViewModel"},
        ["controllers"] = {"SmallSeaSpiritController"},
    },
	[421] = {
		["agents"] = {"Activity421Agent"},
		["controllers"] = {"DonateActController"},
	},
    [422] = {
		["controllers"] = {"RoadSideBakerController"},
	},
    [423] = {
        ["agents"] = {"Activity423Agent"},
        ["controllers"] = {"PetCircusController", "PetCircusObstacleController"},
        ["models"] = {"Activity423Model"}
    },
    [424] = {
        ["agents"] = {"Activity424Agent"},
        ["controllers"] = {"MergeWatermelonController"},
        ["models"] = {"MergeWatermelonModel","MergeWatermelonActModel"},
    },
    [425] = {
        ["agents"] = {"Activity425Agent"},
    },
    [426] = {
        ["agents"] = {"Act426Agent"},
    },
    [427] = {
        ["models"] = {"PetBattlesModel"},
        ["controllers"] = {"PetBattlesController"},
        ["agents"] = {"Activity427Agent"},
    },
    [428] = {
        ["models"] = {"SeaModel"},
        ["controllers"] = {"SeaBossController"},
        ["agents"] = {"Activity428Agent"},
    },
    [429] = {
        ["agents"] = {"Activity429Agent"},
    },
    [430] = {
        ["agents"] = {"Activity430Agent"},
    },
    [431] = {
        ["agents"] = {"Activity431Agent"},
        ["controllers"] = {"UnrealAreaController"},
    },
    [432] = {
        ["agents"] = {"Activity432Agent"}
    },
    [433] = {
        ["agents"] = {"Activity433Agent"},
        ["controllers"] = {"SeaSpaCarController"},
    },
    [434] = {
        ["agents"] = {"Activity434Agent"},
    },
	[435] = {
		["agents"] = {"Activity435Agent"},
		["controllers"] = {"Activity435Controller"},
	},
    [436] = {
        ["controllers"] = { "BreakoutGameController"},
        ["agents"] = {"BreakoutAgent"},
        ["models"] = {"BreakoutModel"},
    },
    [437] = {
        ["agents"] = {"Activity437Agent"},
    },
    [438] = {
        ["models"] = {"Activity438Model"},
        ["agents"] = {"Activity438Agent"},
        ["controllers"] = {"Activity438Controller"},
    },
    [439] = {
        ["agents"] = {"Activity439Agent"},
        ["controllers"] = { "LandlordController"},
    },
	[440] = {
		["agents"] = {"Activity440Agent"},
		["models"] = {"IceSkatingGameModel"},
		["controllers"] = { "IceSkatingController", "IceSkatingGameController"},
	},
    [441] = {
        ["models"] = {"Activity441Model"},
        ["agents"] = {"Activity441Agent"},
        ["controllers"] = {"Activity441Controller"},
    },
    [442] = {
        ["agents"] = {"Activity442Agent"},
    },
    [444] = {
        ["controllers"] = {"LongStreetBanquetController"},
        ["agents"] = {"LongStreetBanquetAgent"},
        ["models"] = {"LongStreetBanquetModel"}
    },
    [445] = {
        ["agents"] = {"Activity445Agent"},
        ["controllers"] = { "BonfirePartyController"},
    },
    [446] = {
        ["agents"] = {"Activity446Agent"},
    },
    [447] = {
        ["agents"] = {"Activity447Agent"},
        ["models"] = {"UpgradeRewardModel"},
    },
    [448] = {
        ["agents"] = {"Activity448Agent"},
        ["models"] = {"XiaoXiaoLeModel","XiaoXiaoLeGameModel","XiaoXiaoLeCardModel"},
        ["controllers"] = {"XiaoXiaoLeController","XiaoXiaoLeLevelController"},
    },
    [449] = {
        ["models"] = {"Activity449Model"},
        ["agents"] = {"Activity449Agent"},
        ["controllers"] = {"Activity449Controller"},
    },
    [451] = {
        ["agents"] = {"Activity451Agent"},
    },
    [452] = {
        ["agents"] = {"Activity452Agent"},
    },
    [453] = {
        ["agents"] = {"Activity453Agent"},
        ["controllers"] = {"ActShareStreetTaskController"},
    },
    [454] = {
        ["agents"] = {"Activity454Agent"},
        ["controllers"] = {"ActivityWelfareController"},
    },
    [455] = {
        ["agents"] = {"Activity455Agent"},
    },
    [456] = {
        ["agents"] = {"PacManGameAgent"},
        ["controllers"] = {"PacManGameController"},
        ["models"] = {"PacManModel"},
    },
    [457] = {
        ["agents"] = {"Activity457Agent"},
    },
    [463] = {
        ["agents"] = {"Activity463Agent"},
        ["controllers"] = {"LiarsBarGameController"},
        ["models"] = {"LiarsBarModel"},
    },
    [458] = {
        ["agents"] = {"Activity458Agent"},
    },
    [461] = {
        ["models"] = {"Activity461Model"},
        ["agents"] = {"Activity461Agent"},
        ["controllers"] = {"Activity461Controller"},
    },
    [462] = {
        ["agents"] = {"Activity462Agent"},
        ["controllers"] = {"FlyingChessGameController"},
        ["models"] = {"FlyingChessGameModel"},
    },
    [464] = {
        ["agents"] = {"Activity464Agent"},
    },
    [465] = {
        ["agents"] = {"Activity465Agent"},
    },
    [466] = {
        ["agents"] = {"Activity466Agent"},
    },
    [467] = {
        ["agents"] = {"Act467Agent"},
        ["controllers"] = {"ExploreGameController"},
        ["models"] = {"Act467Model"},
    },
    [469] = {
        ["agents"] = {"Activity469Agent"},
        ["controllers"] = {"GamePromoteController"},
        ["models"] = {"GamePromoteModel"},
    },
    [471] = {
        ["models"] = {"Activity471Model"},
        ["agents"] = {"Activity471Agent"},
        ["controllers"] = {"Activity471Controller"},
    },
    [2001] = {
        ["models"] = {"GuideModel"},
        ["configs"] = { "GuideConfig" },
    },
    [2002] = {
        ["configs"] = { "TaskCmdSettingConfig", "TaskSubmitToNpcConfig"}
    },
    [2003] = {
        ["controllers"] = {"RoleDialogueController"}
    },
    [2005] = {
        ["models"] = {"CommonHUDViewModel", "DownTabModel"},
        ["controllers"] = {
            "CommonHUDController",
            "DownTabController",
            "CommonHUDFilter_Scene",
            "CommonHUDFilter_EditMode",
            "CommonHUDFilter_CropMode",
            "CommonHUDFilter_FishMode",
            "CommonHUDFilter_PhotoMode",
            "CommonHUDFilter_NewbieTaskMode",
            "CommonHUDFilter_GameMode",
            "CommonHUDFilter_PetFeedMode",
            "CommonHUDFilter_TreasureMode",
            "CommonHUDFilter_DoublePoseMode",
            "CommonHUDFilter_ElfBehMode",
            "CommonHUDFilter_ThrowItemMode",
            "CommonHUDFilter_HappyFightMode",
            "CommonHUDFilter_AffinityCheckMode",
            "CommonHUDFilter_MistyForestMode",
            "CommonHUDFilter_MistyForestTeamMode",
            "CommonHUDFilter_WerewolfMode",
            "CommonHUDFilter_FriendCheckBreezeModel",
            "CommonHUDFilter_FriendCheckShimmerMode",
            "CommonHUDFilter_AirShipMode",
            "CommonHUDFilter_GameRoomWaitingSceneMode",
            "CommonHUDFilter_PartyLimit",
            "CommonHUDFilter_MoveHouseMode",
            "CommonHUDFilter_MusicPartyMode",
            "CommonHUDFilter_AobiReturnPartyLimit",
            "CommonHUDFilter_ColorGrid",
            "CommonHUDFilter_GalaScene",
            "CommonHUDFilter_ElfRoomLimit",
            "CommonHUDFilter_EcoParkLimit",
            "CommonHUDFilter_SeaLimit",
            "CommonHUDFilter_WealthGodMode",
            "CommonHUDFilter_SeaDanceModel",
            "CommonHUDFilter_BonfirePartyModel",
            "CommonHUDFilter_CelebPartyLimit"
        }
    },
    [2006] = {
        ["controllers"] = {"IslanderEventController"},
        ["models"] = {"FindItemModel","StoneGiantTalkModel","IslanderEventBookModel"}
    },
    [2007] = {
        ["controllers"] = {"AffinityController"},
    },
    [2008] = {
        ["controllers"] = {"RelativesBookController",},
        ["models"] = {"RelativesBookModel"}
    },
    [2009] = {
        ["controllers"] = {"RelativeBlessingController"},
    },
    [2010] = {
        ["controllers"] = {"DreamLikeController", "DreamLikeConversationCtrl"},
        ["models"] = {"DreamLikeModel"},
    },
    [2011] = {
        ["controllers"] = {"UniversalLinkController", "AppleRedeemController"}
    },
    [2012] = {
        ["controllers"] = {"LoginPopUpController"}
    },
    [2013] = {
        ["controllers"] = {"TapTapViewController", "TapTapViewController"},
    },
    [2014] = {
        ["controllers"] = {"GameMachineController"},
    },
	[2015] = {
		["controllers"] = {"ActivityRabbitGiftController"},
	},
	[2016] = {
		["controllers"] = {"RainbowTrailController"},
	},
	[2017] = {
		["controllers"] = {"CityPartyController"},
	},
	[2018] = {
		["controllers"] = {"TransformSkinController"},
	},
    [2019] = {
		["controllers"] = {"WeatherUmbrellaController"},
	},
    [4094] = {
        ["controllers"] = { "InGameUpdateController" },
    },
    --native sdk相关controller
    [4095] = {
        ["models"] = {"FuncModel"},
    },
	[10001] = {
        ["agents"] = {"WishingTreeAgent"},
        ["controllers"] = {"WishingTreeController"}
    },
	[10086] = {
		["controllers"] = {"AnimalWorldController","ArtShowController"},
	},
	[20001] = {
        ["agents"] = { "TinyModuleAgent"},
    },
    [23333] = {
        ["agents"] = { "GMAgent" },
        ["models"] = {"GMTaskModel"},
        -- ["configs"] = { "RoomConfig" },
        -- ["models"] = { "RoomModel", "HourseModel", "RoomFurnitureModel", "RoomEditModel", "RoomHeroUpgradeModel", "RoomHeroFoodModel", "RoomVisitModel", "RoomHeroModel", "RoomEditFurnitureModel" },
        -- ["controllers"] = { "GMController" },
    },
}
return setting_extension