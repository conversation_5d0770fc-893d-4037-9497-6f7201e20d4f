module("logic.extensions.system.view.sploading.DreamRoomLoadingPanelPresentor",package.seeall)
local DreamRoomLoadingPanelPresentor = class("DreamRoomLoadingPanelPresentor", ViewPresentor)


function DreamRoomLoadingPanelPresentor:dependWhatResources()
	return {"ui/system/dreamroomloadingpanel.prefab"}
end

function DreamRoomLoadingPanelPresentor:attachToWhichRoot()
	return ViewRootType.TopMost
end

function DreamRoomLoadingPanelPresentor:buildViews()
	return {DreamRoomLoadingPanel.New()}
end


return DreamRoomLoadingPanelPresentor