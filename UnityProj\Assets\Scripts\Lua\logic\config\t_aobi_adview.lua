-- {excel:H活动配置表.xlsx, sheetName:export_奥比广场广告图}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_aobi_adview", package.seeall)

local title = {id=1,sortId=2,activityDefineId=3,functionId=4,channel=5,startTime=6,endTime=7,clickFun=8,clickParams=9,path=10}

local dataList = {
	{1, 3, 0, 0, {110001,210009,610001,610002}, "2025-07-03T05:00:00", "2025-07-23T23:59:59", "activity", "32", "aobiad_5.prefab"},
	{2, 1, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-23T23:59:59", "gotoActivityById", "2476", "aobiad_1.prefab"},
	{3, 2, 0, 0, nil, "2025-07-03T05:00:00", "2025-07-30T23:59:59", "openView", "LifeGuideView", "aobiad_2.prefab"},
	{4, 5, 0, 0, {110001,210009,610001,610002}, "2025-01-22T05:00:00", "2025-02-18T23:59:59", "activity", "64", "aobiad_3.prefab"},
	{5, 6, 0, 0, {110001,210009,610001,610002}, "2025-01-22T10:00:00", "2025-02-12T23:59:59", "activity", "31", "aobiad_6.prefab"},
	{6, 4, 0, 0, nil, "2025-07-17T05:00:00", "2025-07-30T23:59:59", "gotoActivityById", "2496", "aobiad_7.prefab"},
	{7, 7, 0, 0, {110001,210009,610001,610002}, "2025-05-01T12:00:00", "2025-07-16T23:59:59", "activity", "73", "aobiad_4.prefab"},
	{8, 7, 0, 0, {110001,210009,610001,610002}, "2025-06-24T14:00:00", "2025-07-02T23:59:59", "activity", "31", "aobiad_8.prefab"},
}

local t_aobi_adview = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
}

t_aobi_adview.dataList = dataList
local mt
mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_aobi_adview