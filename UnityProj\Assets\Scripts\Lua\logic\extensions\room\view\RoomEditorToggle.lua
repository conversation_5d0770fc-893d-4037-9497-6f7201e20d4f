module("logic.extensions.room.view.RoomEditorToggle", package.seeall)
local RoomEditorToggle = class("RoomEditorToggle", ViewComponent)

RoomEditorToggle.BtnFilter0 = "ui/scene/room/btnfurtoggle0.prefab"
RoomEditorToggle.BtnFilter1 = "ui/scene/room/btnfurtoggle1.prefab"
RoomEditorToggle.BtnFilter2 = "ui/scene/room/btnfurtoggle2.prefab"
RoomEditorToggle.BtnFilter3 = "ui/scene/room/btnfurtoggle3.prefab"
RoomEditorToggle.BtnFilter4 = "ui/scene/room/btnfurtoggle4.prefab"

local blockKey = "RoomEditorToggleBlock"
local HeighterY = 388.41
local LowerY = 222.24
local HideInHeigherMode = {
	"horRight/btnSave", "horRight/btnCancel"
}

function RoomEditorToggle:buildUI()
	self._tgCon0 = self:getGo("verBottom/bg/leftGo")
	self._tgCon1 = self:getGo("verBottom/buttonGroup/bottomGo/Viewport/content")
	self._tg0 = ToggleGroup.New(handler(self._onSelectType0, self))
	self._tg1 = ToggleGroup.New(handler(self._onSelectType1, self), false, true)
	self._tg2 = ToggleGroup.New(handler(self._onSelectType2, self))
	self._viewPortWidth = goutil.getWidth(self:getGo("verBottom/buttonGroup/bottomGo/Viewport").transform)
	self._viewport = self:getGo("verBottom/buttonGroup/bottomGo/Viewport/content")
	self._scrollRect = self:getGo("verBottom/buttonGroup/bottomGo"):GetComponent(typeof(UnityEngine.UI.ScrollRect))
	self:getBtn("verBottom/buttonGroup/btnShow"):AddClickListener(self._onClickShow, self)
	self._bg = self:getGo("verBottom/bg")
	self._off = self:getGo("verBottom/off")
	self._on = self:getGo("verBottom/on")
end

function RoomEditorToggle:onExit()
	UpdateBeat:Remove(self._moveTg1, self)
	UpdateBeat:Remove(self._moveTg2, self)
	self._selectType1 = nil
	self:_clearTG(self._tg0)
	self:_clearTG(self._tg1)
	self:_clearTG(self._tg2)
	self:unregisterLocalNotify(RoomNotifyName.ClickFurniture, self._onSelectFurniture, self)
	self:unregisterLocalNotify(RoomNotifyName.ChangeFurnitureToMultiList, self._onSelectFurniture, self)
	self:unregisterLocalNotify(RoomNotifyName.CancelClickFurniture, self._resetBtnSaveAndCancel, self)
	self:unregisterLocalNotify(RoomNotifyName.PlaceSampleFurniture, self._resetBtnSaveAndCancel, self)
	self:unregisterLocalNotify(RoomNotifyName.OnCancelSelectMultFurniture, self._resetBtnSaveAndCancel, self)
	RoomEditModel.instance:unregisterModeChange(RoomEditModel.Mode.Surface, self._resetBtnSaveAndCancel, self)
end

function RoomEditorToggle:onEnter()
	self:setBgLower(false)
	self._types0 = self:_createToggleView(self._tg0, self._tgCon0, - 1, false)
	local views = self._tg0:getViews()
	goutil.findChild(views[#views], "imgLine"):SetActive(false)
	self:_resetSelect(true, self:getFirstParam() [3])
	self._isBgLower = true
	self:registerLocalNotify(RoomNotifyName.ClickFurniture, self._onSelectFurniture, self)
	self:registerLocalNotify(RoomNotifyName.ChangeFurnitureToMultiList, self._onSelectFurniture, self)
	self:registerLocalNotify(RoomNotifyName.CancelClickFurniture, self._resetBtnSaveAndCancel, self)
	self:registerLocalNotify(RoomNotifyName.PlaceSampleFurniture, self._resetBtnSaveAndCancel, self)
	self:registerLocalNotify(RoomNotifyName.OnCancelSelectMultFurniture, self._resetBtnSaveAndCancel, self)
	RoomEditModel.instance:registerModeChange(RoomEditModel.Mode.Surface, self._resetBtnSaveAndCancel, self)
end

function RoomEditorToggle:backToMain()
	self:selectType({1, 1})
end

function RoomEditorToggle:selectType(selectedIndexs)
	self._selectedIndexs = selectedIndexs
	self._tg0:clickViews(true, selectedIndexs[1])
end

function RoomEditorToggle:_resetSelect(isInit, selectedType)
	if RoomEditModel.instance:checkMode(RoomEditModel.Mode.Paper) then
		local furTypes = self._viewPresentor:getFurTypes(0)
		local index = 1
		for i = 1, #furTypes do
			if RoomConfig.isPaperType(furTypes[i].id) then
				index = i
				break
			end
		end
		self:selectType({1, index + 1})
	elseif isInit then
		if selectedType ~= nil then
			local typeDef = FurnitureConfig.getPartDefine(selectedType)
			local indexs = {}
			while typeDef ~= nil do
				table.insert(indexs, 1, self:_findTypeIndex(typeDef, self._viewPresentor:getFurTypes(typeDef.parentId)))
				typeDef = FurnitureConfig.getPartDefine(typeDef.parentId)
			end
			self:selectType(indexs)
		else
			self:selectType({1, 1})
		end
	else
		self:selectType({1, 1})
	end
end

function RoomEditorToggle:_findTypeIndex(target, list)
	local hasAll = target.parentId ~= - 1 and FurnitureConfig.getPartDefine(target.parentId).parentId == - 1 and target.parentId ~= 16 and target.parentId ~= 54
	for i, part in ipairs(list) do
		if part.id == target.id then
			return i +(hasAll and 1 or 0)
		end
	end
	return 1
end

function RoomEditorToggle:_createToggleView(toggle, container, parentId, needAll)
	self:_clearTG(toggle)
	if needAll then
		local view = self:_createView(container, 1, 0, lang("全部"))
		toggle:addView(view)
	end
	local parts = self._viewPresentor:getFurTypes(parentId)
	for i, part in ipairs(parts) do
		local view = self:_createView(container, part.tgType, part.id, part.name, part.redKey)
		toggle:addView(view)
	end
	return parts
end

function RoomEditorToggle:_createView(container, tgType, urlId, name, redKeys)
	local view = self:getResInstance(RoomEditorToggle["BtnFilter" .. tgType])
	goutil.setActive(view, true)
	goutil.addChildToParent(view, container)
	goutil.setActive(goutil.findChild(view, "redPoint"), false)
	if redKeys ~= nil and goutil.findChild(view, "redPoint") ~= nil then
		RedPointController.instance:registerRedPoint(goutil.findChild(view, "redPoint"), redKeys)
	end
	self:_setIcon(view, "icon", urlId)
	self:_setIcon(view, "imgSelect/icon", urlId)
	self:_setIcon(view, "imgUnSelect/icon", urlId)
	self:_setText(view, "imgSelect/txtName", name)
	self:_setText(view, "imgUnSelect/txtName", name)
	return view
end

function RoomEditorToggle:_onSelectType0(index, isSelected)
	if not isSelected then return end
	self._hasTotal = self._types0[index].id ~= 16 and self._types0[index].id ~= 54
	self._types1 = self:_createToggleView(self._tg1, self._tgCon1, self._types0[index].id, self._hasTotal)
	self._selectTg1View = nil
	if self._selectedIndexs and self._selectedIndexs[2] then
		self._tg1:clickViews(true, self._selectedIndexs[2])
	else
		self._tg1:clickViews(true, 1)
	end
end

function RoomEditorToggle:_onSelectType1(index, isSelected)
	UpdateBeat:Remove(self._moveTg2, self)
	if not self._hasTotal or index ~= 1 then
		if self._selectTg1View then
			-- goutil.findChild(self._selectTg1View, "btnGo"):SetActive(false)
			goutil.findChild(self._selectTg1View, "btnGo1"):SetActive(false)
			goutil.findChild(self._selectTg1View, "imgGo"):SetActive(false)
		end
		self._selectTg1View = self._tg1:getViews() [index]
		self._tgCtrlCon2 = goutil.findChild(self._selectTg1View, "btnGo1")
		local container = goutil.findChild(self._selectTg1View, "btnGo1/btnGo")
		-- self._tgCtrlCon2 = goutil.findChild(self._selectTg1View, "btnGo")
		-- local container = self._tgCtrlCon2
		local selectIndex = index -(self._hasTotal and 1 or 0)
		if self._types1[selectIndex] ~= self._selectType1 or not self._showSubType then
			self._showSubType = true
			self._selectType1 = self._types1[selectIndex]
			self._types2 = self:_createToggleView(self._tg2, container, self._selectType1.id)
			self._targetWidthTg2 = #self._types2 * 70 +(#self._types2 - 1) * 10 + 25
			goutil.setWidth(container.transform, self._targetWidthTg2)
			goutil.setWidth(self._tgCtrlCon2.transform, 0)
			if #self._types2 > 0 then
				UpdateBeat:Add(self._moveTg2, self)
			end
		else
			self._showSubType = false
			self._types2 = nil
		end
		local active = self._showSubType and self._types2 ~= nil and #self._types2 > 0
		self._tgCtrlCon2:SetActive(active)
		goutil.findChild(self._selectTg1View, "imgGo"):SetActive(active)
	else
		self:_clearTG(self._tg2)
		self._showSubType = false
		self._types2 = nil
		self._selectType1 = self._types0[self._tg0:getIndex()]
		if self._selectTg1View then
			goutil.findChild(self._selectTg1View, "btnGo1"):SetActive(false)
			goutil.findChild(self._selectTg1View, "imgGo"):SetActive(false)
		end
	end
	if self._selectedIndexs and self._selectedIndexs[3] then
		self._tg2:clickViews(true, self._selectedIndexs[3])
	else
		if self._showSubType and self._selectType1.id == 42 then
			self._tg2:clickViews(true, 1)
		else
			self._tg2:setIndex(0)
			self:_notifySelect()
		end
		-- self._tg2:clickViews(true, 1)
	end
	if self._showSubType then
		settimer(0, function()
			if self._tg1 == nil or self._tg1:getViews() == nil then
				UpdateBeat:Remove(self._moveTg1, self)
				return
			end
			local focusView = self._tg1:getViews() [self._tg1:getIndex()]
			if focusView == nil then
				UpdateBeat:Remove(self._moveTg1, self)
				return
			end
			self._targetFocusX = GameUtils.getLocalPos(focusView).x
			UpdateBeat:Add(self._moveTg1, self)
		end, nil, false)
	else
		UpdateBeat:Remove(self._moveTg1, self)
	end
end

function RoomEditorToggle:_moveTg1()
	if self._scrollRect == nil then return end
	local current = self._scrollRect.horizontalNormalizedPosition
	local target = math.max(0, math.min(1, self._targetFocusX /(goutil.getWidth(self._viewport.transform) - self._viewPortWidth)))
	local isEnd = math.abs(current - target) < 0.001
	if isEnd then
		self._scrollRect.horizontalNormalizedPosition = target
		UpdateBeat:Remove(self._moveTg1, self)
	else
		self._scrollRect.horizontalNormalizedPosition = current +(target - current) / 3
	end
end
function RoomEditorToggle:_moveTg2()
	if self._tgCtrlCon2 == nil then return end
	local current = goutil.getWidth(self._tgCtrlCon2.transform)
	local isEnd = self._targetWidthTg2 - current < 0.01
	if isEnd then
		goutil.setWidth(self._tgCtrlCon2.transform, self._targetWidthTg2)
		UpdateBeat:Remove(self._moveTg2, self)
	else
		goutil.setWidth(self._tgCtrlCon2.transform, current +(self._targetWidthTg2 - current) / 3)
	end
end
function RoomEditorToggle:_onSelectType2(index, isSelected)
	if not isSelected or index == nil then return end
	self:_notifySelect()
end

function RoomEditorToggle:_notifySelect()
	local selectedType
	if self._types2 and self._tg2:getIndex() ~= 0 then
		selectedType = self._types2[self._tg2:getIndex()]
	elseif not self._hasTotal or self._types1 and self._tg1:getIndex() ~= 1 then
		local selectIndex = self._tg1:getIndex() -(self._hasTotal and 1 or 0)
		selectedType = self._types1[selectIndex]
	else
		selectedType = self._types0[self._tg0:getIndex()]
	end
	print("_notifySelect :", selectedType.id, selectedType.name)
	self._viewPresentor:onSelectSubType(selectedType, true)
	self._selectedIndexs = nil
	if not self._preventFocusCamera then
		if selectedType.focus then
			TaskCamCmdHelper.moveCam(selectedType.focus.x, selectedType.focus.y, 0.3)
		elseif self._lastSelectedType and self._lastSelectedType.focus then
			local cfg = SceneConfig.getSceneConfig(SceneManager.instance:getCurSceneId())
			local pos = cfg:getBornPos()
			TaskCamCmdHelper.moveCam(pos[1], pos[2], 0.3)
		end
	end
	self._preventFocusCamera = false
	self._lastSelectedType = selectedType
end

function RoomEditorToggle:_clearTG(tgGroup)
	for _, view in ipairs(tgGroup:getViews()) do
		if goutil.findChild(view, "redPoint") ~= nil then
			RedPointController.instance:unregisterRedPoint(goutil.findChild(view, "redPoint"))
		end
		goutil.destroy(view)
	end
	tgGroup:clear()
end

function RoomEditorToggle:_setIcon(view, path, urlId)
	local icon = goutil.findChild(view, path)
	if icon then
		IconLoader.setAtlasToImg(icon, "atlas/furnituretype.spriteatlas", urlId)
	end
end

function RoomEditorToggle:_setText(view, path, desc)
	if goutil.findChild(view, path) then
		goutil.findChildTextComponent(view, path).text = desc
	end
end

function RoomEditorToggle:_onClickShow()
	if self._isBgLower then
		self:setBgHeigher(true)
	else
		self:setBgLower(true)
	end
end

function RoomEditorToggle:setBgHeigher(showMovie)
	if not self._isBgLower then return end
	self._isBgLower = false
	self:_resetBtnSaveAndCancel()
	if showMovie then
		self:_setMovie(false)
	else
		self._on:SetActive(true)
		self._off:SetActive(false)
		-- tfutil.SetLY(self._bg, HeighterY)
		goutil.setHeight(self._bg.transform, HeighterY)
		self._viewPresentor:setIsLower(false)
	end
end

function RoomEditorToggle:setBgLower(showMovie)
	if self._isBgLower then return end
	self._isBgLower = true
	self._on:SetActive(false)
	self._off:SetActive(true)
	self:_resetBtnSaveAndCancel()
	self._viewPresentor:setIsLower(true)
	if showMovie then
		self:_setMovie(true)
	else
		goutil.setHeight(self._bg.transform, LowerY)
		-- tfutil.SetLY(self._bg, LowerY)
	end
end

function RoomEditorToggle:_onSelectFurniture()
	self:_resetBtnSaveAndCancel()
	if self._lastSelectedType and RoomConfig.isOutSideType(self._lastSelectedType.id) then
		self._preventFocusCamera = false
		self:backToMain()
	end
end

function RoomEditorToggle:_resetBtnSaveAndCancel()
	local multiList = RoomEditFurnitureModel.instance:getFurnitureMultiList()
	local hasEditFurniture = multiList ~= nil and #multiList > 0
	if not hasEditFurniture then
		multiList = SceneManager.instance:getCurScene().unitFactory:getRoomSuite():getComponent(RoomCompSample)._furnitureMos
		hasEditFurniture = multiList ~= nil and #multiList > 0
	end
	local isSurfaceMode = RoomEditModel.instance:checkMode(RoomEditModel.Mode.Surface)
	local isBuyMode = RoomEditModel.instance:checkMode(RoomEditModel.Mode.Buy)
	for _, path in ipairs(HideInHeigherMode) do
		self:getGo(path):SetActive(not hasEditFurniture and self._isBgLower and not isSurfaceMode and not isBuyMode)
	end
end

function RoomEditorToggle:_setMovie(isLower)
	ViewBlockMgr.instance:blockClick(true, blockKey)
	PjAobi.DOTweenHelper.UpdateValue(isLower and HeighterY or LowerY, isLower and LowerY or HeighterY, 0.3, self._updateHeigh, self)
	:SetEase(DG.Tweening.Ease.OutQuint)
	:OnComplete(function()
		-- self._bg.transform:DOLocalMoveY(isLower and LowerY or HeighterY, 0.3):OnComplete(function()
		ViewBlockMgr.instance:blockClick(false, blockKey)
		self._on:SetActive(not isLower)
		self._off:SetActive(isLower)
		self._viewPresentor:setIsLower(isLower)
	end)
end

function RoomEditorToggle:_updateHeigh(value)
	goutil.setHeight(self._bg.transform, value)
end

return RoomEditorToggle 