module("logic.extensions.room.view.RoomEditorSorter", package.seeall)
local RoomEditorSorter = class("RoomEditorSorter", ViewComponent)

RoomEditorSorter.ItemUrl = "ui/scene/room/editorsorteritem.prefab"

RoomEditorSorter.SortType = {
	Quality = 1, GainTime = 2, Quantity = 3, Size = 4, Comfort = 5
}

local SorterConfig = {
	{name = lang("默认排序"), func = "_sortByQuality", type = RoomEditorSorter.SortType.Quality, showMode = {RoomEditModel.Mode.Edit, RoomEditModel.Mode.Search}},
	{name = lang("获取时间"), func = "_sortByGainTime", type = RoomEditorSorter.SortType.GainTime, showMode = {RoomEditModel.Mode.Edit, RoomEditModel.Mode.Search}},
	{name = lang("数量"), func = "_sortByOwnNum", type = RoomEditorSorter.SortType.Quantity, showMode = {RoomEditModel.Mode.Edit}},
	{name = lang("舒适度"), func = "_sortComfort", type = RoomEditorSorter.SortType.Comfort, showMode = {RoomEditModel.Mode.Edit, RoomEditModel.Mode.Search}},
	{name = lang("大小"), func = "_sortArea", type = RoomEditorSorter.SortType.Size, showMode = {RoomEditModel.Mode.Edit, RoomEditModel.Mode.Search}},
}

function RoomEditorSorter:buildUI()
	self._sorterList = self:getGo("verBottom/buttonGroup/btnSort/sortGo/sorter/list")
	self._btnSort = self:getBtn("verBottom/buttonGroup/btnSort")
	self._unSelect = self:getGo("verBottom/buttonGroup/btnSort/unSelect")
	self._select = self:getGo("verBottom/buttonGroup/btnSort/select")
	self._sorterGo = self:getGo("verBottom/buttonGroup/btnSort/sortGo")
	self:_buildSorterList()
end

function RoomEditorSorter:_buildSorterList()
	self._tgGroup = ToggleGroup.New(handler(self._onSelectSorter, self), false, true)
	local view
	for i = 1, #SorterConfig do
		view = self:getResInstance(RoomEditorSorter.ItemUrl)
		goutil.addChildToParent(view, self._sorterList)
		view.transform:SetSiblingIndex(i - 1)
		goutil.findChildTextComponent(view, "imgSelect/txt").text = SorterConfig[i].name
		goutil.findChildTextComponent(view, "imgUnSelect/txt").text = SorterConfig[i].name
		self._tgGroup:addView(view)
	end
end

function RoomEditorSorter:bindEvents()
	self._btnSort:AddClickListener(self._onClickSorter, self)
	Framework.UIGlobalTouchTrigger.Get(self._sorterGo):AddIgnoreTargetListener(self._hidekSorter, self)
end

function RoomEditorSorter:unbindEvents()
end

function RoomEditorSorter:onExit()
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnEditModeChange, self._onModeChange, self)
end

function RoomEditorSorter:onEnter()
	RoomController.instance:registerLocalNotify(RoomNotifyName.OnEditModeChange, self._onModeChange, self)
	self._curIndex = nil
	self._tgGroup:clickViews(true, 1)
	self._isShowSorter = true
	self:_hidekSorter()
end

function RoomEditorSorter:_onClickSorter()
	if self._isShowSorter then
		self:_hidekSorter()
	else
		self:_showSorter()
	end
end

function RoomEditorSorter:_showSorter()
	if self._isShowSorter then return end
	self._isShowSorter = true
	self._sorterGo:SetActive(true)
end

function RoomEditorSorter:_hidekSorter()
	if not self._isShowSorter then return end
	self._isShowSorter = false
	self._sorterGo:SetActive(false)
end

function RoomEditorSorter:_onModeChange()
	local curMode = RoomEditModel.instance._mode
	if curMode == RoomEditModel.Mode.Search then
		if self._tgGroup:getIndex() == 3 then
			self._tgGroup:clickViews(true, 1)
		end
	end
	for i, config in ipairs(SorterConfig) do
		self._tgGroup:getViews() [i]:SetActive(checknumber(table.indexof(config.showMode, curMode)) > 0)
	end
end

function RoomEditorSorter:getSortType()
	return SorterConfig[self._curIndex].type
end

function RoomEditorSorter:sortData(list)
	if #list < 2 then return list end
	local sorterFunc = SorterConfig[self._curIndex].func
	table.sort(list, function(a, b)
		return RoomEditorSorter[sorterFunc](a, b, self._reverse)
	end)
	return list
end

function RoomEditorSorter:_onSelectSorter(index, isSelected)
	if not isSelected then return end
	if self._curIndex == index then
		self._reverse = not self._reverse
	else
		self._reverse = false
	end
	self._curIndex = index
	self._curSorter = SorterConfig[index]
	self:_resetSorterView()
	self._viewPresentor:onSorterChange()
	
	self._unSelect:SetActive(self._curIndex == 1 and not self._reverse)
	self._select:SetActive(self._curIndex ~= 1 or self._reverse)
end

function RoomEditorSorter:_resetSorterView()
	local tgView = self._tgGroup:getViews() [self._curIndex]
	local imgTgReverse = goutil.findChild(tgView, "imgSelect/imgReverse")
	GameUtils.setLocalRotation(imgTgReverse, self._reverse and 180 or 0, 0, 0)
end

function RoomEditorSorter._sortByOwnNum(a, b, reverse)
	if a.isShare and not b.isShare then
		return false
	elseif not a.isShare and b.isShare then
		return true
	end
	if ItemService.instance:getNewState(a.id) ~= ItemService.instance:getNewState(b.id) then
		return ItemService.instance:getNewState(a.id)
	end
	if a.num == nil or b.num == nil then
		return a.id > b.id
	end
	if b.num == a.num then
		return a.id > b.id
	end
	if reverse then
		return b.num > a.num
	else
		return a.num > b.num
	end
end

function RoomEditorSorter._sortArea(a, b, reverse)
	if a.isShare and not b.isShare then
		return false
	elseif not a.isShare and b.isShare then
		return true
	end
	if ItemService.instance:getNewState(a.id) ~= ItemService.instance:getNewState(b.id) then
		return ItemService.instance:getNewState(a.id)
	end
	local ax, ay = RoomConfig.getFurnitureSize(a.id)
	local bx, by = RoomConfig.getFurnitureSize(b.id)
	local areaA, areaB = ax * ay, bx * by
	if areaA == areaB then
		return a.id > b.id
	end
	if reverse then
		return areaB > areaA
	else
		return areaA > areaB
	end
end

function RoomEditorSorter._sortByQuality(a, b, reverse)
	if a.isShare and not b.isShare then
		return false
	elseif not a.isShare and b.isShare then
		return true
	end
	if ItemService.instance:getNewState(a.id) ~= ItemService.instance:getNewState(b.id) then
		return ItemService.instance:getNewState(a.id)
	end
	local qa = ItemService.instance:getRank(a.id)
	local qb = ItemService.instance:getRank(b.id)
	if qa == qb then
		return a.id > b.id
	end
	if reverse then
		return qa < qb
	else
		return qa > qb
	end
end

function RoomEditorSorter._sortByGainTime(a, b, reverse)
	if a.isShare and b.isShare then
		return a.id > b.id
	elseif a.isShare and not b.isShare then
		return false
	elseif not a.isShare and b.isShare then
		return true
	end
	if ItemService.instance:getNewState(a.id) ~= ItemService.instance:getNewState(b.id) then
		return ItemService.instance:getNewState(a.id)
	end
	local ta = ItemService.instance:getItem(a.id).gainTime
	local tb = ItemService.instance:getItem(b.id).gainTime
	if ta == tb then
		return a.id > b.id
	end
	if reverse then
		return ta < tb
	else
		return ta > tb
	end
end

function RoomEditorSorter._sortComfort(a, b, reverse)
	if a.isShare and not b.isShare then
		return false
	elseif not a.isShare and b.isShare then
		return true
	end
	if ItemService.instance:getNewState(a.id) ~= ItemService.instance:getNewState(b.id) then
		return ItemService.instance:getNewState(a.id)
	end
	local cA, cB = a:getDefine().comfort, b:getDefine().comfort
	if cA == cB then
		return a.id > b.id
	end
	if reverse then
		return cB > cA
	else
		return cA > cB
	end
end

return RoomEditorSorter 