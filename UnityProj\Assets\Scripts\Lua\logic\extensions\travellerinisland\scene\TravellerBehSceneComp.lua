module("logic.extensions.travellerinisland.scene.TravellerBehSceneComp", package.seeall)

local TravellerBehSceneComp = class("TravellerBehSceneComp", SceneComponentBase)

--等到scene里面的所有组件创建完毕后，再调用各个组件的onInit方法
--在这里可以监听其他组件感兴趣的通知等
function TravellerBehSceneComp:onInit()
end

--进入场景，等stage加载完毕时回调
function TravellerBehSceneComp:onEnterSceneFinished(sceneId, bornX, bornZ)
	if HouseModel.instance:isInIsland(1) or HouseModel.instance:isInHouse(true, 1) then
		if HouseModel.instance:_isMine() then
			self._travellerModel = TravellerModel.instance
		else
			self._travellerModel = TravellerModel.New()
			self._travellerModel:onInit()
		end
		RoomController.instance:registerLocalNotify(RoomNotifyName.AllNotInScreenFurnitureLoaded, self._onBuildRoom, self)
	end
end

--退出场景时的处理回调
function TravellerBehSceneComp:onExitScene()
	if HouseModel.instance:isInIsland(1) or HouseModel.instance:isInHouse(true, 1) then
		RoomController.instance:unregisterLocalNotify(RoomNotifyName.AllNotInScreenFurnitureLoaded, self._onBuildRoom, self)
		FarmController.instance:unregisterLocalNotify(FarmNotifyName.OnBeginPlayHarvest, self._onHarvest, self)
		FarmController.instance:unregisterLocalNotify(FarmNotifyName.OnUseFSTProp, self._onUseFSTProp, self)
		ElfSceneController.instance:unregisterLocalNotify(ElfLocalNotify.OnClickPlayElfBtn, self._onPlayWithElf, self)
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerShortActionChange, self._onPlayerShortAction, self)
		TravellerController.instance:unregisterLocalNotify(TravellerNotify.ChangeScene, self._onTravellerChangeScene, self)
		self._npcList = nil
	end
end

function TravellerBehSceneComp:_onBuildRoom()
	self._npcList = {}
	self:_onInitData()
end

function TravellerBehSceneComp:getTravellerModel()
	return self._travellerModel
end

function TravellerBehSceneComp:_onInitData()
	TravellerController.instance:registerLocalNotify(TravellerNotify.ChangeScene, self._onTravellerChangeScene, self)
	RoomController.instance:unregisterLocalNotify(RoomNotifyName.AllNotInScreenFurnitureLoaded, self._onBuildRoom, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.OnBeginPlayHarvest, self._onHarvest, self)
	FarmController.instance:registerLocalNotify(FarmNotifyName.OnUseFSTProp, self._onUseFSTProp, self)
	ElfSceneController.instance:registerLocalNotify(ElfLocalNotify.OnClickPlayElfBtn, self._onPlayWithElf, self)
	SceneController.instance:registerLocalNotify(SceneNotify.PlayerShortActionChange, self._onPlayerShortAction, self)
	local allTravellers = HouseModel.instance:getUserProperty(HouseUserProperty.Travellers, {})
	local randomTravellers = {}
	for i = 1, #allTravellers do
		local travellerMo = self._travellerModel:getTravellerMO(allTravellers[i].travellerId)
		if not HouseModel.instance:isMyHouse() then
			travellerMo:setDataFromServer(allTravellers[i])
		elseif travellerMo:isGoIsland() then
			table.insert(randomTravellers, travellerMo)
		end
		self:addTraveller(travellerMo.id)
	end
	if #randomTravellers > 0 then
		if SettingService.instance:getShowNameSetting(SettingService.ShowTravellerTips) then
			TravellerController.instance:setTravellerTips(randomTravellers)
			LoginPopUpController.instance:addPopupAndPop(handler(TravellerController.instance.checkTravellerTips, TravellerController.instance))
		end
	end
	TravellerController.instance:localNotify(TravellerNotify.OnCreateTravellerUnit)
	-- if HouseModel.instance:isMyHouse() then
	-- 	self:randomTravellerOnIsland()
	-- end
end

--闲时上岛
function TravellerBehSceneComp:randomTravellerOnIsland()
	local limit = tonumber(GameUtils.getCommonConfig("TravellerGoIslandLimit"))
	local num = math.random(1, limit)
	local lst = self:getTravellerModel():getSortedTravellerIdList()
	if num > #lst then
		num = #lst
	end
	local res = arrayutil.randomAndSubArray(lst, num)
	for i=1,#res do
		self:addTraveller(res[i])
	end
end

function TravellerBehSceneComp:startBehavior(travellerId, type, params, callBack)
	local npcUnit = self._npcList[travellerId]
	if not npcUnit then return end
	npcUnit.travellerBehavior:startBehavior(type, params, callBack)
end

function TravellerBehSceneComp:rebuildTravelelr()
	if self._npcList == nil then return end
	for id, traveller in pairs(self._npcList) do
		local suc, pos = RoomFacade.instance:getFreePosForSceneObject(1, 1)
		if suc then
			traveller:setPos(pos.x, pos.y)
		end
	end
end

function TravellerBehSceneComp:addTraveller(travellerId, isLanding)
	if self._npcList[travellerId] ~= nil then
		if isLanding then
			self._npcList[travellerId].travellerBehavior:stopBehavior()
			self._npcList[travellerId].travellerBehavior:startBehavior(TravellerBehaviorType.LandingIsland)
		end
		return
	end
	local x, y = 1000, 1000
	if not isLanding then
		local suc, pos = RoomFacade.instance:getFreePosForSceneObject(1, 1)
		if suc then
			x, y = pos.x, pos.y
		end
	end
	local traveller = self:getTravellerModel():getTravellerMO(travellerId)
	-- local unit = self._scene.unitFactory:addUnit(SceneUnitType.Npc, {id = traveller.config.npcId, travellerMo = traveller, x = x, y = y, skinName = traveller:getDressId(), isLanding = isLanding})
	local unit = self._scene.npcMgr:_addUnit({id = traveller.config.npcId, travellerMo = traveller, pos = {x, y}, skinName = traveller:getDressId(), isLanding = isLanding, actionId = 1})
	self._npcList[travellerId] = unit
	unit:addClickListener(self._onClickTraveller, self)
end

function TravellerBehSceneComp:travellerLanding(travellerId)
	local sceneId = RoomConfig.getShowAreaIdBySceneId(SceneManager.instance:getCurSceneId())
	TravellerAgent.instance:sendChangeSceneRequest({{tId = travellerId, sId = sceneId}})
end

function TravellerBehSceneComp:travellerLeaving(travellerId, sceneId)
	TravellerAgent.instance:sendChangeSceneRequest({{tId = travellerId, sId = sceneId}})
end

function TravellerBehSceneComp:_onTravellerChangeScene(travellerId, newSceneId, oldSceneId)
	local curSceneId = RoomConfig.getShowAreaIdBySceneId(SceneManager.instance:getCurSceneId())
	print(11111111111, travellerId, newSceneId, oldSceneId, curSceneId)
	if newSceneId == curSceneId then
		self:addTraveller(travellerId, true)
	elseif oldSceneId == curSceneId then
		self:removeTraveller(travellerId, true, newSceneId)
	end
end

function TravellerBehSceneComp:getTraveller(travellerId)
	return self._npcList[travellerId]
end

function TravellerBehSceneComp:_onClickTraveller(npcUnit)
	if not npcUnit.travellerBehavior:handleClick() then
		if SceneController.instance:stopWalk() then
			npcUnit.travellerBehavior:startBehavior(TravellerBehaviorType.Click)
		end
	end
end

function TravellerBehSceneComp:removeTraveller(travellerId, isLeave, nextSceneId)
	if self._npcList[travellerId] == nil then return end
	if isLeave then
		local unit = self._npcList[travellerId]
		unit.travellerBehavior:startBehavior(TravellerBehaviorType.LeaveIsland, {nextSceneId = nextSceneId})
	else
		local traveller = TravellerConfig.getTraveller(travellerId)
		-- self._scene.unitFactory:removeUnit(SceneUnitType.Npc, traveller.npcId)
		self._scene.npcMgr:_removeUnit({id = traveller.npcId})
		self._npcList[traveller.id] = nil
	end
end

function TravellerBehSceneComp:_onHarvest(plantMo, unit)
	local config = TravellerConfig.getBehaviorById(TravellerBehaviorType.PlayerHarvest)
	local suc = table.indexof(config.params.plantType:splitToNumber(","), plantMo.cfg.subType)
	if not suc then return end
	self:_startBehIfNearUnit(TravellerBehaviorType.PlayerHarvest, unit:getPos())
end

function TravellerBehSceneComp:_onUseFSTProp(unit)
	self:_startBehIfNearUnit(TravellerBehaviorType.TouchFST, unit:getPos())
end

function TravellerBehSceneComp:_onPlayWithElf(pos)
	self:_startBehIfNearUnit(TravellerBehaviorType.TouchElf, pos.x, pos.y)
end

function TravellerBehSceneComp:_onPlayerShortAction(state, params)
	if state ~= PlayerActionType.Pose.typeId and state ~= PlayerActionType.Throw.typeId then return end
	local playerUnit = SceneManager.instance:getCurScene():getUserPlayer()
	local pose = params[1]
	local animation
	local config = TravellerConfig.getBehaviorById(TravellerBehaviorType.PlayerAction)
	for key, value in pairs(config.params) do
		if tostring(key) == pose then
			animation = value
			break
		end
	end
	if animation == nil then
		TravellerBehaviorUtil.printInfo("没有找到玩家动作对应行为：", state, pose)
		return
	end
	local maxDistance = tonumber(config.distance)
	for travellerId, npcUnit in pairs(self._npcList) do
		local helper = npcUnit.view:getAnimHelper()
		if helper and helper.adapter:HasAnimation(animation) then
			if self:_getDistanceToNpc(npcUnit, playerUnit:getPos()) <= maxDistance then
				self:startBehavior(travellerId, config.id, {state = animation, lookToUnit = playerUnit})
			end
		else
			TravellerBehaviorUtil.printInfo(string.format("玩家做了动作:{1}，旅行者没有相应动作:{2}", pose, animation))
		end
	end
end

function TravellerBehSceneComp:_startBehIfNearUnit(type, targetX, targetY)
	local config = TravellerConfig.getBehaviorById(type)
	local maxDistance = tonumber(config.distance)
	local result = {}
	for travellerId, npcUnit in pairs(self._npcList) do
		if self:_getDistanceToNpc(npcUnit, targetX, targetY) <= maxDistance then
			self:startBehavior(travellerId, type)
		end
	end
end

function TravellerBehSceneComp:_getDistanceToNpc(npcUnit, unitPosX, unitPosY)
	local npcPosX, npcPosY = npcUnit:getPos()
	return Vector2.Distance(Vector2.New(unitPosX, unitPosY), Vector2.New(npcPosX, npcPosY))
end

return TravellerBehSceneComp 