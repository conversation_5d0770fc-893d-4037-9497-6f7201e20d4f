module("logic.extensions.sharestreet.view.ShareStreetCreateViewPresentor",package.seeall)
---@class ShareStreetCreateViewPresentor
local ShareStreetCreateViewPresentor = class("ShareStreetCreateViewPresentor",ViewPresentor)

function ShareStreetCreateViewPresentor:ctor()
	ShareStreetCreateViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetCreateViewPresentor:dependWhatResources()
	return {"ui/street/createstreetview.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetCreateViewPresentor:buildViews()
	self._view = ShareStreetCreateView.New()
	return {self._view}
end

--- 配置view所在的ui层
function ShareStreetCreateViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end

function ShareStreetCreateViewPresentor:onClickOutside()
	self._view:close()
end


return ShareStreetCreateViewPresentor