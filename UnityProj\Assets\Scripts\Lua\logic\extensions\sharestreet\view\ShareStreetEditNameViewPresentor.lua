module("logic.extensions.sharestreet.view.ShareStreetEditNameViewPresentor",package.seeall)
---@class ShareStreetEditNameViewPresentor
local ShareStreetEditNameViewPresentor = class("ShareStreetEditNameViewPresentor",ViewPresentor)

function ShareStreetEditNameViewPresentor:ctor()
	ShareStreetEditNameViewPresentor.super.ctor(self)
end

--- 配置view需要的资源列表
function ShareStreetEditNameViewPresentor:dependWhatResources()
	return {"ui/street/editstreetnamepanel.prefab"}
end

--- view第一次初始化时会执行，在这里创建并返回view的ViewComponent
function ShareStreetEditNameViewPresentor:buildViews()
	return {ShareStreetEditNameView.New()}
end

--- 配置view所在的ui层
function ShareStreetEditNameViewPresentor:attachToWhichRoot()
	return ViewRootType.Popup
end


return ShareStreetEditNameViewPresentor