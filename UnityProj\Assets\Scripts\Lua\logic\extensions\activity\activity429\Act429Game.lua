module("logic.extensions.activity.activity429.Act429Game", package.seeall)

local Act429Game = class("Act429Game", ViewComponent)

function Act429Game:buildUI()
	self._items = {}
	local container, dragTrigger, clickTrigger
	for i = 1, Activity429Model.getComposeAreaWidth() * Activity429Model.getComposeAreaLength() do
		container = self:getGo("itemGo/item_" .. i)
		table.insert(self._items, {
			container = container,
			icon = self:getGo(string.format("itemGo/item_%d/icon", i)),
			imgOk = self:getGo(string.format("itemGo/item_%d/imgOk", i)),
			levelGo = self:getGo(string.format("itemGo/item_%d/levelGo", i))
		})
		dragTrigger = Framework.UIDragTrigger.Get(container)
		dragTrigger:AddBeginDragListener(self.onDragBegin, self, {i})
		dragTrigger:AddDragListener(self.onDraging, self, {i})
		dragTrigger:AddEndDragListener(self.onDragEnd, self, {i})
		clickTrigger = Framework.UIClickTrigger.Get(container)
		clickTrigger:AddClickListener(self._onClickItem, self, {i})
		Framework.UIGlobalTouchTrigger.Get(container):AddIgnoreTargetListener(self._hideLevel, self)	
	end
	self._dragingGo = self:getGo("itemGo/material")

	local config = CommonConfig.getConfig("act_429_order") [Activity429Model.instance:getActivityId()][1].reward[1]
	self:getText("tipTxtGo/txt_2").text = config.count
	IconLoader.setIconForItem(self:getGo("tipTxtGo/imgIcon_3"), config.id)

	config = CommonConfig.getConfig("act_429_order") [Activity429Model.instance:getActivityId()][2].reward[1]
	self:getText("tipTxtGo/txt_4").text = config.count
	IconLoader.setIconForItem(self:getGo("tipTxtGo/imgIcon_1"), config.id)
end

function Act429Game:_hideLevel()
	if self._symble then
		return
	end
	if self._selectItem then
		self._selectItem.levelGo:SetActive(false)
		self._selectItem = nil
	end
end

function Act429Game:_onClickItem(evt, params)
	if self._selectItem then
		self._selectItem.levelGo:SetActive(false)
		self._selectItem = nil
	end
	local item = Activity429Model.instance:getItemInfoByIndex(params[1])
	if item == nil then return end
	self._selectItem = self._items[params[1]]
	self._selectItem.levelGo:SetActive(true)
	self._symble = true
	settimer(0.1, function()
		self._symble = false
	end, nil, false)
end

function Act429Game:onEnter()
	self:getBtn("middleactview/btnDice"):AddClickListener(self.onClickDice, self)
	self:getBtn("middleactview/btnBuyDice"):AddClickListener(self.onClickReward, self)
	IconLoader.setIconForItem(self:getGo("middleactview/btnDice/icon"), Activity429Model.getItemId())
	self:getText("middleactview/btnDice/txtCount").text = Activity429Model.getItemNum()
	self:getBtn("middleactview/tipsGo/btnTips"):AddClickListener(self.onClickTips, self)
	self._putInGo = self:getGo("btnPutin")
	self._dragingGo:SetActive(false)
	Activity429Model.instance:getDataFromServer(self.onGetData, self)
	RedPointController.instance:setLastClick("activity429")

	RedPointController.instance:registerRedPoint(self:getGo("middleactview/btnBuyDice/redPoint"), {"activity429Reward", "activity429Task"})
	RedPointController.instance:registerRedPoint(self:getGo("middleactview/btnDice/redPoint"), {"activity429Item"})
end

function Act429Game:onExit()
	RedPointController.instance:unregisterRedPoint(self.self:getGo("middleactview/btnBuyDice/redPoint"))
	RedPointController.instance:unregisterRedPoint(self:getGo("middleactview/btnDice/redPoint"), {"activity429Item"})
end

function Act429Game:onClickTips()
	ViewMgr.instance:open("HelpView", {"yindao_cmhc_01"})
end

function Act429Game:onGetData()
	self._viewPresentor.itemChangehelper:addItem("middleactview/barCoin", tonumber(CommonConfig.getConfig("act_429_common") [Activity429Model.instance:getActivityId()]["CoinId"].value))
	self._viewPresentor.itemChangehelper:addItem("middleactview/barDiamond", Activity429Model.getItemId())
	for i, gridItem in ipairs(Activity429Model.instance.gridItems) do
		self:_setItem(gridItem.index, gridItem.itemId)
	end
	FuncUnlockFacade.instance:checkAndShowGuide(33)
	if not LocalStorage.instance:getValue(StorageKey.Act429MainHelp) then
		self:onClickTips()
		LocalStorage.instance:setValue(StorageKey.Act429MainHelp, true)
	end
end

function Act429Game:_setItem(index, itemId, playAnim)
	local viewDict = self._items[index]
	viewDict.icon:SetActive(itemId ~= nil)
	viewDict.levelGo:SetActive(false)
	viewDict.imgOk:SetActive(false)
	if itemId ~= nil then
		local itemDefine = Activity429Model.getItemDefineById(itemId)
		for i = 1, 5 do
			goutil.findChild(viewDict.levelGo, "img_" .. i):SetActive(i == itemDefine.level)
		end
		IconLoader.setAtlasToImg(viewDict.icon, "atlas/act429item.spriteatlas", itemDefine.icon)
		viewDict.imgOk:SetActive(Activity429Model.instance:canSubmit(itemId) ~= false)
		if playAnim then
			DOTweenHelper.ZoomEffect(viewDict.container)
			if self._effect == nil then
				self._effect = self:getResInstance(Act429MainPresentor.EffectUrl)
			end
			self._effect:SetActive(false)
			self._effect:SetActive(true)
			goutil.addChildToParent(self._effect, viewDict.container)
			SoundManager.instance:playEffect(141423)
			settimer(3, function()
				goutil.setActive(viewDict.levelGo, false)
			end, nil, false)
		elseif self._effect ~= nil then
			viewDict.levelGo:SetActive(false)
		end
	end
end

function Act429Game:onClickDice()
	if ItemService.instance:getItemNum(Activity429Model.getItemId()) >= Activity429Model.getItemNum() then
		Activity429Model.instance:createItem(self._onCreate, self)
	else
		Act429RewardView.show()
	end
end

function Act429Game:onClickReward()
	Act429RewardView.show()
end

function Act429Game:_onCreate(item)
	self:_setItem(item.index, item.itemId, true)
end

function Act429Game:onDragBegin(eventData, params)
	if ActivityHelper.closeViewOnActivityClosed(429, self._viewPresentor.viewName, true) then
		return
	end
	local item = Activity429Model.instance:getItemInfoByIndex(params[1])
	if item == nil then return end
	self._dragingIndex = params[1]
	local itemDefine = Activity429Model.getItemDefineById(item.itemId)
	IconLoader.setAtlasToImg(goutil.findChild(self._dragingGo, "icon"), "atlas/act429item.spriteatlas", itemDefine.icon)
	goutil.findChild(self._dragingGo, "imgOk"):SetActive(Activity429Model.instance:canSubmit(item.itemId) ~= false)
	
	for i = 1, 5 do
		goutil.findChild(self._dragingGo, "levelGo/img_" .. i):SetActive(i == itemDefine.level)
	end

    self:_setDragGoPos(eventData.position)
	local itemView = self._items[params[1]]
	itemView.icon:SetActive(false)
	itemView.imgOk:SetActive(false)
	itemView.levelGo:SetActive(false)
	self._dragingGo:SetActive(true)
end

function Act429Game:onDraging(eventData, params)
	if self._dragingIndex == nil then return end
    self:_setDragGoPos(eventData.position)
end

function Act429Game:_setDragGoPos(screenPoint)
	local uiRectTransform = ViewMgr.instance._uiRoot.transform
	local b, pos = UnityEngine.RectTransformUtility.ScreenPointToWorldPointInRectangle(uiRectTransform, screenPoint, CameraTargetMgr.instance:getUICameraTarget():getCamera(), Vector3.New(0, 0, 0))
	GameUtils.setPos(self._dragingGo, pos.x, pos.y, 0)
end

function Act429Game:_isHitGo(screenPoint, gameObject)
	return Framework.GeometryUtil.RectContainsScreenPoint(screenPoint, ViewMgr.instance:getUICanvas(), gameObject.transform, CameraTargetMgr.instance:getUICameraTarget():getCamera())
end

function Act429Game:onDragEnd(eventData, params)
	if ActivityHelper.closeViewOnActivityClosed(429, self._viewPresentor.viewName, true) then
		return
	end
	if self._dragingIndex == nil then return end
	self._dragingGo:SetActive(false)
	local screenPoint = eventData.position
	if self:_isHitGo(screenPoint, self._putInGo) then
		Activity429Model.instance:submit(self._dragingIndex, self._onSubmit, self)
	else
		local hitIndex
		for i, item in ipairs(self._items) do
			if self:_isHitGo(screenPoint, item.container) then
				hitIndex = i
				break
			end
		end
		local secondItem = Activity429Model.instance:getItemInfoByIndex(hitIndex)
		if hitIndex and self._dragingIndex ~= hitIndex then
			Activity429Model.instance:compose(self._dragingIndex, hitIndex, self._onCompose, self)
		else
			local item = Activity429Model.instance:getItemInfoByIndex(self._dragingIndex)
			self:_setItem(item.index, item.itemId)
		end
	end
	self._dragingIndex = nil
end

function Act429Game:_onCompose(firstIndex, secondIndex, playAnim)
	local firstItem = Activity429Model.instance:getItemInfoByIndex(firstIndex)
	local secondItem = Activity429Model.instance:getItemInfoByIndex(secondIndex)
	self:_setItem(firstIndex, firstItem and firstItem.itemId, playAnim)
	self:_setItem(secondIndex, secondItem and secondItem.itemId, playAnim)
end

function Act429Game:_onSubmit(suc, index)
	local itemId
	if not suc then
		itemId = Activity429Model.instance:getItemInfoByIndex(index).itemId
	end
	self:_setItem(index, itemId)
end

return Act429Game 