module("logic.extensions.sharestreet.facade.ShareStreetFacade", package.seeall)

local ShareStreetFacade = class("ShareStreetFacade")

function ShareStreetFacade:ctor()

end

function ShareStreetFacade:showView()
    if not FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.ShareStreet, true) then
        return
    end
    ShareStreetController.instance:getMyInfo(handler(function()
        if not ShareStreetModel.instance:hasCreatedStreet() then
            ViewMgr.instance:open("ShareStreetCreateView")
            return
        end
        self:_showMapView()
    end, self))
end

function ShareStreetFacade:showMapView(ownerId)
    ShareStreetController.instance:getInfo(ownerId, false, handler(function()
        self:_showMapView(ownerId)
    end, self))
end

function ShareStreetFacade:getCanShareFurnitures()
    local items = ItemService.instance:getItems(ItemType.FURNITURE)
    local canShareItems = {}
    for i=1, #items do
        local item = items[i]
        if item.num > 0 and item:getDefine().canShare then
            table.insert(canShareItems, item)
        end
    end
    print("canShareItems", #items, #canShareItems)
    return canShareItems
end

function ShareStreetFacade:_showMapView(ownerId)
    ViewMgr.instance:open("ShareStreetMapView", ownerId or UserInfo.userId)
end

function ShareStreetFacade:showDetailView(ownerId)
    ShareStreetController.instance:getInfo(ownerId, true, handler(function(_, info)
        ViewMgr.instance:open("ShareStreetDetailView", info)
    end, self), true)
end
---邀请好友加入界面
function ShareStreetFacade:showInviteFriendView()
    ViewMgr.instance:open("ShareStreetInviteView", true)
end
---处理加入申请界面
function ShareStreetFacade:showAcceptInviteView()
    ViewMgr.instance:open("ShareStreetInviteView", false)
end
---受邀界面
function ShareStreetFacade:showAcceptJoinRequestView()
    ViewMgr.instance:open("ShareStreetJoinView", true)
end
---申请界面
function ShareStreetFacade:showMyJoinRequestView()
    ViewMgr.instance:open("ShareStreetJoinView", false)
end
---带排行的其它街区页面
function ShareStreetFacade:showRankView()
    ViewMgr.instance:open("ShareStreetRankView")
end
---街区成员界面
function ShareStreetFacade:showStreetMemberView(ownerId)
    ViewMgr.instance:open("ShareStreetMemberView")
end

---进入我的街区场景
function ShareStreetFacade:enterMyStreetScene(callbackHandler)
    if not FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.ShareStreet, true) then
        if callbackHandler then
            callbackHandler(false)
        end
        return
    end
    if ShareStreetModel.instance:isStreetInfoInited() then
        local result = self:_enterMyStreetScene()
        if callbackHandler then
            callbackHandler(result)
        end
    else
        ShareStreetController.instance:getMyInfo(handler(function()
            local result = self:_enterMyStreetScene()
            if callbackHandler then
                callbackHandler(result)
            end
        end, self))
    end
end

function ShareStreetFacade:_enterMyStreetScene()
    if ShareStreetModel.instance:hasCreatedStreet() then
        SceneManager.instance:loadScene(109)
        return true
    else
        FlyTextManager.instance:showFlyText(lang("请先创建街区"))
        return false
    end
end

function ShareStreetFacade:isInShareStreet()
    return SceneManager.instance:getCurSceneId() == 109
end

function ShareStreetFacade:isInMyStreet()
    return HouseModel.instance:_isMine() and (HouseModel.instance:isInIsland(HouseModel.Block) or HouseModel.instance:isInHouse(true, HouseModel.Block))
end

function ShareStreetFacade:setDefaultScene2ShareStreet(bool)
    if bool then
        LocalStorage.instance:setValue(StorageKey.DefaultEnterSceneId, 109)
    else
        LocalStorage.instance:setValue(StorageKey.DefaultEnterSceneId, 5)
    end
end

function ShareStreetFacade:isSet2DefaultScene()
    return LocalStorage.instance:getValue(StorageKey.DefaultEnterSceneId) == 109
end

function ShareStreetFacade:checkUserHaveStreet(userId, callbackHandler)
    ShareStreetAgent.instance:sendGetUserJoinedStreetIdRequest(userId, handler(function(_, streetId)
        if callbackHandler then
            callbackHandler(not string.nilorempty(streetId))
        end
    end, self))
end

ShareStreetFacade.instance = ShareStreetFacade.New()
return ShareStreetFacade
