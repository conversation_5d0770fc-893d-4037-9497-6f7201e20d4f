module("logic.extensions.sharestreet.view.ShareStreetInviteItem", package.seeall)

local ShareStreetInviteItem = class("ShareStreetInviteItem", ListBinderCell, BaseLuaComponent)

--- 注意该类有两个面板共享一个资源, 每个面板有两种状态, 所以有四种状态
function ShareStreetInviteItem:Awake()
    self._goHeadIcon = self:getGo("imgHeadIcon")
    self._txtVisitCount = self:getText("state_1/countGo/txt_2")
    self._goBtnContainer = self:getGo("state_1/btns")
    self._btnGo = self:getBtn("state_1/btns/btnGo")
    self._btnOver = self:getBtn("state_1/btns/btnOver")
    self._goNull = self:getGo("txtNull")
    self._txtLikeCount = self:getText("state_1/countGo/txt_3")
    self._btnOk = self:getBtn("state_1/btns/btnOk")
    self._btnCancel = self:getBtn("state_1/btns/btnCancel")
    self._txtStreetName = self:getText("state_1/streetName/nameTxt")
    self._btnInvite = self:getBtn("state_1/btns/btnInvite")
    self._txtRoleName = self:getText("state_1/nameGo/nameTxt")
    self._btnRefuse = self:getBtn("state_1/btns/btnShield")
    self._txtAreaInfo = self:getText("state_1/countGo/txt_1")
    self._goVip = self:getGo("state_1/nameGo/icon/img_1")
    self._goNotVip = self:getGo("state_1/nameGo/icon/img_2")
    
    self._btnMore = self:getBtn("btnMore")
    self._btnReport = self:getBtn("moreGo/objReport")
    self._goMore = self:getGo("moreGo")
end

function ShareStreetInviteItem:onSetMo(mo)
    self._mo = mo
    self._streetInfo = mo.no -- isInviteFriend时为FriendStreetInfoNo, 其它模式时为StreetSimpleInfoNO
    self._roleInfo = mo.roleInfo
    self._isInviteFriend = mo.isInviteFriend
    self._isAcceptInvite = mo.isAcceptInvite
    self._isGetApplyList = mo.isGetApplyList
    self._isJoinRequest = mo.isJoinRequest

    HeadPortraitHelper.instance:setHeadPortraitWithUserId(self._goHeadIcon, self._roleInfo.id)
    self._txtRoleName.text = self._roleInfo.nickname
    self._txtStreetName.text = self._streetInfo.name
    self._txtAreaInfo.text = string.format("%s/%s", self._streetInfo.memberCount, self._streetInfo.areaCount)
    self._txtVisitCount.text = self._streetInfo.visitCount
    self._txtLikeCount.text = self._streetInfo.likeCount

    self._goVip:SetActive(self._roleInfo.vipCardType > 0)
    self._goNotVip:SetActive(self._roleInfo.vipCardType <= 0)

    self:_updateBtns()

    self._btnGo:AddClickListener(self._onClickGo, self)
    self._btnInvite:AddClickListener(self._onClickInvite, self)
    self._btnOk:AddClickListener(self._onClickOk, self)
    self._btnCancel:AddClickListener(self._onClickCancel, self)
    self._btnRefuse:AddClickListener(self._onClickRefuse, self)
    self._btnMore:AddClickListener(self._onClickMore, self)
    self._btnReport:AddClickListener(self._onClickReport, self)
end

function ShareStreetInviteItem:_updateBtns()
    TaskUtil.unactiveAllChildren(self._goBtnContainer)
    self._goNull:SetActive(false)
    if self._isInviteFriend then
        local hasStreet = self._streetInfo.hasStreet
        if not hasStreet then
            self._goNull:SetActive(true)
            self._btnMore.gameObject:SetActive(false)
        else
            self._btnGo.gameObject:SetActive(true)
            local isInvited = self._mo.isInvited
            self._btnInvite.gameObject:SetActive(not isInvited)
            self._btnOver.gameObject:SetActive(isInvited)
            self._btnMore.gameObject:SetActive(true)
        end
    elseif self._isJoinRequest then
        self._btnRefuse.gameObject:SetActive(true)
        self._btnOk.gameObject:SetActive(true)
        self._btnMore.gameObject:SetActive(true)
    elseif self._isAcceptInvite then
        self._btnGo.gameObject:SetActive(true)
        self._btnOk.gameObject:SetActive(true)
        self._btnMore.gameObject:SetActive(true)
    elseif self._isGetApplyList then
        self._btnGo.gameObject:SetActive(true)
        self._btnCancel.gameObject:SetActive(true)
        self._btnMore.gameObject:SetActive(true)
    end
end

function ShareStreetInviteItem:_onClickGo()
	ViewMgr.instance:clearBackStack()
    RoomFacade.instance:enterShareStreet(self._roleInfo.id)
end

function ShareStreetInviteItem:_onClickInvite()
    print("点击邀请加入街区")
    self._mo.isInvited = true
    local isInvited = self._mo.isInvited
    self._btnInvite.gameObject:SetActive(not isInvited)
    self._btnOver.gameObject:SetActive(isInvited)

    ShareStreetController.instance:inviteFriend(self._roleInfo.id)
end

function ShareStreetInviteItem:_onClickOk()
    if self._isAcceptInvite then
        print("点击同意邀请")
        ShareStreetController.instance:acceptInvite(self._roleInfo.id)
    elseif self._isJoinRequest then
        print("点击同意申请")
        ShareStreetController.instance:acceptJoinRequest(self._roleInfo.id)
    end
end

function ShareStreetInviteItem:_onClickCancel()
    print("点击取消邀请")
    ShareStreetController.instance:cancelJoinApply(self._roleInfo.id)
end

function ShareStreetInviteItem:_onClickRefuse()
    print("点击忽略申请")
    ShareStreetController.instance:refuseJoinRequest(self._roleInfo.id)
end

function ShareStreetInviteItem:_onClickMore()
    self._goMore:SetActive(not self._goMore.activeSelf)
end

function ShareStreetInviteItem:_onClickReport()
    local ownerId = self._roleInfo.id
    local ownerName = self._roleInfo.nickname
    local name = self._streetInfo.name
    local desc = self._streetInfo.desc or ""
    local picture = self._streetInfo.picture or ""
    ReportFacade.instance:showReport(ownerId, ownerName, {55, 57, 58}, {name, "", picture})
end

function ShareStreetInviteItem:OnDestroy()
    self._btnGo:RemoveClickListener()
    self._btnInvite:RemoveClickListener()
    self._btnOk:RemoveClickListener()
    self._btnReport:RemoveClickListener()
    self._btnCancel:RemoveClickListener()
    self._btnRefuse:RemoveClickListener()
    self._btnMore:RemoveClickListener()
    self._btnReport:RemoveClickListener()
end

return ShareStreetInviteItem
