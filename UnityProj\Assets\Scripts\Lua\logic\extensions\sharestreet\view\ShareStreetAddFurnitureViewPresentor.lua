module("logic.extensions.sharestreet.view.ShareStreetAddFurnitureViewPresentor",package.seeall)

local ShareStreetAddFurnitureViewPresentor = class("ShareStreetAddFurnitureViewPresentor", ViewPresentor)

ShareStreetAddFurnitureViewPresentor.IconUrl = "ui/street/streetfurnitureicon.prefab"
ShareStreetAddFurnitureViewPresentor.BtnFilter1 = "ui/street/sharefurnituretab_1.prefab"
ShareStreetAddFurnitureViewPresentor.BtnFilter2 = "ui/street/sharefurnituretab_2.prefab"

function ShareStreetAddFurnitureViewPresentor:dependWhatResources()
    return {
        "ui/street/streetaddshareview.prefab",
        ShareStreetAddFurnitureViewPresentor.IconUrl,
        ShareStreetAddFurnitureViewPresentor.BtnFilter1,
        ShareStreetAddFurnitureViewPresentor.BtnFilter2,
        ShareStreetAddFurnitureViewPresentor.BtnFilter3,
        ShareStreetAddFurnitureViewPresentor.BtnFilter4
    }
end

function ShareStreetAddFurnitureViewPresentor:attachToWhichRoot()
    return ViewRootType.Popup
end

function ShareStreetAddFurnitureViewPresentor:buildViews()
    self.listComp = ShareFurnitureListComp.New()
    self.tabComp = ShareFurnitureTab.New()
    self.infoComp = ItemInfoComponent.New("rightGo/single/info")
    return {
        self.tabComp,
        self.infoComp,
        self.listComp,
        ShareStreetAddFurnitureView.New(),
    }
end

function ShareStreetAddFurnitureViewPresentor:onClickOutside()

end

return ShareStreetAddFurnitureViewPresentor


