module("logic.scene.unit.playeraction.WeatherUmbrellaHandler",package.seeall)

local WeatherUmbrellaHandler = class("WeatherUmbrellaHandler",PlayerActionHandlerBase)
--params
--1.itemId
--2.isDouble 是否是双人模式
--3.""
--4.playerId --加入进来玩家的id

local function getResPath(name)
    return "prefabs/umbrella/" .. name .. ".prefab"
end

function WeatherUmbrellaHandler:onStart()
    self.umbrellaConfig = InteractionItemConfig.getWeatherUmbrellaConfigById(tonumber(self.info.params[1]))
	self.unit.skinView:hideHandItem()
    self.oldSpeed = self.unit:getSpeed()
	self.unit:setSpeed(2.5)
    --self.unit.walkEffect:setSpecialMove(true) --关闭脚印
    self.unit:getComponent(EffectLoader):getSlot(EffectLoader.Pose):load(getResPath(self.umbrellaConfig.spineName), self.onEffectLoaded, self)
    self.unit:stop()

    self:_setFront() --开伞动画需要正面
    self.unit.skinView:addTempClothes(self.umbrellaConfig.clothId)
    self.unit:playAnimation("2.11_ys_shuangrenyangsan_start", false,false,true) --开伞动画
    settimer(0.933,function()
        self.unit:playAnimation(self.umbrellaConfig.idleAnim, true,false,false)
    end,_,false)
    self.unit:specificIdleAni(self.umbrellaConfig.idleAnim)

    --随机动画
    if not string.nilorempty(self.umbrellaConfig.randomAnim) then 
		local nextTime = 5 + math.random(5)
        settimer(nextTime,self._randomAnim,self,false)
	end

    GlobalDispatcher:addListener(GlobalNotify.OnSceneWeatherChanged,self._onSceneWeatherChange,self)
    RoomController.instance:registerLocalNotify(RoomNotifyName.SetWeather,self._onIslandWeatherChange,self)
    RoomController.instance:registerLocalNotify(RoomNotifyName.OnWeatherTimeOut,self._onIslandWeatherChange,self)
    
    self:onUpdate()
end

function WeatherUmbrellaHandler:onStop()
    GlobalDispatcher:removeListener(GlobalNotify.OnSceneWeatherChanged,self._onSceneWeatherChange,self)
    RoomController.instance:unregisterLocalNotify(RoomNotifyName.SetWeather,self._onIslandWeatherChange,self)
    RoomController.instance:unregisterLocalNotify(RoomNotifyName.OnWeatherTimeOut,self._onIslandWeatherChange,self)

    self.unit:specificIdleAni(nil)
    self.unit.shareItem:clear()	
    self.unit.skinView:showHandItem()	
    self.unit:setSpeed(self.oldSpeed)
    --self.unit.walkEffect:setSpecialMove(false)
    self.unit.skinView:removeTempClothes(self.umbrellaConfig.clothId)

    if self._isFollowRotation_f then 
        local go = self.unit:getComponent(EffectLoader):getSlot(EffectLoader.Pose):getSpecicalGO("front_f_zs_shouwan_3a")
        local followBone = go:GetComponent("BoneFollower")
        followBone.followBoneRotation = self._isFollowRotation_f
    end

    if self._isFollowRotation_b then 
        local go = self.unit:getComponent(EffectLoader):getSlot(EffectLoader.Pose):getSpecicalGO("back_f_zs_shouwan_3b")
        local followBone = go:GetComponent("BoneFollower")
        followBone.followBoneRotation = self._isFollowRotation_b
    end

    self.unit:getComponent(EffectLoader):getSlot(EffectLoader.Pose):clear()

    if self.unit.id == UserInfo.userId then
        SoundManager.instance:stopEffect(self.umbrellaConfig.rainSound)
        SoundManager.instance:stopEffect(self.umbrellaConfig.snowSound)
        SoundManager.instance:stopEffect(self.umbrellaConfig.normalSound)
    end

    removetimer(self._randomAnim,self)

    self:_removeRes()
end

function WeatherUmbrellaHandler:onUpdate()
    local param = self.info.params

    local itemId = tonumber(param[1]) 
    local itemCfg = InteractionItemConfig.getInteractionItemDefine(itemId)
    local isDouble = itemCfg.type == 8 and tonumber(param[2]) ~= 0
    local handUserId = param[#param]
    if isDouble and string.nilorempty(handUserId) then --双人伞有气泡
        self.unit.shareItem:setShareIconForAtlas(AtlasUrl.InteractionItem, itemId, handler(self.onClick, self), nil ,true)
    else 
        self.unit.shareItem:clear()
    end

    local event = tonumber(param[3]) 
 
    if event == 2 then --播放关伞动画
        self:_setFront()
        self.unit:playAnimation("2.11_ys_shuangrenyangsan_end", false,true,false)	
    end
end

function WeatherUmbrellaHandler:onEffectLoaded()
    self._frontSpine = {}
    self._backSpine = {}
    self.slot = self.unit:getComponent(EffectLoader):getSlot(EffectLoader.Pose)
	

    
    local go = self.slot:getSpecicalGO("front_f_zs_shouwan_3a")
    local sceneType = SceneManager.instance:getCurSceneType()
    --3d场景关闭rotation
    if sceneType == SceneType.Free3DScene then
        local followBone = go:GetComponent("BoneFollower")
        self._isFollowRotation_f = followBone.followBoneRotation
        followBone.followBoneRotation = false
        -- local bone = self.unit.skinView:getBone("zs_shouwan_3a",true)
        -- local boneRotation = bone.WorldRotationX
        -- tfutil.SetRotationZ(go, boneRotation)
        tfutil.SetRotationZ(go, -90)
    end

    for i=1,5 do 
        local spineGo_f = goutil.findChild(go, "front_f_zs_shouwan_3a/"..i)
        if spineGo_f then 
            self._frontSpine[i] = SpineAnimationHelper.New(spineGo_f)
        end
    end

    go = self.slot:getSpecicalGO("back_f_zs_shouwan_3b")
    if sceneType == SceneType.Free3DScene then
        local followBone = go:GetComponent("BoneFollower")
        self._isFollowRotation_b = followBone.followBoneRotation
        followBone.followBoneRotation = false
        -- local bone = self.unit.skinView:getBone("zs_shouwan_3b",false)
        -- local boneRotation = bone.WorldRotationX
        --tfutil.SetRotationZ(go, boneRotation)
        tfutil.SetRotationZ(go, -90)
    end

    for i=1,5 do 
        local spineGo_b = goutil.findChild(go, "back_f_zs_shouwan_3b/"..i)
        if spineGo_b then 
            self._backSpine[i] = SpineAnimationHelper.New(spineGo_b)
        end
    end

    self:_setUmbrellaAnim(self.umbrellaConfig.umbrellaIdleAnim,true)
    --小岛天气和场景天气是两套
    if HouseModel.instance:isInIsland() then 
        self:_onIslandWeatherChange(IslandWeatherModel.instance:getUsingWeather())
    else 
        self:_onSceneWeatherChange()
    end
end

function WeatherUmbrellaHandler:onShortActionStart()
end

function WeatherUmbrellaHandler:onStartMove()
    self.unit:playAnimation(self.umbrellaConfig.walkAnim, true,false,false)
    self:_setUmbrellaAnim(self.umbrellaConfig.umbrellaWalkAnim,true)
    if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_move")
	end
end

function WeatherUmbrellaHandler:onStopMove()
    self.unit:playAnimation(self.umbrellaConfig.idleAnim, true,false,false)	
    self:_setUmbrellaAnim(self.umbrellaConfig.umbrellaIdleAnim,true)
    if self.unit.seaAniComp then
		self.unit.seaAniComp:setLowerBodyAni("3.0_sea_xiaban_idle")
		self.unit.idleComp:setEnable(false)
	end
end

function WeatherUmbrellaHandler:onClick(isClick)
    local userPlayer = SceneManager.instance:getCurScene():getPlayer(UserInfo.userId)
    local actionComp = userPlayer:getComponent(PlayerActionComp)

    if isClick and actionComp.curAction == PlayerActionType.WeatherUmbrella then 
        FlyTextManager.instance:showFlyText(lang("正在做其他动作。"))
        return
    end

    if isClick and actionComp.curAction ~= PlayerActionType.JoinWeatherUmbrella then
        local param = self.info.params
        local itemId = param[1]
        local p = {
            self.unit.id,
            itemId,
        }
        
		SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.JoinWeatherUmbrella,p,_,true)
	else
        GlobalDispatcher:dispatch(GlobalNotify.ClickUmbrellaChange)
	end
end

function WeatherUmbrellaHandler:_setUmbrellaAnim(name,isLoop)
    if string.nilorempty(name) then return end

    local isFront = self.unit.skinView:getFace()
    if isFront then 
        for i=1,5 do 
            if self._frontSpine and self._frontSpine[i] then 
                self._frontSpine[i]:setAnimation(name,isLoop,1)
            end
        end

        -- if self._frontSpine_1 then 
        --     self._frontSpine_1:setAnimation(name,isLoop,1)
        -- end
        -- if self._frontSpine_2 then 
        --     self._frontSpine_2:setAnimation(name,isLoop,1)
        -- end
    elseif not isFront and self._backSpine then
        for i=1,5 do 
            if self._backSpine and self._backSpine[i] then 
                self._backSpine[i]:setAnimation(name,isLoop,1)
            end
        end
        --self._backSpine:setAnimation(name,isLoop,1)
    end
end

function WeatherUmbrellaHandler:_removeRes()
    -- if self._frontSpine_1 then 
    --     self._frontSpine_1:dispose()
    --     self._frontSpine_1 = nil
    -- end
    -- if self._frontSpine_2 then 
    --     self._frontSpine_2:dispose()
    --     self._frontSpine_2 = nil
    -- end
    -- if self._backSpine then 
    --     self._backSpine:dispose()
    --     self._backSpine = nil
    -- end

    for i=1,5 do 
        if self._frontSpine[i] then 
            self._frontSpine[i]:dispose()
            self._frontSpine[i] = nil
        end
        if self._backSpine[i] then 
            self._backSpine[i]:dispose()
            self._backSpine[i] = nil
        end
    end

    self._frontSpine = nil
    self._backSpine = nil
end

function WeatherUmbrellaHandler:_setFront()
    local dir = self.unit:getDirection()
    if dir > 1 then 
        dir = dir -2
    end
    self.unit:setDirection(dir)
end

function WeatherUmbrellaHandler:_onSceneWeatherChange(weather)
    local curSceneId = SceneManager.instance:getCurSceneId()

    if HouseModel.instance:isInIsland() and weather < 0 then 
        return
    end

    local weather = weather or SceneWeather.getWeather(curSceneId)
    self.slot = self.unit:getComponent(EffectLoader):getSlot(EffectLoader.Pose)
	local f = self.slot:getSpecicalGO("front_f_zs_shouwan_3a")
	local b = self.slot:getSpecicalGO("back_f_zs_shouwan_3b")

    self:_activeEffect(f, "rain_front",false,true)
    self:_activeEffect(b, "rain_back",false,false)
    self:_activeEffect(f, "snow_front",false,true)
    self:_activeEffect(b, "snow_back",false,false)
    self:_activeEffect(f, "normal_front",true,true)
    self:_activeEffect(b, "normal_back",true,false)

    if self.unit.id == UserInfo.userId then
        SoundManager.instance:stopEffect(self.umbrellaConfig.rainSound)
        SoundManager.instance:stopEffect(self.umbrellaConfig.snowSound)
        SoundManager.instance:stopEffect(self.umbrellaConfig.normalSound)
    end

    if table.indexof(WeatherUmbrellaController.rain,weather) then 
        self:_activeEffect(f, "rain_front",true,true)
        self:_activeEffect(b, "rain_back",true,false)

        if self.unit.id == UserInfo.userId and self.umbrellaConfig.rainSound > 0 then 
            SoundManager.instance:playEffect(self.umbrellaConfig.rainSound)
        end
    elseif table.indexof(WeatherUmbrellaController.snow,weather) then 
        self:_activeEffect(f, "snow_front",true,true)
        self:_activeEffect(b, "snow_back",true,false)
        if self.unit.id == UserInfo.userId and self.umbrellaConfig.snowSound > 0 then 
            SoundManager.instance:playEffect(self.umbrellaConfig.snowSound)
        end
    else 
        if self.unit.id == UserInfo.userId and self.umbrellaConfig.normalSound > 0 then 
            SoundManager.instance:playEffect(self.umbrellaConfig.normalSound)
        end
        -- self:_activeEffect(f, "normal_front",true,true)
        -- self:_activeEffect(b, "normal_back",true,false)
    end 
end

function WeatherUmbrellaHandler:_onIslandWeatherChange(define)
    local id = define and define.id
    if not id then 
        self:_onSceneWeatherChange(0)
        return
    end
    local weather = 0
    if id then 
        local furDefine = FurnitureConfig.getFurnitureDefine(id)
        weather = furDefine.ext.weatherType or 0
    end

    self:_onSceneWeatherChange(weather)
end

function WeatherUmbrellaHandler:_activeEffect(parent,name,isActive,isFront)
    if not parent then return end
    local str = isFront and "front_f_zs_shouwan_3a/" or "back_f_zs_shouwan_3b/"
    local go = goutil.findChild(parent, str .. name)
    if go then 
        go:SetActive(isActive)
    end
end

function WeatherUmbrellaHandler:_randomAnim()
    if string.nilorempty(self.umbrellaConfig.randomAnim) then 
		return
	end

    if not self.unit:isMoving() then
        self.unit:playAnimation(self.umbrellaConfig.randomAnim, false,true,true)	
        self:_setUmbrellaAnim(self.umbrellaConfig.umbrellaRandomAnim,false)
    end

    local nextTime = 10 + math.random(5)
    settimer(nextTime,self._randomAnim,self,false)
end

return WeatherUmbrellaHandler