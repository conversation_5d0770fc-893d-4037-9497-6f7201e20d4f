module("logic.extensions.turntable.subtable.dragon.view.DragonTurnTableRedBagView", package.seeall)
local DragonTurnTableRedBagView = class("DragonTurnTableRedBagView", ViewComponent)

--- view初始化时会执行
function DragonTurnTableRedBagView:buildUI()
    self:getBtn("btnclose/btnClose"):AddClickListener(self.close, self)
    -- self:getBtn("btnhelp"):AddClickListener(self._onClickHelp, self)
    self:getBtn("btnThanks"):AddClickListener(self._onClickThanks, self)

    self.items = {}
    for i = 1, 2, 1 do
        self.items[i] = self:initItem(self:getGo("item_" .. i), i)
    end
end

function DragonTurnTableRedBagView:initItem(go, i)
    local item = {}
    item.go = go
    item.hongbaoGo = goutil.findChild(go, "hongbaoGo")
    item.commonnullview = goutil.findChild(go, "commonnullview")
    item.txtGetCount = goutil.findChildTextComponent(go, "txtGetCount")
    item.txtCount = goutil.findChildTextComponent(go, "txtCount")
    item.btnGet = Framework.ButtonAdapter.GetFrom(go, "btnGet")
    item.btnGet:AddClickListener(self.onClickGet, self, {i})

    item.imgHeadIcon = goutil.findChild(go, "hongbaoGo/imgHeadIcon")
    item.txtName = goutil.findChildTextComponent(go, "hongbaoGo/txtName")
    item.txtDes = goutil.findChildTextComponent(go, "hongbaoGo/txtDes")

    item.canGet = false
    return item
end

function DragonTurnTableRedBagView:onEnter()
    self:updateView()
end

function DragonTurnTableRedBagView:updateView()
    for i, v in ipairs(self.items) do
        goutil.setActive(v.go, false)
    end
    TurnTableAgent.instance:sendAct161GetRedEnvelopeInfoRequest(handler(self.onGetInfo, self))
end

function DragonTurnTableRedBagView:onGetInfo(msg)
    local infos = {}
    local commonCfg = TurnTableConfig.getCommonConfig(FengHuaTurntableController.instance.activityId)
    infos[1] = {
        redBags = msg.normalStorageInfos,
        drawCounts = msg.drawNormalCountToday,
        maxReceive = commonCfg.drawNormalRedEnvelopeCount,
        maxStore = commonCfg.storeNormalRedEnvelopeCount
    }
    infos[2] = {
        redBags = msg.exquisiteStorageInfos,
        drawCounts = msg.drawExquisiteCountToday,
        maxReceive = commonCfg.drawExquisiteRedEnvelopeCount,
        maxStore = commonCfg.storeExquisiteRedEnvelopeCount
    }
    for i = 1, 2, 1 do
        self:updateItem(self.items[i], infos[i])
    end
end

function DragonTurnTableRedBagView:updateItem(item, info)
    local canReceive = info.maxReceive - info.drawCounts
    item.info = info
    item.txtGetCount.text = lang("今日可领取次数：<color=#efd98e>{1}</color>/{2}", canReceive, info.maxReceive)
    item.txtCount.text = lang("转盘活动红包存储文本", #info.redBags, info.maxStore)
    goutil.setActive(item.hongbaoGo, true)
    goutil.setActive(item.commonnullview, false)
    GameUtils.setUIGray(item.btnGet.gameObject, true, 1)
    goutil.setActive(item.btnGet.gameObject, true)
    item.btnGet.btn.interactable = false
    item.canGet = false
    if #info.redBags == 0 then
        goutil.setActive(item.hongbaoGo, false)
        goutil.setActive(item.btnGet.gameObject, false)
        goutil.setActive(item.commonnullview, true)
    else
        local redBag = info.redBags[1]
        HeadPortraitHelper.instance:setHeadPortraitWithUserId(item.imgHeadIcon, redBag.roleSimpleInfo.id)
        item.txtName.text = redBag.roleSimpleInfo.nickname
        item.txtDes.text = lang(TurnTableConfig.getRedBagDes(FengHuaTurntableController.instance.activityId,
            redBag.redEnvelopeDefineId), redBag.roleSimpleInfo.nickname)
        if canReceive > 0 then
            GameUtils.setUIGray(item.btnGet.gameObject, false, 1)
            item.btnGet.btn.interactable = true
            item.canGet = true
        end
    end
    goutil.setActive(item.go, true)
end

-- function DragonTurnTableRedBagView:_onClickHelp()
--     local langKey = "转盘活动" .. FengHuaTurntableController.instance.activityId .. "红包规则标题"
--     local title = lang(langKey)
--     langKey = "转盘活动" .. FengHuaTurntableController.instance.activityId .. "红包规则描述"
--     local content = lang(langKey)
--     ViewMgr.instance:open("ActivityRuleCommon", title, content)
-- end

function DragonTurnTableRedBagView:_onClickThanks()
    TurnTableAgent.instance:sendAct161GetRedEnvelopeDrawRecordsRequest(function(records)
        DragonTurnTableRedBagGetView.show(records)
    end)
end

function DragonTurnTableRedBagView:onClickGet(params)
    local id = params[1]
    print("领取类型", id)
    if self.items[id].canGet then
        local item = self.items[id]
        local redBag = item.info.redBags[1]
        TurnTableAgent.instance:sendAct161DrawRedEnvelopeRequest(redBag.roleSimpleInfo.id, redBag.redEnvelopeId, id,
            function(ci)
                DialogHelper.showRewardsDlgByCI(ci)
                table.remove(item.info.redBags, 1)
                item.info.drawCounts = item.info.drawCounts + 1
                self:updateItem(item, item.info)
            end)
    end
end

return DragonTurnTableRedBagView
