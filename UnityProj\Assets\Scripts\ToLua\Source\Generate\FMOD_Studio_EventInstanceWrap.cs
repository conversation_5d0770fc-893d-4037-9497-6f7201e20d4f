//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FMOD_Studio_EventInstanceWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FMOD.Studio.EventInstance), null);
		<PERSON>.RegFunction("getDescription", getDescription);
		L.RegFunction("getVolume", getVolume);
		L<PERSON>RegFunction("setVolume", setVolume);
		L.RegFunction("getPitch", getPitch);
		<PERSON><PERSON>RegFunction("setPitch", setPitch);
		L.RegFunction("get3DAttributes", get3DAttributes);
		<PERSON><PERSON>RegFunction("set3DAttributes", set3DAttributes);
		<PERSON><PERSON>RegFunction("getListenerMask", getListenerMask);
		L.RegFunction("setListenerMask", setListenerMask);
		L.RegFunction("getProperty", getProperty);
		<PERSON>.RegFunction("setProperty", setProperty);
		<PERSON>.<PERSON>Function("getReverbLevel", getReverbLevel);
		<PERSON><PERSON>Function("setReverbLevel", setReverbLevel);
		<PERSON><PERSON>unction("getPaused", getPaused);
		L.RegFunction("setPaused", setPaused);
		L.RegFunction("start", start);
		L.RegFunction("stop", stop);
		L.RegFunction("getTimelinePosition", getTimelinePosition);
		L.RegFunction("setTimelinePosition", setTimelinePosition);
		L.RegFunction("getPlaybackState", getPlaybackState);
		L.RegFunction("getChannelGroup", getChannelGroup);
		L.RegFunction("getMinMaxDistance", getMinMaxDistance);
		L.RegFunction("release", release);
		L.RegFunction("isVirtual", isVirtual);
		L.RegFunction("getParameterByID", getParameterByID);
		L.RegFunction("setParameterByID", setParameterByID);
		L.RegFunction("setParameterByIDWithLabel", setParameterByIDWithLabel);
		L.RegFunction("setParametersByIDs", setParametersByIDs);
		L.RegFunction("getParameterByName", getParameterByName);
		L.RegFunction("setParameterByName", setParameterByName);
		L.RegFunction("setParameterByNameWithLabel", setParameterByNameWithLabel);
		L.RegFunction("keyOff", keyOff);
		L.RegFunction("setCallback", setCallback);
		L.RegFunction("getUserData", getUserData);
		L.RegFunction("setUserData", setUserData);
		L.RegFunction("getCPUUsage", getCPUUsage);
		L.RegFunction("getMemoryUsage", getMemoryUsage);
		L.RegFunction("hasHandle", hasHandle);
		L.RegFunction("clearHandle", clearHandle);
		L.RegFunction("isValid", isValid);
		L.RegFunction("New", _CreateFMOD_Studio_EventInstance);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("handle", get_handle, set_handle);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateFMOD_Studio_EventInstance(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				System.IntPtr arg0 = ToLua.CheckIntPtr(L, 1);
				FMOD.Studio.EventInstance obj = new FMOD.Studio.EventInstance(arg0);
				ToLua.PushValue(L, obj);
				return 1;
			}
			else if (count == 0)
			{
				FMOD.Studio.EventInstance obj = new FMOD.Studio.EventInstance();
				ToLua.PushValue(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: FMOD.Studio.EventInstance.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getDescription(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.Studio.EventDescription arg0;
			FMOD.RESULT o = obj.getDescription(out arg0);
			ToLua.Push(L, o);
			ToLua.PushValue(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getVolume(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				float arg0;
				FMOD.RESULT o = obj.getVolume(out arg0);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg0);
				ToLua.SetBack(L, 1, obj);
				return 2;
			}
			else if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				float arg0;
				float arg1;
				FMOD.RESULT o = obj.getVolume(out arg0, out arg1);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg0);
				LuaDLL.lua_pushnumber(L, arg1);
				ToLua.SetBack(L, 1, obj);
				return 3;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.getVolume");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setVolume(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			FMOD.RESULT o = obj.setVolume(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getPitch(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				float arg0;
				FMOD.RESULT o = obj.getPitch(out arg0);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg0);
				ToLua.SetBack(L, 1, obj);
				return 2;
			}
			else if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				float arg0;
				float arg1;
				FMOD.RESULT o = obj.getPitch(out arg0, out arg1);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg0);
				LuaDLL.lua_pushnumber(L, arg1);
				ToLua.SetBack(L, 1, obj);
				return 3;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.getPitch");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setPitch(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			FMOD.RESULT o = obj.setPitch(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get3DAttributes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.ATTRIBUTES_3D arg0;
			FMOD.RESULT o = obj.get3DAttributes(out arg0);
			ToLua.Push(L, o);
			ToLua.PushValue(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set3DAttributes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.ATTRIBUTES_3D arg0 = StackTraits<FMOD.ATTRIBUTES_3D>.Check(L, 2);
			FMOD.RESULT o = obj.set3DAttributes(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getListenerMask(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			uint arg0;
			FMOD.RESULT o = obj.getListenerMask(out arg0);
			ToLua.Push(L, o);
			LuaDLL.lua_pushnumber(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setListenerMask(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			uint arg0 = (uint)LuaDLL.luaL_checknumber(L, 2);
			FMOD.RESULT o = obj.setListenerMask(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getProperty(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.Studio.EVENT_PROPERTY arg0 = (FMOD.Studio.EVENT_PROPERTY)ToLua.CheckObject(L, 2, typeof(FMOD.Studio.EVENT_PROPERTY));
			float arg1;
			FMOD.RESULT o = obj.getProperty(arg0, out arg1);
			ToLua.Push(L, o);
			LuaDLL.lua_pushnumber(L, arg1);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setProperty(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.Studio.EVENT_PROPERTY arg0 = (FMOD.Studio.EVENT_PROPERTY)ToLua.CheckObject(L, 2, typeof(FMOD.Studio.EVENT_PROPERTY));
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			FMOD.RESULT o = obj.setProperty(arg0, arg1);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getReverbLevel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			float arg1;
			FMOD.RESULT o = obj.getReverbLevel(arg0, out arg1);
			ToLua.Push(L, o);
			LuaDLL.lua_pushnumber(L, arg1);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setReverbLevel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			FMOD.RESULT o = obj.setReverbLevel(arg0, arg1);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getPaused(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			bool arg0;
			FMOD.RESULT o = obj.getPaused(out arg0);
			ToLua.Push(L, o);
			LuaDLL.lua_pushboolean(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setPaused(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			FMOD.RESULT o = obj.setPaused(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int start(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.RESULT o = obj.start();
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.Studio.STOP_MODE arg0 = (FMOD.Studio.STOP_MODE)ToLua.CheckObject(L, 2, typeof(FMOD.Studio.STOP_MODE));
			FMOD.RESULT o = obj.stop(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getTimelinePosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			int arg0;
			FMOD.RESULT o = obj.getTimelinePosition(out arg0);
			ToLua.Push(L, o);
			LuaDLL.lua_pushinteger(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setTimelinePosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			FMOD.RESULT o = obj.setTimelinePosition(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getPlaybackState(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.Studio.PLAYBACK_STATE arg0;
			FMOD.RESULT o = obj.getPlaybackState(out arg0);
			ToLua.Push(L, o);
			ToLua.Push(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getChannelGroup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.ChannelGroup arg0;
			FMOD.RESULT o = obj.getChannelGroup(out arg0);
			ToLua.Push(L, o);
			ToLua.PushValue(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getMinMaxDistance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			float arg0;
			float arg1;
			FMOD.RESULT o = obj.getMinMaxDistance(out arg0, out arg1);
			ToLua.Push(L, o);
			LuaDLL.lua_pushnumber(L, arg0);
			LuaDLL.lua_pushnumber(L, arg1);
			ToLua.SetBack(L, 1, obj);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int release(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.RESULT o = obj.release();
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int isVirtual(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			bool arg0;
			FMOD.RESULT o = obj.isVirtual(out arg0);
			ToLua.Push(L, o);
			LuaDLL.lua_pushboolean(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getParameterByID(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID arg0 = StackTraits<FMOD.Studio.PARAMETER_ID>.Check(L, 2);
				float arg1;
				FMOD.RESULT o = obj.getParameterByID(arg0, out arg1);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg1);
				ToLua.SetBack(L, 1, obj);
				return 2;
			}
			else if (count == 4)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID arg0 = StackTraits<FMOD.Studio.PARAMETER_ID>.Check(L, 2);
				float arg1;
				float arg2;
				FMOD.RESULT o = obj.getParameterByID(arg0, out arg1, out arg2);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg1);
				LuaDLL.lua_pushnumber(L, arg2);
				ToLua.SetBack(L, 1, obj);
				return 3;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.getParameterByID");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setParameterByID(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID arg0 = StackTraits<FMOD.Studio.PARAMETER_ID>.Check(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				FMOD.RESULT o = obj.setParameterByID(arg0, arg1);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 4)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID arg0 = StackTraits<FMOD.Studio.PARAMETER_ID>.Check(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				FMOD.RESULT o = obj.setParameterByID(arg0, arg1, arg2);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.setParameterByID");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setParameterByIDWithLabel(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID arg0 = StackTraits<FMOD.Studio.PARAMETER_ID>.Check(L, 2);
				string arg1 = ToLua.CheckString(L, 3);
				FMOD.RESULT o = obj.setParameterByIDWithLabel(arg0, arg1);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 4)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID arg0 = StackTraits<FMOD.Studio.PARAMETER_ID>.Check(L, 2);
				string arg1 = ToLua.CheckString(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				FMOD.RESULT o = obj.setParameterByIDWithLabel(arg0, arg1, arg2);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.setParameterByIDWithLabel");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setParametersByIDs(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID[] arg0 = ToLua.CheckStructArray<FMOD.Studio.PARAMETER_ID>(L, 2);
				float[] arg1 = ToLua.CheckNumberArray<float>(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				FMOD.RESULT o = obj.setParametersByIDs(arg0, arg1, arg2);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 5)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.PARAMETER_ID[] arg0 = ToLua.CheckStructArray<FMOD.Studio.PARAMETER_ID>(L, 2);
				float[] arg1 = ToLua.CheckNumberArray<float>(L, 3);
				int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
				bool arg3 = LuaDLL.luaL_checkboolean(L, 5);
				FMOD.RESULT o = obj.setParametersByIDs(arg0, arg1, arg2, arg3);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.setParametersByIDs");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getParameterByName(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				string arg0 = ToLua.CheckString(L, 2);
				float arg1;
				FMOD.RESULT o = obj.getParameterByName(arg0, out arg1);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg1);
				ToLua.SetBack(L, 1, obj);
				return 2;
			}
			else if (count == 4)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				string arg0 = ToLua.CheckString(L, 2);
				float arg1;
				float arg2;
				FMOD.RESULT o = obj.getParameterByName(arg0, out arg1, out arg2);
				ToLua.Push(L, o);
				LuaDLL.lua_pushnumber(L, arg1);
				LuaDLL.lua_pushnumber(L, arg2);
				ToLua.SetBack(L, 1, obj);
				return 3;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.getParameterByName");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setParameterByName(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				FMOD.RESULT o = obj.setParameterByName(arg0, arg1);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 4)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				string arg0 = ToLua.CheckString(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				FMOD.RESULT o = obj.setParameterByName(arg0, arg1, arg2);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.setParameterByName");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setParameterByNameWithLabel(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				string arg0 = ToLua.CheckString(L, 2);
				string arg1 = ToLua.CheckString(L, 3);
				FMOD.RESULT o = obj.setParameterByNameWithLabel(arg0, arg1);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 4)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				string arg0 = ToLua.CheckString(L, 2);
				string arg1 = ToLua.CheckString(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				FMOD.RESULT o = obj.setParameterByNameWithLabel(arg0, arg1, arg2);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.setParameterByNameWithLabel");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int keyOff(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.RESULT o = obj.keyOff();
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setCallback(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.EVENT_CALLBACK arg0 = (FMOD.Studio.EVENT_CALLBACK)ToLua.CheckDelegate<FMOD.Studio.EVENT_CALLBACK>(L, 2);
				FMOD.RESULT o = obj.setCallback(arg0);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else if (count == 3)
			{
				FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
				FMOD.Studio.EVENT_CALLBACK arg0 = (FMOD.Studio.EVENT_CALLBACK)ToLua.CheckDelegate<FMOD.Studio.EVENT_CALLBACK>(L, 2);
				FMOD.Studio.EVENT_CALLBACK_TYPE arg1 = (FMOD.Studio.EVENT_CALLBACK_TYPE)ToLua.CheckObject(L, 3, typeof(FMOD.Studio.EVENT_CALLBACK_TYPE));
				FMOD.RESULT o = obj.setCallback(arg0, arg1);
				ToLua.Push(L, o);
				ToLua.SetBack(L, 1, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FMOD.Studio.EventInstance.setCallback");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getUserData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			System.IntPtr arg0;
			FMOD.RESULT o = obj.getUserData(out arg0);
			ToLua.Push(L, o);
			LuaDLL.lua_pushlightuserdata(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int setUserData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			System.IntPtr arg0 = ToLua.CheckIntPtr(L, 2);
			FMOD.RESULT o = obj.setUserData(arg0);
			ToLua.Push(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getCPUUsage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			uint arg0;
			uint arg1;
			FMOD.RESULT o = obj.getCPUUsage(out arg0, out arg1);
			ToLua.Push(L, o);
			LuaDLL.lua_pushnumber(L, arg0);
			LuaDLL.lua_pushnumber(L, arg1);
			ToLua.SetBack(L, 1, obj);
			return 3;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int getMemoryUsage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			FMOD.Studio.MEMORY_USAGE arg0;
			FMOD.RESULT o = obj.getMemoryUsage(out arg0);
			ToLua.Push(L, o);
			ToLua.PushValue(L, arg0);
			ToLua.SetBack(L, 1, obj);
			return 2;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int hasHandle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			bool o = obj.hasHandle();
			LuaDLL.lua_pushboolean(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int clearHandle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			obj.clearHandle();
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int isValid(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)ToLua.CheckObject(L, 1, typeof(FMOD.Studio.EventInstance));
			bool o = obj.isValid();
			LuaDLL.lua_pushboolean(L, o);
			ToLua.SetBack(L, 1, obj);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_handle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)o;
			System.IntPtr ret = obj.handle;
			LuaDLL.lua_pushlightuserdata(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index handle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_handle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FMOD.Studio.EventInstance obj = (FMOD.Studio.EventInstance)o;
			System.IntPtr arg0 = ToLua.CheckIntPtr(L, 2);
			obj.handle = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index handle on a nil value");
		}
	}
}

