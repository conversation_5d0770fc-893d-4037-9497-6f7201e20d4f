-- {excel:Z杂货铺配置.xlsx, sheetName:export_杂货店配置}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_grocery", package.seeall)

local title = {groceryId=1,name=2,funcId=3,buyCount=4,showHasGain=5,serverParams=6,refreshParams=7,openTime=8,logId=9,guideId=10,tokenId=11,tokenClickable=12,bgUrl=13,itemListBGUrl=14,soundId=15,bgmId=16,sceneId=17,pos=18}

local dataList = {
	{1, "市集", 6, 99, false, {type=2}, {item={{count=30,id=3}}}, nil, 111, 0, nil, false, "image/grocery/npc_shiji.jpg", "image/grocery/01/shop_pnl_02.png", 141077, 0, 0, nil},
	{2, "服装店", 23, 1, true, {type=2}, {item={{count=30,id=3}}}, nil, 114, 0, nil, false, "image/grocery/npc_fuzhuang.jpg", "image/grocery/01/shop_pnl_02.png", 0, 0, 8, {6.25,-3.12}},
	{3, "家具店", 24, 99, true, {type=2}, {item={{count=30,id=3}}}, nil, 113, 0, nil, false, "image/grocery/npc_jiaju.jpg", "image/grocery/01/shop_pnl_02.png", 0, 0, 8, {1.48,-4.15}},
	{4, "料理店", 25, 1, false, {type=2}, {item={{count=30,id=3}}}, nil, 112, 0, nil, false, "image/grocery/npc_liaoli.jpg", "image/grocery/01/shop_pnl_02.png", 0, 0, 8, {-23.70,9.58}},
	{5, "挖宝树屋", 28, 1, true, {type=6}, nil, nil, 134, 0, {16000046,16000123,16000124,16000125}, false, "image/grocery/npc_wabaoshuwu.jpg", "image/grocery/01/shop_pnl_03.png", 0, 120018, 3, {8.30,-3.17}},
	{6, "美味巴士", 0, 99, false, {type=1}, nil, nil, 0, 0, nil, false, "image/grocery/npc_lingshi.jpg", "image/grocery/01/shop_pnl_03.png", 141083, 0, 6, {14.32,-16.29}},
	{7, "游戏小屋", 0, 99, true, {type=1}, nil, nil, 0, 0, nil, false, "image/grocery/npc_youxi.jpg", "image/grocery/01/shop_pnl_03.png", 141082, 0, 6, {-17.91,-14.71}},
	{8, "种子商店", 0, 99, false, {type=1}, nil, nil, 0, 0, nil, false, "image/grocery/bg_shop_08.png", "image/grocery/01/shop_pnl_03.png", 0, 0, 0, nil},
	{9, "百花园", 0, 999, false, {type=1}, nil, nil, 0, 0, nil, false, "image/grocery/bg_shop_08.png", "image/grocery/01/shop_pnl_03.png", 0, 0, 0, nil},
	{10, "副本商店", 0, 1, false, {type=1}, nil, nil, 0, 0, {16007001}, false, "image/grocery/npc_dreamland_1.jpg", "", 0, 0, 0, nil},
	{11, "会员商店", 0, 1, false, nil, nil, nil, 0, 0, nil, false, "image/grocery/npc_lishihui.jpg", "image/grocery/01/shop_pnl_04.png", 0, 0, 0, nil},
	{12, "商店补货", 0, 1, false, nil, nil, nil, 0, 0, nil, false, "image/grocery/npc_lishihui.jpg", "image/grocery/01/shop_pnl_04.png", 0, 0, 0, nil},
	{13, "礼物商店", 0, 99, false, {type=1}, nil, nil, 0, 0, {3}, false, "image/grocery/npc_shiji.jpg", "image/grocery/01/shop_pnl_06.png", 0, 0, 0, nil},
	{1301, "岛民礼物", 0, 99, false, {type=1}, nil, nil, 0, 0, {3}, false, "image/grocery/npc_shiji.jpg", "image/grocery/01/shop_pnl_06.png", 0, 0, 0, nil},
	{1302, "岛民回礼", 0, 1, true, {type=1}, nil, nil, 0, 0, {3}, false, "image/grocery/npc_shiji.jpg", "image/grocery/01/shop_pnl_06.png", 0, 0, 0, nil},
	{14, "鱼类兑换", 0, 1, true, {type=4}, nil, nil, 0, 0, nil, false, "image/grocery/npc_diaoyuduihuan.jpg", "image/grocery/01/shop_pnl_06.png", 0, 0, 0, nil},
	{1401, "服装家具", 0, 1, true, {type=4}, nil, nil, 0, 0, nil, false, "image/grocery/npc_diaoyuduihuan.jpg", "image/grocery/01/shop_pnl_06.png", 0, 0, 0, nil},
	{1402, "动态鱼缸", 0, 1, true, {type=4}, nil, nil, 0, 0, nil, false, "image/grocery/npc_diaoyuduihuan.jpg", "image/grocery/01/shop_pnl_06.png", 0, 0, 0, nil},
	{15, "迷雾森林", 0, 1, true, {type=1}, nil, nil, 0, 0, {16000121}, false, "image/grocery/npc_miwu.jpg", "image/grocery/01/shop_pnl_03.png", 0, 0, 0, nil},
	{16, "欢乐小屋", 42, 1, true, {type=6}, nil, nil, 0, 0, {10}, false, "image/grocery/npc_huanlexiaowu.png", "image/grocery/01/shop_pnl_03.png", 0, 0, 28, {-26,-10.6}},
	{17, "青森小屋", 0, 1, false, {type=1}, nil, nil, 0, 0, {16000178,5}, false, "image/grocery/npc_qingsenxiaowu.jpg", "image/grocery/01/shop_pnl_03.png", 0, 0, 0, {-14.67,-4.15}},
	{19, "副本商店", 0, 1, false, {type=1}, nil, nil, 0, 0, {16007006}, false, "", "", 0, 0, 0, nil},
	{20, "时空商店", 45, 1, false, {type=6}, nil, nil, 0, 0, {16000234}, false, "image/grocery/npc_shikongshangdian.jpg", "image/grocery/01/shop_pnl_07.png", 0, 0, 0, nil},
	{21, "副本商店", 0, 1, false, {type=1}, nil, nil, 0, 0, {16007008}, false, "", "", 0, 0, 0, nil},
	{2001, "时空服装", 0, 1, true, {type=6}, nil, nil, 0, 0, {16000234}, false, "image/grocery/npc_shikongshangdian.jpg", "image/grocery/01/shop_pnl_07.png", 0, 0, 0, nil},
	{2002, "时空家具", 0, 99, true, {type=6}, nil, nil, 0, 0, {16000234}, false, "image/grocery/npc_shikongshangdian.jpg", "image/grocery/01/shop_pnl_07.png", 0, 0, 0, nil},
	{2003, "时空道具", 0, 99, false, {type=6}, {refreshLimitCount=3,item={{count=100,id=2},{count=200,id=2},{count=350,id=2}}}, nil, 0, 0, {16000234}, false, "image/grocery/npc_shikongshangdian.jpg", "image/grocery/01/shop_pnl_07.png", 0, 0, 0, nil},
	{22, "精灵商店", 55, 1, true, {type=6}, nil, nil, 0, 0, {16000404}, true, "image/grocery/npc_jinglingshangdian.jpg", "image/grocery/01/shop_pnl_03.png", 0, 0, 0, nil},
	{23, "副本商店", 0, 1, false, {type=1}, nil, nil, 0, 0, {16007010}, false, "", "", 0, 0, 0, nil},
	{24, "永恒乐园", 0, 99, false, {type=7}, {refreshLimitCount=11,item={{count=0,id=2},{count=10,id=2},{count=20,id=2},{count=30,id=2},{count=40,id=2},{count=50,id=2},{count=50,id=2},{count=50,id=2},{count=50,id=2},{count=50,id=2},{count=50,id=2}}}, nil, 0, 0, {16000558,1,2}, false, "", "", 0, 0, 0, nil},
	{25, "小岛", 0, 1, true, {type=7}, nil, nil, 0, 0, {1,2}, false, "", "", 0, 0, 0, nil},
	{26, "副本商店", 0, 1, false, {type=1}, nil, nil, 0, 0, {16007012}, false, "", "", 0, 0, 0, nil},
	{105, "冰雪市集", 0, 1, false, {activityId=105,type=3}, nil, nil, 0, 0, {16000061}, false, "", "", 0, 0, 0, nil},
	{112, "快乐商店1", 0, 1, false, {activityId=112,type=3}, nil, "2022-09-16T05:00:00", 0, 0, {16000071}, false, "", "", 0, 0, 0, nil},
	{113, "快乐商店2", 0, 1, false, {activityId=112,type=3}, nil, "2022-09-23T05:00:00", 0, 0, {16000071}, false, "", "", 0, 0, 0, nil},
	{114, "快乐商店3", 0, 1, false, {activityId=112,type=3}, nil, "2022-09-30T05:00:00", 0, 0, {16000071}, false, "", "", 0, 0, 0, nil},
	{122, "小游戏时光", 0, 1, true, {activityId=122,type=3}, nil, nil, 0, 0, {16000114}, false, "", "", 0, 0, 0, nil},
	{127, "花神商店1", 0, 1, false, {activityId=127,type=3}, nil, nil, 0, 0, {16000122}, false, "", "", 0, 0, 0, nil},
	{128, "花神商店2", 0, 1, false, {activityId=127,type=3}, nil, nil, 0, 0, {16000122}, false, "", "", 0, 0, 0, nil},
	{129, "花神商店3", 0, 1, false, {activityId=127,type=3}, nil, nil, 0, 0, {16000122}, false, "", "", 0, 0, 0, nil},
	{139, "星际商店1", 0, 99, false, {activityId=139,type=3}, nil, nil, 0, 0, {16000143}, false, "", "", 0, 0, 0, nil},
	{140, "星际商店2", 0, 99, false, {activityId=139,type=3}, nil, nil, 0, 0, {16000143}, false, "", "", 0, 0, 0, nil},
	{141, "星际商店3", 0, 99, false, {activityId=139,type=3}, nil, nil, 0, 0, {16000143}, false, "", "", 0, 0, 0, nil},
	{155, "庆典商店", 0, 1, false, {activityId=155,refresh=true,type=3}, nil, nil, 0, 0, {16000149}, false, "", "", 0, 0, 0, nil},
	{156, "外观", 0, 1, false, {activityId=155,refresh=true,type=3}, nil, nil, 0, 0, {16000149}, false, "", "", 0, 0, 0, nil},
	{157, "道具", 0, 1, false, {activityId=155,refresh=true,type=3}, nil, nil, 0, 0, {16000149}, false, "", "", 0, 0, 0, nil},
	{165, "满月节活动商店", 0, 99, false, {activityId=165,type=3}, nil, nil, 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{168, "满月童话风儿小摊", 0, 99, false, {activityId=168,refresh=true,type=3}, nil, nil, 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{177, "雾尽商店1", 0, 99, false, {activityId=177,type=3}, nil, "2022-08-11T05:00:00", 0, 0, {16000160}, false, "", "", 0, 0, 0, nil},
	{178, "雾尽商店2", 0, 99, false, {activityId=177,type=3}, nil, "2022-08-12T05:00:00", 0, 0, {16000160}, false, "", "", 0, 0, 0, nil},
	{179, "雾尽商店3", 0, 99, false, {activityId=177,type=3}, nil, "2022-08-13T05:00:00", 0, 0, {16000160}, false, "", "", 0, 0, 0, nil},
	{180, "奇食商店", 0, 99, false, {activityId=201,type=3}, nil, "2022-10-09T05:00:00", 0, 0, {16000200}, false, "", "", 0, 0, 0, nil},
	{181, "郁香商店1", 0, 99, false, {activityId=1132,type=5}, nil, "2022-10-27T05:00:00", 0, 0, {16000122}, false, "", "", 0, 0, 0, nil},
	{182, "郁香商店2", 0, 99, false, {activityId=1132,type=5}, nil, "2022-11-03T05:00:00", 0, 0, {16000122}, false, "", "", 0, 0, 0, nil},
	{183, "郁香商店3", 0, 99, false, {activityId=1132,type=5}, nil, "2022-11-10T05:00:00", 0, 0, {16000122}, false, "", "", 0, 0, 0, nil},
	{184, "蜂蜂商店1", 0, 99, false, {activityId=1143,type=5}, nil, "2022-11-23T05:00:00", 0, 0, {16000217}, false, "", "", 0, 0, 0, nil},
	{185, "蜂蜂商店2", 0, 99, false, {activityId=1143,type=5}, nil, "2022-11-30T05:00:00", 0, 0, {16000217}, false, "", "", 0, 0, 0, nil},
	{186, "蜂蜂商店3", 0, 99, false, {activityId=1143,type=5}, nil, "2022-12-07T05:00:00", 0, 0, {16000217}, false, "", "", 0, 0, 0, nil},
	{187, "冰雪商店1", 0, 99, false, {activityId=1159,type=5}, nil, "2022-12-15T05:00:00", 0, 0, {16000222}, false, "", "", 0, 0, 0, nil},
	{188, "冰雪商店2", 0, 99, false, {activityId=1159,type=5}, nil, "2022-12-22T05:00:00", 0, 0, {16000222}, false, "", "", 0, 0, 0, nil},
	{189, "冰雪商店3", 0, 99, false, {activityId=1159,type=5}, nil, "2022-12-29T05:00:00", 0, 0, {16000222}, false, "", "", 0, 0, 0, nil},
	{190, "祈福商店", 0, 99, false, {activityId=1174,type=5}, nil, "2022-12-29T05:00:00", 0, 0, {16000237}, false, "", "", 0, 0, 0, nil},
	{191, "团圆商店1", 0, 99, false, {activityId=1189,type=5}, nil, "2023-01-12T05:00:00", 0, 0, {16000250}, false, "", "", 0, 0, 0, nil},
	{192, "团圆商店2", 0, 99, false, {activityId=1189,type=5}, nil, "2023-01-19T05:00:00", 0, 0, {16000250}, false, "", "", 0, 0, 0, nil},
	{193, "团圆商店3", 0, 99, false, {activityId=1189,type=5}, nil, "2023-01-26T05:00:00", 0, 0, {16000250}, false, "", "", 0, 0, 0, nil},
	{194, "游仙商店", 0, 99, false, {activityId=1195,type=5}, nil, "2023-01-19T05:00:00", 0, 0, {16000257}, false, "", "", 0, 0, 0, nil},
	{195, "花灯商店", 0, 99, false, {activityId=1202,type=5}, nil, "2023-02-02T05:00:00", 0, 0, {16000272}, false, "", "", 0, 0, 0, nil},
	{196, "花灯商店", 0, 99, false, {activityId=1202,type=5}, nil, "2023-02-02T05:00:00", 0, 0, {16000272}, false, "", "", 0, 0, 0, nil},
	{197, "爱神商店1", 0, 99, false, {activityId=1231,type=5}, nil, "2023-02-09T05:00:00", 0, 0, {16000285}, false, "", "", 0, 0, 0, nil},
	{198, "爱神商店2", 0, 99, false, {activityId=1231,type=5}, nil, "2023-02-16T05:00:00", 0, 0, {16000285}, false, "", "", 0, 0, 0, nil},
	{199, "爱神商店3", 0, 99, false, {activityId=1231,type=5}, nil, "2023-02-23T05:00:00", 0, 0, {16000285}, false, "", "", 0, 0, 0, nil},
	{200, "海神商店1", 0, 99, false, {activityId=1253,type=5}, nil, "2023-03-09T05:00:00", 0, 0, {16000286}, false, "", "", 0, 0, 0, nil},
	{201, "海神商店2", 0, 99, false, {activityId=1253,type=5}, nil, "2023-03-16T05:00:00", 0, 0, {16000286}, false, "", "", 0, 0, 0, nil},
	{202, "海神商店3", 0, 99, false, {activityId=1253,type=5}, nil, "2023-03-23T05:00:00", 0, 0, {16000286}, false, "", "", 0, 0, 0, nil},
	{203, "音符商店1", 0, 99, false, {activityId=1288,type=5}, nil, "2023-04-06T05:00:00", 0, 0, {16000298}, false, "", "", 0, 0, 0, nil},
	{204, "音符商店2", 0, 99, false, {activityId=1288,type=5}, nil, "2023-04-13T05:00:00", 0, 0, {16000298}, false, "", "", 0, 0, 0, nil},
	{205, "音符商店3", 0, 99, false, {activityId=1288,type=5}, nil, "2023-04-20T05:00:00", 0, 0, {16000298}, false, "", "", 0, 0, 0, nil},
	{206, "造梦商店1", 0, 99, false, {activityId=1273,type=5}, nil, "2023-03-22T05:00:00", 0, 0, {16000296}, false, "", "", 0, 0, 0, nil},
	{207, "造物商店", 0, 99, false, {activityId=1315,type=5}, nil, "2023-04-27T05:00:00", 0, 0, {16000315}, false, "", "", 0, 0, 0, nil},
	{208, "灵感小摊", 0, 99, false, {activityId=1314,refresh=true,type=5}, nil, "2023-04-27T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{209, "发条吱吱1", 0, 99, false, {activityId=1322,type=5}, nil, "2023-05-11T05:00:00", 0, 0, {16000329}, false, "", "", 0, 0, 0, nil},
	{210, "发条吱吱2", 0, 99, false, {activityId=1322,type=5}, nil, "2023-05-18T05:00:00", 0, 0, {16000329}, false, "", "", 0, 0, 0, nil},
	{211, "发条吱吱3", 0, 99, false, {activityId=1322,type=5}, nil, "2023-05-25T05:00:00", 0, 0, {16000329}, false, "", "", 0, 0, 0, nil},
	{212, "游园商店1", 0, 99, false, {activityId=1336,type=5}, nil, "2023-05-25T05:00:00", 0, 0, {16000330}, false, "", "", 0, 0, 0, nil},
	{213, "游园商店2", 0, 99, false, {activityId=1336,type=5}, nil, "2023-06-01T05:00:00", 0, 0, {16000330}, false, "", "", 0, 0, 0, nil},
	{214, "奇游商店1", 0, 99, false, {activityId=1360,type=5}, nil, "2023-06-08T05:00:00", 0, 0, {16000335}, false, "", "", 0, 0, 0, nil},
	{215, "奇游商店2", 0, 99, false, {activityId=1360,type=5}, nil, "2023-06-15T05:00:00", 0, 0, {16000335}, false, "", "", 0, 0, 0, nil},
	{216, "奇游商店3", 0, 99, false, {activityId=1360,type=5}, nil, "2023-06-22T05:00:00", 0, 0, {16000335}, false, "", "", 0, 0, 0, nil},
	{217, "勋仔小店1", 0, 99, false, {activityId=1377,type=5}, nil, "2023-06-22T05:00:00", 0, 0, {16000344}, false, "", "", 0, 0, 0, nil},
	{218, "勋仔小店2", 0, 99, false, {activityId=1377,type=5}, nil, "2023-06-29T05:00:00", 0, 0, {16000344}, false, "", "", 0, 0, 0, nil},
	{219, "炫彩商店1", 0, 99, false, {activityId=1395,type=5}, nil, "2023-07-06T05:00:00", 0, 0, {16000350}, false, "", "", 0, 0, 0, nil},
	{220, "炫彩商店2", 0, 99, false, {activityId=1395,type=5}, nil, "2023-07-13T05:00:00", 0, 0, {16000350}, false, "", "", 0, 0, 0, nil},
	{221, "炫彩商店3", 0, 99, false, {activityId=1395,type=5}, nil, "2023-07-20T05:00:00", 0, 0, {16000350}, false, "", "", 0, 0, 0, nil},
	{222, "纪念商店1", 0, 99, false, {activityId=1404,type=5}, nil, "2023-07-12T05:00:00", 0, 0, {16000351}, false, "", "", 0, 0, 0, nil},
	{224, "羊毛商店", 0, 99, false, {activityId=1412,type=5}, nil, "2023-07-12T05:00:00", 0, 0, {16000361}, false, "", "", 0, 0, 0, nil},
	{225, "冰柠商店1", 0, 99, false, {activityId=1416,type=5}, nil, "2023-07-27T05:00:00", 0, 0, {16000378}, false, "", "", 0, 0, 0, nil},
	{226, "冰柠商店2", 0, 99, false, {activityId=1416,type=5}, nil, "2023-08-03T05:00:00", 0, 0, {16000378}, false, "", "", 0, 0, 0, nil},
	{227, "星际商店1", 0, 99, false, {activityId=1442,type=5}, nil, "2023-08-10T05:00:00", 0, 0, {16000388}, false, "", "", 0, 0, 0, nil},
	{228, "星际商店2", 0, 99, false, {activityId=1442,type=5}, nil, "2023-08-17T05:00:00", 0, 0, {16000388}, false, "", "", 0, 0, 0, nil},
	{229, "星际商店3", 0, 99, false, {activityId=1442,type=5}, nil, "2023-08-24T05:00:00", 0, 0, {16000388}, false, "", "", 0, 0, 0, nil},
	{230, "游梦商铺1", 0, 99, false, {activityId=1477,type=5}, nil, "2023-08-22T05:00:00", 0, 0, {16000394}, false, "", "", 0, 0, 0, nil},
	{231, "游梦商铺2", 0, 99, false, {activityId=1477,type=5}, nil, "2023-08-29T05:00:00", 0, 0, {16000394}, false, "", "", 0, 0, 0, nil},
	{232, "快乐商店1", 0, 99, false, {activityId=1496,type=5}, nil, "2023-09-07T05:00:00", 0, 0, {16000402}, false, "", "", 0, 0, 0, nil},
	{233, "快乐商店2", 0, 99, false, {activityId=1496,type=5}, nil, "2023-09-14T05:00:00", 0, 0, {16000402}, false, "", "", 0, 0, 0, nil},
	{234, "快乐商店3", 0, 99, false, {activityId=1496,type=5}, nil, "2023-09-21T05:00:00", 0, 0, {16000402}, false, "", "", 0, 0, 0, nil},
	{235, "满月商店1", 0, 99, false, {activityId=1530,type=5}, nil, "2023-09-27T05:00:00", 0, 0, {16000424}, false, "", "", 0, 0, 0, nil},
	{236, "满月商店2", 0, 99, false, {activityId=1530,type=5}, nil, "2023-10-04T05:00:00", 0, 0, {16000424}, false, "", "", 0, 0, 0, nil},
	{237, "月灵商店1", 0, 99, false, {activityId=1551,type=5}, nil, "2023-10-12T05:00:00", 0, 0, {16000439}, false, "", "", 0, 0, 0, nil},
	{238, "月灵商店2", 0, 99, false, {activityId=1551,type=5}, nil, "2023-10-19T05:00:00", 0, 0, {16000439}, false, "", "", 0, 0, 0, nil},
	{239, "月灵商店3", 0, 99, false, {activityId=1551,type=5}, nil, "2023-10-26T05:00:00", 0, 0, {16000439}, false, "", "", 0, 0, 0, nil},
	{240, "月典商店1", 0, 99, false, {activityId=1577,type=5}, nil, "2023-10-26T05:00:00", 0, 0, {16000442}, false, "", "", 0, 0, 0, nil},
	{241, "月典商店2", 0, 99, false, {activityId=1577,type=5}, nil, "2023-11-02T05:00:00", 0, 0, {16000442}, false, "", "", 0, 0, 0, nil},
	{242, "雾月商店1", 0, 99, false, {activityId=1599,type=5}, nil, "2023-11-09T05:00:00", 0, 0, {16000456}, false, "", "", 0, 0, 0, nil},
	{243, "雾月商店2", 0, 99, false, {activityId=1599,type=5}, nil, "2023-11-16T05:00:00", 0, 0, {16000456}, false, "", "", 0, 0, 0, nil},
	{244, "雾月商店3", 0, 99, false, {activityId=1599,type=5}, nil, "2023-11-23T05:00:00", 0, 0, {16000456}, false, "", "", 0, 0, 0, nil},
	{245, "魔法节目1", 0, 99, false, {activityId=1611,type=5}, nil, "2023-11-23T05:00:00", 0, 0, {16000458}, false, "", "", 0, 0, 0, nil},
	{246, "魔法节目2", 0, 99, false, {activityId=1611,type=5}, nil, "2023-11-30T05:00:00", 0, 0, {16000458}, false, "", "", 0, 0, 0, nil},
	{247, "星之商店1", 0, 99, false, {activityId=1642,type=5}, nil, "2023-12-07T05:00:00", 0, 0, {16000461}, false, "", "", 0, 0, 0, nil},
	{248, "星之商店2", 0, 99, false, {activityId=1642,type=5}, nil, "2023-12-14T05:00:00", 0, 0, {16000461}, false, "", "", 0, 0, 0, nil},
	{249, "星之商店3", 0, 99, false, {activityId=1642,type=5}, nil, "2023-12-21T05:00:00", 0, 0, {16000461}, false, "", "", 0, 0, 0, nil},
	{250, "饼饼小屋1", 0, 99, false, {activityId=1654,type=5}, nil, "2023-12-21T05:00:00", 0, 0, {16000465}, false, "", "", 0, 0, 0, nil},
	{251, "饼饼小屋2", 0, 99, false, {activityId=1654,type=5}, nil, "2023-12-28T05:00:00", 0, 0, {16000465}, false, "", "", 0, 0, 0, nil},
	{252, "新年潮流", 0, 99, false, {activityId=1648,type=5}, nil, "2023-12-28T05:00:00", 0, 0, {1,2}, false, "", "", 0, 0, 0, nil},
	{253, "新年潮流", 0, 99, false, {activityId=1648,type=5}, nil, "2023-12-28T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{254, "回流组队", 0, 99, false, {type=6}, nil, "2023-11-30T05:00:00", 0, 0, {16000478}, false, "", "", 0, 0, 0, nil},
	{255, "回流商店1", 0, 99, false, {activityId=1692,type=5}, nil, "2023-12-28T05:00:00", 0, 0, {16000465}, false, "", "", 0, 0, 0, nil},
	{256, "回流商店2", 0, 99, false, {activityId=1692,type=5}, nil, "2023-12-28T05:00:00", 0, 0, {16000466}, false, "", "", 0, 0, 0, nil},
	{257, "回流商店3", 0, 99, false, {activityId=1692,type=5}, nil, "2023-12-28T05:00:00", 0, 0, {16000467}, false, "", "", 0, 0, 0, nil},
	{258, "光影商店1", 0, 99, false, {activityId=1686,type=5}, nil, "2024-01-04T05:00:00", 0, 0, {16000471}, false, "", "", 0, 0, 0, nil},
	{259, "光影商店2", 0, 99, false, {activityId=1686,type=5}, nil, "2024-01-11T05:00:00", 0, 0, {16000471}, false, "", "", 0, 0, 0, nil},
	{260, "光影商店3", 0, 99, false, {activityId=1686,type=5}, nil, "2024-01-18T05:00:00", 0, 0, {16000471}, false, "", "", 0, 0, 0, nil},
	{261, "复刻·星际商店1", 0, 99, true, {activityId=1694,type=5}, nil, "2024-01-18T05:00:00", 0, 0, {16000485}, false, "", "", 0, 0, 0, nil},
	{262, "复刻·星际商店2", 0, 99, true, {activityId=1694,type=5}, nil, "2024-01-18T05:00:00", 0, 0, {16000485}, false, "", "", 0, 0, 0, nil},
	{263, "复刻·星际商店3", 0, 99, true, {activityId=1694,type=5}, nil, "2024-01-18T05:00:00", 0, 0, {16000485}, false, "", "", 0, 0, 0, nil},
	{264, "糖水货架1", 0, 99, false, {activityId=1699,type=5}, nil, "2024-01-18T05:00:00", 0, 0, {16000483}, false, "", "", 0, 0, 0, nil},
	{265, "糖水货架2", 0, 99, false, {activityId=1699,type=5}, nil, "2024-01-25T05:00:00", 0, 0, {16000483}, false, "", "", 0, 0, 0, nil},
	{266, "糖水工坊", 0, 99, false, {activityId=1703,refresh=true,type=5}, nil, "2024-01-18T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{267, "紫禁珍宝1", 0, 99, false, {activityId=1722,type=5}, nil, "2024-02-01T05:00:00", 0, 0, {16000516}, false, "", "", 0, 0, 0, nil},
	{268, "紫禁珍宝2", 0, 99, false, {activityId=1722,type=5}, nil, "2024-02-08T05:00:00", 0, 0, {16000516}, false, "", "", 0, 0, 0, nil},
	{269, "紫禁珍宝3", 0, 99, false, {activityId=1722,type=5}, nil, "2024-02-15T05:00:00", 0, 0, {16000516}, false, "", "", 0, 0, 0, nil},
	{270, "元灯小铺1", 0, 99, false, {activityId=1756,type=5}, nil, "2024-02-22T05:00:00", 0, 0, {16000480}, false, "", "", 0, 0, 0, nil},
	{271, "元灯小铺2", 0, 99, false, {activityId=1756,type=5}, nil, "2024-02-29T05:00:00", 0, 0, {16000480}, false, "", "", 0, 0, 0, nil},
	{272, "联欢商店", 0, 99, false, {activityId=1764,type=5}, nil, "2024-02-08T05:00:00", 0, 0, {16000505}, false, "", "", 0, 0, 0, nil},
	{273, "春风商店1", 0, 99, false, {activityId=1782,type=5}, nil, "2024-03-07T05:00:00", 0, 0, {16000520}, false, "", "", 0, 0, 0, nil},
	{274, "春风商店2", 0, 99, false, {activityId=1782,type=5}, nil, "2024-03-14T05:00:00", 0, 0, {16000520}, false, "", "", 0, 0, 0, nil},
	{275, "春风商店3", 0, 99, false, {activityId=1782,type=5}, nil, "2024-03-21T05:00:00", 0, 0, {16000520}, false, "", "", 0, 0, 0, nil},
	{276, "茶会小铺1", 0, 99, false, {activityId=1799,type=5}, nil, "2024-03-21T05:00:00", 0, 0, {16000551}, false, "", "", 0, 0, 0, nil},
	{277, "茶会小铺2", 0, 99, false, {activityId=1799,type=5}, nil, "2024-03-28T05:00:00", 0, 0, {16000551}, false, "", "", 0, 0, 0, nil},
	{278, "游戏机时光", 0, 99, false, {type=6}, nil, "2024-04-03T05:00:00", 0, 0, {16000567}, false, "", "", 0, 0, 0, nil},
	{279, "郁香商店", 0, 99, true, {activityId=1822,type=5}, nil, "2024-03-21T05:00:00", 0, 0, {16000486}, false, "", "", 0, 0, 0, nil},
	{280, "郁香商店", 0, 99, true, {activityId=1822,type=5}, nil, "2024-03-21T05:00:00", 0, 0, {16000486}, false, "", "", 0, 0, 0, nil},
	{281, "郁香商店", 0, 99, true, {activityId=1822,type=5}, nil, "2024-03-21T05:00:00", 0, 0, {16000486}, false, "", "", 0, 0, 0, nil},
	{282, "城市纪念1", 0, 99, false, {activityId=1832,type=5}, nil, "2024-04-03T05:00:00", 0, 0, {16000569}, false, "", "", 0, 0, 0, nil},
	{283, "城市纪念2", 0, 99, false, {activityId=1832,type=5}, nil, "2024-04-10T05:00:00", 0, 0, {16000569}, false, "", "", 0, 0, 0, nil},
	{284, "城市纪念3", 0, 99, false, {activityId=1832,type=5}, nil, "2024-04-17T05:00:00", 0, 0, {16000569}, false, "", "", 0, 0, 0, nil},
	{285, "赛季卡包商店", 0, 99, false, {type=6}, nil, "2025-05-01T05:00:00", 0, 0, {16000771,16000772,2}, false, "", "", 0, 0, 0, nil},
	{286, "神庙集市1", 0, 99, false, {activityId=1842,type=5}, nil, "2024-04-18T05:00:00", 0, 0, {16000576}, false, "", "", 0, 0, 0, nil},
	{287, "神庙集市2", 0, 99, false, {activityId=1842,type=5}, nil, "2024-04-25T05:00:00", 0, 0, {16000576}, false, "", "", 0, 0, 0, nil},
	{288, "返场商店1", 0, 99, true, {activityId=1871,type=5}, nil, "2024-04-18T05:00:00", 0, 0, {16000487}, false, "", "", 0, 0, 0, nil},
	{289, "返场商店2", 0, 99, true, {activityId=1871,type=5}, nil, "2024-04-18T05:00:00", 0, 0, {16000487}, false, "", "", 0, 0, 0, nil},
	{290, "返场商店3", 0, 99, true, {activityId=1871,type=5}, nil, "2024-04-18T05:00:00", 0, 0, {16000487}, false, "", "", 0, 0, 0, nil},
	{291, "游戏商店1", 0, 99, false, {activityId=1881,type=5}, nil, "2024-05-09T05:00:00", 0, 0, {16000592}, false, "", "", 0, 0, 0, nil},
	{292, "游戏商店2", 0, 99, false, {activityId=1881,type=5}, nil, "2024-05-16T05:00:00", 0, 0, {16000592}, false, "", "", 0, 0, 0, nil},
	{293, "游戏商店3", 0, 99, false, {activityId=1881,type=5}, nil, "2024-05-23T05:00:00", 0, 0, {16000592}, false, "", "", 0, 0, 0, nil},
	{294, "玩具商店1", 0, 99, false, {activityId=1892,type=5}, nil, "2024-05-23T05:00:00", 0, 0, {16000589}, false, "", "", 0, 0, 0, nil},
	{295, "玩具商店2", 0, 99, false, {activityId=1892,type=5}, nil, "2024-05-30T05:00:00", 0, 0, {16000589}, false, "", "", 0, 0, 0, nil},
	{296, "返场商店1", 0, 99, true, {activityId=1907,type=5}, nil, "2024-05-23T05:00:00", 0, 0, {16000488}, false, "", "", 0, 0, 0, nil},
	{297, "返场商店2", 0, 99, true, {activityId=1907,type=5}, nil, "2024-05-23T05:00:00", 0, 0, {16000488}, false, "", "", 0, 0, 0, nil},
	{298, "返场商店3", 0, 99, true, {activityId=1907,type=5}, nil, "2024-05-23T05:00:00", 0, 0, {16000488}, false, "", "", 0, 0, 0, nil},
	{299, "探案有礼1", 0, 99, false, {activityId=1918,type=5}, nil, "2024-06-07T05:00:00", 0, 0, {16000593}, false, "", "", 0, 0, 0, nil},
	{300, "探案有礼2", 0, 99, false, {activityId=1918,type=5}, nil, "2024-06-14T05:00:00", 0, 0, {16000593}, false, "", "", 0, 0, 0, nil},
	{301, "探案有礼3", 0, 99, false, {activityId=1918,type=5}, nil, "2024-06-21T05:00:00", 0, 0, {16000593}, false, "", "", 0, 0, 0, nil},
	{302, "粽香珍品", 0, 99, false, {activityId=1921,type=5}, nil, "2024-06-07T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{303, "毕业集市1", 0, 99, false, {activityId=1936,type=5}, nil, "2024-06-20T05:00:00", 0, 0, {16000594}, false, "", "", 0, 0, 0, nil},
	{304, "毕业集市2", 0, 99, false, {activityId=1936,type=5}, nil, "2024-06-27T05:00:00", 0, 0, {16000594}, false, "", "", 0, 0, 0, nil},
	{305, "返场商店1", 0, 99, true, {activityId=1965,type=5}, nil, "2024-06-20T05:00:00", 0, 0, {16000489}, false, "", "", 0, 0, 0, nil},
	{306, "返场商店2", 0, 99, true, {activityId=1965,type=5}, nil, "2024-06-20T05:00:00", 0, 0, {16000489}, false, "", "", 0, 0, 0, nil},
	{307, "返场商店3", 0, 99, true, {activityId=1965,type=5}, nil, "2024-06-20T05:00:00", 0, 0, {16000489}, false, "", "", 0, 0, 0, nil},
	{308, "海星商店1", 0, 99, false, {activityId=1981,type=5}, nil, "2024-07-04T05:00:00", 0, 0, {16000626}, false, "", "", 0, 0, 0, nil},
	{309, "海星商店2", 0, 99, false, {activityId=1981,type=5}, nil, "2024-07-11T05:00:00", 0, 0, {16000626}, false, "", "", 0, 0, 0, nil},
	{310, "海星商店3", 0, 99, false, {activityId=1981,type=5}, nil, "2024-07-18T05:00:00", 0, 0, {16000626}, false, "", "", 0, 0, 0, nil},
	{311, "烟花商城1", 0, 99, true, {activityId=1987,type=5}, nil, "2024-07-12T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{312, "烟花商城2", 0, 99, false, {activityId=1987,type=5}, nil, "2024-07-12T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{313, "派对礼物1", 0, 99, false, {activityId=1999,type=5}, nil, "2024-07-18T05:00:00", 0, 0, {16000624}, false, "", "", 0, 0, 0, nil},
	{314, "派对礼物2", 0, 99, false, {activityId=1999,type=5}, nil, "2024-07-25T05:00:00", 0, 0, {16000624}, false, "", "", 0, 0, 0, nil},
	{315, "返场商店1", 0, 99, true, {activityId=2010,type=5}, nil, "2024-07-18T05:00:00", 0, 0, {16000490}, false, "", "", 0, 0, 0, nil},
	{316, "返场商店2", 0, 99, true, {activityId=2010,type=5}, nil, "2024-07-18T05:00:00", 0, 0, {16000490}, false, "", "", 0, 0, 0, nil},
	{317, "返场商店3", 0, 99, true, {activityId=2010,type=5}, nil, "2024-07-18T05:00:00", 0, 0, {16000490}, false, "", "", 0, 0, 0, nil},
	{318, "滚滚机兑换商店", 0, 99, true, {activityId=2019,type=5}, nil, "2024-08-20T05:00:00", 0, 0, {16000650}, false, "", "", 0, 0, 0, nil},
	{319, "星际商店1", 0, 99, false, {activityId=2031,type=5}, nil, "2024-08-02T05:00:00", 0, 0, {16000638}, false, "", "", 0, 0, 0, nil},
	{320, "星际商店2", 0, 99, false, {activityId=2031,type=5}, nil, "2024-08-08T05:00:00", 0, 0, {16000638}, false, "", "", 0, 0, 0, nil},
	{321, "星际商店3", 0, 99, false, {activityId=2031,type=5}, nil, "2024-08-15T05:00:00", 0, 0, {16000638}, false, "", "", 0, 0, 0, nil},
	{322, "夏祭小摊1", 0, 99, false, {activityId=2058,type=5}, nil, "2024-08-20T05:00:00", 0, 0, {16000657}, false, "", "", 0, 0, 0, nil},
	{323, "夏祭小摊2", 0, 99, false, {activityId=2058,type=5}, nil, "2024-08-26T05:00:00", 0, 0, {16000657}, false, "", "", 0, 0, 0, nil},
	{324, "满月服装", 0, 99, true, {activityId=2054,type=5}, nil, "2024-09-12T05:00:00", 0, 0, nil, false, "", "", 0, 0, 0, nil},
	{325, "满月家具", 0, 99, true, {activityId=2054,type=5}, nil, "2024-09-12T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{326, "返场商店1", 0, 99, true, {activityId=2063,type=5}, nil, "2024-08-16T05:00:00", 0, 0, {16000491}, false, "", "", 0, 0, 0, nil},
	{327, "返场商店2", 0, 99, true, {activityId=2063,type=5}, nil, "2024-08-16T05:00:00", 0, 0, {16000491}, false, "", "", 0, 0, 0, nil},
	{328, "返场商店3", 0, 99, true, {activityId=2063,type=5}, nil, "2024-08-16T05:00:00", 0, 0, {16000491}, false, "", "", 0, 0, 0, nil},
	{329, "学院奖励1", 0, 99, false, {activityId=2075,type=5}, nil, "2024-08-29T05:00:00", 0, 0, {16000661}, false, "", "", 0, 0, 0, nil},
	{330, "学院奖励2", 0, 99, false, {activityId=2075,type=5}, nil, "2024-09-05T05:00:00", 0, 0, {16000661}, false, "", "", 0, 0, 0, nil},
	{331, "学院奖励3", 0, 99, false, {activityId=2075,type=5}, nil, "2024-09-12T05:00:00", 0, 0, {16000661}, false, "", "", 0, 0, 0, nil},
	{332, "快乐商店1", 0, 99, false, {activityId=2099,type=5}, nil, "2024-09-20T05:00:00", 0, 0, {16000665}, false, "", "", 0, 0, 0, nil},
	{333, "快乐商店2", 0, 99, false, {activityId=2099,type=5}, nil, "2024-09-27T05:00:00", 0, 0, {16000665}, false, "", "", 0, 0, 0, nil},
	{334, "快乐商店3", 0, 99, false, {activityId=2099,type=5}, nil, "2024-10-04T05:00:00", 0, 0, {16000665}, false, "", "", 0, 0, 0, nil},
	{335, "3.3.2滚滚机兑换商店", 0, 99, true, {activityId=2104,type=5}, nil, "2024-10-11T05:00:00", 0, 0, {16000678}, false, "", "", 0, 0, 0, nil},
	{336, "草莓货架1", 0, 99, false, {activityId=2105,type=5}, nil, "2024-10-11T05:00:00", 0, 0, {16000679}, false, "", "", 0, 0, 0, nil},
	{337, "草莓货架2", 0, 99, false, {activityId=2105,type=5}, nil, "2024-10-17T05:00:00", 0, 0, {16000679}, false, "", "", 0, 0, 0, nil},
	{338, "返场商店1", 0, 99, true, {activityId=2134,type=5}, nil, "2024-10-11T05:00:00", 0, 0, {16000492}, false, "", "", 0, 0, 0, nil},
	{339, "返场商店2", 0, 99, true, {activityId=2134,type=5}, nil, "2024-10-11T05:00:00", 0, 0, {16000492}, false, "", "", 0, 0, 0, nil},
	{340, "返场商店3", 0, 99, true, {activityId=2134,type=5}, nil, "2024-10-11T05:00:00", 0, 0, {16000492}, false, "", "", 0, 0, 0, nil},
	{341, "3.4.2滚滚机兑换商店", 0, 99, true, {activityId=2146,type=5}, nil, "2024-11-07T05:00:00", 0, 0, {16000695}, false, "", "", 0, 0, 0, nil},
	{342, "人偶商店1", 0, 99, false, {activityId=2143,type=5}, nil, "2024-10-24T05:00:00", 0, 0, {16000682}, false, "", "", 0, 0, 0, nil},
	{343, "人偶商店2", 0, 99, false, {activityId=2143,type=5}, nil, "2024-10-31T05:00:00", 0, 0, {16000682}, false, "", "", 0, 0, 0, nil},
	{344, "人偶商店3", 0, 99, false, {activityId=2143,type=5}, nil, "2024-11-07T05:00:00", 0, 0, {16000682}, false, "", "", 0, 0, 0, nil},
	{345, "影楼柜台1", 0, 99, false, {activityId=2152,type=5}, nil, "2024-11-07T05:00:00", 0, 0, {16000693}, false, "", "", 0, 0, 0, nil},
	{346, "影楼柜台2", 0, 99, false, {activityId=2152,type=5}, nil, "2024-11-14T05:00:00", 0, 0, {16000693}, false, "", "", 0, 0, 0, nil},
	{347, "返场商店1", 0, 99, true, {activityId=2165,type=5}, nil, "2024-11-07T05:00:00", 0, 0, {16000493}, false, "", "", 0, 0, 0, nil},
	{348, "返场商店2", 0, 99, true, {activityId=2165,type=5}, nil, "2024-11-07T05:00:00", 0, 0, {16000493}, false, "", "", 0, 0, 0, nil},
	{349, "返场商店3", 0, 99, true, {activityId=2165,type=5}, nil, "2024-11-07T05:00:00", 0, 0, {16000493}, false, "", "", 0, 0, 0, nil},
	{350, "游戏机时光", 0, 99, false, {type=6}, nil, "2024-04-03T05:00:00", 0, 0, {16000702}, false, "", "", 0, 0, 0, nil},
	{351, "雾月商店1", 0, 99, false, {activityId=2178,type=5}, nil, "2024-11-21T05:00:00", 0, 0, {16000692}, false, "", "", 0, 0, 0, nil},
	{352, "雾月商店2", 0, 99, false, {activityId=2178,type=5}, nil, "2024-11-28T05:00:00", 0, 0, {16000692}, false, "", "", 0, 0, 0, nil},
	{353, "雾月商店3", 0, 99, false, {activityId=2178,type=5}, nil, "2024-12-05T05:00:00", 0, 0, {16000692}, false, "", "", 0, 0, 0, nil},
	{354, "3.5.2滚滚机兑换商店", 0, 99, true, {activityId=2183,type=5}, nil, "2024-12-05T05:00:00", 0, 0, {16000700}, false, "", "", 0, 0, 0, nil},
	{355, "小丑商店1", 0, 99, false, {activityId=2180,type=5}, nil, "2024-12-05T05:00:00", 0, 0, {16000698}, false, "", "", 0, 0, 0, nil},
	{356, "小丑商店2", 0, 99, false, {activityId=2180,type=5}, nil, "2024-12-12T05:00:00", 0, 0, {16000698}, false, "", "", 0, 0, 0, nil},
	{357, "3.6.2滚滚机兑换商店", 0, 99, true, {activityId=2189,type=5}, nil, "2025-01-09T05:00:00", 0, 0, {16000709}, false, "", "", 0, 0, 0, nil},
	{358, "恋冬小铺1", 0, 99, false, {activityId=2186,type=5}, nil, "2025-01-09T05:00:00", 0, 0, {16000707}, false, "", "", 0, 0, 0, nil},
	{359, "恋冬小铺2", 0, 99, false, {activityId=2186,type=5}, nil, "2025-01-16T05:00:00", 0, 0, {16000707}, false, "", "", 0, 0, 0, nil},
	{360, "新年之潮", 0, 99, false, {activityId=2207,type=5}, nil, "2024-12-28T05:00:00", 0, 0, {1,2}, false, "", "", 0, 0, 0, nil},
	{361, "岁月重燃", 0, 99, true, {activityId=2207,type=5}, nil, "2024-12-28T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{362, "返场商店1", 0, 99, true, {activityId=2214,type=5}, nil, "2024-12-05T05:00:00", 0, 0, {16000494}, false, "", "", 0, 0, 0, nil},
	{363, "返场商店2", 0, 99, true, {activityId=2214,type=5}, nil, "2024-12-05T05:00:00", 0, 0, {16000494}, false, "", "", 0, 0, 0, nil},
	{364, "返场商店3", 0, 99, true, {activityId=2214,type=5}, nil, "2024-12-05T05:00:00", 0, 0, {16000494}, false, "", "", 0, 0, 0, nil},
	{365, "凛冬商店1", 0, 99, false, {activityId=2218,type=5}, nil, "2024-12-19T05:00:00", 0, 0, {16000712}, false, "", "", 0, 0, 0, nil},
	{366, "凛冬商店2", 0, 99, false, {activityId=2218,type=5}, nil, "2024-12-26T05:00:00", 0, 0, {16000712}, false, "", "", 0, 0, 0, nil},
	{367, "凛冬商店3", 0, 99, false, {activityId=2218,type=5}, nil, "2025-01-02T05:00:00", 0, 0, {16000712}, false, "", "", 0, 0, 0, nil},
	{368, "同福客栈1", 0, 99, false, {activityId=2262,type=5}, nil, "2025-01-22T05:00:00", 0, 0, {16000736}, false, "", "", 0, 0, 0, nil},
	{369, "同福客栈2", 0, 99, false, {activityId=2262,type=5}, nil, "2025-01-29T05:00:00", 0, 0, {16000736}, false, "", "", 0, 0, 0, nil},
	{370, "同福客栈3", 0, 99, false, {activityId=2262,type=5}, nil, "2025-02-05T05:00:00", 0, 0, {16000736}, false, "", "", 0, 0, 0, nil},
	{371, "返场商店1", 0, 99, true, {activityId=2253,type=5}, nil, "2025-01-09T05:00:00", 0, 0, {16081001}, false, "", "", 0, 0, 0, nil},
	{372, "返场商店2", 0, 99, true, {activityId=2253,type=5}, nil, "2025-01-09T05:00:00", 0, 0, {16081001}, false, "", "", 0, 0, 0, nil},
	{373, "返场商店3", 0, 99, true, {activityId=2253,type=5}, nil, "2025-01-09T05:00:00", 0, 0, {16081001}, false, "", "", 0, 0, 0, nil},
	{374, "3.7.2滚滚机兑换商店", 0, 99, true, {activityId=2277,type=5}, nil, "2025-02-10T05:00:00", 0, 0, {16000742}, false, "", "", 0, 0, 0, nil},
	{375, "花灯商店1", 0, 99, false, {activityId=2279,type=5}, nil, "2025-02-10T05:00:00", 0, 0, {16000740}, false, "", "", 0, 0, 0, nil},
	{376, "花灯商店1", 0, 99, false, {activityId=2279,type=5}, nil, "2025-02-15T05:00:00", 0, 0, {16000740}, false, "", "", 0, 0, 0, nil},
	{377, "灯会小摊", 0, 99, true, {activityId=2285,type=5}, nil, "2025-02-10T05:00:00", 0, 0, {2,5}, false, "", "", 0, 0, 0, nil},
	{378, "返场商店1", 0, 99, true, {activityId=2301,type=5}, nil, "2025-02-07T05:00:00", 0, 0, {16081002}, false, "", "", 0, 0, 0, nil},
	{379, "返场商店2", 0, 99, true, {activityId=2301,type=5}, nil, "2025-02-07T05:00:00", 0, 0, {16081002}, false, "", "", 0, 0, 0, nil},
	{380, "返场商店3", 0, 99, true, {activityId=2301,type=5}, nil, "2025-02-07T05:00:00", 0, 0, {16081002}, false, "", "", 0, 0, 0, nil},
	{381, "百花商店1", 0, 99, false, {activityId=2297,type=5}, nil, "2025-02-21T05:00:00", 0, 0, {16000745}, false, "", "", 0, 0, 0, nil},
	{382, "百花商店2", 0, 99, false, {activityId=2297,type=5}, nil, "2025-02-27T05:00:00", 0, 0, {16000745}, false, "", "", 0, 0, 0, nil},
	{383, "百花商店3", 0, 99, false, {activityId=2297,type=5}, nil, "2025-03-06T05:00:00", 0, 0, {16000745}, false, "", "", 0, 0, 0, nil},
	{384, "漫展摊位1", 0, 99, false, {activityId=2316,type=5}, nil, "2025-03-06T05:00:00", 0, 0, {16000746}, false, "", "", 0, 0, 0, nil},
	{385, "漫展摊位2", 0, 99, false, {activityId=2316,type=5}, nil, "2025-03-13T05:00:00", 0, 0, {16000746}, false, "", "", 0, 0, 0, nil},
	{386, "3.8.2滚滚机兑换商店", 0, 99, true, {activityId=2312,type=5}, nil, "2025-03-06T05:00:00", 0, 0, {16000748}, false, "", "", 0, 0, 0, nil},
	{387, "返场商店1", 0, 99, true, {activityId=2323,type=5}, nil, "2025-03-06T05:00:00", 0, 0, {16081003}, false, "", "", 0, 0, 0, nil},
	{388, "返场商店2", 0, 99, true, {activityId=2323,type=5}, nil, "2025-03-06T05:00:00", 0, 0, {16081003}, false, "", "", 0, 0, 0, nil},
	{389, "返场商店3", 0, 99, true, {activityId=2323,type=5}, nil, "2025-03-06T05:00:00", 0, 0, {16081003}, false, "", "", 0, 0, 0, nil},
	{390, "心森商店1", 0, 99, false, {activityId=2345,type=5}, nil, "2025-03-20T05:00:00", 0, 0, {16000773}, false, "", "", 0, 0, 0, nil},
	{391, "心森商店2", 0, 99, false, {activityId=2345,type=5}, nil, "2025-03-27T05:00:00", 0, 0, {16000773}, false, "", "", 0, 0, 0, nil},
	{392, "心森商店3", 0, 99, false, {activityId=2345,type=5}, nil, "2025-04-03T05:00:00", 0, 0, {16000773}, false, "", "", 0, 0, 0, nil},
	{393, "艺展商店", 0, 99, false, {type=6}, nil, "2025-03-20T05:00:00", 0, 0, {16000764}, false, "", "", 0, 0, 0, nil},
	{394, "3.9.2滚滚机兑换商店", 0, 99, true, {activityId=2355,type=5}, nil, "2025-04-03T05:00:00", 0, 0, {16000763}, false, "", "", 0, 0, 0, nil},
	{395, "巡游小摊1", 0, 99, false, {activityId=2352,type=5}, nil, "2025-04-03T05:00:00", 0, 0, {16000752}, false, "", "", 0, 0, 0, nil},
	{396, "巡游小摊2", 0, 99, false, {activityId=2352,type=5}, nil, "2025-04-10T05:00:00", 0, 0, {16000752}, false, "", "", 0, 0, 0, nil},
	{397, "返场商店1", 0, 99, true, {activityId=2360,type=5}, nil, "2025-04-03T05:00:00", 0, 0, {16081004}, false, "", "", 0, 0, 0, nil},
	{398, "返场商店2", 0, 99, true, {activityId=2360,type=5}, nil, "2025-04-03T05:00:00", 0, 0, {16081004}, false, "", "", 0, 0, 0, nil},
	{399, "返场商店3", 0, 99, true, {activityId=2360,type=5}, nil, "2025-04-03T05:00:00", 0, 0, {16081004}, false, "", "", 0, 0, 0, nil},
	{400, "港城纪念1", 0, 99, false, {activityId=2386,type=5}, nil, "2025-04-17T05:00:00", 0, 0, {16000779}, false, "", "", 0, 0, 0, nil},
	{401, "港城纪念2", 0, 99, false, {activityId=2386,type=5}, nil, "2025-04-24T05:00:00", 0, 0, {16000779}, false, "", "", 0, 0, 0, nil},
	{402, "港城纪念3", 0, 99, false, {activityId=2386,type=5}, nil, "2025-05-01T05:00:00", 0, 0, {16000779}, false, "", "", 0, 0, 0, nil},
	{405, "返场商店1", 0, 99, true, {activityId=2402,type=5}, nil, "2025-04-30T05:00:00", 0, 0, {16081005}, false, "", "", 0, 0, 0, nil},
	{406, "返场商店2", 0, 99, true, {activityId=2402,type=5}, nil, "2025-04-30T05:00:00", 0, 0, {16081005}, false, "", "", 0, 0, 0, nil},
	{407, "返场商店3", 0, 99, true, {activityId=2402,type=5}, nil, "2025-04-30T05:00:00", 0, 0, {16081005}, false, "", "", 0, 0, 0, nil},
	{408, "市集商店1", 0, 99, false, {activityId=2411,type=5}, nil, "2025-05-15T05:00:00", 0, 0, {16000781}, false, "", "", 0, 0, 0, nil},
	{409, "市集商店2", 0, 99, false, {activityId=2411,type=5}, nil, "2025-05-22T05:00:00", 0, 0, {16000781}, false, "", "", 0, 0, 0, nil},
	{410, "市集商店3", 0, 99, false, {activityId=2411,type=5}, nil, "2025-05-29T05:00:00", 0, 0, {16000781}, false, "", "", 0, 0, 0, nil},
	{411, "返场商店1", 0, 99, true, {activityId=2433,type=5}, nil, "2025-06-19T05:00:00", 0, 0, {16081006}, false, "", "", 0, 0, 0, nil},
	{412, "返场商店2", 0, 99, true, {activityId=2433,type=5}, nil, "2025-06-19T05:00:00", 0, 0, {16081006}, false, "", "", 0, 0, 0, nil},
	{413, "返场商店3", 0, 99, true, {activityId=2433,type=5}, nil, "2025-06-19T05:00:00", 0, 0, {16081006}, false, "", "", 0, 0, 0, nil},
	{414, "素材商店1", 0, 99, false, {activityId=2437,type=5}, nil, "2025-06-07T05:00:00", 0, 0, {16000783}, false, "", "", 0, 0, 0, nil},
	{415, "素材商店2", 0, 99, false, {activityId=2437,type=5}, nil, "2025-06-14T05:00:00", 0, 0, {16000783}, false, "", "", 0, 0, 0, nil},
	{416, "素材商店3", 0, 99, false, {activityId=2437,type=5}, nil, "2025-06-21T05:00:00", 0, 0, {16000783}, false, "", "", 0, 0, 0, nil},
	{417, "烟花商城1", 0, 99, true, {activityId=2486,type=5}, nil, "2025-07-11T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{418, "烟花商城2", 0, 99, false, {activityId=2486,type=5}, nil, "2025-07-11T05:00:00", 0, 0, {2}, false, "", "", 0, 0, 0, nil},
	{419, "奇想商店1", 0, 99, false, {activityId=2479,type=5}, nil, "2025-07-03T05:00:00", 0, 0, {16000786}, false, "", "", 0, 0, 0, nil},
	{420, "奇想商店2", 0, 99, false, {activityId=2479,type=5}, nil, "2025-07-10T05:00:00", 0, 0, {16000786}, false, "", "", 0, 0, 0, nil},
	{421, "奇想商店3", 0, 99, false, {activityId=2479,type=5}, nil, "2025-07-17T05:00:00", 0, 0, {16000786}, false, "", "", 0, 0, 0, nil},
	{422, "宜居家具市场", 0, 99, true, {activityId=2489,type=5}, nil, "2025-07-03T05:00:00", 0, 0, {16000810}, false, "", "", 0, 0, 0, nil},
	{423, "宜居装饰市场", 0, 99, true, {activityId=2489,type=5}, nil, "2025-07-03T05:00:00", 0, 0, {16000810}, false, "", "", 0, 0, 0, nil},
	{424, "4.0.2滚滚机兑换商店", 0, 99, true, {activityId=2496,type=5}, nil, "2025-07-17T05:00:00", 0, 0, {16000814}, false, "", "", 0, 0, 0, nil},
	{425, "牵牛小铺1", 0, 99, false, {activityId=2506,type=5}, nil, "2025-07-17T05:00:00", 0, 0, {16000752}, false, "", "", 0, 0, 0, nil},
	{426, "牵牛小铺2", 0, 99, false, {activityId=2506,type=5}, nil, "2025-07-24T05:00:00", 0, 0, {16000752}, false, "", "", 0, 0, 0, nil},
	{427, "返场商店1", 0, 99, true, {activityId=2512,type=5}, nil, "2025-07-17T05:00:00", 0, 0, {16081004}, false, "", "", 0, 0, 0, nil},
	{428, "返场商店2", 0, 99, true, {activityId=2512,type=5}, nil, "2025-07-17T05:00:00", 0, 0, {16081004}, false, "", "", 0, 0, 0, nil},
	{429, "返场商店3", 0, 99, true, {activityId=2512,type=5}, nil, "2025-07-17T05:00:00", 0, 0, {16081004}, false, "", "", 0, 0, 0, nil},
}

local t_grocery = {
	[1] = dataList[1],
	[2] = dataList[2],
	[3] = dataList[3],
	[4] = dataList[4],
	[5] = dataList[5],
	[6] = dataList[6],
	[7] = dataList[7],
	[8] = dataList[8],
	[9] = dataList[9],
	[10] = dataList[10],
	[11] = dataList[11],
	[12] = dataList[12],
	[13] = dataList[13],
	[1301] = dataList[14],
	[1302] = dataList[15],
	[14] = dataList[16],
	[1401] = dataList[17],
	[1402] = dataList[18],
	[15] = dataList[19],
	[16] = dataList[20],
	[17] = dataList[21],
	[19] = dataList[22],
	[20] = dataList[23],
	[21] = dataList[24],
	[2001] = dataList[25],
	[2002] = dataList[26],
	[2003] = dataList[27],
	[22] = dataList[28],
	[23] = dataList[29],
	[24] = dataList[30],
	[25] = dataList[31],
	[26] = dataList[32],
	[105] = dataList[33],
	[112] = dataList[34],
	[113] = dataList[35],
	[114] = dataList[36],
	[122] = dataList[37],
	[127] = dataList[38],
	[128] = dataList[39],
	[129] = dataList[40],
	[139] = dataList[41],
	[140] = dataList[42],
	[141] = dataList[43],
	[155] = dataList[44],
	[156] = dataList[45],
	[157] = dataList[46],
	[165] = dataList[47],
	[168] = dataList[48],
	[177] = dataList[49],
	[178] = dataList[50],
	[179] = dataList[51],
	[180] = dataList[52],
	[181] = dataList[53],
	[182] = dataList[54],
	[183] = dataList[55],
	[184] = dataList[56],
	[185] = dataList[57],
	[186] = dataList[58],
	[187] = dataList[59],
	[188] = dataList[60],
	[189] = dataList[61],
	[190] = dataList[62],
	[191] = dataList[63],
	[192] = dataList[64],
	[193] = dataList[65],
	[194] = dataList[66],
	[195] = dataList[67],
	[196] = dataList[68],
	[197] = dataList[69],
	[198] = dataList[70],
	[199] = dataList[71],
	[200] = dataList[72],
	[201] = dataList[73],
	[202] = dataList[74],
	[203] = dataList[75],
	[204] = dataList[76],
	[205] = dataList[77],
	[206] = dataList[78],
	[207] = dataList[79],
	[208] = dataList[80],
	[209] = dataList[81],
	[210] = dataList[82],
	[211] = dataList[83],
	[212] = dataList[84],
	[213] = dataList[85],
	[214] = dataList[86],
	[215] = dataList[87],
	[216] = dataList[88],
	[217] = dataList[89],
	[218] = dataList[90],
	[219] = dataList[91],
	[220] = dataList[92],
	[221] = dataList[93],
	[222] = dataList[94],
	[224] = dataList[95],
	[225] = dataList[96],
	[226] = dataList[97],
	[227] = dataList[98],
	[228] = dataList[99],
	[229] = dataList[100],
	[230] = dataList[101],
	[231] = dataList[102],
	[232] = dataList[103],
	[233] = dataList[104],
	[234] = dataList[105],
	[235] = dataList[106],
	[236] = dataList[107],
	[237] = dataList[108],
	[238] = dataList[109],
	[239] = dataList[110],
	[240] = dataList[111],
	[241] = dataList[112],
	[242] = dataList[113],
	[243] = dataList[114],
	[244] = dataList[115],
	[245] = dataList[116],
	[246] = dataList[117],
	[247] = dataList[118],
	[248] = dataList[119],
	[249] = dataList[120],
	[250] = dataList[121],
	[251] = dataList[122],
	[252] = dataList[123],
	[253] = dataList[124],
	[254] = dataList[125],
	[255] = dataList[126],
	[256] = dataList[127],
	[257] = dataList[128],
	[258] = dataList[129],
	[259] = dataList[130],
	[260] = dataList[131],
	[261] = dataList[132],
	[262] = dataList[133],
	[263] = dataList[134],
	[264] = dataList[135],
	[265] = dataList[136],
	[266] = dataList[137],
	[267] = dataList[138],
	[268] = dataList[139],
	[269] = dataList[140],
	[270] = dataList[141],
	[271] = dataList[142],
	[272] = dataList[143],
	[273] = dataList[144],
	[274] = dataList[145],
	[275] = dataList[146],
	[276] = dataList[147],
	[277] = dataList[148],
	[278] = dataList[149],
	[279] = dataList[150],
	[280] = dataList[151],
	[281] = dataList[152],
	[282] = dataList[153],
	[283] = dataList[154],
	[284] = dataList[155],
	[285] = dataList[156],
	[286] = dataList[157],
	[287] = dataList[158],
	[288] = dataList[159],
	[289] = dataList[160],
	[290] = dataList[161],
	[291] = dataList[162],
	[292] = dataList[163],
	[293] = dataList[164],
	[294] = dataList[165],
	[295] = dataList[166],
	[296] = dataList[167],
	[297] = dataList[168],
	[298] = dataList[169],
	[299] = dataList[170],
	[300] = dataList[171],
	[301] = dataList[172],
	[302] = dataList[173],
	[303] = dataList[174],
	[304] = dataList[175],
	[305] = dataList[176],
	[306] = dataList[177],
	[307] = dataList[178],
	[308] = dataList[179],
	[309] = dataList[180],
	[310] = dataList[181],
	[311] = dataList[182],
	[312] = dataList[183],
	[313] = dataList[184],
	[314] = dataList[185],
	[315] = dataList[186],
	[316] = dataList[187],
	[317] = dataList[188],
	[318] = dataList[189],
	[319] = dataList[190],
	[320] = dataList[191],
	[321] = dataList[192],
	[322] = dataList[193],
	[323] = dataList[194],
	[324] = dataList[195],
	[325] = dataList[196],
	[326] = dataList[197],
	[327] = dataList[198],
	[328] = dataList[199],
	[329] = dataList[200],
	[330] = dataList[201],
	[331] = dataList[202],
	[332] = dataList[203],
	[333] = dataList[204],
	[334] = dataList[205],
	[335] = dataList[206],
	[336] = dataList[207],
	[337] = dataList[208],
	[338] = dataList[209],
	[339] = dataList[210],
	[340] = dataList[211],
	[341] = dataList[212],
	[342] = dataList[213],
	[343] = dataList[214],
	[344] = dataList[215],
	[345] = dataList[216],
	[346] = dataList[217],
	[347] = dataList[218],
	[348] = dataList[219],
	[349] = dataList[220],
	[350] = dataList[221],
	[351] = dataList[222],
	[352] = dataList[223],
	[353] = dataList[224],
	[354] = dataList[225],
	[355] = dataList[226],
	[356] = dataList[227],
	[357] = dataList[228],
	[358] = dataList[229],
	[359] = dataList[230],
	[360] = dataList[231],
	[361] = dataList[232],
	[362] = dataList[233],
	[363] = dataList[234],
	[364] = dataList[235],
	[365] = dataList[236],
	[366] = dataList[237],
	[367] = dataList[238],
	[368] = dataList[239],
	[369] = dataList[240],
	[370] = dataList[241],
	[371] = dataList[242],
	[372] = dataList[243],
	[373] = dataList[244],
	[374] = dataList[245],
	[375] = dataList[246],
	[376] = dataList[247],
	[377] = dataList[248],
	[378] = dataList[249],
	[379] = dataList[250],
	[380] = dataList[251],
	[381] = dataList[252],
	[382] = dataList[253],
	[383] = dataList[254],
	[384] = dataList[255],
	[385] = dataList[256],
	[386] = dataList[257],
	[387] = dataList[258],
	[388] = dataList[259],
	[389] = dataList[260],
	[390] = dataList[261],
	[391] = dataList[262],
	[392] = dataList[263],
	[393] = dataList[264],
	[394] = dataList[265],
	[395] = dataList[266],
	[396] = dataList[267],
	[397] = dataList[268],
	[398] = dataList[269],
	[399] = dataList[270],
	[400] = dataList[271],
	[401] = dataList[272],
	[402] = dataList[273],
	[405] = dataList[274],
	[406] = dataList[275],
	[407] = dataList[276],
	[408] = dataList[277],
	[409] = dataList[278],
	[410] = dataList[279],
	[411] = dataList[280],
	[412] = dataList[281],
	[413] = dataList[282],
	[414] = dataList[283],
	[415] = dataList[284],
	[416] = dataList[285],
	[417] = dataList[286],
	[418] = dataList[287],
	[419] = dataList[288],
	[420] = dataList[289],
	[421] = dataList[290],
	[422] = dataList[291],
	[423] = dataList[292],
	[424] = dataList[293],
	[425] = dataList[294],
	[426] = dataList[295],
	[427] = dataList[296],
	[428] = dataList[297],
	[429] = dataList[298],
}

t_grocery.dataList = dataList
local mt
if GroceryDefine then
	mt = {
		__cname =  "GroceryDefine",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or GroceryDefine[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_grocery