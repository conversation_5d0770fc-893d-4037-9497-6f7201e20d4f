module("logic.scene.common.action.JoinWeatherUmbrellaAction",package.seeall)

local JoinWeatherUmbrellaAction = class("JoinWeatherUmbrellaAction",SceneActionBase)

function JoinWeatherUmbrellaAction:onStart()
	local player = SceneManager.instance:getCurScene():getPlayer(self.params[1])
	if player then
		local userPlayer = SceneManager.instance:getCurScene():getUserPlayer()
		local x1 ,y1 = userPlayer:getPos()
		local x2, y2 = player:getPos()
		if (x1-x2)^2 + (y1-y2)^2 < 16 then
			self.params.forceStop = true
			SceneAgent.instance:sendSceneShareRequest(PlayerActionType.JoinWeatherUmbrella.typeId, self.params, handler(self.onShareResponse, self))
			return
		else 
			FlyTextManager.instance:showFlyText(lang("距离太远了~")) 
		end
	end
	self:finish(false)
end

function JoinWeatherUmbrellaAction:onShareResponse(status)
	if status == 0 then
		TaskCamCmdHelper.focusCharacter(0)
		SceneController.instance:registerLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		ViewMgr.instance:open("JoinUmbrellaPanel")
		SceneManager.instance:getCurScene():getUserPlayer():setTriggerEnable(false)
	else
		DialogHelper.showErrorMsg(status)
		self:finish(false)
	end
end

function JoinWeatherUmbrellaAction:onStop()
	self:finish(true)
	SceneAgent.instance:sendCancelSceneShareRequest(PlayerActionType.JoinWeatherUmbrella.typeId, handler(self.onCancelRequest, self))
	SceneManager.instance:getCurScene():getUserPlayer():setTriggerEnable(true)
	ViewMgr.instance:close("JoinUmbrellaPanel")
end

function JoinWeatherUmbrellaAction:onCancelRequest(status)
	SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
end

function JoinWeatherUmbrellaAction:onActionChange(nowState, oldState)
	if oldState == PlayerActionType.JoinWeatherUmbrella and nowState ~= PlayerActionType.JoinWeatherUmbrella then
		SceneController.instance:unregisterLocalNotify(SceneNotify.PlayerActionChange, self.onActionChange, self)
		SceneManager.instance:getCurScene():getUserPlayer():setTriggerEnable(true)		
		ViewMgr.instance:close("JoinUmbrellaPanel")
		self:finish(true)
	end
end

function JoinWeatherUmbrellaAction:getType()
	return SceneActionType.JoinWeatherUmbrella
end


return JoinWeatherUmbrellaAction