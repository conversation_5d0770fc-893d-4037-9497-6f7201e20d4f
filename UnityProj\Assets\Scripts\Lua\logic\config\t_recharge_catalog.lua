-- {excel:C充值配置表.xlsx, sheetName:export_显示分类}AUTO GENERATED. DO NOT MODIFY !!!
module("logic.config.t_recharge_catalog", package.seeall)

local title = {catalog=1,name=2,parent=3,currencyId=4,style=5,part=6,pageClass=7,isLimit=8,buyConfirmer=9,iconUrl=10,txtUnselectedUrl=11,txtSelectedUrl=12,txtOffset=13,logOpenId=14,ignoreSoldOut=15}

local dataList = {
	{11, "新品", 0, 0, 0, 0, "RechargeStorePageNewGoods", false, 5, "com_icon_xinpin", "aobibaihuo_btn_xinpin_01", "aobibaihuo_btn_xinpin_02", nil, 0, false},
	{1, "推荐", 0, 0, -1, 0, "RechargeStorePageRecommend", false, 5, "com_icon_tuijian", "aobibaihuo_btn_tuijian_01", "aobibaihuo_btn_tuijian_02", {0,0}, 160, false},
	{101, "熊次元特惠", 1, 0, 0, 0, "", false, 5, "", "", "", nil, 0, false},
	{106, "夏花特惠", 1, 0, 0, 0, "", false, 5, "", "", "", nil, 0, false},
	{102, "行星记特惠", 1, 0, 0, 0, "", false, 5, "", "", "", nil, 0, false},
	{103, "优米鹿特惠", 1, 0, 0, 0, "", false, 5, "", "", "", nil, 0, false},
	{104, "龙之心特惠", 1, 0, 0, 0, "", false, 5, "", "", "", nil, 0, false},
	{105, "返场好礼", 1, 0, 0, 0, "", false, 5, "", "", "", nil, 0, false},
	{107, "日常好礼", 1, 0, 0, 0, "", false, 5, "", "", "", nil, 0, false},
	{5, "礼包", 0, 0, 0, 0, "", false, 0, "com_icon_libao", "aobibaihuo_btn_libao_01", "aobibaihuo_btn_libao_02", {0,0}, 164, false},
	{501, "新手", 5, 0, 0, 0, "RechargeStorePageNormal2", false, 5, "aobibaihuo_ico_xinshou", "", "", nil, 0, false},
	{502, "超值", 5, 0, 0, 0, "RechargeStorePageNormal2", false, 5, "aobibaihuo_ico_chaozhi", "", "", nil, 0, false},
	{504, "限时", 5, 0, 0, 0, "RechargeStorePageNormal2", false, 5, "aobibaihuo_ico_xianshi", "", "", nil, 0, false},
	{2, "道具", 0, 0, 0, 0, "", false, 0, "com_icon_daoju", "aobibaihuo_btn_daoju_01", "aobibaihuo_btn_daoju_02", {0,0}, 161, false},
	{201, "消耗", 2, 0, 1, 0, "RechargeStorePageNormal2", false, 2, "aobibaihuo_ico_jinying", "", "", nil, 0, false},
	{20201, "经营", 201, 0, 1, 0, "RechargeStorePageNormal3", false, 2, "aobibaihuo_ico_jinying", "", "", nil, 0, false},
	{20202, "装扮", 201, 0, 1, 0, "RechargeStorePageNormal3", false, 2, "aobibaihuo_ico_fuzhuang", "", "", nil, 0, false},
	{20203, "社交", 201, 0, 1, 0, "RechargeStorePageNormal3", false, 2, "aobibaihuo_ico_shejiao", "", "", nil, 0, false},
	{202, "互动", 2, 0, 1, 0, "RechargeStorePageNormal2", false, 2, "aobibaihuo_ico_jinying", "", "", nil, 0, false},
	{207, "演奏", 2, 0, 1, 0, "RechargeStorePageInstrument", false, 2, "aobibaihuo_icon_yanzou", "", "", nil, 0, true},
	{204, "动作", 2, 0, 1, 0, "RechargeStorePageNormal2", false, 2, "aobibaihuo_ico_biaoqing", "", "", nil, 0, false},
	{6, "红宝石", 0, 0, 0, 0, "RechargeStorePageNormal2", true, 0, "aobibaihuo_ico_hongbaoshi", "aobibaihuo_btn_hongbaoshi_01", "aobibaihuo_btn_hongbaoshi_02", nil, 0, false},
	{601, "礼包", 6, 0, 0, 0, "RechargeStorePageNormal2", true, 5, "aobibaihuo_ico_hongbaoshi", "", "", nil, 0, false},
	{602, "道具", 6, 0, 1, 0, "RechargeStorePageNormal2", true, 2, "aobibaihuo_ico_hongbaoshi", "", "", nil, 0, false},
	{7, "直购", 0, 5, 0, 0, "RechargeStorePageNormal1", false, 1, "com_icon_zhigou", "aobibaihuo_btn_zhigou_01", "aobibaihuo_btn_zhigou_02", {0,0}, 166, false},
	{506, "日常", 5, 0, 0, 0, "RechargeStorePageNormal2", false, 5, "aobibaihuo_ico_xianshi", "", "", nil, 0, false},
	{10, "赠礼", 0, 0, 0, 0, "", false, 0, "com_icon_zengli", "aobibaihuo_btn_zengli_01", "aobibaihuo_btn_zengli_02", nil, 0, false},
	{1001, "特权", 10, 0, 1, 0, "RechargeStorePageNormal2", false, 5, "aobibaihuo_ico_chaozhi", "", "", nil, 0, false},
	{1003, "礼包", 10, 0, 1, 0, "RechargeStorePageNormal2", false, 5, "aobibaihuo_ico_huodong", "", "", nil, 0, false},
	{1002, "道具", 10, 0, 1, 0, "RechargeStorePageNormal2", false, 5, "aobibaihuo_ico_jinying", "", "", nil, 0, false},
}

local t_recharge_catalog = {
	[11] = dataList[1],
	[1] = dataList[2],
	[101] = dataList[3],
	[106] = dataList[4],
	[102] = dataList[5],
	[103] = dataList[6],
	[104] = dataList[7],
	[105] = dataList[8],
	[107] = dataList[9],
	[5] = dataList[10],
	[501] = dataList[11],
	[502] = dataList[12],
	[504] = dataList[13],
	[2] = dataList[14],
	[201] = dataList[15],
	[20201] = dataList[16],
	[20202] = dataList[17],
	[20203] = dataList[18],
	[202] = dataList[19],
	[207] = dataList[20],
	[204] = dataList[21],
	[6] = dataList[22],
	[601] = dataList[23],
	[602] = dataList[24],
	[7] = dataList[25],
	[506] = dataList[26],
	[10] = dataList[27],
	[1001] = dataList[28],
	[1003] = dataList[29],
	[1002] = dataList[30],
}

t_recharge_catalog.dataList = dataList
local mt
if RechargeCatalog then
	mt = {
		__cname =  "RechargeCatalog",
		__index = function(t, key)
			local index = title[key]		
			if index then
				return t[index]
			else
				return rawget(t, key) or RechargeCatalog[key]
			end	
		end
	}
else
	mt = {
		__index = function(t, key)
			local index = title[key]
			if index then
				return rawget(t, index)
			end
		end
	}
end


for i,v in ipairs(dataList) do
	setmetatable(v, mt)
	if v.init then
		v:init()
	end	
end

return t_recharge_catalog