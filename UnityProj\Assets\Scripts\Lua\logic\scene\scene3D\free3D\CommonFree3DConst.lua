module("logic.scene.scene3D.free3D.CommonFree3DConst", package.seeall)

local CommonFree3DConst = {}

-- 摄像机参数 --

CommonFree3DConst.camFar = 20 -- 与目标的初始距离
CommonFree3DConst.camFarMin = 0 -- 与目标最近的距离
CommonFree3DConst.camFarMax = 20 -- 与目标最远的距离

CommonFree3DConst.camHig = 3 -- 相机的初始高度
CommonFree3DConst.camHigMin = 0 -- 相机最小高度
CommonFree3DConst.camHigMax = 999 -- 相机最大高度

CommonFree3DConst.camFocus = 1.8 -- 相机的聚焦高度
CommonFree3DConst.camRotaH = 270 --初始的水平旋转量
CommonFree3DConst.camRotaV = 5 -- 初始的垂直旋转量
CommonFree3DConst.camRotaVMin = -10 -- 垂直旋转量下限
CommonFree3DConst.camRotaVMax = 5 -- 垂直旋转量上限

CommonFree3DConst.camMoveSpeedH = 8 -- 水平方向移动速度
CommonFree3DConst.camMoveSpeedV = 8 -- 垂直方向移动速度
CommonFree3DConst.camRotaSpeedH = 300 -- 水平方向旋转速度
CommonFree3DConst.camRotaSpeedV = 200 -- 垂直方向旋转速度

CommonFree3DConst.showPlayerMin = 2.2 -- 最近距离显示玩家
CommonFree3DConst.showPlayerMax = 9999 -- 最远距离显示玩家

return CommonFree3DConst
