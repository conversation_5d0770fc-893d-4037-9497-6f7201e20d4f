module("logic.extensions.scene.controller.SceneController", package.seeall)
local SceneController = class("SceneController", BaseController)

local posIds = {}
posIds[4] = 1
posIds[9] = 2
posIds[10] = 3
posIds[80] = 4
posIds[81] = 5
posIds[82] = 6
posIds[5] = 0
posIds[79] = 1
posIds[109] = 2
--to be overrided
function SceneController:onInit()
	self.cacheRoleInfos = {}
	self.cacheSceneInfos = {}
	self.cacheSceneEntryInfos = {}
end

function SceneController:onReset()
end

--走向场景中的一个位置
--传3D场景一个位置的时候,注意posZ是根据XZ去计算的,所以传的时候传入目标点的x,z->posX,posY.
--walkto结束后会callback  会传出一个是否成功的参数 例如用一个function(suc)去接收
--targetRange:是到达目标点范围附近会停下
function SceneController:walkTo(posX, posY, posZ, canJump, callback, targetRange)
	local params = {}
	params.targetPos = {posX = posX, posY = posY, posZ = posZ}
	params.canJump = canJump
	params.targetRange = targetRange
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Walk, params, callback)
end

function SceneController:stopWalk()
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.StopWalk, nil, nil, true)
end

function SceneController:teleport(posX, posY, posZ, force, callBack, preventTips)
	local params = {}
	params.targetPos = {posX = posX, posY = posY, posZ = posZ}
	params.forceJump = force
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Teleport, params, callBack, preventTips)
end

function SceneController:jump(posX, posY, type, callback, posZ)
	local params = {}
	params.targetPos = {posX = posX, posY = posY, posZ=posZ}
	params.forceJump = true
	params.type = type
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Jump, params, callback)
end

function SceneController:snowBallHit(posX, posY, type, toOutArea, callback)
	local params = {}
	params.targetPos = {posX = posX, posY = posY}
	params.type = type
	params.toOutArea = toOutArea
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Jump, params, callback)
end

function SceneController:knockout(posX, posY, hitUpAni, hitDownAni, callback)
	local params = {}
	params.targetPos = {posX = posX, posY = posY}
	params.type = SpecialMoveType.SnowBallHit
	params.toOutArea = true
	params.hitUpAni = hitUpAni
	params.hitDownAni = hitDownAni
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Jump, params, callback)
end

--被食人花丢
function SceneController:huaYunFly(posX, posY, callback)
	local params = {}
	params.targetPos = {posX = posX, posY = posY}
	params.type = SpecialMoveType.HuaYunAway
	params.forceJump = true
	SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Jump, params, callback)
end

-- 4.0周年庆新增，被场景里的猫弹开
function SceneController:dreamRoomCatThrow(posX, posY, callback)
	local params = {}
	params.targetPos = {posX = posX, posY = posY}
	params.type = SpecialMoveType.DreamRoomCatThrow
	params.forceJump = true
	SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Jump, params, callback)
end

function SceneController:sitChair(walkPos, chairInfo, canJump)
	return self:useObj(walkPos, chairInfo, UseObjectType.Sit, canJump)
end

function SceneController:useObj(walkPos, triggerId, actionType, canJump, getTriggerIdFunc, param)
	local params = {}
	local targetPos = {}
	targetPos.posX = walkPos.x
	targetPos.posY = walkPos.y
	params.targetPos = targetPos
	params.triggerId = triggerId
	params.canJump = canJump
	params.type = actionType
	params.getTriggerId = getTriggerIdFunc
	params.param = param
	if canJump then
		params.checkJumpWalkable = false
	end
	if not SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.UseObj, params) then
		return false
	end
	return self:walkTo(targetPos.posX, targetPos.posY, nil, canJump, function(suc)
		printInfo("walkTo", targetPos.posX, targetPos.posY, suc)
		if suc then
			SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.UseObj, params)
		end
	end)

end

-- 家具交互
function SceneController:furnitureInteraction(walkPos, furInfo, canJump)
	return self:furInteraction(walkPos, furInfo, canJump)
end

function SceneController:furInteraction(walkPos, furInfo, canJump)
	local params = {}
	local targetPos = {}
	local lookPos = {}
	targetPos.posX = walkPos.x
	targetPos.posY = walkPos.y
	params.targetPos = targetPos
	params.canJump = canJump
	params.furInfo = furInfo
	if canJump then
		params.checkJumpWalkable = false
	end
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.FurnitureInteract, params)
end

function SceneController:furnitureStatusSync(furInfo)
	local params = {}
	params.furInfo = furInfo
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.FurnitureStatusSync, params)
end

function SceneController:furnitureSceneAction(walkPos, triggerId, actionType, canJump, getTriggerIdFunc, param, timeInfo,hideHand)
	local params = {}
	local targetPos = {}
	targetPos.posX = walkPos.x
	targetPos.posY = walkPos.y
	params.targetPos = targetPos
	params.triggerId = triggerId
	params.canJump = canJump
	params.type = actionType
	params.getTriggerId = getTriggerIdFunc
	params.param = param
	params.timeInfo = timeInfo
	params.hideHand = hideHand
	if canJump then
		params.checkJumpWalkable = false
	end
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.FurnitureSceneAction, params)
end

function SceneController:goJoinSceneGame(walkPos, params, callback)
	local targetPos = {}
	targetPos.posX = walkPos.x
	targetPos.posY = walkPos.y
	params.targetPos = targetPos
	params.canJump = canJump
	if canJump then
		params.checkJumpWalkable = false
	end
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.JoinSceneGame, params, callback)
end

-- function SceneController:onTalkResponse(status)
-- 	if status ~= 0 then
-- 		DialogHelper.showErrorMsg(status)
-- 	end
-- end

function SceneController:shareItem(id)
	LimitService.instance:showLimitDlg(7,
		function(isOk)
			if not isOk then return end
			local params = {}
			params.shareId = id
			SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Share, params)
		end
	)
end

function SceneController:treasureHunt(id)
	local currentSceneId = SceneManager.instance:getCurSceneId()
	local treasureList, showTip = SceneConfig.getTreasureArea(currentSceneId)
	if treasureList == nil then
		FlyTextManager.instance:showFlyText(lang("可到{1}等公共场景进行挖宝。", showTip))
		return
	end


	if SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.TreasureHunt) then
		if UserInfo.isRubyMember() and ItemService.instance:getItemNum(id) > 1 then
			ViewMgr.instance:open("BatchUsePanel", id, handler(self._goTreasureHunt, self))
			return true
		else
			return self:_goTreasureHunt(id, 1)
		end
	else
		return false
	end
end

function SceneController:_goTreasureHunt(id, count)
	local params = {}
	params.itemId = id
	params.count = count
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.TreasureHunt, params)
end

function SceneController:trapPeople(trapId)
	local params = {}
	params.trapId = trapId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.SceneTrap, params)
end

local TakeShareInterval = 2.5
local lastTakeShareTime = 0

function SceneController:takeShare(unit)
	if Time.realtimeSinceStartup - lastTakeShareTime < TakeShareInterval then
		FlyTextManager.instance:showFlyText(lang("正在做其他动作。"))
		return
	end
	LimitService.instance:checkShowFlyMsg(8)
	LimitService.instance:checkShowFlyMsg(302, lang("分享物晶钻上限"))
	local params = {}
	local targetPos = {}
	local scene = SceneManager.instance:getCurScene()
	if scene:getMapMgr():isWalkable(unit:getPos()) then
		targetPos.posX, targetPos.posY = unit:getPos()
	else
		local myPlayer = scene:getUserPlayer()
		targetPos.posX, targetPos.posY = myPlayer:getPos()
		params.jump = true
	end
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetRange = 1
	params.targetId = unit.id
	params.targetPos = targetPos
	if not SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.TakeShare, params) then
		return false
	end
	return self:walkTo(targetPos.posX, targetPos.posY, nil, false, function(suc)
		if SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.TakeShare, params) then
			lastTakeShareTime = Time.realtimeSinceStartup
		end
	end, 1)
end

--广播一个短动作,需要到场景相关-b表情配置中增加一列,不想在表情列表展现填2
--direction:人物朝向,nil:不做变化,其他的按照playerUnit的朝向来设置
--preventTips:传true则不飘字"正在做其他动作。"
function SceneController:showPose(id, extParams, direction, preventTips)
	local params = {}
	params.poseId = id
	params.extParams = extParams
	params.direction = direction
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Pose, params, nil, preventTips)
end

function SceneController:createPortal(id, direction, preventTips)
	local params = {}
	params.id = id
	params.direction = direction
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.CreatePortal, params, nil, preventTips)
end


function SceneController:collect(cfg, target, count)
	local typeCfg = CollectionConfig.getCollectionType(cfg.type)
	local productId = typeCfg:getProductId()
	if productId then
		local num = typeCfg:getLevelProductNum(UserInfo.getUserLevel())
		if not ItemService.instance:canAdd({{id = productId, num = num * count}}) then
			DialogHelper.showFullDlg()
			return
		end
	end
	local params = {}
	local targetPos = {}
	local px, py = SceneManager.instance:getCurScene():getUserPlayer():getPos()
	local isFlip = typeCfg.posOffset[1] > 0
	-- if isFlip ~= (typeCfg.posOffset[1] > 0) then
	-- targetPos.posX = cfg.pos[1] - typeCfg.posOffset[1]
	-- else
	targetPos.posX = cfg.pos[1] + typeCfg.posOffset[1]
	-- end
	targetPos.posY = cfg.pos[2] + typeCfg.posOffset[2]
	params.targetPos = targetPos
	params.isFlip = isFlip
	params.cfg = cfg
	params.target = target
	-- params.toolId = toolId
	params.canJump = false
	params.count = count
	if not SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.Collect, params) then
		return false
	end
	return self:walkTo(targetPos.posX, targetPos.posY, nil, false, function(suc)
		if suc then
			SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Collect, params)
		end
	end)
end

function SceneController:parttime(type, grade)
	local params = {}
	params.type = type
	params.grade = grade
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Parttime, params)
end

function SceneController:seaFishing(ani, direction)
    local params = {}
    params.ani = ani
    params.direction = direction
    return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.SeaFishing, params)
end

function SceneController:seaShowFish(ani, fishId, weight)
    local params = {}
    params.ani = ani
    params.fishId = fishId
    params.weight = weight
    return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.SeaShowFish, params)
end

function SceneController:IslanderEventFind(pos, findItemId)
	local typeCfg = CollectionConfig.getCollectionType(2)
	local params = {}
	local targetPos = {}
	local px, py = SceneManager.instance:getCurScene():getUserPlayer():getPos()
	local isFlip = typeCfg.posOffset[1] > 0
	targetPos.posX = pos[1] + typeCfg.posOffset[1]
	targetPos.posY = pos[2] + typeCfg.posOffset[2]
	params.targetPos = targetPos
	params.isFlip = isFlip
	params.cfgId = 2
	params.canJump = false
	params.findItemId = findItemId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.IslanderEventFind, params)
end

function SceneController:simpleInteraction(cfg, target, posX, posY)
	local typeCfg = SimpleInteractionConfig.getInteractionTypeDefine(cfg.type)
	local params = {}
	local targetPos = {}
	local isFlip = typeCfg.posOffset[1] > 0
	if posX and posY then
		targetPos.posX = posX
		targetPos.posY = posY
	else
		targetPos.posX = cfg.pos[1] + typeCfg.posOffset[1]
		targetPos.posY = cfg.pos[2] + typeCfg.posOffset[2]
	end

	params.targetPos = targetPos
	params.isFlip = isFlip
	params.cfg = cfg
	params.target = target
	params.canJump = false
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.SimpleInteraction, params)
end

function SceneController:talkToNpc(posX, posY, npcId, needSearch)
	local params = {}
	local targetPos = {}
	targetPos.posX = posX
	targetPos.posY = posY
	params.targetPos = targetPos
	params.canJump = true
	if needSearch then
		params.targetRange = 1
	end
	params.npcId = npcId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Talk, params)
end

function SceneController:gotoUserScene(userId)
	SceneAgent.instance:sendGetRoleInSceneInfoRequest(
		userId,
		function(sceneId, channelId, targetId,extend)
			local cfg = SceneConfig.getSceneConfig(sceneId)
			if cfg then
				--if SceneHelp.instance:followJudge(cfg,extend) == false then
				--	return
				--end
				if cfg.jumpType == 1 then
					FlyTextManager.instance:showFlyText(lang("暂时无法定位到对方的位置哦"))
					return
				elseif cfg.jumpType == 2 then
					local myCouncilId = CouncilManager.instance:getCouncilId()
					if myCouncilId == nil or myCouncilId ~= extend then
						FlyTextManager.instance:showFlyText(lang("暂时无法定位到对方的位置哦"))
						return
					end
				end
				ViewMgr.instance:clearBackStack()
				if cfg.sceneType == 2 then
					RoomFacade.instance:enterRoomScene(targetId,extend,nil,nil,nil,nil,posIds[sceneId])
				elseif cfg.sceneType == 3 then
					FarmFacade.instance:enterOthersIsland(targetId, false, nil, nil, nil, nil, nil, nil, posIds[sceneId])
				else
					if SceneManager.instance:getCurSceneId() ~= sceneId or SceneManager.instance:getCurChannel() ~= channelId then
						if cfg.sceneType == SceneType.Sea then
							SeaFacade.gotoScene(sceneId, channelId)
						else
							SceneManager.instance:loadSceneWithChannel(sceneId, nil, nil, channelId)
						end
					end
				end
			else
				printError("找不到场景配置")
			end
		end
	)
end

function SceneController:stopAction(targetAction, callBack)
	local mgr = SceneManager.instance:getCurScene().actionMgr
	if not targetAction or mgr:hasAction(targetAction) then
		return mgr:stopAction(targetAction, callBack)
	else
		return false
	end
end

function SceneController:goFishing(x, y, dir, pondId)
	printInfo("pondId", pondId)
	local params = {}
	local targetPos = {posX = x, posY = y}
	params.targetPos = targetPos
	params.pondId = pondId
	params.dir = dir
	if not SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.Fishing, params) then
		return false
	end
	self:walkTo(targetPos.posX, targetPos.posY, nil, false, function(suc)
		if suc then
			return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Fishing, params)
		end
	end)

end

function SceneController:startJoystickMove()
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Joystick, nil)
end

function SceneController:startWerewolfJoystickMove()
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.WerewolfJoystick, nil)
end

function SceneController:startCommonGameJoystickMove()
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.CommonGameJoystick, nil)
end

function SceneController:startGame(id)
	local define = ItemService.instance:getDefine(id)
	local gameCfg = TinyGameConfig.getConfig(define.properties.gameId)
	local params = {}
	params.id = define.properties.gameId
	if gameCfg.gameType == 1 then
		LimitService.instance:checkShowFlyMsg(10)
		return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.StartGame, params)
	else
		return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.StartQueue, params)
	end
end

function SceneController:joinQueue(unit)
	local params = {id = unit.id}
	local px, py = unit:getPos()
	params.targetPos = {posX = px, posY = py}
	params.forceStop = true
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.JoinQueue, params)
end

function SceneController:statThrowItem(id, specifiedList, helpHandler, itemEmptyHandler, onThrowEndHandler, throwType)
	printInfo("statThrowItem:", id)
	local params = {}
	params.id = id
	params.specifiedList = specifiedList
	params.helpHandler = helpHandler
	params.itemEmptyHandler = itemEmptyHandler
	params.onThrowEndHandler = onThrowEndHandler
	params.throwType = throwType
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Throw, params)
end

function SceneController:doThrowItem(id, x, y, z, throwDistance, throwType, isThrowSucc, throwTimeInterval, callBack)
	local params = {}
	local targetPos
	if VirtualCameraMgr.instance:is3DScene() then
		targetPos = {posX = x, posY = z}
	else
		targetPos = {posX = x, posY = y}
	end
	params.targetPos = targetPos
	params.targetRange = throwDistance or 5
	--throwParams id \ throwType \ isThrowSucc 后端用来做物品扣除逻辑等，后面参数回传前端（广播）
	local numberIsThrowSucc = false
	if isThrowSucc == true then
		numberIsThrowSucc = 1
	else
		numberIsThrowSucc = 0
	end
	if not throwTimeInterval then
		throwTimeInterval = 0
	end
	params.throwParams = {id, throwType or 0, numberIsThrowSucc, x, y, z, throwTimeInterval}
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.DoThrow, params, callBack, true)
end

--在小岛里进行touch，
--targetPos:目标点
--animName:动作名
--lastTime:持续时间，不传则读t_commonConfig：PlayerEndHarvestTime
--jumpBack:是否跳回原位
function SceneController:playTouchInIsland(targetPos, animName, lastTime, jumpBack, animIsLoop, callBack, tempClothesId)
	local params = {
		targetPos = targetPos,
		animName = animName,
		lastTime = lastTime,
		jumpBack = jumpBack,
		animIsLoop = animIsLoop,
		callBack = callBack,
		tempClothesId = tempClothesId
	}
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Harvest, params)
end

function SceneController:joinGame(unit)
	LimitService.instance:checkShowFlyMsg(10)
	local params = {}
	local targetPos = Vector3.New()
	targetPos.posX, targetPos.posY = unit:getPos()
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetRange = 1
	params.userId = unit.id
	params.targetPos = targetPos
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.JoinGame, params)
end

function SceneController:catchPet(id)
	if not FuncUnlockFacade.instance:checkIsUnlocked(FuncIds.Pet, true) then
		return
	end
	local scenes, showScencesTip = PetCatchConfig.getPetCatchScences()
	dump(showScencesTip)
	local currentSceneId = SceneManager.instance:getCurSceneId()
	if not (scenes[currentSceneId]) then
		FlyTextManager.instance:showFlyText(lang("可到{1}等公共场景捕捉宠物。", showScencesTip))
		return
	end
	local params = {}
	params.itemId = id
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.CatchPet, params)
end

function SceneController:hitIce(x, y, callback)
	local params = {}
	local targetPos = {posX = x, posY = y}
	params.targetPos = targetPos
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.HitIce, params, callback)
end

function SceneController:showRead()
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.Read, nil, nil, true)
end

function SceneController:showAR()
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AR, nil, nil, true)
end

function SceneController:sendCustomCommand(key, id, listParam, strParam)
	SceneAgent.instance:sendBroadcastStateRequest(key, id, listParam, strParam)
end

function SceneController:startDoublePose(poseId, unit)
	local params = {}
	local targetPos = {}
	targetPos.posX, targetPos.posY = unit:getPos()
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetRange = 1.5
	params.targetId = unit.id
	params.targetPos = targetPos
	params.poseId = poseId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.StartDoublePose, params)
end

function SceneController:acceptDoublePose(poseId, unit)
	local params = {}
	local targetPos = {}
	local cfg = PoseConfig.getDoublePose(poseId)
	local dir = unit:getDirection()
	local isReverse = UnitDirection.isFaceLeft(dir)
	local px, py = unit:getPos()
	local offsetX = isReverse and -cfg.subOffset[1] or cfg.subOffset[1]
	local offsetY = cfg.subOffset[2]
	targetPos.posX = px + offsetX
	targetPos.posY = py + offsetY
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetId = unit.id
	params.targetPos = targetPos
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AcceptDoublePose, params)
end

function SceneController:startDoubleFollow(poseId, unit)
	local params = {}
	local targetPos = {}
	targetPos.posX, targetPos.posY = unit:getPos()
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetRange = 1.5
	params.targetId = unit.id
	params.targetPos = targetPos
	params.poseId = poseId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.StartDoubleFollow, params)
end

function SceneController:acceptDoubleFollow(poseId, unit)
	local params = {}
	local targetPos = {}
	local cfg = PoseConfig.getDoublePose(poseId)
	local dir = unit:getDirection()
	local isReverse = UnitDirection.isFaceLeft(dir)
	local px, py = unit:getPos()
	local offsetX = isReverse and -cfg.subOffset[1] or cfg.subOffset[1]
	local offsetY = cfg.subOffset[2]
	targetPos.posX = px + offsetX
	targetPos.posY = py + offsetY
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetId = unit.id
	params.targetPos = targetPos
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AcceptDoubleFollow, params)
end

function SceneController:startMultiInteraction(walkPos, paramList, canJump)
	local params = {}
	local targetPos = {}
	targetPos.posX = walkPos.x
	targetPos.posY = walkPos.y
	params.targetPos = targetPos
	params.paramList = paramList
	params.canJump = canJump
	if canJump then
		params.checkJumpWalkable = false
	end
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.MultiInteraction, params)
end

function SceneController:startSitCushion(walkX, walkY, cushionId, sitPos, ownerId, dir, isCreate, teapartyId, teapartyPwd, teapartyName)
	
	local params = {}
	params.sitPos = sitPos

	params.canJump = true
	params.cushionId = cushionId
	params.owner = ownerId
	params.dir = dir
	params.teapartyPwd = teapartyPwd
	params.teapartyName = teapartyName
	params.teapartyId = teapartyId
	if not SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.CushionJoin, params) then
		return false
	end
	if not isCreate then
		return self:walkTo(walkX, walkY, nil, params.canJump, function(suc)
			if suc then
				SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.CushionJoin, params)
			end
		end)
	else
		SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.CushionJoin, params)
	end

end

function SceneController:startFurnitureCushion(walkX, walkY, cushionId, sitPos, ownerId, dir, isCreate, openDonate, nusidIndex, actionIndexs)
	local params = {}
	params.sitPos = sitPos

	params.isCreate = isCreate
	params.canJump = true
	params.cushionId = cushionId
	params.owner = ownerId
	params.dir = dir
	params.openDonate = openDonate
	params.nusidIndex = nusidIndex
	params.actionIndexs = actionIndexs
	if not SceneManager.instance:getCurScene().actionMgr:canStart(SceneActionType.CushionJoin, params) then
		return false
	end
	if not isCreate and ownerId ~= UserInfo.userId then
		return self:walkTo(walkX, walkY, nil, params.canJump, function(suc)
			if suc then
				SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.CushionJoin, params)
			end
		end)
	else
		SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.CushionJoin, params)
	end
end

function SceneController:startAnniversaryGalaAttachedAsk(unit)
	local params = {}
	local targetPos = {}
	targetPos.posX, targetPos.posY = unit:getPos()
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetRange = 1.5
	params.targetId = unit.id
	params.targetPos = targetPos
	params.canJump = true
	params.checkJumpWalkable = false
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AnniversaryGalaAskAttached, params)
end

function SceneController:startAnniversaryGalaAttachedAnswer(unit)
	local params = {}
	local px, py = unit:getPos()
	local targetPos = {}
	targetPos.posX = px + 1.5
	targetPos.posY = py
	params.targetId = unit.id
	params.targetPos = targetPos
	params.canJump = true
	params.checkJumpWalkable = false
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AnniversaryGalaAnswerAttached, params)
end

function SceneController:startAnniversaryGalaStartHand(unit)
	local params = {}
	local targetPos = {}
	targetPos.posX, targetPos.posY = unit:getPos()
	-- local r = math.random() * math.pi * 2
	-- local len = 1
	-- local isFaceRight = unit:getFace()
	-- targetPos.posX = targetPos.posX + (isFaceRight and -1 or 1)
	params.targetRange = 1.5
	params.targetId = unit.id
	params.targetPos = targetPos
	params.canJump = true
	params.checkJumpWalkable = false
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AnniversaryGalaStartHand, params)
end

function SceneController:startAnniversaryGalaAcceptHand(unit)
	local params = {}
	local targetPos = {}
	targetPos.posX, targetPos.posY = unit:getPos()
	params.targetRange = 1.5
	params.targetId = unit.id
	params.targetPos = targetPos
	params.canJump = true
	params.checkJumpWalkable = false
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AnniversaryGalaAcceptHand, params)
end

function SceneController:startJoinGodlikeAction(triggerParams)
	local params = {}
	params.triggerId = triggerParams.triggerId
	params.focusUnit = triggerParams.focusUnit
	-- local trigger = SceneManager.instance:getCurScene().triggerController:getTrigger(tostring(triggerParams.triggerId))
	-- local triggerPos = trigger.triggerGO.transform.position
	-- local targetPos = {posX=triggerPos.x, posY=triggerPos.z, posZ=triggerPos.y}
	-- params.targetPos = targetPos

	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AnniversaryGalaJoinGodlike, params)
end

function SceneController:startJoinFireworkAction(triggerParams)
	local params = {}
	params.triggerId = triggerParams.triggerId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.AnniversaryGalaJoinFirework, params)
end

function SceneController:startRideMountAction(mountId)
	local params = {}
	params.mountId = mountId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.StartRideMount, params)
end

function SceneController:joinRideMount(ownerId)
	local params = {}
	params.ownerId = ownerId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.JoinRideMount, params)
end

function SceneController:startLaunchSeaDanceAction()
	local params = {}
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.LaunchSeaDance, params)
end

function SceneController:startJoinSeaDanceAction(unit)
	local params = {}
	local targetPos = {}
	targetPos.posX, targetPos.posY = unit:getPos()
	params.targetRange = 1.5
	params.targetId = unit.id
	params.targetPos = targetPos
	params.canJump = true
	params.checkJumpWalkable = false
	
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.JoinSeaDance, params)
end

function SceneController:startWaitTogetherDanceAction()
	local params = {}
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.WaitTogetherDance, params)
end

function SceneController:startBonfirePartyAction(showPointId)
	local params = {}
	params.showPointId = showPointId
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.BonfireParty, params)
end

function SceneController:startBonfirePartyCallAction()
	local params = {}
	return SceneManager.instance:getCurScene().actionMgr:startAction(SceneActionType.BonfirePartyCall, params)
end

--检查是否在做其他动作
function SceneController:checkIdle()
	if SceneManager.instance:getCurScene().actionMgr:isIdle() then
		return true
	else
		FlyTextManager.instance:showFlyText(lang("正在做其他动作。"))
		return false
	end
end

---进场景逻辑
function SceneController:joinScene(channelId, sceneId, bornX, bornY, userId, isNpc, isSingle, teamId, roomId, password,councilId,callback)
	printInfo("join scene", channelId, sceneId, bornX, bornY, userId, isNpc, isSingle, teamId, roomId, password,councilId)
	GlobalDispatcher:dispatch(GlobalNotify.StartLoadScene)
	LoadingMask.instance:show()
	self.joinSceneHandler = callback
	self.tryJoinSceneId = sceneId
	self._sameGroupBeforeJoinSceneReply = {}
	SceneAgent.instance:sendJoinSceneRequest(
		channelId,
		sceneId,
		bornX,
		bornY,
		userId,
		isNpc,
		isSingle,
		teamId,
		roomId,
		password,
		councilId,
		handler(self.onJoinScene, self)
	)
end

function SceneController:onJoinScene(status, msg)
	LoadingMask.instance:close()
	self:localNotify(SceneNotify.JoinSceneFinish)
	if status == 0 then
		SceneManager.instance:setChannel(msg.channelId)
		SceneManager.instance:setSceneOwner(msg.ownerId)
		local roleInfos = msg.sceneRoleInfos
		local newRoleInfo
		self.cacheRoleInfos = {}
		for i = 1, #roleInfos do
			newRoleInfo = {id = roleInfos[i].id, roleInfoField = {}}
			_setUserVar(newRoleInfo.roleInfoField, roleInfos[i])
			self.cacheRoleInfos[roleInfos[i].id] = newRoleInfo
		end
		local sceneInfos = msg.sceneStateInfo
		-- dump(sceneInfos)
		self.cacheSceneInfos = {}
		self.cacheSceneCustomInfos = {}
		table.insert(self.cacheSceneInfos, {sceneInfos, {}})
		SceneManager.instance:setSceneTime(msg.joinOffsetMillis)

		self:initServerSceneEntryInfo(msg.sceneEntryInfo)

		--场景唯一标识
		self.sceneUniqueId = msg.uniqueId
		self.needCache = true
		self.needCacheSceneInfo = true
		-- settimer(2, self.joinSceneHandler, self, false)
		if self.tryJoinSceneId ~= 52 then
			self.sameGroupUniqueId = 0
		else
			self:onReceivedJoinReply()
		end
		self._sameGroupBeforeJoinSceneReply = nil
		self.joinSceneHandler()
	elseif status == 203 or SceneManager.instance.isReload then
		SceneManager.instance:forceLoadScene(4)
	else
		GlobalDispatcher:dispatch(GlobalNotify.OnTryEnterSceneFailed, status, self.tryJoinSceneId)
		DialogHelper.showErrorMsg(status)
	end
end

--- 场景中其他玩法的入口
function SceneController:initServerSceneEntryInfo(sceneEntryInfo)
	local sceneEntryInfos = sceneEntryInfo.entryPos
	self.cacheSceneEntryInfos = {}
	-- entryPos = {[index] = {entryType = type, entryPosInfos = {[index] = {posX,posY,posZ}, [index2] = {posX,posY,posZ} } } }
	for i = 1, #sceneEntryInfos do
		local entryType = sceneEntryInfos[i].entryType
		self.cacheSceneEntryInfos[entryType] = sceneEntryInfos[i].areaIds
		-- if not self.cacheSceneEntryInfos[entryType] then
		-- 	self.cacheSceneEntryInfos[entryType] = sceneEntryInfos[i].areaIds
		-- end
	end
end

function SceneController:getJoinSceneInfo()
	local infos = {}
	if self.cacheRoleInfos ~= nil then
		for _, v in pairs(self.cacheRoleInfos) do
			v.roleInfoField = map2list(v.roleInfoField)
			table.insert(infos, v)
		end
	end
	self.needCache = false
	self.cacheRoleInfos = nil
	return infos
end

function map2list(obj)
	local list = {}
	for _, v in pairs(obj) do
		table.insert(list, v)
	end
	return list
end

function clonePb(pb)
	local obj = {}
	for field, value in pb:ListFields() do
		obj[field.name] = value
	end
	return obj
end

function SceneController:enterZone(roleInfos)
	if self.needCache then
		local newRoleInfo
		for i = 1, #roleInfos do
			if roleInfos[i].id ~= UserInfo.userId then
				newRoleInfo = {id = roleInfos[i].id, roleInfoField = {}}
				_setUserVar(newRoleInfo.roleInfoField, roleInfos[i])
				self.cacheRoleInfos[roleInfos[i].id] = newRoleInfo
			end
		end
	end
	SceneController.instance:localNotify(SceneNotify.EnterZone, roleInfos)
end

function SceneController:exitZone(userIds)
	if self.needCache then
		for i = 1, #userIds do
			self.cacheRoleInfos[userIds[i]] = nil
		end
	end
	SceneController.instance:localNotify(SceneNotify.ExitZone, userIds)
end

--- 被从场景中t出去
function SceneController:onBeenKickedOut(targetSceneId, type)
	self._kickedOutType = type
	self._kickedToSceneId = targetSceneId	
	if type == 1128 then
		--- 在别人小岛被t的时候等idle再跳
		self._houseBelongId = SceneManager.instance:getSceneOwner()
		ViewFacade.addCallBackWhenIdle(handler(self.doHandleKickedOut, self))
	else
		self:doHandleKickedOut()
	end
end

function SceneController:doHandleKickedOut()
	local targetSceneId = self._kickedToSceneId
	local type = self._kickedOutType
	self._kickedToSceneId = nil
	self._kickedOutType = nil

	if type == 1128 then
		--- 过程中跳去了其他地方要判断一层
		local curOwner = SceneManager.instance:getSceneOwner()
		if self._houseBelongId and self._houseBelongId ~= curOwner then
			self._houseBelongId = nil
			return
		end
	end
	self._houseBelongId = nil
	ViewMgr.instance:clearBackStack()
	ViewMgr.instance:closeAllModalViews()
	--- 本来是有类型枚举的，后端做着做着就变错误码了=-=
	if type == GameEnum.SceneKickOutUserType.GAME_ROOM_OWNER then
		FlyTextManager.instance:showFlyText(lang("被踢出场景提示".. type))
	else
		DialogHelper.showErrorMsg(type)
	end
	if not SceneConfig.checkEnter(targetSceneId, true) then
		-- 去不了的场景就回小岛
		FarmFacade.instance:enterIsland()
		return
	end
	
	SceneManager.instance:forceLoadScene(targetSceneId)
end

function SceneController:onSameGroupSceneChange(changeInfo, sceneStateInfo, keys, joinType, groupUniqueId)
	-- printError("onSameGroupSceneChange",tostring(joinType), tostring(groupUniqueId), tostring(self.sameGroupUniqueId)
	-- ,tostring(self._sameGroupBeforeJoinSceneReply))
	if joinType == 1 then
		self.sameGroupUniqueId = groupUniqueId
		if self._sameGroupBeforeJoinSceneReply then
			local cache = {changeInfo=changeInfo, sceneStateInfo=sceneStateInfo, keys=keys, groupUniqueId=groupUniqueId}
			table.insert(self._sameGroupBeforeJoinSceneReply, cache)
			return
		end
	end
	
	if self.sameGroupUniqueId ~= groupUniqueId then
		return
	end
	for i = 1, #changeInfo do
		local info = changeInfo[i]
		local type = info.type
		if type == 1 then
			self:enterZone({info.joinRoleInfo})
		elseif type == 2 then
			self:roleInfoChange(self.sceneUniqueId, {info.changeRoleInfo})
		else
			self:exitZone({info.leaveSceneUserId})
		end
	end

	if self.needCacheSceneInfo then
		table.insert(self.cacheSceneInfos, {sceneStateInfo, keys})
		print("push SameGroup CacheSceneInfo -=>> " .. #self.cacheSceneInfos)
		return
	end

	SceneController.instance:localNotify(SceneNotify.InteractionChange, sceneStateInfo, keys)
end

function SceneController:onReceivedJoinReply()
	if not self._sameGroupBeforeJoinSceneReply then
		return
	end
	for i = 1, #self._sameGroupBeforeJoinSceneReply do
		local cache = self._sameGroupBeforeJoinSceneReply[i]
		-- self:onSameGroupSceneChange(cache.changeInfo, cache.sceneStateInfo, cache.keys, nil, cache.groupUniqueId)
		if cache.groupUniqueId == self.sameGroupUniqueId then
			for i = 1, #cache.changeInfo do
				local info = cache.changeInfo[i]
				local type = info.type
				if type == 1 then
					local newRoleInfo = {id = info.joinRoleInfo.id, roleInfoField = {}}
					_setUserVar(newRoleInfo.roleInfoField, info.joinRoleInfo)
					self.cacheRoleInfos[info.joinRoleInfo.id] = newRoleInfo
				elseif type == 2 then
					local roleInfo = info.changeRoleInfo
					local cacheInfo = self.cacheRoleInfos[roleInfo.id]
					if cacheInfo then
						_setUserVar(cacheInfo.roleInfoField, roleInfo)
					end
				else
					self.cacheRoleInfos[info.leaveSceneUserId] = nil
				end
			end
			
			table.insert(self.cacheSceneInfos, {cache.sceneStateInfo, cache.keys})
		end
	end
end

-- 处理场景中的所有变化信息
function SceneController:sceneChange(uniqueId, sceneChangeInfos)
	if self.sceneUniqueId ~= uniqueId then
		--不是本场景的消息，忽略
		return
	end

	if #sceneChangeInfos == 0 then
		--没有什么变动数据，就返回了
		return
	end

	local changeInfo = sceneChangeInfos
	for i = 1, #changeInfo do
		local info = changeInfo[i]
		local type = info.type
		if type == 1 then
			self:enterZone({info.joinRoleInfo})
		elseif type == 2 then
			self:roleInfoChange(uniqueId, {info.changeRoleInfo})
		else
			self:exitZone({info.leaveSceneUserId})
		end
	end
end

-- function SceneController:targetMove(moveInfos)
-- 	local roleInfo
-- 	if self.needCache then
-- 		for i = 1, #moveInfos do
-- 			roleInfo = self.cacheRoleInfos[moveInfos[i].id]
-- 			roleInfo.initPos = moveInfos[i]
-- 		end
-- 	end
-- 	SceneController.instance:localNotify(SceneNotify.TargetMove, moveInfos)
-- end

-- function SceneController:userClothesChange(roleClothes)
-- 	if self.needCache then
-- 		for i = 1, #roleClothes do
-- 			self.cacheRoleInfos[roleClothes[i].id].clothes = roleClothes[i].clothes
-- 		end
-- 	end
-- 	SceneController.instance:localNotify(SceneNotify.UserClothesChange, roleClothes)
-- end

function SceneController:sceneStateChange(sceneStateInfo, removeKeys, uniqueId)
	if self.sceneUniqueId ~= uniqueId then
		--不是本场景的消息，忽略
		return
	end

	-- if self.needCache then
	-- 	for i = 1, #removeKeys do
	-- 		self.cacheSceneInfos[removeKeys[i]] = nil
	-- 	end
	-- 	for i = 1, #sceneStateInfo do
	-- 		self.cacheSceneInfos[sceneStateInfo[i].key] = sceneStateInfo[i]
	-- 	end
	-- end
	if self.needCacheSceneInfo then
		table.insert(self.cacheSceneInfos, {sceneStateInfo, removeKeys})
		print("push CacheSceneInfo -=>> " .. #self.cacheSceneInfos)
		return
	end

	SceneController.instance:localNotify(SceneNotify.InteractionChange, sceneStateInfo, removeKeys)
end

function SceneController:getCacheSceneInfos()
	self.needCacheSceneInfo = false
	local infos = {}
	for k, v in ipairs(self.cacheSceneInfos) do
		table.insert( infos, v)
	end

	local customInfos = {}
	for k, state in ipairs(self.cacheSceneCustomInfos) do
		table.insert(customInfos, state)
	end

	self.cacheSceneInfos = nil
	self.cacheSceneCustomInfos = nil
	self.needCacheSceneInfo = false
	return infos, customInfos
end

function SceneController:sceneCustomCommandChanged(state)
	if self.needCacheSceneInfo then
		table.insert(self.cacheSceneCustomInfos, state)
		return
	end
	SceneController.instance:localNotify(SceneNotify.OnCustomCommand, state)
end

function SceneController:roleInfoChange(uniqueId, sceneRoleStateInfos)
	if self.sceneUniqueId ~= uniqueId then
		--不是本场景的消息，忽略
		return
	end
	if self.needCache then
		local roleInfo, cacheInfo
		for i = 1, #sceneRoleStateInfos do
			roleInfo = sceneRoleStateInfos[i]
			cacheInfo = self.cacheRoleInfos[roleInfo.id]
			if cacheInfo then
				_setUserVar(cacheInfo.roleInfoField, roleInfo)
			end
		end
	end

	SceneController.instance:localNotify(SceneNotify.RoleInfoChange, sceneRoleStateInfos)
end

function SceneController:serverSceneEntryInfoChange(sceneEntryInfo, needRemoveType)
	if #needRemoveType > 0 then
		for i = 1, #needRemoveType do
			local type = needRemoveType[i]
			self.cacheSceneEntryInfos[type] = {}
		end
		SceneController.instance:localNotify(SceneNotify.SceneEntryInfoChange, sceneEntryInfo, needRemoveType)
		return
	end

	for i = 1, #sceneEntryInfo do
		local changedType = sceneEntryInfo[i].entryType

		-- self.cacheSceneEntryInfos[changedType] = sceneEntryInfo[i].entryPosInfos
		self.cacheSceneEntryInfos[changedType] = sceneEntryInfo[i].areaIds
	end
	-- GlobalDispatcher:dispatch(GlobalNotify.SceneEntryInfoChenge, sceneEntryInfo, needRemoveType)
	SceneController.instance:localNotify(SceneNotify.SceneEntryInfoChange, sceneEntryInfo, needRemoveType)
end

function _setUserVar(userVar, roleInfo)
	for i = 1, #roleInfo.delType do
		userVar[roleInfo.delType[i]] = nil
	end
	for i = 1, #roleInfo.roleInfoField do
		local info = roleInfo.roleInfoField[i]
		if info.type ~= UserVarKey.SceneRolePose.id then
			userVar[info.type] = info
		end
	end
end

--进场景逻辑end

SceneController.instance = SceneController.New()
return SceneController
